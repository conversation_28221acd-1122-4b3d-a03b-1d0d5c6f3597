# type: ignore
VALID_IMAGE_NAME_CHARS = "abcdefghijklmnopqrstuvwxyz0123456789-"


def se_image(image_name, pex_entry_point, docker_dependencies=None, tags=None,
             pex_dependencies=None, dockerfile_source='Dockerfile', pex_bin_name='bin'):
    # validate image name is alphanumeric and lowercase, with dashes allowed
    for char in image_name:
        if char not in VALID_IMAGE_NAME_CHARS:
            raise ValueError(
                f"Invalid character in image_name param of se_image target: {char} in {image_name}")

    tags = tags or []

    pex_binary(
        name=pex_bin_name,
        entry_point=pex_entry_point,
        # loose layout to allow vulnerability scanning
        layout="loose",
        tags=tags,
        dependencies=pex_dependencies,
    )

    common_docker_image_args = dict(
        repository=image_name,
        extra_build_args=[
            "SE_MONO_PY11_BASE",
            "SE_MONO_PY11_BASE_SUPERUSER",
            "ARIES_ML_PY11_BASE",
            "SE_MONO_PY11_FFMPEG_BASE",
        ],
        dependencies=docker_dependencies,
        image_tags=["{build_args.IMAGE_TAG}","{build_args.GITHUB_RELEASE_TAG}"],
        source=dockerfile_source,
        pull=True,  # always pull the base image
    )

    # used to create the docker image, without pushing, used on validation job to check for validity of images
    docker_image(
        name=f"{image_name}_docker",
        # do not push images to registry
        # output={"type": "docker"},
        skip_push=True,
        tags=tags + ["local_image"],
        **common_docker_image_args,
    )

    # used to create the docker image and push it to the registry in publish job
    docker_image(
        name=f"{image_name}_registry",
        # output={"type": "registry"},
        tags=tags + ["remote_image"],
        skip_push=False,
        **common_docker_image_args,
    )
