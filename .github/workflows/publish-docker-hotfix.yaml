name: Build Hotfix Docker Images
on:
  workflow_dispatch:
    inputs:
      pants-docker-image-target:
        description: Pants target for the docker image to be built and published
        required: true
      github-tag:
        description: Base tag from which hotfix needs to be released
        required: true

env:
  GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}
  JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
  ARIES_ML_PY11_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/aries-ml-py11-base:latest
  SE_MONO_PY11_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-base:latest
  SE_MONO_PY11_FFMPEG_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-ffmpeg-base:latest
  ARIES_ML_PY11_BASE_SUPERUSER: ${{ vars.PROD_AWS_ECR_REGISTRY }}/aries-ml-py11-base-superuser:latest
  SE_MONO_PY11_BASE_SUPERUSER: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-base-superuser:latest
  PANTS_TARGET_PATTERN: 'src\/py\/.+\/BUILD:.+'
  PANTS_CONFIG_FILES: pants.ci.toml
  # registry definitions for pantsbuild via env vars
  PANTS_DOCKER_REGISTRIES: '{"ecr_prod":{"address":"698897937809.dkr.ecr.eu-west-1.amazonaws.com","default":"true"},"acr_prod":{"address":"acrsteeleyehub.azurecr.io","default":"true"}}'

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  publish-hotfix-check:
    runs-on: ubuntu-latest
    steps:
      - name: check pants docker image target
        run: |
          if ! [[ "${{ github.event.inputs.pants-docker-image-target }}" =~ ${{ env.PANTS_TARGET_PATTERN }} ]]; then
            echo "Input validation failed. The argument should be a pants target for the docker image to be built and published"
            exit 1
          fi

  publish-hotfix-github-tag:
    runs-on: se-mono-large-runner
    needs: [ publish-hotfix-check ]
    outputs:
      next_hotfix_release_tag: ${{ steps.create-hotfix-tag.outputs.next_hotfix_release_tag }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          fetch-tags: true

      - name: Disable git repository validation
        # this is done to avoid pants failure which fails to recognize the git repo
        run: |
          git config --add safe.directory '*'

      - name: Create hotfix tag and do a GitHub release for the hotfix
        id: create-hotfix-tag
        run: |
          # Find the latest release tag for the hotfix branch
          CURRENT_RELEASE_GIT_TAG=${{ github.event.inputs.github-tag }}
          GH_RELEASE=$(gh api "repos/${{ github.repository }}/releases/tags/$CURRENT_RELEASE_GIT_TAG" --silent)

          # Fetch the list of tags sorted by creation date and merged into the current HEAD
          tags=$(git tag --sort=-creatordate --merged HEAD)

          # Initialize the variable for the actual release tag
          ACTUAL_RELEASE_GIT_TAG=""

          # Loop through the tags and find the first one that doesn't contain 'dev' or 'hotfix'
          for tag in $tags; do
              if [[ "$tag" != *dev* ]] && [[ "$tag" != *hotfix* ]]; then
                  ACTUAL_RELEASE_GIT_TAG="$tag"
                  break
              fi
          done

          # Check if a valid tag was found
          if [ -z "$ACTUAL_RELEASE_GIT_TAG" ]; then
              echo "Error: No valid tags found after filtering out 'dev' and 'hotfix' tags."
              exit 1
          fi

          # Debugging: Output the tags being considered
          echo "CURRENT_RELEASE_GIT_TAG: $CURRENT_RELEASE_GIT_TAG"
          echo "ACTUAL_RELEASE_GIT_TAG: $ACTUAL_RELEASE_GIT_TAG"

          # If the actual and current do not match, fail with error
          if [ "$CURRENT_RELEASE_GIT_TAG" != "$ACTUAL_RELEASE_GIT_TAG" ]; then
              echo "The provided tag $CURRENT_RELEASE_GIT_TAG does not match the latest release tag $ACTUAL_RELEASE_GIT_TAG. Please create branch again from the GitHub tag on which the hotfix is to be applied, without any other tags."
              exit 1
          fi

          # Check if the tag is a valid release
          if gh api "repos/${{ github.repository }}/releases/tags/$CURRENT_RELEASE_GIT_TAG" --silent; then
              echo "Found valid release for tag: $CURRENT_RELEASE_GIT_TAG"
              COMMIT_SHA=$(git rev-parse --short HEAD)
              TIMESTAMP=$(date +%s)

              NEXT_HOTFIX_RELEASE_TAG="${CURRENT_RELEASE_GIT_TAG}-hotfix.${TIMESTAMP}.${COMMIT_SHA}"

              git config user.name "${GITHUB_ACTOR}"
              git config user.email "${GITHUB_ACTOR}@users.noreply.github.com"
              git tag -a ${NEXT_HOTFIX_RELEASE_TAG} -m "Run ID: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

              echo "CURRENT_RELEASE_GIT_TAG=${CURRENT_RELEASE_GIT_TAG}" >> "$GITHUB_ENV"
              echo "NEXT_HOTFIX_RELEASE_TAG=${NEXT_HOTFIX_RELEASE_TAG}" >> "$GITHUB_ENV"
          else
              echo "No release found for tag: $CURRENT_RELEASE_GIT_TAG"
              exit 1
          fi
          
          # push the tag to the repository iff the docker image is published
          git push origin ${NEXT_HOTFIX_RELEASE_TAG}
          echo "next_hotfix_release_tag=${NEXT_HOTFIX_RELEASE_TAG}" >> $GITHUB_OUTPUT
          echo "${NEXT_HOTFIX_RELEASE_TAG}" >> $GITHUB_STEP_SUMMARY

  publish-hotfix-docker-amd:
    runs-on: se-mono-amd-runners
    needs: [ publish-hotfix-github-tag ]
    outputs:
      docker-images-published: ${{ steps.publish.outputs.docker-images-published }}
    container:
      image: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-ci:latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 25
          lfs: true
          fetch-tags: true

      - name: Disable git repository validation
        # this is done to avoid pants failure which fails to recognize the git repo
        run: |
          git config --global --add safe.directory '*'

      - name: validation
        run: |
          # custom rolling pants cache
          export PANTS_NAMED_CACHES_DIR="$HOME/.cache/pants/named_caches/period_$((10#$(date +%j) / 10))"
          
          # unit tests
          pants \
            --tag=amd_only,build_amd \
            --changed-since=${{ github.event.inputs.github-tag }} \
            --changed-dependents=transitive \
            test

      - name: Publish requested hotfix docker image
        id: publish
        run: |
          # Erase stale AWS credentials
          rm -rf ~/.aws
          
          # Create or replace the docker config file in GitHub temp directory
          export DOCKER_CONFIG="${{ runner.temp }}/.docker"
          mkdir -p "$DOCKER_CONFIG"
          echo '{}' > "$DOCKER_CONFIG/config.json"
          
          # Login to ECR
          aws ecr get-login-password | docker login --username AWS --password-stdin "${{ vars.PROD_AWS_ECR_REGISTRY }}"
          
          # Login to ACR
          az config set core.collect_telemetry=false
          az acr login --name "${{ vars.PROD_AZ_ACR_REGISTRY }}" --username "${{ secrets.AZ_ACR_USERNAME }}" --password "${{ secrets.AZ_ACR_PASSWORD }}"
          
          # custom rolling pants cache
          export PANTS_NAMED_CACHES_DIR="$HOME/.cache/pants/named_caches/period_$((10#$(date +%j) / 10))"
          
          # image tag is used to publish the docker images
          export IMAGE_TAG="${{ needs.publish-hotfix-github-tag.outputs.next_hotfix_release_tag }}.amd64"
          export GITHUB_RELEASE_TAG="${{ needs.publish-hotfix-github-tag.outputs.next_hotfix_release_tag }}.amd64"

          pants \
            --tag=amd_only,build_amd \
            --tag=remote_image \
            --filter-target-type="docker_image" \
            --publish-output=docker_images_published.json \
            publish "${{ github.event.inputs.pants-docker-image-target }}_registry"
          
          # Output the docker_images_published.json file's names array in GITHUB_OUTPUT
          docker_images=$([ -f ./docker_images_published.json ] && jq -r '.[].names[]' docker_images_published.json | tr '\n' ' ' || echo "")
          echo "$docker_images" >> amd64_docker_images.txt
          

      - name: Upload published images file
        uses: actions/upload-artifact@v4
        with:
          name: amd64_docker_images
          path: amd64_docker_images.txt
          retention-days: 1
          compression-level: 0

  publish-hotfix-docker-arm:
    runs-on: se-mono-arm-runners
    needs: [ publish-hotfix-github-tag ]
    outputs:
      docker-images-published: ${{ steps.publish.outputs.docker-images-published }}
    container:
      image: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-ci:latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 25
          lfs: true
          fetch-tags: true

      - name: Disable git repository validation
        # this is done to avoid pants failure which fails to recognize the git repo
        run: |
          git config --global --add safe.directory '*'

      - name: validation
        run: |
          # custom rolling pants cache
          export PANTS_NAMED_CACHES_DIR="$HOME/.cache/pants/named_caches/period_$((10#$(date +%j) / 10))"
          # unit tests
          pants \
            --tag=arm_only,build_arm \
            --changed-since=${{ github.event.inputs.github-tag }} \
            --changed-dependents=transitive \
            test

      - name: Publish requested hotfix docker image
        id: publish
        run: |
          # Erase stale AWS credentials
          rm -rf ~/.aws
          
          # Create or replace the docker config file in GitHub temp directory
          export DOCKER_CONFIG="${{ runner.temp }}/.docker"
          mkdir -p "$DOCKER_CONFIG"
          echo '{}' > "$DOCKER_CONFIG/config.json"
          
          # Login to ECR
          aws ecr get-login-password | docker login --username AWS --password-stdin "${{ vars.PROD_AWS_ECR_REGISTRY }}"
          
          # Login to ACR
          az config set core.collect_telemetry=false
          az acr login --name "${{ vars.PROD_AZ_ACR_REGISTRY }}" --username "${{ secrets.AZ_ACR_USERNAME }}" --password "${{ secrets.AZ_ACR_PASSWORD }}"
          
          # custom rolling pants cache
          export PANTS_NAMED_CACHES_DIR="$HOME/.cache/pants/named_caches/period_$((10#$(date +%j) / 10))"
          
          # image tag is used to publish the docker images
          export IMAGE_TAG="${{ needs.publish-hotfix-github-tag.outputs.next_hotfix_release_tag }}.arm64"
          export GITHUB_RELEASE_TAG="${{ needs.publish-hotfix-github-tag.outputs.next_hotfix_release_tag }}.arm64"

          pants \
            --tag=-amd_only \
            --tag=remote_image \
            --filter-target-type="docker_image" \
            --publish-output=docker_images_published.json \
            publish "${{ github.event.inputs.pants-docker-image-target }}_registry"
          
          # Output the docker_images_published.json file's names array in GITHUB_OUTPUT
          docker_images=$([ -f ./docker_images_published.json ] && jq -r '.[].names[]' docker_images_published.json | tr '\n' ' ' || echo "")
          echo "$docker_images" >> arm64_docker_images.txt

      - name: Upload published images file
        uses: actions/upload-artifact@v4
        with:
          name: arm64_docker_images
          path: arm64_docker_images.txt
          retention-days: 1
          compression-level: 0

  publish-hotfix-docker-manifests:
    runs-on: ubuntu-latest
    needs: [ publish-hotfix-docker-amd, publish-hotfix-docker-arm ]
    steps:
      - name: Download ARM published images
        uses: actions/download-artifact@v4
        with:
          name: arm64_docker_images
          path: ./image_description

      - name: Download AMD published images
        uses: actions/download-artifact@v4
        with:
          name: amd64_docker_images
          path: ./image_description

      - name: Publish Docker Manifests
        run: |
          # create or replace the docker config file in github temp directory
          export DOCKER_CONFIG="${{ runner.temp }}/.docker"
          mkdir -p $DOCKER_CONFIG
          echo '{}' > $DOCKER_CONFIG/config.json

          # login to ECR
          aws configure set aws_access_key_id ${{ secrets.AWS_PROD_ECR_ACCESS_KEY_ID }}
          aws configure set aws_secret_access_key ${{ secrets.AWS_PROD_ECR_SECRET_ACCESS_KEY }}
          aws configure set region eu-west-1
          aws ecr get-login-password | docker login --username AWS --password-stdin ${{ vars.PROD_AWS_ECR_REGISTRY }}

          # login to ACR
          az config set core.collect_telemetry=false
          az acr login --name ${{ vars.PROD_AZ_ACR_REGISTRY }} --username ${{ secrets.AZ_ACR_USERNAME }} --password ${{ secrets.AZ_ACR_PASSWORD }}

          # get images published in needs.publish-docker-amd.outputs.docker-images-published json list into a list array variable
          IMAGES_PUBLISHED_AMD=$(cat ./image_description/amd64_docker_images.txt)
          IMAGES_PUBLISHED_ARM=$(cat ./image_description/arm64_docker_images.txt)

          echo "### Docker image manifests published" >> $GITHUB_STEP_SUMMARY

          # publish docker manifests for each image from the AMD list
          for AMD_IMAGE in $IMAGES_PUBLISHED_AMD; do
            # the manifest name is the AMD image name without the suffix .amd64
            MANIFEST_NAME=$(echo $AMD_IMAGE | sed 's/.amd64//')
            # corresponding ARM image name
            ARM_IMAGE="$MANIFEST_NAME.arm64"
            # Check if the ARM image is in the ARM list
            if [[ " ${IMAGES_PUBLISHED_ARM[@]} " =~ " ${ARM_IMAGE} " ]]; then
              docker buildx imagetools create -t $MANIFEST_NAME $AMD_IMAGE $ARM_IMAGE
              echo "$MANIFEST_NAME (amd64, arm64)" >> $GITHUB_STEP_SUMMARY
            else
              docker buildx imagetools create -t $MANIFEST_NAME $AMD_IMAGE
              echo "$MANIFEST_NAME (amd64)" >> $GITHUB_STEP_SUMMARY
            fi
          done

          # publish docker manifests for each exclusive image from the ARM list that isn't in the AMD list
          for ARM_IMAGE in $IMAGES_PUBLISHED_ARM; do
            # the manifest name is the ARM image name without the suffix .arm64
            MANIFEST_NAME=$(echo $ARM_IMAGE | sed 's/.arm64//')
            # corresponding AMD image name
            AMD_IMAGE="$MANIFEST_NAME.amd64"
            # Check if the AMD image is NOT in the AMD list
            if [[ ! " ${IMAGES_PUBLISHED_AMD[@]} " =~ " ${AMD_IMAGE} " ]]; then
              docker buildx imagetools create -t $MANIFEST_NAME $ARM_IMAGE
              echo "$MANIFEST_NAME (arm64)" >> $GITHUB_STEP_SUMMARY
            fi
          done
