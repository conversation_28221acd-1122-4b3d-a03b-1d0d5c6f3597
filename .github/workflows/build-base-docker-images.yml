# This workflow builds and published the base images to use as containers in GitHub Actions
name: Build Base Docker Images
on:
  workflow_dispatch:
defaults:
  run:
    shell: bash

jobs:
  ecr-creds:
    uses: ./.github/workflows/generate-ecr-creds.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_PROD_ECR_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_PROD_ECR_SECRET_ACCESS_KEY }}

  build-and-publish:
    runs-on: se-mono-large-runner
    needs: [ ecr-creds ]
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Build Docker Image
        run: |
          set -ex
          docker buildx create --name container --driver=docker-container --use --bootstrap
          docker login --username ${{ needs.ecr-creds.outputs.ecr-username }} --password ${{ needs.ecr-creds.outputs.ecr-password }} ${{ vars.PROD_AWS_ECR_REGISTRY }}
          DOCKER_BASE_IMAGES_DIR=docker-base-images
          
          SHA_SHORT=$(git rev-parse --short HEAD)
          # get date in format YYYYMMDD
          DATE=$(date +'%Y%m%d')
          
          # go through each Dockerfile directory in docker-base-images and build the image in each directory
          # the image name will be the same as the directory name
          for dir in $DOCKER_BASE_IMAGES_DIR/*/; do
            dir=${dir%*/}
            image_name=$(basename $dir)
            # skip images se-mono-py11-alpine-base and se-mono-py11-ci-docker-build-alpine
            if [[ $image_name == "se-mono-py11-alpine-base" || $image_name == "se-mono-py11-ci-docker-build-alpine" ]]; then
              echo "Skipping $image_name" >> $GITHUB_STEP_SUMMARY
              continue
            fi
            DOCKER_CONTEXT_DIR=$DOCKER_BASE_IMAGES_DIR/$image_name
            DOCKER_IMAGE=${{ vars.PROD_AWS_ECR_REGISTRY }}/$image_name
            echo "### $DOCKER_IMAGE" >> $GITHUB_STEP_SUMMARY
          
            DOCKER_PLATFORMS=linux/amd64,linux/arm64
          
            # if file amd_only exists in DOCKER_CONTEXT_DIR, then skip arm64 build
            if [[ -f "$DOCKER_CONTEXT_DIR/amd_only" ]]; then
                echo "Skipping arm64 build for $image_name" >> $GITHUB_STEP_SUMMARY
                DOCKER_PLATFORMS=linux/amd64
            fi
          
            # Check if the superuser stage exists in the Dockerfile
            if grep -iq "as superuser" "$DOCKER_CONTEXT_DIR/Dockerfile"; then
              # Build and push the superuser image first
              IMAGE_URI="${DOCKER_IMAGE}-superuser:$SHA_SHORT.$DATE"
              IMAGE_URI_LATEST="${DOCKER_IMAGE}-superuser:latest"
              docker buildx build --pull --builder container --platform $DOCKER_PLATFORMS --target superuser --push -t $IMAGE_URI -t $IMAGE_URI_LATEST -f $DOCKER_CONTEXT_DIR/Dockerfile $DOCKER_CONTEXT_DIR/
              echo "Superuser image Tagged $IMAGE_URI" >> $GITHUB_STEP_SUMMARY
              echo "Superuser image Tagged $IMAGE_URI_LATEST" >> $GITHUB_STEP_SUMMARY
            else
              echo "Skipping superuser image build for $image_name" >> $GITHUB_STEP_SUMMARY
            fi
          
          
            # Build and push the image with non-root user
            IMAGE_URI="${DOCKER_IMAGE}:$SHA_SHORT.$DATE"
            IMAGE_URI_LATEST="${DOCKER_IMAGE}:latest"
            docker buildx build --pull --builder container --platform $DOCKER_PLATFORMS --push -t $IMAGE_URI -t $IMAGE_URI_LATEST -f $DOCKER_CONTEXT_DIR/Dockerfile $DOCKER_CONTEXT_DIR/
            echo "Non Superuser image Tagged $IMAGE_URI" >> $GITHUB_STEP_SUMMARY
            echo "Non Superuser image Tagged $IMAGE_URI_LATEST" >> $GITHUB_STEP_SUMMARY
          
          done