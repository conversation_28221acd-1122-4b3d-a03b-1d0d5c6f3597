name: Publish Github Release

on:
  push:
    branches:
      - main

defaults:
  run:
    shell: bash

env:
  GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}


jobs:
  # Publish github tag
  publish-git-tag:
    runs-on: ubuntu-latest
    concurrency:
      # allows only single run for computing git tag and publishing it
      group: single_instance_git_tagging
    env:
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      # Release versions
      - name: Release versions
        id: release-versions
        uses: ./.github/actions/release-versions
        env:
          GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Git Tag
        run: |
          git config user.name "${GITHUB_ACTOR}"
          git config user.email "${GITHUB_ACTOR}@users.noreply.github.com"
          git tag -a "${{ steps.release-versions.outputs.current-git-tag }}" -m "Run ID: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          git push origin "${{ steps.release-versions.outputs.current-git-tag }}"
          echo "${{ steps.release-versions.outputs.current-git-tag }}" >> $GITHUB_STEP_SUMMARY

      - name: GitHub Release
        run: |
          RELEASE_LINK=`gh release create ${{ steps.release-versions.outputs.current-git-tag }} --target main --generate-notes --notes-start-tag ${{ steps.release-versions.outputs.latest-release-git-tag }}`
          echo "### Released: ${{ steps.release-versions.outputs.current-git-tag }}" >> $GITHUB_STEP_SUMMARY
          echo ${RELEASE_LINK} >> $GITHUB_STEP_SUMMARY

