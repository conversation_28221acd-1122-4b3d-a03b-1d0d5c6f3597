name: Publish Release Docker Image

on:
  workflow_dispatch:
    inputs:
      github-release-tag:
        description: Github Release tag
        required: true
        type: string
      docker-image-names:
        description: Docker image names
        required: true
        type: string


# only single docker image publish per github release tag
concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.github-release-tag }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

env:
  GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  PUBLISH_DOCKER_COMMIT_PATTERN: '^([a-z0-9\-]+(\|[a-z0-9\-]+)*)$' # https://regex101.com/r/cQFlRq/1
  PANTS_CONFIG_FILES: pants.ci.toml


jobs:
  publish-check:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.check.outputs.image-tag }}
    steps:
      - name: Check github release is valid
        id: check
        run: |
          set -ex
          # validation of github release tag, exists on 404
          gh api "repos/${{ github.repository }}/releases/tags/${{ github.event.inputs.github-release-tag }}" --silent || ( echo "Github release tag not found" && exit 1 )
          
          # validation of commit message only for push events on feature branches
          if [[ "${{ github.event.inputs.docker-image-names }}" =~ ${{ env.PUBLISH_DOCKER_COMMIT_PATTERN }} ]]; then
            docker_image_tag="${{ github.event.inputs.github-release-tag }}.$(date +'%Y%m%d.%H%M')"
            echo "image-tag=${docker_image_tag}" >> $GITHUB_OUTPUT
            echo "Images - ${BASH_REMATCH[1]}" >> $GITHUB_STEP_SUMMARY
            echo "Tags - ${docker_image_tag}, ${{ github.event.inputs.github-release-tag }}" >> $GITHUB_STEP_SUMMARY
          else
            echo "Invalid docker image names ${{ github.event.inputs.docker-image-names }}" >> $GITHUB_STEP_SUMMARY
            exit 1
          fi
          


  publish-docker-amd:
    uses: ./.github/workflows/publish-docker-reusable.yaml
    needs: [ publish-check ]
    with:
      github-release-tag: ${{ github.event.inputs.github-release-tag }}
      image-tag: ${{ needs.publish-check.outputs.image-tag }}
      runner-label: se-mono-amd-runners
      additional-pants-options: "--tag=amd_only,build_amd"
      pants-filter-address-regex: ${{ github.event.inputs.docker-image-names }}
      aarch: amd64
      registry-cloud: ecr-acr
    secrets:
      AZ_ACR_PASSWORD: ${{ secrets.AZ_ACR_PASSWORD }}
      AZ_ACR_USERNAME: ${{ secrets.AZ_ACR_USERNAME }}
      GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
      JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
      JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}

  publish-docker-arm:
    uses: ./.github/workflows/publish-docker-reusable.yaml
    needs: [ publish-check ]
    with:
      github-release-tag: ${{ github.event.inputs.github-release-tag }}
      image-tag: ${{ needs.publish-check.outputs.image-tag }}
      runner-label: se-mono-arm-runners
      additional-pants-options: "--tag=-amd_only"
      aarch: arm64
      pants-filter-address-regex: ${{ github.event.inputs.docker-image-names }}
      registry-cloud: ecr-acr
    secrets:
      AZ_ACR_PASSWORD: ${{ secrets.AZ_ACR_PASSWORD }}
      AZ_ACR_USERNAME: ${{ secrets.AZ_ACR_USERNAME }}
      GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
      JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
      JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}

  publish-docker-manifests:
    runs-on: ubuntu-latest
    needs: [ publish-docker-amd, publish-docker-arm ]
    steps:
      - name: Download ARM published images
        uses: actions/download-artifact@v4
        with:
          name: arm64_docker_images
          path: ./image_description

      - name: Download AMD published images
        uses: actions/download-artifact@v4
        with:
          name: amd64_docker_images
          path: ./image_description

      - name: Publish Docker Manifests
        run: |
          # create or replace the docker config file in github temp directory
          export DOCKER_CONFIG="${{ runner.temp }}/.docker"
          mkdir -p $DOCKER_CONFIG
          echo '{}' > $DOCKER_CONFIG/config.json
          
          # login to ECR
          aws configure set aws_access_key_id ${{ secrets.AWS_PROD_ECR_ACCESS_KEY_ID }}
          aws configure set aws_secret_access_key ${{ secrets.AWS_PROD_ECR_SECRET_ACCESS_KEY }}
          aws configure set region eu-west-1
          aws ecr get-login-password | docker login --username AWS --password-stdin ${{ vars.PROD_AWS_ECR_REGISTRY }}
          
          # login to ACR
          az config set core.collect_telemetry=false
          az acr login --name ${{ vars.PROD_AZ_ACR_REGISTRY }} --username ${{ secrets.AZ_ACR_USERNAME }} --password ${{ secrets.AZ_ACR_PASSWORD }}
          
          # get images published in needs.publish-docker-amd.outputs.docker-images-published json list into a list array variable
          IMAGES_PUBLISHED_AMD=$(cat ./image_description/amd64_docker_images.txt)
          IMAGES_PUBLISHED_ARM=$(cat ./image_description/arm64_docker_images.txt)
          
          echo "### Docker image manifests published" >> $GITHUB_STEP_SUMMARY
          
          # publish docker manifests for each image from the AMD list
          for AMD_IMAGE in $IMAGES_PUBLISHED_AMD; do
            # the manifest name is the AMD image name without the suffix .amd64
            MANIFEST_NAME=$(echo $AMD_IMAGE | sed 's/.amd64//')
            # corresponding ARM image name
            ARM_IMAGE="$MANIFEST_NAME.arm64"
            # Check if the ARM image is in the ARM list
            if [[ " ${IMAGES_PUBLISHED_ARM[@]} " =~ " ${ARM_IMAGE} " ]]; then
              docker buildx imagetools create -t $MANIFEST_NAME $AMD_IMAGE $ARM_IMAGE
              echo "$MANIFEST_NAME (amd64, arm64)" >> $GITHUB_STEP_SUMMARY
            else
              docker buildx imagetools create -t $MANIFEST_NAME $AMD_IMAGE
              echo "$MANIFEST_NAME (amd64)" >> $GITHUB_STEP_SUMMARY
            fi
          done
          
          # publish docker manifests for each exclusive image from the ARM list that isn't in the AMD list
          for ARM_IMAGE in $IMAGES_PUBLISHED_ARM; do
            # the manifest name is the ARM image name without the suffix .arm64
            MANIFEST_NAME=$(echo $ARM_IMAGE | sed 's/.arm64//')
            # corresponding AMD image name
            AMD_IMAGE="$MANIFEST_NAME.amd64"
            # Check if the AMD image is NOT in the AMD list
            if [[ ! " ${IMAGES_PUBLISHED_AMD[@]} " =~ " ${AMD_IMAGE} " ]]; then
              docker buildx imagetools create -t $MANIFEST_NAME $ARM_IMAGE
              echo "$MANIFEST_NAME (arm64)" >> $GITHUB_STEP_SUMMARY
            fi
          done
