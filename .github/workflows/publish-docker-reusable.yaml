name: Publish Docker Reusable Workflow

on:
  workflow_call:
    secrets:
      JFROG_PYPI_USERNAME:
        required: true
      JFROG_PYPI_PASSWORD:
        required: true
      GH_SE_BOT_TOKEN:
        required: true
      AZ_ACR_USERNAME:
        required: true
      AZ_ACR_PASSWORD:
        required: true

    inputs:
      runner-label:
        required: true
        type: string
        description: github runner label
      github-release-tag:
        required: true
        type: string
        description: current git tag
      image-tag:
        required: true
        type: string
        description: docker image tag
      additional-pants-options:
        required: false
        type: string
        description: additional pants options
      aarch:
        required: true
        type: string
        description: architecture
      registry-cloud:
        required: true
        type: string
        description: registry cloud
      pants-filter-address-regex:
        required: true
        type: string
        description: pants filter address regex

    outputs:
      docker-images-published:
        description: "Docker images published"
        value: ${{ jobs.publish.outputs.docker-images-published }}

defaults:
  run:
    shell: bash

env:
  GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}
  JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
  PUBLISH_DOCKER_COMMIT_PATTERN: '\[publish-docker\|(changed|src\/py\/.+\/BUILD:.+)\].*' # https://regex101.com/r/OZDLcc/1
  PANTS_CONFIG_FILES: pants.ci.toml
  # se-mono-py11-base
  SE_MONO_PY11_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-base:latest
  SE_MONO_PY11_BASE_SUPERUSER: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-base-superuser:latest
  # se-mono-py11-ffmpeg-base
  SE_MONO_PY11_FFMPEG_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-py11-ffmpeg-base:latest
  # aries-ml-py11-base
  ARIES_ML_PY11_BASE: ${{ vars.PROD_AWS_ECR_REGISTRY }}/aries-ml-py11-base:latest
  ARIES_ML_PY11_BASE_SUPERUSER: ${{ vars.PROD_AWS_ECR_REGISTRY }}/aries-ml-py11-base-superuser:latest
  ECR_ACR_REGISTRIES: '{"ecr_prod":{"address":"698897937809.dkr.ecr.eu-west-1.amazonaws.com","default":"true"},"acr_prod":{"address":"acrsteeleyehub.azurecr.io","default":"true"}}'
  ECR_REGISTRY: '{"ecr_prod":{"address":"698897937809.dkr.ecr.eu-west-1.amazonaws.com","default":"true"}}'
  ACR_REGISTRY: '{"acr_prod":{"address":"acrsteeleyehub.azurecr.io","default":"true"}}'

jobs:
  # Publish docker images
  publish:
    runs-on: ${{ inputs.runner-label }}
    container:
      image: ${{ vars.PROD_AWS_ECR_REGISTRY }}/se-mono-ci:latest
    outputs:
      docker-images-published: ${{ steps.publish.outputs.docker-images-published }}
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
          ref: '${{ inputs.github-release-tag }}'
          fetch-tags: true
          fetch-depth: 0

      - name: Disable git repository validation
        # this is done to avoid pants failure which fails to recognize the git repo
        run: |
          git config --system --add safe.directory '*'


      - name: Publish images
        id: publish
        run: |
          set -ex
          # Erase stale AWS credentials
          rm -rf ~/.aws
          
          # Create or replace the docker config file in GitHub temp directory
          export DOCKER_CONFIG="${{ runner.temp }}/.docker"
          mkdir -p "$DOCKER_CONFIG"
          echo '{}' > "$DOCKER_CONFIG/config.json"
          
          # Login to ECR
          aws ecr get-login-password | docker login --username AWS --password-stdin "${{ vars.PROD_AWS_ECR_REGISTRY }}"
          
          # Login to ACR
          az config set core.collect_telemetry=false
          az acr login --name "${{ vars.PROD_AZ_ACR_REGISTRY }}" --username "${{ secrets.AZ_ACR_USERNAME }}" --password "${{ secrets.AZ_ACR_PASSWORD }}"
          
          # custom rolling pants cache
          CACHE_DIRS="$HOME/.cache/pants"
          CURRENT_CACHE_DIR="$CACHE_DIRS/named_caches/period_$((10#$(date +%j) / 10))"
          
          # delete old caches if new 10-day period detected
          [ ! -d "$CURRENT_CACHE_DIR" ] && echo "New 10-day period detected. Clearing old caches..." && rm -rf "$CACHE_DIRS"
          
          
          # PANTS_NAMED_CACHES_DIR env var is recognized by pants to configure the named caches directory
          export PANTS_NAMED_CACHES_DIR="$CURRENT_CACHE_DIR"
          
          # Image tag used to publish the Docker images, which is qualified with the aarch to differentiate between the images
          export IMAGE_TAG="${{ inputs.image-tag }}.${{ inputs.aarch }}"
          export GITHUB_RELEASE_TAG="${{ inputs.github-release-tag }}.${{ inputs.aarch }}"
          
          # specify `docker.registries` in pants.toml via the ENV variable PANTS_DOCKER_REGISTRIES, based on the registry-cloud
          if [[ "${{ inputs.registry-cloud }}" == "acr" ]]; then
            export PANTS_DOCKER_REGISTRIES='${{ env.ACR_REGISTRY }}'
          elif [[ "${{ inputs.registry-cloud }}" == "ecr" ]]; then
            export PANTS_DOCKER_REGISTRIES='${{ env.ECR_REGISTRY }}'
          elif [[ "${{ inputs.registry-cloud }}" == "ecr-acr" ]]; then
            export PANTS_DOCKER_REGISTRIES='${{ env.ECR_ACR_REGISTRIES }}'
          fi
          
          pants \
            ${{ inputs.additional-pants-options }} \
            --tag=remote_image \
            --filter-target-type="docker_image" \
            --filter-address-regex='${{ inputs.pants-filter-address-regex }}' \
            --publish-output=docker_images_published.json \
            publish ::
          
          # echo the contents of docker_images_published.json if it exists
          if [ -f ./docker_images_published.json ]; then
              cat docker_images_published.json
          fi
          
          
          # Output the docker_images_published.json file's names array in GITHUB_OUTPUT
          docker_images=$([ -f ./docker_images_published.json ] && jq -r '.[].names[]' docker_images_published.json | tr '\n' ' ' || echo "")
          echo "$docker_images" >> ${{ inputs.aarch }}_docker_images.txt

      - name: Upload published images file
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.aarch }}_docker_images
          path: ${{ inputs.aarch }}_docker_images.txt
          retention-days: 1
          compression-level: 0

      - name: Cleanup
        id: cleanup
        run: |
          # delete pants local cache, because we are using remote cache
          rm -rf $HOME/.cache/pants/lmdb_store
