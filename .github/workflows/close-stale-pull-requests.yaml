name: Close stale PRs
on:
  workflow_dispatch:
  schedule:
    - cron: 0 0 * * 1 # Every Monday at 00:00
jobs:
  close_stale_pull_requests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          # Options: https://github.com/actions/stale#list-of-input-options
          close-pr-message: |
            This PR was automatically closed after 30 days of inactivity.
          days-before-pr-stale: 30
          days-before-pr-close: 0
          operations-per-run: 500
