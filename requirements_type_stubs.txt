# Contains sorted list of type stubs to prevent mypy failures
# do not pin versions for these, as they will be auto resolved based on the version of libs

boto3-stubs
botocore-stubs
data-science-types
pandas-stubs
ratelimit-stubs
sqlalchemy-stubs
types-Jinja2
types-PyYAML
types-cachetools
types-click
types-mock
types-openpyxl
types-orjson
types-paramiko
types-psutil
types-psycopg2
types-python-dateutil
types-pytz
types-requests
types-tqdm
types-urllib3
types-xmltodict