[GLOBAL]
dynamic_ui = false
colors = true
level = "info"
remote_cache_read = true
remote_cache_write = true
# this is private IP address of se-mono-cache ec2 instance in nonprod account
remote_store_address = "grpc://*************:9092"
# disable local cache, since remote cache is enabled
local_cache = false
remote_cache_rpc_timeout_millis = 60000
remote_store_rpc_timeout_millis = 120000
session_end_tasks_timeout = 120000
remote_store_rpc_retries = 3
remote_store_chunk_bytes = ******** # 25MiB
remote_store_batch_api_size_limit = ******** # 10iMB
remote_cache_warnings = "always"

[pytest]
args = ["-vv", "--no-header"]

[python-repos]
indexes = ["https://pypi.org/simple/", "https://%(env.JFROG_PYPI_USERNAME)s:%(env.JFROG_PYPI_PASSWORD)<EMAIL>/artifactory/api/pypi/pypi-local/simple"]

#[docker.registries.ecr_prod]
#address = "************.dkr.ecr.eu-west-1.amazonaws.com"
#default = true
## because we used buildx output type=registry to directly push to registry
#skip_push = true
#
#[docker.registries.acr_prod]
#address = "acrsteeleyehub.azurecr.io"
#default = true
## because we used buildx output type=registry to directly push to registry
#skip_push = true