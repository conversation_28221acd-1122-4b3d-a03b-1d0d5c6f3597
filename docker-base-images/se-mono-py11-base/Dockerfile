# Base stage: update mirrors based on TARGETPLATFORM
FROM ubuntu:25.04 AS base
ARG TARGETPLATFORM
# Update apt sources using a common script so it’s not repeated later
RUN set -ex; \
    case "${TARGETPLATFORM}" in \
        "linux/amd64") \
            sed -i 's|http://archive.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources && \
            sed -i 's|http://security.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
        "linux/arm64") \
            sed -i 's|http://ports.ubuntu.com/ubuntu-ports/|http://eu-west-1.ec2.ports.ubuntu.com/ubuntu-ports/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac


# First Stage: Builder - Compile and install Python
FROM base AS builder
ARG DEBIAN_FRONTEND=noninteractive

# Install required dependencies
RUN apt update && \
    apt install -y --no-install-recommends \
      curl ca-certificates

# Define Python version and platform
ARG PBS_PY_VERSION=3.11.12
ARG PBS_RELEASE_TAG=20250517
ARG TARGETPLATFORM

# Determine PLATFORM_ARCH based on TARGETPLATFORM
RUN case "${TARGETPLATFORM}" in \
        "linux/amd64") PLATFORM_ARCH="x86_64-unknown-linux-gnu" ;; \
        "linux/arm64") PLATFORM_ARCH="aarch64-unknown-linux-gnu" ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac \
    && mkdir -p /usr/src/python \
    && curl -L -o python.tar.gz \
    "https://github.com/astral-sh/python-build-standalone/releases/download/${PBS_RELEASE_TAG}/cpython-${PBS_PY_VERSION}+${PBS_RELEASE_TAG}-${PLATFORM_ARCH}-install_only_stripped.tar.gz" \
    && tar -axvf python.tar.gz -C /usr/src/python --strip-components=1 \
    && rm python.tar.gz

# Second Stage: Runtime(superuser) - Install runtime dependencies and copy Python
FROM base AS superuser
ARG DEBIAN_FRONTEND=noninteractive

RUN apt update && \
      apt upgrade -y && \
      apt install -y --no-install-recommends \
        ca-certificates && \
      rm -rf /var/lib/apt/lists/*

# Copy the installed Python binary and libraries from builder
COPY --from=builder /usr/src/python /usr/local

# patch setuptools
RUN /usr/local/bin/python -m pip install --no-cache-dir \
      --upgrade setuptools

RUN groupadd -r -g 9000 app && useradd --no-log-init -r -u 9000 -g app -m app
WORKDIR /home/<USER>

# Third Stage: Final - Create a non-root user and switch context
FROM superuser AS final
USER app
