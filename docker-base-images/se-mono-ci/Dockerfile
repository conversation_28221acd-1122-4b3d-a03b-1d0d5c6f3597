FROM ubuntu:24.10
ARG DEBIAN_FRONTEND=noninteractive
ARG PBS_PY_VERSION=3.11.12
ARG PBS_RELEASE_TAG=20250517
ARG TARGETPLATFORM
# Update software mirrors based on platform
RUN case "${TARGETPLATFORM}" in \
    "linux/amd64") \
        sed -i 's|http://archive.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources && \
        sed -i 's|http://security.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
    "linux/arm64") \
        sed -i 's|http://ports.ubuntu.com/ubuntu-ports/|http://eu-west-1.ec2.ports.ubuntu.com/ubuntu-ports/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
    *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac

# Install base dependencies and add deadsnakes PPA
RUN apt update && apt upgrade -y && \
    apt install -y \
        build-essential \
        ca-certificates \
        clang \
        curl \
        git \
        git-lfs \
        golang \
        jq \
        libmagic-dev \
        libpq-dev \
        librdkafka-dev \
        libssl-dev \
        libxml2 \
        libxml2-dev \
        libxmlsec1-dev \
        libxmlsec1-openssl \
        openssl \
        pkg-config \
        postgresql \
        postgresql-client \
        rsync \
        unzip \
    && rm -rf /var/lib/apt/lists/*



# Download and install FFmpeg based on platform
ENV FFMPEG_BIN_PATH=/usr/local/bin/ffmpeg
ENV FFPROBE_BIN_PATH=/usr/local/bin/ffprobe
RUN set -ex; \
    TMP_FFMPEG=$(mktemp -d) && \
    case "${TARGETPLATFORM}" in \
      "linux/amd64") FFMPEG_URL="https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2024-01-31-12-54/ffmpeg-n6.1.1-1-g61b88b4dda-linux64-gpl-6.1.tar.xz" ;; \
      "linux/arm64") FFMPEG_URL="https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2024-01-31-12-54/ffmpeg-n6.1.1-1-g61b88b4dda-linuxarm64-gpl-6.1.tar.xz" ;; \
      *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    curl -fsSL -o "$TMP_FFMPEG/ffmpeg.tar.xz" "$FFMPEG_URL" && \
    tar -xJf "$TMP_FFMPEG/ffmpeg.tar.xz" -C "$TMP_FFMPEG" --strip-components=1 && \
    mv "$TMP_FFMPEG/bin/ffmpeg" $FFMPEG_BIN_PATH && \
    mv "$TMP_FFMPEG/bin/ffprobe" $FFPROBE_BIN_PATH && \
    rm -rf "$TMP_FFMPEG"


# Pre-install Pants for faster CI
RUN curl --proto '=https' --tlsv1.2 -fsSL https://static.pantsbuild.org/setup/get-pants.sh | bash -s -- -d /usr/local/bin

# Install Docker (combine repository setup and package installation)
RUN mkdir -m 0755 -p /etc/apt/keyrings && \
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc && \
    chmod a+r /etc/apt/keyrings/docker.asc && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt update && \
    apt install -y docker-ce containerd.io && \
    rm -rf /var/lib/apt/lists/*

# Install AWS CLI
RUN set -ex; \
    case "${TARGETPLATFORM}" in \
      "linux/amd64") AWS_CLI_URL="https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" ;; \
      "linux/arm64") AWS_CLI_URL="https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" ;; \
      *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    curl -fsSL --retry 5 "$AWS_CLI_URL" -o awscliv2.zip && \
    unzip -q awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws

# Install GitHub CLI
RUN curl -fsSL --retry 5 https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg && \
    chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null && \
    apt update && apt install -y gh && \
    rm -rf /var/lib/apt/lists/*


# Determine PLATFORM_ARCH based on TARGETPLATFORM
RUN case "${TARGETPLATFORM}" in \
        "linux/amd64") PLATFORM_ARCH="x86_64-unknown-linux-gnu" ;; \
        "linux/arm64") PLATFORM_ARCH="aarch64-unknown-linux-gnu" ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac \
    && curl -L -o python.tar.gz \
    "https://github.com/astral-sh/python-build-standalone/releases/download/${PBS_RELEASE_TAG}/cpython-${PBS_PY_VERSION}+${PBS_RELEASE_TAG}-${PLATFORM_ARCH}-install_only_stripped.tar.gz" \
    && tar -axvf python.tar.gz -C /usr/local/ --strip-components=1 \
    && rm -rf python.tar.gz


# Install Azure CLI via pip
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11 && \
    python3.11 -m pip install --no-cache-dir azure-cli



