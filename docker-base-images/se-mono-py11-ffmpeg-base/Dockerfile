FROM ubuntu:latest AS builder
ARG DEBIAN_FRONTEND=noninteractive
ARG TARGETPLATFORM


# Update software mirrors based on platform
RUN case "${TARGETPLATFORM}" in \
        "linux/amd64") \
            sed -i 's|http://archive.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources && \
            sed -i 's|http://security.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
        "linux/arm64") \
            sed -i 's|http://ports.ubuntu.com/ubuntu-ports/|http://eu-west-1.ec2.ports.ubuntu.com/ubuntu-ports/|g' /etc/apt/sources.list.d/ubuntu.sources ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac


# Install necessary dependencies
RUN apt update && \
    apt install -y --no-install-recommends \
      build-essential \
      curl \
      ca-certificates \
      unzip \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV FFMPEG_BIN_PATH=/usr/local/bin/ffmpeg
ENV FFPROBE_BIN_PATH=/usr/local/bin/ffprobe

# Download and install FFmpeg based on platform
RUN mkdir -p /tmp/ffmpeg_tmp && \
    case "${TARGETPLATFORM}" in \
    "linux/amd64") \
        curl -L -o /tmp/ffmpeg_tmp/ffmpeg.tar.xz https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2024-01-31-12-54/ffmpeg-n6.1.1-1-g61b88b4dda-linux64-gpl-6.1.tar.xz ;; \
    "linux/arm64") \
        curl -L -o /tmp/ffmpeg_tmp/ffmpeg.tar.xz https://github.com/BtbN/FFmpeg-Builds/releases/download/autobuild-2024-01-31-12-54/ffmpeg-n6.1.1-1-g61b88b4dda-linuxarm64-gpl-6.1.tar.xz ;; \
    *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    tar -xvJf /tmp/ffmpeg_tmp/ffmpeg.tar.xz -C /tmp/ffmpeg_tmp --strip-components=1 && \
    mv /tmp/ffmpeg_tmp/bin/ffmpeg $FFMPEG_BIN_PATH && \
    mv /tmp/ffmpeg_tmp/bin/ffprobe $FFPROBE_BIN_PATH && \
    rm -rf /tmp/ffmpeg_tmp