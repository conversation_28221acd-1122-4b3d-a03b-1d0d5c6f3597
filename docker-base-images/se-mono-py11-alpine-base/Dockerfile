# Stage 1: Build Python 3.11
FROM alpine:3.21 AS builder

# Install necessary build dependencies (no caching)
RUN apk add --no-cache \
      build-base \
      libffi-dev \
      openssl-dev \
      bzip2-dev \
      zlib-dev \
      xz-dev \
      linux-headers \
      curl \
      sqlite-dev


RUN mkdir -p /usr/src && cd /usr/src && \
    curl -fsSL https://www.python.org/ftp/python/3.11.11/Python-3.11.11.tgz | tar -xz && \
    cd Python-3.11.11 && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make install -j$(nproc)

# Stage 2: Minimal Final Image
FROM alpine:3.21 AS intermediate

# Copy only necessary parts of Python from the builder stage
COPY --from=builder /usr/local /usr/local

# Update & upgrade system packages without caching
RUN apk add --no-cache \
      curl \
      libffi \
      openssl \
      bzip2 \
      zlib \
      xz \
      libbz2 \
      ca-certificates \
      sqlite-libs && \
    curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11 && \
    python3.11 -m pip install --no-cache-dir --upgrade cryptography setuptools



FROM alpine:3.21 AS final

# copy all installed packages from intermediate stage
COPY --from=intermediate /usr /usr
# copy all ssl certs from intermediate stage
COPY --from=intermediate /etc/ssl/certs/ /etc/ssl/certs/

# Create a non-root user securely
RUN addgroup -g 9000 -S app && adduser -u 9000 -G app -h /home/<USER>

# Switch to non-root user
USER app:app

