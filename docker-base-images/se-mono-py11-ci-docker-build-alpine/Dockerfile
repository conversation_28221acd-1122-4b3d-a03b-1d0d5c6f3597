# Stage 1: Build Python 3.11
FROM alpine:3.21 AS builder

# Install necessary build dependencies (no caching)
RUN apk add --no-cache \
      build-base \
      libffi-dev \
      openssl-dev \
      bzip2-dev \
      zlib-dev \
      xz-dev \
      linux-headers \
      curl \
      sqlite-dev \
      coreutils \
      bash \
      wget \
      git \
      git-lfs \
      librdkafka-dev \
      jq \
      libpq-dev \
      rsync \
      gnupg \
      libmagic \
      docker \
      github-cli

# Build and install Python 3.11
RUN mkdir -p /usr/src && cd /usr/src && \
    curl -fsSL https://www.python.org/ftp/python/3.11.11/Python-3.11.11.tgz | tar -xz && \
    cd Python-3.11.11 && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make -j$(nproc) install && \
    cd / && rm -rf /usr/src/Python-3.11.11

# Install pip and essential Python packages
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11 && \
    python3.11 -m pip install --no-cache-dir --upgrade cryptography setuptools azure-cli

# Install AWS CLI v2
ARG TARGETPLATFORM
RUN case "${TARGETPLATFORM}" in \
    "linux/amd64") \
        curl -fsSL --retry 5 "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" ;; \
    "linux/arm64") \
        curl -fsSL --retry 5 "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip" ;; \
    *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    unzip -q awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws

# Install Go
RUN case "${TARGETPLATFORM}" in \
    "linux/amd64") \
        curl -fsSL --retry 5 https://go.dev/dl/go1.24.0.linux-amd64.tar.gz | tar -C /usr/local -xzf - ;; \
    "linux/arm64") \
        curl -fsSL --retry 5 https://go.dev/dl/go1.24.0.linux-arm64.tar.gz | tar -C /usr/local -xzf - ;; \
    *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac
ENV PATH=$PATH:/usr/local/go/bin

# Pre-install Pants for faster CI
RUN curl --proto '=https' --tlsv1.2 -fsSL https://static.pantsbuild.org/setup/get-pants.sh | bash -s -- -d /usr/local/bin

