FROM pytorch/pytorch:2.6.0-cuda12.6-cudnn9-runtime AS superuser
ARG DEBIAN_FRONTEND=noninteractive
ARG TARGETPLATFORM

# Create user, update software mirrors, install dependencies, and set up Conda environment in a single step
RUN groupadd -r -g 9000 app && \
    useradd --no-log-init -r -u 9000 -g app -m app && \
    case "${TARGETPLATFORM}" in \
        "linux/amd64") \
            sed -i 's|http://archive.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list && \
            sed -i 's|http://security.ubuntu.com/ubuntu/|http://eu-west-1.ec2.archive.ubuntu.com/ubuntu/|g' /etc/apt/sources.list ;; \
        "linux/arm64") \
            sed -i 's|http://ports.ubuntu.com/ubuntu-ports/|http://eu-west-1.ec2.ports.ubuntu.com/ubuntu-ports/|g' /etc/apt/sources.list ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    apt update && \
    apt upgrade -y && \
    rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /home/<USER>

# Use superuser stage and switch to non-root user
FROM superuser
USER app