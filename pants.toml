[GLOBAL]
pants_version = "2.26.0"
unmatched_build_file_globs = "error"
pythonpath = ["%(buildroot)s/pants-plugins"]
backend_packages.add = [
    "pants.backend.docker",
    "pants.backend.python",
    "pants.backend.python.providers.experimental.python_build_standalone",
    "pants.backend.build_files.fmt.ruff",
    "pants.backend.experimental.python.lint.ruff.check",
    "pants.backend.experimental.python.lint.ruff.format",
    "pants.backend.python.typecheck.mypy",
    "pants.backend.docker.lint.hadolint",
]
pants_ignore.add = ["!.no_exist/", "docker-base-images/**", "tools/**", "ci-setup/**", "docs/**", ]
colors = true
build_file_prelude_globs = ["pants-plugins/macros.py"]
enable_target_origin_sources_blocks = true

[pex]
emit_warnings = true

[export]
resolve = ['python-default']

[anonymous-telemetry]
enabled = false

[source]
marker_filenames = ["SOURCE_ROOT"]

[docker]
use_buildx = false
env_vars = ["DOCKER_CONFIG"]
build_args = ["IMAGE_TAG", "GITHUB_RELEASE_TAG"]
build_verbose = true

[dockerfile-parser]
use_rust_parser = true

[python]
enable_resolves = true
interpreter_constraints = ["==3.11.*"]
# latest pip versions are faster
pip_version = "latest"

[generate-lockfiles]
diff = true

[pytest]
install_from_resolve = "python-default"
requirements = ["//:test_libs"]

[ruff]
version = "0.11.10"
config = "3rdparty/python/config/ruff.toml"
known_versions = [
    "0.11.10|macos_arm64|1c4187c5ecd76535b76e991af1114d9c9947fe69e6d1a69c9ed6723d6777a53e|10431736",
    "0.11.10|macos_x86_64|25f0a8b819ea5c9377be02fd98c363ffb5659c35358881a1ce909f30b362cb39|11069844",
    "0.11.10|linux_arm64|acddbf808ba83c9dda01391ddd1c7f0c23d36f1f2cbbee6290d98363f3e4cafc|10499607",
    "0.11.10|linux_x86_64|8765d2f71c422f60a1456a2b2d9b2ee2ab286cb4859a774b5efb0a837a07f6dd|11638304",
]

[mypy]
install_from_resolve = "python-default"
interpreter_constraints = ["==3.11.*"]
config = "3rdparty/python/config/mypy.ini"
requirements = [
    "//:tools_libs#mypy",
    "//:3rdparty#pydantic",
    "//:type_stubs#sqlalchemy-stubs",
    "//:3rdparty#click",
]

[hadolint]
config = "3rdparty/docker/config/hadolint.yaml"

[python-bootstrap]
names = ["python3.11"]

[python-infer]
unowned_dependency_behavior = "error"
use_rust_parser = true

[pex-cli]
version = "v2.39.0"
known_versions = [
    "v2.39.0|macos_arm64|3b7391011bb0eebb8d8bb1cc3e6ad577ea5c9a38ab0667a75cf9c272d366faef|4830546",
    "v2.39.0|macos_x86_64|3b7391011bb0eebb8d8bb1cc3e6ad577ea5c9a38ab0667a75cf9c272d366faef|4830546",
    "v2.39.0|linux_x86_64|3b7391011bb0eebb8d8bb1cc3e6ad577ea5c9a38ab0667a75cf9c272d366faef|4830546",
    "v2.39.0|linux_arm64|3b7391011bb0eebb8d8bb1cc3e6ad577ea5c9a38ab0667a75cf9c272d366faef|4830546",
]
