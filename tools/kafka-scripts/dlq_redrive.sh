#!/bin/bash

# Variables for easy configuration
STACK="dev-blue"
BROKER="kafka.$STACK.steeleye.co:9094"
SOURCE_TOPIC="aries.stream.events.dlq"
TARGET_TOPIC="aries.stream.events"

# Log starting
echo "Starting to copy messages from $SOURCE_TOPIC to $TARGET_TOPIC on $STACK at $(date)"

# Check if there are any messages in the DLQ
if ! kcat -b $BROKER -C -t $SOURCE_TOPIC -e -o beginning -c 1 | read line; then
    echo "No messages to copy from $SOURCE_TOPIC on $STACK. Exiting..."
    exit 0
fi

# Perform the copy operation
kcat -b $BROKER -C -t $SOURCE_TOPIC -K'#' -e -o beginning | kcat -b $BROKER -P -t $TARGET_TOPIC -K'#'

# Check if the pipe command was successful
if [ $? -eq 0 ]; then
    echo "Successfully copied messages from $SOURCE_TOPIC to $TARGET_TOPIC on $STACK at $(date)"
else
    echo "Failed to copy messages from $SOURCE_TOPIC to $TARGET_TOPIC on $STACK at $(date)"
    exit 1
fi
