#!/bin/bash

# Kafka topic and stack configuration
STACK="prod-shell"
TOPIC="aries.oma.events.dlq"

# Retention times in milliseconds
NEW_RETENTION_MS=1000
DEFAULT_RETENTION_MS=604800000

# Kafka and kcat paths (change these if your installation paths are different)
KAFKA_PATH="/opt/homebrew/opt/kafka/bin/kafka-configs"
KCAT_PATH="kcat"

# Kafka server address
BOOTSTRAP_SERVER="kafka.$STACK.steeleye.co:9094"

# Alter retention period to 1000ms to expedite deletion of messages
$KAFKA_PATH --alter --add-config retention.ms=$NEW_RETENTION_MS --bootstrap-server=$BOOTSTRAP_SERVER --topic $TOPIC

# Check if topic is empty and sleep in between checks
while true; do
    # Check if any messages are left in the topic
    # The '-e' flag makes kcat exit when it reaches the end of the topic
    # The '-c 1' limits the output to the first message
    if $KCAT_PATH -b $BOOTSTRAP_SERVER -C -t $TOPIC -e -o beginning -c 1 | read line; then
        echo "Messages still present in $STACK in $TOPIC. Checking again in 10 seconds..."
        sleep 10
    else
        echo "No more messages in the topic. Restoring the original retention period."
        break
    fi
done

# Restore the original retention period
$KAFKA_PATH --alter --add-config retention.ms=$DEFAULT_RETENTION_MS --bootstrap-server=$BOOTSTRAP_SERVER --topic $TOPIC
