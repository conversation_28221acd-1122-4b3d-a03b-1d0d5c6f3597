import logging
import pathlib
import polars as pl
from aries_se_api_client.client import AriesApiClient
from efdh_tasks.tick_parquet_converter.config import settings
from efdh_tasks.tick_parquet_converter.static import (
    AUCTION_FILE_DTYPES,
    INPUT_FILE_DTYPES,
    ORIGINAL_CURRENCY,
    QUOTE_FILE_DTYPES,
    TRADE_FILE_DTYPES,
    FileNameTemplates,
    Resolution,
)
from market_data_utils.schema.currency import CurrencyMappings
from market_data_utils.schema.parquet import (
    SEQ_NO_COLUMN_MAP,
    AuctionTickInputColumns,
    NewAuctionColumns,
    NewQuoteColumns,
    QuoteTickInputColumns,
    TradeTickInputColumns,
)
from market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from pendulum.date import Date
from reference_api_client.ric import Ric
from typing import Any, Dict, List, Optional, Tuple, Type

logger = logging.getLogger(__name__)


class NormaliseTickError(Exception):
    pass


class NormaliseTickSkip(Exception):
    pass


class NormaliseTick:
    """Transforms raw RIC csv file data to parquet and normalises columns and
    rows."""

    def __init__(
        self,
        ric: str,
        date: Date,
        input_filepath: str,
        bucket: str,
        existing_files: Dict[str, Dict[str, str]],
        target_dir: pathlib.Path,
        currency: Optional[str],
        append: bool = True,
        currency_norm_override: Optional[bool] = None,
    ):
        logger.info(f"NormaliseTick args: {ric} | {input_filepath}")
        self.ric = ric
        self.date = date
        self.bucket = bucket
        self.input_filepath = input_filepath
        self.append_data = append
        self.existing_files = existing_files
        self.target_dir = target_dir
        self.currency_norm_override = currency_norm_override
        self.currency_raw = currency if currency else self.get_ric_currency()

    def process(
        self,
    ) -> Optional[Dict[str, Any]]:
        try:
            files = []
            failed_count = 0

            lf = self.read_lf(
                input_file_path=self.input_filepath,
                dtypes=INPUT_FILE_DTYPES,
            )

            lf = lf.with_columns(
                pl.col(RefinitivExtractColumns.DATE_TIME)
                .cast(pl.Datetime("ns"))
                .cast(pl.Int64)
                .name.keep()
            )
            lf = lf.cast({RefinitivExtractColumns.GMT_OFFSET: pl.Int64})

            try:
                trades_files, primary_trades_ccy = self.process_trades(
                    trades=lf,
                )
                self.currency_raw = primary_trades_ccy if primary_trades_ccy else self.currency_raw
                files += trades_files
            except Exception as e:
                logger.exception(e)
                failed_count += 1

            try:
                files += self.process_quotes(quotes=lf)
            except Exception as e:
                logger.exception(e)
                failed_count += 1

            try:
                files += self.process_auctions(auctions=lf)
            except Exception as e:
                logger.exception(e)
                failed_count += 1

            return {"ric": self.ric, "files": files, "failed_count": failed_count}

        except NormaliseTickSkip as e:
            logger.exception(f"Exception occurred while processing ric: {self.ric}.")
            raise NormaliseTickError(f"Failed to run parquet converter for ric: {self.ric}") from e

    def process_trades(
        self,
        trades: pl.LazyFrame,
    ) -> Tuple[List, Optional[str]]:
        """Takes the full CSV, filters to valid trade rows, normalises currencies
           appends to existing parquet, resamples to min/hr intervals and writes
           3 local files.
        Args:
            trades (pl.LazyFrame): Full input data
            append_data (bool): Append to existing parquet and deduplicate
            ric (str): RIC code for the instrument (for output file name)
            date (str): Date from file (for output file name)
            output_path (pathlib.Path): Target output file
            bucket (str): S3 bucket of output
            output_prefix (str): S3 key prefix of output

        Returns:
            Tuple[List, Union[None, Coverage], str]: CopyLocalObjects for each file written,
            coverage, and currency code derived from data
        """
        copy_local_objects = []
        output_file_name = self._format_daily_filename(
            event_type=RefinitivEventType.TRADE,
            resolution=Resolution.TICK,
            ric=self.ric,
            date=self.date,
        )
        trades = self.filter_by_event_type(lf=trades, event_type=RefinitivEventType.TRADE)
        if trades.head(1).collect().is_empty():
            return [], None
        trades = trades.rename(SEQ_NO_COLUMN_MAP)
        trades = self.remove_invalid_rows(
            lf=trades, columns=[RefinitivExtractColumns.PRICE, RefinitivExtractColumns.VOLUME]
        )

        trades = self.fill_empty_ccy(
            lf=trades,
            column=RefinitivExtractColumns.TRADE_PRICE_CURRENCY,
        )
        trades = self._normalise_currency(
            lf=trades,
            ccy_col=RefinitivExtractColumns.TRADE_PRICE_CURRENCY,
            price_cols=[RefinitivExtractColumns.PRICE],
        )

        existing_file: Optional[str] = self.existing_files.get(RefinitivEventType.TRADE, {}).get(
            "local_destination"
        )
        if self.append_data and existing_file:
            existing = self.read_lf(
                input_file_path=existing_file,
                fmt="parquet",
                dtypes=TRADE_FILE_DTYPES,
            )
            trades = self.append_to_existing(
                trades,
                existing=existing,
                invalid_subset=[RefinitivExtractColumns.PRICE, RefinitivExtractColumns.VOLUME],
                dtypes=TRADE_FILE_DTYPES,
            )

        df = trades.collect()

        if df.is_empty():
            return [], None

        df, primary_ccy = self.retain_max_representation(
            df=df, ccy_col=RefinitivExtractColumns.TRADE_PRICE_CURRENCY
        )

        df = self._drop_rows_by_mad_score(df)

        if df.is_empty():
            return [], None

        copy_local_objects.append(
            self.write_local(
                df=df,
                file_name=output_file_name,
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=TRADE_FILE_DTYPES,
            )
        )

        minutely = self.resample(
            df=df,
            col=RefinitivExtractColumns.DATE_TIME,
            period=Resolution.MINUTE,
            event=RefinitivEventType.TRADE,
        )

        copy_local_objects.append(
            self.write_local(
                df=minutely,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.TRADE,
                    resolution=Resolution.MINUTE,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=TRADE_FILE_DTYPES,
            )
        )

        hourly = self.resample(
            df=df,
            col=RefinitivExtractColumns.DATE_TIME,
            period=Resolution.HOUR,
            event=RefinitivEventType.TRADE,
        )

        copy_local_objects.append(
            self.write_local(
                df=hourly,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.TRADE,
                    resolution=Resolution.HOUR,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=TRADE_FILE_DTYPES,
            )
        )

        return copy_local_objects, primary_ccy

    def process_quotes(
        self,
        quotes: pl.LazyFrame,
    ) -> List[Dict[str, str]]:
        """Takes the full CSV, filters to valid quotes rows, normalises currencies
           appends to existing parquet, resamples to min/hr intervals and writes
           3 local files.
        Args:
            quotes (pl.LazyFrame): Full input data
            append_data (bool): Append to existing parquet and deduplicate
            preferred_ccy (str): Currency from the ric lookup index
            ric (str): RIC code for the instrument (for output file name)
            date (str): Date from file (for output file name)
            output_path (pathlib.Path): Target output file
            bucket (str): S3 bucket of output
            output_prefix (str): S3 key prefix of output

        Returns:
            Tuple[List, Union[None, Coverage]]: CopyLocalObjects for each file written, coverage
        """

        logger.debug("Processing quotes")
        copy_local_objects = []
        quotes = self.filter_by_event_type(lf=quotes, event_type=RefinitivEventType.QUOTE)
        if quotes.head(1).collect().is_empty():
            return []
        # Valid bid/ask only
        quotes = self.remove_invalid_rows(
            lf=quotes,
            columns=[
                RefinitivExtractColumns.BID_PRICE,
                RefinitivExtractColumns.ASK_PRICE,
            ],
        )

        # Non-zero sizes only
        quotes = quotes.filter(
            (
                (pl.col(RefinitivExtractColumns.ASK_SIZE).is_null())
                | (pl.col(RefinitivExtractColumns.ASK_SIZE).ne(pl.lit(0.0)))
            )
            & (
                (pl.col(RefinitivExtractColumns.BID_SIZE).is_null())
                | (pl.col(RefinitivExtractColumns.BID_SIZE).ne(pl.lit(0.0)))
            )
        )
        quotes = self.fill_empty_ccy(quotes, column=NewQuoteColumns.QUOTE_PRICE_CURRENCY)
        # Fill Mid Price
        quotes = self.fill_mid_price(quotes)

        # Convert to major if needed and then add currency column
        quotes = self.min_maj_conversion(
            lf=quotes,
            price_cols=[
                RefinitivExtractColumns.ASK_PRICE,
                RefinitivExtractColumns.BID_PRICE,
                RefinitivExtractColumns.MID_PRICE,
            ],
            ccy_col=NewQuoteColumns.QUOTE_PRICE_CURRENCY,
        )

        existing_file: Optional[str] = self.existing_files.get(RefinitivEventType.QUOTE, {}).get(
            "local_destination"
        )
        if self.append_data and existing_file:
            existing = self.read_lf(
                input_file_path=existing_file,
                fmt="parquet",
                dtypes=QUOTE_FILE_DTYPES,
            )

            quotes = self.append_to_existing(
                quotes,
                existing=existing,
                invalid_subset=[
                    RefinitivExtractColumns.BID_PRICE,
                    RefinitivExtractColumns.ASK_PRICE,
                ],
                dtypes=QUOTE_FILE_DTYPES,
            )

        df = quotes.collect()

        if df.is_empty():
            logger.warning(f"empty dataframe for {self.ric} | {self.date}")
            return []

        copy_local_objects.append(
            self.write_local(
                df=df,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.QUOTE,
                    resolution=Resolution.TICK,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=QUOTE_FILE_DTYPES,
            )
        )

        minutely = self.resample(
            df=df,
            col=RefinitivExtractColumns.DATE_TIME,
            period=Resolution.MINUTE,
            event=RefinitivEventType.QUOTE,
        )

        copy_local_objects.append(
            self.write_local(
                df=minutely,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.QUOTE,
                    resolution=Resolution.MINUTE,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=QUOTE_FILE_DTYPES,
            )
        )

        hourly = self.resample(
            df=df,
            col=RefinitivExtractColumns.DATE_TIME,
            period=Resolution.HOUR,
            event=RefinitivEventType.QUOTE,
        )

        copy_local_objects.append(
            self.write_local(
                df=hourly,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.QUOTE,
                    resolution=Resolution.HOUR,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=QUOTE_FILE_DTYPES,
            )
        )

        return copy_local_objects

    def process_auctions(
        self,
        auctions: pl.LazyFrame,
    ) -> List[Dict[str, str]]:
        """Takes the full CSV, filters to valid quotes rows, normalises currencies
           appends to existing parquet, resamples to min/hr intervals and writes
           3 local files.
        Args:
            quotes (pl.LazyFrame): Full input data
            append_data (bool): Append to existing parquet and deduplicate
            preferred_ccy (str): Currency from the ric lookup index
            ric (str): RIC code for the instrument (for output file name)
            date (str): Date from file (for output file name)
            output_path (pathlib.Path): Target output file
            bucket (str): S3 bucket of output
            output_prefix (str): S3 key prefix of output

        Returns:
            Tuple[List, Union[None, Coverage]]: CopyLocalObjects for each file written, coverage
        """

        logger.debug("Processing quotes")
        copy_local_objects = []
        auctions = self.filter_by_event_type(lf=auctions, event_type=RefinitivEventType.AUCTION)
        if auctions.head(1).collect().is_empty():
            return []
        auctions = auctions.rename(SEQ_NO_COLUMN_MAP)

        # Valid bid/ask only
        auctions = self.remove_invalid_rows(
            lf=auctions,
            columns=[
                RefinitivExtractColumns.PRICE,
                RefinitivExtractColumns.VOLUME,
            ],
        )
        auctions = self.fill_empty_ccy(auctions, column=NewAuctionColumns.AUCTION_PRICE_CURRENCY)
        # Convert to major if needed and then add currency column
        auctions = self.min_maj_conversion(
            lf=auctions,
            price_cols=[
                RefinitivExtractColumns.IND_AUCTION_PRICE,
                RefinitivExtractColumns.PRICE,
            ],
            ccy_col=NewAuctionColumns.AUCTION_PRICE_CURRENCY,
        )

        existing_file: Optional[str] = self.existing_files.get(RefinitivEventType.AUCTION, {}).get(
            "local_destination"
        )
        if self.append_data and existing_file:
            existing = self.read_lf(
                input_file_path=existing_file,
                fmt="parquet",
                dtypes=AUCTION_FILE_DTYPES,
            )

            auctions = self.append_to_existing(
                auctions,
                existing=existing,
                invalid_subset=[
                    RefinitivExtractColumns.PRICE,
                    RefinitivExtractColumns.VOLUME,
                ],
                dtypes=AUCTION_FILE_DTYPES,
            )

        df = auctions.collect()

        if df.is_empty():
            return []

        copy_local_objects.append(
            self.write_local(
                df=df,
                file_name=self._format_daily_filename(
                    event_type=RefinitivEventType.AUCTION,
                    resolution=Resolution.TICK,
                    ric=self.ric,
                    date=self.date,
                ),
                _dir=self.target_dir,
                output_prefix=self.target_dir,
                dtypes=AUCTION_FILE_DTYPES,
            )
        )

        return copy_local_objects

    def min_maj_conversion(
        self,
        lf: pl.LazyFrame,
        ccy_col: str,
        price_cols: List[str],
    ) -> pl.LazyFrame:
        """If the preferred_currency is minor, divide all the columns in
        price_cols by 100 and return an updated LazyFrame. NOT APPLICABLE TO
        TRADES!

        Args:
            lf (pl.LazyFrame): Input data
            price_cols (List[str]): List of names of columns that contain prices
            preferred_currency (str): The currency of the prices

        Returns:
            pl.LazyFrame: Updated LazyFrame
        """

        if self.currency_norm_override is not None and not self.currency_norm_override:
            return lf.with_columns(
                pl.lit(
                    CurrencyMappings.MINOR_TO_MAJOR_CCY.get(self.currency_raw, self.currency_raw)  # type: ignore
                ).alias(ccy_col)
            )

        logger.debug("Converting quotes to major")

        if self.currency_raw in CurrencyMappings.MINOR_TO_MAJOR_CCY or self.currency_norm_override:
            for column in price_cols:
                lf = lf.with_columns((pl.col(column) / 100).name.keep())

        final_ccy = CurrencyMappings.MINOR_TO_MAJOR_CCY.get(self.currency_raw, self.currency_raw)  # type: ignore

        return lf.with_columns(pl.lit(final_ccy).alias(ccy_col))

    def read_lf(
        self,
        input_file_path: str | pathlib.Path,
        dtypes: Optional[Dict[str, Type[pl.DataType]]] = None,
        fmt: str = "csv",
    ) -> pl.LazyFrame:
        """Gets the LazyFrame from S3.

        Args:
            bucket (str): S3 bucket containing the target file
            key (str): S3 key of the target file
            target_dir (pathlib.Path): Directory in which the file should be stored
            dtypes (Dict[str, pl.datatypes.PolarsDataType]): A dictionary of
                the columns and their desired datatypes
            file_name (str, optional): The local file name. Defaults to s3_key[-1].
            fmt (str, optional): "csv" or "parquet" file to be read. Defaults to "csv".

        Raises:
            NotImplementedError: If you choose a
                format that is neither csv or parquet, this will be raised

        Returns:
            pl.LazyFrame: The LazyFrame you wanted to download
        """
        logger.debug("Getting input file from local filesystem")
        if fmt == "csv":
            return pl.scan_csv(input_file_path, dtypes=dtypes)  # type: ignore
        elif fmt == "parquet":
            lf = pl.scan_parquet(input_file_path)
            if not dtypes:
                return lf
            lf = lf.with_columns([pl.col(col).cast(dtype=dtype) for col, dtype in dtypes.items()])
            return lf
        else:
            raise NotImplementedError(f"Format {fmt} not recognised")

    def remove_invalid_rows(self, lf: pl.LazyFrame, columns: List[str]) -> pl.LazyFrame:
        """Remove rows which are null or 0 on any of the columns given.

        Args:
            lf (pl.LazyFrame): Input data
            columns (List[str]): Columns which,
            if they are null or 0, the entire row will be dropped

        Returns:
            pl.LazyFrame: Updated LazyFrame with nulls removed in requested columns
        """
        logger.debug("Removing invalid rows")
        lf = lf.drop_nulls(subset=columns)
        for column in columns:
            lf = lf.filter(pl.col(column).ne(0))
        return lf

    def fill_empty_ccy(self, lf: pl.LazyFrame, column: str) -> pl.LazyFrame:
        """If the currency column is null, fill it with the preferred currency.

        Args:
            lf (pl.LazyFrame): Input data
            column (str): Currency column
            preferred_ccy (str): Currency value to fill in

        Returns:
            pl.LazyFrame: Updated LazyFrame with currency always populated
        """
        logger.debug("Filling missing ccys")
        if column not in lf.collect_schema().names():
            return lf.with_columns(pl.lit(self.currency_raw).alias(column))
        return lf.with_columns(
            pl.when(pl.col(column).is_null())
            .then(pl.lit(self.currency_raw))
            .otherwise(pl.col(column))
            .name.keep()
        )

    def _normalise_currency(
        self, lf: pl.LazyFrame, price_cols: List[str], ccy_col: str
    ) -> pl.LazyFrame:
        """If the preferred_currency is minor, divide all the columns in
        price_cols by 100 and return an updated LazyFrame.

        Args:
            lf (pl.LazyFrame): Input data
            price_cols (List[str]): List of names of columns that contain prices
            ccy_col (str): The column which contains the currency of the prices

        Returns:
            pl.LazyFrame: Updated LazyFrame with normalised currencies
        """
        lf = lf.with_columns(pl.col(ccy_col).alias(ORIGINAL_CURRENCY))
        for column in price_cols:
            lf = lf.with_columns(
                pl.when(pl.col(ccy_col).is_in(list(CurrencyMappings.MINOR_TO_MAJOR_CCY.keys())))
                .then(pl.col(column) / 100)
                .otherwise(pl.col(column))
                .alias(column)
            )

        lf = lf.with_columns(
            pl.col(ccy_col).replace(CurrencyMappings.MINOR_TO_MAJOR_CCY, default=pl.col(ccy_col))
        )
        return lf

    def fill_mid_price(self, lf: pl.LazyFrame) -> pl.LazyFrame:
        """Generate the mid price (mean of bid and ask price) where it does not
        already exist.

        Args:
            lf (pl.LazyFrame): Input LazyFrame

        Returns:
            pl.LazyFrame: Output LazyFrame with mid price
        """
        logger.debug("Filling mid price")
        lf = lf.with_columns(
            pl.when(pl.col(RefinitivExtractColumns.MID_PRICE).is_null())
            .then(
                (
                    pl.col(RefinitivExtractColumns.ASK_PRICE)
                    + pl.col(RefinitivExtractColumns.BID_PRICE)
                )
                / 2
            )
            .otherwise(pl.col(RefinitivExtractColumns.MID_PRICE))
            .alias(RefinitivExtractColumns.MID_PRICE)
        )
        return lf

    def append_to_existing(
        self,
        lf: pl.LazyFrame,
        existing: pl.LazyFrame,
        invalid_subset: List[str],
        dtypes: Optional[Dict[str, type[pl.DataType]]],
    ) -> pl.LazyFrame:
        """Append to the existing parquet file, deduplicate (prioritise most
        recent row) and remove invalid rows again.

        Args:
            lf (pl.LazyFrame): The LazyFrame from the current run
            existing (pl.LazyFrame): The LazyFrame of the previous parquet
            invalid_subset (List[str]): Columns which,
                if they are null or 0, the entire row will be dropped
        Returns:
            pl.LazyFrame: Combined LazyFrame, which is unique and valid
        """
        logger.debug("Appending to existing")
        lf = lf.cast(dtypes=dtypes)  # type: ignore
        common_cols = list(set(lf.columns) & set(existing.columns))
        lf = pl.concat([existing, lf], how="diagonal").unique(keep="last", subset=common_cols)
        lf = self.remove_invalid_rows(lf, columns=invalid_subset)
        return lf

    def resample(
        self,
        df: pl.DataFrame,
        period: str,
        col: str = RefinitivExtractColumns.DATE_TIME,
        event: Optional[str] = "UNKNOWN",
    ) -> pl.DataFrame:
        """_summary_

        Args:
            df (pl.DataFrame): Input data
            period (str): Resampling period (MINUTE/HOUR)
            event (Optional[str]): TRADE OR QUOTE (for logs, not needed)
            col (Optional[str]): The column which will be used to resample (must be i64 timestamp).
            Defaults to RefinitivExtractColumns.DATE_TIME.

        Returns:
            pl.DataFrame: _description_
        """
        logger.debug(f"Resampling {event} to {period} on {col}")
        resample_options = {"MINUTE": 1e9 * 60, "HOUR": 1e9 * 3600}
        df = df.with_columns(
            ((pl.col(col) / resample_options[period]).ceil() * resample_options[period])
            .cast(pl.Int64)
            .alias("_group")
        )
        df = df.group_by("_group").map_groups(lambda g: self.tail_with_count(g))
        df = df.drop("_group")
        return df

    def write_local(
        self,
        df: pl.DataFrame,
        file_name: str,
        _dir: pathlib.Path,
        output_prefix,
        dtypes: Optional[Dict[str, type[pl.DataType]]] = None,
    ) -> Dict[str, str]:
        """Writes the Polars dataframe to a local dir.

        Args:
            df (pl.DataFrame): Polars DataFrame
            file_name (str): Local file name
            _dir (pathlib.Path): Local dir in which to save
            bucket (str): S3 Bucket for copy local object info
            output_prefix (_type_): S3 parquet key

        Returns:
            CopyLocalObjectInfo: Object with local file and remote target file for parquet uploader
        """
        logger.debug("Writing file")
        output_file_path = _dir.joinpath(file_name).absolute()
        try:
            df = df.cast(dtypes=dtypes)  # type: ignore
        except Exception:
            pass
        df.write_parquet(output_file_path, use_pyarrow=True)
        return {
            "local_destination": str(output_file_path),
            "remote_key": f"{output_prefix}/{file_name}",
        }

    def filter_by_event_type(
        self, lf: pl.LazyFrame, event_type: RefinitivEventType
    ) -> pl.LazyFrame:
        """Filter to RefinitivExtractColumns.TYPE = event_type

        Args:
            lf (pl.LazyFrame): Input LazyFrame
            event_type (str): Event type to filter to

        Returns:
            pl.LazyFrame: Filtered LazyFrame
        """
        logger.debug("Filtering by event")
        match event_type:
            case RefinitivEventType.QUOTE:
                columns = QuoteTickInputColumns().get_columns()

            case RefinitivEventType.TRADE:
                columns = TradeTickInputColumns().get_columns()

            case RefinitivEventType.AUCTION:
                columns = AuctionTickInputColumns().get_columns()

        events = lf.filter(pl.col(RefinitivExtractColumns.TYPE).eq(event_type.value)).select(
            columns
        )
        return events

    def retain_max_representation(
        self, df: pl.DataFrame, ccy_col: str
    ) -> Tuple[pl.DataFrame, Optional[str]]:
        """Filter Polars DataFrame to the most common currency.

        Args:
            df (pl.DataFrame): Input DataFrame
            ccy_col (str): Column that contains the target currency codes

        Raises:
            Exception: If there is more than one currency in the DataFrame, raise an Exception

        Returns:
            pl.DataFrame: Filtered DataFrame
        """
        logger.debug("Retaining currency with max representation")

        if list(df[ccy_col].unique()) == [None]:
            logger.exception(f"{self.ric} missing currency")
            return df.drop(ORIGINAL_CURRENCY), None

        ccys = (
            df.group_by(ccy_col)
            .count()
            .sort("count")
            .select(pl.col(ccy_col).last())
            .get_column(ccy_col)
            .to_list()
        )

        original_ccys = (
            df.group_by(ORIGINAL_CURRENCY)
            .count()
            .sort("count")
            .select(pl.col(ORIGINAL_CURRENCY).last())
            .get_column(ORIGINAL_CURRENCY)
            .to_list()
        )
        df = df.drop(ORIGINAL_CURRENCY)
        if len(ccys) == 1 and len(original_ccys) == 1:
            ccy = ccys[0]
            original_ccy = original_ccys[0]
        else:
            raise Exception("Missing currency")

        return df.filter(pl.col(ccy_col).eq(ccy)), original_ccy

    @staticmethod
    def _format_daily_filename(
        event_type: RefinitivEventType, resolution: Resolution, ric: str, date: Date
    ) -> str:
        """Generates the output file name from the args.

        Args:
            event_type (RefinitivEventType): TRADE or QUOTE
            resolution (Resolution): TICK, MINUTE, HOUR
            ric (str): RIC code from input S3 key
            date (pendulum.date): Date from input S3 key

        Returns:
            str: File name
        """
        filename = FileNameTemplates.OUTPUT_FILENAME_TPL.format(
            ric=ric,
            event_type=event_type.value.upper(),
            resolution=resolution.value.upper(),
            year=date.year,
            month=date.month,
            day=date.day,
        )
        return filename

    def _drop_rows_by_mad_score(self, trades: pl.DataFrame) -> pl.DataFrame:
        """Calculate the median absolute deviation of the price, and filter to
        where it is -10 -> 10, or invalid. Invalid data is included because it
        likely means there was not enough data to build a reliable model. This
        unreliable model would usually also be unreliable if it was the old Z
        score.

        Args:
            trades: Input frame

        Returns: Updated frame with outliers on PRICE removed
        """

        logger.debug("dropping by mad score")

        head = trades.head(-100)
        tail = trades.tail(100)

        median_price = head[RefinitivExtractColumns.PRICE].median()

        tail = tail.with_columns(
            (
                (pl.col(RefinitivExtractColumns.PRICE) - median_price)
                / ((pl.col(RefinitivExtractColumns.PRICE) - median_price).median()).abs().median()
            ).alias("mad_score")
        )

        tail = tail.filter(
            pl.col("mad_score").is_between(-20, 20)
            | pl.col("mad_score").is_null()
            | pl.col("mad_score").is_nan()
            | pl.col("mad_score").is_infinite()
        )

        tail = tail.drop("mad_score")

        return pl.concat([head, tail])

    @staticmethod
    def tail_with_count(temp_df: pl.DataFrame) -> pl.DataFrame:
        _len = len(temp_df)
        temp_df = temp_df.tail(1)
        return temp_df.with_columns(pl.lit(_len).alias("Count").cast(pl.Int64))

    def get_ric_currency(self) -> Optional[str]:
        try:
            ric_api = Ric(client=AriesApiClient(host=settings.SE_REFERENCE_API_URL))
            result: Optional[str] = ric_api.get_currency(self.ric)["currencyRaw"]
            return result
        except Exception as e:
            logger.warning(f"Unable to fetch currencies for ric {self.ric}: {str(e)}")
            return None
