# type: ignore
import logging
import os
import pandas as pd
from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.repository.elastic import EsRepositorySync
from api_sdk.repository.syncronous.request_bound import BoundRepository
from api_sdk.utils.elastic import ElasticClient
from api_tasks.api_task_status_update import run_api_task_status_update
from aries_io_event.io_param import IOParamFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from case_bulk_workflow.case_attach_records import (
    CaseAttachRecordsByFilterTask,
    CaseAttachRecordsByIdsTask,
)
from case_bulk_workflow.case_delete_records import (
    CaseDeleteRecordsByFilterTask,
    CaseDeleteRecordsByIdsTask,
)
from case_bulk_workflow.schemas.case_bulk_in import CaseBulkRecordIn
from case_bulk_workflow.schemas.common import case_bulk_models_map
from data_platform_config_api_client.tenant import TenantAPI
from enum import Enum
from se_elastic_schema.static.provenance import TaskStatus
from se_enums.elastic_search import EsActionEnum
from typing import Dict, List, Union

logger = logging.getLogger(__name__)


def generate_output_param(
    records,
    aries_task_input: AriesTaskInput,
    tenant_bucket: str,
    model_name: MetaModel,
    data_model_path: str,
    es_action: str,
):
    """Generates the output parameters for uploading the records as an ndjson
    file to the cloud.

    This function creates the appropriate path where the records' is to
    be uploaded in the cloud. And It also writes the records into an
    ndjson file and returns the final output parameter.
    """

    ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket,
        aries_task_input=aries_task_input,
        model=model_name,
    )

    run_write_ndjson(
        source_serializer_result=records,
        output_filepath=ndjson_path,
    )

    output = add_nested_params(
        file_uri=ndjson_path,
        es_action=es_action,
        data_model=data_model_path,
        ignore_empty_file_uri=True,
    )
    return output


class FakeElasticReferenceClient:
    def get(self):
        return {}


class BulkOperation:
    def __init__(self, aries_task_input: AriesTaskInput):
        self.aries_task = aries_task_input
        self._tenant = aries_task_input.workflow.tenant
        self._task_name = aries_task_input.input_param.params.get("task_name", None)
        self._bulk_request = aries_task_input.input_param.params.get("bulk_request", None)

        config = ApiConfig()

        self._record_handler = BoundRepository(
            tenancy=Tenancy(tenant=self._tenant),
            es_repo=EsRepositorySync(
                api_config=config,
                elastic_client=ElasticClient(config),
                reference_client=FakeElasticReferenceClient(),
            ),
        )
        tenant_api_client: TenantAPI = TenantAPI(
            AriesApiClient(host=os.environ["DATA_PLATFORM_CONFIG_API_URL"])
        )

        self.tenant_bucket = tenant_api_client.get(
            stack_name=self.aries_task.workflow.stack, tenant_name=self.aries_task.workflow.tenant
        ).content["lake_prefix"]

    @staticmethod
    def get_enum_member_from_value(enum: Enum, value: str):
        """Retrieves an enum member corresponding to the given value."""
        value_to_member_map = enum.__dict__.get("_value2member_map_", {})
        return value_to_member_map.get(value) or value

    @staticmethod
    def flat_records_dict(records_data: Union[Dict, List[Dict]]) -> List[Dict]:
        """flat dict or list of dicts."""
        if not isinstance(records_data, List):
            records_data = [records_data]

        return [
            pd.json_normalize(data, sep=".").to_dict(orient="records")[0] for data in records_data
        ]

    @staticmethod
    def to_snake_case(obj: str):
        """converts str from camel case to snake case."""
        return "".join(["_" + i.lower() if i.isupper() else i for i in obj]).lstrip("_")

    @staticmethod
    def get_result_data(
        aries_task, _records, es_action: EsActionEnum, models_type: str, tenant_bucket: str
    ):
        """Generate result data for record models keys of '_records' param.

        Args:
            aries_task: task input
            _records (dict): A dictionary containing records for different models.
                example: {"CaseEmail": [{..}, {..}]}
            es_action (EsActionEnum): An enum representing the Elasticsearch action to perform.
            models_type (str): A string indicating the type of models (e.g., "case", "surveillance").
            tenant_bucket (str) : lake prefix (e.g., "az://iris")
        """  # noqa: E501
        result_data = []

        for model in case_bulk_models_map[models_type]:
            # Get the path of data model in se-elastic-schema
            model_path = f"se_elastic_schema.models.tenant.{models_type}.{BulkOperation.to_snake_case(model)}:{model}"  # noqa: E501
            records = _records.get(model, {})

            # If no records found, continue
            if not records:
                continue

            # Get the Enum member from value
            model_enum = BulkOperation.get_enum_member_from_value(enum=MetaModel, value=model)
            # Convert records to pandas DataFrame
            df = pd.DataFrame(BulkOperation.flat_records_dict(records))

            result_data.append(
                generate_output_param(
                    records=df,
                    aries_task_input=aries_task,
                    model_name=model_enum,
                    data_model_path=model_path,
                    es_action=es_action,
                    tenant_bucket=tenant_bucket,
                )
            )

        return result_data

    def update_api_task_status(self, status: str):
        """Updates the status value of api task status record in APITask
        postgres table."""
        run_api_task_status_update(
            params={"status": status, "task_name": "bulk_record_processor_task"},
            workflow=self.aries_task.workflow,
        )

    def execute(self):
        try:
            # Update the task status
            self.update_api_task_status(TaskStatus.PROCESSING)

            if not self._task_name:
                logger.error("Task Name not provided. Flow cannot execute further")
                raise Exception

            if not self._bulk_request:
                logger.error("bulk request params are not provided. Flow cannot execute further")
                raise Exception

            task = getattr(BulkOperation, self._task_name)
            return task(self)

        except Exception as e:
            self.update_api_task_status(TaskStatus.ERRORED)
            logger.error(f"Task: {self._task_name} had errors in execution:\n{e}")
            raise e

    def bulk_attach_records_to_case(self):
        # Validate the input
        bulk_request = CaseBulkRecordIn.parse_obj(self._bulk_request)

        task_repo = CaseAttachRecordsByIdsTask
        if bulk_request.use_filter:
            task_repo = CaseAttachRecordsByFilterTask

        logger.info(f"Initiating task repo: {task_repo}")
        attach_task: task_repo = task_repo(
            tenancy=Tenancy(tenant=self._tenant), record_handler=self._record_handler
        )

        task_output = attach_task.run(bulk_request)
        result_data = self.get_result_data(
            _records=task_output.case_records,
            es_action=EsActionEnum.INDEX,
            models_type="case",
            aries_task=self.aries_task,
            tenant_bucket=self.tenant_bucket,
        )
        result_data.extend(
            self.get_result_data(
                _records=task_output.alert_records,
                es_action=EsActionEnum.UPDATE,
                models_type="surveillance",
                aries_task=self.aries_task,
                tenant_bucket=self.tenant_bucket,
            )
        )

        output_param = IOParamFieldSet(params={"case_records": result_data})
        return AriesTaskResult(output_param=output_param)

    def bulk_delete_records_from_case(self):
        # Validate the input
        bulk_request = CaseBulkRecordIn.parse_obj(self._bulk_request)

        task_repo = CaseDeleteRecordsByIdsTask
        if bulk_request.use_filter:
            task_repo = CaseDeleteRecordsByFilterTask

        logger.info(f"Initiating task repo: {task_repo}")
        delete_task: task_repo = task_repo(
            tenancy=Tenancy(tenant=self._tenant), record_handler=self._record_handler
        )

        task_output = delete_task.run(bulk_request)
        result_data = self.get_result_data(
            _records=task_output.case_records,
            es_action=EsActionEnum.DELETE,
            models_type="case",
            aries_task=self.aries_task,
            tenant_bucket=self.tenant_bucket,
        )
        result_data.extend(
            self.get_result_data(
                _records=task_output.alert_records,
                es_action=EsActionEnum.UPDATE,
                models_type="surveillance",
                aries_task=self.aries_task,
                tenant_bucket=self.tenant_bucket,
            )
        )

        output_param = IOParamFieldSet(params={"case_records": result_data})
        return AriesTaskResult(output_param=output_param)
