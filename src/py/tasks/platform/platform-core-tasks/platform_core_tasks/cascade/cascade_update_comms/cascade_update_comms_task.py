import fsspec
import logging
import or<PERSON><PERSON>
import os
import se_schema_meta
import uuid
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_logger import AriesTaskLogInfo, configure_logging
from aries_utils.kafka_rest import KafkaRestClient
from cascade_utils import cascade_schema, cascade_utils
from collections import defaultdict
from platform_core_tasks.cascade.cascade_update_comms.config import configuration
from pydantic import ValidationError
from se_elastic_schema.components.communication.identifiers import Identifiers
from se_elastic_schema.components.communication.participant import Participant
from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeRecordValidationMixin,
)
from se_elastic_schema.models.tenant.account.account_person import AccountPerson
from se_elastic_schema.models.tenant.market.market_person import MarketPerson
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_fsspec_utils.file_utils import write
from typing import Any, Tuple

_log_info = AriesTaskLogInfo(
    stack=configuration.stack,
    task_name=configuration.task_name,
    task_version=configuration.task_version,
)
log_level = "DEBUG" if int(configuration.debug) else "INFO"
logger = logging.getLogger(configuration.task_name)
configure_logging(_log_info, logger=logger, level=log_level)

ROOT_DIR_NAME = os.path.dirname(os.path.realpath(__file__))
IDENTIFIERS_MAP = {
    "fromId": "FROM",
    "toIds": "TO",
    "ccIds": "CC",
    "bccIds": "BCC",
    "onBehalfOf": "BEHALF",
}


class CascadeUpdateComms:
    def __init__(self, aries_task_input: AriesTaskInput):
        self._tenant_name = aries_task_input.workflow.tenant
        self._aries_task_input = aries_task_input
        self._local_dir = os.path.join(ROOT_DIR_NAME, f"target_record_downloads_{uuid.uuid4()}")
        # Getting data lake output path prefix
        self._output_path_prefix = cascade_utils.get_output_path_prefix(
            workflow=self._aries_task_input.workflow,
            task=self._aries_task_input.task,
            input_param=self._aries_task_input.input_param,
        )
        self._kafka_rest_client = KafkaRestClient(configuration.kafka_rest_proxy_url)
        self._feature_flags: list[str] = []

    def evaluate_new_participant(
        self,
        target_record,
        source_record: AccountPerson | MarketPerson,
    ) -> Participant | None:
        """Returns a new participants entry if source uniqueIds are in common
        with target identifiers, evaluated by participant type.

        Args:
            target_record: (like Call, Chat, etc.)
            source_record (AccountPerson | MarketPerson): like MarketCounterparty, Person, etc.
        Returns:
            new participant (Participant | None): returns new participant if found
        Raises:
        """
        if target_record.identifiers is None:
            return None

        identifiers: Identifiers = target_record.identifiers
        src_ids = set(source_record.uniqueIds if source_record.uniqueIds is not None else [])

        added_types = []

        for identifier, participant_type in IDENTIFIERS_MAP.items():
            target_ids = self.collect_targets(
                identifiers=identifiers,
                identifier=identifier,
                model=target_record.model__,
            )
            # if there is intersection, adds participant_type to added_types
            if not src_ids.isdisjoint(set(target_ids)):
                added_types.append(participant_type)

        if added_types:
            return Participant.validate(
                {
                    "types": added_types,
                    "value": orjson.loads(
                        source_record.to_json(
                            by_alias=True,
                            exclude_none=True,
                            exclude_meta=False,
                        )
                    ),
                }
            )
        else:
            return None

    def collect_targets(
        self,
        identifiers: Identifiers,
        identifier: str,
        model: str,
    ) -> list[str]:
        """Returns a list of target identifiers. During collection converts
        identifiers to lowercase.

        If model is Call, transform phone numbers collected from identifiers to e164 format
        if `E164_CONVERT` `featureFlag` is set in tenant config.

        Args:
            identifiers (communication.identifiers.Identifiers): Identifiers of target model
            identifier (str): identifier string like TO, FROM etc.
            model (str): target model name
        Returns:
            targets (list[str])
        Raises:

        :Example:

        # from cascade_flows.tasks.batch.cascade.communications import CascadeCommunications
        # CascadeCommunications(
                identifiers={'toIds': ['+4407860685346'], 'fromId': '+440070987612'},
                item="fromId",
            )
        # ['+440070987612']
        """
        if not hasattr(identifiers, identifier):
            return []

        targets = []

        value = getattr(identifiers, identifier)

        if isinstance(value, str):
            targets.append(value.lower())

        elif isinstance(value, list):
            for s in value:
                if s:
                    targets.append(s.lower())

        # apply E164 format for Call records where tenant has E164-CONVERT feature flag active
        if targets and model.lower() == "call" and FeatureFlags.E164_CONVERT in self._feature_flags:
            import e164_converter

            transformed_phone_numbers = set()
            for phone_number in targets:
                phone_number = e164_converter.convert_number(phone_number)

                phone_number_converted = (
                    phone_number.transformed  # type: ignore[attr-defined]
                    if phone_number.transformed is not None  # type: ignore[attr-defined]
                    else phone_number.raw  # type: ignore[attr-defined]
                )
                transformed_phone_numbers.add(phone_number_converted)
            targets = sorted(transformed_phone_numbers)

        return targets

    @staticmethod
    def clear_updated_participants(
        participants: list[Participant], ids: list[str]
    ) -> list[Participant]:
        """Return new participants after removing the created/modified/deleted
        market/account person from participants [i.e., if its part of ids
        list].

        Args:
            participants (list[Participant]): list of target record participants
            ids (list[str]): list if source ids
        Returns:
            new_participants (list[Participant]): returns list of new participants
        Raises:
        """
        new_participants = []

        for person in participants:
            value = person.value
            if value.id__ not in ids:
                new_participants.append(person)

        return new_participants

    def _updated_new_participants(
        self,
        target_record,
        source_record: AccountPerson | MarketPerson,
        input_params: cascade_schema.CascadeInputParams,
    ) -> list[Participant] | None:
        """Returns a list of participants updated to be included in the target
        record.

        Args:
            target_record: (like Call, Chat, etc.)
            source_record (AccountPerson | MarketPerson): like MarketCounterparty, Person, etc.
            input_params (CommsInputParams): cascade input params
        Returns:
            new_participants (list[Participant]): returns list of new participants if any
        Raises:
        """
        old_participants = (
            target_record.participants if target_record.participants is not None else []
        )

        # if present remove the source person from comms record
        # it will be picked by "_evaluate_new_participant" if relevant
        new_participants = self.clear_updated_participants(
            participants=old_participants, ids=[input_params.sourceId]
        )

        changed = len(old_participants) != len(new_participants)

        if input_params.action != cascade_schema.ActionEnum.DELETED:
            new_participant = self.evaluate_new_participant(
                target_record=target_record,
                source_record=source_record,
            )

            if new_participant:
                new_participants.append(new_participant)
                changed = True

        return new_participants if changed else None

    @staticmethod
    def _format_bulk_records(
        es_record_id: str,
        target_record,
        index: str,
        cascade_id: str,
        new_participants: list[Participant],
    ) -> Tuple[dict[str, Any], dict[str, Any]]:
        """Normalizes target participants and generates bulk record header and
        doc strings.

        Args:
            es_record_id (str): ES record id
            target_record: Target record (like Call, Chat, etc.)
            index (str): Index name of target record
            cascade_id (str): cascade_id from cascade input params
            new_participants (list[Participant]): New participants that need to be updated
        Returns:
            es_header (str): bulk record es header
            es_doc (str): bulk record es doc
        Raises:
        """
        es_header = {
            "update": {
                "_index": index,
                "_id": es_record_id,  # Using _id file while updating
            }
        }

        normalized_participants = []
        for participant in new_participants:
            normalized_types = (
                [i.value for i in participant.types] if participant.types is not None else []
            )
            if not participant.value:
                continue
            # Pruning meta fields that startswith `&` or that endswith `__`
            # as these should not be present in update doc and also ignoring
            # nones as the action is update.
            normalized_value = cascade_utils.prune_meta(
                orjson.loads(
                    participant.value.to_json(
                        by_alias=True,
                        exclude_none=True,
                    )
                ),
            )
            normalized_participants.append({"types": normalized_types, "value": normalized_value})

        normalized_validation_errors = (
            [
                orjson.loads(i.json(by_alias=True, exclude_none=True))
                for i in target_record.validationErrors__
            ]
            if target_record.validationErrors__
            else None
        )

        # Updating cascadeId, hash and participants
        es_doc = {
            "doc": {
                se_schema_meta.ID: target_record.id__,
                se_schema_meta.CASCADE_ID: cascade_id,
                se_schema_meta.HASH: target_record.hash__,
                se_schema_meta.VALIDATION_ERRORS: normalized_validation_errors,
                "participants": normalized_participants,
            }
        }

        return es_header, es_doc

    def run(self) -> AriesTaskResult:
        """Processes each target record and publishes update event to ES
        writer.

        Args:
        Returns:
            AriesTaskResult
        Raises:
        """
        base_task_result = AriesTaskResult()
        params = cascade_schema.ProcessTaskOutParams.validate(
            self._aries_task_input.input_param.params
        )

        # Reads meta data file from object store and returns meta data: cascade_schema.MetaDataFile
        # Any error here should be fatal
        meta_data: cascade_schema.MetaDataFile = cascade_utils.get_meta_data(
            file_uri=params.meta_data_file_uri
        )

        self._feature_flags = (
            meta_data.tenant_config.featureFlags if meta_data.tenant_config.featureFlags else []
        )

        # Will hold list of bulk load records
        # Each bulk record will have 2 lines - one with es_header and another with es_doc
        bulk_records: list[dict[str, Any]] = []
        batch_metrics: dict[str, int] = {"total": 0, "errored": 0, "updated": 0}
        records_effected_by_model: defaultdict[str, int] = defaultdict(int)

        logger.info(
            "[Update Comms] Processing batch file from parent "
            f"process task, File: {params.file_uri}"
        )
        # Downloading the target ndorjson file to local and then processing it.
        fs, _, _ = fsspec.get_fs_token_paths(params.file_uri)

        with fs.open(params.file_uri, "r") as records:
            for target_es_record in records:
                target_es_record = orjson.loads(target_es_record)
                batch_metrics["total"] += 1
                target_es_record_source = target_es_record["_source"]
                target_model_name = target_es_record_source["&model"]
                target_model_ref = cascade_utils.COMMS_TARGET_MODEL_OBJ_MAP[target_model_name]
                try:
                    target_record = target_model_ref.validate(target_es_record_source)
                except ValidationError as e:
                    # Error message should be string,
                    # using loads to remove extra characters like "\n" etc
                    err_msg = orjson.dumps(orjson.loads(e.json()))
                    logger.exception(
                        f"[VALIDATION] failed for targetRecord Id: {target_es_record['_id']}, "  # type: ignore
                        f"Index: {target_es_record['_index']}, error: {err_msg}"
                    )
                    batch_metrics["errored"] += 1
                    continue

                # In comms cascade we know that source record will
                # be of only COMMS_SOURCE_MODELS types.
                # Overriding type to fix type hints.
                source_record: cascade_utils.PERSON_MODELS
                source_record = meta_data.source_record

                new_participants = self._updated_new_participants(
                    target_record=target_record,
                    source_record=source_record,
                    input_params=meta_data.workflow_io_params,
                )
                if new_participants is None:
                    # check against None since empty list is valid use case where
                    # the existing participant(s) is removed from communication record
                    continue

                target_record.participants = new_participants

                # Recalculating hash and running data validations
                target_record.hash__ = target_record.generate_steeleye_meta_hash()
                if isinstance(target_record, SteelEyeRecordValidationMixin):
                    target_record.run_steeleye_validations()

                try:
                    target_record = target_model_ref.validate(
                        orjson.loads(target_record.to_json(by_alias=True, exclude_none=True))
                    )
                except ValidationError as e:
                    # Error message should be string,
                    # using loads to remove extra characters like "\n" etc
                    err_msg = orjson.dumps(orjson.loads(e.json()))
                    logger.exception(
                        f"[VALIDATION] failed for updated targetRecord Id: "  # type: ignore
                        f"{target_es_record['_id']}, "
                        f"Index: {target_es_record['_index']}, error: {err_msg}"
                    )
                    batch_metrics["errored"] += 1
                    continue

                es_header, es_doc = self._format_bulk_records(
                    es_record_id=target_es_record["_id"],
                    target_record=target_record,
                    index=target_es_record["_index"],
                    cascade_id=meta_data.workflow_io_params.cascadeId,
                    new_participants=new_participants,
                )

                # Appending the formatted bulk records to grouped_bulk_records
                # Each grouped batch records will be processed once at the end of batch
                bulk_records.append(es_header)
                bulk_records.append(es_doc)
                records_effected_by_model[target_model_name] += 1
        logger.info(
            "[Update Comms] Completed processing batch file from parent "
            f"process task, File: {params.file_uri}"
        )

        # This processes each grouped bulk records,
        # Uploads model+batch file to data lake and publishes kafka message to
        # elastic writer topic
        cascade_utils.write_grouped_bulk_records(
            model_name=params.model,
            data_model=cascade_utils.COMMS_TARGET_MODEL_MAP[params.model],
            kafka_rest_client=self._kafka_rest_client,
            data_events_topic=configuration.data_events_topic,
            output_path_prefix=self._output_path_prefix,
            aries_task_input=self._aries_task_input,
            bulk_records=bulk_records,
            batch_metrics=batch_metrics,
        )
        logger.info(
            f"[Update Comms] Processed events from Batch. Metrics: {batch_metrics}, "
            f"RecordsEffected: {dict(records_effected_by_model)}"
        )

        stats = cascade_schema.CascadeStats(
            records_effected=records_effected_by_model, **batch_metrics
        )

        # Writing final stats to file and passing the file name in output
        # to down stream tasks.
        stats_file = f"cascade-update-comms-stats-{params.model}-{uuid.uuid4()}.json"
        output_path: str = os.path.join(self._output_path_prefix, stats_file)
        fs, _, _ = fsspec.get_fs_token_paths(output_path)
        write(fs=fs, target_path=output_path, file_content=stats.json(), write_mode="w")

        output_params = cascade_schema.UpdateTaskOutParams(file_uri=output_path)
        base_task_result.output_param = IOParamFieldSet(params=output_params.dict())

        return base_task_result


def cascade_update_comms_task_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    fetch = CascadeUpdateComms(
        aries_task_input=aries_task_input,
    )

    return fetch.run()
