import addict
import botocore.exceptions
import logging
import os
import pandas
import time
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_core_tasks.utilities.s3_utils import get_bucket_and_key_from_file_url
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task
from es_snapshots_archival_utils import archival_utils
from es_snapshots_process_index.schemas import AriesInputParams
from omegaconf import OmegaConf
from pathlib import Path
from se_s3_utils import s3_utils

logger = logging.getLogger("es-snapshot-process-index-task")
ROOT_DIR_NAME = os.path.dirname(os.path.realpath(__file__))
DEFAULT_ES_SNAPSHOTS_BUCKET_NAME = "{stack}.elastic.{aws_region}.steeleye.co"


def get_resources() -> addict.Dict:  # pragma: no cover
    resources = addict.Dict()
    resources.config = OmegaConf.load(Path(__file__).parent.parent.joinpath("config.yml"))
    resources.s3_client = s3_utils.get_s3_client()
    _root_dir = resources.config.volume_mount_path or ROOT_DIR_NAME
    resources.download_paths = archival_utils.get_download_paths(root_dir=_root_dir)
    return resources


def _post_error_slack_message(
    stack: str,
    bucket_name: str,
    snapshot_repository: str,
    s3_snapshot_path: str,
    slack_webhook_url: str,
    aries_task_name: str,
    error_message: str,
    index_name: str = "",
    index_id: str = "",
):
    title = f"Process Index File - Stack: {stack} - Repo: {snapshot_repository} - Status: Error"
    if index_name:
        field_title = (
            f"Bucket: `{bucket_name}`, Snapshot Repository Path: `{s3_snapshot_path}`, "
            f"Index Name: {index_name}, S3 Index Id: {index_id}"
        )
    else:
        field_title = f"Bucket: `{bucket_name}`, Snapshot Repository Path: `{s3_snapshot_path}`"
    logger.error(title + " " + error_message)
    archival_utils.send_slack_message(
        webhook_url=slack_webhook_url,
        field_title=field_title,
        field_message=error_message,
        message_title=title,
        service_name=aries_task_name,
        success=False,
    )


def _get_index_info_from_csv(
    stack: str,
    bucket_name: str,
    snapshot_repository: str,
    s3_tar_file_path: str,
    slack_webhook_url: str,
    aries_task_name: str,
    s3_indices_csv_file_path: str,
    csv_index_no: int,
    resources: addict.Dict,
) -> archival_utils.IndexInfo:
    """This process gets IndexNo of index in csv file to process.

    Params:
        stack (str): Stack name
        bucket_name (str): S3 bucket name
        snapshot_repository (str): Snapshot repository name
        slack_webhook_url (str): Slack webhook url
        aries_task_name (str): Task name, used in producing slack messages
        s3_indices_csv_file_path (str): S3 CSV path
        csv_index_no (str): IndexNo of index to process in CSV file
        resources (addict.Dict): addict Dict with s3_client, config, download paths etc
    Returns:
        index_info (archival_utils.IndexInfo): Index info index_name, s3_path, size_b etc
    Raises:
        botocore.exceptions.ClientError
    """
    index_info = archival_utils.IndexInfo()
    try:
        resp = s3_utils.get_object(
            s3_client=resources.s3_client,
            bucket_name=bucket_name,
            s3_key=s3_indices_csv_file_path,
        )

        # Skipping rows such that the final df will have only the row that matches csv_index_no
        # header will be at 0'th index so we can directly skip the index number we get as input
        # and the next row will be the desired row.
        df = pandas.read_csv(
            resp["Body"],
            skiprows=csv_index_no,
            nrows=1,
            header=0,
            names=list(archival_utils.IndexInfo.__fields__.keys()),
        )
        if df.empty:
            error_message = (
                f"Failed to get row using index value: `{csv_index_no}` "
                f"from {s3_indices_csv_file_path}"
            )
            _post_error_slack_message(
                stack=stack,
                bucket_name=bucket_name,
                snapshot_repository=snapshot_repository,
                s3_snapshot_path=s3_tar_file_path,
                slack_webhook_url=slack_webhook_url,
                aries_task_name=aries_task_name,
                error_message=error_message,
            )
            raise Exception(error_message)

        # df will always have only one row or else empty
        # so directly using the index number
        index_info = archival_utils.IndexInfo.validate(df.iloc[0])
    except botocore.exceptions.ClientError as e:
        error_message = f"Failed to process Index CSV File, Error: {e}"
        _post_error_slack_message(
            stack=stack,
            bucket_name=bucket_name,
            snapshot_repository=snapshot_repository,
            s3_snapshot_path=s3_tar_file_path,
            slack_webhook_url=slack_webhook_url,
            aries_task_name=aries_task_name,
            error_message=error_message,
        )
        raise e
    return index_info


def process_index_file(
    stack: str,
    bucket_name: str,
    snapshot_repository: str,
    csv_index_no: int,
    slack_webhook_url: str,
    aries_task_name: str,
    resources: addict.Dict,
):
    """This process gets IndexNo of index in csv file to process and processes
    the respective index file if tar file of index doesn't exists in S3.

    Params:
        stack (str): Stack name
        bucket_name (str): S3 bucket name
        snapshot_repository (str): Snapshot repository name
        csv_index_no (str): IndexNo of index to process in CSV file
        slack_webhook_url (str): Slack webhook url
        aries_task_name (str): Task name, used in producing slack messages
        resources (addict.Dict): addict Dict with s3_client, config, download paths etc
    Returns:
    Raises:
        botocore.exceptions.ClientError
    """
    _start_time = time.time()
    # Getting download paths
    indices_download_folder = resources.download_paths.indices_download_folder
    tarred_files_folder = resources.download_paths.tarred_files_folder

    s3_tar_file_path = archival_utils.DEFAULT_S3_TAR_FILES_PATH.format(
        stack=stack,
        snapshot_folder=snapshot_repository,
    )
    # Index csv file will be present at snapshot root path
    s3_indices_csv_file_path = os.path.join(
        os.path.dirname(s3_tar_file_path),
        archival_utils.S3_INDICES_CSV_FILE_NAME,
    )

    index_info = _get_index_info_from_csv(
        stack=stack,
        bucket_name=bucket_name,
        snapshot_repository=snapshot_repository,
        s3_tar_file_path=s3_tar_file_path,
        slack_webhook_url=slack_webhook_url,
        aries_task_name=aries_task_name,
        csv_index_no=csv_index_no,
        s3_indices_csv_file_path=s3_indices_csv_file_path,
        resources=resources,
    )
    logger.info(
        f"Started processing Index: {index_info.index_name}, IndexId: {index_info.index_id}, "
        f"CSV Index No: `{csv_index_no}`, Size: {index_info.size_hum}, "
        f"TotalObjects: {index_info.total_objects}. "
        f"Bucket: {bucket_name}, File: {s3_indices_csv_file_path}"
    )
    _, index_s3_key = get_bucket_and_key_from_file_url(file_url=index_info.s3_path)
    local_file_path = os.path.join(indices_download_folder, index_info.index_id)
    tar_file_path = archival_utils.get_tar_file_path(
        source_folder=local_file_path,
        tarred_folder=tarred_files_folder,
    )
    tar_file_name = os.path.basename(tar_file_path)
    s3_tar_upload_path = os.path.join(s3_tar_file_path, tar_file_name)
    try:
        _exists = s3_utils.file_exists(
            s3_client=resources.s3_client,
            bucket_name=bucket_name,
            s3_key=s3_tar_upload_path,
        )
        if _exists:
            # We don't need to proceed further as tar file already exists in S3
            logger.info(
                f"Index tar file already exists in S3, S3Path: {s3_tar_upload_path}. "
                "Skipping process."
            )
            return

        # Downloading all index files using aws cli
        # command `aws sync`
        _d_start_time = time.time()
        archival_utils.download_with_aws_sync(
            bucket_name=bucket_name,
            index_prefix=index_s3_key,
            local_file_path=local_file_path,
        )
        _d_time_taken = round(time.time() - _d_start_time)
        resources.process_metrics["process_index_download_time_s"] = _d_time_taken
        resources.process_metrics["process_index_download_time_hum"] = (
            archival_utils.human_readable_time(seconds=_d_time_taken)
        )

        _t_start_time = time.time()
        tar_file_path = archival_utils.create_tar_file_cli(
            source_folder=local_file_path,
            destination_folder=tarred_files_folder,
        )
        _t_time_taken = round(time.time() - _t_start_time)
        resources.process_metrics["process_index_tar_time_s"] = _t_time_taken
        resources.process_metrics["process_index_tar_time_hum"] = (
            archival_utils.human_readable_time(seconds=_t_time_taken)
        )

        # Using cli `aws cp`` command to upload instead of boto3
        # with an understanding that it handles refresh token better and
        # also should faster.
        _u_start_time = time.time()
        archival_utils.upload_with_aws_cp(
            bucket_name=bucket_name,
            upload_path=s3_tar_upload_path,
            local_file_path=tar_file_path,
            storage_class=archival_utils.S3_DEEP_ARCHIVE_STORAGE,
        )
        _u_time_taken = round(time.time() - _u_start_time)
        resources.process_metrics["process_index_upload_time_s"] = _u_time_taken
        resources.process_metrics["process_index_upload_time_hum"] = (
            archival_utils.human_readable_time(seconds=_u_time_taken)
        )
    except (
        botocore.exceptions.ClientError,
        OSError,
        archival_utils.CalledSubProcessError,
    ) as e:
        error_message = f"Failed to process Index File, Error: {e}"
        _post_error_slack_message(
            stack=stack,
            bucket_name=bucket_name,
            snapshot_repository=snapshot_repository,
            s3_snapshot_path=s3_tar_file_path,
            slack_webhook_url=slack_webhook_url,
            aries_task_name=aries_task_name,
            error_message=error_message,
            index_name=index_info.index_name,
            index_id=index_info.index_id,
        )
        raise e
    finally:
        # This clause satisfies in all cases either success or error.
        archival_utils.clean_up(files=[indices_download_folder, tarred_files_folder])

    _time_taken = round(time.time() - _start_time)
    logger.info(
        f"Completed processing Index: {index_info.index_name}, IndexId: {index_info.index_id}, "
        f"CSVIndexNo: `{csv_index_no}`, Size: {index_info.size_hum}, "
        f"TotalObjects: {index_info.total_objects}. "
        f"Bucket: {bucket_name}, File: {s3_indices_csv_file_path}. "
        f"TimeTaken: {_time_taken}"
    )

    # This will create these metrics in OMA and will also be used for filtering and analysis.
    resources.process_metrics["process_index_bucket"] = bucket_name
    resources.process_metrics["process_index_snapshot_repository"] = snapshot_repository
    resources.process_metrics["process_index_name"] = index_info.index_name
    resources.process_metrics["process_index_id"] = index_info.index_id
    resources.process_metrics["process_index_size_b"] = index_info.size_b
    resources.process_metrics["process_index_size_hum"] = index_info.size_hum
    resources.process_metrics["process_index_total_objects"] = index_info.total_objects
    resources.process_metrics["process_index_time_s"] = _time_taken
    resources.process_metrics["process_index_time_hum"] = archival_utils.human_readable_time(
        seconds=_time_taken
    )


@aries_task
def run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """Run method to process each snapshot index."""
    base_task_result = AriesTaskResult()
    app_metric = AppMetricFieldSet(
        metrics={
            "generic": {
                "output_count": 0,
                "errored_count": 0,
            }
        }
    )
    base_task_result.app_metric = app_metric

    # Getting resources needed, return addict
    # with s3_client, config, download paths etc
    resources = get_resources()

    # This throws an error if the env variables are not set
    # and run fails fast.
    aws_region = resources.config.aws_region
    slack_webhook_url = resources.config.slack.webhook_url

    # Getting stack name from workflow tenant instead of from ENV,
    # this is to support archival process on older stacks like prod-blue etc.
    stack = aries_task_input.workflow.tenant

    bucket_name = DEFAULT_ES_SNAPSHOTS_BUCKET_NAME.format(stack=stack, aws_region=aws_region)
    # Validating input params - this raises error if validation fails
    input_params = AriesInputParams.validate(aries_task_input.input_param.params)

    process_index_file(
        stack=stack,
        bucket_name=bucket_name,
        snapshot_repository=input_params.snapshot_repository,
        csv_index_no=input_params.csv_index_no,
        slack_webhook_url=slack_webhook_url,
        aries_task_name=aries_task_input.task.name,
        resources=resources,
    )

    base_task_result.app_metric.metrics["custom"] = resources.process_metrics
    return base_task_result
