import fsspec
import json
import logging
import pandas as pd
import pathlib
import tempfile
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task
from aries_utils.io_param_utils import IGNORE_EMPTY_FILE_URI, handle_empty_file_uri
from collections import defaultdict
from elastic_connector.utils import batched, get_model_for_records
from fastparquet import write as parquet_write
from integration_audit.auditor import (
    Auditor,
    AuditorStaticFields,
    get_record_identifier,
    upload_audit,
    upsert_audit,
)
from omegaconf import DictConfig, ListConfig, OmegaConf
from pathlib import Path
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_prefixed_ingest_lake_path_for_task
from se_elasticsearch.repository import ResourceConfig, get_repository_by_cluster_version
from se_enums.cloud import CloudProviderEnum
from se_es_utils.utils import es_api_retriable_call
from se_io_utils.json_utils import write_named_temporary_json
from se_schema_meta import HASH, ID
from typing import Any, Dict, List, TextIO, Union

logger = logging.getLogger("elastic-connector")
logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)

STORED_HASH = "stored_hash"
STORED_ID = "stored_id"
RESPONSE_FILENAME_SUFFIX = "raw_es_response.parquet"
SUCCESS_RESPONSE_FILENAME_SUFFIX = "raw_success.ndjson"
SUPPORTED_ES_ACTIONS = ["create", "update", "index"]


class BulkWriterColumns:
    FILE_INDEX = "file_index"
    RAW_INDEX = "raw_index"
    MODEL = "model"
    HASH = "hash"
    ID = "id"
    ES_INDEX = "es_index"
    STATUS = "status"
    ERROR_TYPE = "error_type"
    ERROR_REASON = "error_reason"


class WriteStatus:
    CREATED = "created"
    UPDATED = "updated"
    ERRORED = "errored"
    VERSION_CONFLICT = "version_conflict"
    DUPLICATE = "duplicate"


class Resources:
    _es_repo = None
    _config: Union[DictConfig, ListConfig] | None = None

    @classmethod
    def config(cls):
        if not cls._config:
            cls._config = OmegaConf.load(Path(__file__).parent.parent.joinpath("config.yml"))
        return cls._config

    @classmethod
    def es_repo(cls):
        if not cls._es_repo:
            cls._es_repo = get_repository_by_cluster_version(
                resource_config=ResourceConfig(
                    host=cls.config().elastic.host,
                    port=cls.config().elastic.port,
                    scheme=cls.config().elastic.scheme,
                )
            )
        return cls._es_repo


def audit_results(
    status_df: pd.DataFrame,
    records_df: pd.DataFrame,
    audit_path: str,
    streamed: bool,
    cloud_provider: CloudProviderEnum,
):
    """Populate the Task Audit JSON based on the ES response. Created records
    count as `created`, `version_conflict` and `duplicate` (after comparing
    `&hash`) record count as `duplicate`, `updated` and `errored` records count
    as `updated` and `errored` accordingly.

    :param status_df: Pandas DataFrame with the ES response for each record
    :param records_df: Pandas DataFrame with each record's data
    :param audit_path: Location of the audit JSON file
    :param streamed: For streamed-workflows, auditable assets are input files,
    while for non-streamed workflows they are the individual records
    :param cloud_provider: CloudProvider is required to populate streamed workflows'
    input files identifying paths correctly
    """

    data_model, update_action = get_model_for_records(records_df)

    if streamed:
        record_identifiers = []
        if update_action:
            records_list = list(records_df.loc[:, "doc"].values)
        else:
            records_list = records_df.to_dict(orient="records")

        unknown_records_count = 0

        for record in records_list:
            record_id = get_record_identifier(
                streamed=streamed,
                record=record,
                data_model=data_model,
                cloud_provider=cloud_provider,
                already_unflattened=True,
            )
            # It might happen that a record misses identifying fields, but we still want to audit it
            if not record_id:
                record_id = f"unknown_record_{unknown_records_count + 1}"
                unknown_records_count += 1

            record_identifiers.append(record_id)

    else:
        record_identifiers = (
            [x[ID] for x in list(records_df.loc[:, "doc"].values)]
            if update_action
            else records_df.loc[:, ID].to_list()
        )

    records_status_metrics = status_df.loc[:, BulkWriterColumns.STATUS].to_list()
    records_error_reason = status_df.loc[:, BulkWriterColumns.ERROR_REASON].to_list()

    audit_result: Dict[Any, Any] = {}
    for record_id, metric, error_reason in zip(
        record_identifiers, records_status_metrics, records_error_reason
    ):
        # We do not want to fetch the `error_reason` for duplicate records
        # because those records are self-explanatory
        if error_reason and metric == WriteStatus.ERRORED:
            error_reason = [f"Failed to write to database: {error_reason}"]
        else:
            error_reason = []

        # TODO both version_conflicts and duplicates are registered as duplicates
        #   to avoid confusing the clients. Note that both groups do not overlap.
        #   If/when we decide to add Quarantine logic to Aries
        #   we need to separate |Duplicates| and |Version Conflicts -> Quarantined|
        if metric == WriteStatus.VERSION_CONFLICT:
            metric = WriteStatus.DUPLICATE

        if record_id not in audit_result:
            audit_result[record_id] = Auditor.get_default_audit_fields()
            audit_result[record_id][metric] = 1
            audit_result[record_id][AuditorStaticFields.STATUS] = error_reason

        else:
            audit_result[record_id][metric] += 1
            audit_result[record_id][AuditorStaticFields.STATUS] += error_reason

    upsert_audit(
        audit_path=audit_path, streamed=streamed, models=[data_model], input_data=audit_result
    )


def _flush_content(
    content: str,
    status_metrics: defaultdict[str, int],
    records_fo: TextIO,
    status_file_path: pathlib.Path,
    audit_path: str,
    streamed: bool,
    cloud_provider: CloudProviderEnum,
) -> None:
    """Flush the content string into the elastic bulk endpoint.

    :param content: strings with the ndjson contents to post to elastic
    :return: Pandas DataFrame with the status for each record
    """
    response = es_api_retriable_call(
        Resources.es_repo().client.bulk,
        body=content,
        refresh="true",
        timeout="15m",
        request_timeout=900,
    )

    statuses = list(map(lambda x: _make_status(response=x), response["items"]))
    status_df = pd.DataFrame(statuses)

    errored_mask = ~status_df.status.isin([200, 201, 409])
    status_df.loc[errored_mask, BulkWriterColumns.STATUS] = WriteStatus.ERRORED
    status_metrics[WriteStatus.ERRORED] += errored_mask.sum().item()  # type: ignore

    updated_mask = status_df.status == 200
    status_df.loc[updated_mask, BulkWriterColumns.STATUS] = WriteStatus.UPDATED
    status_metrics[WriteStatus.UPDATED] += updated_mask.sum().item()  # type: ignore

    created_mask = status_df.status == 201
    status_df.loc[created_mask, BulkWriterColumns.STATUS] = WriteStatus.CREATED
    status_metrics[WriteStatus.CREATED] += created_mask.sum().item()  # type: ignore

    conflict_mask = status_df.status == 409
    status_df.loc[conflict_mask, BulkWriterColumns.STATUS] = WriteStatus.VERSION_CONFLICT
    status_metrics[WriteStatus.VERSION_CONFLICT] += conflict_mask.sum().item()  # type: ignore

    status_metrics["es_status_records_count"] += len(status_df)

    # Odd indexed elements in split content are always create query
    # Even indexed elements in split content are always actual record
    # So getting only actual records (using list[1::2]) from content
    records_df = pd.DataFrame([json.loads(i) for i in content.strip().split("\n")[1::2]])

    # Filtering only success records
    success_records_df = records_df.loc[
        status_df.index[
            (status_df["status"] == WriteStatus.CREATED)
            | (status_df["status"] == WriteStatus.UPDATED)
        ]
    ]
    if not success_records_df.empty:
        # Appending success records to file
        status_metrics["success_records_count"] += len(success_records_df)
        success_records_df.to_json(records_fo, orient="records", lines=True)

    version_conflicts = status_df.loc[(status_df.status == WriteStatus.VERSION_CONFLICT)]

    if not version_conflicts.empty:
        # Getting hash_ids of version conflict records, which will used for comparing hashids
        version_conflicts = version_conflicts.merge(
            records_df[[ID, HASH]],
            left_on=BulkWriterColumns.ID,
            right_on=ID,
            how="left",
        )

        # Dropping &id as we already have id in version_conflicts df
        if ID in version_conflicts.columns:
            version_conflicts.drop(columns=[ID], inplace=True)

        duplicate_ids = _compare_hash(
            status_frame=version_conflicts,
            alias=version_conflicts.iloc[0][BulkWriterColumns.ES_INDEX],
        )
        # Choosing rows with duplicate ids and also with status is VERSION_CONFLICT
        # Handles edge case where we have 2 entries withe same id, but with different status
        duplicates_mask = (status_df[BulkWriterColumns.ID].isin(duplicate_ids)) & (
            status_df[BulkWriterColumns.STATUS] == WriteStatus.VERSION_CONFLICT
        )
        status_df.loc[duplicates_mask, BulkWriterColumns.STATUS] = WriteStatus.DUPLICATE
        status_metrics[WriteStatus.DUPLICATE] += duplicates_mask.sum().item()  # type: ignore
        # Need to decrement VERSION_CONFLICT,
        # as initially all the records are marked as VERSION_CONFLICT
        status_metrics[WriteStatus.VERSION_CONFLICT] -= duplicates_mask.sum().item()  # type: ignore

    audit_results(
        status_df=status_df,
        records_df=records_df,
        audit_path=audit_path,
        streamed=streamed,
        cloud_provider=cloud_provider,
    )
    # cast the entire dataframe to string before going to parquet, as its optimal and avoid
    # issues with mixed types in the same column
    status_df = status_df.astype("string")
    if status_file_path.is_file():
        parquet_write(status_file_path.as_posix(), status_df, compression="GZIP", append=True)
    else:
        parquet_write(status_file_path.as_posix(), status_df, compression="GZIP")


def _make_status(response: dict[str, Any]) -> dict[str, Any]:
    """Applied to each item in the elastic response, this formats the data.

    :param response: a dictionary for an item in the response.
    :return: dict
    """
    result = None

    for action in SUPPORTED_ES_ACTIONS:
        if action in response:
            result = response.get(action)

    if result is None:
        logger.error(f"Action not supported in _make_status for {response}")
        return {}

    error = result.get("error", dict())

    status = {
        BulkWriterColumns.ID: result["_id"],
        BulkWriterColumns.ES_INDEX: result["_index"],
        BulkWriterColumns.STATUS: result["status"],
        BulkWriterColumns.ERROR_TYPE: error.get("type"),
        BulkWriterColumns.ERROR_REASON: error.get("reason"),
    }

    return status


def _compare_hash(status_frame: pd.DataFrame, alias: str) -> List[str]:
    ids_to_inspect = status_frame.loc[:, BulkWriterColumns.ID].unique().tolist()
    resp_chunk = []

    for items in batched(ids_to_inspect, 1024):
        response = es_api_retriable_call(
            Resources.es_repo().client.mget,
            body={"ids": items},
            index=alias,
            _source=True,
            _source_includes=[HASH, ID],
        )
        resp_chunk.append(pd.DataFrame([i["_source"] for i in response.get("docs", [])]))

    resp_df = pd.concat(resp_chunk).reset_index(drop=True)

    resp_df.rename(
        columns={
            HASH: STORED_HASH,
            ID: STORED_ID,
        },
        inplace=True,
    )

    # Merging dataframes and getting duplicate ids
    df = status_frame.merge(
        resp_df[[STORED_ID, STORED_HASH]],
        left_on=BulkWriterColumns.ID,
        right_on=STORED_ID,
        how="left",
    )

    duplicates_mask = df[STORED_HASH] == df[STORED_HASH]
    return df.loc[duplicates_mask, BulkWriterColumns.ID].unique().tolist()  # type: ignore


@aries_task
def run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """Load the ndjson file content into ElasticSearch using the bulk endpoint.
    Create a file in the curated zone for the records that where successfully
    loaded. Produce a message into the event bus.

    Create a file with metrics based on the response from the elastic
    request. Produce the corresponding event into the bus.
    """
    ignore_empty_file_uri = aries_task_input.input_param.params.get(IGNORE_EMPTY_FILE_URI, False)

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        ).tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(cloud_provider)

    streamed = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    ).workflow.streamed
    input_path = aries_task_input.input_param.params["file_uri"]

    empty_file_uri_output = handle_empty_file_uri(
        file_uri=input_path,
        cloud_provider=cloud_provider,
        streamed=streamed,
        ignore_empty_file_uri=ignore_empty_file_uri,
    )
    if empty_file_uri_output:
        return empty_file_uri_output
    elif not aries_task_input.input_param.params.get("file_uri"):
        raise ValueError("file_uri parameter is required")

    # Using traceid and task-id for creating output files
    status_file_path = Path(tempfile.gettempdir()).joinpath(
        f"es_connector__{aries_task_input.workflow.trace_id}__{aries_task_input.task.id}"
        f"__{RESPONSE_FILENAME_SUFFIX}"
    )
    records_file_path = Path(tempfile.gettempdir()).joinpath(
        f"es_connector__{aries_task_input.workflow.trace_id}__{aries_task_input.task.id}"
        f"__{SUCCESS_RESPONSE_FILENAME_SUFFIX}"
    )
    # delete files to avoid collisions due to repeated runs
    status_file_path.unlink(missing_ok=True)
    records_file_path.unlink(missing_ok=True)

    audit_filepath = write_named_temporary_json(content={}, output_filename="audit.json")

    try:
        logger.info(f"[PROCESSING] file {input_path}")
        status_metrics: defaultdict[str, int] = defaultdict(int)
        file_content_length = 0
        with (
            open(records_file_path, "w") as records_fo,
            fsspec.open(aries_task_input.input_param.params["file_uri"], mode="r") as input_fo,
        ):
            chunk_size = 0
            content = ""
            for action_line in input_fo:
                record_line = next(input_fo, "")
                content += action_line
                content += record_line
                chunk_size += len(action_line)
                chunk_size += len(record_line)
                file_content_length += chunk_size
                status_metrics["total_records_parsed"] += 1
                payload_size = __get_payload_size()
                if chunk_size >= payload_size:
                    _flush_content(
                        content=content,
                        status_metrics=status_metrics,
                        records_fo=records_fo,
                        status_file_path=status_file_path,
                        streamed=streamed,
                        audit_path=audit_filepath,
                        cloud_provider=cloud_provider,
                    )
                    content = ""
                    chunk_size = 0

            if content:
                _flush_content(
                    content=content,
                    status_metrics=status_metrics,
                    records_fo=records_fo,
                    status_file_path=status_file_path,
                    streamed=streamed,
                    audit_path=audit_filepath,
                    cloud_provider=cloud_provider,
                )

        if not file_content_length:
            logger.warning(f"No records found in file {input_path}")

        # Create successful records filepath (bucket + key) and write + produce.
        output_path_prefix = get_prefixed_ingest_lake_path_for_task(
            workflow_name=aries_task_input.workflow.name,
            task_name=aries_task_input.task.name,
            task_io_params=aries_task_input.input_param.params,
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            workflow_trace_id=aries_task_input.workflow.trace_id,
        )

        #  create cloud agnostic File system pointer
        fs, _, _ = fsspec.get_fs_token_paths(input_path)
        success_file_path = f"{output_path_prefix}{SUCCESS_RESPONSE_FILENAME_SUFFIX}"
        success_file_path = f"{cloud_provider_prefix}{get_bucket(input_path)}/{success_file_path}"
        logger.info(f"Writing {success_file_path}")

        try:
            fs.put(records_file_path.as_posix(), success_file_path)
        except FileExistsError:
            # Azure will raise a FileExistsError if you retry EsConnector on the same file.
            fs.rm(success_file_path)
            fs.put(records_file_path.as_posix(), success_file_path)

        # create status frame path and upload to blob storage
        es_response_file_path = f"{output_path_prefix}{RESPONSE_FILENAME_SUFFIX}"
        es_response_file_path = (
            f"{cloud_provider_prefix}{get_bucket(input_path)}/{es_response_file_path}"
        )
        logger.info(f"Writing {es_response_file_path}")
        if not status_file_path.is_file():
            # create an empty file, if no records were written due to empty input file
            status_file_path.touch()

        try:
            fs.put(status_file_path.as_posix(), es_response_file_path)
        except FileExistsError:
            fs.rm(es_response_file_path)
            fs.put(status_file_path.as_posix(), es_response_file_path)

        app_metrics = AppMetricFieldSet(
            metrics=dict(
                custom=dict(
                    elastic_connector=dict(
                        input_count=status_metrics["total_records_parsed"],
                        error_count=status_metrics.get(WriteStatus.ERRORED) or 0,
                        update_count=status_metrics.get(WriteStatus.UPDATED) or 0,
                        create_count=status_metrics.get(WriteStatus.CREATED) or 0,
                        duplicate_count=status_metrics.get(WriteStatus.DUPLICATE) or 0,
                        version_conflict_count=status_metrics.get(WriteStatus.VERSION_CONFLICT)
                        or 0,
                        file_size_b=(
                            status_file_path.stat().st_size if status_file_path.is_file() else 0
                        ),
                    )
                ),
                generic=dict(
                    input_count=status_metrics["total_records_parsed"],
                    output_count=(status_metrics.get(WriteStatus.CREATED) or 0)
                    + (status_metrics.get(WriteStatus.UPDATED) or 0),
                    errored_count=status_metrics.get(WriteStatus.ERRORED) or 0,
                    duplicate_count=status_metrics.get(WriteStatus.DUPLICATE) or 0,
                    skipped_count=(status_metrics.get(WriteStatus.DUPLICATE) or 0)
                    + (status_metrics.get(WriteStatus.VERSION_CONFLICT) or 0),
                ),
            )
        )

        upload_audit(
            aries_task_input=aries_task_input,
            audit_filepath=audit_filepath,
            cloud_provider=cloud_provider,
        )

        return AriesTaskResult(
            output_param=IOParamFieldSet(params=dict(file_uri=success_file_path)),
            app_metric=app_metrics,
        )

    finally:
        # remove created temp files
        status_file_path.unlink(missing_ok=True)
        records_file_path.unlink(missing_ok=True)


def __get_payload_size() -> int:
    """Extracted to a separate method to facilitate test.

    :return: Max payload size for ES request
    """
    return int(Resources.config().payload_size)
