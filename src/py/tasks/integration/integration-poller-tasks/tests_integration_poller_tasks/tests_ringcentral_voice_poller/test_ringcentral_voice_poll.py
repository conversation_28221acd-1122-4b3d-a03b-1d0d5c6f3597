# flake8: noqa: E402
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

import botocore.exceptions
import datetime
import pytest
from addict import addict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll import (
    RingcentralVoicePoll,
)
from mock.mock import DEFAULT, MagicMock, patch


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ringcentral_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_back_fill() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ringcentral_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ringcentral_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
            custom_lake_path="boarding/",  # optional
            should_event=False,  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


MOCK_RESPONSE = {
    "paused": False,
    "id": 3,
    "max_batch_size": None,
    "workflow_last_executed": None,
    "time_created": "2023-06-09T10:06:14.733020",
    "io_topic": "mock_topic",
    "tenant_id": 1,
    "workflow_id": 3,
    "batch_timeout_s": None,
    "workflow_execution_ref": None,
    "time_updated": None,
    "tenant": {
        "id": 1,
        "lake_prefix": "s3://pinafore.dev.steeleye.co/",
        "time_updated": None,
        "stack_id": 1,
        "name": "pinafore",
        "time_created": "2023-06-09T10:00:22.860947",
        "stack": {
            "paused": False,
            "name": "dev-blue",
            "time_updated": None,
            "id": 1,
            "time_created": "2023-06-09T10:00:03.477200",
        },
    },
    "workflow": {
        "s3_feed_prefix": "test",
        "name": "ringcentral_voice_poll",
        "time_created": "2023-06-09T10:02:34.313252",
        "id": 3,
        "streamed": False,
        "time_updated": None,
    },
}

MOCK_SECRETS = {
    "default": {
        "RINGCENTRAL_CLIENTID": "mock_client_id",
        "RINGCENTRAL_CLIENTSECRET": "mock_client_secret",
        "RINGCENTRAL_SERVER": "mock_server",
        "RINGCENTRAL_TOKEN": "mock_token",
    }
}

MOCK_RESPONSE_API = {
    "records": [
        {"id": "mock_id_1", "recording": {"contentUri": "mock_url_1"}},
        {"id": "mock_id_2", "recording": {"contentUri": "mock_url_2"}},
    ],
    "navigation": {"nextPage": None},
}


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll",
    ringcentral=DEFAULT,
    write=DEFAULT,
)
def test_ringcentral_voice_success_with_custom_path(
    sample_input_with_custom_path, config, **kwargs
):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["ringcentral"].SDK.return_value.platform.return_value.login.return_value = None
    mock_resp = MagicMock()
    mock_resp.response.return_value.status_code = 200
    mock_resp.json_dict.return_value = MOCK_RESPONSE_API
    mock_resp.response.return_value.content = b"mock_content_for_attachment"

    kwargs["ringcentral"].SDK.return_value.platform.return_value.get.return_value = mock_resp
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    poller = RingcentralVoicePoll(config=config, aries_task_input=sample_input_with_custom_path)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )
    kwargs["write"].assert_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll",
    ringcentral=DEFAULT,
    write=DEFAULT,
)
def test_ringcentral_voice_success(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["ringcentral"].SDK.return_value.platform.return_value.login.return_value = None
    mock_resp = MagicMock()
    mock_resp.response.return_value.status_code = 200
    mock_resp.json_dict.return_value = MOCK_RESPONSE_API
    mock_resp.response.return_value.content = b"mock_content_for_attachment"

    kwargs["ringcentral"].SDK.return_value.platform.return_value.get.return_value = mock_resp
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    poller = RingcentralVoicePoll(config=config, aries_task_input=sample_input)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )
    kwargs["write"].assert_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll",
    ringcentral=DEFAULT,
    write=DEFAULT,
)
def test_ringcentral_voice_success_backfill(sample_input_back_fill, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["ringcentral"].SDK.return_value.platform.return_value.login.return_value = None
    mock_resp = MagicMock()
    mock_resp.response.return_value.status_code = 200
    mock_resp.json_dict.return_value = MOCK_RESPONSE_API
    mock_resp.response.return_value.content = b"mock_content_for_attachment"

    kwargs["ringcentral"].SDK.return_value.platform.return_value.get.return_value = mock_resp
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    poller = RingcentralVoicePoll(config=config, aries_task_input=sample_input_back_fill)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )
    kwargs["write"].assert_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll",
    ringcentral=DEFAULT,
    write=DEFAULT,
)
def test_ringcentral_voice_failure_fatal_exception(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["ringcentral"].SDK.return_value.platform.return_value.login.return_value = None
    mock_resp = MagicMock()
    mock_resp.response.return_value.status_code = 200
    mock_resp.json_dict.return_value = MOCK_RESPONSE_API
    mock_resp.response.return_value.content = b"mock_content_for_attachment"

    kwargs["ringcentral"].SDK.return_value.platform.return_value.get.return_value = mock_resp
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)

    poller = RingcentralVoicePoll(config=config, aries_task_input=sample_input)
    kwargs["write"].side_effect = botocore.exceptions.ClientError({}, "PutObject")
    with pytest.raises(botocore.exceptions.ClientError):
        poller.run_poller()
    kwargs["write"].assert_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.ringcentral_voice_poller.ringcentral_voice_poll",
    ringcentral=DEFAULT,
    write=DEFAULT,
)
def test_ringcentral_voice_failure_api_exception(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    kwargs["ringcentral"].SDK.return_value.platform.return_value.login.side_effect = Exception
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    poller = RingcentralVoicePoll(config=config, aries_task_input=sample_input)
    with pytest.raises(Exception):
        poller.run_poller()
    kwargs["write"].assert_not_called()
