# flake8: noqa: E402
import addict
import botocore.exceptions
import os
import pytest
from datetime import datetime
from integration_poller_tasks.kerv_text_poller.static import TRANSFORM_FLOW_NAME

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from fsspec.implementations.sftp import SFTPFileSystem
from integration_poller_tasks.kerv_text_poller.kerv_text_poll import KervTextPoll
from mock import mock
from mock.mock import DEFAULT, MagicMock, patch

workflow = WorkflowFieldSet(
    name="kerv_text_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
workflow_batch = WorkflowFieldSet(
    name="kerv_text_poll",
    stack="uat-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)

input_param = IOParamFieldSet(
    params=dict(
        from_date="2023-03-15",  # optional
        to_date="2023-03-15",  # optional
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)

SAMPLE_EVENT = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
SAMPLE_EVENT_BATCH = AriesTaskInput(workflow=workflow_batch, input_param=input_param, task=task)

MOCK_SFTP_SECRETS = {
    "password": "mockpassword",
    "port": "22",
    "private_key_ascii": "",
    "username": "mockuser",
}
MOCK_RESPONSE = {
    "tenant": {"lake_prefix": "s3://pinafore.dev.steeleye.co/"},
    "io_topic": "test_topic",
}


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "proxy": {"host": "", "port": 8080},
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


@pytest.fixture()
def config_without_proxy():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "uat",
            "version": "version",
            "proxy": {"host": "", "port": None},
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


EXPECTED_EVENT = IOEvent(
    workflow=WorkflowFieldSet(
        name="kerv_text",
        stack="local",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    ),
    task=TaskFieldSet(name="kerv_text_poll", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT.workflow.trace_id = mock.ANY
EXPECTED_EVENT.task.id = mock.ANY
EXPECTED_EVENT.task.timestamp = mock.ANY
EXPECTED_EVENT.workflow.start_timestamp = mock.ANY
EXPECTED_EVENT.io_param.params["file_uri"] = mock.ANY  # type: ignore

EXPECTED_EVENT_UAT = IOEvent(
    workflow=WorkflowFieldSet(
        name="kerv_text",
        stack="uat",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    ),
    task=TaskFieldSet(name="kerv_text_poll", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT_UAT.workflow.trace_id = mock.ANY
EXPECTED_EVENT_UAT.task.id = mock.ANY
EXPECTED_EVENT_UAT.task.timestamp = mock.ANY
EXPECTED_EVENT_UAT.workflow.start_timestamp = mock.ANY
EXPECTED_EVENT_UAT.io_param.params["file_uri"] = mock.ANY  # type: ignore


class FileSystemA(SFTPFileSystem):  # type: ignore
    def __init__(self):
        self._file_instance = MagicMock()

    def listdir(self, path):
        # test for mock sftp path:
        if path == "data/pinafore/kerv_text_poll/recordings/":
            return [
                {
                    "name": "/data/pinafore/kerv_text_poll/recordings/"
                    "1146-************-************-1678873798-ee-sms.zip",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
                {
                    "name": "/data/pinafore/kerv_text_poll/recordings/"
                    "1146-************-************-1678673798-ee-sms.zip",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
                {
                    "name": "/data/pinafore/kerv_text_poll/recordings/"
                    "1146-************-************-1678673798-ee-sms.zip",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
            ]
        # test for prod path:
        elif path == "/recordings/":
            data = {
                "size": 37,
                "type": "file",
                "uid": 1001,
                "gid": 100,
            }
            list_of_sftp_files = []
            for i in range(0, 2001):
                data["name"] = (
                    "/data/pinafore/kerv_text_poll"
                    "/recordings/"
                    "1146-************-************-1678673798-ee-sms.zip"
                )
                list_of_sftp_files.append(data)
            return list_of_sftp_files
        else:
            return []

    def cp_file(self, path1, path2, **kwargs):
        pass

    def created(self, path):
        pass

    def modified(self, path):
        pass

    def sign(self, path, expiration=100, **kwargs):
        pass

    def put_file(self, lpath, rpath, callback=None, **kwargs):
        pass

    def open(
        self,
        path,
        mode="rb",
        block_size=None,
        cache_options=None,
        compression=None,
        **kwargs,
    ):
        return self._file_instance

    def get(self, path1, path2):
        pass


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT, config=config, transform_workflow_name=TRANSFORM_FLOW_NAME
    )
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_with_batch(config_without_proxy, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT_BATCH,
        config=config_without_proxy,
        transform_workflow_name=TRANSFORM_FLOW_NAME,
    )
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_already_existing_files(config_without_proxy, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = True
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    kwargs["fsspec"].filesystem.return_value = FileSystemA()

    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT,
        config=config_without_proxy,
        transform_workflow_name=TRANSFORM_FLOW_NAME,
    )
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
    write=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_failure_fatal(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    kwargs["write"].side_effect = botocore.exceptions.ClientError({}, "PutObject")
    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT, config=config, transform_workflow_name=TRANSFORM_FLOW_NAME
    )
    with pytest.raises(botocore.exceptions.ClientError):
        poller.run_poller()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
    write=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_failure_non_fatal(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    kwargs["write"].side_effect = Exception
    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT, config=config, transform_workflow_name=TRANSFORM_FLOW_NAME
    )
    with pytest.raises(Exception):
        poller.run_poller()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.kerv_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
    write=DEFAULT,
)
def test_poller_failure_fatal_when_listing_folder(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SFTP_SECRETS)
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["get_sftp_fs_from_addict"]().listdir.side_effect = IOError
    poller = KervTextPoll(
        aries_task_input=SAMPLE_EVENT, config=config, transform_workflow_name=TRANSFORM_FLOW_NAME
    )
    with pytest.raises(IOError):
        poller.run_poller()
