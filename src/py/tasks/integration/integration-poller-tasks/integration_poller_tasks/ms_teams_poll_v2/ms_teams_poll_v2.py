import addict
import json
import logging
import nanoid
import os
import shutil
import time
import uuid
from aries_config_api_compatible_client.ms_graph_conf import (
    CompatibleMSGraphConfAPIClient,
)
from aries_config_api_compatible_client.tenant_workflow import (
    CompatibleTenantWorkflowAPIClient,
)
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_se_comms_tasks.feeds.message.ms_teams_chat.utils import (
    ms_teams_message_attachment_location,
)
from aries_task_link.models import AriesTaskResult
from aries_utils.kafka_rest import KafkaRestClient
from concurrent.futures import ThreadPoolExecutor, as_completed
from data_platform_config_api_client.ms_graph_conf import MSGraphConfAPI
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from datetime import datetime, timedelta
from functools import partial
from integration_poller_tasks.ms_teams_poll_v2 import static
from se_comms_graph_api_utils.client import Graph<PERSON><PERSON><PERSON><PERSON>
from se_comms_graph_api_utils.static import (
    GRAPH_API_BASE_URL,
    GRAPH_API_DATETIME_FORMAT,
)
from se_comms_ingress_utils.common_util import ingestion_trigger_event
from se_comms_ingress_utils.custom_exceptions import (
    EitherStartOrEndDateNotExists,
    FromDateGreaterThanToDate,
)
from se_fsspec_utils.file_system import get_filesystem
from se_secrets_client.utils import secrets_client

logger = logging.getLogger(static.MS_TEAMS_CHAT_POLLER_WORKFLOW_NAME)


class MsTeamsPollV2:
    def __init__(self, aries_task_input, config):
        self._workflow_name = aries_task_input.workflow.name
        self._workflow_trace_id = aries_task_input.workflow.trace_id
        self._tenant_name = aries_task_input.workflow.tenant
        self._input_microsoft_tenant_id = aries_task_input.input_param.params.get(
            "microsoft_tenant_id", None
        )
        self._message_batch_size = aries_task_input.input_param.params.get(
            "message_batch_size", 250
        )
        self._start_date = aries_task_input.input_param.params.get("from_date", None)
        self._end_date = aries_task_input.input_param.params.get("to_date", None)
        self._max_workers = int(
            aries_task_input.input_param.params.get("max_worker_pool", static.MAX_WORKER_POOL)
        )
        self._custom_download_path = aries_task_input.input_param.params.get(
            "custom_lake_path", None
        )
        self._should_event = aries_task_input.input_param.params.get("should_event", False)
        self._config = config
        self._secrets_client = secrets_client(self._config.vault, self._config)
        self._app_metric = AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})

        # get tenant workflow configuration for destination workflow
        self._config_api_client = AriesApiClient(host=config.data_platform_config_api_url)
        self._tenant_workflow_api = TenantWorkflowAPI(self._config_api_client)
        self._transform_workflow_tw_config = CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=self._tenant_workflow_api,
            tenant_name=self._tenant_name,
            workflow_name=static.MS_TEAMS_CHAT_TRANSFORM_WORKFLOW_NAME,
        )
        # get filesystem
        self._destination_fs = get_filesystem(
            cloud=self._transform_workflow_tw_config.tenant.cloud,
            lake_prefix=self._transform_workflow_tw_config.tenant.lake_prefix,
        )
        # create the download_path
        self._timestamp_now = datetime.utcnow()
        if self._custom_download_path:
            self._object_store_download_path = (
                f"{self._transform_workflow_tw_config.tenant.lake_prefix.rstrip('/')}"
                f"/{self._custom_download_path.rstrip('/')}/{static.DESTINATION_PATH_PREFIX.rstrip('/')}"
                f"/{self._timestamp_now.strftime('%Y/%m/%d')}/{self._workflow_trace_id}/"
            )
        else:
            self._object_store_download_path = (
                f"{self._transform_workflow_tw_config.tenant.lake_prefix.rstrip('/')}"
                f"/{static.DESTINATION_PATH_PREFIX.rstrip('/')}"
                f"/{self._timestamp_now.strftime('%Y/%m/%d')}/{self._workflow_trace_id}/"
            )

        # kafka client
        self._kafka_rest_proxy_client = KafkaRestClient(
            kafka_rest_proxy_url=self._config.kafka_rest_proxy_url,
        )
        self._local_dir = f"{self._workflow_trace_id}/{str(uuid.uuid4())}/"

        self.seen_message_ids = []

    def _get_attachment_url(
        self, content_url, from_user_id, subscription: addict.Dict | None = None
    ):
        """compose attachment url for given subscription and attachment content
        url."""

        attachment_url = None

        """
        sample content urls for user and group
        https://steeleyed-my.sharepoint.com/personal/suresh_.._com/Documents/Microsoft
        Teams Chat Files/test.json
        https://steeleyed.sharepoint.com/sites/Ste...2/Shared
        Documents/test_channel/test copy.json
        """
        att_file_path = "/".join(content_url.split("/")[-2:])

        if not subscription:
            if from_user_id:
                attachment_url = (
                    f"{GRAPH_API_BASE_URL}/users/{from_user_id}/drive/root:/{att_file_path}"
                )
        elif subscription.resource.startswith("/users"):
            # from_user_id is prioritized over user_id in subscription
            # since attachments are stored in
            # a message sender's drive.
            if from_user_id:
                attachment_url = (
                    f"{GRAPH_API_BASE_URL}/users/{from_user_id}/drive/root:/{att_file_path}"
                )
            else:
                # example subscription -> /users/$user_id/chats/getAllMessages
                user_id = subscription.resource.split("/")[2]
                attachment_url = f"{GRAPH_API_BASE_URL}/users/{user_id}/drive/root:/{att_file_path}"

        elif subscription.resource.startswith("/teams"):
            # example subscriptions:
            #           /teams/$group_id/channels/getAllMessages
            #           /teams/$group_id/channels/$channel_id/getAllMessages
            group_id = subscription.resource.split("/")[2]
            attachment_url = f"{GRAPH_API_BASE_URL}/groups/{group_id}/drive/root:/{att_file_path}"
        else:
            logger.error("Subscription type %s is not supported", subscription)
        return attachment_url

    def _upload_message_attachments(self, message, subscription):
        """Goes through a message and uploads each attachment."""
        new_attachments = []
        for attachment in message.get(static.ATTACHMENTS, []):
            attachment_id = attachment[static.ID]
            destination = ms_teams_message_attachment_location(
                input_file_path=self._object_store_download_path.rstrip("/"),
                message_id=message[static.ID],
                attachment_id=attachment_id,
            )
            logger.info(f"Uploading attachment to {destination}")
            try:
                if attachment.get("content"):
                    with self._destination_fs.open(destination, mode="wb") as f:
                        f.write(bytes(attachment["content"], "utf8"))
                    new_attachments.append(attachment)
                elif attachment.get("contentUrl"):
                    from_user_id = message.get("from", {}).get("user", {}).get("id")
                    self._process_attachment(
                        attachment=attachment,
                        destination=destination,
                        from_user_id=from_user_id,
                        subscription=subscription,
                    )
                    new_attachments.append(attachment)
            except Exception:
                error_message = (
                    f"Failed to poll attachment {attachment_id}"
                    f"for message id: `{message[static.ID]}` "
                    f"and subscription `{subscription}`"
                )
                logger.exception(error_message)

        message[static.ATTACHMENTS] = new_attachments

    def _process_attachment(self, attachment, destination, from_user_id, subscription):
        attachment_url = self._get_attachment_url(
            content_url=attachment["contentUrl"],
            from_user_id=from_user_id,
            subscription=subscription,
        )
        if not attachment_url:
            raise ValueError(f"Unsupported subscription {subscription.resource} for attachments")
        try:
            self._extract_attachment(attachment_url=attachment_url, destination=destination)
        except:  # noqa E722
            # if the message is shared in a channel then try to
            # download attachment from sent user's drive
            if subscription.resource.startswith("/teams"):
                new_attachment_url = self._get_attachment_url(
                    content_url=attachment["contentUrl"], from_user_id=from_user_id
                )
                if not new_attachment_url:
                    raise ValueError(
                        f"Unsupported subscription {subscription.resource} for attachments"
                    )
                logger.info(
                    f"Failed to download attachment using `{attachment_url}`."
                    f" Trying to download using `{new_attachment_url}`"
                )
                self._extract_attachment(attachment_url=new_attachment_url, destination=destination)

    def _extract_attachment(self, attachment_url, destination):
        """download attachment from graph api and store in object store."""
        download_url = self._graph_api_client.get_attachment_download_url(attachment_url)
        attachment_stream = self._graph_api_client.get_streamed_response(
            attachment_url=download_url
        )
        with self._destination_fs.open(destination, mode="wb") as f:
            for data in attachment_stream:
                f.write(data)

    def _get_start_end_date(self):  # review
        """Method used for back-filling of data."""
        if self._start_date and self._end_date:
            self._start_date = datetime.strptime(self._start_date, "%Y-%m-%d")
            self._end_date = datetime.strptime(self._end_date, "%Y-%m-%d")
            logger.info(
                f"Back-filling from Start date: {self._start_date}, End date: {self._end_date}"
            )
        elif not self._start_date and not self._end_date:
            return
        else:
            logger.error("Either start date or end date does not exist")
            raise EitherStartOrEndDateNotExists()
        if self._start_date > self._end_date:
            logger.error(
                f"start date:{self._start_date} can't be greater than end date:{self._end_date}"
            )
            raise FromDateGreaterThanToDate()

    def _set_up_local_dirs(self) -> None:
        """_set_up_local_dirs creates local dir."""
        self._clean_up_local_dirs()
        if not os.path.exists(self._local_dir):
            os.makedirs(self._local_dir)

    def _clean_up_local_dirs(self) -> None:
        """_clean_up_local_dirs clears the local dir."""
        if os.path.exists(self._local_dir):
            shutil.rmtree(self._local_dir)

    def _message_iterator(self, subscription: addict.Dict, start, end):
        """this iterator gets messages of incoming subscriptions and iterates
        until no more messages."""
        should_yield_data = False
        # Construct initial URL base on resource for subscription
        url = f"{GRAPH_API_BASE_URL}{subscription.resource}"

        if not subscription.resource:
            raise ValueError(f"Unsupported subscription.resource {subscription.resource} ")

        # check if start and end date exists in flow-args
        if start and end:
            if "getAllMessages" not in subscription.resource:
                return []
            start = start.strftime(GRAPH_API_DATETIME_FORMAT)
            end = end.strftime(GRAPH_API_DATETIME_FORMAT)
        else:
            start = (
                (subscription.last_polled and datetime.fromisoformat(subscription.last_polled))
                or (self._timestamp_now.date() - timedelta(days=static.DEFAULT_LOOKBACK_DAYS))
            ).strftime(GRAPH_API_DATETIME_FORMAT)
            end = self._timestamp_now.strftime(GRAPH_API_DATETIME_FORMAT)

        # channels url with delta
        if "getAllmessages" not in subscription.resource and "chats" not in subscription.resource:
            should_yield_data = True

            # channels url with deltaLink
            if subscription.deltaLink:
                url = (
                    f"{url}/delta?"
                    f"$top={static.GRAPH_API_GET_CHANNEL_MSG_BATCH_SIZE}&"
                    f"$expand=replies&$deltatoken={subscription.deltaLink}"
                )

            # channels url without deltaLink
            else:
                url = (
                    f"{url}/delta?$filter=lastModifiedDateTime gt {start}&"
                    f"$top={static.GRAPH_API_GET_CHANNEL_MSG_BATCH_SIZE}&"
                    f"$expand=replies"
                )

        # users url
        else:
            url = f"{url}?$filter=lastModifiedDateTime gt {start} and lastModifiedDateTime lt {end}"
            if self.license_model:
                url += f"&model={self.license_model}"
            url += f"&$top={self._message_batch_size}"

        for message in self._graph_api_client.paginated_query(
            api_endpoint=url, should_yield_delta=should_yield_data
        ):
            # If message does not contain an att the upload function will be called
            # but then promptly exited, as the att list will be empty
            self._upload_message_attachments(message, subscription)
            yield message

    def _dump_batch_and_event(self, batch: list[str]):
        time.sleep(0.5)
        batch_file_name = f"{self._timestamp_now.strftime('%Y%m%d')}_{nanoid.generate(size=10)}"
        full_path = self._object_store_download_path.rstrip("/") + f"/{batch_file_name}.json"
        with self._destination_fs.open(full_path, mode="w") as f:
            json.dump({"value": batch}, f)
        logger.info(f"saved message batch in: `{full_path}`")
        if self._custom_download_path and not self._should_event:
            logger.info(
                "Skipping ingress io event as custom_lake_path is set and should_event is not set"
            )
            return

        logger.info("sending ingress IO event")
        self._send_event(target_path=full_path)

    def _send_event(self, target_path):
        # Producing ingestion trigger event
        event = ingestion_trigger_event(
            target_path=target_path,
            workflow_name=static.MS_TEAMS_CHAT_TRANSFORM_WORKFLOW_NAME,
            tenant_name=self._tenant_name,
            stack_name=self._config.stack,
            task_name=self._workflow_name,
            streamed=False,
            version=self._config.version,
            aries_task_to_domain_dict={},
        )
        logger.info(f"[CREATED] Ingress IO Event: `{event}`")
        self._kafka_rest_proxy_client.send(
            io_event=event,
            topic=self._transform_workflow_tw_config.io_topic,
            raise_on_connection_error=True,
            raise_on_serder_error=True,
        )

    def _process_batch(self) -> int:
        """Processes each batch by dumping it to object_store."""
        batch = []
        batch_count = 0
        for entry in os.scandir(self._local_dir):
            file_path = entry.path
            if file_path.endswith(".ndjson") and entry.is_file():
                with open(file_path) as fh:
                    for line in fh:
                        batch.append(json.loads(line))

                        if len(batch) >= static.FILE_BATCH_SIZE:
                            self._dump_batch_and_event(batch)
                            batch = []
                            batch_count += 1
        if batch:
            self._dump_batch_and_event(batch)
        total_messages = (batch_count * static.FILE_BATCH_SIZE) + len(batch)
        logger.info(f"processed all batches and the total number messages is `{total_messages}`")
        return total_messages

    def _process_subscription(self, subscription, start_date, end_date):
        """Process the subscriptions messages to a batch file:

        path: "Message_local_folder/process_subscription_{id}.ndjson
        """
        result = {
            "is_failed": False,
            "is_delta_updated": False,
            "subscription": subscription,
        }
        try:
            with open(
                os.path.join(
                    self._local_dir,
                    f"{subscription.id}_{nanoid.generate(size=8)}.ndjson",
                ),
                "w",
            ) as fh:
                for message in self._message_iterator(subscription, start_date, end_date):
                    message_id = message.get(static.ID, None)
                    message_chat_id = message.get(static.CHAT_ID, None)

                    # Instead of handling messages with missing ids that would be polled and then
                    # discarded in transform we discard here instead and simplify the logic ahead
                    if not message_id:
                        logger.info("Message is missing message_id, skipping message processing")
                        continue
                    if not message_chat_id:
                        logger.info(
                            "Message is missing message_chat_id, skipping message processing"
                        )
                        continue

                    # Message_id is the timestamp in epoch with ms of when the message was sent,
                    # so we add the chat_id to confirm there are no wrong duplicates.
                    composed_id = f"{message_id}_{message_chat_id}"

                    # We only discard messages that have existing seen ids or are not message type
                    if (composed_id not in self.seen_message_ids) and (
                        message.get(static.MESSAGE_TYPE, "") == "message"
                    ):
                        self.seen_message_ids.append(composed_id)

                        members_list = self._graph_api_client.get_chat_members(
                            chat_id=message_chat_id
                        )
                        sender_id = message["from"]["user"]["id"]
                        message["from"]["user"]["email"], message["toId"] = (
                            self._graph_api_client.get_recipients_from_members_pool(
                                members_list=members_list, sender_id=sender_id
                            )
                        )

                        # dump the message in local download dir
                        if message and static.DELTA_LINK in message.keys():
                            # message["@odata.deltaLink"] =
                            # https://graph.microsoft.com/v1.0/teams/b4683139-55a3-4ad8
                            # -a0b4-8f428e5ff6fd/channels/19:VWpPY4M5SZKKw1@thread.tacv2
                            # /messages/delta?$deltatoken=n52cVMiBbnucBIfdXCen-9gDD5BV
                            subscription.deltaLink = message[static.DELTA_LINK].split(
                                "$deltatoken="
                            )[1]
                            result["is_delta_updated"] = True
                        else:
                            fh.write(json.dumps(message))
                            fh.write("\n")
            logger.info(f"fetched all messages for `{subscription.resource}`")
        # this ensures that failure in 1 subscription does not affect other subscriptions
        except Exception:
            error_message = f"Failed to poll messages for subscription `{subscription}`"
            logger.exception(error_message)
            result["is_failed"] = True

        return result

    def _get_eligible_db_subscriptions(self, ms_teams_api, microsoft_tenant_id, user_group):
        """get pollable subscriptions from database pollable subscriptions are
        those that are not skipped and not deleted."""
        logger.info(
            f"Fetching subscription details from config db "
            f"for microsoft_tenant_id `{microsoft_tenant_id}`"
        )
        all_subscriptions = []
        response = CompatibleMSGraphConfAPIClient.get_teams_subscriptions_by_user_group(
            ms_graph_config_api=ms_teams_api,
            tenant_name=self._tenant_name,
            skip_poll=False,
            deleted=False,
            microsoft_tenant_id=microsoft_tenant_id,
            user_group=user_group,
            page=None,
        )
        while response["page"] < response["total_pages"]:
            all_subscriptions.extend(response["data"])
            response = CompatibleMSGraphConfAPIClient.get_teams_subscriptions_by_user_group(
                ms_graph_config_api=ms_teams_api,
                tenant_name=self._tenant_name,
                skip_poll=False,
                deleted=False,
                microsoft_tenant_id=microsoft_tenant_id,
                user_group=user_group,
                page=response["page"] + 1,
            )
        all_subscriptions.extend(response["data"])

        logger.info("successfully fetched subscription details from config db")
        if all_subscriptions:
            logger.info(
                f"Found {len(all_subscriptions)} in config db for "
                f"microsoft_tenant_id `{microsoft_tenant_id}`"
            )
        else:
            logger.info(
                f"No subscriptions found in config db for "
                f"microsoft_tenant_id `{microsoft_tenant_id}`"
            )
        return all_subscriptions

    def run(self) -> AriesTaskResult:
        """Fetches messages for the given subscriptions."""

        self._get_start_end_date()

        ms_teams_api = MSGraphConfAPI(self._config_api_client)

        user_groups_map = {}

        # user_groups_map = {'microsoft_tenant_id':{'user_groups': ['u1','u2'],
        # 'config':{
        #   "tenantId": 1,
        #   "microsoftTenantId": "t1",
        #   "updatedDateTime": null,
        #   "createdDateTime": "2024-02-23T10:56:24.942810",
        #   "id": "b4b232c5-e8c9-4909-ac3a-47f31d99a4b7",
        #   "workflows": [
        #     {
        #       "id": "58752334-9e02-4221-99e8-c4584debf2c6",
        #       "createdDateTime": "2024-02-23T11:04:01.202502",
        #       "config": {
        #         "licence_model": "A"
        #       },
        #       "msGraphConfigId": "b4b232c5-e8c9-4909-ac3a-47f31d99a4b7",
        #       "workflowId": 2,
        #       "updatedDateTime": null
        #     }
        #   ]
        # } }}

        if self._input_microsoft_tenant_id:
            logger.info(
                f"Getting user groups for microsoft tenant id: {self._input_microsoft_tenant_id}"
            )
            user_groups = CompatibleMSGraphConfAPIClient.get_user_groups(
                ms_graph_config_api=ms_teams_api,
                tenant_name=self._tenant_name,
                workflow_name=static.MS_TEAMS_CHAT_POLLER_WORKFLOW_NAME,
                microsoft_tenant_id=self._input_microsoft_tenant_id,
            )

            if not user_groups:
                self._app_metric.metrics["generic"]["errored_count"] += 1
                raise ValueError(
                    f"No user groups found for tenant: {self._tenant_name}"
                    f" and microsoft tenant id {self._input_microsoft_tenant_id}"
                    f" for polling subscriptions."
                )

            config = CompatibleMSGraphConfAPIClient.get_config_by_workflow_name(
                ms_graph_config_api=ms_teams_api,
                tenant_name=self._tenant_name,
                workflow_name=static.MS_TEAMS_CHAT_POLLER_WORKFLOW_NAME,
                microsoft_tenant_id=self._input_microsoft_tenant_id,
            )

            # Update user_groups_map
            user_groups_map[self._input_microsoft_tenant_id] = {
                "user_groups": user_groups,
                "config": config,
            }
        else:
            # get all microsoft_tenant_ids for a tenant and workflow-name
            ms_teams_configs = CompatibleMSGraphConfAPIClient.get_configs(
                ms_graph_config_api=ms_teams_api, tenant_name=self._tenant_name
            )

            if not ms_teams_configs:
                self._app_metric.metrics["generic"]["errored_count"] += 1
                raise ValueError(f"No configs found for tenant: {self._tenant_name}")

            logger.info(f"Getting user groups for tenant: {self._tenant_name}")
            # get all user-groups for each microsoft_tenant_id
            for ms_teams_config in ms_teams_configs:
                ms_tenant_id = ms_teams_config["microsoftTenantId"]
                user_groups = CompatibleMSGraphConfAPIClient.get_user_groups(
                    ms_graph_config_api=ms_teams_api,
                    tenant_name=self._tenant_name,
                    workflow_name=static.MS_TEAMS_CHAT_POLLER_WORKFLOW_NAME,
                    microsoft_tenant_id=ms_tenant_id,
                )

                if not user_groups:
                    self._app_metric.metrics["generic"]["errored_count"] += 1
                    raise ValueError(
                        f"No user groups found for tenant: {self._tenant_name}"
                        f" and microsoft tenant id {ms_tenant_id}"
                        f" for polling subscriptions."
                    )

                config = CompatibleMSGraphConfAPIClient.get_config_by_workflow_name(
                    ms_graph_config_api=ms_teams_api,
                    tenant_name=self._tenant_name,
                    workflow_name=static.MS_TEAMS_CHAT_POLLER_WORKFLOW_NAME,
                    microsoft_tenant_id=ms_tenant_id,
                )

                # Update user_groups_map
                user_groups_map[self._input_microsoft_tenant_id] = {
                    "user_groups": user_groups,
                    "config": config,
                }

        if not user_groups_map:
            self._app_metric.metrics["generic"]["errored_count"] += 1
            raise ValueError(
                f"No user groups found for tenant: {self._tenant_name} for polling subscriptions."
            )

        logger.info(f"User Groups map: {user_groups_map}")

        for microsoft_tenant_id, items_dictionary in user_groups_map.items():
            api_secrets = self._secrets_client.get_secrets(
                path=static.MS_TEAMS_SECRET_PATH.format(
                    tenant=self._tenant_name,
                    microsoft_tenant_id=microsoft_tenant_id,
                )
            )

            # create graph api client
            self._graph_api_client = GraphApiClient(
                microsoft_tenant_id=microsoft_tenant_id,
                tenant=self._tenant_name,
                client_id=api_secrets.microsoft_application_id,
                client_secret=api_secrets.microsoft_application_secret,
                timeout=int(self._config.graph_api_timeout_s),
            )

            self.license_model = items_dictionary["config"]["workflows"][0]["config"][
                "licence_model"
            ]

            for user_group in items_dictionary["user_groups"]:
                self._set_up_local_dirs()
                ug = user_group["userGroup"]

                subscriptions = self._get_eligible_db_subscriptions(
                    ms_teams_api=ms_teams_api,
                    microsoft_tenant_id=microsoft_tenant_id,
                    user_group=ug,
                )

                if not subscriptions:
                    logger.warning(
                        f"No subscriptions found for tenant: {self._tenant_name}."
                        f"Subscriptions {subscriptions}"
                    )
                    continue
                logger.info(f"polling messages for: `{len(subscriptions)}` subscriptions")

                failed_subscriptions: list[dict[str, str]] = []
                updated_delta_link_subscriptions: dict[str, dict[str, str]] = {}

                with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                    # Submit each subscription
                    futures = [
                        executor.submit(
                            partial(
                                self._process_subscription,
                                subscription,
                                self._start_date,
                                self._end_date,
                            )
                        )
                        for subscription in subscriptions
                    ]
                    for future in as_completed(futures, timeout=14400):
                        result = future.result()
                        if result["is_delta_updated"]:
                            updated_delta_link_subscriptions[result["subscription"]["resource"]] = (
                                result["subscription"]
                            )
                        if result["is_failed"]:
                            failed_subscriptions.append(result["subscription"])

                logger.info("Start processing the batch file")
                total_messages = self._process_batch()
                self._clean_up_local_dirs()
                if failed_subscriptions:
                    logger.error(
                        f"failed to poll data for following subscriptions: `{failed_subscriptions}`"
                    )
                self._app_metric.metrics["generic"]["errored_count"] += len(failed_subscriptions)
                logger.info(
                    f"Finished collecting messages successfully for "
                    f"`{microsoft_tenant_id}` and user_group {ug} "
                    f"and total number of messages is: `{total_messages}`"
                )

                if all(
                    [
                        not self._end_date,
                        not self._start_date,
                        len(failed_subscriptions) < len(subscriptions),
                    ]
                ):
                    failed_resources = [i["resource"] for i in failed_subscriptions]
                    upsert_payload = [
                        {
                            "resource": each_subscription["resource"],
                            "lastPolled": self._timestamp_now.isoformat(),
                            "subscriptionName": each_subscription["subscriptionName"],
                            "deltaLink": updated_delta_link_subscriptions.get(
                                each_subscription["resource"], each_subscription
                            )["deltaLink"],
                        }
                        for each_subscription in subscriptions
                        if each_subscription["resource"] not in failed_resources
                    ]
                    CompatibleMSGraphConfAPIClient.bulk_upsert_teams_subscriptions_by_user_group(
                        ms_graph_config_api=ms_teams_api,
                        json_body={"resources": upsert_payload},
                        tenant_name=self._tenant_name,
                        microsoft_tenant_id=microsoft_tenant_id,
                        user_group=ug,
                    )

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)
