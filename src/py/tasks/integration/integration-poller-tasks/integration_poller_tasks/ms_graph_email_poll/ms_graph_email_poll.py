"""The module contain ms_graph_email_poll class."""

import botocore.exceptions
import httpx
import logging
import nanoid
import orjson
import os
import pybase64
import shutil
import uuid
from addict import Dict as AddictDict
from aries_config_api_compatible_client.ms_graph_conf import (
    CompatibleMSGraphConfAPIClient,
)
from aries_config_api_compatible_client.tenant_workflow import (
    CompatibleTenantWorkflowAPIClient,
)
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApi<PERSON>lient
from aries_se_comms_tasks.feeds.email.ms_graph.static import SourceEmailColumns
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_utils.kafka_rest import KafkaRestClient
from concurrent.futures import ThreadPoolExecutor, as_completed
from data_platform_config_api_client.ms_graph_conf import MSGraph<PERSON>onf<PERSON><PERSON>
from data_platform_config_api_client.tenant_workflow import <PERSON>ant<PERSON>or<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, time, timedelta, timezone
from functools import partial
from integration_poller_tasks.ms_graph_email_poll.static import (
    DEFAULT_LOOKBACK_DAYS,
    MAX_BATCH_SIZE,
    MS_GRAPH_ATTACHMENTS_PAGE_SIZE,
    MS_GRAPH_CONFIG_UPSERT_BATCH_SIZE,
    MS_GRAPH_EMAIL_FOLDERS_END_POINT,
    MS_GRAPH_EMAIL_POLLER_WORKFLOW_NAME,
    MS_GRAPH_EMAIL_SECRET_PATH,
    MS_GRAPH_EMAIL_SUB_FOLDERS_END_POINT,
    MS_GRAPH_EMAIL_TRANSFORM_WORKFLOW_NAME,
    MS_GRAPH_EMAILS_PAGE_SIZE,
    MS_GRAPH_FOLDER_EMAILS_END_POINT,
    MS_GRAPH_GROUP_MEMBERS_PAGE_SIZE,
    MS_GRAPH_GROUP_USERS_END_POINT,
    MS_GRAPH_USER_MESSAGE_ATTACHMENTS_END_POINT,
    MS_GRAPH_USER_MESSAGES_END_POINT,
    MS_GRAPH_USER_MIME_MESSAGE_BY_ID_END_POINT,
)
from pydantic import BaseModel, Field, root_validator
from se_comms_graph_api_utils.client import GraphApiClient, RequestMethods
from se_comms_graph_api_utils.static import GRAPH_API_DATETIME_FORMAT
from se_comms_ingress_utils.common_util import ingestion_trigger_event
from se_data_lake.lake_path import get_non_streamed_poller_file_path
from se_fsspec_utils.file_system import get_filesystem
from se_fsspec_utils.file_utils import write, write_successful_ndjson_files
from se_secrets_client.utils import secrets_client
from threading import Lock
from typing import Any, Dict
from urllib.parse import urlparse


class MSGraphEmailPollParams(BaseModel):
    custom_lake_path: str | None = Field(default=None, description="custom path")
    from_date: str | None = Field(default=None, description="from date timestamp")
    last_poll_override: str | None = Field(
        default=None, description="Used when migrating from non graph api based to graph api based"
    )
    max_batch_size: int | None = Field(description="file size for transform flow")
    microsoft_tenant_id: str | None = Field(default=None, description="microsoft tenant id")
    should_event: bool = Field(default=False, description="should event")
    to_date: str | None = Field(default=None, description="to date timestamp")
    process_mime_content: bool = Field(
        default=False,
        description="fetch, upload to file storage, and link raw .eml files per message if True",
    )

    @root_validator(pre=True)
    def from_date_and_last_poll_override(cls, values):
        if values.get("last_poll_override") and values.get("from_date"):
            raise ValueError("from_date and last_poll_override are mutually exclusive")
        return values


class MSGraphEmailPoll:
    """class MSGraphEmailPoll polls for email data."""

    def __init__(self, aries_task_input: AriesTaskInput, config):
        """__init__ initializes the class."""
        self._lock = Lock()
        self._aries_task_input = aries_task_input
        self._params = MSGraphEmailPollParams.validate(self._aries_task_input.input_param.params)
        self._config = config
        self._max_workers = int(self._config.max_worker_pool)
        # get the secrets from the vault
        self._secrets_client = secrets_client(self._config.vault, self._config)

        self._app_metric = AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})

        # get tenant workflow configuration for destination workflow
        self._config_api_client = AriesApiClient(host=config.data_platform_config_api_url)
        self._tenant_workflow_api = TenantWorkflowAPI(self._config_api_client)
        self._transform_workflow_tw_config = CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=self._tenant_workflow_api,
            tenant_name=self._aries_task_input.workflow.tenant,
            workflow_name=MS_GRAPH_EMAIL_TRANSFORM_WORKFLOW_NAME,
        )
        # get filesystem
        self._destination_fs = get_filesystem(
            cloud=self._transform_workflow_tw_config.tenant.cloud,
            lake_prefix=self._transform_workflow_tw_config.tenant.lake_prefix,
        )
        self._lake_prefix = self._transform_workflow_tw_config.tenant.lake_prefix.rstrip("/")
        self._batch_size = (
            self._params.max_batch_size
            or self._transform_workflow_tw_config.max_batch_size
            or MAX_BATCH_SIZE
        )

        # kafka client
        self._kafka_rest_proxy_client = KafkaRestClient(
            kafka_rest_proxy_url=self._config.kafka_rest_proxy_url,
        )

        self._local_dir = str(uuid.uuid4())

        self._ms_graph_config_api = MSGraphConfAPI(
            AriesApiClient(host=self._config.data_platform_config_api_url)
        )

        self._poll_from_timestamp = self._params.from_date
        self._poll_to_timestamp = self._params.to_date
        self._backfill = False
        # set the from and to timestamp based on backfill/ normal run
        if not self._poll_from_timestamp and not self._poll_to_timestamp:
            # regular poller run
            # setting to None to pick the last possible date from DB
            self._default_lookback_timestamp = datetime.combine(
                datetime.now(timezone.utc).date() - timedelta(days=float(DEFAULT_LOOKBACK_DAYS)),
                time.min,
            ).strftime(GRAPH_API_DATETIME_FORMAT)
            self._poll_from_timestamp = None
            self._poll_to_timestamp = datetime.now(timezone.utc).strftime(GRAPH_API_DATETIME_FORMAT)
        else:
            self._backfill = True

        self._folder_date = datetime.now()
        self._workflow_start_timestamp = datetime.now(timezone.utc).strftime(
            GRAPH_API_DATETIME_FORMAT
        )

    def _set_up_local_dirs(self) -> None:
        self._clean_up_local_dirs()
        if not os.path.exists(self._local_dir):
            os.makedirs(self._local_dir)

    def _clean_up_local_dirs(self) -> None:
        if os.path.exists(self._local_dir):
            shutil.rmtree(self._local_dir)

    def _get_poll_from_timestamp(self, user: AddictDict) -> Any:
        """Get the poll from timestamp for the user based on the last polled
        timestamp from the DB or the last poll override timestamp if set."""
        if not self._backfill:
            # Normal poller run
            # Check if the last poll override is set, if yes use that
            # else use the last polled timestamp from the DB if present or default lookback
            if self._params.last_poll_override:
                poll_from_timestamp = datetime.fromisoformat(
                    self._params.last_poll_override
                ).strftime(GRAPH_API_DATETIME_FORMAT)
            else:
                poll_from_timestamp = datetime.fromisoformat(
                    user.lastPolled if user.lastPolled else self._default_lookback_timestamp
                ).strftime(GRAPH_API_DATETIME_FORMAT)
            return poll_from_timestamp

        # Backfill run
        return self._poll_from_timestamp

    def _get_deletions_folder_id(self, user):
        # get Recoverable Items → Deletions folder id
        deletions_folder_id = None

        # step 1: get the folder id
        folder_id_url = (
            f"{MS_GRAPH_EMAIL_FOLDERS_END_POINT.format(user_id=user.userId)}"
            f"?$top={MS_GRAPH_EMAILS_PAGE_SIZE}"
            f"&$filter=displayName eq 'Recoverable Items'"
        )
        try:
            folder_id = self._graph_api_client.do_request(
                url=folder_id_url, method=RequestMethods.GET
            ).json()["value"][0]["id"]

            # step 2: get the deletion sub-folder id if folder_id exists
            if folder_id:
                subfolders_endpoint_base_url = MS_GRAPH_EMAIL_SUB_FOLDERS_END_POINT.format(
                    user_id=user.userId, folder_id=folder_id
                )
                deletion_sub_folder_id_url = (
                    f"{subfolders_endpoint_base_url}"
                    f"?$top={MS_GRAPH_EMAILS_PAGE_SIZE}"
                    f"&$filter=displayName eq 'Deletions'"
                )
                deletions_folder_id = self._graph_api_client.do_request(
                    url=deletion_sub_folder_id_url, method=RequestMethods.GET
                ).json()["value"][0]["id"]
        except Exception as exc:
            logging.error(f"Failed to get deletion folder id: `{exc}`")
        return deletions_folder_id

    def _download_messages(self, user: AddictDict) -> str | None:
        """Downloads the messages for the given user. Iterates over the
        messages and writes them to a file and also checks for the deletion
        folder and downloads the messages from there, if any.

        :param user: user
        :type user: AddictDict
        :return: file name for given user
        :rtype: str
        """
        file_name = f"{user.userId}__messages.ndjson"
        total_messages = 0
        return_none_file_name = False
        logging.info(f"Downloading messages for user {user.userId} and writing to {file_name}")
        target_raw_email_path = get_non_streamed_poller_file_path(
            workflow_name=self._aries_task_input.workflow.name,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            workflow_start_timestamp=self._folder_date,
            lake_prefix=self._lake_prefix,
            custom_path=self._params.custom_lake_path,
            is_evented=False,
        )

        try:
            poll_from_timestamp = self._get_poll_from_timestamp(user=user)
            url = (
                f"{MS_GRAPH_USER_MESSAGES_END_POINT.format(user_id=user.userId)}"
                f"?$top={MS_GRAPH_EMAILS_PAGE_SIZE}"
                f"&$filter=sentDateTime ge {poll_from_timestamp}"
                f" and sentDateTime le {self._poll_to_timestamp}"
                f" and receivedDateTime ge {poll_from_timestamp}"
                f" and receivedDateTime le {self._poll_to_timestamp}"
            )

            # store the messages locally via paginated response
            # Ex: {user_id}__messages.ndjson
            with open(os.path.join(self._local_dir, file_name), "w") as fp:
                for message in self._graph_api_client.paginated_query(api_endpoint=url):
                    # Write only if the message is not empty
                    if message:
                        if self._params.process_mime_content:
                            message = self.process_mime_content(
                                message_as_dict=message,
                                user_id=user.userId,
                                target_raw_email_path=target_raw_email_path,
                            )

                        total_messages += 1
                        fp.write(orjson.dumps(message).decode())
                        fp.write("\n")

            # Get the deletion folder id for the user
            # If the user has deletion folder, download the messages from the deletion folder
            deletions_folder_id = self._get_deletions_folder_id(user)
            if deletions_folder_id:
                deleted_email_base_url = MS_GRAPH_FOLDER_EMAILS_END_POINT.format(
                    user_id=user.userId, folder_id=deletions_folder_id
                )
                deleted_email_url = (
                    f"{deleted_email_base_url}"
                    f"?$top={MS_GRAPH_EMAILS_PAGE_SIZE}"
                    f"&$filter=sentDateTime ge {poll_from_timestamp}"
                    f" and sentDateTime le {self._poll_to_timestamp}"
                    f" and receivedDateTime ge {poll_from_timestamp}"
                    f" and receivedDateTime le {self._poll_to_timestamp}"
                )

                # Since the file is already created, open it in append mode
                # to write the deleted messages
                with open(os.path.join(self._local_dir, file_name), "a") as fp:
                    for deleted_email_message in self._graph_api_client.paginated_query(
                        api_endpoint=deleted_email_url
                    ):
                        # Write only if the message is not empty
                        if deleted_email_message:
                            total_messages += 1
                            fp.write(orjson.dumps(deleted_email_message).decode())
                            fp.write("\n")

        except httpx.HTTPStatusError as exc:
            if exc.response.status_code != httpx.codes.NOT_FOUND:
                raise exc

            # If any http error happens, except for the NOT FOUND error,
            # log it, increment the error count and set file as None
            logging.error(
                f"Exception occurred while downloading messages for user {user.userId}: {exc}"
            )
            with self._lock:
                self._app_metric.metrics["generic"]["errored_count"] += 1
            return_none_file_name = True

        logging.info(f"Downloaded {total_messages} messages for user {user.userId}")
        return None if return_none_file_name else file_name

    def process_mime_content(
        self, message_as_dict: Dict[str, Any], user_id: str, target_raw_email_path: str
    ) -> Dict[str, Any]:
        """See https://steeleye.atlassian.net/browse/ON-4722 for more details.

        The default behavior of the MS Graph Email Poller is to retrieve the
        emails and its metadata in JSON format, as provided by the MS Graph API by default.
        However, some clients require us to pull the raw .eml files as well and serve that in the
        front-end. This is a feature that is not provided by the MS Graph API by default.

        Hence, per message, we will fetch the MIME content of it in a separate endpoint,
        and store it in the file store + update the message JSON object with a
        reference to its location.
        The raw emails will be stored in the file store,
        subject to the same lifecycle policies as the email
        attachment also retrieved by this poller.

        :param message_as_dict: Dictionary that represents an email as
        returned by the MS Graph API
        :param user_id: User id of the user for which the message/email belongs
        :param target_raw_email_path: Partial path where the raw email
        will be stored in the file store
        :return: The updated `message_as_dict` object with the raw email cloud URI
        """
        message_as_dict[SourceEmailColumns.RAW_EMAIL_CLOUD_URI] = None

        # NOTE: MS Graph API supports pulling emails in batches, but we are currently
        # iterating over individual messages + the size of the emails is unpredictable, hence
        # it is not advisable to hold an entire batch in memory.
        # Thus, we are retrieving each individual email's MIME content in a separate request.
        email_endpoint_url = MS_GRAPH_USER_MIME_MESSAGE_BY_ID_END_POINT.format(
            message_id=message_as_dict["id"], user_id=user_id
        )
        email_response = self._graph_api_client.do_request(
            url=email_endpoint_url, method=RequestMethods.GET
        )
        target_file_path = f"{target_raw_email_path}/raw_eml/{message_as_dict['id']}.eml"

        try:
            # write the MIME content to the object store as a raw .eml file
            file_content = email_response.content
            write(
                fs=self._destination_fs,
                target_path=target_file_path,
                file_content=file_content,
            )

            message_as_dict[SourceEmailColumns.RAW_EMAIL_CLOUD_URI] = target_file_path

            logging.debug(
                "Successfully [UPLOADED] raw email to `%s`",
                target_file_path,
            )

        except (botocore.exceptions.ClientError, OSError) as exc:
            # S3(botocore) should be fatal failures
            logging.exception(exc)
            raise exc

        except Exception as exc:
            self._app_metric.metrics["generic"]["errored_count"] += 1
            logging.exception(
                f"[FAILED] Error Count: "
                f"{self._app_metric.metrics['generic']['errored_count']}"
                f". Error to fetch MIME content of message "
                f"{message_as_dict['id']} for user {user_id}"
                f"{exc}"
            )

        return message_as_dict

    def _build_batches(self, users_message_file_names: list[str]) -> None:
        """Build batches for all the successfully downloaded messages files."""
        target_ingress_file_path = get_non_streamed_poller_file_path(
            workflow_name=self._aries_task_input.workflow.name,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            workflow_start_timestamp=self._folder_date,
            lake_prefix=self._lake_prefix,
            custom_path=self._params.custom_lake_path,
        )
        batch_data = []
        batch_counter = 0
        # Iterate over the message files and build the batch data
        # for the messages in the files - and if the message has attachments
        # download the attachments and add the attachment metadata to the message
        for index, file_name in enumerate(users_message_file_names, start=1):
            user_id = file_name.split("__")[0]
            logging.info(f"Processing message file {index} of {len(users_message_file_names)}")

            with open(os.path.join(self._local_dir, file_name)) as raw_messages:
                for raw_message in raw_messages:
                    message = orjson.loads(raw_message)
                    # Add the user_id to the message for debugging purpose
                    message["poller.email.metadata.user_id"] = user_id
                    # Download the attachment to cloud if it has attachments
                    if message.get("hasAttachments"):
                        message["poller.attachments.metadata"] = self._process_message_attachments(
                            user_id=user_id,
                            message_id=message["id"],
                            target_ingress_file_path=target_ingress_file_path,
                        )

                    batch_data.append(message)
                    batch_counter += 1
                    if batch_counter >= self._batch_size:
                        self._dump_batch_and_event(
                            batch_data=batch_data,
                            file_path=target_ingress_file_path,
                        )
                        batch_counter = 0
                        batch_data = []

        # Write the remaining messages to the file
        if batch_data:
            self._dump_batch_and_event(batch_data=batch_data, file_path=target_ingress_file_path)
            del batch_data
            del batch_counter

    def _dump_batch_and_event(self, batch_data: list[dict[str, Any]], file_path: str) -> None:
        """Dump the batch data to the file in cloud and trigger the ingestion
        event."""
        file_path += f"/{self._folder_date.strftime('%Y%m%d')}__{nanoid.generate(size=10)}.ndjson"
        write_successful_ndjson_files(fs=self._destination_fs, file_path=file_path, data=batch_data)

        if self._params.custom_lake_path and not self._params.should_event:
            logging.warning(
                "Skipping ingress io event as custom_lake_path is set and should_event is not set"
            )
            return

        # For each batch, trigger an event with the target path and unique trace id
        event = ingestion_trigger_event(
            target_path=file_path,
            workflow_name=MS_GRAPH_EMAIL_TRANSFORM_WORKFLOW_NAME,
            tenant_name=self._aries_task_input.workflow.tenant,
            stack_name=self._config.stack,
            task_name=self._aries_task_input.workflow.name,
            streamed=False,
            version=self._config.version,
            aries_task_to_domain_dict=dict(),
        )
        logging.debug("[EVENT]: %s", event)
        self._kafka_rest_proxy_client.send(
            io_event=event,
            topic=self._transform_workflow_tw_config.io_topic,
            raise_on_connection_error=True,
            raise_on_serder_error=True,
        )
        logging.info("[SENT] Ingestion Trigger Event.")

    def _process_message_attachments(
        self, user_id: str, message_id: str, target_ingress_file_path: str
    ) -> list[dict[str, Any]]:
        """Fetches the attachments for a message id and writes to object store
        and returns the metadata for the attachments.

        :param user_id: User id of the user for which the message belongs
        :param message_id: Message id for which the attachments are to be fetched
        :param target_ingress_file_path: Target path for the attachments to be written in cloud
        """

        def process_attachment(attachment: dict[str, Any]) -> dict[str, Any] | None:
            attachment_metadata = None
            file_name = attachment["name"]

            try:
                target_file_path = (
                    f"{target_ingress_file_path}/attachments/"
                    f"{message_id}/{attachment['id']}.{file_name.split('.')[-1]}"
                )
                # write the attachment to the object store
                file_content = attachment.get("contentBytes")
                if not file_content:
                    logging.debug("Attachment `%s` is empty", file_name)
                    return attachment_metadata

                write(
                    fs=self._destination_fs,
                    target_path=target_file_path,
                    file_content=pybase64.b64decode(file_content, validate=True),
                )
                # add the attachment metadata
                parsed_uri_path = urlparse(target_file_path)
                attachment_metadata = {
                    "fileName": file_name,
                    "fileType": attachment.get("contentType", "Unknown"),
                    "sizeInBytes": attachment.get("size", 0),
                    "fileInfo": {
                        "location": {
                            "bucket": parsed_uri_path.netloc,
                            "key": parsed_uri_path.path.lstrip("/"),
                        },
                        "contentLength": attachment.get("size", 0),
                        "processed": datetime.now(timezone.utc).strftime(GRAPH_API_DATETIME_FORMAT),
                    },
                }
                logging.debug(
                    "Successfully [DOWNLOADED] `%s` and [UPLOADED] to `%s`",
                    file_name,
                    target_file_path,
                )
            except (botocore.exceptions.ClientError, OSError) as exc:
                # S3(botocore) should be fatal failures
                logging.exception(exc)
                raise exc
            except Exception as exc:
                self._app_metric.metrics["generic"]["errored_count"] += 1
                logging.exception(
                    f"[FAILED] Error Count: "
                    f"{self._app_metric.metrics['generic']['errored_count']}"
                    f". Error to fetch file {file_name}"
                    f"{exc}"
                )
            return attachment_metadata

        attachments_metadata = []
        url = MS_GRAPH_USER_MESSAGE_ATTACHMENTS_END_POINT.format(
            user_id=user_id, message_id=message_id
        )
        url += f"?&$top={MS_GRAPH_ATTACHMENTS_PAGE_SIZE}"
        # 3x fewer workers than the main pool, if the value is > 3, use 1 worker
        with ThreadPoolExecutor(max_workers=self._max_workers // 3 or 1) as executor:
            futures = {
                executor.submit(process_attachment, attachment): attachment
                for attachment in self._graph_api_client.paginated_query(api_endpoint=url)
            }

            for future in as_completed(futures):
                attachment_metadata_res = future.result()
                if attachment_metadata_res:
                    attachments_metadata.append(attachment_metadata_res)
        return attachments_metadata

    def _sync_users(
        self, group_id: str, current_db_users_status: dict[str, dict]
    ) -> dict[str, Any]:
        """_sync_users checks graph API and categorize users into new users,
        existing users, deleted users.

        :param group_id: group id
        :type group_id: str
        :param current_db_users_status: dictionary with current users and their email ids
        :type current_db_users_status: dict[str, str]
        :return: addict dictionary holding all users
        :rtype: dict
        """
        latest_users_status: dict[str, Any] = dict(
            new_users=[], existing_users=[], deleted_users=[]
        )
        url = (
            f"{MS_GRAPH_GROUP_USERS_END_POINT.format(group_id=group_id)}"
            f"?&$top={MS_GRAPH_GROUP_MEMBERS_PAGE_SIZE}"
        )
        for user in self._graph_api_client.paginated_query(api_endpoint=url):
            user_id = user["id"]
            if user_id in current_db_users_status:
                last_polled = (
                    current_db_users_status[user_id]["lastPolled"]
                    if current_db_users_status[user_id]["skipPoll"]
                    else self._poll_to_timestamp
                )  # if the user is skipped from polling then keep the lastPolled unchanged.
                # build the existing users status list
                latest_users_status["existing_users"].append(
                    AddictDict(
                        userId=user_id,
                        userEmailId=current_db_users_status[user_id]["userEmailId"],
                        lastPolled=last_polled,
                    )
                )
            else:
                # New user, we explicitely need the deleted and skipPoll flag to be false
                # to avoid any edge cases when users was marked deleted accidently
                latest_users_status["new_users"].append(
                    AddictDict(
                        userId=user_id,
                        userEmailId=user.get("mail", "") or "",
                        lastPolled=None,
                        deleted=False,
                        skipPoll=False,
                    )
                )

        # get deleted users
        deleted_user_ids = list(
            set(current_db_users_status.keys())
            - {existing_user.userId for existing_user in latest_users_status["existing_users"]}
        )

        # add deleted users to the latest_users_status
        for user_id in deleted_user_ids:
            latest_users_status["deleted_users"].append(
                {
                    "userId": user_id,
                    "userEmailId": current_db_users_status[user_id]["userEmailId"],
                    "lastPolled": self._poll_to_timestamp,
                    "deleted": True,
                }
            )

        return latest_users_status

    def _bulk_upsert_users_batch(
        self, microsoft_tenant_id: str, user_group_id: str, users: list[dict[str, Any]]
    ) -> None:
        """_bulk_upsert_users_batch upserts the users in the DB.

        :param microsoft_tenant_id: microsoft tenant id
        :type microsoft_tenant_id: str
        :param user_group_id: user group id
        :type user_group_id: str
        :param users: list of users
        :type users: list[dict[str, Any]]
        """
        # upsert users in batches of 100
        for index in range(0, len(users), MS_GRAPH_CONFIG_UPSERT_BATCH_SIZE):
            CompatibleMSGraphConfAPIClient.bulk_upsert_users_by_user_group(
                ms_graph_config_api=self._ms_graph_config_api,
                json_body={"users": users[index : index + MS_GRAPH_CONFIG_UPSERT_BATCH_SIZE]},
                tenant_name=self._aries_task_input.workflow.tenant,
                microsoft_tenant_id=microsoft_tenant_id,
                user_group=user_group_id,
                workflow_name=MS_GRAPH_EMAIL_POLLER_WORKFLOW_NAME,
            )

    def _bulk_delete_users_batch(
        self, microsoft_tenant_id: str, user_group_id: str, users: list[dict[str, Any]]
    ) -> None:
        """_bulk_upsert_users_batch upserts the users in the DB.

        :param microsoft_tenant_id: microsoft tenant id
        :type microsoft_tenant_id: str
        :param user_group_id: user group id
        :type user_group_id: str
        :param users: list of users
        :type users: list[dict[str, Any]]
        """
        # delete users in batches of 100
        for index in range(0, len(users), MS_GRAPH_CONFIG_UPSERT_BATCH_SIZE):
            CompatibleMSGraphConfAPIClient.bulk_delete_users_by_user_group(
                ms_graph_config_api=self._ms_graph_config_api,
                json_body={"users": users[index : index + MS_GRAPH_CONFIG_UPSERT_BATCH_SIZE]},
                tenant_name=self._aries_task_input.workflow.tenant,
                microsoft_tenant_id=microsoft_tenant_id,
                user_group=user_group_id,
                workflow_name=MS_GRAPH_EMAIL_POLLER_WORKFLOW_NAME,
            )

    def _update_config_db(
        self,
        latest_users_status: dict[str, Any],
        microsoft_tenant_id: str,
        user_group_id: str,
    ) -> None:
        """_update_config_db updates the config db.

        :param latest_users_status: dict containing the latest users status
        :type latest_users_status: dict[str, Any]
        :param microsoft_tenant_id: microsoft tenant id
        :type microsoft_tenant_id: str
        :param user_group_id: user group id
        :type user_group_id: str
        """
        if latest_users_status["deleted_users"]:
            self._bulk_delete_users_batch(
                microsoft_tenant_id=microsoft_tenant_id,
                user_group_id=user_group_id,
                users=latest_users_status["deleted_users"],
            )

        if latest_users_status["existing_users"]:
            self._bulk_upsert_users_batch(
                microsoft_tenant_id=microsoft_tenant_id,
                user_group_id=user_group_id,
                users=latest_users_status["existing_users"],
            )

        # new users
        if latest_users_status["new_users"]:
            self._bulk_upsert_users_batch(
                microsoft_tenant_id=microsoft_tenant_id,
                user_group_id=user_group_id,
                users=latest_users_status["new_users"],
            )

    def _get_all_users(
        self, tenant_name: str, microsoft_tenant_id: str, user_group_id: str
    ) -> list[AddictDict]:
        """_get_all_users gets the paginated users.

        :param tenant_name: tenant name
        :type tenant_name: str
        :param microsoft_tenant_id: microsoft tenant id
        :type microsoft_tenant_id: str
        :param user_group_id: user group id
        :type user_group_id: str
        :return: list of users
        :rtype: list[AddictDict]
        """
        page_counter = 1
        users_list = []
        while True:
            users = CompatibleMSGraphConfAPIClient.get_users_by_user_group(
                ms_graph_config_api=self._ms_graph_config_api,
                tenant_name=tenant_name,
                microsoft_tenant_id=microsoft_tenant_id,
                user_group=user_group_id,
                page=page_counter,
                workflow_name=MS_GRAPH_EMAIL_POLLER_WORKFLOW_NAME,
                deleted=False,
            ).get("data", [])
            if not users:
                break
            users_list.extend(users)
            page_counter += 1

        logging.info(f"No of users found in db: {len(users_list)}")
        return users_list

    def run_poller(self) -> AriesTaskResult:
        """run_poller exceutes the poller steps.

        :return: task result
        :rtype: AriesTaskResult
        """
        # folder date is used to create folder dates

        if self._backfill:
            logging.info(
                f"poll_from_timestamp {self._poll_from_timestamp}, "
                f"poll_to_timestamp: {self._poll_to_timestamp}"
            )

        ms_graph_configs = CompatibleMSGraphConfAPIClient.get_configs(
            ms_graph_config_api=self._ms_graph_config_api,
            tenant_name=self._aries_task_input.workflow.tenant,
        )

        # filter the ms_graph configs for the tenant and microsoft_tenant_id
        # if provided
        if self._params.microsoft_tenant_id:
            ms_graph_configs = [
                ms_graph_config
                for ms_graph_config in ms_graph_configs
                if ms_graph_config.microsoftTenantId == self._params.microsoft_tenant_id
            ]

        if not ms_graph_configs:
            raise Exception(
                f"No configurations found for tenant: {self._aries_task_input.workflow.tenant}."
                f"microsoft_tenant_id {self._params.microsoft_tenant_id}"
            )

        self._set_up_local_dirs()
        for ms_graph_config in ms_graph_configs:
            # get the secrets from the vault
            api_secrets = self._secrets_client.get_secrets(
                path=MS_GRAPH_EMAIL_SECRET_PATH.format(
                    tenant=self._aries_task_input.workflow.tenant,
                    microsoft_tenant_id=ms_graph_config.microsoftTenantId,
                )
            )

            # create graph api client
            self._graph_api_client = GraphApiClient(
                microsoft_tenant_id=ms_graph_config.microsoftTenantId,
                tenant=self._aries_task_input.workflow.tenant,
                client_id=api_secrets.microsoft_application_id,
                client_secret=api_secrets.microsoft_application_secret,
                timeout=int(self._config.graph_api_timeout_s),
            )

            # get the user groups for the ms_graph config from the config DB
            ms_graph_user_groups = CompatibleMSGraphConfAPIClient.get_user_groups(
                ms_graph_config_api=self._ms_graph_config_api,
                tenant_name=self._aries_task_input.workflow.tenant,
                microsoft_tenant_id=ms_graph_config.microsoftTenantId,
                workflow_name=MS_GRAPH_EMAIL_POLLER_WORKFLOW_NAME,
            )

            users_message_file_names = []
            non_skipped_users = []
            for ms_graph_user_group in ms_graph_user_groups:
                logging.info(f"Processing user group: {ms_graph_user_group.userGroup}")
                # get the users from the user group paginated and download messages
                current_db_users_status = {}
                for user in self._get_all_users(
                    tenant_name=self._aries_task_input.workflow.tenant,
                    microsoft_tenant_id=ms_graph_config.microsoftTenantId,
                    user_group_id=ms_graph_user_group.userGroup,
                ):
                    # add to the set for reference
                    current_db_users_status[user.userId] = user
                    if not user["skipPoll"]:
                        non_skipped_users.append(user)

                # sync data for all the users in the user group in DB
                # parallel execution to extract messages for each user in the user group
                with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                    futures = [
                        executor.submit(partial(self._download_messages, user))
                        for user in non_skipped_users
                    ]
                    for future in as_completed(futures):
                        try:
                            user_messages_file = future.result()
                            if user_messages_file:
                                users_message_file_names.append(user_messages_file)

                        except Exception as exc:
                            logging.error(f"Exception occurred while downloading messages: {exc}")
                            self._app_metric.metrics["generic"]["errored_count"] += 1

                self._build_batches(users_message_file_names=users_message_file_names)

                # update the last polled timestamp for the user if no backfill
                if not self._backfill:
                    # all users dict contains new users, existing users, deleted users
                    latest_users_status = self._sync_users(
                        group_id=ms_graph_user_group.userGroup,
                        current_db_users_status=current_db_users_status,
                    )
                    self._update_config_db(
                        latest_users_status=latest_users_status,
                        microsoft_tenant_id=ms_graph_config.microsoftTenantId,
                        user_group_id=ms_graph_user_group.userGroup,
                    )

                # Reset the non-skipped list for new group and the messages.
                non_skipped_users = []
                users_message_file_names = []

        self._clean_up_local_dirs()
        return AriesTaskResult(app_metric=self._app_metric)
