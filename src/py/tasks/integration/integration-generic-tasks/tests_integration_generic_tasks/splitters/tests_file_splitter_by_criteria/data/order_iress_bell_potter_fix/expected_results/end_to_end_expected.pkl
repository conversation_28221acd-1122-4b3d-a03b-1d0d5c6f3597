��`      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager����pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(��                    �`@                     @X@                     �`@                     �B@             �R@     @X@     j�@                     j�@��numpy��dtype����f8�����R�(K�<�NNNJ����J����K t�bKK���C�t�R�h(�0       
                                          �h�i8�����R�(KhNNNJ����J����K t�bK��ht�R�K��R�hh(�0       w      �      �      ��      <�            �hKK��ht�R��builtins��slice���KKK��R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK>K��h�O8�����R�(K�|�NNNJ����J����K?t�b�]�(�{ingress/raw/order_iress_bell_potter_fix/20241004/00018c310621eb43dcf21af0ea34570eb94e8e5def0bf8c007adac8fdd7726ad_51389.fix��{ingress/raw/order_iress_bell_potter_fix/20241004/000367861c055c706b8bc9284eb298576e2fd5777eeec3028778d18d99d3b029_43836.fix��yingress/raw/order_iress_bell_potter_fix/20241003/ef79741116e7649967668f9b91fd475eae488389d33ed6189edc41432dc49120_531.fix��FIX.4.4��FIX.4.4��FIX.4.4��8�hGhG�IRESS��IRESS��IRESS��20241004-05:44:27.039��20241004-05:00:50.895��20241003-23:59:57.183��STEELEYE��STEELEYE��STEELEYE��
BELLPOTTER��
BELLPOTTER��
BELLPOTTER��AUSBILW��23472878��594823��AUD��AUD��AUD��810554��801731��749920��4�h]h]�136029��134869��126362�h]�2��0�hahaha�AU000000ORI1��AU000000CBA7��AU0000185910��1�haha�ORI��CBA��VTX��pandas._libs.missing��NA���hlhlhb�6�hm�20241004-05:44:26.972��20241004-05:00:50.812��20241003-23:59:57.115�h]�F�hb�CS��CS��CS��	203316106��	103208682��	203300642��ASX��ASX��ASX�hlhlhl�A�h{h{�DEF�hlhl�7916273442686845198��7916273446981039315��7916273442683764685��ASX��ASX��ASX��AUSB��MN886309��PCJC5391��FIXALGO��CMurphy@BellPotter��CCarter@BellPotter��	203316106��	203314270��	203300642��	203316103��	203314267��	203300641��145��252��182�G@1�
=p��G@`�     G?ə�����hlhfhlhl�XASX�hlhl�20250106��20250106�hl�
1820214841�hlhlKhlhl�Active�hlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlhlet�bh(��                                                        	              
                                                                                                                 !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       �hK>��ht�R�K��R���]�(�pandas.core.indexes.base��
_new_Index���h��Index���}�(�data�h3h5K ��h7��R�(KKF��h�O8�����R�(Kh>NNNJ����J����K?t�b�]�(�	S3FileURL��BeginString��
BodyLength��MsgType��	MsgSeqNum��SenderCompID��SendingTime��TargetCompID��OnBehalfOfCompID��Account��AvgPx��CumQty��Currency��ExecID��SecurityIDSource��LastPx��LastQty��OrderID��OrderQty��	OrdStatus��OrdType��
SecurityID��Side��Symbol��Text��TimeInForce��TransactTime��ExecType��	LeavesQty��SecurityType��SecondaryOrderID��SecurityExchange��ExecRestatementReason��
OrderCapacity��ClOrdLinkID��ff_5000��ff_5100��ff_8138��ff_8139��ff_9026��ff_9027��CheckSum��Price��LastCapacity��LastMkt��
ExpireDate��SecondaryExecID��LastLiquidityInd��ff_5265��ClientID��ff_5001��ff_10010��AccountType��LegSide��	LegLastPx��StrikePrice��OrderRestrictions��	PriceType��NoLegs��StopPx��
ExDestination��ff_8200��ff_109��ff_1057��	PartyRole��ff_8140��MaturityDate��PartyID��CFICode��ff_10051�et�b�name�Nu��R�h��pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.