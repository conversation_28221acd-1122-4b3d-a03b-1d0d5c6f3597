# flake8: noqa: E501
# mypy: disable-error-code="attr-defined"
# This needs to be here since the conftest is loaded first
# than any import on the test files
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables
from se_api_svc.utils import datetime
from typing import Dict

os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"

BUCKET_PATH = (
    "s3://test.dev.steeleye.co/aries/ingest/order_tr_fidessa_eod/"
    "2024/10/29/trace_id/file_splitter_by_criteria"
)


@pytest.fixture()
def aries_task_input_default() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id_default",
        name="order_tr_fidessa_eod",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUCKET_PATH}/FIDESSA_EOD_MERGED_20241029_00.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "order_tr_fidessa_eod/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="order_tr_fidessa_eod", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_no_execution_match() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id_no_execution_match",
        name="order_tr_fidessa_eod",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUCKET_PATH}/FIDESSA_EOD_MERGED_20250108_22.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "order_tr_fidessa_eod/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="order_tr_fidessa_eod", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_oppenheimer() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id_oppenheimer",
        name="order_tr_fidessa_eod",
        stack="dev-shared-2",
        tenant="oppenheimer",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUCKET_PATH}/FIDESSA_EOD_MERGED_20241029_00.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "order_tr_fidessa_eod/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="order_tr_fidessa_eod", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_oppenheimer_all_skipped() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id_oppenheimer_all_skipped",
        name="order_tr_fidessa_eod",
        stack="dev-shared-2",
        tenant="oppenheimer",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"{BUCKET_PATH}/FIDESSA_EOD_MERGED_20241029_skipped.csv",
            source_file_uri=(
                "s3://test.dev.steeleye.co/aries/ingress/depository/nonstreamed/"
                "order_tr_fidessa_eod/source_file.csv"
            ),
        )
    )
    task = TaskFieldSet(name="order_tr_fidessa_eod", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def audit_default_file() -> Dict:
    return {
        "input_records": {
            "00014828054ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 2,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014830903ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 1,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014833551ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 10,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014833556ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 8,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014828054ORNY1:2:00118523671TRNY1:PARF:2024-10-29T13:44:28.197000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014828054ORNY1:2:00118523703TRNY1:PARF:2024-10-29T13:44:31.743000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014828054ORNY1:2:00118523692TRNY1:PARF:2024-10-29T13:44:31.271000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014829495ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014829495ORNY1:1:00118532847TRNY1:PARF:2024-10-29T14:15:09.895193Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014829495ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014829495ORNY1:2:00118532846TRNY1:PARF:2024-10-29T14:15:09.889089Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014830903ORNY1:1:00118545961TRNY1:PARF:2024-10-29T14:54:16.312000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014830903ORNY1:1:00118545957TRNY1:PARF:2024-10-29T14:54:16.290000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118523671TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118523703TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118523692TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118545961TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118545957TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118532846TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118532847TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118619262TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118601367TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118571542TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118609337TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118619384TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118632108TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118573374TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118592335TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118596630TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118601788TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118596571TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118612098TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118605217TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118620041TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118565806TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118609705TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118585134TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118635217TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118612102TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118634435TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118572426TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118622510TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118572153TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118626199TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118622471TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118611382TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118609387TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118601788TRNY1:PARF:2024-10-29T18:47:50.366000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118596571TRNY1:PARF:2024-10-29T18:20:11.885000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118612098TRNY1:PARF:2024-10-29T19:27:51.827000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118605217TRNY1:PARF:2024-10-29T19:02:00.782000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118620041TRNY1:PARF:2024-10-29T19:46:39.529000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118565806TRNY1:PARF:2024-10-29T16:25:49.601000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118609705TRNY1:PARF:2024-10-29T19:18:16.047000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118585134TRNY1:PARF:2024-10-29T17:36:48.235000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118635217TRNY1:PARF:2024-10-29T19:59:22.053000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118612102TRNY1:PARF:2024-10-29T19:27:51.827000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118634435TRNY1:PARF:2024-10-29T19:59:08.852000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118619262TRNY1:PARF:2024-10-29T19:45:06.875000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118601367TRNY1:PARF:2024-10-29T18:46:04.111000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118571542TRNY1:PARF:2024-10-29T16:54:06.046000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118609337TRNY1:PARF:2024-10-29T19:17:09.723000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118619384TRNY1:PARF:2024-10-29T19:45:28.829000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118632108TRNY1:PARF:2024-10-29T19:58:10.598000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118573374TRNY1:PARF:2024-10-29T17:01:27.658000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118592335TRNY1:PARF:2024-10-29T18:04:27.650000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118596630TRNY1:PARF:2024-10-29T18:20:34.059000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014834840ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014834840ORNY1:2:00118572153TRNY1:PARF:2024-10-29T16:56:34.958000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014835015ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014835015ORNY1:1:00118572426TRNY1:PARF:2024-10-29T16:57:02.967000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014839430ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014839430ORNY1:1:00118609387TRNY1:PARF:2024-10-29T19:17:17.235000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014839744ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014839744ORNY1:1:00118611382TRNY1:PARF:2024-10-29T19:25:34.469000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840656ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840656ORNY1:2:00118622471TRNY1:PARF:2024-10-29T19:50:03.307000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840689ORNY1:1:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840689ORNY1:1:00118622510TRNY1:PARF:2024-10-29T19:50:03.631000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840859ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014840859ORNY1:2:00118626199TRNY1:PARF:2024-10-29T19:54:06.630000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
    }


@pytest.fixture()
def audit_no_execution_match_file() -> Dict:
    return {
        "input_records": {
            "00015677844ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677845ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677860ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677862ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677970ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677972ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677973ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677976ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677977ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677978ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677980ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677982ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677986ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677987ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00015677988ORNY0:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": [
            "Missing columns in data source {'error': 'Missing columns', 'columns': ['execution.#', 'execution.BUY_SELL', 'execution.COMPLETION_REASON', 'execution.CONTRACT_SIZE', 'execution.COUNTERPARTY_CODE', 'execution.CUST_ALLOCATION_COUNTERPARTY_ID', 'execution.DEALT_TO_SETTLEMENT_RATE', 'execution.EXECUTION_VENUE', 'execution.EXPIRY_DATE', 'execution.FILL_TYPE', 'execution.FOREIGN_CURRENCY', 'execution.FOREIGN_SETTLEMENT_CURRENCY', 'execution.GROSS_PRICE', 'execution.INSTRUMENT_CODE', 'execution.INSTRUMENT_ID', 'execution.LINKED_BROKER_CPTY_ID', 'execution.LIQUIDITY_INDICATOR', 'execution.OPRA_CODE', 'execution.OPTION_TYPE', 'execution.ORDER_ID', 'execution.ORDER_ORIGINATOR_ID', 'execution.ORIGINATING_COUNTERPARTY', 'execution.ORIGINATOR_ORDER_ID', 'execution.PRIMARY_STATE', 'execution.QUANTITY', 'execution.ROOT_ORDER_ID', 'execution.STATE', 'execution.STRIKE_PRICE', 'execution.TIMESTAMP', 'execution.TRADE_DATETIME', 'execution.TRADE_ID', 'execution.TRADE_PART_INDEX', 'execution.TRADE_SOURCE', 'execution.TRADE_VERSION', 'execution.VALUE_DATE', 'execution.__swarm_raw_index__']}",
            "BestExFxRatesPlugin:ecb ref rates not found ",
        ],
    }


@pytest.fixture()
def audit_oppenheimer_file() -> Dict:
    return {
        "input_records": {
            "765_13007_00014835015ORNY1_00118572426TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3411_20901_00014840689ORNY1_00118622510TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "2133_12916_00014834926ORNY1_00118572153TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "888_21139_00014840859ORNY1_00118626199TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3100_20868_00014840656ORNY1_00118622471TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3340_19547_00014839744ORNY1_00118611382TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "4595_19121_00014839430ORNY1_00118609387TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "63_3173_00014828465ORNY1_00118523671TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "1535_3173_00014828465ORNY1_00118523703TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "4623_3173_00014828465ORNY1_00118523692TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "108_6807_00014830903ORNY1_00118545961TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3601_6807_00014830903ORNY1_00118545957TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "154_5260_00014829962ORNY1_00118532846TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "2075_5260_00014829962ORNY1_00118532847TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "00014833551ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 10,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00014833556ORNY1:2:NEWO": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 8,
                    "updated": 0,
                    "status": [
                        "This is a duplicate NEW Order record and will not be processed",
                        "This instrument was created through fallback and was not available in reference data",
                    ],
                }
            },
            "00118619262TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118601367TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118571542TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118609337TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118619384TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118632108TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118573374TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118592335TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118596630TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118601788TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118596571TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118612098TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118605217TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118620041TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118565806TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118609705TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118585134TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118635217TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118612102TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00118634435TRNY1:2024-10-29:NEWT": {
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118601788TRNY1:PARF:2024-10-29T18:47:50.366000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118596571TRNY1:PARF:2024-10-29T18:20:11.885000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118612098TRNY1:PARF:2024-10-29T19:27:51.827000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118605217TRNY1:PARF:2024-10-29T19:02:00.782000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118620041TRNY1:PARF:2024-10-29T19:46:39.529000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118565806TRNY1:PARF:2024-10-29T16:25:49.601000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118609705TRNY1:PARF:2024-10-29T19:18:16.047000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118585134TRNY1:PARF:2024-10-29T17:36:48.235000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118635217TRNY1:PARF:2024-10-29T19:59:22.053000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118612102TRNY1:PARF:2024-10-29T19:27:51.827000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833551ORNY1:2:00118634435TRNY1:PARF:2024-10-29T19:59:08.852000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118619262TRNY1:PARF:2024-10-29T19:45:06.875000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118601367TRNY1:PARF:2024-10-29T18:46:04.111000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118571542TRNY1:PARF:2024-10-29T16:54:06.046000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118609337TRNY1:PARF:2024-10-29T19:17:09.723000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118619384TRNY1:PARF:2024-10-29T19:45:28.829000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118632108TRNY1:PARF:2024-10-29T19:58:10.598000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118573374TRNY1:PARF:2024-10-29T17:01:27.658000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118592335TRNY1:PARF:2024-10-29T18:04:27.650000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
            "00014833556ORNY1:2:00118596630TRNY1:PARF:2024-10-29T18:20:34.059000Z": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "This instrument was created through fallback and was not available in reference data"
                    ],
                }
            },
        },
        "workflow_status": ["BestExFxRatesPlugin:ecb ref rates not found "],
    }


@pytest.fixture()
def audit_skipped_oppenheimer_file() -> Dict:
    return {
        "input_records": {
            "3123_10933_00014833610ORNY1_00118619262TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3336_10933_00014833610ORNY1_00118601367TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "3406_10933_00014833610ORNY1_00118571542TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
            "4095_10933_00014833610ORNY1_00118609337TRNY1": {
                "Order": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
                "RTS22Transaction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Did not match query: \"((`execution.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False))) | ((`order.LINKED_BROKER_CPTY_ID`.astype('str').str.fullmatch('OPEUR',case=False,na=False)))\""
                    ],
                },
            },
        },
        "workflow_status": [
            "Processing will be skipped for the 'FIDESSA_EOD_MERGED_20241029_skipped.csv' file as there are no rows left after pre processing."
        ],
    }
