��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�h                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKKچ��C�t�R�h(�               (       *       6       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(��6                   J�@             J�@        )\���hZ@        )\���hZ@        )\���hZ@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �(\���Q@        �(\���Q@        �(\���Q@        �(\���Q@        ��C�l@        V-��@        ����̌?@        ףp=
W1@        �z�G�G@        ��(\�bA@        �p=
�`@        =
ףp]1@        =
ףp]1@        =
ףp]1@        ��Q�eA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        �(\��eA@        �(\��eA@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ���(\ @        ���(\ @        ���(\ @        }?5^��@        fffff`@        fffff`@        fffff`@        fffff`@        5^�I@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �G�z`@        �G�z`@        H�z�gA@        H�z�gA@        �(\��eA@        H�z�gA@        H�z�gA@        H�z�gA@        {�G�ZG@        �����,;@        ��Q��,@        R���?@        
ףp=�$@        
ףp=�$@        ;�O��n@        !�rh�m@        !�rh�m@        !�rh�m@        �p=
��G@        �p=
��G@        {�G�ZG@        ;�O��n@        �p=
׃@@        �p=
׃@@        �p=
׃@@        !�rh�m@        ;�O��n@        ;�O��n@        !�rh�m@        ;�O��n@        !�rh�m@        
ףp=�@        !�rh�m@        �x�&1@        �x�&1@        o��ʡ@        ��Q��,@        {�G�ZG@        ��C�l@        ��o_�@        ��o_�@        ��K7	@        \���(\G@        �(\�µ@        �(\�µ@        q=
ף0Y@        q=
ף0Y@        /�$@        w��/@        ��S�@        R���?@        ����Mb@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        R���1Y@        333333Y@             J�@             J�@        )\���hZ@        )\���hZ@        )\���hZ@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �(\���Q@        �(\���Q@        �(\���Q@        �(\���Q@        ��C�l@        V-��@        ����̌?@        ףp=
W1@        �z�G�G@        ��(\�bA@        �p=
�`@        =
ףp]1@        =
ףp]1@        =
ףp]1@        ��Q�eA@        -!�lfA@        t���fA@        ��H.gA@        �4�8EgA@        �=�UgA@        V}��bgA@        :��HgA@        ��H.gA@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ���(\ @        ���(\ @        ���(\ @        }?5^��@        fffff`@        fffff`@        fffff`@        fffff`@        5^�I@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �G�z`@        �G�z`@        e�`TRgA@        �=�UgA@        :��HgA@        ��o_gA@        ��C�lgA@        HP�sgA@        {�G�ZG@        �����,;@        ��Q��,@        R���?@        
ףp=�$@        
ףp=�$@        ;�O��n@        vq
�m@        !�rh�m@        !�rh�m@        �p=
��G@        �p=
��G@        {�G�ZG@        !�rh�m@        �p=
׃@@        �p=
׃@@        �p=
׃@@        !�rh�m@        !�rh�m@        !�rh�m@        !�rh�m@        !�rh�m@        !�rh�m@        � �	�@        !�rh�m@        �x�&1@        �x�&1@        o��ʡ@        ��Q��,@        {�G�ZG@        ��C�l@        ��o_�@        ��o_�@        O��e@        ��{�E[G@        �(\�µ@        �(\�µ@        q=
ף0Y@        q=
ף0Y@        /�$@        w��/@        �,�@        R���?@        ����Mb@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        ��镲0Y@        �� �1Y@             ��@              6@              A@             @l@              $@             @\@             |�@             @\@             ʢ@             X�@             ��@             p�@             ȫ@             k�@             �`@             �P@              @@             �L@              @             �E@             ��@            ��@              �@             �f@             `g@             �c@             0y@              i@             �i@             �p@              ]@             �\@             ��@             �@             8�@             ܢ@             !�@             !�@             �@             ԯ@              ^@              ^@              ^@              $@             z�@            ���@             %�@             %�@             %�@             ��@             @�@              g@             ؆@             �V@             �W@             ؆@             ؆@             ��@             ��@             �@             �s@              �@             Px@             ��@              M@             �e@             �@             �d@             \�@             ��@             ��@             �e@             �@             �@             @e@             �@             �e@             �e@             �@             �e@              <@             ��@             >�@             �g@             Ę@            �pA             ��@              5@             ��@            `��@             r�@             ��@             l�@             ��@             p�@             �S@             ��@             ��@             Ѐ@             �@             8�@             ��@             �S@             �S@             �S@             �S@             �S@             �S@              n@             ��@             ��@              A@             @p@             �p@             @\@             ^�@             @�@             �@             X�@             Ȟ@             @�@             �@             k�@             �`@             �P@              @@             �L@              @             �E@             ��@            ���@            ��@             �f@              w@             h�@              �@             ��@             Ԕ@             �@             ܚ@             ��@             ��@             L�@             �@             {�@             !�@             !�@             �@             ԯ@              ^@              n@             �v@              w@             z�@            ���@            ���@             u�@            �9�@            �x�@             @�@              �@             
�@             ��@             z�@             0�@             s�@             �@             ��@             �@             �s@              �@             Px@            ���@              M@              m@             ��@            ���@             \�@            �J�@             ��@            �5�@             �@             ��@             �@            ���@            @��@            ���@            �	�@            @5�@            @<�@             t�@             �@            `��@            �V�@            �pA             ��@             �@            ���@            `��@             r�@            @��@             ��@             ��@             ��@             �S@             ؒ@             ��@             Ѐ@             8�@             8�@             ��@             �@             H�@             ��@             ��@             �@             (�@             �@     ��@     ��@     ��@     ��@     �p@     �p@     �p@     �p@     �p@     �p@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@    ���@    ���@     �`@     �`@     �P@     �P@      @@      @@     �L@     �L@      @      @     �E@     �E@    ��@    ��@    ��@    ��@    ��@    ��@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     {�@     {�@     {�@     {�@     {�@     {�@     {�@     {�@     �@     �@     �@     �@     �@     �@     t�@     t�@      w@      w@      w@      w@      w@      w@      w@      w@     z�@     z�@    @��@    @��@    @��@    @��@    @��@    @��@    @��@    @��@    @��@    @��@      �@      �@      �@      �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     ��@     ��@     �@     �@     ��@     ��@      �@      �@    ���@    ���@    ���@    ���@     �@     �@     �@     �@     �@     �@     �@     �@    �J�@    �J�@    �J�@    �J�@     ��@     ��@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     t�@     t�@     �@     �@    @��@    @��@    @��@    @��@    �pA    �pA     ��@     ��@     ��@     ��@    ���@    ���@    `��@    `��@     r�@     r�@    @��@    @��@     ��@     ��@     ��@     ��@     ��@     ��@     �@     �@     �@     �@     ��@     ��@     8�@     8�@     8�@     8�@     8�@     8�@     ��@     ��@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     �@     ��@             ��@             �p@             �p@             �p@             �@             �@             �@             �@             �@             �@             �@             �@            ���@             �`@             �P@              @@             �L@              @             �E@            ��@            ��@            ��@             �@             �@             �@             �@             �@             �@             �@             �@             �@             {�@             {�@             {�@             {�@             �@             �@             �@             t�@              w@              w@              w@              w@             z�@            @��@            @��@            @��@            @��@            @��@              �@              �@             �@             �@             �@             �@             �@             �@             ��@             �@             ��@              �@            ���@            ���@             �@             �@             �@             �@            �J�@            �J�@             ��@             �@             �@             �@             �@             �@             �@             �@             �@             �@             �@             t�@             �@            @��@            @��@            �pA             ��@             ��@            ���@            `��@             r�@            @��@             ��@             ��@             ��@             �@             �@             ��@             8�@             8�@             8�@             ��@             �@             �@             �@             �@             �@             �@             �@                     ��@              6@              A@             @l@              $@             @\@             |�@             @\@             ʢ@             X�@             ��@             p�@             ȫ@             k�@             �`@             �P@              @@             �L@              @             �E@             ��@            ��@              �@             �f@             `g@             �c@             0y@              i@             �i@             �p@              ]@             �\@             ��@             �@             8�@             ܢ@             !�@             !�@             �@             ԯ@              ^@              ^@              ^@              $@             z�@            ���@             %�@             %�@             %�@             ��@             @�@              g@             ؆@             �V@             �W@             ؆@             ؆@             ��@             ��@             �@             �s@              �@             Px@             ��@              M@             �e@             �@             �d@             \�@             ��@             ��@             �e@             �@             �@             @e@             �@             �e@             �e@             �@             �e@              <@             ��@             >�@             �g@             Ę@            �pA             ��@              5@             ��@            `��@             r�@             ��@             l�@             ��@             p�@             �S@             ��@             ��@             Ѐ@             �@             8�@             ��@             �S@             �S@             �S@             �S@             �S@             �S@              n@             J�@             J�@        )\���hZ@        )\���hZ@        )\���hZ@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �Q��Ka@        �(\���Q@        �(\���Q@        �(\���Q@        �(\���Q@        ��C�l@        V-��@        ����̌?@        ףp=
W1@        �z�G�G@        ��(\�bA@        �p=
�`@        =
ףp]1@        =
ףp]1@        =
ףp]1@        ��Q�eA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        H�z�gA@        �(\��eA@        �(\��eA@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ףp=
�8@        ���(\ @        ���(\ @        ���(\ @        }?5^��@        fffff`@        fffff`@        fffff`@        fffff`@        5^�I@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �x�&1@        �G�z`@        �G�z`@        H�z�gA@        H�z�gA@        �(\��eA@        H�z�gA@        H�z�gA@        H�z�gA@        {�G�ZG@        �����,;@        ��Q��,@        R���?@        
ףp=�$@        
ףp=�$@        ;�O��n@        !�rh�m@        !�rh�m@        !�rh�m@        �p=
��G@        �p=
��G@        {�G�ZG@        ;�O��n@        �p=
׃@@        �p=
׃@@        �p=
׃@@        !�rh�m@        ;�O��n@        ;�O��n@        !�rh�m@        ;�O��n@        !�rh�m@        
ףp=�@        !�rh�m@        �x�&1@        �x�&1@        o��ʡ@        ��Q��,@        {�G�ZG@        ��C�l@        ��o_�@        ��o_�@        ��K7	@        \���(\G@        �(\�µ@        �(\�µ@        q=
ף0Y@        q=
ף0Y@        /�$@        w��/@        ��S�@        R���?@        ����Mb@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        q=
ף0Y@        R���1Y@        333333Y@�h�f8�����R�(Kh NNNJ����J����K t�bKKچ�ht�R�h(�@                                           !       "       #       �hK��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK/Kچ�h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�}�(�labelId��JP3420600003��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hFhGhHhIhJhPua]�}�(hF�JP3420600003�hHhIhJhPua]�}�(hFhUhHhIhJhPua]�}�(hF�GB0009895292�hHhIhJhPua]�}�(hFhZhHhIhJhPua]�}�(hF�GB0009895292�hHhIhJhPua]�}�(hFh_hHhIhJhPua]�}�(hF�GB0009895292�hHhIhJhPua]�}�(hFhdhHhIhJhPua]�}�(hF�IE00B1FZS798�hHhIhJhPua]�}�(hFhihHhIhJhPua]�}�(hF�IE00B1FZS798�hHhIhJhPua]�}�(hFhnhHhIhJhPua]�}�(hF�IE00B1FZS798�hHhIhJhPua]�}�(hFhshHhIhJhPua]�}�(hF�IE00B1FZS798�hHhIhJhPua]�}�(hFhxhHhIhJhPua]�}�(hF�IE00B4L5Y983�hHhIhJhPua]�}�(hFh}hHhIhJhPua]�}�(hF�IE00B4L5Y983�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00B4L5Y983�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00B4L5Y983�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BF540Y54�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE0003WV2ME7�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00B4ND3602�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BD6FTQ80�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BL25JL35�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00B4WXJK79�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BD6FTQ80�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BD6FTQ80�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BD6FTQ80�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BKM4GZ66�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BKM4GZ66�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BKM4GZ66�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00BKM4GZ66�hHhIhJhPua]�}�(hFh�hHhIhJhPua]�}�(hF�IE00B5SSQT16�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00B5SSQT16�hHhIhJhPua]�}�(hFj	  hHhIhJhPua]�}�(hF�IE00B5SSQT16�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BDFK1N50�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00B14X4S71�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00B14X4S71�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00B14X4S71�hHhIhJhPua]�}�(hFj"  hHhIhJhPua]�}�(hF�IE00B14X4S71�hHhIhJhPua]�}�(hFj'  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj,  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj1  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj6  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj;  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj@  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFjE  hHhIhJhPua]�}�(hF�IE00B4WXJK79�hHhIhJhPua]�}�(hFjJ  hHhIhJhPua]�}�(hF�IE00B4WXJK79�hHhIhJhPua]�}�(hFjO  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFjT  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFjY  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFj^  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFjc  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFjh  hHhIhJhPua]�}�(hF�GB00B15KXQ89�hHhIhJhPua]�}�(hFjm  hHhIhJhPua]�}�(hF�IE00BP3QZ601�hHhIhJhPua]�}�(hFjr  hHhIhJhPua]�}�(hF�LU1792117779�hHhIhJhPua]�}�(hFjw  hHhIhJhPua]�}�(hF�XS2425848053�hHhIhJhPua]�}�(hFj|  hHhIhJhPua]�}�(hF�IE00B4ND3602�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�LU1407888483�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�LU1407888483�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BL25JL35�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BL25JL35�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BP3QZ601�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BL25JM42�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BL25JM42�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BL25JM42�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BDFK1N50�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BGPP8L80�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE0003WV2ME7�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�XS2425848053�hHhIhJhPua]�}�(hFj�  hHhIhJhPua]�}�(hF�IE00BP3QZ601�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BF540Y54�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BDFK1N50�hHhIhJhPua]�}�(hFj
  hHhIhJhPua]�}�(hF�IE00BDFK1N50�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BK5MT033�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BP3QZ601�hHhIhJhPua]�}�(hFj  hHhIhJhPua]�}�(hF�IE00BDFL4P12�hHhIhJhPua]�}�(hFj!  hHhIhJhPua]�}�(hF�IE00BDFL4P12�hHhIhJhPua]�}�(hFj&  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFj+  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFj0  hHhIhJhPua]�}�(hF�IE00BF7TPM11�hHhIhJhPua]�}�(hFj5  hHhIhJhPua]�}�(hF�IE0005042456�hHhIhJhPua]�}�(hFj:  hHhIhJhPua]�}�(hF�IE0005042456�hHhIhJhPua]�}�(hFj?  hHhIhJhPua]�}�(hF�IE00B4ND3602�hHhIhJhPua]�}�(hFjD  hHhIhJhPua]�}�(hF�IE00BDZVHB89�hHhIhJhPua]�}�(hFjI  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjN  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjS  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjX  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFj]  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjb  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjg  hHhIhJhPua]�}�(hF�IE00BCRY6441�hHhIhJhPua]�}�(hFjl  hHhIhJhPua�XTKS�jo  �XTKS�jp  �AQXE�jq  �BATE�jr  �CHIX�js  �XLON�jt  �XLON�ju  �XLON�jv  �XLON�jw  �XLON�jx  �XLON�jy  �XLON�jz  �XLON�j{  �XLON�j|  �XLON�j}  �XLON�j~  �XLON�j  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �BATP�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �BATP�j�  �AQXD�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �BATP�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �BATP�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �XLON�j�  �364536��364536��364536��364536��364755��364755��364755��364755��364755��364755��364775��364775��364775��364775��364775��364775��364775��364775��364779��364779��364779��364779��364779��364779��364779��364779��364811��364811��364898��364898��364838��364838��364813��364813��364899��364899��364833��364833��364791��364791��364848��364848��364848��364848��364848��364848��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364837��364837��364837��364837��364837��364837��364837��364837��364893��364893��364893��364893��364893��364893��364884��364884��364863��364863��364863��364863��364863��364863��364863��364863��364859��364859��364821��364821��364821��364821��364821��364821��364821��364821��364821��364821��364807��364807��364807��364807��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364870��364875��364875��364853��364853��364891��364891��364796��364796��364881��364881��364881��364881��364800��364800��364800��364800��364800��364800��364800��364800��364918��364918��364918��364918��364875��364875��364800��364800��364892��364892��364892��364892��364892��364892��364800��364800��364800��364800��364800��364800��364800��364800��364800��364800��364800��364800��364884��364884��364800��364800��364821��364821��364821��364821��364917��364917��364891��364891��364875��364875��364811��364811��364883��364883��364902��364902��364821��364821��364875��364875��364933��364933��364933��364933��364931��364931��364931��364931��364932��364932��364930��364930��364930��364930��364928��364928��364934��364934��364931��364931��364931��364931��364931��364931��364931��364931��364931��364931��364931��364931��364931��364931��1�j�  j�  j�  j�  j�  j�  j�  j�  j�  �2�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29�e(�
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29��
2023-12-29�]�(}�(hF�	id:140760�hH�buyer�hJhM�ARRAY���R�u}�(hF�id:sebe�hH�seller�hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hH�reportDetails.executingEntity�hJhPu}�(hF�id:nore�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPu}�(hF�id:adickie8�hH�trader�hJj�  u}�(hF�id:sebe�hH�counterparty�hJhPue]�(}�(hF�	id:140760�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJhPue]�(}�(hF�	id:140760�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJhPue]�(}�(hF�	id:140760�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:sebe�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140692�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:140702�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:140702�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:596001�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:596001�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08��      hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:kclark206�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:595998�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:595998�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:595998�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�	id:595998�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue]�(}�(hF�	id:595998�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJj�  u}�(hF�lei:213800kpd9lz8zfp1x08�hHj�  hJhPu}�(hF�id:nore�hHj�  hJhPu}�(hF�id:adickie8�hHj�  hJj�  u}�(hF�id:lbrm�hHj�  hJhPue�account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:kclark206��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:adickie8��account:sebe��account:sebe��account:sebe��account:sebe��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:140760��account:140760��account:140760��account:140760��account:140692��account:140692��account:140692��account:140692��account:140692��account:140692��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:595998��account:sebe��account:sebe��account:sebe��account:sebe��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:140702��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001�e(�account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:596001��account:596001��account:596001��account:596001��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:595998��account:595998��account:595998��account:595998��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��account:lbrm��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��lei:213800kpd9lz8zfp1x08��140760��140760��140760��140760��140692��140692��140692��140692��140692��140692��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��140702��140702��140702��140702��140702��140702��140702��140702��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��596001��596001��lbrm��lbrm��596001��596001��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��lbrm��lbrm��lbrm��lbrm��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��595998��sebe��sebe��sebe��sebe��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��140702��140702��140702��140702��140702��140702��140702��140702��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��596001��lbrm��lbrm��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��596001��596001��596001��596001��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��595998��595998��595998��595998��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��sebe��sebe��sebe��sebe��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm�e(�lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��lbrm��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��	kclark206��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��adickie8��Order�jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  jW  �ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��ts3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/multrees_emsx/EXPORTFILLSALL_31209609_20231229.csv��0��0��1��1��10��10��11��11��12��12��15��15��16��16��17��17��18��18��19��19��20��20��21��21��22��22��31��31��32��32��33��33��34��34��35��35��36��36��37��37��38��38��39��39��40��40��41��41��42��42��43��43��44��44��45��45��46��46��47��47��48��48��49��49��50��50��51��51��52��52��53��53��54��54��55��55��56��56��57��57��58��58��59��59��60��60��61��61��62��62��63��63��64��64��65��65��66��66��67��67��68��68��69��69��70��70��71��71��72��72��73��73��74��74��75��75��76��76��77��77��78��78��79��79��80��80��81��81��82��82��83��83��84��84��85��85��86��86��87��87��88��88��89��89��90��90��91��91��92��92��93��93��94��94��95��95��96��96��97��97��98��98��99��99��100��100��101��101��102��102��103��103��104��104��105��105��106��106��107��107��108��108��109��109��110��110��111��111��112��112��113��113��114��114��115��115��116��116��117��117��118��118��119��119��120��120��121��121��122��122��123��123��124��124��125��125��126��126��
Multrees EMSX�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  jo  jo  jp  jp  jq  jq  jr  jr  js  js  jt  jt  ju  ju  jv  jv  jw  jw  jx  jx  jy  jy  jz  jz  j{  j{  j|  j|  j}  j}  j~  j~  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �JPY��JPY��JPY��JPY��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��USD��USD��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��USD��USD��USD��USD��USD��USD��USD��USD��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��      �USD��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��GBP��L.EMS.925572.231816.0�j�  �L.EMS.925572.231816.0�j�  �L.EMS.925572.232740.0�j�  �L.EMS.925572.232740.0�j�  �L.EMS.925572.232740.0�j�  �L.EMS.925572.232857.0�j�  �L.EMS.925572.232857.0�j�  �L.EMS.925572.232857.0�j�  �L.EMS.925572.232857.0�j�  �L.EMS.925572.232859.0�j�  �L.EMS.925572.232859.0�j�  �L.EMS.925572.232859.0�j�  �L.EMS.925572.232859.0�j�  �L.EMS.925572.232891.0�j�  �L.EMS.925572.232927.0�j�  �L.EMS.925572.232901.0�j�  �L.EMS.925572.232893.0�j�  �L.EMS.925572.232929.0�j�  �L.EMS.925572.232897.0�j�  �L.EMS.925572.232883.0�j�  �L.EMS.925572.232903.0�j�  �L.EMS.925572.232903.0�j�  �L.EMS.925572.232903.0�j�  �L.EMS.925572.232911.0�j�  �L.EMS.925572.232911.0�j�  �L.EMS.925572.232911.0�j   �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232899.0�j  �L.EMS.925572.232899.0�j  �L.EMS.925572.232899.0�j	  �L.EMS.925572.232899.0�j
  �L.EMS.925572.232925.0�j  �L.EMS.925572.232925.0�j  �L.EMS.925572.232925.0�j
  �L.EMS.925572.232919.0�j  �L.EMS.925572.232909.0�j  �L.EMS.925572.232909.0�j  �L.EMS.925572.232909.0�j  �L.EMS.925572.232909.0�j  �L.EMS.925572.232907.0�j  �L.EMS.925572.232895.0�j  �L.EMS.925572.232895.0�j  �L.EMS.925572.232895.0�j  �L.EMS.925572.232895.0�j  �L.EMS.925572.232895.0�j  �L.EMS.925572.232889.0�j  �L.EMS.925572.232889.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j  �L.EMS.925572.232911.0�j   �L.EMS.925572.232913.0�j!  �L.EMS.925572.232905.0�j"  �L.EMS.925572.232921.0�j#  �L.EMS.925572.232885.0�j$  �L.EMS.925572.232915.0�j%  �L.EMS.925572.232915.0�j&  �L.EMS.925572.232887.0�j'  �L.EMS.925572.232887.0�j(  �L.EMS.925572.232887.0�j)  �L.EMS.925572.232887.0�j*  �L.EMS.925572.232935.0�j+  �L.EMS.925572.232935.0�j,  �L.EMS.925572.232913.0�j-  �L.EMS.925572.232887.0�j.  �L.EMS.925572.232923.0�j/  �L.EMS.925572.232923.0�j0  �L.EMS.925572.232923.0�j1  �L.EMS.925572.232887.0�j2  �L.EMS.925572.232887.0�j3  �L.EMS.925572.232887.0�j4  �L.EMS.925572.232887.0�j5  �L.EMS.925572.232887.0�j6  �L.EMS.925572.232887.0�j7  �L.EMS.925572.232919.0�j8  �L.EMS.925572.232887.0�j9  �L.EMS.925572.232895.0�j:  �L.EMS.925572.232895.0�j;  �L.EMS.925572.232933.0�j<  �L.EMS.925572.232921.0�j=  �L.EMS.925572.232913.0�j>  �L.EMS.925572.232891.0�j?  �L.EMS.925572.232917.0�j@  �L.EMS.925572.232931.0�jA  �L.EMS.925572.232895.0�jB  �L.EMS.925572.232913.0�jC  �L.EMS.925572.233107.0�jD  �L.EMS.925572.233107.0�jE  �L.EMS.925572.233103.0�jF  �L.EMS.925572.233103.0�jG  �L.EMS.925572.233105.0�jH  �L.EMS.925572.233101.0�jI  �L.EMS.925572.233101.0�jJ  �L.EMS.925572.233099.0�jK  �L.EMS.925572.233109.0�jL  �L.EMS.925572.233103.0�jM  �L.EMS.925572.233103.0�jN  �L.EMS.925572.233103.0�jO  �L.EMS.925572.233103.0�jP  �L.EMS.925572.233103.0�jQ  �L.EMS.925572.233103.0�jR  �L.EMS.925572.233103.0�jS  �BUYI�jT  jT  jT  jT  jT  jT  jT  jT  jT  �SELL�jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jT  jT  jT  jT  e(jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  �2023-12-29T00:00:00.000000Z��2023-12-29T00:00:00.000000Z��2023-12-29T00:00:56.000000Z��2023-12-29T00:00:56.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T09:01:05.000000Z��2023-12-29T10:11:24.000000Z��2023-12-29T10:11:24.000000Z��2023-12-29T10:11:24.000000Z��2023-12-29T10:11:24.000000Z��2023-12-29T10:11:25.000000Z��2023-12-29T10:11:25.000000Z��2023-12-29T10:11:25.000000Z��2023-12-29T10:11:25.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:11:26.000000Z��2023-12-29T10:43:50.000000Z��2023-12-29T10:43:50.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:11.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:23.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:28.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:35.000000Z��2023-12-29T10:44:41.000000Z��2023-12-29T10:44:41.000000Z��2023-12-29T10:44:44.000000Z��2023-12-29T10:44:44.000000Z��2023-12-29T10:44:48.000000Z��2023-12-29T10:44:48.000000Z��2023-12-29T10:44:54.000000Z��2023-12-29T10:44:54.000000Z��2023-12-29T10:44:58.000000Z��2023-12-29T10:44:58.000000Z��2023-12-29T10:44:59.000000Z��2023-12-29T10:44:59.000000Z��2023-12-29T10:44:59.000000Z��2023-12-29T10:44:59.000000Z��2023-12-29T10:45:00.000000Z��2023-12-29T10:45:00.000000Z��2023-12-29T10:45:03.000000Z��2023-12-29T10:45:03.000000Z��2023-12-29T10:45:08.000000Z��2023-12-29T10:45:08.000000Z��2023-12-29T10:45:08.000000Z��2023-12-29T10:45:08.000000Z��2023-12-29T10:45:09.000000Z��2023-12-29T10:45:09.000000Z��2023-12-29T10:45:09.000000Z��2023-12-29T10:45:09.000000Z��2023-12-29T10:45:10.000000Z��2023-12-29T10:45:10.000000Z��2023-12-29T10:45:13.000000Z��2023-12-29T10:45:13.000000Z��2023-12-29T10:45:14.000000Z��2023-12-29T10:45:14.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:34.000000Z��2023-12-29T10:45:58.000000Z��2023-12-29T10:45:58.000000Z��2023-12-29T10:46:02.000000Z��2023-12-29T10:46:02.000000Z��2023-12-29T10:46:06.000000Z��2023-12-29T10:46:06.000000Z��2023-12-29T10:46:17.000000Z��2023-12-29T10:46:17.000000Z��2023-12-29T10:46:24.000000Z��2023-12-29T10:46:24.000000Z��2023-12-29T10:46:24.000000Z��2023-12-29T10:46:24.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:38.000000Z��2023-12-29T10:46:40.000000Z��2023-12-29T10:46:40.000000Z��2023-12-29T10:46:40.000000Z��2023-12-29T10:46:40.000000Z��2023-12-29T10:46:41.000000Z��2023-12-29T10:46:41.000000Z��2023-12-29T10:46:43.000000Z��2023-12-29T10:46:43.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:47.000000Z��2023-12-29T10:46:48.000000Z��2023-12-29T10:46:48.000000Z��2023-12-29T10:46:48.000000Z��2023-12-29T10:46:48.000000Z��2023-12-29T10:46:53.000000Z��2023-12-29T10:46:53.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:58.000000Z��2023-12-29T10:46:59.000000Z��2023-12-29T10:46:59.000000Z��2023-12-29T10:47:08.000000Z��2023-12-29T10:47:08.000000Z��2023-12-29T10:47:49.000000Z��2023-12-29T10:47:49.000000Z��2023-12-29T10:47:49.000000Z��2023-12-29T10:47:49.000000Z��2023-12-29T10:48:49.000000Z��2023-12-29T10:48:49.000000Z��2023-12-29T10:49:07.000000Z��2023-12-29T10:49:07.000000Z��2023-12-29T10:49:28.000000Z��2023-12-29T10:49:28.000000Z��2023-12-29T10:50:31.000000Z��2023-12-29T10:50:31.000000Z��2023-12-29T10:51:09.000000Z��2023-12-29T10:51:09.000000Z��2023-12-29T10:51:19.000000Z��2023-12-29T10:51:19.000000Z��2023-12-29T10:51:28.000000Z��2023-12-29T10:51:28.000000Z��2023-12-29T10:51:40.000000Z��2023-12-29T10:51:40.000000Z��2023-12-29T12:04:40.000000Z��2023-12-29T12:04:40.000000Z��2023-12-29T12:04:40.000000Z��2023-12-29T12:04:40.000000Z��2023-12-29T12:04:45.000000Z��2023-12-29T12:04:45.000000Z��2023-12-29T12:04:45.000000Z��2023-12-29T12:04:45.000000Z��2023-12-29T12:04:49.000000Z��2023-12-29T12:04:49.000000Z��2023-12-29T12:04:56.000000Z��2023-12-29T12:04:56.000000Z��2023-12-29T12:04:56.000000Z��2023-12-29T12:04:56.000000Z��2023-12-29T12:05:02.000000Z��2023-12-29T12:05:02.000000Z��2023-12-29T12:05:08.000000Z��2023-12-29T12:05:08.000000Z��2023-12-29T12:05:38.000000Z��2023-12-29T12:05:38.000000Z��2023-12-29T12:05:38.000000Z��2023-12-29T12:05:38.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:05:39.000000Z��2023-12-29T12:06:01.000000Z��2023-12-29T12:06:01.000000Z��2023-12-29T12:06:07.000000Z��2023-12-29T12:06:07.000000Z��AOTC�j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  �364536�j1  �364536�j2  �364755�j3  �364755�j4  �364755�j5  �364775�j6  �364775�j7  �364775�j8  �364775�j9  �364779�j:  �364779�j;  �364779�j<  �364779�j=  �364811�j>  �364898�j?  �364838�j@  �364813�jA  �364899�jB  �364833�jC  �364791�jD  �364848�jE  �364848�jF  �364848�jG  �364870�jH  �364870�jI  �364870�jJ  �364870�jK  �364870�jL  �364870�jM  �364870�jN  �364870�jO  �364870�jP  �364837�jQ  �364837�jR  �364837�jS  �364837�jT  �364893�jU  �364893�jV  �364893�jW  �364884�jX  �364863�jY  �364863�jZ  �364863�j[  �364863�j\  �364859�j]  �364821�j^  �364821�j_  �364821�j`  �364821�ja  �364821�jb  �364807�jc  �364807�jd  �364870�je  �364870�jf  �364870�jg  �364870�jh  �364870�ji  �364870�jj  �364875�jk  �364853�jl  �364891�jm  �364796�jn  �364881�jo  �364881�jp  �364800�jq  �364800�jr  �364800�js  �364800�jt  �364918�ju  �364918�jv  �364875�jw  �364800�jx  �364892�jy  �364892�jz  �364892�j{  �364800�j|  �364800�j}  �364800�j~  �364800�j  �364800�j�  �364800�j�  �364884�j�  �364800�j�  �364821�j�  �364821�j�  �364917�j�  �364891�j�  �364875�j�  �364811�j�  �364883�j�  �364902�j�  �364821�j�  �364875�j�  �364933�j�  �364933�j�  �364931�j�  �364931�j�  �364932�j�  �364930�j�  �364930�j�  �364928�j�  �364934�j�  �364931�j�  �364931�j�  �364931�j�  �364931�j�  �364931�j�  �364931�j�  �364931�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ��Client Account: MUL140760, Security Name: SEKISUI HOUSE, Internal Order Id: 364536, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3109.0���Client Account: MUL140760, Security Name: SEKISUI HOUSE, Internal Order Id: 364536, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3109.0���Client Account: MUL140760, Security Name: SEKISUI HOUSE, Internal Order Id: 364536, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3109.0���Client Account: MUL140760, Security Name: SEKISUI HOUSE, Internal Order Id: 364536, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3109.0���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140692, Security Name: ASTRAZENECA PLC, Internal Order Id: 364755, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 105.64���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISHR $ TRES 7-10, Internal Order Id: 364775, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 138.36���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL140702, Security Name: ISH CORE MSCI WD, Internal Order Id: 364779, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 71.59���Client Account: MUL596001, Security Name: ISH GLB AGG £H D, Internal Order Id: 364811, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.606���Client Account: MUL596001, Security Name: ISH GLB AGG £H D, Internal Order Id: 364811, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.606���Client Account: MUL596001, Security Name: ISH S&P500EW£HA, Internal Order Id: 364898, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.404���Client Account: MUL596001, Security Name: ISH S&P500EW£HA, Internal Order Id: 364898, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.404���Client Account: MUL596001, Security Name: ISHA GOLD - USD, Internal Order Id: 364838, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.55���Client Account: MUL596001, Security Name: ISHA GOLD - USD, Internal Order Id: 364838, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.55���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364813, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.34���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364813, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.34���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364899, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.51���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364899, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.51���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364833, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.77���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364833, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.77���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364791, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.87���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364791, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.87���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: INVSC BBG CMOD, Internal Order Id: 364848, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 17.365���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: ISHR CORE EM IMI, Internal Order Id: 364837, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 24.965���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: HSBC MSCI EMERGI, Internal Order Id: 364893, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 8.03���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364884, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6973���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364884, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6973���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: ISH $TRS 1-3Y $D, Internal Order Id: 364863, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.45���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364859, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.376���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364859, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.376���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364807, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.94���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364807, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.94���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364807, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.94���Client Account: MUL596001, Security Name: ISHR UK GILT 0-5, Internal Order Id: 364807, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 128.94���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082�e(��Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: WT COPPER, Internal Order Id: 364870, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 34.8082���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: LYX MSCI WLD ESG, Internal Order Id: 364853, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 27.175���Client Account: MUL596001, Security Name: LYX MSCI WLD ESG, Internal Order Id: 364853, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 27.175���Client Account: MUL596001, Security Name: WT ENERGYTMETALS, Internal Order Id: 364891, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 14.31���Client Account: MUL596001, Security Name: WT ENERGYTMETALS, Internal Order Id: 364891, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 14.31���Client Account: MUL596001, Security Name: ISHA GOLD - USD, Internal Order Id: 364796, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.57���Client Account: MUL596001, Security Name: ISHA GOLD - USD, Internal Order Id: 364796, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.57���Client Account: MUL596001, Security Name: AMUNDI US T7-10Y, Internal Order Id: 364881, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 10.27���Client Account: MUL596001, Security Name: AMUNDI US T7-10Y, Internal Order Id: 364881, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 10.27���Client Account: MUL596001, Security Name: AMUNDI US T7-10Y, Internal Order Id: 364881, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 10.27���Client Account: MUL596001, Security Name: AMUNDI US T7-10Y, Internal Order Id: 364881, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 10.27���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364918, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.53���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364918, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.53���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364918, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.53���Client Account: MUL596001, Security Name: X WORLD QLTY 1C, Internal Order Id: 364918, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 47.53���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: X WORLD VALUE 1C, Internal Order Id: 364892, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 33.03���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364884, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6973���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364884, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6973���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: ISH EDGE WLD £HA, Internal Order Id: 364800, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 6.357���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: ISH S&P500EW£HA, Internal Order Id: 364917, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.408���Client Account: MUL596001, Security Name: ISH S&P500EW£HA, Internal Order Id: 364917, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.408���Client Account: MUL596001, Security Name: WT ENERGYTMETALS, Internal Order Id: 364891, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 14.31���Client Account: MUL596001, Security Name: WT ENERGYTMETALS, Internal Order Id: 364891, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 14.31���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH GLB AGG £H D, Internal Order Id: 364811, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.606���Client Account: MUL596001, Security Name: ISH GLB AGG £H D, Internal Order Id: 364811, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.606���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364883, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6947���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364883, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6947���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364902, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6947���Client Account: MUL596001, Security Name: ISH $TRS1-3Y £HD, Internal Order Id: 364902, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.6947���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: SHR $TRS 20+ £HD, Internal Order Id: 364821, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 3.3791���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL596001, Security Name: ISH EDG WLD QLY, Internal Order Id: 364875, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 46.***************���Client Account: MUL595998, Security Name: ISH DIV CMD SWAP, Internal Order Id: 364933, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.1775���Client Account: MUL595998, Security Name: ISH DIV CMD SWAP, Internal Order Id: 364933, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.1775���Client Account: MUL595998, Security Name: ISH DIV CMD SWAP, Internal Order Id: 364933, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.1775���Client Account: MUL595998, Security Name: ISH DIV CMD SWAP, Internal Order Id: 364933, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 5.1775���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISH $FLT BD £H D, Internal Order Id: 364932, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.751���Client Account: MUL595998, Security Name: ISH $FLT BD £H D, Internal Order Id: 364932, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.751���Client Account: MUL595998, Security Name: ISH CORE FTSE100, Internal Order Id: 364930, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 7.527366���Client Account: MUL595998, Security Name: ISH CORE FTSE100, Internal Order Id: 364930, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 7.527366���Client Account: MUL595998, Security Name: ISH CORE FTSE100, Internal Order Id: 364930, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 7.527366���Client Account: MUL595998, Security Name: ISH CORE FTSE100, Internal Order Id: 364930, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 7.527366���Client Account: MUL595998, Security Name: ISHA GOLD - USD, Internal Order Id: 364928, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.57���Client Account: MUL595998, Security Name: ISHA GOLD - USD, Internal Order Id: 364928, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 31.57���Client Account: MUL595998, Security Name: ISH $TIPS0-5 £HD, Internal Order Id: 364934, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.846���Client Account: MUL595998, Security Name: ISH $TIPS0-5 £HD, Internal Order Id: 364934, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 4.846���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658���Client Account: MUL595998, Security Name: ISHR £ ULTSHT BD, Internal Order Id: 364931, Original Order Id: ********, Instructions: , Parent Order Average Execution Price: 100.7658��Market�jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jx  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jT  jT  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jU  jU  jU  jU  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  jT  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  j0  �PASV�jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  jy  �NEWO��FILL�jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  e(jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  jz  j{  ]��GTCV�a]�j}  a]�j}  a]�j}  a]��DAVY�a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  a]�j�  aj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  e(jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jz  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  �s�      j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  �2023-12-28T10:27:46.000000Z��2023-12-28T10:27:46.000000Z��2023-12-28T10:27:46.000000Z��2023-12-28T10:27:46.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T08:59:24.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:29.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:09:30.000000Z��2023-12-29T10:41:13.000000Z��2023-12-29T10:41:13.000000Z��2023-12-29T10:42:08.000000Z��2023-12-29T10:42:08.000000Z��2023-12-29T10:41:28.000000Z��2023-12-29T10:41:28.000000Z��2023-12-29T10:41:17.000000Z��2023-12-29T10:41:17.000000Z��2023-12-29T10:42:10.000000Z��2023-12-29T10:42:10.000000Z��2023-12-29T10:41:22.000000Z��2023-12-29T10:41:22.000000Z��2023-12-29T10:41:01.000000Z��2023-12-29T10:41:01.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:31.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:41:26.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:42:04.000000Z��2023-12-29T10:41:55.000000Z��2023-12-29T10:41:55.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:41.000000Z��2023-12-29T10:41:37.000000Z��2023-12-29T10:41:37.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:10.000000Z��2023-12-29T10:41:10.000000Z��2023-12-29T10:41:10.000000Z��2023-12-29T10:41:10.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:43.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:35.000000Z��2023-12-29T10:41:35.000000Z��2023-12-29T10:41:56.000000Z��2023-12-29T10:41:56.000000Z��2023-12-29T10:41:05.000000Z��2023-12-29T10:41:05.000000Z��2023-12-29T10:41:49.000000Z��2023-12-29T10:41:49.000000Z��2023-12-29T10:41:49.000000Z��2023-12-29T10:41:49.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:42:20.000000Z��2023-12-29T10:42:20.000000Z��2023-12-29T10:42:20.000000Z��2023-12-29T10:42:20.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:42:01.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:55.000000Z��2023-12-29T10:41:55.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:09.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:42:17.000000Z��2023-12-29T10:42:17.000000Z��2023-12-29T10:41:56.000000Z��2023-12-29T10:41:56.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:13.000000Z��2023-12-29T10:41:13.000000Z��2023-12-29T10:41:52.000000Z��2023-12-29T10:41:52.000000Z��2023-12-29T10:42:14.000000Z��2023-12-29T10:42:14.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:20.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T10:41:46.000000Z��2023-12-29T12:03:37.000000Z��2023-12-29T12:03:37.000000Z��2023-12-29T12:03:37.000000Z��2023-12-29T12:03:37.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:33.000000Z��2023-12-29T12:03:33.000000Z��2023-12-29T12:03:26.000000Z��2023-12-29T12:03:26.000000Z��2023-12-29T12:03:26.000000Z��2023-12-29T12:03:26.000000Z��2023-12-29T12:03:24.000000Z��2023-12-29T12:03:24.000000Z��2023-12-29T12:03:39.000000Z��2023-12-29T12:03:39.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-29T12:03:30.000000Z��2023-12-28T10:30:24.000000Z��2023-12-28T10:30:24.000000Z��2023-12-28T10:30:24.000000Z��2023-12-28T10:30:24.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T09:00:50.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:11:02.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T10:43:31.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z��2023-12-29T12:04:21.000000Z�jX  jW  jZ  jY  j\  j[  j^  j]  j`  j_  jb  ja  jd  jc  jf  je  jh  jg  jj  ji  jl  jk  jn  jm  jp  jo  jr  jq  jt  js  jv  ju  jx  jw  jz  jy  j|  j{  j~  j}  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j�  j  j  j  j  j  j  j  j  j
  j	  j  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j  j"  j!  j$  j#  j&  j%  j(  j'  j*  j)  j,  j+  j.  j-  j0  j/  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  e(jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore�G�      �XOFF�G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      j�  G�      �$364536-20231229-B-122910310020641IN3�G�      �$364536-20231229-B-122910310023823IN3�G�      �364755-00029237054EXLO0�G�      �364755-00029237055EXLO0�G�      �364755-00029237056EXLO0�G�      �364775-00029237185EXLO0�G�      �364775-00029237186EXLO0�G�      �364775-00029237187EXLO0�G�      �364775-00029237188EXLO0�G�      �364779-00029237189EXLO0�G�      �364779-00029237190EXLO0�G�      �364779-00029237191EXLO0�G�      �364779-00029237192EXLO0�G�      �364811-00029237559EXLO0�G�      �364898-00029237560EXLO0�G�      �364838-00029237561EXLO0�G�      �364813-00029237562EXLO0�G�      �364899-00029237563EXLO0�G�      �364833-00029237564EXLO0�G�      �364791-00029237565EXLO0�G�      �364848-00029237566EXLO0�G�      �364848-00029237567EXLO0�G�      �364848-00029237568EXLO0�G�      �364870-00029237569EXLO0�G�      �364870-00029237570EXLO0�G�      �364870-00029237571EXLO0�G�      �364870-00029237572EXLO0�G�      �364870-00029237573EXLO0�G�      �364870-00029237574EXLO0�G�      �364870-00029237575EXLO0�G�      �364870-00029237576EXLO0�G�      �364870-00029237577EXLO0�G�      �364837-00029237579EXLO0�G�      �364837-00029237580EXLO0�G�      �364837-00029237581EXLO0�G�      �364837-00029237582EXLO0�G�      �364893-00029237584EXLO0�G�      �364893-00029237585EXLO0�G�      �364893-00029237586EXLO0�G�      �364884-00029237587EXLO0�G�      �364863-00029237590EXLO0�G�      �364863-00029237591EXLO0�G�      �364863-00029237592EXLO0�G�      �364863-00029237593EXLO0�G�      �364859-00029237596EXLO0�G�      �364821-00029237597EXLO0�G�      �364821-00029237598EXLO0�G�      �364821-00029237599EXLO0�G�      �364821-00029237600EXLO0�G�      �364821-00029237601EXLO0�G�      �364807-00029237602EXLO0�G�      �364807-00029237603EXLO0�G�      �364870-00029237604EXLO0�G�      �364870-00029237605EXLO0�G�      �364870-00029237606EXLO0�G�      �364870-00029237607EXLO0�G�      �364870-00029237608EXLO0�G�      �364870-00029237609EXLO0�G�      �364875-00029237644EXLO0�G�      �364853-00029237681EXLO0�G�      �364891-00029237697EXLO0�G�      �364796-00029237750EXLO0�G�      �364881-00029237751EXLO0�G�      �364881-00029237752EXLO0�G�      �364800-00029237753EXLO0�G�      �364800-00029237754EXLO0�G�      �364800-00029237755EXLO0�G�      �364800-00029237756EXLO0�G�      �364918-00029237757EXLO0�G�      �364918-00029237758EXLO0�G�      �364875-00029237759EXLO0�G�      �364800-00029237760EXLO0�G�      �364892-00029237761EXLO0�G�      �364892-00029237762EXLO0�G�      �364892-00029237763EXLO0�G�      �364800-00029237764EXLO0�G�      �364800-00029237765EXLO0�G�      �364800-00029237767EXLO0�G�      �364800-00029237768EXLO0�G�      �364800-00029237769EXLO0�G�      �364800-00029237770EXLO0�G�      �364884-00029237771EXLO0�G�      �364800-00029237795EXLO0�G�      �364821-00029237799EXLO0�G�      �364821-00029237800EXLO0�G�      �364917-00029237813EXLO0�G�      �364891-00029237814EXLO0�G�      �364875-00029237815EXLO0�G�      �364811-00029237818EXLO0�G�      �364883-00029237821EXLO0�G�      �364902-00029237822EXLO0�G�      �364821-00029237823EXLO0�G�      �364875-00029237824EXLO0�G�      �364933-00029238160EXLO0�G�      �364933-00029238161EXLO0�G�      �364931-00029238162EXLO0�G�      �364931-00029238163EXLO0�G�      �364932-00029238164EXLO0�G�      �364930-00029238171EXLO0�G�      �364930-00029238172EXLO0�G�      �364928-00029238174EXLO0�G�      �364934-00029238176EXLO0�G�      �364931-00029238178EXLO0�G�      �364931-00029238179EXLO0�G�      �364931-00029238180EXLO0�G�      �364931-00029238181EXLO0�G�      �364931-00029238182EXLO0�G�      �364931-00029238183EXLO0�G�      �364931-00029238184EXLO0�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      e(G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  G�      G�      jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      jU  jU  jU  jU  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh(�x                                                               	       
                     
                                                                                                         $       %       &       '       )       +       ,       -       .       /       0       1       2       3       4       5       7       8       9       :       �hK/��ht�R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(�pandas._libs.missing��NA���jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�b�builtins��slice���K;K<K��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   K<K=K��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   K=K>K��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   K>K?K��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   K?K@K��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   K@KAK��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   KAKBK��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   KBKCK��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   KCKDK��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   jb   et�bjf   KDKEK��R�K��R�hh7h9K ��h;��R�(KKKچ�hA�]�(]�(hEj�  j�  j�  j�  j�  j�  e]�(hRj�  j�  j�  j�  j�  j�  e]�(hTj�  j�  j�  j�  j�  j�  e]�(hWj�  j�  j�  j�  j�  j�  e]�(hYj�  j�  j�  j�  j�  j�  e]�(h\j�  j�  j�  j�  j�  j�  e]�(h^j�  j�  j�  j�  j�  j�  e]�(haj�  j�  j�  j�  j�  j  e]�(hcj  j  j  j
  j  j  e]�(hfj  j  j  j  j  j  e]�(hhj  j   j"  j$  j&  j(  e]�(hkj+  j-  j/  j1  j3  j5  e]�(hmj8  j:  j<  j>  j@  jB  e]�(hpjE  jG  jI  jK  jM  jO  e]�(hrjR  jT  jV  jX  jZ  j\  e]�(huj_  ja  jc  je  jg  ji  e]�(hwjl  jn  jp  jr  jt  jv  e]�(hzjy  j{  j}  j  j�  j�  e]�(h|j�  j�  j�  j�  j�  j�  e]�(hj�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j  j  j  e]�(h�j  j
  j  j  j  j  e]�(h�j  j  j  j  j  j  e]�(h�j"  j$  j&  j(  j*  j,  e]�(h�j/  j1  j3  j5  j7  j9  e]�(h�j<  j>  j@  jB  jD  jF  e]�(h�jI  jK  jM  jO  jQ  jS  e]�(h�jV  jX  jZ  j\  j^  j`  e]�(h�jc  je  jg  ji  jk  jm  e]�(h�jp  jr  jt  jv  jx  jz  e]�(h�j}  j  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j  j  j  j  j	  e]�(h�j  j  j  j  j  j  e]�(h�j  j  j  j  j!  j#  e]�(h�j&  j(  j*  j,  j.  j0  e]�(h�j3  j5  j7  j9  j;  j=  e]�(h�j@  jB  jD  jF  jH  jJ  e]�(h�jM  jO  jQ  jS  jU  jW  e]�(h�jZ  j\  j^  j`  jb  jd  e]�(h�jg  ji  jk  jm  jo  jq  e]�(h�jt  jv  jx  jz  j|  j~  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j   e]�(h�j  j  j  j	  j  j
  e]�(h�j  j  j  j  j  j  e]�(h�j  j  j!  j#  j%  j'  e]�(h�j*  j,  j.  j0  j2  j4  e]�(j  j7  j9  j;  j=  j?  jA  e]�(j  jD  jF  jH  jJ  jL  jN  e]�(j  jQ  jS  jU  jW  jY  j[  e]�(j  j^  j`  jb  jd  jf  jh  e]�(j  jk  jm  jo  jq  js  ju  e]�(j
  jx  jz  j|  j~  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  e]�(j!  j�  j�  j�  j�  j�  j�  e]�(j$  j�  j�  j�  j�  j�  j�  e]�(j&  j�  j�  j�  j 	  j	  j	  e]�(j)  j	  j		  j	  j
	  j	  j	  e]�(j+  j	  j	  j	  j	  j	  j	  e]�(j.  j!	  j#	  j%	  j'	  j)	  j+	  e]�(j0  j.	  j0	  j2	  j4	  j6	  j8	  e]�(j3  j;	  j=	  j?	  jA	  jC	  jE	  e]�(j5  jH	  jJ	  jL	  jN	  jP	  jR	  e]�(j8  jU	  jW	  jY	  j[	  j]	  j_	  e]�(j:  jb	  jd	  jf	  jh	  jj	  jl	  e]�(j=  jo	  jq	  js	  ju	  jw	  jy	  e]�(j?  j|	  j~	  j�	  j�	  j�	  j�	  e]�(jB  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jD  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jG  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jI  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jL  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jN  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jQ  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jS  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jV  j�	  j�	  j�	  j�	  j�	  j�	  e]�(jX  j�	  j 
  j
  j
  j
  j
  e]�(j[  j
  j
  j
  j
  j
  j
  e]�(j]  j
  j
  j
  j
  j 
  j"
  e]�(j`  j%
  j'
  j)
  j+
  j-
  j/
  e]�(jb  j2
  j4
  j6
  j8
  j:
  j<
  e]�(je  j?
  jA
  jC
  jE
  jG
  jI
  e]�(jg  jL
  jN
  jP
  jR
  jT
  jV
  e]�(jj  jY
  j[
  j]
  j_
  ja
  jc
  e]�(jl  jf
  jh
  jj
  jl
  jn
  jp
  e]�(jo  js
  ju
  jw
  jy
  j{
  j}
  e]�(jq  j�
  j�
  j�
  j�
  j�
  j�
  e]�(jt  j�
  j�
  j�
  j�
  j�
  j�
  e]�(jv  j�
  j�
  j�
  j�
  j�
  j�
  e]�(jy  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j{  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j~  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j  j  j  j  j
  j  e]�(j�  j  j  j  j  j  j  e]�(j�  j  j  j   j"  j$  j&  e]�(j�  j)  j+  j-  j/  j1  j3  e]�(j�  j6  j8  j:  j<  j>  j@  e]�(j�  jC  jE  jG  jI  jK  jM  e]�(j�  jP  jR  jT  jV  jX  jZ  e]�(j�  j]  j_  ja  jc  je  jg  e]�(j�  jj  jl  jn  jp  jr  jt  e]�(j�  jw  jy  j{  j}  j  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j  j  e]�(j�  j  j  j
  j  j  j  e]�(j�  j  j  j  j  j  j  e]�(j�  j   j"  j$  j&  j(  j*  e]�(j�  j-  j/  j1  j3  j5  j7  e]�(j�  j:  j<  j>  j@  jB  jD  e]�(j�  jG  jI  jK  jM  jO  jQ  e]�(j�  jT  jV  jX  jZ  j\  j^  e]�(j�  ja  jc  je  jg  ji  jk  e]�(j�  jn  jp  jr  jt  jv  jx  e]�(j�  j{  j}  j  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j
  j
  j
  j
  e]�(j�  j

  j
  j
  j
  j
  j
  e]�(j�  j
  j
  j
  j
  j
  j!
  e]�(j�  j$
  j&
  j(
  j*
  j,
  j.
  e]�(j�  j1
  j3
  j5
  j7
  j9
  j;
  e]�(j�  j>
  j@
  jB
  jD
  jF
  jH
  e]�(j�  jK
  jM
  jO
  jQ
  jS
  jU
  e]�(j�  jX
  jZ
  j\
  j^
  j`
  jb
  e]�(j   je
  jg
  ji
  jk
  jm
  jo
  e]�(j  jr
  jt
  jv
  jx
  jz
  j|
  e]�(j  j
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j  j  j  j  j  j	  j  e]�(j   j  j  j  j  j  j  e]�(j#  j  j  j  j!  j#  j%  e]�(j%  j(  j*  j,  j.  j0  j2  e]�(j(  j5  j7  j9  j;  j=  j?  e]�(j*  jB  jD  jF  jH  jJ  jL  e]�(j-  jO  jQ  jS  jU  jW  jY  e]�(j/  j\  j^  j`  jb  jd  jf  e]�(j2  ji  jk  jm  jo  jq  js  e]�(j4  jv  jx  jz  j|  j~  j�  e]�(j7  j�  j�  j�  j�  j�  j�  e]�(j9  j�  j�  j�  j�  j�  j�  e]�(j<  j�  j�  j�  j�  j�  j�  e]�(j>  j�  j�  j�  j�  j�  j�  e]�(jA  j�  j�  j�  j�  j�  j�  e]�(jC  j�  j�  j�  j�  j�  j�  e]�(jF  j�  j�  j�  j�  j�  j�  e]�(jH  j�  j�  j�  j�  j�  j�  e]�(jK  j�  j�  j�  j�  j�  j�  e]�(jM  j�  j�  j�  j�  j   j  e]�(jP  j  j  j	  j  j
  j  e]�(jR  j  j  j  j  j  j  e]�(jU  j  j!  j#  j%  j'  j)  e]�(jW  j,  j.  j0  j2  j4  j6  e]�(jZ  j9  j;  j=  j?  jA  jC  e]�(j\  jF  jH  jJ  jL  jN  jP  e]�(j_  jS  jU  jW  jY  j[  j]  e]�(ja  j`  jb  jd  jf  jh  jj  e]�(jd  jm  jo  jq  js  ju  jw  e]�(jf  jz  j|  j~  j�  j�  j�  e]�(ji  j�  j�  j�  j�  j�  j�  e]�(jk  j�  j�  j�  j�  j�  j�  e]�(jn  j�  j�  j�  j�  j�  j�  eet�bjf   KEKFK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j�!  �Index���}�(�data�h7h9K ��h;��R�(KKF��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����__instrument_venue__���R�j�!  �"__fb_is_created_through_fallback__���R��id��buySell��date��marketIdentifiers.parties��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� transactionDetails.ultimateVenue��transactionDetails.price��transactionDetails.priceAverage��transactionDetails.quantity��%transactionDetails.cumulativeQuantity�� transactionDetails.priceCurrency��transactionDetails.basketId��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��priceFormingData.price��&executionDetails.outgoingOrderAddlInfo��executionDetails.orderType��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��+executionDetails.liquidityProvisionActivity��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��executionDetails.validityPeriod��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��transactionDetails.venue��!orderIdentifiers.transactionRefNo��&executionDetails.shortSellingIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��__asset_class__��__contract_multiplier__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__option_strike_price__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�j�!  �pandas.core.indexes.range��
RangeIndex���}�(j"  N�start�K �stop�Kڌstep�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.