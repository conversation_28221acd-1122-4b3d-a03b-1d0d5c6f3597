���p      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(��                                                                                           ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKK���C�t�R�h(�(              '       (       *       4       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(�@      ���QPG@��Q�~L@��Q�~L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@�uK1A�?x�=\r\�?�D���\�?�D���\�?��X@�?����?�?fffff�?@fffffNd@�G�z.E@q=
ף�Q@q=
ף�Q@fffff�?@fffffNd@�G�z.E@q=
ף�Q@�!�uq�?�������?�p=
ף�?     @�@    �,A    @$A     �u@     �~@     0q@     �H@     pw@     p�@     `g@   `��dA    )CBA    �	�@   ��RHA    �LA    � A     X�@     X�@     ��@     |�@     |�@     X�@     X�@     ��@     �@H�z �$sA     @�@    ?A     @�@    �,A    @$A     �u@     �~@     0q@     �H@     pw@     p�@     `g@   `��dA    )CBA    �	�@   ��RHA    �LA    � A     X�@     X�@     ��@     |�@     |�@     X�@     X�@     ��@     �@H�z �$sA     @�@    ?A     @�@    �,A    @$A     �u@     �~@     0q@     �H@     pw@     p�@     `g@   `��dA    )CBA    �	�@   ��RHA    �LA    � A     X�@     X�@     ��@     |�@     |�@     X�@     X�@     ��@     �@H�z �$sA     @�@    ?A     @�@    �,A    @$A     �u@     �~@     0q@     �H@     pw@     p�@     `g@   `��dA    )CBA    �	�@   ��RHA    �LA    � A     X�@     X�@     ��@     |�@     |�@     X�@     X�@     ��@     �@H�z �$sA     @�@    ?A���QPG@��Q�~L@��Q�~L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@fffff�L@�uK1A�?x�=\r\�?�D���\�?�D���\�?��X@�?����?�?fffff�?@fffffNd@�G�z.E@q=
ף�Q@q=
ף�Q@fffff�?@fffffNd@�G�z.E@q=
ף�Q@�!�uq�?�������?�p=
ף�?�h�f8�����R�(Kh NNNJ����J����K t�bKK��ht�R�h(�0                             !       "       #       �hK��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK+K��h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�}�(�labelId��DE000A2YN504��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�AU000000JBH7�hHhIhJhPua]�}�(hF�XXXX$$USDAUDFXSPOT�hHhIhJhPua]�}�(hF�XXXX$$GBPAUDFXSPOT�hHhIhJhPua]�}�(hF�XXXX$$GBPAUDFXSPOT�hHhIhJhPua]�}�(hF�XXXX$$GBPAUDFXSPOT�hHhIhJhPua]�}�(hF�XXXX$$USDAUDFXSPOT�hHhIhJhPua]�}�(hF�XXXX$$USDAUDFXSPOT�hHhIhJhPua]�}�(hF�US9345502036�hHhIhJhPua]�}�(hF�IE00BWT6H894�hHhIhJhPua]�}�(hF�US98954M2008�hHhIhJhPua]�}�(hF�US5312297550�hHhIhJhPua]�}�(hF�US5312297550�hHhIhJhPua]�}�(hF�US9345502036�hHhIhJhPua]�}�(hF�IE00BWT6H894�hHhIhJhPua]�}�(hF�US98954M2008�hHhIhJhPua]�}�(hF�US5312297550�hHhIhJhPua]�}�(hF�XXXX$$AUDUSDFXSPOT�hHhIhJhPua]�}�(hF�AU000000ONE9�hHhIhJhPua]�}�(hF�AU000000EGH7�hHhIhJhPua�2024051300RL��2024051200E0��2024051200E0��2024051300C1��2024051300C1��2024051300C1��2024051300C1��2024051300C1��2024051300C1��2024051300C1��2024051200DR��2024051200DL��2024051200DN��2024051200DP��2024051200DT��2024051200DV��2024051200ER��2024051200ES��2024051200ET��2024051200EU��2024051200EY��2024051200F7��2024051200F8��2024051200F9��2024051200FA��2024051300C5��2024051300CI��2024051300CQ��2��1�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13��
2024-05-13�]�(}�(hF�id:wiluk�hH�buyer�hJhM�ARRAY���R�u}�(hF�id:caledonia�hH�seller�hJh�u}�(hF�id:caledonia�hH�sellerDecisionMaker�hJh�u}�(hF�id:wiluk�hH�counterparty�hJhPu}�(hF�id:caledonia�hH�reportDetails.executingEntity�hJhPu}�(hF�id:kl�hH�trader�hJh�u}�(hF�id:kl�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hH�buyerDecisionMaker�hJh�u}�(hF�id:brnjy�hHh�hJh�u}�(hF�id:brnjy�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:brnjy�hHh�hJh�u}�(hF�id:brnjy�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsaau�hHh�hJh�u}�(hF�id:gsaau�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsfxc�hHh�hJh�u}�(hF�id:gsfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hH�:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:ctfxc�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ctfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ubfxc�hHh�hJh�u}�(hF�id:ubfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:ubfxc�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ubfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ubfxc�hHh�hJh�u}�(hF�id:ubfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ctfxc�hHh�hJh�u}�(hF�id:ctfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gspb�hHh�hJh�u}�(hF�id:gspb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gspb�hHh�hJh�u}�(hF�id:gspb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gspb�hHh�hJh�u}�(hF�id:gspb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gspb�hHh�hJh�u}�(hF�id:gspb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:jpma�hHh�hJh�u}�(hF�id:jpma�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:ubsa�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ubsa�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:cntpb�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:cntpb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:cntpb�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:cntpb�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:ubsa�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ubsa�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:ops�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:ops�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:gsfxc�hHh�hJh�u}�(hF�id:gsfxc�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:al�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:al�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:bpsyd�hHh�hJh�u}�(hF�id:bpsyd�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:js�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:js�hHh�hJhPue]�(}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:caledonia�hHh�hJh�u}�(hF�id:ords�hHh�hJh�u}�(hF�id:ords�hHh�hJhPu}�(hF�id:caledonia�hHh�hJhPu}�(hF�id:js�hHh�hJh�u}�(hF�id:jwv�hHj�  hJhPu}�(hF�id:js�hHh�hJhPue�id:kl��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:al��id:js��id:js��id:wiluk��id:brnjy��id:brnjy��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsfxc��id:ctfxc��id:ubfxc��id:ubfxc��id:ubfxc��id:ctfxc��id:gspb��id:gspb��id:gspb��id:gspb��id:jpma��id:ubsa��id:cntpb��id:cntpb��id:ubsa��id:gsfxc��id:bpsyd��id:ords��id:wiluk��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:ctfxc��id:caledonia��id:ubfxc��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:ubsa��id:cntpb��id:cntpb��id:ubsa��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:brnjy��id:brnjy��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsaau��id:gsfxc��id:caledonia��id:ubfxc��id:caledonia��id:ubfxc��id:ctfxc��id:gspb��id:gspb��id:gspb��id:gspb��id:jpma��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:gsfxc��id:bpsyd��id:ords��id:caledonia�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �id:caledonia�G�      �id:caledonia�G�      G�      G�      G�      G�      G�      G�      �id:caledonia��id:caledonia��id:caledonia��id:caledonia�G�      G�      G�      �id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��wiluk��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��ctfxc��	caledonia��ubfxc��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��ubsa��cntpb��cntpb��ubsa��	caledonia��	caledonia��	caledonia��	caledonia��brnjy��brnjy��gsaau��gsaau��gsaau��gsaau��gsaau��gsaau��gsaau��gsfxc��	caledonia��ubfxc��	caledonia��ubfxc��ctfxc��gspb��gspb��gspb��gspb��jpma��	caledonia��	caledonia��	caledonia��	caledonia��gsfxc��bpsyd��ords��wiluk��brnjy��brnjy��gsaau��gsaau��gsaau��gsaau��gsaau��gsaau��gsaau��gsfxc��ctfxc��ubfxc��ubfxc��ubfxc��ctfxc��gspb��gspb��gspb��gspb��jpma��ubsa��cntpb��cntpb��ubsa��gsfxc��bpsyd��ords��	caledonia�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �	caledonia�G�      �	caledonia�G�      G�      G�      G�      G�      G�      G�      �	caledonia��	caledonia��	caledonia��	caledonia�G�      G�      G�      �kl��al��al��al��al��al��al��al��al��al��al��al��al��al��al��al��ops��ops��ops��ops��ops��ops��ops��ops��ops��al��js��js��Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ��s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/caledonia_eze/Caledonia_SteelEye_Executions_051324_testing.csv��0��1��2��3��4��5��6��7��8��9��11��12��13��14��15��16��17��18��19��20��21��22��23��24��25��26��28��29��EZE OMS�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      �id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia�G�      �id:caledonia�G�      �id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia��id:caledonia�G�      G�      G�      G�      �id:caledonia��id:caledonia��id:caledonia�G�      �	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia�G�      �	caledonia�G�      �	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia��	caledonia�G�      G�      G�      G�      �	caledonia��	caledonia��	caledonia��EUR��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��USD��GBP��USD��USD��USD��USD��GBP��USD��USD��USD��AUD��AUD��SELL��BUYI�j;  j;  j;  j;  j;  j;  j;  j;  j;  j:  j;  j:  j;  j;  j;  j;  j;  j;  j;  j:  j:  j:  j:  j;  j;  j;  �2024-05-13T16:58:02.000000Z��2024-05-13T03:24:28.000000Z��2024-05-13T03:54:26.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T07:10:33.000000Z��2024-05-13T01:50:23.000000Z��2024-05-13T01:48:14.000000Z��2024-05-13T01:46:15.000000Z��2024-05-13T01:46:15.000000Z��2024-05-13T01:49:56.000000Z��2024-05-13T01:49:48.000000Z��2024-05-13T04:51:46.000000Z��2024-05-13T04:51:46.000000Z��2024-05-13T04:51:46.000000Z��2024-05-13T04:51:46.000000Z��2024-05-13T04:51:46.000000Z��2024-05-13T04:52:31.000000Z��2024-05-13T04:52:31.000000Z��2024-05-13T04:52:31.000000Z��2024-05-13T04:52:31.000000Z��2024-05-13T07:18:13.000000Z��2024-05-13T07:39:07.000000Z��2024-05-13T07:45:29.000000Z��AOTC�jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  �2024051300001OZ��202405120000008��202405120000009��202405130000001��202405130000002��202405130000006��202405130000007��202405130000008��202405130000009��20240513000000A��202405120000007��202405120000003��202405120000001��202405120000002��202405120000006��202405120000005��2024051200000VU��2024051200000VV��2024051200000VW��2024051200000VX��2024051200000W1��2024051200000WA��2024051200000WB��2024051200000WC��2024051200000WD��20240513000000B��2024051300000VR��2024051300000VV��2024051300RL|202405100102��2024051200E0|20240512005H��2024051200E0|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051300C1|20240512005H��2024051200DR|20240512006M��2024051200DL|20240512006N��2024051200DN|20240512006Q��2024051200DP|20240512008P��2024051200DT|20240512008T��2024051200DV|20240512008U��2024051200ER|20240512009H��2024051200ES|20240512009I��2024051200ET|20240512009J��2024051200EU|20240512009K��2024051200EY|20240512009K��2024051200F7|20240512009X��2024051200F8|20240512009Y��2024051200F9|20240512009Z��2024051200FA|2024051200A0��2024051300C5|20240513006U��2024051300CI|202405130078��2024051300CQ|20240513007B�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j:  j;  j;  j;  j;  j;  j;  j;  j;  j;  j;  j:  j;  j:  j;  j;  j;  j;  j;  j;  j;  j:  j:  j:  j:  j;  j;  j;  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  j:  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j:  G�      j:  G�      G�      G�      G�      G�      G�      G�      j:  j:  j:  j:  G�      G�      G�      �PASV�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �PARF�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�e(h�h�h�h�h�h�h�h��NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  �id:kl��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:al��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:ops��id:al��id:js��id:js�j:  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j:  G�      j:  G�      G�      G�      G�      G�      G�      G�      j:  j:  j:  j:  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv��id:jwv�et�bh(�X                                                               	       
                     
                                                                                                                       $       %       &       )       +       ,       -       .       /       0       1       2       3       5       �hK+��ht�R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(�pandas._libs.missing��NA���j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�b�builtins��slice���K6K7K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K7K8K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K8K9K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K9K:K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K:K;K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K;K<K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K<K=K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K=K>K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K>K?K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K?K@K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K@KAK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(]�(hEh�h�h�h�h�h�h�e]�(hRh�h�h�h�h�j  j  e]�(hUj  j  j
  j  j  j  j  e]�(hXj  j  j  j  j  j  j!  e]�(h[j$  j&  j(  j*  j,  j.  j0  e]�(h^j3  j5  j7  j9  j;  j=  j?  e]�(hajB  jD  jF  jH  jJ  jL  jN  e]�(hdjQ  jS  jU  jW  jY  j[  j]  e]�(hgj`  jb  jd  jf  jh  jj  jl  e]�(hjjo  jq  js  ju  jw  jy  j{  e]�(hmj~  j�  j�  j�  j�  j�  j�  j�  e]�(hpj�  j�  j�  j�  j�  j�  j�  j�  e]�(hsj�  j�  j�  j�  j�  j�  j�  j�  e]�(hvj�  j�  j�  j�  j�  j�  j�  j�  e]�(hyj�  j�  j�  j�  j�  j�  j�  j�  e]�(h|j�  j�  j�  j�  j�  j�  j�  j�  e]�(hj�  j�  j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j   j  j  e]�(h�j  j	  j  j
  j  j  j  j  e]�(h�j  j  j  j  j   j"  j$  j&  e]�(h�j)  j+  j-  j/  j1  j3  j5  j7  e]�(h�j:  j<  j>  j@  jB  jD  jF  jH  e]�(h�jK  jM  jO  jQ  jS  jU  jW  jY  e]�(h�j\  j^  j`  jb  jd  jf  jh  jj  e]�(h�jm  jo  jq  js  ju  jw  jy  j{  e]�(h�j~  j�  j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  j�  j�  e]�(h�j�  j�  j�  j�  j�  j�  j�  j�  eet�bj�  KAKBK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���je  �Index���}�(�data�h7h9K ��h;��R�(KKB��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����"__fb_is_created_through_fallback__���R��id��buySell��date��marketIdentifiers.parties��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��!sellerDecisionMakerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_seller_dec_maker__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� buyerDecisionMakerFileIdentifier��__fallback_buyer_dec_maker__��transactionDetails.price��transactionDetails.quantity�� transactionDetails.priceCurrency��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity��!orderIdentifiers.transactionRefNo��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��priceFormingData.price��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��&executionDetails.shortSellingIndicator��+executionDetails.liquidityProvisionActivity��&executionDetails.mesFirstExecutionOnly��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.tradingDateTime��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��3tradersAlgosWaiversIndicators.shortSellingIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��__asset_class__��__contract_multiplier__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__instrument_venue__��__option_strike_price__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�jg  �pandas.core.indexes.range��
RangeIndex���}�(j�  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.