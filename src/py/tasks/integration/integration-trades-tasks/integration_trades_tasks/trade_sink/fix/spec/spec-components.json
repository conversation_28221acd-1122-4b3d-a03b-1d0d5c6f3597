[{"ComponentID": "1000", "ComponentType": "Block", "CategoryID": "Common", "Name": "CommissionData", "AbbrName": "<PERSON><PERSON>", "NotReqXML": "0", "Description": "The CommissionDate component block is used to carry commission information such as the type of commission and the rate.", "Added": "FIX.4.3"}, {"ComponentID": "1001", "ComponentType": "Block", "CategoryID": "Common", "Name": "DiscretionInstructions", "AbbrName": "DiscInstr", "NotReqXML": "0", "Description": "The presence of DiscretionInstructions component block on an order indicates that the trader wishes to display one price but will accept trades at another price.", "Added": "FIX.4.4"}, {"ComponentID": "1002", "ComponentType": "Block", "CategoryID": "Common", "Name": "FinancingDetails", "AbbrName": "FinDetls", "NotReqXML": "0", "Description": "Component block is optionally used only for financing deals to identify the legal agreement under which the deal was made and other unique characteristics of the transaction. The AgreementDesc field refers to base standard documents such as MRA 1996 Repurchase Agreement, GMRA 2000 Bills Transaction (U.K.), MSLA 1993 Securities Loan – Amended 1998, for example.", "Added": "FIX.4.4"}, {"ComponentID": "1003", "ComponentType": "Block", "CategoryID": "Common", "Name": "Instrument", "AbbrName": "Instrmt", "NotReqXML": "0", "Description": "The Instrument component block contains all the fields commonly used to describe a security or instrument. Typically the data elements in this component block are considered the static data of a security, data that may be commonly found in a security master database. The Instrument component block can be used to describe any asset type supported by FIX.", "Added": "FIX.4.3"}, {"ComponentID": "1004", "ComponentType": "Block", "CategoryID": "Common", "Name": "InstrumentExtension", "AbbrName": "InstrmtExt", "NotReqXML": "0", "Description": "The InstrumentExtension component block identifies additional security attributes that are more commonly found for Fixed Income securities.", "Added": "FIX.4.4"}, {"ComponentID": "1005", "ComponentType": "Block", "CategoryID": "Common", "Name": "InstrumentLeg", "AbbrName": "Leg", "NotReqXML": "0", "Description": "The InstrumentLeg component block, like the Instrument component block, contains all the fields commonly used to describe a security or instrument. In the case of the InstrumentLeg component block it describes a security used in multileg-oriented messages.", "Added": "FIX.4.3"}, {"ComponentID": "1006", "ComponentType": "Block", "CategoryID": "Common", "Name": "LegBenchmarkCurveData", "AbbrName": "BnchmkCurve", "NotReqXML": "0", "Description": "The LegBenchmarkCurveData is used to convey the benchmark information used for pricing in a multi-legged Fixed Income security.", "Added": "FIX.4.4"}, {"ComponentID": "1007", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "LegStipulations", "AbbrName": "<PERSON><PERSON>", "NotReqXML": "0", "Description": "The LegStipulations component block has the same usage as the Stipulations component block, but for a leg instrument in a multi-legged security.", "Added": "FIX.4.4"}, {"ComponentID": "1008", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "NestedParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The NestedParties component block is identical to the Parties Block. It is used in other component blocks and repeating groups when nesting will take place resulting in multiple occurrences of the Parties block within a single FIX message.. Use of NestedParties under these conditions avoids multiple references to the Parties block within the same message which is not allowed in FIX tag/value syntax.", "Added": "FIX.4.3"}, {"ComponentID": "1011", "ComponentType": "Block", "CategoryID": "Common", "Name": "OrderQtyData", "AbbrName": "OrdQty", "NotReqXML": "0", "Description": "The OrderQtyData component block contains the fields commonly used for indicating the amount or quantity of an order. Note that when this component block is marked as \"required\" in a message either one of these three fields must be used to identify the amount: OrderQty, CashOrderQty or OrderPercent (in the case of CIV).", "Added": "FIX.4.3"}, {"ComponentID": "1012", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "Parties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The Parties component block is used to identify and convey information on the entities both central and peripheral to the financial transaction represented by the FIX message containing the Parties Block. The Parties block allows many different types of entites to be expressed through use of the PartyRole field and identifies the source of the PartyID through the the PartyIDSource.", "Added": "FIX.4.3"}, {"ComponentID": "1013", "ComponentType": "Block", "CategoryID": "Common", "Name": "PegInstructions", "AbbrName": "PegInstr", "NotReqXML": "0", "Description": "The Peg Instructions component block is used to tie the price of a security to a market event such as opening price, mid-price, best price. The Peg Instructions block may also be used to tie the price to the behavior of a related security.", "Added": "FIX.4.4"}, {"ComponentID": "1014", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "PositionAmountData", "AbbrName": "Amt", "NotReqXML": "0", "Description": "The PositionAmountData component block is used to report netted amounts associated with position quantities. In the listed derivatives market the amount is generally expressing a type of futures variation or option premium. In the equities market this may be the net pay or collect on a given position.", "Added": "FIX.4.4"}, {"ComponentID": "1015", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "PositionQty", "AbbrName": "Qty", "NotReqXML": "0", "Description": "The PositionQty component block specifies the various types of position quantity in the position life-cycle including start-of-day, intraday, trade, adjustments, and end-of-day position quantities. Quantities are expressed in terms of long and short quantities.", "Added": "FIX.4.4"}, {"ComponentID": "1016", "ComponentType": "Block", "CategoryID": "Common", "Name": "SettlInstructionsData", "AbbrName": "SetInstr", "NotReqXML": "0", "Description": "The SettlInstructionsData component block is used to convey key information regarding standing settlement and delivery instructions. It also provides a reference to standing settlement details regarding the source, delivery instructions, and settlement parties", "Added": "FIX.4.4"}, {"ComponentID": "1017", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "SettlParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The SettlParties component block is used in a similar manner as Parties Block within the context of settlement instruction messages to distinguish between parties involved in the settlement and parties who are expected to execute the settlement process.", "Added": "FIX.4.4"}, {"ComponentID": "1018", "ComponentType": "Block", "CategoryID": "Common", "Name": "SpreadOrBenchmarkCurveData", "AbbrName": "SprdBnchmkCurve", "NotReqXML": "0", "Description": "The SpreadOrBenchmarkCurveData component block is primarily used for Fixed Income to convey spread to a benchmark security or curve.", "Added": "FIX.4.3"}, {"ComponentID": "1019", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "Stipulations", "AbbrName": "<PERSON><PERSON>", "NotReqXML": "0", "Description": "The Stipulations component block is used in Fixed Income to provide additional information on a given security. These additional information are usually not considered static data information.", "Added": "FIX.4.3"}, {"ComponentID": "1020", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "TrdRegTimestamps", "AbbrName": "TrdRegTS", "NotReqXML": "0", "Description": "The TrdRegTimestamps component block is used to express timestamps for an order or trade that are required by regulatory agencies These timesteamps are used to identify the timeframes for when an order or trade is received on the floor, received and executed by the broker, etc.", "Added": "FIX.4.4"}, {"ComponentID": "1021", "ComponentType": "Block", "CategoryID": "Common", "Name": "UnderlyingInstrument", "AbbrName": "Undly", "NotReqXML": "0", "Description": "The UnderlyingInstrument component block, like the Instrument component block, contains all the fields commonly used to describe a security or instrument. In the case of the UnderlyingInstrument component block it describes an instrument which underlies the primary instrument Refer to the Instrument component block comments as this component block mirrors Instrument, except for the noted fields.", "Added": "FIX.4.3"}, {"ComponentID": "1022", "ComponentType": "Block", "CategoryID": "Common", "Name": "YieldData", "AbbrName": "Yield", "NotReqXML": "0", "Description": "The YieldData component block conveys yield information for a given Fixed Income security.", "Added": "FIX.4.3"}, {"ComponentID": "1023", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "UnderlyingStipulations", "AbbrName": "<PERSON><PERSON>", "NotReqXML": "0", "Description": "The UnderlyingStipulations component block has the same usage as the Stipulations component block, but for an underlying security.", "Added": "FIX.4.4"}, {"ComponentID": "1024", "ComponentType": "Block", "CategoryID": "Session", "Name": "StandardHeader", "AbbrName": "BaseHeader", "NotReqXML": "0", "Description": "The standard FIX message header", "Added": "FIX.4.0"}, {"ComponentID": "1025", "ComponentType": "Block", "CategoryID": "Session", "Name": "StandardTrailer", "AbbrName": "Trlr", "NotReqXML": "1", "Description": "The standard FIX message trailer", "Added": "FIX.4.0"}, {"ComponentID": "1009", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "NestedParties2", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The NestedParties2 component block is identical to the Parties Block. It is used in other component blocks and repeating groups when nesting will take place resulting in multiple occurrences of the Parties block within a single FIX message.. Use of NestedParties2 under these conditions avoids multiple references to the Parties block within the same message which is not allowed in FIX tag/value syntax.", "Added": "FIX.4.4"}, {"ComponentID": "1010", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "NestedParties3", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The NestedParties3 component block is identical to the Parties Block. It is used in other component blocks and repeating groups when nesting will take place resulting in multiple occurrences of the Parties block within a single FIX message.. Use of NestedParties3 under these conditions avoids multiple references to the Parties block within the same message which is not allowed in FIX tag/value syntax.", "Added": "FIX.4.4"}, {"ComponentID": "2001", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "OrderMassHandling", "Name": "AffectedOrdGrp", "AbbrName": "AffectOrd", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2002", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Allocation", "Name": "AllocAckGrp", "AbbrName": "AllocAck", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2003", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Allocation", "Name": "AllocGrp", "AbbrName": "Alloc", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2004", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "BidCompReqGrp", "AbbrName": "CompReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2005", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "BidCompRspGrp", "AbbrName": "CompRsp", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2006", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "BidDescReqGrp", "AbbrName": "DescReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2007", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ClrInstGrp", "AbbrName": "ClrInst", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2008", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CollateralManagement", "Name": "CollInqQualGrp", "AbbrName": "Qual", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2009", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "CompIDReqGrp", "AbbrName": "CIDReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2010", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "CompIDStatGrp", "AbbrName": "CIDStat", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2011", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ContAmtGrp", "AbbrName": "ContAmt", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2012", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ContraGrp", "AbbrName": "Contra", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2013", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Confirmation", "Name": "CpctyConfGrp", "AbbrName": "Cpcty", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2014", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Allocation", "Name": "ExecAllocGrp", "AbbrName": "AllExc", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2015", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CollateralManagement", "Name": "ExecCollGrp", "AbbrName": "CollExc", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2017", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtGrp", "AbbrName": "Inst", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2018", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtLegExecGrp", "AbbrName": "Exec", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2019", "ComponentType": "OptimisedImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtLegGrp", "AbbrName": "Leg", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "76", "Added": "FIX.4.4"}, {"ComponentID": "2020", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtLegIOIGrp", "AbbrName": "IOI", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2021", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtLegSecListGrp", "AbbrName": "SecL", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2022", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrmtMDReqGrp", "AbbrName": "InstReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2023", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "InstrmtStrkPxGrp", "AbbrName": "StrkPX", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2024", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Indication", "Name": "IOIQualGrp", "AbbrName": "Qual", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2025", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MultilegOrders", "Name": "LegOrdGrp", "AbbrName": "Ord", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2026", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "LegPreAllocGrp", "AbbrName": "PreAll", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2027", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "LegQuotGrp", "AbbrName": "Quot", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2028", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "LegQuotStatGrp", "AbbrName": "QuoteStat", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2029", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "LinesOfTextGrp", "AbbrName": "TxtLn", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2030", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "ListOrdGrp", "AbbrName": "Ord", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2031", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "MDFullGrp", "AbbrName": "Full", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2032", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "MDIncGrp", "AbbrName": "Inc", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2033", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "MDReqGrp", "AbbrName": "Req", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2034", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "MDRjctGrp", "AbbrName": "Rjct", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2035", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MiscFeesGrp", "AbbrName": "MiscFees", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2036", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "OrdAllocGrp", "AbbrName": "OrdAlloc", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2037", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "ProgramTrading", "Name": "OrdListStatGrp", "AbbrName": "ListStat", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2038", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "PositionMaintenance", "Name": "PosUndInstrmtGrp", "AbbrName": "PosUnd", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2039", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "PreAllocGrp", "AbbrName": "PreAll", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2040", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "PreAllocMlegGrp", "AbbrName": "PreAllocMleg", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2041", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotCxlEntriesGrp", "AbbrName": "QuotCxlEntry", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2042", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotEntryAckGrp", "AbbrName": "QuotEntryAck", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2043", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotEntryGrp", "AbbrName": "QuotEntry", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2044", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotQualGrp", "AbbrName": "QuotQual", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2045", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotReqGrp", "AbbrName": "QuotReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2046", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotReqLegsGrp", "AbbrName": "Leg", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2047", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotReqRjctGrp", "AbbrName": "QuotReqRej", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2048", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotSetAckGrp", "AbbrName": "QuotSetAck", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2049", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "QuotSetGrp", "AbbrName": "QuotSet", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2050", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SecuritiesReferenceData", "Name": "RelSymDerivSecGrp", "AbbrName": "<PERSON><PERSON><PERSON><PERSON>", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2051", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "QuotationNegotiation", "Name": "RFQReqGrp", "AbbrName": "RFQReq", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2052", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "RegistrationInstruction", "Name": "RgstDistInstGrp", "AbbrName": "RgDtlInst", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2053", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "RegistrationInstruction", "Name": "RgstDtlsGrp", "AbbrName": "RgDtl", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2054", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "RoutingGrp", "AbbrName": "Rtg", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2055", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SecuritiesReferenceData", "Name": "SecListGrp", "AbbrName": "SecL", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2056", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SecuritiesReferenceData", "Name": "SecTypesGrp", "AbbrName": "SecT", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2057", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SettlementInstruction", "Name": "SettlInstGrp", "AbbrName": "SetInst", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2058", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CrossOrders", "Name": "SideCrossOrdCxlGrp", "AbbrName": "SideCrossCxl", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2059", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CrossOrders", "Name": "SideCrossOrdModGrp", "AbbrName": "SideCrossMod", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2060", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdAllocGrp", "AbbrName": "Alloc", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2061", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdCapRptSideGrp", "AbbrName": "RptSide", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2062", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CollateralManagement", "Name": "TrdCollGrp", "AbbrName": "TrdColl", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4"}, {"ComponentID": "2063", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdInstrmtLegGrp", "AbbrName": "TrdLeg", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2064", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "TrdgSesGrp", "AbbrName": "TrdSes", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2065", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "CollateralManagement", "Name": "UndInstrmtCollGrp", "AbbrName": "UndColl", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2066", "ComponentType": "OptimisedImplicitBlockRepeating", "CategoryID": "Common", "Name": "UndInstrmtGrp", "AbbrName": "Undly", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2069", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdCapDtGrp", "AbbrName": "TrdCapDt", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2070", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "EvntGrp", "AbbrName": "Evnt", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2071", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "SecAltIDGrp", "AbbrName": "AID", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2072", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "LegSecAltIDGrp", "AbbrName": "LegAID", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2073", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "UndSecAltIDGrp", "AbbrName": "UndAID", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2074", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "AttrbGrp", "AbbrName": "Attrb", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2075", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DlvyInstGrp", "AbbrName": "DlvInst", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2076", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "SettlPtysSubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2077", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "PtysSubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2078", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "NstdPtysSubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2085", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Session", "Name": "HopGrp", "AbbrName": "Hop", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "95", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2079", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "NstdPtys2SubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2080", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "NstdPtys3SubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4"}, {"ComponentID": "2086", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "StrategyParametersGrp", "AbbrName": "StrtPrmGrp", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "95", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2087", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SecuritiesReferenceData", "Name": "SecLstUpdRelSymGrp", "AbbrName": "SecL", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2088", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SecuritiesReferenceData", "Name": "SecLstUpdRelSymsLegGrp", "AbbrName": "SecLstUpdRelSymsLegGrp", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1026", "ComponentType": "BlockRepeating", "CategoryID": "PositionMaintenance", "Name": "UnderlyingAmount", "AbbrName": "UndDlvAmt", "NotReqXML": "0", "Description": "The UnderlyingAmount component block is used to supply the underlying amounts, dates, settlement status and method for derivative positions.", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1027", "ComponentType": "BlockRepeating", "CategoryID": "PositionMaintenance", "Name": "ExpirationQty", "AbbrName": "Qty", "NotReqXML": "0", "Description": "The ExpirationQty component block identified the expiration quantities and type of expiration.", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1032", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "InstrumentParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The use of this component block is restricted to instrument definition only and is not permitted to contain transactional information. Only a specified subset of party roles will be supported within the InstrumentParty block.", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2093", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "InstrumentPtysSubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1028", "ComponentType": "BlockRepeating", "CategoryID": "TradeCapture", "Name": "SideTrdRegTS", "AbbrName": "TrdRegTS", "NotReqXML": "0", "Description": "The SideTrdRegTS component block is used to convey regulatory timestamps associated with one side of a multi-sided trade event.", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2094", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdCapRptAckSideGrp", "AbbrName": "RptSide", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1033", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "UndlyInstrumentParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The use of this component block is restricted to instrument definition only and is not permitted to contain transactional information. Only a specified subset of party roles will be supported within the InstrumentParty block.", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2096", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "UndlyInstrumentPtysSubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1029", "ComponentType": "Block", "CategoryID": "Common", "Name": "DisplayInstruction", "AbbrName": "DsplyInstr", "NotReqXML": "0", "Description": "The DisplayInstruction component block is used to convey instructions on how a reserved order is to be handled in terms of when and how much of the order quantity is to be displayed to the market.", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1030", "ComponentType": "Block", "CategoryID": "Common", "Name": "TriggeringInstruction", "AbbrName": "TrgrInstr", "NotReqXML": "0", "Description": "The TriggeringInstruction component block specifies the conditions under which an order will be triggered by related market events as well as the behavior of the order in the market once it is triggered.", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1031", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "RootParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The RootParties component block is a version of the Parties component block used to provide root information regarding the owning and entering parties of a transaction.", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2097", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "RootSubParties", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2099", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "TrdSessLstGrp", "AbbrName": "TrdSessLstGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "2098", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MsgTypeGrp", "AbbrName": "MsgTypeGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.4.4", "AddedEP": "-1"}, {"ComponentID": "1058", "ComponentType": "Block", "CategoryID": "Common", "Name": "SecurityTradingRules", "AbbrName": "SecTrdgRules", "NotReqXML": "0", "Description": "Ths SecurityTradingRules component block is used as part of security definition to specify the specific security\"s standard trading parameters such as trading session eligibility and other attributes of the security.", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2139", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "SettlDetails", "AbbrName": "SettlDetails", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2101", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SettlementInstruction", "Name": "SettlObligationInstructions", "AbbrName": "SettlObligInst", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2102", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "SecSizesGrp", "AbbrName": "SecSizesGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2103", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "StatsIndGrp", "AbbrName": "StatsIndGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "1060", "ComponentType": "XMLDataBlock", "CategoryID": "Common", "Name": "SecurityXML", "AbbrName": "SecXML", "NotReqXML": "0", "Description": "The SecurityXML component is used for carrying security description or definition in an XML format. See \"Specifying an FpML product specification from within the FIX Instrument Block\" for more information on using this component block with FpML as a guideline.", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2118", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "TickRules", "AbbrName": "TickRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2119", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "StrikeRules", "AbbrName": "StrkRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2120", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MaturityRules", "AbbrName": "MatRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2121", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "SecondaryPriceLimits", "AbbrName": "PxLmts2", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2122", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "PriceLimits", "AbbrName": "PxLmts", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2123", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MarketDataFeedTypes", "AbbrName": "MDFeedTyps", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2124", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "LotTypeRules", "AbbrName": "LotTypeRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2125", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MatchRules", "AbbrName": "MtchRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2126", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ExecInstRules", "AbbrName": "ExecInstRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2127", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "TimeInForceRules", "AbbrName": "TmInForceRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2128", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "OrdTypeRules", "AbbrName": "OrdTypRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2129", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "TradingSessionRules", "AbbrName": "TrdgSesRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2130", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "TradingSessionRulesGrp", "AbbrName": "TrdgSesRulesGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2131", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "BaseTradingRules", "AbbrName": "BaseTrdgRules", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2132", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "MarketSegmentGrp", "AbbrName": "MktSegGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2104", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DerivativeInstrumentPartySubIDsGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2141", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DerivativeInstrumentParties", "AbbrName": "Pty", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2136", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DerivativeInstrumentAttribute", "AbbrName": "Attrb", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2135", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "NestedInstrumentAttribute", "AbbrName": "Attrb", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2140", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "DerivativeInstrument", "AbbrName": "DerivInstrmt", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2105", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DerivativeSecurityAltIDGrp", "AbbrName": "AID", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2106", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "DerivativeEventsGrp", "AbbrName": "Evnt", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2133", "ComponentType": "ImplicitBlock", "CategoryID": "Common", "Name": "DerivativeSecurityDefinition", "AbbrName": "DerivSecDef", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2107", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "RelSymDerivSecUpdGrp", "AbbrName": "<PERSON><PERSON><PERSON><PERSON>", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "1061", "ComponentType": "XMLDataBlock", "CategoryID": "Common", "Name": "DerivativeSecurityXML", "AbbrName": "SecXML", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2108", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "UnderlyingLegSecurityAltIDGrp", "AbbrName": "AID", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2134", "ComponentType": "ImplicitBlock", "CategoryID": "TradeCapture", "Name": "UnderlyingLegInstrument", "AbbrName": "Instrmt", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2109", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TradeCapLegUnderlyingsGrp", "AbbrName": "TradeCapLegUndlyGrp", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2137", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "UserManagement", "Name": "UsernameGrp", "AbbrName": "UserGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2111", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "OrderMassHandling", "Name": "NotAffectedOrdersGrp", "AbbrName": "NotAffectedOrdersGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2112", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "SingleGeneralOrderHandling", "Name": "FillsGrp", "AbbrName": "FillsGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2113", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "TradeCapture", "Name": "TrdRepIndicatorsGrp", "AbbrName": "TrdRepIndicatorsGrp", "NotReqXML": "0", "Description": "", "Updated": "FIX.5.0SP1", "UpdatedEP": "97", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "1057", "ComponentType": "Block", "CategoryID": "Common", "Name": "ApplicationSequenceControl", "AbbrName": "ApplSeqCtrl", "NotReqXML": "0", "Description": "The ApplicationSequenceControl is used for application sequencing and recovery. Consisting of ApplSeqNum (1181), ApplID (1180), ApplLastSeqNum (1350), and ApplResendFlag (1352), FIX application messages that carries this component block will be able to use application level sequencing. ApplID, ApplSeqNum and ApplLastSeqNum fields identify the application id, application sequence number and the previous application sequence number (in case of intentional gaps) on each application message that carries this block.", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2115", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Application", "Name": "ApplIDRequestGrp", "AbbrName": "ApplIDReqGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2116", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Application", "Name": "ApplIDRequestAckGrp", "AbbrName": "ApplIDReqAckGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2117", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Application", "Name": "ApplIDReportGrp", "AbbrName": "ApplIDRptGrp", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2142", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "NstdPtys4SubGrp", "AbbrName": "Sub", "NotReqXML": "0", "Description": "", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "1059", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "NestedParties4", "AbbrName": "Pty", "NotReqXML": "0", "Description": "The NestedParties4 component block is identical to the Parties Block. It is used in other component blocks and repeating groups when nesting will take place resulting in multiple occurrences of the Parties block within a single FIX message. Use of NestedParties4 under these conditions avoids multiple references to the Parties block within the same message which is not allowed in FIX tag/value syntax.", "Added": "FIX.5.0", "AddedEP": "-1"}, {"ComponentID": "2143", "ComponentType": "ImplicitBlock", "CategoryID": "TradeCapture", "Name": "TradeReportOrderDetail", "AbbrName": "TrdRptOrdDetl", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "77"}, {"ComponentID": "1062", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "RateSource", "AbbrName": "RtSrc", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "82"}, {"ComponentID": "1063", "ComponentType": "BlockRepeating", "CategoryID": "Common", "Name": "TargetParties", "AbbrName": "TgtPty", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "85"}, {"ComponentID": "2144", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "EventCommunication", "Name": "NewsRefGrp", "AbbrName": "Refs", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "90"}, {"ComponentID": "2145", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ComplexEvents", "AbbrName": "CmplxEvnt", "NotReqXML": "0", "Description": "The ComplexEvent Group is a repeating block which allows an unlimited number and types of events in the lifetime of an option to be specified.", "Added": "FIX.5.0SP1", "AddedEP": "92"}, {"ComponentID": "2146", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ComplexEventDates", "AbbrName": "EvntDts", "NotReqXML": "0", "Description": "The ComplexEventDate and ComplexEventTime components are used to constrain a complex event to a specific date range or time range. If specified the event is only effective on or within the specified dates and times.", "Added": "FIX.5.0SP1", "AddedEP": "92"}, {"ComponentID": "2147", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "Common", "Name": "ComplexEventTimes", "AbbrName": "EvntTms", "NotReqXML": "0", "Description": "The ComplexEventTime component is nested within the ComplexEventDate in order to further qualify any dates placed on the event and is used to specify time ranges for which a complex event is effective. It is always provided within the context of start and end dates. The time range is assumed to be in effect for the entirety of the date or date range specified.", "Added": "FIX.5.0SP1", "AddedEP": "92"}, {"ComponentID": "2148", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "StrmAsgnReqGrp", "AbbrName": "Reqs", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "93"}, {"ComponentID": "2149", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "StrmAsgnRptGrp", "AbbrName": "Rpts", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "93"}, {"ComponentID": "2150", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "StrmAsgnReqInstrmtGrp", "AbbrName": "Instrmts", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "93"}, {"ComponentID": "2151", "ComponentType": "ImplicitBlockRepeating", "CategoryID": "MarketData", "Name": "StrmAsgnRptInstrmtGrp", "AbbrName": "Instrmts", "NotReqXML": "0", "Added": "FIX.5.0SP1", "AddedEP": "93"}]