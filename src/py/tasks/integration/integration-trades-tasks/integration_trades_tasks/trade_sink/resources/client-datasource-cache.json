[{"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Andurand Equity Trade Capture", "s3Path": "feeds/trade/andurand-eqt-tc", "tenant": "andurand"}, {"crossTenant": true, "fileType": "XML", "models": ["Transaction"], "name": "CME Trade Capture", "s3Path": "feeds/trade/cme-tc"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Concord BGC COMEX", "s3Path": "feeds/trade/concord-bgc-comex", "tenant": "concord"}, {"crossTenant": false, "fileType": "CSV", "models": ["Order"], "name": "Finsa mt4 orders", "s3Path": "feeds/trade/finsa-mt4-orders", "tenant": "<PERSON>a"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "One Financial", "s3Path": "feeds/trade/one-financial-mt4-csv", "tenant": "onefinancial"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Hantec FIXLog", "s3Path": "feeds/trade/hantec-mt4-static", "tenant": "hantec"}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "Concord BSC", "s3Path": "feeds/trade/concord-bsc", "tenant": "concord"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Concord BGC", "s3Path": "feeds/trade/concord-bgc", "tenant": "concord"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "<PERSON><PERSON> fix", "s3Path": "feeds/trade/reyl-fix", "tenant": "reyl"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction"], "name": "Non Automated Trade Uploader", "s3Path": "feeds/trade/natu"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Andurand Equity Static Instruments ", "s3Path": "feeds/task/eqt-static", "tenant": "andurand"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Brooklands - TR", "s3Path": "feeds/trade/fierro-brooklands", "tenant": "fierro"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Hantec MT4", "s3Path": "feeds/trade/hantec-mt4log-static", "tenant": "hantec"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Hywin CS Fills", "s3Path": "feeds/trade/hywin-cs-fills", "tenant": "hywin"}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "Fixi ISAM", "s3Path": "feeds/trade/fixi-isam", "tenant": "fixi"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Fierro Brooklands", "s3Path": "feeds/trade/fierro-brooklands", "tenant": "fierro"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "FIXI - Integral", "s3Path": "feeds/trade/fixi-integral", "tenant": "fixi"}, {"crossTenant": true, "fileType": "XML", "models": ["Transaction", "Order"], "name": "MT4 Fix Execution Report", "s3Path": "feeds/trade/mt4-ex"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction"], "name": "Credit Suisse Fills", "s3Path": "feeds/trade/cs-fills"}, {"crossTenant": true, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "ICE Trade Capture", "s3Path": "feeds/trade/ice-tc"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction"], "name": "UBS Jersey FIM", "s3Path": "feeds/trade/ubsj-fim"}, {"crossTenant": true, "fileType": "FIX", "models": ["Transaction"], "name": "ICE Trade Capture", "s3Path": "trade/ice-tc"}, {"crossTenant": true, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Avatrade Equities", "s3Path": "feeds/trade/avatrade-eqt", "tenant": "avatrade"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Finsa mt4 trades", "s3Path": "feeds/trade/finsa-mt4-trades", "tenant": "<PERSON>a"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction"], "name": "CME Allocation", "s3Path": "feeds/trade/cme-alloc"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Promeritum Investment Management LLP", "s3Path": "feeds/trade/promeritum-tc", "tenant": "promeritum"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Leste ESMX", "s3Path": "feeds/trade/leste-esmx", "tenant": "leste"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Tradar EOD Trades", "s3Path": "feeds/trade/promeritum-tc", "tenant": "promeritum"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Equity ISINs", "s3Path": "feeds/task/eqt-static", "tenant": "andurand"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "One Financial", "s3Path": "feeds/trade/one-financial-mt4", "tenant": "onefinancial"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Avatrade", "s3Path": "feeds/trade/avatrade-csv", "tenant": "avatrade"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "MT4 Fix Execution Report", "s3Path": "feeds/trade/mt4-ex"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Concord BSC", "s3Path": "feeds/trade/concord-bsc", "tenant": "concord"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Finsa ct trades", "s3Path": "feeds/trade/finsa-ct-trades", "tenant": "<PERSON>a"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Leste EMSX", "s3Path": "feeds/trade/leste-emsx", "tenant": "leste"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Valbury Fidessa", "s3Path": "feeds/trade/valbury-fidessa", "tenant": "valbury", "featureFlags": ["SKIP_FILE:execution", "SKIP_FILE:release", "SKIP_FILE:audit"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Leste ESMX", "s3Path": "feeds/trade/leste-esmx", "tenant": "leste"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction"], "name": "Andurand Equity Trade Capture", "s3Path": "trade/andurand-eqt-tc", "tenant": "andurand"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Concord BGC COMEX", "s3Path": "feeds/trade/concord-bsc-comex", "tenant": "concord"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Concord Resources Limited", "s3Path": "feeds/trade/hsbc-evolve", "tenant": "concord"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Avatrade", "s3Path": "feeds/trade/avatrade-mt4-csv", "tenant": "avatrade"}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "Avatrade mt4", "s3Path": "feeds/batch/fix-trade/avatrade-mt4", "tenant": "avatrade", "featureFlags": ["SKIP_FILE:fix_server", "read_bytes"]}, {"crossTenant": true, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "MT4 Fix Execution Report", "s3Path": "feeds/trade/mt4-ex"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction"], "name": "ICE Allocation", "s3Path": "feeds/trade/ice-alloc"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Abide TR", "s3Path": "feeds/trade/abide-tr"}, {"crossTenant": false, "fileType": "CSV", "models": ["Order"], "name": "Finsa ct orders", "s3Path": "feeds/trade/finsa-ct-orders", "tenant": "<PERSON>a"}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "Fixi CFH", "s3Path": "feeds/trade/fixi-cfh", "tenant": "fixi"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "<PERSON><PERSON>", "s3Path": "feeds/trade/reyl-finnova", "tenant": "reyl", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Gold-i FIX", "s3Path": "feeds/trade/hantec-mt4-static", "tenant": "hantec"}, {"name": "Retail CSV", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/retail-csv", "featureFlags": ["instrument_cache"]}, {"name": "Jpmpb Tran", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/jpmpb-tran"}, {"name": "Valbury FIX", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "FIX", "s3Path": "feeds/batch/fix-trade/valbury-fix", "tenant": "valbury"}, {"name": "PrimeXM", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/prime-xm", "featureFlags": ["instrument_cache", "read_bytes"]}, {"name": "OneZero", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/one-zero"}, {"name": "Leste - Enfusion", "crossTenant": false, "tenant": "leste", "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/leste-enfusion"}, {"name": "Osmo - Enfusion", "tenant": "osmo", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/osmo-enfusion"}, {"name": "Cheyne - Equities", "tenant": "cheyne", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/cheyne-equities"}, {"name": "Cheyne - CDS", "tenant": "cheyne", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/cheyne-cds"}, {"name": "SunGlobal - Trade Blotter", "tenant": "sunglobal", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/sunglobal-tradeblotter", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Multrees TSOX", "s3Path": "feeds/trade/multrees-tsox", "tenant": "multrees", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "TXT", "models": ["Transaction", "Order"], "name": "GKFX PrimeXM", "s3Path": "feeds/trade/gkfx-prime-xm", "tenant": "gkfx", "featureFlags": ["instrument_cache"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Capricorn SteelEye Trade Blotter", "s3Path": "feeds/trade/capricorn-trade-blotter", "tenant": "cpi", "featureFlags": ["read_bytes"]}, {"name": "FCS - EMSX", "crossTenant": false, "tenant": "fcs", "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/fcs-emsx"}, {"name": "Vibhs - Torch", "crossTenant": false, "tenant": "vibhs", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/vibhs-torch"}, {"name": "<PERSON>ib<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "crossTenant": false, "tenant": "vibhs", "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/dabbl-trade-blotter"}, {"name": "Concord - Marex", "crossTenant": false, "tenant": "concord", "models": ["Transaction", "Order"], "fileType": "XML", "s3Path": "feeds/trade/concord-marex"}, {"name": "ENA - Bloomberg FXGO", "crossTenant": false, "tenant": "ena", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/ena-bloomberg-fxgo"}, {"crossTenant": true, "fileType": "CSV", "models": ["Order", "Transaction"], "name": "Steeleye trade blotter", "s3Path": "feeds/trade/steeleye-trade-blotter", "featureFlags": ["read_bytes"]}, {"name": "Jub Transaction Reporting", "crossTenant": false, "tenant": "jub", "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/jub-tr"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Mu<PERSON><PERSON>s fxall", "s3Path": "feeds/trade/multrees-fxall", "tenant": "multrees", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Andurand Voice", "s3Path": "feeds/trade/andurand-voice-trades", "tenant": "andurand"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Andurand CME/ICE", "s3Path": "feeds/trade/andurand-cme-ice", "tenant": "andurand"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Theorema EMSX", "s3Path": "feeds/trade/theorema-emsx", "tenant": "theorema", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "Avatrade Gold-I", "s3Path": "feeds/batch/fix-trade/avatrade-gold-i", "tenant": "avatrade", "featureFlags": ["batch_ingestion"]}, {"name": "Bloomberg FXGO / TSOX / EMSX", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/bloomberg"}, {"name": "Valbury TT", "crossTenant": false, "tenant": "valbury", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/valbury-tt"}, {"name": "Portsea Enfusion", "crossTenant": false, "tenant": "portsea", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/portsea-enfusion", "featureFlags": ["read_bytes"]}, {"name": "Thornbridge Enfusion", "crossTenant": false, "tenant": "thornbridge", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/thornbridge-enfusion", "featureFlags": ["read_bytes"]}, {"name": "IBP Marex CME", "crossTenant": false, "tenant": "ibp", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/trade/marex-cme"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "IBP EMSX", "s3Path": "feeds/trade/ibp-emsx", "tenant": "ibp", "featureFlags": []}, {"name": "TT", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/tt"}, {"name": "Engadine Ezesoft", "crossTenant": false, "models": ["Transaction", "Order"], "tenant": "engadine", "fileType": "TXT", "s3Path": "feeds/trade/engadine-eze"}, {"name": "Matrix Ezesoft", "crossTenant": false, "models": ["Order"], "tenant": "matrix", "fileType": "TXT", "s3Path": "feeds/trade/matrix-eze"}, {"name": "EZE OMS", "crossTenant": false, "models": ["Order"], "tenant": "caledonia", "fileType": "CSV", "s3Path": "feeds/trade/caledonia-eze"}, {"name": "EZE OMS", "crossTenant": false, "models": ["Order"], "tenant": "scoggin", "fileType": "CSV", "s3Path": "feeds/trade/scoggin-eze"}, {"name": "EZE OMS", "crossTenant": false, "models": ["Order"], "tenant": "foresightgroup", "fileType": "CSV", "s3Path": "feeds/trade/foresightgroup-eze"}, {"name": "UBIX", "crossTenant": false, "tenant": "kytebroking", "models": ["Transaction", "Order"], "fileType": "JSON", "s3Path": "feeds/trade/kytebroking-ubix"}, {"name": "<PERSON><PERSON><PERSON> Hedged", "crossTenant": false, "fileType": "CSV", "tenant": "kestrel", "s3Path": "feeds/trade/kestrel-hedged", "models": ["Transaction", "Order"]}, {"crossTenant": false, "models": ["Transaction", "Order"], "fileType": "CSV", "name": "Thornbridge - DMA", "s3Path": "feeds/trade/thornbridge-dma", "tenant": "thornbridge", "featureFlags": ["read_bytes"]}, {"name": "Bloomberg AIM", "crossTenant": false, "tenant": "intermede", "models": ["Transaction", "Order"], "fileType": "XML", "s3Path": "feeds/trade/intermede-bloomberg-aim"}, {"name": "monsas-kooltra", "crossTenant": false, "tenant": "monsas", "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/monsas-kooltra", "featureFlags": ["read_bytes"]}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "pcuk_lisa_edf", "s3Path": "feeds/trade/pcuk-lisa-edf", "tenant": "pcuk", "crossTenant": false}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "One Financial", "s3Path": "feeds/batch/fix-trade/one-financial-one-zero", "tenant": "onefinancial"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "One Financial One Zero Hedged", "s3Path": "feeds/trade/one-financial-one-zero-eod", "tenant": "onefinancial"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Cedar Knight Equities", "s3Path": "feeds/trade/ck-equities", "tenant": "cedarknight", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Cedar Knight Fixed Income", "s3Path": "feeds/trade/ck-fixed-income", "tenant": "cedarknight", "featureFlags": ["read_bytes"]}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "CIAM EMSI", "s3Path": "feeds/trade/ciam-emsi", "tenant": "ciam", "featureFlags": []}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Sigma EMSI", "s3Path": "feeds/trade/sigma-emsi", "tenant": "sigma", "featureFlags": []}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Kestrel EMSI", "s3Path": "feeds/trade/kestrel-emsi", "tenant": "kestrel", "featureFlags": []}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "covalis emsi", "s3Path": "feeds/trade/covalis-emsi", "tenant": "covalis", "featureFlags": []}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "ibp_cowen", "s3Path": "feeds/trade/ibp-cowen", "tenant": "ibp", "crossTenant": false}, {"models": ["Transaction", "Order"], "fileType": "TXT", "name": "kytebroking_fidessa", "s3Path": "feeds/trade/kytebroking-fidessa", "tenant": "kytebroking", "crossTenant": false}, {"models": ["Transaction", "Order"], "fileType": "TXT", "name": "prism_fidessa", "s3Path": "feeds/trade/prism-fidessa", "tenant": "prism", "crossTenant": false}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "turnerpope_export", "s3Path": "feeds/trade/turnerpope-export", "tenant": "turner<PERSON>e", "crossTenant": false, "featureFlags": ["read_bytes"]}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "Starmark Orders", "s3Path": "feeds/trade/starmark-orders", "tenant": "starmark", "crossTenant": false, "featureFlags": []}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "ATFX One Zero", "s3Path": "feeds/batch/fix-trade/atfx-one-zero", "tenant": "atfx", "crossTenant": false, "featureFlags": []}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "tavira_fidessa", "s3Path": "feeds/trade/tavira-fidessa", "tenant": "tavira", "crossTenant": false, "featureFlags": []}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "jellyfish_jpm", "s3Path": "feeds/trade/jellyfish-jpm", "tenant": "jellyfish", "crossTenant": false, "featureFlags": []}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "Oppenheimer Fidessa", "s3Path": "feeds/batch/fix-trade/oppenheimer-fidessa", "tenant": "<PERSON><PERSON><PERSON>", "crossTenant": false, "featureFlags": []}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "AFTX MT4", "s3Path": "feeds/trade/atfx-mt4-logs", "tenant": "atfx"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Axis Gpp Trade Activity", "s3Path": "feeds/trade/gpp-trade-activity", "tenant": "axis"}, {"crossTenant": true, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "LME Select FIX", "s3Path": "feeds/trade/lme-select-fix"}, {"crossTenant": false, "fileType": "FIX", "models": ["Transaction", "Order"], "name": "ICBC lme fix", "s3Path": "feeds/trade/lme-icbc-fix", "tenant": "icbc"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "tenant": "covalis", "name": "Covalis EMSX", "s3Path": "feeds/trade/covalis-emsx"}, {"fileType": "CSV", "name": "ATFX Celer Tech", "s3Path": "feeds/batch/fix-trade/atfx-celer-tech", "tenant": "atfx", "crossTenant": false, "featureFlags": [], "models": ["Transaction", "Order"]}, {"fileType": "CSV", "name": "EC Markets One Zero", "s3Path": "feeds/batch/fix-trade/ec-markets-one-zero", "tenant": "ecmarkets", "crossTenant": false, "featureFlags": [], "models": ["Transaction", "Order"]}, {"fileType": "CSV", "name": "PCUK One Zero", "s3Path": "feeds/batch/fix-trade/pcuk-one-zero", "tenant": "pcuk", "crossTenant": false, "featureFlags": [], "models": ["Transaction", "Order"]}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "axis_blotter", "s3Path": "feeds/trade/axis-blotter", "tenant": "axis", "crossTenant": false, "featureFlags": ["read_bytes"]}, {"models": ["Transaction", "Order"], "fileType": "CSV", "name": "Interactive Brokers", "s3Path": "feeds/trade/interactive-brokers-activity", "tenant": "thornbridge", "crossTenant": false, "featureFlags": []}, {"fileType": "CSV", "name": "NHF ESunny", "s3Path": "feeds/trade/nhf-esunny", "tenant": "nhfinancial", "crossTenant": false, "featureFlags": [], "models": ["Transaction", "Order"]}, {"tenant": "thornbridge", "crossTenant": false, "fileType": "XML", "models": ["Transaction", "Order"], "name": "Thornbridge CarbonCap EEX", "s3Path": "feeds/trade/carboncap-eex"}, {"tenant": "pdm", "crossTenant": false, "fileType": "CSV", "featureFlags": [], "models": ["Transaction"], "name": "PDM Trades", "s3Path": "feeds/trade/pdm-trades"}, {"tenant": "thornbridge", "crossTenant": false, "fileType": "CSV", "featureFlags": [], "models": ["Transaction", "Order"], "name": "Thornbridge ICE Trades", "s3Path": "feeds/batch/fix-trade/ice-fix-trades-tb-carbon-cap"}, {"tenant": "covalis", "crossTenant": false, "fileType": "CSV", "featureFlags": [], "models": ["Transaction", "Order"], "name": "Covalis EZE", "s3Path": "feeds/trade/covalis-eze"}, {"tenant": "nhfinancial", "crossTenant": false, "fileType": "CSV", "featureFlags": ["read_bytes"], "models": ["Transaction", "Order"], "name": "NHF LME EOD", "s3Path": "feeds/trade/lme-eod"}, {"name": "TT", "tenant": "pcuk", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/tt"}, {"name": "Thornbridge CubeCapital", "tenant": "thornbridge", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/cubecapital"}, {"name": "SCM DMA", "tenant": "scm", "crossTenant": false, "featureFlags": ["read_bytes"], "models": ["Transaction", "Order"], "fileType": "CSV", "s3Path": "feeds/trade/opus-dma"}, {"crossTenant": false, "tenant": "nhfinancial", "fileType": "CSV", "models": ["Transaction", "Order"], "name": "NH Financial LME Select FIX", "s3Path": "feeds/batch/fix-trade/nhf-lme-select-fix"}, {"crossTenant": false, "tenant": "kytebroking", "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Kyte Skew FIX", "s3Path": "feeds/batch/fix-trade/kyte-fix-skew"}, {"crossTenant": false, "tenant": "thornbridge", "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Thornbridge Universum", "s3Path": "feeds/trade/thornbridge-universum"}, {"crossTenant": false, "tenant": "thornbridge", "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Thornbridge Julius <PERSON>", "s3Path": "feeds/trade/julius-baer"}, {"crossTenant": false, "tenant": "nhfinancial", "fileType": "CSV", "models": ["Transaction", "Order"], "name": "NH Financial CME EOD", "s3Path": "feeds/trade/cme-eod"}, {"name": "Sova Bloomberg AIM", "crossTenant": false, "tenant": "sova", "models": ["Transaction", "Order"], "fileType": "XML", "s3Path": "feeds/trade/sova-bloomberg-aim"}, {"crossTenant": true, "fileType": "CSV", "featureFlags": [], "models": ["Transaction", "Order"], "name": "ICE Fix Trades", "s3Path": "feeds/batch/fix-trade/ice-fix-trades-universal"}, {"tenant": "pcuk", "crossTenant": false, "fileType": "CSV", "featureFlags": [], "models": ["Transaction", "Order"], "name": "ICE Fix Trades PCUK", "s3Path": "feeds/batch/fix-trade/ice-fix-trades-pcuk"}, {"name": "CQG Fix Trades PCUK", "crossTenant": false, "tenant": "pcuk", "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/cqg-fix-trades-pcuk"}, {"name": "CQG Fix Trades", "crossTenant": true, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/cqg-fix-trades-universal"}, {"name": "Borntec PCUK", "tenant": "pcuk", "crossTenant": false, "models": ["Transaction", "Order"], "fileType": "TXT", "s3Path": "feeds/batch/fix-trade/borntec-pcuk"}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Hansa EMSI", "s3Path": "feeds/trade/hansa-emsi", "tenant": "hansa", "featureFlags": []}, {"crossTenant": false, "fileType": "CSV", "models": ["Transaction", "Order"], "name": "Multrees EMSX", "s3Path": "feeds/trade/multrees-emsx", "tenant": "multrees", "featureFlags": []}]