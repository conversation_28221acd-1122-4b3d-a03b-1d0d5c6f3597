import csv
import pandas as pd
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import StandardAuditMessages
from aries_se_core_tasks.aries.utility_tasks.finalize_task import (
    create_path_and_upload_model_results,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.core_dataclasses import Serial<PERSON>R<PERSON>ult
from aries_se_core_tasks.frame.frame_concatenator import (  # type: ignore[attr-defined]
    Params as FrameConcatenatorParams,
)
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.get_primary_transformations import (  # type: ignore[attr-defined]
    run_get_primary_transformations,
)
from aries_se_core_tasks.io.read.batch_producer import (  # type: ignore[attr-defined]
    Params as BatchProducerParams,
)
from aries_se_core_tasks.io.read.batch_producer import (  # type: ignore[attr-defined]
    run_batch_producer,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_filter import (  # type: ignore[attr-defined]
    Params as CsvFileFilterParams,
)
from aries_se_core_tasks.io.read.csv_file_filter import (  # type: ignore[attr-defined]
    run_csv_file_filter,
)
from aries_se_core_tasks.io.read.csv_file_splitter import (  # type: ignore[attr-defined]
    Params as CsvSplitterParams,
)
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_core_tasks.utilities.serializer import serializer
from aries_se_trades_tasks.instrument.instrument_fallback import (  # type: ignore[attr-defined]
    Params as InstrumentFallbackParams,
)
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import (  # type: ignore[attr-defined]
    Params as LinkInstrumentParams,
)
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.orders_and_tr.quarantine import run_quarantined_records
from aries_se_trades_tasks.party.link_parties import (  # type: ignore[attr-defined]
    Params as LinkPartiesParams,
)
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import (
    run_eligibility_assessor,
)
from aries_se_trades_tasks.tr.feed.bbg.emsi.orders.merge_order_and_fill_csv import (
    SkipIfEmptyDataframeAfterJoin,
    run_merge_order_and_fill_csv,
)
from aries_se_trades_tasks.tr.static import RTS22TransactionWorkflowNames
from aries_se_trades_tasks.tr.tr_app_metrics_enum import TRAppMetricsEnum
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.static import (
    SOURCE_SCHEMA,
    FetchedMarketCounterpartyColumns,
    TempColumns,
)
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import upsert_audit
from integration_trades_tasks.tr_bbg_emsi_orders.abstract_tr_bgg_emsi_orders_flow import (
    AbstractTrBBGEMSIOrders,
)
from integration_trades_tasks.tr_bbg_emsi_orders.input_schema import TrBBGEMSIOrdersAriesTaskInput
from pathlib import Path
from pydantic import BaseSettings, Field
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import QuarantinedRTS22Transaction, RTS22Transaction, SinkRecordAudit
from se_elastic_schema.static.mifid2 import BuySellIndicator, TradingCapacity
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, InstrumentFields
from se_trades_tasks.tr.static import RTS22TransactionColumns
from typing import Dict, List, Type


class TrBBGEMSIOrdersSettings(BaseSettings):
    srp_through_master_data: bool = Field(default=False, env="SRP_THROUGH_MASTER_DATA")


class TrBBGEMSIOrders(AbstractTrBBGEMSIOrders):
    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str,
    ):
        # BEGIN SETUP #
        env_config = TrBBGEMSIOrdersSettings()

        # Parse and validate AriesTaskInput parameters
        tr_bbg_emsi_orders_input: TrBBGEMSIOrdersAriesTaskInput = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=TrBBGEMSIOrdersAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        streamed: bool = cached_tenant_workflow_config.workflow.streamed
        tenant: str = aries_task_input.workflow.tenant

        # Create local temporary directory to store intermediate files
        tmp_storage: str = tmp_directory().as_posix()

        # Determine Cloud Provider
        cloud_provider = get_cloud_provider_from_file_uri(
            file_uri=tr_bbg_emsi_orders_input.file_uri
        )
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
            task_input=tr_bbg_emsi_orders_input, cloud_provider_prefix=cloud_provider_prefix
        )

        es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )
        es_client_srp = self.instantiate_srp_es_client(
            srp_through_master_data=env_config.srp_through_master_data
        )
        # END SETUP #

        # BEGIN PRE-PROCESSING #

        # download file to local path
        local_file_path: str = run_download_file(file_url=tr_bbg_emsi_orders_input.file_uri)

        # Split the multi header file into 2 different dataframes
        # ROUTE rows are discarded
        filtered_csv_files: Dict[str, Path] = run_csv_file_filter(
            local_file_path=local_file_path,
            params=CsvFileFilterParams(
                split_pattern={
                    "order": '^.*?,"ORDER",',
                    "fill": '^.*?,"FILL",',
                },
                use_custom_quoting=True,
                quoting=csv.QUOTE_ALL,
                quote_char='"',
            ),
        )

        try:
            merged_dataframe_path: str = run_merge_order_and_fill_csv(
                files=filtered_csv_files,
                file_uri=tr_bbg_emsi_orders_input.file_uri,
                output_dir=tmp_storage,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )
        except SkipIfEmptyDataframeAfterJoin:
            upsert_audit(
                audit_path=audit_path,
                streamed=streamed,
                workflow_status=[
                    "Processing skipped as no valid transactions exist in the file",
                ],
                models=[RTS22Transaction],
            )
            sink_record_audit_df = quarantined_rts22_transaction_df = rts22_transaction_df = (
                pd.DataFrame()
            )

        else:
            # Read the input CSV file, normalise its columns,
            # and convert null-like "string",s to real null values.
            # This Task was used in Swarm-Flows to produce multiple CSV files
            # but that behavior is not needed here as we are already working
            # with a chunk of the original input file, thus we are always
            # getting the first and only element of the resulting list of FileSplitterResults.
            csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
                streamed=streamed,
                params=CsvSplitterParams(
                    chunksize=cached_tenant_workflow_config.max_batch_size,
                    detect_encoding=True,
                    normalise_columns=True,
                    audit_input_rows=True,
                    drop_empty_rows=False,
                ),
                csv_path=merged_dataframe_path,
                realm=tenant_bucket_with_cloud_prefix,
                sources_dir=tmp_storage,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )[0]

            # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
            # The Task also enforces datatypes, creates missing columns and
            # audits missing/unnecessary/empty columns
            input_df: SerializerResult = run_batch_producer(
                streamed=streamed,
                params=BatchProducerParams(
                    source_schema=SOURCE_SCHEMA,
                    remove_unknown_columns=True,
                    audit_null_columns=False,
                ),
                file_splitter_result=csv_splitter_result,
                return_dataframe=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            # Used for Chelverton override. If file has GLTH in name, skip non GLTH rows
            input_df = self.skip_non_glth(
                input_df=input_df,
                file_uri=tr_bbg_emsi_orders_input.source_file_uri,
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            # END PRE-PROCESSING #

            # BEGIN BUSINESS LOGIC #

            if input_df.empty:
                rts22_transaction_df = pd.DataFrame()
                quarantined_rts22_transaction_df = pd.DataFrame()
                sink_record_audit_df = pd.DataFrame()

                upsert_audit(
                    audit_path=audit_path,
                    streamed=streamed,
                    workflow_status=[
                        f"Processing will be skipped for the "
                        f"'{Path(tr_bbg_emsi_orders_input.file_uri).name}' file "
                        f"as there are no rows left after pre processing.",
                    ],
                    models=[RTS22Transaction],
                )
            else:
                # Populate the majority of the target fields in a centralized transformations class
                # Note that the original source file URI is used for the source key
                rts22_transaction_mappings_df = run_get_primary_transformations(
                    source_frame=input_df,
                    flow=RTS22TransactionWorkflowNames.TR_BBG_EMSI_ORDERS,
                    tenant=tenant,
                    realm=tenant_bucket_with_cloud_prefix,
                    source_file_uri=tr_bbg_emsi_orders_input.source_file_uri,
                    es_client=es_client_tenant,
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                # Link identifiers built in PartyIdentifiers from
                # input party data with tenant MyMarket data
                link_parties_df = run_link_parties(
                    tenant=aries_task_input.workflow.tenant,
                    source_frame=rts22_transaction_mappings_df,
                    params=LinkPartiesParams(
                        identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                        add_investment_firm_covered_directive=True,
                    ),
                    es_client=es_client_tenant,
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                mappings_with_parties_df = run_frame_concatenator(
                    transformed_df=rts22_transaction_mappings_df,
                    instrument_fallback_df=link_parties_df,
                    params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                mappings_with_parties_df = self._overwrite_fields_based_on_parties(
                    df_with_parties=mappings_with_parties_df
                )

                # Link identifiers built in InstrumentIdentifier from input instrument
                # data with SRP and tenant instrument data
                link_instrument_df = run_link_instrument(
                    source_frame=rts22_transaction_mappings_df,
                    params=LinkInstrumentParams(
                        identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                        currency_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        venue_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    ),
                    tenant=tenant,
                    es_client_srp=es_client_srp,
                    es_client_tenant=es_client_tenant,
                )

                instrument_fallback_input = run_frame_concatenator(
                    transformed_df=rts22_transaction_mappings_df,
                    link_instrument_df=link_instrument_df,
                    params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                # Create Instrument records embedded in the Orders for records
                # where LinkInstrument did not produce any hits
                instrument_fallback_df = run_instrument_fallback(
                    source_frame=instrument_fallback_input,
                    params=InstrumentFallbackParams(
                        market_instrument_identifiers_attribute=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                        instrument_fields_map=[
                            # ISIN and Currency are populated directly from
                            # the respective instrument identifier columns in
                            # INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP
                            FallbackInstrumentAttributes(
                                source_field=TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                                target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                            ),
                        ],
                        str_to_bool_dict={
                            "true": True,
                            "false": False,
                        },
                    ),
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                eligibility_assessor_input = run_frame_concatenator(
                    transformed_df=mappings_with_parties_df,
                    instrument_fallback_df=instrument_fallback_df,
                    params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                eligibility_assessor_df = run_eligibility_assessor(
                    source_frame=eligibility_assessor_input,
                    tenant=tenant,
                    es_client=es_client_tenant,
                    srp_client=es_client_srp,
                )

                final_frame = run_frame_concatenator(
                    transformed_df=mappings_with_parties_df,
                    instrument_fallback_df=instrument_fallback_df,
                    eligibility_assessor_df=eligibility_assessor_df,
                    params=FrameConcatenatorParams(
                        orient=OrientEnum.horizontal,
                        drop_columns=[
                            RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                            RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                            TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                            "currency_attribute",
                            "isin_attribute",
                            TempColumns.FIRST_BUYER,
                            TempColumns.FIRST_SELLER,
                        ],
                    ),
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

                rts22_transaction_df, quarantined_rts22_transaction_df, sink_record_audit_df = (
                    run_quarantined_records(
                        source_frame=final_frame,
                        tenant=tenant,
                        model=RTS22Transaction,
                        cloud_provider=cloud_provider,
                        aries_task_input=aries_task_input,
                        es_client=es_client_tenant,
                        audit_path=audit_path,
                        app_metrics_path=app_metrics_path,
                    )
                )

                if not rts22_transaction_df.empty:
                    self._audit_fallbacks(
                        df=rts22_transaction_df,
                        col_name=INSTRUMENT_PATH,
                        fallback_key="isCreatedThroughFallback",
                        status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                        meta_model=RTS22Transaction,
                        models=[RTS22Transaction],
                        streamed=streamed,
                        cloud_provider=cloud_provider,
                        app_metrics_path=app_metrics_path,
                        audit_path=audit_path,
                    )

        rts22_transaction_output = create_path_and_upload_model_results(
            final_transformed_df=rts22_transaction_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=RTS22Transaction,
            es_action=EsActionEnum.CREATE,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        quarantined_output = create_path_and_upload_model_results(
            final_transformed_df=quarantined_rts22_transaction_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=QuarantinedRTS22Transaction,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        sink_record_audit_output = create_path_and_upload_model_results(
            final_transformed_df=sink_record_audit_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=SinkRecordAudit,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        shutil.rmtree(tmp_storage)
        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.RTS22_TRANSACTION: rts22_transaction_output,
                MetaModel.QUARANTINED_RTS22_TRANSACTION: quarantined_output,
                MetaModel.SINK_RECORD_AUDIT: sink_record_audit_output,
            },
        )

    @staticmethod
    def instantiate_srp_es_client(srp_through_master_data: bool):
        if srp_through_master_data:
            raise Exception("Master Data connection is currently not supported for this feed.")
        es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_srp_es_config()
        )
        return es_client_srp

    @serializer
    def _audit_fallbacks(
        self,
        df: pd.DataFrame,
        col_name: str,
        fallback_key: str,
        status_message: str,
        meta_model: Type[SteelEyeSchemaBaseModelES8],
        models: List[Type[SteelEyeSchemaBaseModelES8]],
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ):
        instruments_created_through_fallback = (
            df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
        )
        if any(instruments_created_through_fallback):
            run_auditor_and_metrics_producer(
                source_frame=df.loc[instruments_created_through_fallback, :],
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=status_message,
                            meta_model=meta_model,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=TRAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                        )
                    ],
                    models=models,
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

    def skip_non_glth(
        self,
        input_df: SerializerResult,
        file_uri: str,
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ) -> SerializerResult:
        """For some clients, i.e. Chelverton we want to add additional skip
        logic This is implemented in an override."""
        return input_df

    @serializer
    def _overwrite_fields_based_on_parties(self, df_with_parties: pd.DataFrame):
        """Based on certain conditions in the fetched party records, this
        function overwrites the values of certain schema fields. System
        Internalisers and Multilateral Trading Facilities cannot be detected
        from the input file, and are therefore detected AFTER linking parties.

        Based on this, the trading capacity and venue fields are
        updated.
        """
        # If BUYI and Seller is a MarketCounterparty OR
        # If SELL and Buyer is a MarketCounterparty
        # IF marketCounterparty.details.orgType = "Systematic Internaliser"
        # or "Multilateral Trading Facility"
        # transactionDetails.venue and transactionDetails.ultimateVenue =
        # marketCounterparty.firmIdentifiers.mic

        # Temp columns: get seller &key, buyer &key, details.orgType, firmIdentifiers.mic
        for col in {RTS22TransactionColumns.PARTIES_BUYER, RTS22TransactionColumns.PARTIES_SELLER}:
            if col not in df_with_parties.columns:
                df_with_parties[col] = pd.NA
        df_with_parties[TempColumns.FIRST_BUYER] = df_with_parties.loc[
            :, RTS22TransactionColumns.PARTIES_BUYER
        ].str.get(0)
        df_with_parties[TempColumns.FIRST_SELLER] = df_with_parties.loc[
            :, RTS22TransactionColumns.PARTIES_SELLER
        ].str.get(0)
        buy_seller_counterparty_mask = (
            df_with_parties.loc[
                :, RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
            ].str.fullmatch(BuySellIndicator.BUYI.value)
            & (
                df_with_parties.loc[:, TempColumns.FIRST_SELLER]
                .str.get("&key")  # type: ignore[arg-type]
                .astype(str)
                .str.startswith("MarketCounterparty")
            )
        ).fillna(False)
        sell_buyer_counterparty_mask = (
            df_with_parties.loc[
                :, RTS22TransactionColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
            ].str.fullmatch(BuySellIndicator.SELL.value)
            & (
                df_with_parties.loc[:, TempColumns.FIRST_BUYER]
                .str.get("&key")  # type: ignore[arg-type]
                .astype(str)
                .str.startswith("MarketCounterparty")
            )
        ).fillna(False)
        buyer_si_mtf_mask = sell_buyer_counterparty_mask & (
            df_with_parties.loc[:, TempColumns.FIRST_BUYER]
            .str.get(FetchedMarketCounterpartyColumns.DETAILS_ORG_TYPE)  # type: ignore[arg-type]
            .astype(str)
            .str.fullmatch(
                "Systematic Internaliser|Multilateral Trading Facility", case=False, na=False
            )
        ).fillna(False)
        seller_si_mtf_mask = buy_seller_counterparty_mask & (
            df_with_parties.loc[:, TempColumns.FIRST_SELLER]
            .str.get(FetchedMarketCounterpartyColumns.DETAILS_ORG_TYPE)  # type: ignore[arg-type]
            .astype(str)
            .str.fullmatch(
                "Systematic Internaliser|Multilateral Trading Facility", case=False, na=False
            )
        ).fillna(False)
        buyer_mtf_mask = sell_buyer_counterparty_mask & (
            df_with_parties.loc[:, TempColumns.FIRST_BUYER]
            .str.get(FetchedMarketCounterpartyColumns.DETAILS_ORG_TYPE)  # type: ignore[arg-type]
            .astype(str)
            .str.fullmatch("Multilateral Trading Facility", case=False, na=False)
        ).fillna(False)
        seller_mtf_mask = buy_seller_counterparty_mask & (
            df_with_parties.loc[:, TempColumns.FIRST_SELLER]
            .str.get(FetchedMarketCounterpartyColumns.DETAILS_ORG_TYPE)  # type: ignore[arg-type]
            .astype(str)
            .str.fullmatch("Multilateral Trading Facility", case=False, na=False)
        ).fillna(False)

        df_with_parties.loc[
            (seller_mtf_mask | buyer_mtf_mask),
            RTS22TransactionColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
        ] = TradingCapacity.MTCH.value
        # Set orderTransmissionIndicator=False for MTCH
        df_with_parties.loc[
            (seller_mtf_mask | buyer_mtf_mask),
            RTS22TransactionColumns.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR,
        ] = False
        df_with_parties.loc[
            seller_si_mtf_mask, RTS22TransactionColumns.TRANSACTION_DETAILS_VENUE
        ] = df_with_parties.loc[seller_si_mtf_mask, TempColumns.FIRST_SELLER].str.get(
            FetchedMarketCounterpartyColumns.FIRM_IDENTIFIERS_MIC
        )
        df_with_parties.loc[
            buyer_si_mtf_mask, RTS22TransactionColumns.TRANSACTION_DETAILS_VENUE
        ] = df_with_parties.loc[buyer_si_mtf_mask, TempColumns.FIRST_BUYER].str.get(
            FetchedMarketCounterpartyColumns.FIRM_IDENTIFIERS_MIC
        )
        df_with_parties[RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE] = (
            df_with_parties[RTS22TransactionColumns.TRANSACTION_DETAILS_VENUE]
        )
        return df_with_parties


def tr_bbg_emsi_orders_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )
