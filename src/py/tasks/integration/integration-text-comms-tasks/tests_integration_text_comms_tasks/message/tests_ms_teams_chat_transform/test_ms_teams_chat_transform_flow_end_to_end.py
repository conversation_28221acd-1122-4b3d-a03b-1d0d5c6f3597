# type: ignore
import boto3
import fsspec
import pandas as pd
import pytest
import tempfile
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_text_comms_tasks.message.ms_teams_chat_transform.ms_teams_chat_transform_task import (  # noqa E501
    ms_teams_chat_transform_run,
)
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Message
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from tests_integration_text_comms_tasks.message.tests_ms_teams_chat_transform.conftest import s3_key
from unittest.mock import patch

BUCKET_NAME: str = "test.dev.steeleye.co"
DATA_PATH = Path(__file__).parent.joinpath("data")
JSON_FILE_TO_DATAFRAME_CONVERTER_RESULT_PATH = DATA_PATH.joinpath(
    "json_file_to_dataframe_converter_result_ouput.pkl"
)
INPUT_FILE_PATH = DATA_PATH.joinpath("ms_teams_input.json")
EXPECTED_OUTPUT_NDJSON_PATH = DATA_PATH.joinpath("expected_output.ndjson")


@pytest.fixture
def setup_write_file_for_records():
    temp_dir = tempfile.TemporaryDirectory()
    temp_file = tempfile.NamedTemporaryFile(suffix=".ndjson", dir=temp_dir.name)
    yield temp_file
    temp_file.close()


@pytest.fixture
def setup_write_file_for_audit():
    temp_dir = tempfile.TemporaryDirectory()
    temp_file = tempfile.NamedTemporaryFile(suffix=".json", dir=temp_dir.name)
    yield temp_file
    temp_file.close()


@pytest.fixture
def cloud_agnostic_obj_buffer(
    monkeypatch, setup_write_file_for_records, setup_write_file_for_audit
):
    """Emulate cloud object store by referring to local files."""

    class MockCloudFileSystem(fsspec.AbstractFileSystem):
        @staticmethod
        def open(path, mode, *args, **kwargs):
            # This is required for writing the Record Ndjson file
            if mode == "w" and Path(path).suffix == ".ndjson":
                return open(setup_write_file_for_records.name, "w")
            # This is required for writing the Audit Json file
            elif mode == "wb" and Path(path).suffix == ".json":
                return open(setup_write_file_for_audit.name, "wb")
            else:
                # This is required to read the input file from cloud storage
                return open(INPUT_FILE_PATH, "r")

        @staticmethod
        def info(path: str) -> dict:
            # Simulate metadata for the file
            return {
                "name": "foo",
                "size": 12345,
                "type": "file",
                "mtime": "2023-09-14 18:42:00.000000+00:00",
                "LastModified": "2023-09-14 18:42:00.000000+00:00",
                "last_modified": "2023-09-14 18:42:00.000000+00:00",
                "ETag": None,
                "etag": None,
                "ContentType": None,
                "VersionId": None,
                "StorageClass": None,
            }

    # Overwrites the default implementation for base Cloud System to our mock class.
    fsspec.register_implementation("s3", MockCloudFileSystem, clobber=True)
    fsspec.register_implementation("az", MockCloudFileSystem, clobber=True)
    # fsspec.register_implementation("gs", MockCloudFileSystem, clobber=True)

    return cloud_agnostic_obj_buffer


class TestMsTeamsChatTransformFlow:
    @pytest.mark.parametrize(
        "cloud_provider_prefix",
        ["s3://", "az://"],  # , "gs://"
    )
    @mock_aws
    @freeze_time(time_to_freeze="2023-09-14 18:42:00.000000+00:00")
    def test_flow_end_to_end(
        self,
        cloud_agnostic_obj_buffer,
        cloud_provider_prefix,
        sample_aries_task_input: AriesTaskInput,
        setup_write_file_for_records,
    ) -> None:
        sample_aries_task_input.input_param.params["file_uri"] = (
            sample_aries_task_input.input_param.params["file_uri"].replace(
                "s3://", cloud_provider_prefix
            )
        )
        aries_task_result = self._run_flow(sample_aries_task_input)

        assert aries_task_result.output_param.params["file_uri"].startswith(cloud_provider_prefix)

        final_result: pd.DataFrame = pd.read_json(setup_write_file_for_records.name, lines=True)

        final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        final_result_expected["sourceKey"] = final_result_expected["sourceKey"].str.replace(
            "s3://", cloud_provider_prefix
        )

        assert_record_is_schema_compliant(
            input_df=final_result,
            model=Message,
            accepted_validation_error_codes=[
                SteeleyeRecordErrorCodesEnum.SE_DV_507,
                SteeleyeRecordErrorCodesEnum.SE_DV_508,
                SteeleyeRecordErrorCodesEnum.SE_DV_515,
                SteeleyeRecordErrorCodesEnum.SE_DV_521,
                SteeleyeRecordErrorCodesEnum.SE_DV_522,
                SteeleyeRecordErrorCodesEnum.SE_DV_531,
                SteeleyeRecordErrorCodesEnum.SE_DV_533,
            ],
        )

        # Assert Dataframe result
        pd.testing.assert_frame_equal(final_result, final_result_expected)

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{BUCKET_NAME}",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def _run_flow(
        sample_aries_task_input: AriesTaskInput,
        _,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
    ):
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = pd.DataFrame()
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        return ms_teams_chat_transform_run(aries_task_input=sample_aries_task_input)


@freeze_time(time_to_freeze="2023-09-14 14:20:00.000000+00:00")
def create_and_add_objects_to_s3_bucket():
    """Uploads test files to mock s3."""

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=BUCKET_NAME)

    with open(INPUT_FILE_PATH, "rb") as f:
        s3.put_object(Bucket=BUCKET_NAME, Key=s3_key, Body=f.read())
