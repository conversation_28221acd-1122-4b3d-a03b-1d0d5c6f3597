{"body":{"displayText":"You missed calls from me at 11:05 and 12:04 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed calls from me at 11:05 and 12:04 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442071342587","+447706338302","442071342587","447706338302"],"fromId":"+442071342587","fromIdAddlInfo":{"countryCode":"GB","raw":"442071342587"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3720","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442071342587 and 447706338302"},"roomId":"442071342587 and 447706338302","roomName":"442071342587 and 447706338302","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-19T11:04:53.000000Z","localTimestampStart":"2023-07-19T11:04:53.000000Z","timestampEnd":"2023-07-19T11:04:53.000000Z","timestampStart":"2023-07-19T11:04:53.000000Z"}}
{"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447714755207","447714755207","901"],"fromId":"901","fromIdAddlInfo":{"raw":"901"},"toIds":["+447714755207"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447714755207"}]},"metadata":{"messageId":"3953","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447714755207 and 901"},"roomId":"447714755207 and 901","roomName":"447714755207 and 901","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-26T15:21:32.000000Z","localTimestampStart":"2023-07-26T15:21:32.000000Z","timestampEnd":"2023-07-26T15:21:32.000000Z","timestampStart":"2023-07-26T15:21:32.000000Z"}}
{"body":{"displayText":"Hi Mark Darrel-brown, your booking for 14/07/2023 06:55 from HEATHROW, TERMINAL FIVE, , LONDON HEATHROW AIRPORT, HOUNSLOW, TW62GA is confirmed. Thank you for","text":"Hi Mark Darrel-brown, your booking for 14/07/2023 06:55 from HEATHROW, TERMINAL FIVE, , LONDON HEATHROW AIRPORT, HOUNSLOW, TW62GA is confirmed. Thank you for"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447851254113","+447921853490","447851254113","447921853490"],"fromId":"+447921853490","fromIdAddlInfo":{"countryCode":"GB","raw":"447921853490"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3443","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447851254113 and 447921853490"},"roomId":"447851254113 and 447921853490","roomName":"447851254113 and 447921853490","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-04T14:13:45.000000Z","localTimestampStart":"2023-07-04T14:13:45.000000Z","timestampEnd":"2023-07-04T14:13:45.000000Z","timestampStart":"2023-07-04T14:13:45.000000Z"}}
{"body":{"displayText":"Du kan n\u00e5r du vil","text":"Du kan n\u00e5r du vil"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3835","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-23T10:44:47.000000Z","localTimestampStart":"2023-07-23T10:44:47.000000Z","timestampEnd":"2023-07-23T10:44:47.000000Z","timestampStart":"2023-07-23T10:44:47.000000Z"}}
{"body":{"displayText":"KX68PLZ parked in zone 2033. Session will expire at 04:00 on Thu 13/07/23. Sent to you now at a sociable time. Try our apps or book at myringgo.co.uk","text":"KX68PLZ parked in zone 2033. Session will expire at 04:00 on Thu 13/07/23. Sent to you now at a sociable time. Try our apps or book at myringgo.co.uk"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447786203121","************","447786203121"],"fromId":"+447786203121","fromIdAddlInfo":{"countryCode":"GB","raw":"447786203121"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3603","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447786203121"},"roomId":"************ and 447786203121","roomName":"************ and 447786203121","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-12T20:40:19.000000Z","localTimestampStart":"2023-07-12T20:40:19.000000Z","timestampEnd":"2023-07-12T20:40:19.000000Z","timestampStart":"2023-07-12T20:40:19.000000Z"}}
{"body":{"displayText":"Thanks Kelly can I move to 10am the same day pleaae","text":"Thanks Kelly can I move to 10am the same day pleaae"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447723475721","+447921460361","447723475721","447921460361"],"fromId":"+447921460361","fromIdAddlInfo":{"countryCode":"GB","raw":"447921460361"},"toIds":["+447723475721"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447723475721"}]},"metadata":{"messageId":"3698","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447723475721 and 447921460361"},"participants":[{"types":["FROM"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447723475721 and 447921460361","roomName":"447723475721 and 447921460361","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-17T15:38:31.000000Z","localTimestampStart":"2023-07-17T15:38:31.000000Z","timestampEnd":"2023-07-17T15:38:31.000000Z","timestampStart":"2023-07-17T15:38:31.000000Z"}}
{"body":{"displayText":"The chauffeur for your ride 886703419 at 23:10 is Hesham Abou Shady, who can be reached at +4369911341459. Download our app: blacklane.com/app?t=g17c7n","text":"The chauffeur for your ride 886703419 at 23:10 is Hesham Abou Shady, who can be reached at +4369911341459. Download our app: blacklane.com/app?t=g17c7n"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447706338302","447706338302","blacklane"],"fromId":"blacklane","fromIdAddlInfo":{"raw":"blacklane"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3602","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338302 and Blacklane"},"roomId":"447706338302 and Blacklane","roomName":"447706338302 and Blacklane","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-12T20:07:13.000000Z","localTimestampStart":"2023-07-12T20:07:13.000000Z","timestampEnd":"2023-07-12T20:07:13.000000Z","timestampStart":"2023-07-12T20:07:13.000000Z"}}
{"body":{"displayText":"Som sagt!","text":"Som sagt!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3833","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-23T10:44:22.000000Z","localTimestampStart":"2023-07-23T10:44:22.000000Z","timestampEnd":"2023-07-23T10:44:22.000000Z","timestampStart":"2023-07-23T10:44:22.000000Z"}}
{"body":{"displayText":"You missed a call from me at 16:06 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 16:06 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB","US"],"allIds":["+12129061828","+************","12129061828","************"],"fromId":"+12129061828","fromIdAddlInfo":{"countryCode":"US","raw":"12129061828"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3730","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"12129061828 and ************"},"roomId":"12129061828 and ************","roomName":"12129061828 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-19T15:06:28.000000Z","localTimestampStart":"2023-07-19T15:06:28.000000Z","timestampEnd":"2023-07-19T15:06:28.000000Z","timestampStart":"2023-07-19T15:06:28.000000Z"}}
{"body":{"displayText":"How was your ride? Rate us online https://r.blacklane.com/UC-Hqqim3w4 or in the app blacklane://rides/past","text":"How was your ride? Rate us online https://r.blacklane.com/UC-Hqqim3w4 or in the app blacklane://rides/past"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447706338302","447706338302","blacklane"],"fromId":"blacklane","fromIdAddlInfo":{"raw":"blacklane"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3571","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338302 and Blacklane"},"roomId":"447706338302 and Blacklane","roomName":"447706338302 and Blacklane","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-11T19:55:35.000000Z","localTimestampStart":"2023-07-11T19:55:35.000000Z","timestampEnd":"2023-07-11T19:55:35.000000Z","timestampStart":"2023-07-11T19:55:35.000000Z"}}
{"body":{"displayText":"Your DocuSign identity confirmation code is: HHEHNW","text":"Your DocuSign identity confirmation code is: HHEHNW"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","docusign"],"fromId":"docusign","fromIdAddlInfo":{"raw":"docusign"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3935","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Docusign"},"roomId":"************ and Docusign","roomName":"************ and Docusign","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T16:30:25.000000Z","localTimestampStart":"2023-07-25T16:30:25.000000Z","timestampEnd":"2023-07-25T16:30:25.000000Z","timestampStart":"2023-07-25T16:30:25.000000Z"}}
{"body":{"displayText":"Your DocuSign identity confirmation code is: 9HBK69","text":"Your DocuSign identity confirmation code is: 9HBK69"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","docusign"],"fromId":"docusign","fromIdAddlInfo":{"raw":"docusign"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3936","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Docusign"},"roomId":"************ and Docusign","roomName":"************ and Docusign","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T17:14:36.000000Z","localTimestampStart":"2023-07-25T17:14:36.000000Z","timestampEnd":"2023-07-25T17:14:36.000000Z","timestampStart":"2023-07-25T17:14:36.000000Z"}}
{"body":{"displayText":"This conversation has ended. Please contact the restaurant for further assistance.","text":"This conversation has ended. Please contact the restaurant for further assistance."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447588679800","+447706353283","447588679800","447706353283"],"fromId":"+447588679800","fromIdAddlInfo":{"countryCode":"GB","raw":"447588679800"},"toIds":["+447706353283"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706353283"}]},"metadata":{"messageId":"4035","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447588679800 and 447706353283"},"roomId":"447588679800 and 447706353283","roomName":"447588679800 and 447706353283","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T15:36:57.000000Z","localTimestampStart":"2023-07-28T15:36:57.000000Z","timestampEnd":"2023-07-28T15:36:57.000000Z","timestampStart":"2023-07-28T15:36:57.000000Z"}}
{"body":{"displayText":"Jeg g\u00e5r i MS og handler","text":"Jeg g\u00e5r i MS og handler"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3957","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-26T16:20:12.000000Z","localTimestampStart":"2023-07-26T16:20:12.000000Z","timestampEnd":"2023-07-26T16:20:12.000000Z","timestampStart":"2023-07-26T16:20:12.000000Z"}}
{"body":{"displayText":"Jeg er i lobbyen","text":"Jeg er i lobbyen"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+447867728196"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447867728196"}]},"metadata":{"messageId":"3899","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:48:30.000000Z","localTimestampStart":"2023-07-24T17:48:30.000000Z","timestampEnd":"2023-07-24T17:48:30.000000Z","timestampStart":"2023-07-24T17:48:30.000000Z"}}
{"body":{"displayText":"Hej","text":"Hej"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3800","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T08:23:17.000000Z","localTimestampStart":"2023-07-22T08:23:17.000000Z","timestampEnd":"2023-07-22T08:23:17.000000Z","timestampStart":"2023-07-22T08:23:17.000000Z"}}
{"body":{"displayText":"You missed a call from me at 12:02 10 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 12:02 10 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442045388904","+447706338302","442045388904","447706338302"],"fromId":"+442045388904","fromIdAddlInfo":{"countryCode":"GB","raw":"442045388904"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3507","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442045388904 and 447706338302"},"roomId":"442045388904 and 447706338302","roomName":"442045388904 and 447706338302","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-10T11:02:54.000000Z","localTimestampStart":"2023-07-10T11:02:54.000000Z","timestampEnd":"2023-07-10T11:02:54.000000Z","timestampStart":"2023-07-10T11:02:54.000000Z"}}
{"body":{"displayText":"Har lige snuppet dig 2 t-shirts s\u00e5 du ikke l\u00f8ber t\u00f8r","text":"Har lige snuppet dig 2 t-shirts s\u00e5 du ikke l\u00f8ber t\u00f8r"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3866","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:33:33.000000Z","localTimestampStart":"2023-07-24T09:33:33.000000Z","timestampEnd":"2023-07-24T09:33:33.000000Z","timestampStart":"2023-07-24T09:33:33.000000Z"}}
{"body":{"displayText":"Ok","text":"Ok"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+447867728196"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447867728196"}]},"metadata":{"messageId":"3898","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:48:24.000000Z","localTimestampStart":"2023-07-24T17:48:24.000000Z","timestampEnd":"2023-07-24T17:48:24.000000Z","timestampStart":"2023-07-24T17:48:24.000000Z"}}
{"body":{"displayText":"Thanks for travelling with Addison Lee. How would you rate your experience out of 5? 5 - very satisfied to 1 - very unsatisfied for job 621725?","text":"Thanks for travelling with Addison Lee. How would you rate your experience out of 5? 5 - very satisfied to 1 - very unsatisfied for job 621725?"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447800006796","+447821646846","447800006796","447821646846"],"fromId":"+447800006796","fromIdAddlInfo":{"countryCode":"GB","raw":"447800006796"},"toIds":["+447821646846"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447821646846"}]},"metadata":{"messageId":"3436","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447800006796 and 447821646846"},"roomId":"447800006796 and 447821646846","roomName":"447800006796 and 447821646846","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-04T09:55:25.000000Z","localTimestampStart":"2023-07-04T09:55:25.000000Z","timestampEnd":"2023-07-04T09:55:25.000000Z","timestampStart":"2023-07-04T09:55:25.000000Z"}}
{"body":{"displayText":"And then the spicy rice","text":"And then the spicy rice"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3874","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:34:09.000000Z","localTimestampStart":"2023-07-24T09:34:09.000000Z","timestampEnd":"2023-07-24T09:34:09.000000Z","timestampStart":"2023-07-24T09:34:09.000000Z"}}
{"body":{"displayText":"\ud83c\udde9\ud83c\uddf0","text":"\ud83c\udde9\ud83c\uddf0"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3801","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T08:24:46.000000Z","localTimestampStart":"2023-07-22T08:24:46.000000Z","timestampEnd":"2023-07-22T08:24:46.000000Z","timestampStart":"2023-07-22T08:24:46.000000Z"}}
{"body":{"displayText":"You missed a call from me at 11:38 25 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 11:38 25 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442075087805","+************","442075087805","************"],"fromId":"+442075087805","fromIdAddlInfo":{"countryCode":"GB","raw":"442075087805"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3921","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442075087805 and ************"},"roomId":"442075087805 and ************","roomName":"442075087805 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T10:38:37.000000Z","localTimestampStart":"2023-07-25T10:38:37.000000Z","timestampEnd":"2023-07-25T10:38:37.000000Z","timestampStart":"2023-07-25T10:38:37.000000Z"}}
{"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447921460361","447921460361","901"],"fromId":"901","fromIdAddlInfo":{"raw":"901"},"toIds":["+447921460361"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447921460361"}]},"metadata":{"messageId":"3760","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447921460361 and 901"},"participants":[{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447921460361 and 901","roomName":"447921460361 and 901","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-20T15:19:28.000000Z","localTimestampStart":"2023-07-20T15:19:28.000000Z","timestampEnd":"2023-07-20T15:19:28.000000Z","timestampStart":"2023-07-20T15:19:28.000000Z"}}
{"body":{"displayText":"REG-RESP?v=3;r=216160845;n=+447593438428;s=0264BBC0F1FFFFFFFFE174B2F76BFF6C1AE8D7A48F72C459C5743BA4A7","text":"REG-RESP?v=3;r=216160845;n=+447593438428;s=0264BBC0F1FFFFFFFFE174B2F76BFF6C1AE8D7A48F72C459C5743BA4A7"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447593438428","+447860019210","447593438428","447860019210"],"fromId":"+447860019210","fromIdAddlInfo":{"countryCode":"GB","raw":"447860019210"},"toIds":["+447593438428"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447593438428"}]},"metadata":{"messageId":"3813","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447593438428 and 447860019210"},"roomId":"447593438428 and 447860019210","roomName":"447593438428 and 447860019210","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T11:43:46.000000Z","localTimestampStart":"2023-07-22T11:43:46.000000Z","timestampEnd":"2023-07-22T11:43:46.000000Z","timestampStart":"2023-07-22T11:43:46.000000Z"}}
{"body":{"displayText":"MBOXUPDATE?name=<EMAIL>;server=82.132.141.107;port=143;pw=hKn1Vi7Y","text":"MBOXUPDATE?name=<EMAIL>;server=82.132.141.107;port=143;pw=hKn1Vi7Y"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447706338301","447706338301","notify"],"fromId":"notify","fromIdAddlInfo":{"raw":"notify"},"toIds":["+447706338301"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338301"}]},"metadata":{"messageId":"3486","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338301 and Notify"},"roomId":"447706338301 and Notify","roomName":"447706338301 and Notify","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-07T12:51:55.000000Z","localTimestampStart":"2023-07-07T12:51:55.000000Z","timestampEnd":"2023-07-07T12:51:55.000000Z","timestampStart":"2023-07-07T12:51:55.000000Z"}}
{"body":{"displayText":"We will deliver your PeterMillar parcel today between 09:28-10:28, if not in for your driver Lukest you have options www.dpd.co.uk/b/6d3z_2adO8OU","text":"We will deliver your PeterMillar parcel today between 09:28-10:28, if not in for your driver Lukest you have options www.dpd.co.uk/b/6d3z_2adO8OU"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447921460361","447921460361","dpduk"],"fromId":"dpduk","fromIdAddlInfo":{"raw":"dpduk"},"toIds":["+447921460361"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447921460361"}]},"metadata":{"messageId":"3447","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447921460361 and DPD UK"},"participants":[{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447921460361 and DPD UK","roomName":"447921460361 and DPD UK","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-05T07:02:17.000000Z","localTimestampStart":"2023-07-05T07:02:17.000000Z","timestampEnd":"2023-07-05T07:02:17.000000Z","timestampStart":"2023-07-05T07:02:17.000000Z"}}
{"body":{"displayText":"You've used 80% of your UK + Europe Zone data allowance. If you go over your allowance before it resets on 08/08/23, you will be charged 2.5p per MB. To reduce your data usage, connect over Wi-Fi. For more information on your data options, go to www.o2.co.uk/businessdata. Increasing your allowance is easy, ask your account administrator to call 0800 028 0202.","text":"You've used 80% of your UK + Europe Zone data allowance. If you go over your allowance before it resets on 08/08/23, you will be charged 2.5p per MB. To reduce your data usage, connect over Wi-Fi. For more information on your data options, go to www.o2.co.uk/businessdata. Increasing your allowance is easy, ask your account administrator to call 0800 028 0202."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","o2business"],"fromId":"o2business","fromIdAddlInfo":{"raw":"o2business"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3781","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and O2Business"},"roomId":"************ and O2Business","roomName":"************ and O2Business","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-21T09:00:52.000000Z","localTimestampStart":"2023-07-21T09:00:52.000000Z","timestampEnd":"2023-07-21T09:00:52.000000Z","timestampStart":"2023-07-21T09:00:52.000000Z"}}
{"body":{"displayText":"Your chauffeur, Mujahid Iqbal, has arrived in a Mercedes-Benz E-Class (OF NA 1261). Enjoy your ride!","text":"Your chauffeur, Mujahid Iqbal, has arrived in a Mercedes-Benz E-Class (OF NA 1261). Enjoy your ride!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447706338302","447706338302","blacklane"],"fromId":"blacklane","fromIdAddlInfo":{"raw":"blacklane"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3562","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338302 and Blacklane"},"roomId":"447706338302 and Blacklane","roomName":"447706338302 and Blacklane","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-11T17:26:09.000000Z","localTimestampStart":"2023-07-11T17:26:09.000000Z","timestampEnd":"2023-07-11T17:26:09.000000Z","timestampStart":"2023-07-11T17:26:09.000000Z"}}
{"body":{"displayText":"Your appointment at Clay Hair Salon is confirmed for 20/07/23 at 11:00. Please open http://phore.st/8nfGi to amend your appointment","text":"Your appointment at Clay Hair Salon is confirmed for 20/07/23 at 11:00. Please open http://phore.st/8nfGi to amend your appointment"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+441372844446","+************","441372844446","************"],"fromId":"+441372844446","fromIdAddlInfo":{"countryCode":"GB","raw":"441372844446"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3718","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"441372844446 and ************"},"roomId":"441372844446 and ************","roomName":"441372844446 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-19T11:00:44.000000Z","localTimestampStart":"2023-07-19T11:00:44.000000Z","timestampEnd":"2023-07-19T11:00:44.000000Z","timestampStart":"2023-07-19T11:00:44.000000Z"}}
{"body":{"displayText":"Rigtig Kalle","text":"Rigtig Kalle"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3864","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:28:29.000000Z","localTimestampStart":"2023-07-24T09:28:29.000000Z","timestampEnd":"2023-07-24T09:28:29.000000Z","timestampStart":"2023-07-24T09:28:29.000000Z"}}
{"body":{"displayText":"You missed a call from me at 06:41 14 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 06:41 14 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447453263150","+447851254113","447453263150","447851254113"],"fromId":"+447453263150","fromIdAddlInfo":{"countryCode":"GB","raw":"447453263150"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3645","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447453263150 and 447851254113"},"roomId":"447453263150 and 447851254113","roomName":"447453263150 and 447851254113","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-14T07:13:55.000000Z","localTimestampStart":"2023-07-14T07:13:55.000000Z","timestampEnd":"2023-07-14T07:13:55.000000Z","timestampStart":"2023-07-14T07:13:55.000000Z"}}
{"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447714755207","447714755207","901"],"fromId":"901","fromIdAddlInfo":{"raw":"901"},"toIds":["+447714755207"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447714755207"}]},"metadata":{"messageId":"3976","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447714755207 and 901"},"roomId":"447714755207 and 901","roomName":"447714755207 and 901","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T09:19:15.000000Z","localTimestampStart":"2023-07-27T09:19:15.000000Z","timestampEnd":"2023-07-27T09:19:15.000000Z","timestampStart":"2023-07-27T09:19:15.000000Z"}}
{"body":{"displayText":"Good swim","text":"Good swim"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3913","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T08:40:47.000000Z","localTimestampStart":"2023-07-25T08:40:47.000000Z","timestampEnd":"2023-07-25T08:40:47.000000Z","timestampStart":"2023-07-25T08:40:47.000000Z"}}
{"body":{"displayText":"Eller kommer du nu?","text":"Eller kommer du nu?"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+447867728196"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447867728196"}]},"metadata":{"messageId":"3895","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:45:41.000000Z","localTimestampStart":"2023-07-24T17:45:41.000000Z","timestampEnd":"2023-07-24T17:45:41.000000Z","timestampStart":"2023-07-24T17:45:41.000000Z"}}
{"body":{"displayText":"KX68PLZ parked in zone 2033 from 07:39 on Wed 12/07/23 until 04:00 on Thu 13/07/23. You have been charged \u00a37.70. myringgo.com","text":"KX68PLZ parked in zone 2033 from 07:39 on Wed 12/07/23 until 04:00 on Thu 13/07/23. You have been charged \u00a37.70. myringgo.com"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447786203121","************","447786203121"],"fromId":"+447786203121","fromIdAddlInfo":{"countryCode":"GB","raw":"447786203121"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3601","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447786203121"},"roomId":"************ and 447786203121","roomName":"************ and 447786203121","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-12T18:03:22.000000Z","localTimestampStart":"2023-07-12T18:03:22.000000Z","timestampEnd":"2023-07-12T18:03:22.000000Z","timestampStart":"2023-07-12T18:03:22.000000Z"}}
{"body":{"displayText":"Yes was okay","text":"Yes was okay"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3865","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:33:27.000000Z","localTimestampStart":"2023-07-24T09:33:27.000000Z","timestampEnd":"2023-07-24T09:33:27.000000Z","timestampStart":"2023-07-24T09:33:27.000000Z"}}
{"body":{"displayText":"Hvis jeg ikke har h\u00f8rt fra sig by 5pm g\u00e5r jeg i MS og s\u00e5 kan vi aftale mens jeg handler","text":"Hvis jeg ikke har h\u00f8rt fra sig by 5pm g\u00e5r jeg i MS og s\u00e5 kan vi aftale mens jeg handler"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4039","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T15:45:55.000000Z","localTimestampStart":"2023-07-28T15:45:55.000000Z","timestampEnd":"2023-07-28T15:45:55.000000Z","timestampStart":"2023-07-28T15:45:55.000000Z"}}
{"body":{"displayText":"\ud83d\udcaf","text":"\ud83d\udcaf"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3802","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T08:24:49.000000Z","localTimestampStart":"2023-07-22T08:24:49.000000Z","timestampEnd":"2023-07-22T08:24:49.000000Z","timestampStart":"2023-07-22T08:24:49.000000Z"}}
{"body":{"displayText":"Back in reception area","text":"Back in reception area"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3812","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T11:07:23.000000Z","localTimestampStart":"2023-07-22T11:07:23.000000Z","timestampEnd":"2023-07-22T11:07:23.000000Z","timestampStart":"2023-07-22T11:07:23.000000Z"}}
{"body":{"displayText":"Amex SafeKey code is 762679 for \u00a3236.87 transaction attempt at Amazon for Card ending in 62019. Never share this code.","text":"Amex SafeKey code is 762679 for \u00a3236.87 transaction attempt at Amazon for Card ending in 62019. Never share this code."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","amex"],"fromId":"amex","fromIdAddlInfo":{"raw":"amex"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3965","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Amex"},"roomId":"************ and Amex","roomName":"************ and Amex","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-26T21:49:49.000000Z","localTimestampStart":"2023-07-26T21:49:49.000000Z","timestampEnd":"2023-07-26T21:49:49.000000Z","timestampStart":"2023-07-26T21:49:49.000000Z"}}
{"body":{"displayText":"Blonk street - jeg er p\u00e5 calls til kl 4.00","text":"Blonk street - jeg er p\u00e5 calls til kl 4.00"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4002","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T14:38:20.000000Z","localTimestampStart":"2023-07-27T14:38:20.000000Z","timestampEnd":"2023-07-27T14:38:20.000000Z","timestampStart":"2023-07-27T14:38:20.000000Z"}}
{"body":{"displayText":"Hi  Mark Darrel-brown, your vehicle, a SILVER MERCEDES E220 WA14TVE has arrived. Please proceed to meet your driver.","text":"Hi  Mark Darrel-brown, your vehicle, a SILVER MERCEDES E220 WA14TVE has arrived. Please proceed to meet your driver."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447851254113","+447921853490","447851254113","447921853490"],"fromId":"+447921853490","fromIdAddlInfo":{"countryCode":"GB","raw":"447921853490"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3643","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447851254113 and 447921853490"},"roomId":"447851254113 and 447921853490","roomName":"447851254113 and 447921853490","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-14T07:13:17.000000Z","localTimestampStart":"2023-07-14T07:13:17.000000Z","timestampEnd":"2023-07-14T07:13:17.000000Z","timestampStart":"2023-07-14T07:13:17.000000Z"}}
{"body":{"displayText":"Det var godt Kalle - du ville!","text":"Det var godt Kalle - du ville!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4031","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T15:34:38.000000Z","localTimestampStart":"2023-07-28T15:34:38.000000Z","timestampEnd":"2023-07-28T15:34:38.000000Z","timestampStart":"2023-07-28T15:34:38.000000Z"}}
{"body":{"displayText":"It\u2019s time to turn up your summer. Download the Priority app and level up your summer with 5 days of amazing offers and experiences, starting Monday 31 July. Can\u2019t wait \u2019til then?   Get the app now and check out our current offers. http://v.o2.co.uk/d4B5e3T4  Terms apply. To stop texts, call 2220.","text":"It\u2019s time to turn up your summer. Download the Priority app and level up your summer with 5 days of amazing offers and experiences, starting Monday 31 July. Can\u2019t wait \u2019til then?   Get the app now and check out our current offers. http://v.o2.co.uk/d4B5e3T4  Terms apply. To stop texts, call 2220."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","o2uk"],"fromId":"o2uk","fromIdAddlInfo":{"raw":"o2uk"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4042","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and O2UK"},"roomId":"************ and O2UK","roomName":"************ and O2UK","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T17:17:10.000000Z","localTimestampStart":"2023-07-28T17:17:10.000000Z","timestampEnd":"2023-07-28T17:17:10.000000Z","timestampStart":"2023-07-28T17:17:10.000000Z"}}
{"body":{"displayText":"OK","text":"OK"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3875","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:34:18.000000Z","localTimestampStart":"2023-07-24T09:34:18.000000Z","timestampEnd":"2023-07-24T09:34:18.000000Z","timestampStart":"2023-07-24T09:34:18.000000Z"}}
{"body":{"displayText":"Hi Rob, it's Max at Headland  Are you free to join the Risk.net interview now?","text":"Hi Rob, it's Max at Headland  Are you free to join the Risk.net interview now?"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447590120533","+447706338302","447590120533","447706338302"],"fromId":"+447590120533","fromIdAddlInfo":{"countryCode":"GB","raw":"447590120533"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3697","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447590120533 and 447706338302"},"roomId":"447590120533 and 447706338302","roomName":"447590120533 and 447706338302","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-17T15:36:18.000000Z","localTimestampStart":"2023-07-17T15:36:18.000000Z","timestampEnd":"2023-07-17T15:36:18.000000Z","timestampStart":"2023-07-17T15:36:18.000000Z"}}
{"body":{"displayText":"H\u00e5ber i har en god tur til Portugal","text":"H\u00e5ber i har en god tur til Portugal"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4053","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-30T12:24:04.000000Z","localTimestampStart":"2023-07-30T12:24:04.000000Z","timestampEnd":"2023-07-30T12:24:04.000000Z","timestampStart":"2023-07-30T12:24:04.000000Z"}}
{"body":{"displayText":"OK either way","text":"OK either way"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+447867728196"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447867728196"}]},"metadata":{"messageId":"3896","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:45:53.000000Z","localTimestampStart":"2023-07-24T17:45:53.000000Z","timestampEnd":"2023-07-24T17:45:53.000000Z","timestampStart":"2023-07-24T17:45:53.000000Z"}}
{"body":{"displayText":"And best in Guildford!","text":"And best in Guildford!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3917","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T08:41:55.000000Z","localTimestampStart":"2023-07-25T08:41:55.000000Z","timestampEnd":"2023-07-25T08:41:55.000000Z","timestampStart":"2023-07-25T08:41:55.000000Z"}}
{"body":{"displayText":"Mad choice for lunch?","text":"Mad choice for lunch?"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3869","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:33:46.000000Z","localTimestampStart":"2023-07-24T09:33:46.000000Z","timestampEnd":"2023-07-24T09:33:46.000000Z","timestampStart":"2023-07-24T09:33:46.000000Z"}}
{"body":{"displayText":"You missed a call from me at 14:28 02 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 14:28 02 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442045797857","+447921460361","442045797857","447921460361"],"fromId":"+442045797857","fromIdAddlInfo":{"countryCode":"GB","raw":"442045797857"},"toIds":["+447921460361"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447921460361"}]},"metadata":{"messageId":"3407","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442045797857 and 447921460361"},"participants":[{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"442045797857 and 447921460361","roomName":"442045797857 and 447921460361","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-02T13:28:07.000000Z","localTimestampStart":"2023-07-02T13:28:07.000000Z","timestampEnd":"2023-07-02T13:28:07.000000Z","timestampStart":"2023-07-02T13:28:07.000000Z"}}
{"body":{"displayText":"Hey mate  Still have not heard from Jo, going to issue the Utilisation Requests tomorrow","text":"Hey mate  Still have not heard from Jo, going to issue the Utilisation Requests tomorrow"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447824429366","+447921460361","447824429366","447921460361"],"fromId":"+447824429366","fromIdAddlInfo":{"countryCode":"GB","raw":"447824429366"},"toIds":["+447921460361"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447921460361"}]},"metadata":{"messageId":"3626","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447824429366 and 447921460361"},"participants":[{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447824429366 and 447921460361","roomName":"447824429366 and 447921460361","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-13T15:22:10.000000Z","localTimestampStart":"2023-07-13T15:22:10.000000Z","timestampEnd":"2023-07-13T15:22:10.000000Z","timestampStart":"2023-07-13T15:22:10.000000Z"}}
{"body":{"displayText":"Kommer tilbage om 10mins til Ponds Forge","text":"Kommer tilbage om 10mins til Ponds Forge"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3810","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T10:34:25.000000Z","localTimestampStart":"2023-07-22T10:34:25.000000Z","timestampEnd":"2023-07-22T10:34:25.000000Z","timestampStart":"2023-07-22T10:34:25.000000Z"}}
{"body":{"displayText":"Du har et swim tilbage - swim the race of your life! It is all in your head....","text":"Du har et swim tilbage - swim the race of your life! It is all in your head...."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3843","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-23T15:15:05.000000Z","localTimestampStart":"2023-07-23T15:15:05.000000Z","timestampEnd":"2023-07-23T15:15:05.000000Z","timestampStart":"2023-07-23T15:15:05.000000Z"}}
{"body":{"displayText":"On behalf of Legal & General, Inuvi are inviting you to book a telephone interview for your group scheme protection policy:  https://landg.bookwithinuvi.co.uk/A9D0B38a2EaA","text":"On behalf of Legal & General, Inuvi are inviting you to book a telephone interview for your group scheme protection policy:  https://landg.bookwithinuvi.co.uk/A9D0B38a2EaA"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","inuvi"],"fromId":"inuvi","fromIdAddlInfo":{"raw":"inuvi"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3409","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Inuvi"},"roomId":"************ and Inuvi","roomName":"************ and Inuvi","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-03T08:18:36.000000Z","localTimestampStart":"2023-07-03T08:18:36.000000Z","timestampEnd":"2023-07-03T08:18:36.000000Z","timestampStart":"2023-07-03T08:18:36.000000Z"}}
{"body":{"displayText":"Hey mate it's in train with Jo she's coordinating across other EAs i gather","text":"Hey mate it's in train with Jo she's coordinating across other EAs i gather"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447824429366","+447921460361","447824429366","447921460361"],"fromId":"+447921460361","fromIdAddlInfo":{"countryCode":"GB","raw":"447921460361"},"toIds":["+447824429366"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447824429366"}]},"metadata":{"messageId":"3589","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447824429366 and 447921460361"},"participants":[{"types":["FROM"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447824429366 and 447921460361","roomName":"447824429366 and 447921460361","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-12T14:41:11.000000Z","localTimestampStart":"2023-07-12T14:41:11.000000Z","timestampEnd":"2023-07-12T14:41:11.000000Z","timestampStart":"2023-07-12T14:41:11.000000Z"}}
{"body":{"displayText":"Og push - du kan sagtens!","text":"Og push - du kan sagtens!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3980","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T11:01:36.000000Z","localTimestampStart":"2023-07-27T11:01:36.000000Z","timestampEnd":"2023-07-27T11:01:36.000000Z","timestampStart":"2023-07-27T11:01:36.000000Z"}}
{"body":{"displayText":"Michael, could you please call me back, otherwise I could call Christian.","text":"Michael, could you please call me back, otherwise I could call Christian."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB","DE"],"allIds":["+447706338301","+491791019025","447706338301","491791019025"],"fromId":"+447706338301","fromIdAddlInfo":{"countryCode":"GB","raw":"447706338301"},"toIds":["+491791019025"],"toIdsAddlInfo":[{"countryCode":"DE","raw":"491791019025"}]},"metadata":{"messageId":"3688","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338301 and 491791019025"},"roomId":"447706338301 and 491791019025","roomName":"447706338301 and 491791019025","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-17T14:25:51.000000Z","localTimestampStart":"2023-07-17T14:25:51.000000Z","timestampEnd":"2023-07-17T14:25:51.000000Z","timestampStart":"2023-07-17T14:25:51.000000Z"}}
{"body":{"displayText":"You missed a call from me at 17:40 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 17:40 19 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442079939300","+************","442079939300","************"],"fromId":"+442079939300","fromIdAddlInfo":{"countryCode":"GB","raw":"442079939300"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3740","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442079939300 and ************"},"roomId":"442079939300 and ************","roomName":"442079939300 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-19T16:43:52.000000Z","localTimestampStart":"2023-07-19T16:43:52.000000Z","timestampEnd":"2023-07-19T16:43:52.000000Z","timestampStart":"2023-07-19T16:43:52.000000Z"}}
{"body":{"displayText":"Har du g\u00e5et til m&s eller til blonk street","text":"Har du g\u00e5et til m&s eller til blonk street"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4001","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T14:36:05.000000Z","localTimestampStart":"2023-07-27T14:36:05.000000Z","timestampEnd":"2023-07-27T14:36:05.000000Z","timestampStart":"2023-07-27T14:36:05.000000Z"}}
{"body":{"displayText":"You missed a call from me at 17:23 26 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 17:23 26 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3958","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-26T16:23:49.000000Z","localTimestampStart":"2023-07-26T16:23:49.000000Z","timestampEnd":"2023-07-26T16:23:49.000000Z","timestampStart":"2023-07-26T16:23:49.000000Z"}}
{"body":{"displayText":"\ud83d\udca5","text":"\ud83d\udca5"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3803","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T08:24:56.000000Z","localTimestampStart":"2023-07-22T08:24:56.000000Z","timestampEnd":"2023-07-22T08:24:56.000000Z","timestampStart":"2023-07-22T08:24:56.000000Z"}}
{"body":{"displayText":"You missed a call from me at 16:24 20 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 16:24 20 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442077797239","+447706338302","442077797239","447706338302"],"fromId":"+442077797239","fromIdAddlInfo":{"countryCode":"GB","raw":"442077797239"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3762","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442077797239 and 447706338302"},"roomId":"442077797239 and 447706338302","roomName":"442077797239 and 447706338302","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-20T15:24:22.000000Z","localTimestampStart":"2023-07-20T15:24:22.000000Z","timestampEnd":"2023-07-20T15:24:22.000000Z","timestampStart":"2023-07-20T15:24:22.000000Z"}}
{"body":{"displayText":"Your One Time Password to edit payee X5324 is 234254. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00.","text":"Your One Time Password to edit payee X5324 is 234254. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","citibank"],"fromId":"citibank","fromIdAddlInfo":{"raw":"citibank"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3969","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Citibank"},"roomId":"************ and Citibank","roomName":"************ and Citibank","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T07:11:49.000000Z","localTimestampStart":"2023-07-27T07:11:49.000000Z","timestampEnd":"2023-07-27T07:11:49.000000Z","timestampStart":"2023-07-27T07:11:49.000000Z"}}
{"body":{"displayText":"It is in your head at du vinder / swims well! Din krop har det allerede.","text":"It is in your head at du vinder / swims well! Din krop har det allerede."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3981","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T11:03:47.000000Z","localTimestampStart":"2023-07-27T11:03:47.000000Z","timestampEnd":"2023-07-27T11:03:47.000000Z","timestampStart":"2023-07-27T11:03:47.000000Z"}}
{"body":{"displayText":"https://amazon.co.uk/a/c/r/yDksyfD2tPv9khsZhaneE1Bq9  Amazon: Sign-in from SHF, GB. Tap link to respond.","text":"https://amazon.co.uk/a/c/r/yDksyfD2tPv9khsZhaneE1Bq9  Amazon: Sign-in from SHF, GB. Tap link to respond."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","amazon"],"fromId":"amazon","fromIdAddlInfo":{"raw":"amazon"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4043","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Amazon"},"roomId":"************ and Amazon","roomName":"************ and Amazon","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T19:10:46.000000Z","localTimestampStart":"2023-07-28T19:10:46.000000Z","timestampEnd":"2023-07-28T19:10:46.000000Z","timestampStart":"2023-07-28T19:10:46.000000Z"}}
{"body":{"displayText":"Hi good morning Mr Mark I'm avenue drivers when you collect your luggage give me a call please thank you I am just outside the airport","text":"Hi good morning Mr Mark I'm avenue drivers when you collect your luggage give me a call please thank you I am just outside the airport"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447453263150","+447851254113","447453263150","447851254113"],"fromId":"+447453263150","fromIdAddlInfo":{"countryCode":"GB","raw":"447453263150"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3647","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447453263150 and 447851254113"},"roomId":"447453263150 and 447851254113","roomName":"447453263150 and 447851254113","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-14T07:14:28.000000Z","localTimestampStart":"2023-07-14T07:14:28.000000Z","timestampEnd":"2023-07-14T07:14:28.000000Z","timestampStart":"2023-07-14T07:14:28.000000Z"}}
{"body":{"displayText":"Du er super st\u00e6rk!","text":"Du er super st\u00e6rk!"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3806","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-22T08:55:42.000000Z","localTimestampStart":"2023-07-22T08:55:42.000000Z","timestampEnd":"2023-07-22T08:55:42.000000Z","timestampStart":"2023-07-22T08:55:42.000000Z"}}
{"body":{"displayText":"i will go now","text":"i will go now"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+447867728196","fromIdAddlInfo":{"countryCode":"GB","raw":"447867728196"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3897","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:48:08.000000Z","localTimestampStart":"2023-07-24T17:48:08.000000Z","timestampEnd":"2023-07-24T17:48:08.000000Z","timestampStart":"2023-07-24T17:48:08.000000Z"}}
{"body":{"displayText":"Your telephone interview is arranged: Friday, 14/07/2023 10:15. For questions call 01184032446 quoting reference number GPE0723860.","text":"Your telephone interview is arranged: Friday, 14/07/2023 10:15. For questions call 01184032446 quoting reference number GPE0723860."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","inuvi"],"fromId":"inuvi","fromIdAddlInfo":{"raw":"inuvi"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3449","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Inuvi"},"roomId":"************ and Inuvi","roomName":"************ and Inuvi","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-05T08:41:40.000000Z","localTimestampStart":"2023-07-05T08:41:40.000000Z","timestampEnd":"2023-07-05T08:41:40.000000Z","timestampStart":"2023-07-05T08:41:40.000000Z"}}
{"body":{"displayText":"You missed a call from me at 16:52 24 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 16:52 24 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB","US"],"allIds":["+16465945945","+************","16465945945","************"],"fromId":"+16465945945","fromIdAddlInfo":{"countryCode":"US","raw":"16465945945"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3887","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"16465945945 and ************"},"roomId":"16465945945 and ************","roomName":"16465945945 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T15:52:44.000000Z","localTimestampStart":"2023-07-24T15:52:44.000000Z","timestampEnd":"2023-07-24T15:52:44.000000Z","timestampStart":"2023-07-24T15:52:44.000000Z"}}
{"body":{"displayText":"\ud83c\udde9\ud83c\uddf0","text":"\ud83c\udde9\ud83c\uddf0"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4032","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T15:35:03.000000Z","localTimestampStart":"2023-07-28T15:35:03.000000Z","timestampEnd":"2023-07-28T15:35:03.000000Z","timestampStart":"2023-07-28T15:35:03.000000Z"}}
{"body":{"displayText":"Your One Time Password to edit payee X4305 is 224268. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00.","text":"Your One Time Password to edit payee X4305 is 224268. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","citibank"],"fromId":"citibank","fromIdAddlInfo":{"raw":"citibank"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3970","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Citibank"},"roomId":"************ and Citibank","roomName":"************ and Citibank","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T07:13:08.000000Z","localTimestampStart":"2023-07-27T07:13:08.000000Z","timestampEnd":"2023-07-27T07:13:08.000000Z","timestampStart":"2023-07-27T07:13:08.000000Z"}}
{"body":{"displayText":"You've now used 100% of your UK + Europe Zone data allowance. You will be charged 2.5p per MB for any extra usage before your allowance resets on 08/08/23, your next bill date.  Alternatively you can connect to Wi-Fi. For more information on your data options, go to www.o2.co.uk/businessdata. Increasing your allowance is easy, ask your account administrator to call 0800 028 0202.","text":"You've now used 100% of your UK + Europe Zone data allowance. You will be charged 2.5p per MB for any extra usage before your allowance resets on 08/08/23, your next bill date.  Alternatively you can connect to Wi-Fi. For more information on your data options, go to www.o2.co.uk/businessdata. Increasing your allowance is easy, ask your account administrator to call 0800 028 0202."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","o2business"],"fromId":"o2business","fromIdAddlInfo":{"raw":"o2business"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3784","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and O2Business"},"roomId":"************ and O2Business","roomName":"************ and O2Business","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-21T09:47:52.000000Z","localTimestampStart":"2023-07-21T09:47:52.000000Z","timestampEnd":"2023-07-21T09:47:52.000000Z","timestampStart":"2023-07-21T09:47:52.000000Z"}}
{"body":{"displayText":"Christian calls you right away","text":"Christian calls you right away"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB","DE"],"allIds":["+447706338301","+491791019025","447706338301","491791019025"],"fromId":"+491791019025","fromIdAddlInfo":{"countryCode":"DE","raw":"491791019025"},"toIds":["+447706338301"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338301"}]},"metadata":{"messageId":"3689","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338301 and 491791019025"},"roomId":"447706338301 and 491791019025","roomName":"447706338301 and 491791019025","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-17T14:32:16.000000Z","localTimestampStart":"2023-07-17T14:32:16.000000Z","timestampEnd":"2023-07-17T14:32:16.000000Z","timestampStart":"2023-07-17T14:32:16.000000Z"}}
{"body":{"displayText":"You missed calls from me at 17:23 and 17:24 26 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed calls from me at 17:23 and 17:24 26 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3960","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-26T16:24:07.000000Z","localTimestampStart":"2023-07-26T16:24:07.000000Z","timestampEnd":"2023-07-26T16:24:07.000000Z","timestampStart":"2023-07-26T16:24:07.000000Z"}}
{"body":{"displayText":"5","text":"5"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447800006796","+447821646846","447800006796","447821646846"],"fromId":"+447821646846","fromIdAddlInfo":{"countryCode":"GB","raw":"447821646846"},"toIds":["+447800006796"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447800006796"}]},"metadata":{"messageId":"3460","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447800006796 and 447821646846"},"roomId":"447800006796 and 447821646846","roomName":"447800006796 and 447821646846","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-05T22:11:03.000000Z","localTimestampStart":"2023-07-05T22:11:03.000000Z","timestampEnd":"2023-07-05T22:11:03.000000Z","timestampStart":"2023-07-05T22:11:03.000000Z"}}
{"body":{"displayText":"A SILVER MERCEDES E220 WA14TVE is enroute. Please proceed to your pickup point. Avenue Taxis 01473 888888","text":"A SILVER MERCEDES E220 WA14TVE is enroute. Please proceed to your pickup point. Avenue Taxis 01473 888888"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447851254113","+447921853490","447851254113","447921853490"],"fromId":"+447921853490","fromIdAddlInfo":{"countryCode":"GB","raw":"447921853490"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3644","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447851254113 and 447921853490"},"roomId":"447851254113 and 447921853490","roomName":"447851254113 and 447921853490","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-14T07:13:17.000000Z","localTimestampStart":"2023-07-14T07:13:17.000000Z","timestampEnd":"2023-07-14T07:13:17.000000Z","timestampStart":"2023-07-14T07:13:17.000000Z"}}
{"body":{"displayText":"Go go Kalle, held og lykke","text":"Go go Kalle, held og lykke"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3979","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T11:01:00.000000Z","localTimestampStart":"2023-07-27T11:01:00.000000Z","timestampEnd":"2023-07-27T11:01:00.000000Z","timestampStart":"2023-07-27T11:01:00.000000Z"}}
{"body":{"displayText":"Ok fish it is","text":"Ok fish it is"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3873","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:34:07.000000Z","localTimestampStart":"2023-07-24T09:34:07.000000Z","timestampEnd":"2023-07-24T09:34:07.000000Z","timestampStart":"2023-07-24T09:34:07.000000Z"}}
{"body":{"displayText":"1","text":"1"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447588679800","+447706353283","447588679800","447706353283"],"fromId":"+447706353283","fromIdAddlInfo":{"countryCode":"GB","raw":"447706353283"},"toIds":["+447588679800"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447588679800"}]},"metadata":{"messageId":"4034","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447588679800 and 447706353283"},"roomId":"447588679800 and 447706353283","roomName":"447588679800 and 447706353283","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T15:36:53.000000Z","localTimestampStart":"2023-07-28T15:36:53.000000Z","timestampEnd":"2023-07-28T15:36:53.000000Z","timestampStart":"2023-07-28T15:36:53.000000Z"}}
{"body":{"displayText":"You missed a call from me at 12:52 28 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 12:52 28 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442077741000","+447706338302","442077741000","447706338302"],"fromId":"+442077741000","fromIdAddlInfo":{"countryCode":"GB","raw":"442077741000"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"4021","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442077741000 and 447706338302"},"roomId":"442077741000 and 447706338302","roomName":"442077741000 and 447706338302","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-28T11:52:53.000000Z","localTimestampStart":"2023-07-28T11:52:53.000000Z","timestampEnd":"2023-07-28T11:52:53.000000Z","timestampStart":"2023-07-28T11:52:53.000000Z"}}
{"body":{"displayText":"You missed a call from me at 17:40 27 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed a call from me at 17:40 27 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+442079939300","+************","442079939300","************"],"fromId":"+442079939300","fromIdAddlInfo":{"countryCode":"GB","raw":"442079939300"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"4010","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"442079939300 and ************"},"roomId":"442079939300 and ************","roomName":"442079939300 and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T16:46:52.000000Z","localTimestampStart":"2023-07-27T16:46:52.000000Z","timestampEnd":"2023-07-27T16:46:52.000000Z","timestampStart":"2023-07-27T16:46:52.000000Z"}}
{"body":{"displayText":"The chauffeur for your ride 376158103 at 22:20 is Shahid Ehsan, who can be reached at +447300830938. Download our app: blacklane.com/app?t=g17c7n","text":"The chauffeur for your ride 376158103 at 22:20 is Shahid Ehsan, who can be reached at +447300830938. Download our app: blacklane.com/app?t=g17c7n"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447706338302","447706338302","blacklane"],"fromId":"blacklane","fromIdAddlInfo":{"raw":"blacklane"},"toIds":["+447706338302"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447706338302"}]},"metadata":{"messageId":"3773","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447706338302 and Blacklane"},"roomId":"447706338302 and Blacklane","roomName":"447706338302 and Blacklane","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-20T21:15:23.000000Z","localTimestampStart":"2023-07-20T21:15:23.000000Z","timestampEnd":"2023-07-20T21:15:23.000000Z","timestampStart":"2023-07-20T21:15:23.000000Z"}}
{"body":{"displayText":"Your One Time Password to edit payee X4371 is 403412. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00.","text":"Your One Time Password to edit payee X4371 is 403412. Do not share this code with anyone. If you did not request this, call Citiphone UK on +44 207 500 55 00."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","citibank"],"fromId":"citibank","fromIdAddlInfo":{"raw":"citibank"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3968","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and Citibank"},"roomId":"************ and Citibank","roomName":"************ and Citibank","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-27T07:10:16.000000Z","localTimestampStart":"2023-07-27T07:10:16.000000Z","timestampEnd":"2023-07-27T07:10:16.000000Z","timestampStart":"2023-07-27T07:10:16.000000Z"}}
{"body":{"displayText":"The m and s best ever one","text":"The m and s best ever one"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3872","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T09:33:59.000000Z","localTimestampStart":"2023-07-24T09:33:59.000000Z","timestampEnd":"2023-07-24T09:33:59.000000Z","timestampStart":"2023-07-24T09:33:59.000000Z"}}
{"body":{"displayText":"You missed calls from me at 07:33, 07:59 and 08:11 14 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5.","text":"You missed calls from me at 07:33, 07:59 and 08:11 14 Jul.This is a free Call Alert from O2. To disable this dial 901, press *, then option 5 and option 5."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447453263150","+447851254113","447453263150","447851254113"],"fromId":"+447453263150","fromIdAddlInfo":{"countryCode":"GB","raw":"447453263150"},"toIds":["+447851254113"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447851254113"}]},"metadata":{"messageId":"3648","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447453263150 and 447851254113"},"roomId":"447453263150 and 447851254113","roomName":"447453263150 and 447851254113","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-14T07:15:11.000000Z","localTimestampStart":"2023-07-14T07:15:11.000000Z","timestampEnd":"2023-07-14T07:15:11.000000Z","timestampStart":"2023-07-14T07:15:11.000000Z"}}
{"body":{"displayText":"Venter vi p\u00e5 Christian","text":"Venter vi p\u00e5 Christian"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+447867728196","************","447867728196"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+447867728196"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447867728196"}]},"metadata":{"messageId":"3894","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and 447867728196"},"roomId":"************ and 447867728196","roomName":"************ and 447867728196","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T17:45:34.000000Z","localTimestampStart":"2023-07-24T17:45:34.000000Z","timestampEnd":"2023-07-24T17:45:34.000000Z","timestampStart":"2023-07-24T17:45:34.000000Z"}}
{"body":{"displayText":"O2: You've reached 50% of your Spend Cap. If you want to see your options, go to o2.co.uk/business/support/spendcap call 8002 free from your O2 mobile or chat to your contract holder.","text":"O2: You've reached 50% of your Spend Cap. If you want to see your options, go to o2.co.uk/business/support/spendcap call 8002 free from your O2 mobile or chat to your contract holder."},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","************","o2uk"],"fromId":"o2uk","fromIdAddlInfo":{"raw":"o2uk"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3905","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and O2UK"},"roomId":"************ and O2UK","roomName":"************ and O2UK","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T07:02:05.000000Z","localTimestampStart":"2023-07-25T07:02:05.000000Z","timestampEnd":"2023-07-25T07:02:05.000000Z","timestampStart":"2023-07-25T07:02:05.000000Z"}}
{"body":{"displayText":"Godt swim Kalle","text":"Godt swim Kalle"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3836","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-23T10:44:48.000000Z","localTimestampStart":"2023-07-23T10:44:48.000000Z","timestampEnd":"2023-07-23T10:44:48.000000Z","timestampStart":"2023-07-23T10:44:48.000000Z"}}
{"body":{"displayText":"Rigtig godt klaret","text":"Rigtig godt klaret"},"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+************","+************","************","************"],"fromId":"+************","fromIdAddlInfo":{"countryCode":"GB","raw":"************"},"toIds":["+************"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"************"}]},"metadata":{"messageId":"3916","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"************ and ************"},"roomId":"************ and ************","roomName":"************ and ************","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-25T08:41:31.000000Z","localTimestampStart":"2023-07-25T08:41:31.000000Z","timestampEnd":"2023-07-25T08:41:31.000000Z","timestampStart":"2023-07-25T08:41:31.000000Z"}}
{"chatType":"SMS","identifiers":{"allCountryCodes":["GB"],"allIds":["+447921460361","447921460361","901"],"fromId":"901","fromIdAddlInfo":{"raw":"901"},"toIds":["+447921460361"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447921460361"}]},"metadata":{"messageId":"3883","sizeInBytes":12407,"source":{"client":"O2Mobile_SMS","fileInfo":{"contentLength":12407,"contentMD5":"\"e4626bb5298330ab7915d1f28bdcd3c7\"","lastModified":"2022-07-06 06:59:38+00:00","location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv"},"processed":"2022-07-06T06:59:38.911459Z"}},"threadId":"447921460361 and 901"},"participants":[{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+447921460361"}]},"name":"MJ","personalDetails":{"firstName":"M","lastName":"J"},"uniqueIds":["+447921460361"]}}],"roomId":"447921460361 and 901","roomName":"447921460361 and 901","sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/o2_sms_poll/4775282_SMSMessages_2023_01.csv","timestamps":{"localTimestampEnd":"2023-07-24T13:44:49.000000Z","localTimestampStart":"2023-07-24T13:44:49.000000Z","timestampEnd":"2023-07-24T13:44:49.000000Z","timestampStart":"2023-07-24T13:44:49.000000Z"}}