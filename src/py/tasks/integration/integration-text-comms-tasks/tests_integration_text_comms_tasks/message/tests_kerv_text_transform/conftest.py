# This needs to be here since the conftest is loaded first
# than any import on the test files
import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def aries_task_input_json() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="kerv_text",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2023, 9, 14),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/kerv_text/"
            "2023/12/22/iuyt3k/batch_test_kerv_json.ndjson",
            streamed=True,
        )
    )

    task = TaskFieldSet(name="kerv_text_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&timestamp": 1652689230569,
            "uniqueIds": ["+447527421464"],
            "&uniqueProps": ["+447527421464"],
            "&model": "MarketPerson",
            "name": "MJ",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+447527421464"}
            ],
            "personalDetails": {"firstName": "Mary", "lastName": "Jane"},
        }
    ]

    return pd.DataFrame(data=list_dict)


@pytest.fixture()
def aries_task_input_zip() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="kerv_text",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2023, 9, 14),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/kerv_text/"
            "2023/12/22/iuyt3k/batch_test_kerv_zip.ndjson",
            streamed=True,
        )
    )

    task = TaskFieldSet(name="kerv_text_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
