# mypy: disable-error-code="attr-defined"
import logging
import os
import pandas as pd
import tempfile
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.chat_event.static import ChatEventColumns
from aries_se_comms_tasks.feeds.message.asc_chat.resolve_modification_items import (
    run_resolve_modification_items,
)
from aries_se_comms_tasks.feeds.message.asc_chat.static import (
    ASC_CHAT_FLOW_NAME,
    ASC_CHAT_SOURCE_SCHEMA,
    CHAT_EVENT_COLUMN_SUFFIX,
    MESSAGES_XML_ROOT,
    METADATA_XML_ROOT,
    FileUrlColumns,
    SkipIfIncorrectFile,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as ParamsLinkParticipants,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_comms_tasks.voice.generic.xml_batch_download_parser import (
    Params as XmlBatchDownloadParserParams,
)
from aries_se_comms_tasks.voice.generic.xml_batch_download_parser import (
    run_xml_batch_download_parser,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    Params as ParamsMultipleFilesInputController,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.frame.filter_columns import Params as FilterColumnsParams
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as ParamsFrameColumnManipulator,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    Params as ParamsMergeAndSplitDataframes,
)
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    SkipIfResultEmpty,
    run_merge_and_split_dataframes,
)
from aries_se_core_tasks.frame.remove_duplicates import Params as ParamsRemoveDuplicate
from aries_se_core_tasks.frame.remove_duplicates import run_remove_duplicates
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as ParamsGenerateRecordFileIdentifiersForDf,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import Params as ParamsBatchProducer
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.xml_file_splitter import Params as ParamsXmlFileSplitter
from aries_se_core_tasks.io.read.xml_file_splitter import run_xml_file_splitter
from aries_se_core_tasks.io.utils import FileInfo, get_file_info
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.frame_manipulation import get_rows_not_in_other_df
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import upsert_audit
from integration_text_comms_tasks.message.asc_chat_transform.input_schema import (
    AscChatAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_elastic_schema.models import ChatEvent, Message
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional

logger = logging.getLogger(__name__)


def asc_chat_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    ------------------------------------------------------------
    ||                   AscChatTransform                    ||
    ------------------------------------------------------------
    - Input: The csv file which is dropped in aries/ingest/asc_chat/*.

    This flow expects an XMPP file containing messages and an XML file
    with which we resolve chat event information and message record participants.

    The flow is triggered by the upload of either of these files, and looks for
    the corresponding pair file.

    If the pair file is missing, it finishes the flow immediately and sets all
    the dynamic tasks to [] and dynamic task inputs to {}

    If both files are present, they are linked together, and message update, create
    and chat event records are created (if applicable).
    and we are able to generate the chat event record.

    'Entered' Chat events are created for each participant of each message, and
    duplicate chat events are discarded at the end of the flow.

    Note that the flow creates batches in all three of its outputs (when they
    are not null), and the workflow uses 3 dynamic fork joins

    NOTE:
    Supports an additional env var (which has a default when not provided)
    MESSAGE_BATCH_SIZE. This
    """

    # Get the message batch size for the xml file splitter
    message_batch_size = int(os.environ.get("MESSAGE_BATCH_SIZE", 1000))

    # Get tenant workflow tenant config from postgres config db
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix: str = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Parse and validate AriesTaskInput parameters
    task_transform_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=AscChatAriesTaskInput
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    # Get all the dynamic task definitions from workflow input
    # in this case it is required, "message"
    message_create_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "message_create"
    )

    message_update_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "message_update"
    )

    chat_event_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "chat_event"
    )
    #  Initialise variables
    messages_to_create_ndjson_paths_list = list()
    messages_to_update_ndjson_paths_list = list()
    message_update_batch_num = 0
    list_of_chat_event_dfs = list()

    # Check if both required files are present. If they are, this task will return
    # a single row with 2 columns cloud_xmpp_url and cloud_xml_url.
    # If they are not, a SKIP exception is raised, which is caught. In these cases,
    # the flow finishes immediately by writing out a JSON with null file_uri

    try:
        input_df = run_multiple_files_input_controller(
            file_url=task_transform_input.file_uri,
            cloud_provider=cloud_provider,
            realm=realm,
            params=ParamsMultipleFilesInputController(
                list_of_files_regex=[".cht.(xmpp)$", ".xmpp.(xml)$"],
                unique_identifier_regex=["^(.+?)(?=(?:\.xmpp\.xml|\.cht\.xmpp))"],  # noqa W605
                file_links_in_output=True,
                prefix_for_file_links_in_output="cloud_",
                list_of_files_regex_for_which_empty_frame_returned=[
                    r"^(?!.*(?:xmpp|cht)\.xml$).*xml$"
                ],
            ),
        )
        if input_df.empty:
            raise SkipIfIncorrectFile("Just xml file sent which contains voice metadata")
    except (
        SkipIfIncorrectFile,
        SkipIfMissingFiles,
        SkipIfSourceTimestampSameAlphaLess,
        SkipIfSourceTimestampLessThanPair,
    ) as e:
        logger.warning(f"Skipped due to: {e}")
        if task_transform_input.file_uri.endswith(".xmpp.xml"):
            source_file = "xmpp.xml"
            pair_file = ".cht.xmpp"
        elif task_transform_input.file_uri.endswith(".cht.xmpp"):
            pair_file = "xmpp.xml"
            source_file = ".cht.xmpp"
        else:
            pair_file = "<No pair file>"
            source_file = ".xml"
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            workflow_status=[
                f"Processing will be skipped for the {source_file} file"
                f" '{Path(task_transform_input.file_uri).name}' as"
                f" it will be processed along with"
                f" the corresponding {pair_file} file in a separate run",
            ],
            models=[Message],
        )

    # Else cause for try, except
    else:
        xmpp_file_url = input_df.frame().loc[0, FileUrlColumns.XMPP_FILE_URL]
        xml_file_url = input_df.frame().loc[0, FileUrlColumns.XML_FILE_URL]
        local_file_path: str = run_download_file(file_url=xmpp_file_url)

        # Get metadata of the file
        input_file_metadata: FileInfo = get_file_info(path=xmpp_file_url)

        # Download and parse the XML file
        metadata_df = run_xml_batch_download_parser(
            source_frame=input_df,
            params=XmlBatchDownloadParserParams(
                metadata_url_column=FileUrlColumns.XML_FILE_URL,
                target_source_key_column=FileUrlColumns.XML_FILE_URL,
                target_s3_bucket_column=TempColumns.METADATA_SOURCE_BUCKET,
                target_s3_key_column=TempColumns.METADATA_SOURCE_KEY,
                use_xmltodict=True,
                data_path=METADATA_XML_ROOT,
                xml_attribute_key_value_dict={"key_field": "@name", "value_field": "@value"},
            ),
        )

        # Parse the XMPP file and create chunks
        messages_chunks = run_xml_file_splitter(
            extractor_result=ExtractPathResult(path=Path(local_file_path)),
            params=ParamsXmlFileSplitter(
                nested_data_path=MESSAGES_XML_ROOT, chunksize=message_batch_size
            ),
            streamed=streamed,
            sources_dir=tempfile.mkdtemp(),
        )

        for idx, file_splitter_chunk in enumerate(messages_chunks):
            # ----------------------------------Chat Processing----------------------------------
            message_data_batch = run_batch_producer(
                params=ParamsBatchProducer(source_schema=ASC_CHAT_SOURCE_SCHEMA),
                file_splitter_result=file_splitter_chunk,
                streamed=streamed,
                return_dataframe=True,
            )
            message_data_batch = _assign_xml_file_url(
                xmpp_df=message_data_batch, xml_file_url=xml_file_url
            )
            # Join together the message and metadata dfs on FileUrlColumns.XML_FILE_URL
            message_with_metadata_df = run_frame_concatenator(
                message_df=message_data_batch,
                metadata_df=metadata_df,
                params=ParamsFrameConcatenator(on_column=FileUrlColumns.XML_FILE_URL),
            )

            mappings_df = run_get_primary_transformations(
                source_frame=message_with_metadata_df,
                flow=ASC_CHAT_FLOW_NAME,
                realm=realm,
                tenant=tenant,
                input_file_path=xmpp_file_url,
                input_file_metadata=input_file_metadata,
            )

            # In the mappings, we have created duplicate rows for chat events
            # We need to drop these for messages
            deduplicated_mappings_df = run_remove_duplicates(
                source_frame=mappings_df,
                params=ParamsRemoveDuplicate(keep="first", drop_duplicates_by_index=True),
            )

            # Create message dataframe
            message_mappings_df = run_filter_columns(
                source_frame=deduplicated_mappings_df,
                params=FilterColumnsParams(
                    action=ActionEnum.drop,
                    column_regex=CHAT_EVENT_COLUMN_SUFFIX,
                ),
            )

            message_update_df = run_resolve_modification_items(
                source_frame=message_mappings_df, tenant=tenant
            )

            # Any records in message_update_df should be removed from
            # message_mappings_df. These messages shouldn't be ingested as
            # new messages.
            # Also, chat events shouldn't be ingested for update records.
            # So, as chat events are present in message_mappings_df (and
            # separated later), this will also remove chat events for
            # updates.
            if not message_update_df.empty:
                message_mappings_df = get_rows_not_in_other_df(
                    source_frame=message_mappings_df,
                    other_frame=message_update_df,
                    source_frame_columns=[MessageColumns.METADATA_MESSAGE_ID],
                    other_frame_columns=[MessageColumns.METADATA_MESSAGE_ID],
                    reset_index=True,
                )
            if not message_mappings_df.empty:
                # Get the amp ID, this is used for the audit keys
                frame_with_amp_id = run_generate_record_identifiers_for_df(
                    source_frame=message_mappings_df,
                    params=ParamsGenerateRecordFileIdentifiersForDf(
                        data_model=MetaModel.MESSAGE,
                        target_record_identifier_col="record_identifier",
                    ),
                    streamed=streamed,
                    cloud_provider=cloud_provider,
                )
                # Summary -> Link participant identifiers with real tenant ElasticSearch documents
                message_participants_df = run_link_participants(
                    source_frame=frame_with_amp_id,
                    tenant=tenant,
                    params=ParamsLinkParticipants(all_ids=MessageColumns.IDENTIFIERS_ALL_IDS),
                    streamed=streamed,
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                    data_models=[Message],
                    record_identifier_column="record_identifier",
                )

                # Concatenate the mappings_df and participants_df
                final_message_df = run_frame_concatenator(
                    mappings_df=message_mappings_df,
                    participants_df=message_participants_df,
                    params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
                )

                if not final_message_df.empty:
                    message_create_ndjson_path = create_ndjson_path(
                        tenant_bucket=tenant_bucket_with_cloud_prefix,
                        aries_task_input=aries_task_input,
                        model=MetaModel.MESSAGE,
                        suffix=f"{idx}",
                    )

                    messages_to_create_ndjson_paths_list.append(message_create_ndjson_path)

                    run_write_ndjson(
                        source_serializer_result=final_message_df,
                        output_filepath=message_create_ndjson_path,
                        audit_output=True,
                        app_metrics_path=app_metrics_path,
                        audit_path=audit_path,
                    )

            if not message_update_df.empty:
                message_update_ndjson_path = create_ndjson_path(
                    tenant_bucket=tenant_bucket_with_cloud_prefix,
                    aries_task_input=aries_task_input,
                    model=MetaModel.MESSAGE,
                    suffix=f"U_{message_update_batch_num}",
                )

                message_update_batch_num += 1
                messages_to_update_ndjson_paths_list.append(message_update_ndjson_path)

                run_write_ndjson(
                    source_serializer_result=message_update_df,
                    output_filepath=message_update_ndjson_path,
                    audit_output=True,
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                )

            # ----------------------------------Chat Event Processing-------------------------------
            # We only produce 1 type of Chat Event record (ENTERED Event) for a specific
            # conversation.
            # We create 1 chat event record for each of the participants present in the chat.

            chat_event_df = run_filter_columns(
                source_frame=mappings_df,
                params=FilterColumnsParams(
                    action=ActionEnum.keep,
                    column_regex=CHAT_EVENT_COLUMN_SUFFIX,
                ),
            )

            chat_event_df_normalized = run_frame_column_manipulator(
                source_frame=chat_event_df,
                params=ParamsFrameColumnManipulator(
                    action=Action.strip,
                    suffix=CHAT_EVENT_COLUMN_SUFFIX,
                ),
            )

            # We will have duplicate chat events because multiple messages can have
            # the same participants, and we create ENTERED chat events for each
            # participant.
            chat_event_df_deduplicated = run_remove_duplicates(
                source_frame=chat_event_df_normalized,
                params=ParamsRemoveDuplicate(
                    keep="first",
                    drop_duplicates_subset=[
                        ChatEventColumns.ROOM_ID,
                        ChatEventColumns.EVENT_TYPE,
                        MessageColumns.IDENTIFIERS_FROM_ID,
                    ],
                    reset_index=True,
                ),
            )

            # Filter chat event records in case they do not have any meaningful information.
            # This would most likely be caused by the flow not linking to the chat metadata file
            chat_event_df_deduplicated = run_get_rows_by_condition(
                params=GetRowsByConditionParams(
                    query=f"`{ChatEventColumns.TIMESTAMPS_TIMESTAMP_START}`.notnull()",
                    skip_on_empty=False,
                ),
                source_frame=chat_event_df_deduplicated,
            )

            list_of_chat_event_dfs.append(chat_event_df_deduplicated)

    message_update_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in messages_to_update_ndjson_paths_list],
        task=message_update_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": Message.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )

    message_create_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in messages_to_create_ndjson_paths_list],
        task=message_create_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": Message.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )

    # Post-process chat events to make sure we don't have duplicates
    chat_event_ndjson_paths_list = chat_event_processing(
        list_of_chat_event_dfs=list_of_chat_event_dfs,
        bucket=tenant_bucket_with_cloud_prefix,
        streamed=streamed,
        cloud_provider=cloud_provider,
        aries_task_input=aries_task_input,
        tenant=tenant,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        batch_size=message_batch_size,
    )
    chat_event_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in chat_event_ndjson_paths_list],
        task=chat_event_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": ChatEvent.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )
    finish_flow(
        result_path=result_path,
        result_data={
            f"{MetaModel.MESSAGE}_create": message_create_output,
            f"{MetaModel.MESSAGE}_update": message_update_output,
            MetaModel.CHAT_EVENT: chat_event_output,
        },
    )


def chat_event_processing(
    list_of_chat_event_dfs: List[pd.DataFrame],
    bucket: str,
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    tenant: str,
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str],
    audit_path: Optional[str],
    batch_size: int,
) -> list:
    """This function  creates a batch of Chat ndjson files (or None if there
    are no chat events after removing the chat events from updates in the
    calling function) after running LinkParticipants.

    :param list_of_chat_event_dfs: List with batches of chat events dfs
    the metadata.messageId column
    :param bucket: Bucket
    :param streamed: Streamed
    :param cloud_provider: Cloud Provider
    :param tenant: Tenant
    :param aries_task_input: Aries task input
    :param app_metrics_path: App metrics path
    :param audit_path: Audit path
    :param batch_size: Batch Size used to create chat event batches
    """
    chat_events_ndjson_paths_list = []
    if not list_of_chat_event_dfs:
        return []

    try:
        chat_events_df_list = run_merge_and_split_dataframes(
            source_frame_list=list_of_chat_event_dfs,
            params=ParamsMergeAndSplitDataframes(max_chunk_size=batch_size),
        )
    except SkipIfResultEmpty:
        logger.warning("No chat events")
        return  # type: ignore[return-value]

    for idx, chat_event_df in enumerate(chat_events_df_list):
        # Get the &id, this is used for the audit keys
        chat_event_df_with_amp_id = run_generate_record_identifiers_for_df(
            source_frame=chat_event_df,
            params=ParamsGenerateRecordFileIdentifiersForDf(
                data_model=MetaModel.CHAT_EVENT,
                target_record_identifier_col="record_identifier",
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
        )

        chat_event_participants_df = run_link_participants(
            source_frame=chat_event_df_with_amp_id,
            tenant=tenant,
            params=ParamsLinkParticipants(all_ids=MessageColumns.IDENTIFIERS_ALL_IDS),
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            data_models=[Message],
            record_identifier_column="record_identifier",
        )

        final_chat_event_df = run_frame_concatenator(
            chat_event_df_deduplicated=chat_event_df,
            chat_event_participants_df=chat_event_participants_df,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        chat_event_ndjson_path = create_ndjson_path(
            tenant_bucket=bucket,
            aries_task_input=aries_task_input,
            model=MetaModel.CHAT_EVENT,
            suffix=f"{idx}",
        )

        run_write_ndjson(
            source_serializer_result=final_chat_event_df,
            output_filepath=chat_event_ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
        chat_events_ndjson_paths_list.append(chat_event_ndjson_path)

    return chat_events_ndjson_paths_list


@serializer
def _assign_xml_file_url(xmpp_df: pd.DataFrame, xml_file_url: str):
    """Assigns the XML file URL to the XMPP df so to the XML and XMPP files can
    be joined in the calling function."""
    xmpp_df[FileUrlColumns.XML_FILE_URL] = xml_file_url
    return xmpp_df
