# mypy: disable-error-code="attr-defined, assignment"
import logging
import os
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.feeds.message.refinitiv_fxt_chat.pre_process_refinitiv_fxt_messages import (  # noqa: E501
    run_pre_process_refinitiv_fxt_messages,
)
from aries_se_comms_tasks.feeds.message.refinitiv_fxt_chat.static import (
    FLOW_NAME,
    REFINITIV_FXT_CHAT_SOURCE_SCHEMA,
    RefinitivFxtSourceColumns,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import Params as ParamsBatchProducer
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import Params as ParamsCsvFileSplitter
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.read.xls_to_csv_converter import Params as ParamsXlsToCsvConverter
from aries_se_core_tasks.io.read.xls_to_csv_converter import run_xls_to_csv_converter
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_task_link.models import AriesTaskInput
from integration_text_comms_tasks.message.refinitiv_fxt_chat_transform.input_schema import (
    RefinitivFxtChatAriesTaskInput,
)
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Message
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional

logger = logging.getLogger(__name__)

MESSAGES_BATCH_SIZE = int(os.environ.get("MESSAGE_BATCH_SIZE", 500))


def refinitiv_fxt_chat_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    # Init output paths with null
    messages_ndjson_path_list: List[str] = []

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Parse and validate AriesTaskInput parameters
    task_transform_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=RefinitivFxtChatAriesTaskInput
    )

    # Get all the dynamic task definitions from workflow input
    # in this case it is required, "message"
    message_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "message"
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Determine the Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    # Download remote file from the cloud provider
    local_file_path: str = run_download_file(file_url=task_transform_input.file_uri)

    # Convert the xls file to csv
    csv_file_path = run_xls_to_csv_converter(
        file_path=local_file_path,
        params=ParamsXlsToCsvConverter(
            source_date_columns=[RefinitivFxtSourceColumns.TIME_OF_DEAL]
        ),
    )

    # Read downloaded file and split it into chunks
    csv_chunks_paths = run_csv_file_splitter(
        params=ParamsCsvFileSplitter(chunksize=MESSAGES_BATCH_SIZE, audit_input_rows=True),
        csv_path=csv_file_path.as_posix(),
        realm=realm,
        sources_dir="",
        streamed=streamed,
    )

    batch_number = 0

    for csv_chunk_path in csv_chunks_paths:
        # For each of the chunks, it adds missing columns, and converts columns to the right
        # datatype as per the REFINITIV_FXT_SOURCE_SCHEMA
        batch_producer_result = run_batch_producer(
            params=ParamsBatchProducer(
                source_schema=REFINITIV_FXT_CHAT_SOURCE_SCHEMA, remove_unknown_columns=True
            ),
            file_splitter_result=csv_chunk_path,
            return_dataframe=True,
            streamed=streamed,
        )

        os.remove(csv_chunk_path.path)

        # Process Message into multiple messages
        pre_processed_messages_df = run_pre_process_refinitiv_fxt_messages(
            source_frame=batch_producer_result
        )

        primary_mappings_result = run_get_primary_transformations(
            source_frame=pre_processed_messages_df,
            flow=FLOW_NAME,
            realm=realm,
            tenant=tenant,
            source_file_uri=task_transform_input.file_uri,
        )

        ndjson_path = _process(
            data_models=[Message],
            meta_model=MetaModel.MESSAGE,
            streamed=streamed,
            tenant=tenant,
            bucket=tenant_bucket_with_cloud_prefix,
            batch_num=batch_number,
            source_frame=primary_mappings_result,
            target_participants_column=MessageColumns.PARTICIPANTS,
            aries_task_input=aries_task_input,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        if ndjson_path:
            messages_ndjson_path_list.append(ndjson_path)
            batch_number += 1

    messages_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in messages_ndjson_path_list],
        task=message_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.message:Message",
        },
        workflow=aries_task_input.workflow,
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.MESSAGE: messages_output,
        },
    )


def _process(
    data_models: List[SteelEyeSchemaBaseModelES8],
    meta_model: MetaModel,
    streamed: bool,
    tenant: str,
    bucket: str,
    batch_num: int,
    source_frame: pd.DataFrame,
    target_participants_column: str,
    aries_task_input: AriesTaskInput,
    cloud_provider: CloudProviderEnum,
    app_metrics_path: Optional[str],
    audit_path: Optional[str],
) -> str | None:
    # Determine audit id of each individual message
    frame_with_amp_id = run_generate_record_identifiers_for_df(
        source_frame=source_frame,
        params=GenerateRecordFileIdentifiersForDfParams(
            data_model=meta_model, target_record_identifier_col="record_identifier"
        ),
        streamed=streamed,
        cloud_provider=cloud_provider,
    )

    # Enrichment: Link participants based on the created participant identifiers
    participants_result = run_link_participants(
        tenant=tenant,
        source_frame=frame_with_amp_id,
        params=LinkParticipantsParams(target_participants_column=target_participants_column),
        streamed=streamed,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        data_models=data_models,
        record_identifier_column="record_identifier",
    )

    final_result = run_frame_concatenator(
        participants_result=participants_result,
        primary_mappings_result=source_frame,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
    )

    final_records_length: int = final_result.shape[0]

    if final_records_length > 0:
        # Create the appropriate path where the ndjson result is to be uploaded
        ndjson_path = create_ndjson_path(
            tenant_bucket=bucket,
            aries_task_input=aries_task_input,
            model=meta_model,
            suffix=f"{batch_num}",
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=final_result,
            output_filepath=ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        return ndjson_path

    return None
