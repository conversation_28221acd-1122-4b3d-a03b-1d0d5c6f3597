import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables
from typing import List

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["TENANT_DB_PG_URL"] = "sample_db_url"


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="info_barrier_project",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/"
            "evented/info_barrier_project/sample_projects.xlsx",
            dynamic_tasks=dict(
                info_barrier_project=dict(
                    name="elastic_ingestion",
                    task_reference_name="elastic_ingestion_ref",
                    type="SUB_WORKFLOW",
                ),
                account_person_update=dict(
                    name="elastic_ingestion_person_update",
                    task_reference_name="elastic_ingestion_person_update_ref",
                    type="SUB_WORKFLOW",
                ),
            ),
        )
    )
    task = TaskFieldSet(name="info_barrier_project_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def account_person_non_empty_scroll_result() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "f51b93c8-7678-4c47-83e5-259d5620cc55",
                "&uniqueProps": [],
                "&ancestor": "AccountPerson:f51b93c8-7678-4c47-83e5-259d5620cc55:*************",
                "retailOrProfessional": "N/A",
                "&key": "AccountPerson:f51b93c8-7678-4c47-83e5-259d5620cc55:*************",
                "&model": "AccountPerson",
                "&version": 2,
                "name": "salman khan",
                "&hash": "dbefa6ecbe8760f31021bc8bd39a212d9f56c9578d4a1260953e0e63efd4f1fd",
                "&timestamp": *************,
                "&user": "<EMAIL>",
                "personalDetails.firstName": "salman",
                "personalDetails.lastName": "khan",
                "personalDetails.middleName": pd.NA,
                "officialIdentifiers.employeeId": "SK",
                "officialIdentifiers.passports": pd.NA,
                "sourceKey": pd.NA,
                "monitoring": pd.NA,
                "sourceIndex": pd.NA,
                "employeeStatus": pd.NA,
                "uniqueIds": pd.NA,
                "structure": pd.NA,
                "communications": pd.NA,
                "counterparty": pd.NA,
                "location": pd.NA,
                "personalDetails.nationality": pd.NA,
                "personalDetails.dob": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.traderIds": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.fcaNo": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
            },
            {
                "&id": "a4ffc256-bb0f-4ba6-aef2-dede4e2f5919",
                "&uniqueProps": [],
                "&ancestor": "AccountPerson:a4ffc256-bb0f-4ba6-aef2-dede4e2f5919:*************",
                "retailOrProfessional": "N/A",
                "&key": "AccountPerson:a4ffc256-bb0f-4ba6-aef2-dede4e2f5919:*************",
                "&model": "AccountPerson",
                "&version": 2,
                "name": "shah rukh khan",
                "&hash": "b2e34a95a0140a40403b55064b0c18dbb0c3f2ac9b42b6003251ec2b999a0007",
                "&timestamp": *************,
                "&user": "<EMAIL>",
                "personalDetails.firstName": "shah",
                "personalDetails.lastName": "khan",
                "personalDetails.middleName": "rukh",
                "officialIdentifiers.employeeId": "srk",
                "officialIdentifiers.passports": [
                    {"id": "sadlkjn 3e2", "label": "AF"},
                    {"id": "m asdf a", "label": "AF"},
                ],
                "sourceKey": pd.NA,
                "monitoring": pd.NA,
                "sourceIndex": pd.NA,
                "employeeStatus": pd.NA,
                "uniqueIds": pd.NA,
                "structure": pd.NA,
                "communications": pd.NA,
                "counterparty": pd.NA,
                "location": pd.NA,
                "personalDetails.nationality": pd.NA,
                "personalDetails.dob": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.traderIds": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.fcaNo": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
            },
            {
                "&id": "f40b9a2b-abcd-4ba9-a47e-dc951fd6108a",
                "&uniqueProps": ["jb:jb"],
                "&ancestor": "AccountPerson:f40b9a2b-abcd-4ba9-a47e-dc951fd6108a:*************",
                "retailOrProfessional": "N/A",
                "&key": "AccountPerson:f40b9a2b-abcd-4ba9-a47e-dc951fd6108a:*************",
                "&model": "AccountPerson",
                "&version": 4,
                "name": "James Bond",
                "&hash": "322d83acf6c013b9423a84040c0a3e4d7eed331bc44690fe7a5c25e5426b46c4",
                "&timestamp": *************,
                "&user": "<EMAIL>",
                "personalDetails.firstName": "James",
                "personalDetails.lastName": "Bond",
                "personalDetails.middleName": None,
                "officialIdentifiers.employeeId": "007",
                "officialIdentifiers.passports": None,
                "sourceKey": pd.NA,
                "monitoring": pd.NA,
                "sourceIndex": pd.NA,
                "employeeStatus": pd.NA,
                "uniqueIds": ["jb:jb"],
                "structure": pd.NA,
                "communications": pd.NA,
                "counterparty": pd.NA,
                "location": pd.NA,
                "personalDetails.nationality": ["GB"],
                "personalDetails.dob": "1972-02-03",
                "sinkIdentifiers.tradeFileIdentifiers": [{"id": "JB", "label": "JB"}],
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "officialIdentifiers.concatId": "GB19720203JAMESBOND#",
                "officialIdentifiers.mifirIdSubType": "NIDN",
                "officialIdentifiers.traderIds": pd.NA,
                "officialIdentifiers.mifirIdType": "N",
                "officialIdentifiers.clientMandate": pd.NA,
                "officialIdentifiers.mifirId": "GB*********",
                "officialIdentifiers.nationalIds": [
                    {"id": "*********", "label": "GB - National Insurance"}
                ],
                "officialIdentifiers.fcaNo": pd.NA,
                "officialIdentifiers.branchCountry": "GB",
            },
        ]
    )


@pytest.fixture()
def info_barrier_related_project_non_empty_scroll_result() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&hash": "a94297155e0ed1a0c8a44a90ee3211bfd91109d10dc382049c6c3d84eca209ac",
                "&id": "4",
                "&key": "InfoBarrierProject:2:1728978852800",
                "&model": "InfoBarrierProject",
                "&timestamp": 1728978852800,
                "&user": "<EMAIL>",
                "&version": 7,
                "codeNames": ["alpha1"],
                "createdBy": "test",
                "dateTimeCreated": "2024-10-14T11:22:08.003122",
                "dateTimeEnd": "2024-10-23T00:00:00+00:00",
                "dateTimeStart": "2024-10-09T00:00:00+00:00",
                "description": "testing proj",
                "employeeIds": ["tony-stark"],
                "internalId": "2",
                "lexicaBehaviourName": "2",
                "name": "testrailproject",
                "provenance": "USER_CREATED",
                "sensitivity": "MEDIUM",
                "status": "ACTIVE",
                "types": ["COMPANY ACQUISITION"],
                "sourceKey": None,
                "customTag2": None,
                "customTag1": None,
                "customTag4": None,
                "customTag3": None,
                "relatedProjectInternalIDs": None,
                "updatedBy": "1433",
                "&ancestor": "InfoBarrierProject:2:*************",
                "dateTimeUpdated": "2024-10-15T07:54:12.765047",
                "typeCustom": None,
                "monitoringDetails.monitoringPermanency": "INDEFINITE",
                "monitoringDetails.monitoringDateTimeStart": None,
                "monitoringDetails.monitoringDateTimeEnd": None,
            },
            {
                "&hash": "a94297155e443ee3211bfd91109d10dc382049c6c3d84eca209ac",
                "&id": "4",
                "&key": "InfoBarrierProject:4:1728978852800",
                "&model": "InfoBarrierProject",
                "&timestamp": 1728978852800,
                "&user": "<EMAIL>",
                "&version": 7,
                "codeNames": ["alpha1"],
                "createdBy": "test",
                "dateTimeCreated": "2024-10-14T11:22:08.003122",
                "dateTimeEnd": "2024-10-23T00:00:00+00:00",
                "dateTimeStart": "2024-10-09T00:00:00+00:00",
                "description": "testing proj",
                "employeeIds": ["tony-stark"],
                "internalId": "4",
                "lexicaBehaviourName": "4",
                "name": "testrailproject",
                "provenance": "USER_CREATED",
                "sensitivity": "MEDIUM",
                "status": "ACTIVE",
                "types": ["COMPANY ACQUISITION"],
                "sourceKey": None,
                "customTag2": None,
                "customTag1": None,
                "customTag4": None,
                "customTag3": None,
                "relatedProjectInternalIDs": None,
                "updatedBy": "414",
                "&ancestor": "InfoBarrierProject:4:*************",
                "dateTimeUpdated": "2024-10-15T07:54:12.765047",
                "typeCustom": None,
                "monitoringDetails.monitoringPermanency": "INDEFINITE",
                "monitoringDetails.monitoringDateTimeStart": None,
                "monitoringDetails.monitoringDateTimeEnd": None,
            },
        ]
    )


@pytest.fixture()
def info_barrier_related_project_empty_scroll_result() -> pd.DataFrame:
    return pd.DataFrame([])


@pytest.fixture()
def account_person_empty_scroll_result() -> pd.DataFrame:
    return pd.DataFrame([])


@pytest.fixture()
def related_projects_and_account_person_non_empty_scroll(
    account_person_non_empty_scroll_result: pd.DataFrame,
    info_barrier_related_project_non_empty_scroll_result: pd.DataFrame,
) -> List[pd.DataFrame]:
    return [
        info_barrier_related_project_non_empty_scroll_result,
        account_person_non_empty_scroll_result,
    ]


@pytest.fixture()
def related_projects_non_empty_account_person_empty_scroll(
    account_person_empty_scroll_result: pd.DataFrame,
    info_barrier_related_project_non_empty_scroll_result: pd.DataFrame,
) -> List[pd.DataFrame]:
    return [
        info_barrier_related_project_non_empty_scroll_result,
        account_person_empty_scroll_result,
    ]


@pytest.fixture()
def audit_end_to_end_some_employees_found() -> dict:
    return {
        "input_records": {
            "7": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 1,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Missing mandatory columns: Project Description, Project Name, "
                        "Project Employee ID/s. Missing mandatory columns when "
                        "MONITORING_PERMANENCY='Temporary': Monitoring Start"
                    ],
                }
            },
            "Missing project ID": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 2,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Missing mandatory columns: Project ID, "
                        "Monitoring Permanency, Project Employee ID/s"
                    ],
                }
            },
            "1": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Removed related project ID(s) (['5']) as the"
                        " corresponding project(s) were not in the database",
                        "Removed Employee ID(s) (['1']) as the corresponding"
                        " employee(s) were not in the database",
                        "Removed project row as all employee IDs (['1']) were not in the database",
                    ],
                }
            },
            "6": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Removed related project ID(s) (['3']) as the corresponding project(s)"
                        " were not in the database"
                    ],
                }
            },
            "2": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Removed Employee ID(s) (['2']) as the corresponding employee(s)"
                        " were not in the database",
                        "Removed project row as all employee IDs (['2']) were not in the database",
                    ],
                }
            },
            "5": {
                "InfoBarrierProject": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [
                        "Removed Employee ID(s) (['1', '2']) as the corresponding employee(s)"
                        " were not in the database"
                    ],
                }
            },
        },
        "workflow_status": [],
    }
