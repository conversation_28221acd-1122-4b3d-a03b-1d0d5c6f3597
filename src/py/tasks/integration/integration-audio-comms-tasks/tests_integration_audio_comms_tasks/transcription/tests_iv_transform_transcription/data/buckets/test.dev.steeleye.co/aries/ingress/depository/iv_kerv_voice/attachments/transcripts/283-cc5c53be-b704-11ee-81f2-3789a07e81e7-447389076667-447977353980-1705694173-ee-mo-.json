{"allText": "No. Hello. Hi. Uh, where are you for the pick up? Right, in front of <PERSON>. Oh, okay. Because <PERSON>, I'm just here but I can't see you. I'm second. If you turn to turn to your back. Can turn to your back and back with you. Ah. Do you sense that? Yeah, yeah. They're, uh, in front I'll of you. go Rightly. back back. Oh, sorry. Okay. ", "attachment": [], "body": "<p style='font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>erd<PERSON>;font-size:11pt;'>Channel 0: No. </p><p style='font-family:Calib<PERSON>,Arial,Verdana;font-size:11pt;'>Channel 1: Hello. Hi. Uh, where are you for the pick up? </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 0: Right, in front of Costa. </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 1: Oh, okay. Because I, I'm just here but I can't see you. I'm second. </p><p style='font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>erdana;font-size:11pt;'>Channel 0: If you turn to turn to your back. Can turn to your back and back with you. </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 1: Ah. </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 0: Do you sense that? Yeah, yeah. They're, uh, in front </p><p style='font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>;font-size:11pt;'>Channel 1: I'll </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 0: of you. </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 1: go </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 0: Rightly. </p><p style='font-family:Calibri,Arial,Verdana;font-size:11pt;'>Channel 1: back back. Oh, sorry. Okay. </p>", "docID": 22522853, "duration": 23, "from": "", "id": 22522853, "itemQualityScore": {}, "localFilename": "/uksivmprd1/dcme1/ABC1234/2024-01-21/283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-.wav", "peopleCc": [], "peopleFrom": [], "peopleTo": [], "recordings": [{"channelNumber": 0, "id": 22698686, "mimeType": "audio/wav", "modelLattices": [{"modelId": 13, "wordLattice": "[ い 0.4846158383139352 <eps> 0.4040677099716892 お 0.11131645171437558 ]\n[ た 0.4610726643598616 <eps> 0.4156672695816294 お 0.11107069833280905 て 0.00810003145643284 あ 0.003932054105064486 な 0.00015728216420257942 ]\n[ ん 0.5712979710600817 <eps> 0.3990740012582573 お 0.019896193771626297 て 0.006025872916011324 う 0.0037059609940232776 ]\n[ ま 0.9922931739540736 まあ 0.007706826045926392 ]\n[ 一 0.9619180559924505 <eps> 0.038081944007549544 ]\n[ つ 0.7496264548600189 <eps> 0.2244023277760302 本 0.025971217363950926 ]\n[ と 0.7203818024536017 は 0.11179812834224599 <eps> 0.08023356401384082 行 0.03450377477194086 とこ 0.026787118590751807 こ 0.021960522176785152 本 0.004335089650833596 ]\n[ し 0.9871126926706512 <eps> 0.012887307329348853 ]\n[ た 0.948381959735766 て 0.028664674425920102 <eps> 0.021763919471531928 行 0.001189446366782007 ]\n[ い 1.0 ]\n[ て 1.0 ]\n[ い 1.0 ]\n[ て 1.0 ]\n[ ま 1.0 ]\n[ の 1.0 ]\n[ て 1.0 ]\n[ つ 1.0 ]\n[ ちゃ 0.7156436772569991 <eps> 0.28435632274300093 ]\n[ ラ 0.7156436772569991 <eps> 0.28435632274300093 ]\n[ ト 0.7156436772569991 <eps> 0.28435632274300093 ]\n[ を 0.7156436772569991 や 0.28435632274300093 ]\n[ バ 0.7282754010695187 バー 0.2717245989304813 ]\n[ ク 1.0 ]\n[ ト 1.0 ]\n[ ル 0.9960089650833596 <eps> 0.003991034916640453 ]\n[ と 0.9762798836111984 ト 0.02372011638880151 ]\n[ キ 1.0 ]\n[ ト 1.0 ]\n[ と 1.0 ]\n[ 言 1.0 ]\n[ ば 1.0 ]\n[ ば 1.0 ]\n[ よ 0.34204938659955963 <eps> 0.31013093740169867 言 0.1934079112928594 ビ 0.11773553004089336 行 0.021056149732620322 び 0.01562008493236867 ]\n[ 出 1.0 ]\n[ た 1.0 ]\n[ け 0.9980732934885184 る 0.001926706511481598 ]\n[ ん 0.9999901698647373 <eps> 9.830135262661214e-06 ]\n[ や 0.8417938030827304 じゃ 0.10745320855614973 じ 0.05074315822585719 か 9.830135262661214e-06 ]\n[ や 1.0 ]\n[ が 0.9983878578169235 で 0.0016121421830764392 ]\n[ さ 0.8334971689210443 <eps> 0.16650283107895564 ]\n[ は 0.30746697074551743 き 0.19669117647058823 <eps> 0.1436772569990563 ま 0.10662747719408619 わ 0.07969290657439447 け 0.04631959735765964 っ 0.03826871657754011 り 0.025853255740798994 ワ 0.023415382195659012 キ 0.015256369927650204 ー 0.005662157911292859 切 0.004335089650833596 そ 0.0035486788298206983 きっ 0.0031849638251022334 ]\ncluster0: 59.250000000000014 113.50000000000003\ncluster1: 113.50000000000003 167.75000000000003\ncluster2: 167.75000000000006 222.00000000000006\ncluster3: 580.0000000000001 582.0000000000001\ncluster4: 604.0000000000001 654.0000000000001\ncluster5: 666.0 678.0\ncluster6: 678.0 728.0\ncluster7: 728.0 740.0\ncluster8: 740.0 752.0\ncluster9: 1071.0 1121.0\ncluster10: 1147.0 1149.0\ncluster11: 1163.0 1207.0\ncluster12: 1297.0 1299.0\ncluster13: 1298.9999999999998 1300.9999999999998\ncluster14: 1300.9999999999998 1344.9999999999998\ncluster15: 1344.9999999999998 1346.9999999999998\ncluster16: 1347.0 1377.0\ncluster17: 1393.0 1451.0\ncluster18: 1479.0 1501.0\ncluster19: 1517.0 1519.0\ncluster20: 1529.0 1609.0\ncluster21: 1609.0 1667.0\ncluster22: 1667.0000000000002 1725.0000000000002\ncluster23: 1725.0 1747.0\ncluster24: 1747.0 1749.0\ncluster25: 1748.9999999999998 1828.9999999999998\ncluster26: 1829.0 1876.25\ncluster27: 1876.25 1923.5\ncluster28: 1923.5 1970.75\ncluster29: 1970.75 2050.75\ncluster30: 2050.75 2098.0\ncluster31: 2098.0 2145.25\ncluster32: 2145.25 2192.5\ncluster33: 2192.5 2194.5\ncluster34: 2194.5 2196.5\ncluster35: 2196.5 2198.5\ncluster36: 2198.5 2228.5\ncluster37: 2228.5 2258.5\ncluster38: 2258.5 2288.5\ncluster39: 2288.5 2318.5\ncluster40: 2318.5 2348.5\ncluster41: 2348.5000000000005 2378.5000000000005\n"}, {"modelId": 17, "wordLattice": "[ no 0.16307211387228687 fo 0.03901580685750236 ne 0.037787039949669705 ono 0.03657793331236238 now 0.03538848694558037 nour 0.034218700849323685 n 0.033068575023592325 four 0.03193810946838629 wo 0.030827304183705568 one 0.029736159169550174 fe 0.028664674425920102 nzo 0.02761284995281535 tho 0.02658068575023592 ner 0.02556818181818182 noe 0.024575338156653036 nove 0.023602154765649575 eo 0.022648631645171436 flo 0.021714768795218622 bo 0.02080056621579113 two 0.019906023906888958 nu 0.01903114186851211 foll 0.018175920100660586 na 0.017340358603334383 nwo 0.0165244573765335 nop 0.015728216420257943 nur 0.014951635734507706 noo 0.014194715319282793 f 0.013457455174583202 nor 0.012739855300408933 on 0.012041915696759987 lo 0.011363636363636364 te 0.010705017301038061 nob 0.010066058508965083 fer 0.009446759987417426 noll 0.008847121736395092 oo 0.008267143755898081 nero 0.007706826045926392 nox 0.007166168606480026 nin 0.00664517143755898 heo 0.006143834539163259 to 0.005662157911292859 neur 0.005200141553947783 po 0.004757785467128028 neo 0.004335089650833596 zo 0.003932054105064486 non 0.0035486788298206983 ao 0.0031849638251022334 foo 0.002840909090909091 ni 0.0025165146272412707 onlo 0.002211780434098773 nlo 0.001926706511481598 moo 0.0014155394778232148 hoo 0.001189446366782007 o 0.0009830135262661214 night 0.0007962409562755583 goo 0.0006291286568103177 foe 0.0004816766278703995 <eps> 0.00024575338156653036 er 0.00015728216420257942 new 8.847121736395092e-05 oner 3.9320541050644855e-05 nly 9.830135262661214e-06 ]\n[ right 1.0 ]\n[ in 1.0 ]\n[ front 1.0 ]\n[ of 1.0 ]\n[ costa 0.2174327618748034 <eps> 0.19884397609311105 the 0.13094723183391002 coster 0.04220077068260459 cluster 0.030827304183705568 cooster 0.029736159169550174 coaster 0.02761284995281535 cster 0.02658068575023592 coloster 0.024575338156653036 closter 0.023602154765649575 usa 0.022648631645171436 colosa 0.021714768795218622 coosta 0.020810396351053792 koster 0.019906023906888958 kosa 0.01903114186851211 colster 0.017340358603334383 costter 0.0165244573765335 coolster 0.015728216420257943 poster 0.014951635734507706 custer 0.013457455174583202 a 0.012739855300408933 coolsa 0.012041915696759987 colosta 0.008847121736395092 colsta 0.007706826045926392 losa 0.006143834539163259 coolsta 0.003932054105064486 clster 0.0035486788298206983 loster 0.002840909090909091 cokoster 0.0025165146272412707 clsa 0.002211780434098773 lsa 0.001661292859389745 luster 0.0009830135262661214 cusa 0.0003538848694558037 ]\n[ if 1.0 ]\n[ you 1.0 ]\n[ turn 0.69603255740799 turned 0.3039674425920101 ]\n[ to 1.0 ]\n[ turn 1.0 ]\n[ to 1.0 ]\n[ your 1.0 ]\n[ back 1.0 ]\n[ can 0.5922558194400755 <eps> 0.2941864580056622 to 0.11083477508650519 you 0.0027229474677571562 ]\n[ turn 1.0 ]\n[ to 1.0 ]\n[ your 1.0 ]\n[ back 1.0 ]\n[ and 0.9962055677886128 on 0.003794432211387229 ]\n[ back 1.0 ]\n[ with 0.5378263604907203 of 0.3173954073608053 to 0.1446897609311104 <eps> 8.847121736395092e-05 ]\n[ you 1.0 ]\n[ do 0.9737731991192199 can 0.02622680088078012 ]\n[ you 1.0 ]\n[ sense 0.5241133217993079 <eps> 0.2891829191569676 send 0.09736748977665932 say 0.05558941491034917 spend 0.029814800251651462 sending 0.003932054105064486 ]\n[ that 0.5417485844605222 <eps> 0.21486709657124883 send 0.08854002831078955 spend 0.05104789241899969 sending 0.04644738911607424 say 0.03284248191255112 about 0.023090987731991194 spending 0.0014155394778232148 ]\n[ yeah 1.0 ]\n[ yeah 1.0 ]\n[ they're 1.0 ]\n[ uh 0.9964513211701793 ah 0.0035486788298206983 ]\n[ in 1.0 ]\n[ front 1.0 ]\n[ of 1.0 ]\n[ you 0.8819793960364894 me 0.11802060396351054 ]\n[ rightly 0.6580587448883297 <eps> 0.34194125511167034 ]\ncluster0: 101.0 105.0\ncluster1: 574.0 578.0\ncluster2: 606.0 610.0\ncluster3: 618.0000000000001 622.0000000000001\ncluster4: 646.0000000000001 650.0000000000001\ncluster5: 658.0 662.0\ncluster6: 1261.0 1265.0\ncluster7: 1280.9999999999998 1284.9999999999998\ncluster8: 1341.0 1345.0\ncluster9: 1369.0 1373.0\ncluster10: 1373.0 1377.0\ncluster11: 1377.0 1381.0\ncluster12: 1380.9999999999998 1388.9999999999998\ncluster13: 1393.0 1397.0\ncluster14: 1573.0 1577.0\ncluster15: 1577.0 1581.0\ncluster16: 1580.9999999999998 1584.9999999999998\ncluster17: 1584.9999999999998 1592.9999999999998\ncluster18: 1592.9999999999998 1596.9999999999998\ncluster19: 1597.0 1601.0\ncluster20: 1609.0 1613.0\ncluster21: 1637.0 1641.0\ncluster22: 1649.0000000000002 1653.0000000000002\ncluster23: 1750.0 1754.0\ncluster24: 1766.0 1770.0\ncluster25: 1773.9999999999998 1777.9999999999998\ncluster26: 1850.0 1858.0\ncluster27: 1873.9999999999998 1893.9999999999998\ncluster28: 1898.0 1902.0\ncluster29: 1902.0 1906.0\ncluster30: 1918.0 1922.0\ncluster31: 1946.0 1950.0\ncluster32: 1954.0 1962.0\ncluster33: 1978.0 1982.0\ncluster34: 1989.9999999999998 1993.9999999999998\ncluster35: 2014.0 2018.0\n"}, {"modelId": 18, "wordLattice": "[ mike 0.7380072349795533 <eps> 0.07671437558980812 mikefo 0.047853098458634793 mikef 0.0413357187794904 mikeo 0.04043134633532557 mikepo 0.029736159169550174 mikee 0.01903114186851211 mikevo 0.0031849638251022334 mikey 0.002211780434098773 mikeoo 0.0009830135262661214 mikep 0.0003538848694558037 mikefe 0.00015728216420257942 ]\n[ to 0.2811222082415854 <eps> 0.2750865051903114 ⁇ 0.0481283422459893 f 0.036931818181818184 o 0.03422853098458635 pho 0.03193810946838629 fo 0.030827304183705568 photo 0.028664674425920102 p 0.024899732620320855 fto 0.024575338156653036 folo 0.023602154765649575 bo 0.021714768795218622 wo 0.02080056621579113 fe 0.019906023906888958 fol 0.017340358603334383 po 0.014951635734507706 feo 0.009446759987417426 proo 0.008847121736395092 ft 0.007166168606480026 au 0.00664517143755898 poto 0.006143834539163259 fanto 0.005200141553947783 fro 0.004757785467128028 biking 0.004335089650833596 pe 0.0035486788298206983 proto 0.002840909090909091 t 0.0025165146272412707 toe 0.001926706511481598 foo 0.001189446366782007 ph 0.0006291286568103177 tok 8.847121736395092e-05 ]\n[ costa 0.9208870714061026 consta 0.045778939918213274 costta 0.018175920100660586 costas 0.013457455174583202 <eps> 0.00170061340044039 ]\n[ it 1.0 ]\n[ is 1.0 ]\n[ trybacker 0.6732561340044039 <eps> 0.3267438659955961 ]\n[ tokyok 0.4889509279647688 <eps> 0.2250314564328405 tokerb 0.16160742371815037 tokerbk 0.03814092481912551 tokerba 0.03567356086819755 tokyobk 0.025558351682919155 tokerbac 0.018333202264863166 tokba 0.003932054105064486 tokerback 0.0025165146272412707 tokyback 0.00024575338156653036 tokyobac 9.830135262661214e-06 ]\n[ comme 0.8685809216734822 <eps> 0.09054537590437245 bac 0.04087370242214533 ]\n[ bkovie 0.602046634161686 <eps> 0.3979533658383139 ]\n[ qui 1.0 ]\n[ est 1.0 ]\n[ contée 0.6791738754325259 altérée 0.1158776344762504 <eps> 0.07571170179301667 é 0.06839808115759673 calé 0.038278546712802765 calée 0.01125550487574709 consé 0.006143834539163259 tenté 0.0025165146272412707 alé 0.001926706511481598 tiré 0.0006291286568103177 altéré 8.847121736395092e-05 ]\n[ par 0.7609507706826046 <eps> 0.23904922931739542 ]\n[ il 1.0 ]\n[ ya 0.5149221453287197 y 0.4850778546712803 ]\n[ déjà 1.0 ]\n[ une 1.0 ]\n[ fe 0.5327245202893992 forte 0.2755976722239698 fortune 0.11506173324944952 f 0.05185396351053791 fune 0.008512897137464611 fea 0.007706826045926392 fant 0.003932054105064486 fente 0.0031849638251022334 fil 0.0014155394778232148 pointe 9.830135262661214e-06 ]\ncluster0: 566.0 574.0\ncluster1: 670.0 678.0\ncluster2: 678.0 686.0\ncluster3: 1448.9999999999998 1456.9999999999998\ncluster4: 1609.0 1617.0\ncluster5: 1617.0000000000002 1625.0000000000002\ncluster6: 1625.0 1633.0\ncluster7: 1632.9999999999998 1640.9999999999998\ncluster8: 1640.9999999999995 1648.9999999999995\ncluster9: 1734.0 1742.0\ncluster10: 1757.9999999999998 1765.9999999999998\ncluster11: 1773.9999999999998 1781.9999999999998\ncluster12: 1830.0 1838.0\ncluster13: 1854.0 1862.0\ncluster14: 1878.0 1894.0\ncluster15: 1918.0 1926.0\ncluster16: 1942.0000000000002 1950.0000000000002\ncluster17: 1957.9999999999998 1965.9999999999998\n"}, {"modelId": 19, "wordLattice": "[ a 0.6565656565656566 <eps> 0.2398989898989899 i 0.10353535353535354 ]\n[ lot 0.9242424242424242 <eps> 0.07323232323232323 alot 0.0025252525252525255 ]\n[ but 1.0 ]\n[ you 1.0 ]\n[ port 0.8005050505050505 <eps> 0.1994949494949495 ]\n[ of 1.0 ]\n[ costa 0.9242424242424242 coster 0.04292929292929293 costar 0.03282828282828283 ]\n[ the 1.0 ]\n[ bus 1.0 ]\n[ i 1.0 ]\n[ do 1.0 ]\n[ on 1.0 ]\n[ of 1.0 ]\n[ this 1.0 ]\n[ i 1.0 ]\n[ turn 1.0 ]\n[ acrobat 1.0 ]\n[ tertiary 0.7373737373737373 tertatolol 0.18686868686868688 tortola 0.04040404040404041 torta 0.022727272727272728 tertatolo 0.010101010101010102 tertato 0.0025252525252525255 ]\n[ tatatataaaa 0.7702020202020202 tatanagar 0.09090909090909091 tataite 0.06313131313131314 tataya 0.04040404040404041 tatha 0.022727272727272728 tatwas 0.010101010101010102 <eps> 0.0025252525252525255 ]\ncluster0: 111.00000000000001 115.00000000000001\ncluster1: 115.00000000000001 119.00000000000001\ncluster2: 620.0 634.0\ncluster3: 634.0 640.0\ncluster4: 640.0 642.0\ncluster5: 642.0 648.0\ncluster6: 660.0 688.0\ncluster7: 1073.0 1077.0\ncluster8: 1087.0 1097.0\ncluster9: 1113.0 1131.0\ncluster10: 1173.0 1179.0\ncluster11: 1179.0 1187.0\ncluster12: 1187.0 1193.0\ncluster13: 1269.0 1289.0\ncluster14: 1289.0 1307.0\ncluster15: 1307.0 1321.0\ncluster16: 1333.0 1373.0\ncluster17: 1507.0 1515.0\ncluster18: 1789.9999999999998 1797.9999999999998\n"}], "sourceFile": "/uksivmprd1/dcme1/ABC1234/2024-01-21/283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-.wav", "utteranceCount": 5, "wordLattice": "[ no 0.16307211387228687 fo 0.03901580685750236 ne 0.037787039949669705 ono 0.03657793331236238 now 0.03538848694558037 nour 0.034218700849323685 n 0.033068575023592325 four 0.03193810946838629 wo 0.030827304183705568 one 0.029736159169550174 fe 0.028664674425920102 nzo 0.02761284995281535 tho 0.02658068575023592 ner 0.02556818181818182 noe 0.024575338156653036 nove 0.023602154765649575 eo 0.022648631645171436 flo 0.021714768795218622 bo 0.02080056621579113 two 0.019906023906888958 nu 0.01903114186851211 foll 0.018175920100660586 na 0.017340358603334383 nwo 0.0165244573765335 nop 0.015728216420257943 nur 0.014951635734507706 noo 0.014194715319282793 f 0.013457455174583202 nor 0.012739855300408933 on 0.012041915696759987 lo 0.011363636363636364 te 0.010705017301038061 nob 0.010066058508965083 fer 0.009446759987417426 noll 0.008847121736395092 oo 0.008267143755898081 nero 0.007706826045926392 nox 0.007166168606480026 nin 0.00664517143755898 heo 0.006143834539163259 to 0.005662157911292859 neur 0.005200141553947783 po 0.004757785467128028 neo 0.004335089650833596 zo 0.003932054105064486 non 0.0035486788298206983 ao 0.0031849638251022334 foo 0.002840909090909091 ni 0.0025165146272412707 onlo 0.002211780434098773 nlo 0.001926706511481598 moo 0.0014155394778232148 hoo 0.001189446366782007 o 0.0009830135262661214 night 0.0007962409562755583 goo 0.0006291286568103177 foe 0.0004816766278703995 <eps> 0.00024575338156653036 er 0.00015728216420257942 new 8.847121736395092e-05 oner 3.9320541050644855e-05 nly 9.830135262661214e-06 ]\n[ right 1.0 ]\n[ in 1.0 ]\n[ front 1.0 ]\n[ of 1.0 ]\n[ costa 0.2174327618748034 <eps> 0.19884397609311105 the 0.13094723183391002 coster 0.04220077068260459 cluster 0.030827304183705568 cooster 0.029736159169550174 coaster 0.02761284995281535 cster 0.02658068575023592 coloster 0.024575338156653036 closter 0.023602154765649575 usa 0.022648631645171436 colosa 0.021714768795218622 coosta 0.020810396351053792 koster 0.019906023906888958 kosa 0.01903114186851211 colster 0.017340358603334383 costter 0.0165244573765335 coolster 0.015728216420257943 poster 0.014951635734507706 custer 0.013457455174583202 a 0.012739855300408933 coolsa 0.012041915696759987 colosta 0.008847121736395092 colsta 0.007706826045926392 losa 0.006143834539163259 coolsta 0.003932054105064486 clster 0.0035486788298206983 loster 0.002840909090909091 cokoster 0.0025165146272412707 clsa 0.002211780434098773 lsa 0.001661292859389745 luster 0.0009830135262661214 cusa 0.0003538848694558037 ]\n[ if 1.0 ]\n[ you 1.0 ]\n[ turn 0.69603255740799 turned 0.3039674425920101 ]\n[ to 1.0 ]\n[ turn 1.0 ]\n[ to 1.0 ]\n[ your 1.0 ]\n[ back 1.0 ]\n[ can 0.5922558194400755 <eps> 0.2941864580056622 to 0.11083477508650519 you 0.0027229474677571562 ]\n[ turn 1.0 ]\n[ to 1.0 ]\n[ your 1.0 ]\n[ back 1.0 ]\n[ and 0.9962055677886128 on 0.003794432211387229 ]\n[ back 1.0 ]\n[ with 0.5378263604907203 of 0.3173954073608053 to 0.1446897609311104 <eps> 8.847121736395092e-05 ]\n[ you 1.0 ]\n[ do 0.9737731991192199 can 0.02622680088078012 ]\n[ you 1.0 ]\n[ sense 0.5241133217993079 <eps> 0.2891829191569676 send 0.09736748977665932 say 0.05558941491034917 spend 0.029814800251651462 sending 0.003932054105064486 ]\n[ that 0.5417485844605222 <eps> 0.21486709657124883 send 0.08854002831078955 spend 0.05104789241899969 sending 0.04644738911607424 say 0.03284248191255112 about 0.023090987731991194 spending 0.0014155394778232148 ]\n[ yeah 1.0 ]\n[ yeah 1.0 ]\n[ they're 1.0 ]\n[ uh 0.9964513211701793 ah 0.0035486788298206983 ]\n[ in 1.0 ]\n[ front 1.0 ]\n[ of 1.0 ]\n[ you 0.8819793960364894 me 0.11802060396351054 ]\n[ rightly 0.6580587448883297 <eps> 0.34194125511167034 ]\ncluster0: 1.01 1.05\ncluster1: 5.74 5.78\ncluster2: 6.06 6.1\ncluster3: 6.1800000000000015 6.2200000000000015\ncluster4: 6.460000000000001 6.500000000000001\ncluster5: 6.58 6.62\ncluster6: 12.61 12.65\ncluster7: 12.809999999999997 12.849999999999998\ncluster8: 13.41 13.45\ncluster9: 13.69 13.73\ncluster10: 13.73 13.77\ncluster11: 13.77 13.81\ncluster12: 13.809999999999997 13.889999999999997\ncluster13: 13.93 13.97\ncluster14: 15.73 15.77\ncluster15: 15.77 15.81\ncluster16: 15.809999999999997 15.849999999999998\ncluster17: 15.849999999999998 15.929999999999998\ncluster18: 15.929999999999998 15.969999999999997\ncluster19: 15.97 16.01\ncluster20: 16.09 16.13\ncluster21: 16.37 16.41\ncluster22: 16.490000000000002 16.53\ncluster23: 17.5 17.54\ncluster24: 17.66 17.7\ncluster25: 17.74 17.779999999999998\ncluster26: 18.5 18.58\ncluster27: 18.74 18.939999999999998\ncluster28: 18.98 19.02\ncluster29: 19.02 19.06\ncluster30: 19.18 19.22\ncluster31: 19.46 19.5\ncluster32: 19.54 19.62\ncluster33: 19.78 19.82\ncluster34: 19.9 19.939999999999998\ncluster35: 20.14 20.18\n"}, {"channelNumber": 1, "id": 22698687, "mimeType": "audio/wav", "modelLattices": [{"modelId": 13, "wordLattice": "[ から 0.9899339414910349 か 0.010066058508965083 ]\n[ は 0.979396036489462 <eps> 0.020603963510537907 ]\n[ や 0.917770918527839 <eps> 0.04768598615916955 親 0.034543095312991504 ]\n[ は 1.0 ]\n[ い 0.7824591066373073 家 0.19274929223026108 いえ 0.018647766593268323 <eps> 0.006143834539163259 ]\n[ これ 0.9780296476879522 それ 0.015826517772884553 家 0.006143834539163259 ]\n[ で 0.9857463038691412 <eps> 0.014253696130858761 ]\n[ て 0.6895151777288455 <eps> 0.3104848222711544 ]\n[ い 0.7244613085876062 <eps> 0.27553869141239384 ]\n[ か 0.9936202422145328 <eps> 0.006379757785467128 ]\n[ あ 1.0 ]\n[ あ 1.0 ]\n[ あ 1.0 ]\n[ ぶ 0.8343818810946838 <eps> 0.16561811890531614 ]\n[ か 0.9745104592639194 <eps> 0.025489540736080528 ]\n[ さ 1.0 ]\n[ じ 0.7835207612456747 ジ 0.20552846807172068 <eps> 0.010950770682604593 ]\n[ って 0.7678122050959422 っ 0.13352272727272727 <eps> 0.06991192198804655 て 0.028753145643284052 ]\n[ 言 0.9300880780119535 て 0.05896115130544196 ジ 0.010950770682604593 ]\n[ ば 0.9874764076753696 ばら 0.012523592324630387 ]\n[ 日 1.0 ]\n[ は 1.0 ]\n[ か 0.6360195816294433 最 0.11911174897766594 ら 0.10268559295375905 歳 0.06240169864737339 さ 0.028664674425920102 会 0.018962330921673484 から 0.018175920100660586 <eps> 0.012995438817238126 際 0.0009830135262661214 ]\n[ あ 0.835797420572507 <eps> 0.11796162315193456 は 0.046240956275558354 ]\n[ あ 0.7609507706826046 <eps> 0.23586426549229317 は 0.0031849638251022334 ]\n[ あ 0.7005150990877634 <eps> 0.20841852783894307 ア 0.06037669078326518 が 0.014106244101918842 グ 0.012209027996225228 は 0.004374410191884241 ]\n[ ク 0.798718150361749 ア 0.08338903743315508 ッ 0.07846413966656181 <eps> 0.03327500786410821 フ 0.006143834539163259 ダ 9.830135262661214e-06 ]\n[ あ 0.40832415854042153 あっ 0.2534896980182447 ああ 0.21757038376848065 <eps> 0.1206157596728531 ]\n[ さ 0.8793842403271469 <eps> 0.09604042151620007 ああ 0.024575338156653036 ]\n[ れ 0.9039595784837999 <eps> 0.09604042151620007 ]\n[ は 0.6261697860962567 か 0.10502516514627241 い 0.10116192198804655 あ 0.05627752437873545 く 0.03901580685750236 や 0.030217835797420572 が 0.024781770997168922 はい 0.01530552060396351 他 0.0019070462409562755 <eps> 0.000137621893677257 ]\ncluster0: 198.0 208.0\ncluster1: 232.00000000000003 312.0\ncluster2: 350.0 358.0\ncluster3: 388.0 390.0\ncluster4: 404.0 420.0\ncluster5: 420.0 435.99999999999994\ncluster6: 435.99999999999994 451.99999999999994\ncluster7: 451.99999999999994 467.99999999999994\ncluster8: 468.0 484.0\ncluster9: 484.0 500.0\ncluster10: 777.9999999999999 779.9999999999999\ncluster11: 826.0 904.0\ncluster12: 927.9999999999999 939.9999999999999\ncluster13: 986.0 988.0\ncluster14: 1009.6 1033.1999999999998\ncluster15: 1033.1999999999998 1056.7999999999997\ncluster16: 1056.8 1080.3999999999999\ncluster17: 1080.4 1104.0\ncluster18: 1104.0 1127.6\ncluster19: 1127.6 1151.1999999999998\ncluster20: 1151.2 1174.8\ncluster21: 1174.8000000000002 1198.4\ncluster22: 1198.4 1222.0\ncluster23: 1913.0 1960.0\ncluster24: 1960.0000000000002 2007.0000000000002\ncluster25: 2007.0 2054.0\ncluster26: 2054.0000000000005 2101.0000000000005\ncluster27: 2151.0 2159.0\ncluster28: 2185.0 2205.0\ncluster29: 2205.0 2215.0\ncluster30: 2215.0 2245.0\n"}, {"modelId": 17, "wordLattice": "[ hello 0.972347829506134 <eps> 0.027652170493865997 ]\n[ hi 0.7279411764705882 <eps> 0.1245183233721296 hiu 0.05214886756841774 hia 0.04817749292230261 hih 0.04436340044039006 h 0.002850739226171752 ]\n[ uh 0.3246795375904372 <eps> 0.31671712802768165 h 0.08330056621579113 ah 0.06960718779490406 u 0.06056346335325574 er 0.05627752437873545 and 0.03386481597986788 au 0.03067985215476565 a 0.011805992450456118 eh 0.008267143755898081 i 0.0033422459893048127 huh 0.0005996382510223341 ua 0.00024575338156653036 or 4.9150676313306074e-05 ]\n[ where 1.0 ]\n[ are 1.0 ]\n[ you 1.0 ]\n[ for 0.9372837370242214 f 0.02340555206039635 form 0.020771075810003144 on 0.013811340044039005 with 0.004728295061340044 ]\n[ the 1.0 ]\n[ pick 0.5445796634161686 pickup 0.4319951242529097 <eps> 0.019876533501100975 pig 0.0035486788298206983 ]\n[ up 0.5640531613715005 <eps> 0.43594683862849953 ]\n[ oh 0.7762169707455174 ah 0.21960522176785152 uh 0.004177807486631016 ]\n[ okay 1.0 ]\n[ because 1.0 ]\n[ i 0.6659326832337213 <eps> 0.3136402956904687 i'm 0.020427021075810004 ]\n[ i'm 0.9985648002516515 i 0.0014351997483485373 ]\n[ just 1.0 ]\n[ here 1.0 ]\n[ but 1.0 ]\n[ i 1.0 ]\n[ can't 1.0 ]\n[ see 1.0 ]\n[ you 1.0 ]\n[ i'm 0.4328896665618119 on 0.3004089336269267 one 0.14764863164517145 i 0.0800369613085876 <eps> 0.02582376533501101 and 0.008856951871657755 onm 0.004335089650833596 ]\n[ second 0.8137582573136206 seconds 0.16949119219880465 <eps> 0.007175998741742687 secant 0.00664517143755898 on 0.002840909090909091 one 8.847121736395092e-05 ]\n[ ah 0.18185750235923245 oh 0.052738675684177415 uh 0.04664399182132746 h 0.04043134633532557 wh 0.03538848694558037 a 0.03445462409562756 auh 0.034218700849323685 r 0.033068575023592325 ouh 0.03193810946838629 oorh 0.030827304183705568 ahh 0.029736159169550174 aorh 0.028664674425920102 orh 0.02761284995281535 agh 0.02658068575023592 augh 0.02556818181818182 am 0.024575338156653036 or 0.023602154765649575 mh 0.022648631645171436 erh 0.019906023906888958 m 0.01903114186851211 ohh 0.018175920100660586 aah 0.017340358603334383 ooh 0.0165244573765335 ough 0.015728216420257943 all 0.014194715319282793 ugh 0.013457455174583202 huh 0.012041915696759987 <eps> 0.011363636363636364 ogh 0.010705017301038061 aw 0.009446759987417426 ao 0.008847121736395092 om 0.008267143755898081 oo 0.007706826045926392 um 0.007166168606480026 oah 0.00664517143755898 ach 0.006143834539163259 uhh 0.005662157911292859 aerh 0.005200141553947783 are 0.004757785467128028 rh 0.004335089650833596 o 0.003932054105064486 er 0.0035486788298206983 aor 0.0031849638251022334 aer 0.002840909090909091 ch 0.0025165146272412707 ow 0.002211780434098773 orr 0.001926706511481598 au 0.001661292859389745 u 0.001189446366782007 hh 0.0009830135262661214 al 0.0007962409562755583 aoh 0.0006291286568103177 ll 0.0004816766278703995 aa 0.0003538848694558037 uuh 0.00024575338156653036 amh 0.00015728216420257942 oruh 8.847121736395092e-05 oor 3.9320541050644855e-05 hah 9.830135262661214e-06 ]\n[ i'll 0.7562716262975778 i 0.09166601132431582 <eps> 0.08155080213903744 i've 0.021714768795218622 i'd 0.018175920100660586 he'll 0.008267143755898081 we'll 0.007706826045926392 all 0.00664517143755898 i' 0.003932054105064486 you'll 0.0031849638251022334 i'm 0.0008847121736395093 ]\n[ go 0.7170985372758729 got 0.19085207612456748 get 0.03193810946838629 goe 0.0165244573765335 goet 0.015728216420257943 come 0.012041915696759987 g 0.010705017301038061 <eps> 0.004757785467128028 be 0.0003538848694558037 ]\n[ back 0.8917112299465241 <eps> 0.10828877005347594 ]\n[ back 0.7062755583516829 <eps> 0.2795297263290343 backed 0.014194715319282793 ]\n[ oh 0.3645603963510538 ah 0.20729789241899968 uh 0.10208595470273671 h 0.09804576910978295 ahh 0.04618197546398239 oorh 0.0338353255740799 ohh 0.026757628184963824 a 0.026049858446052218 auh 0.019709421201635734 oah 0.019158933626926708 ouh 0.018569125511167035 ooh 0.012041915696759987 aah 0.009446759987417426 uhh 0.007166168606480026 aorh 0.004757785467128028 o 0.0025165146272412707 ach 0.001661292859389745 <eps> 0.00015728216420257942 ]\n[ sorry 0.9711190625983014 sororry 0.022068653664674427 soorry 0.0058587606165460835 <eps> 0.0009535231204781377 ]\n[ okay 0.9668429537590437 <eps> 0.03252791758414596 okaya 0.0006291286568103177 ]\ncluster0: 190.0 194.0\ncluster1: 234.0 242.0\ncluster2: 242.0 250.0\ncluster3: 282.0 294.0\ncluster4: 306.0 310.0\ncluster5: 322.0 326.0\ncluster6: 346.0 350.0\ncluster7: 370.0 374.0\ncluster8: 378.0 382.0\ncluster9: 382.0 386.0\ncluster10: 752.0 756.0\ncluster11: 784.0 788.0\ncluster12: 827.9999999999999 831.9999999999999\ncluster13: 855.9999999999999 859.9999999999999\ncluster14: 871.9999999999999 883.9999999999999\ncluster15: 887.9999999999999 891.9999999999999\ncluster16: 916.0 920.0\ncluster17: 940.0 944.0\ncluster18: 952.0 956.0\ncluster19: 964.0 968.0\ncluster20: 1000.0 1004.0\ncluster21: 1028.0 1032.0\ncluster22: 1032.0 1044.0\ncluster23: 1080.0 1084.0\ncluster24: 1720.0 1724.0\ncluster25: 1966.0 1970.0\ncluster26: 1989.9999999999998 1993.9999999999998\ncluster27: 2014.0 2018.0\ncluster28: 2042.0000000000002 2046.0000000000002\ncluster29: 2137.0 2141.0\ncluster30: 2181.0 2185.0\ncluster31: 2237.0 2241.0\n"}, {"modelId": 18, "wordLattice": "[ alors 0.5826517772884555 <eps> 0.37065508021390375 ah 0.04669314249764077 ]\n[ ah 0.7207750078641082 aha 0.18994770368040265 <eps> 0.0629030355457691 ha 0.02266829191569676 aa 0.0035486788298206983 ahh 0.00015728216420257942 ]\n[ ou 0.9247011638880152 ouais 0.0752988361119849 ]\n[ ailleurs 1.0 ]\n[ pas 1.0 ]\n[ de 1.0 ]\n[ b 0.46227194086190626 <eps> 0.45370989304812837 be 0.0840181660899654 ]\n[ gap 0.5462901069518716 <eps> 0.40258335954702734 bgap 0.030896115130544195 begap 0.017045454545454544 bégap 0.0031849638251022334 ]\n[ aka 0.5755839100346021 ah 0.3639607581000315 ahka 0.06045533186536647 ]\n[ à 1.0 ]\n[ anjstirb 0.42083792072978926 <eps> 0.4151757628184964 anjsty 0.16398631645171438 ]\n[ county 1.0 ]\n[ wagon 0.7904116860648003 <eps> 0.1255013368983957 antagon 0.06276541365209186 andragan 0.02048600188738597 andragon 0.0008355614973262032 ]\n[ on 0.5084342560553633 ont 0.08812716262975778 en 0.08149182132746147 <eps> 0.07208438188109469 h 0.035319675998741744 onon 0.028664674425920102 ou 0.027878263604907205 un 0.02777996225228059 et 0.024821091538219566 onant 0.017340358603334383 a 0.0165244573765335 ant 0.014951635734507706 eu 0.010066058508965083 onh 0.009446759987417426 enon 0.008267143755898081 au 0.006143834539163259 n 0.005662157911292859 t 0.004335089650833596 hon 0.003932054105064486 onan 0.002840909090909091 l 0.0025165146272412707 y 0.001661292859389745 in 0.0006291286568103177 non 0.0004816766278703995 an 0.0003538848694558037 enh 0.00015728216420257942 onton 8.847121736395092e-05 ]\n[ hey 0.378047341931425 <eps> 0.3703896665618119 heil 0.09311104120792701 hein 0.08149182132746147 e 0.07696012897137465 ]\n[ hagard 0.351004639823844 <eps> 0.3447723340673168 hagardback 0.3042230261088393 ]\n[ backpack 0.42865287826360493 <eps> 0.3039379521862221 back 0.16835089650833596 pack 0.04395053475935829 e 0.021754089336269267 hardback 0.01903114186851211 backs 0.007077697389116074 bac 0.00453169235608682 backpacks 0.002555835168291916 pak 0.00015728216420257942 ]\n[ ah 1.0 ]\n[ ça 0.7866958949355143 <eps> 0.2133041050644857 ]\n[ c' 0.47634869455803713 c'est 0.18972161056936143 <eps> 0.09206904687008494 ça 0.06101564957533816 les 0.050703837684806546 fait 0.03927139037433155 c 0.037787039949669705 s 0.012739855300408933 fac 0.010705017301038061 s' 0.008847121736395092 c'estc 0.00664517143755898 s'est 0.006143834539163259 fallait 0.0025165146272412707 s'ac 0.002211780434098773 f 0.0014155394778232148 c'c 0.0009830135262661214 le 0.0006291286568103177 a 0.00024575338156653036 ]\ncluster0: 310.0 318.0\ncluster1: 350.0 358.0\ncluster2: 358.0 366.0\ncluster3: 366.0 374.0\ncluster4: 374.0 382.0\ncluster5: 382.0 390.0\ncluster6: 390.00000000000006 398.0\ncluster7: 398.0 405.99999999999994\ncluster8: 752.0 760.0\ncluster9: 855.9999999999999 863.9999999999999\ncluster10: 864.0 872.0\ncluster11: 976.0 984.0\ncluster12: 1048.0 1056.0\ncluster13: 1056.0 1086.0\ncluster14: 1930.0 1938.0\ncluster15: 1938.0 1946.0\ncluster16: 1945.9999999999998 1953.9999999999998\ncluster17: 2149.0 2157.0\ncluster18: 2173.0 2181.0\ncluster19: 2203.0 2233.0\n"}, {"modelId": 19, "wordLattice": "[ no 1.0 ]\n[ hooroar 1.0 ]\n[ the 1.0 ]\n[ backup 0.7373737373737373 pickup 0.18686868686868688 bigot 0.04040404040404041 big 0.03282828282828283 becu 0.0025252525252525255 ]\n[ okay 1.0 ]\n[ because 1.0 ]\n[ i 1.0 ]\n[ injustice 1.0 ]\n[ on 1.0 ]\n[ track 0.7095959595959596 <eps> 0.255050505050505 trike 0.022727272727272728 trice 0.010101010101010102 trika 0.0025252525252525255 ]\n[ i 1.0 ]\n[ s 1.0 ]\n[ are 1.0 ]\n[ of 0.851010101010101 o 0.14898989898989898 ]\n[ back 1.0 ]\n[ fuck 0.7828282828282829 fork 0.11363636363636363 lock 0.06313131313131314 cock 0.04040404040404041 ]\n[ so 0.8106060606060606 <eps> 0.1893939393939394 ]\n[ AU 0.6464646464646465 <eps> 0.1893939393939394 A 0.12373737373737374 AC 0.04040404040404041 ]\ncluster0: 216.0 220.0\ncluster1: 354.0 362.0\ncluster2: 370.0 376.0\ncluster3: 382.0 392.0\ncluster4: 786.0 810.0\ncluster5: 830.0000000000001 854.0000000000001\ncluster6: 860.0 862.0\ncluster7: 871.9999999999999 877.9999999999999\ncluster8: 892.0 916.0\ncluster9: 1082.0 1092.0\ncluster10: 1939.9999999999998 1955.9999999999998\ncluster11: 1955.9999999999998 1971.9999999999998\ncluster12: 1978.0 2004.0\ncluster13: 2004.0000000000002 2018.0\ncluster14: 2018.0 2028.0\ncluster15: 2042.0000000000002 2054.0000000000005\ncluster16: 2159.0000000000005 2209.0000000000005\ncluster17: 2249.0 2251.0\n"}], "sourceFile": "/uksivmprd1/dcme1/ABC1234/2024-01-21/283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-.wav", "utteranceCount": 6, "wordLattice": "[ hello 0.972347829506134 <eps> 0.027652170493865997 ]\n[ hi 0.7279411764705882 <eps> 0.1245183233721296 hiu 0.05214886756841774 hia 0.04817749292230261 hih 0.04436340044039006 h 0.002850739226171752 ]\n[ uh 0.3246795375904372 <eps> 0.31671712802768165 h 0.08330056621579113 ah 0.06960718779490406 u 0.06056346335325574 er 0.05627752437873545 and 0.03386481597986788 au 0.03067985215476565 a 0.011805992450456118 eh 0.008267143755898081 i 0.0033422459893048127 huh 0.0005996382510223341 ua 0.00024575338156653036 or 4.9150676313306074e-05 ]\n[ where 1.0 ]\n[ are 1.0 ]\n[ you 1.0 ]\n[ for 0.9372837370242214 f 0.02340555206039635 form 0.020771075810003144 on 0.013811340044039005 with 0.004728295061340044 ]\n[ the 1.0 ]\n[ pick 0.5445796634161686 pickup 0.4319951242529097 <eps> 0.019876533501100975 pig 0.0035486788298206983 ]\n[ up 0.5640531613715005 <eps> 0.43594683862849953 ]\n[ oh 0.7762169707455174 ah 0.21960522176785152 uh 0.004177807486631016 ]\n[ okay 1.0 ]\n[ because 1.0 ]\n[ i 0.6659326832337213 <eps> 0.3136402956904687 i'm 0.020427021075810004 ]\n[ i'm 0.9985648002516515 i 0.0014351997483485373 ]\n[ just 1.0 ]\n[ here 1.0 ]\n[ but 1.0 ]\n[ i 1.0 ]\n[ can't 1.0 ]\n[ see 1.0 ]\n[ you 1.0 ]\n[ i'm 0.4328896665618119 on 0.3004089336269267 one 0.14764863164517145 i 0.0800369613085876 <eps> 0.02582376533501101 and 0.008856951871657755 onm 0.004335089650833596 ]\n[ second 0.8137582573136206 seconds 0.16949119219880465 <eps> 0.007175998741742687 secant 0.00664517143755898 on 0.002840909090909091 one 8.847121736395092e-05 ]\n[ ah 0.18185750235923245 oh 0.052738675684177415 uh 0.04664399182132746 h 0.04043134633532557 wh 0.03538848694558037 a 0.03445462409562756 auh 0.034218700849323685 r 0.033068575023592325 ouh 0.03193810946838629 oorh 0.030827304183705568 ahh 0.029736159169550174 aorh 0.028664674425920102 orh 0.02761284995281535 agh 0.02658068575023592 augh 0.02556818181818182 am 0.024575338156653036 or 0.023602154765649575 mh 0.022648631645171436 erh 0.019906023906888958 m 0.01903114186851211 ohh 0.018175920100660586 aah 0.017340358603334383 ooh 0.0165244573765335 ough 0.015728216420257943 all 0.014194715319282793 ugh 0.013457455174583202 huh 0.012041915696759987 <eps> 0.011363636363636364 ogh 0.010705017301038061 aw 0.009446759987417426 ao 0.008847121736395092 om 0.008267143755898081 oo 0.007706826045926392 um 0.007166168606480026 oah 0.00664517143755898 ach 0.006143834539163259 uhh 0.005662157911292859 aerh 0.005200141553947783 are 0.004757785467128028 rh 0.004335089650833596 o 0.003932054105064486 er 0.0035486788298206983 aor 0.0031849638251022334 aer 0.002840909090909091 ch 0.0025165146272412707 ow 0.002211780434098773 orr 0.001926706511481598 au 0.001661292859389745 u 0.001189446366782007 hh 0.0009830135262661214 al 0.0007962409562755583 aoh 0.0006291286568103177 ll 0.0004816766278703995 aa 0.0003538848694558037 uuh 0.00024575338156653036 amh 0.00015728216420257942 oruh 8.847121736395092e-05 oor 3.9320541050644855e-05 hah 9.830135262661214e-06 ]\n[ i'll 0.7562716262975778 i 0.09166601132431582 <eps> 0.08155080213903744 i've 0.021714768795218622 i'd 0.018175920100660586 he'll 0.008267143755898081 we'll 0.007706826045926392 all 0.00664517143755898 i' 0.003932054105064486 you'll 0.0031849638251022334 i'm 0.0008847121736395093 ]\n[ go 0.7170985372758729 got 0.19085207612456748 get 0.03193810946838629 goe 0.0165244573765335 goet 0.015728216420257943 come 0.012041915696759987 g 0.010705017301038061 <eps> 0.004757785467128028 be 0.0003538848694558037 ]\n[ back 0.8917112299465241 <eps> 0.10828877005347594 ]\n[ back 0.7062755583516829 <eps> 0.2795297263290343 backed 0.014194715319282793 ]\n[ oh 0.3645603963510538 ah 0.20729789241899968 uh 0.10208595470273671 h 0.09804576910978295 ahh 0.04618197546398239 oorh 0.0338353255740799 ohh 0.026757628184963824 a 0.026049858446052218 auh 0.019709421201635734 oah 0.019158933626926708 ouh 0.018569125511167035 ooh 0.012041915696759987 aah 0.009446759987417426 uhh 0.007166168606480026 aorh 0.004757785467128028 o 0.0025165146272412707 ach 0.001661292859389745 <eps> 0.00015728216420257942 ]\n[ sorry 0.9711190625983014 sororry 0.022068653664674427 soorry 0.0058587606165460835 <eps> 0.0009535231204781377 ]\n[ okay 0.9668429537590437 <eps> 0.03252791758414596 okaya 0.0006291286568103177 ]\ncluster0: 1.9 1.94\ncluster1: 2.34 2.42\ncluster2: 2.42 2.5\ncluster3: 2.82 2.94\ncluster4: 3.06 3.1\ncluster5: 3.22 3.26\ncluster6: 3.46 3.5\ncluster7: 3.7 3.74\ncluster8: 3.78 3.82\ncluster9: 3.82 3.86\ncluster10: 7.52 7.56\ncluster11: 7.84 7.88\ncluster12: 8.28 8.319999999999999\ncluster13: 8.559999999999999 8.6\ncluster14: 8.719999999999999 8.839999999999998\ncluster15: 8.879999999999999 8.919999999999998\ncluster16: 9.16 9.2\ncluster17: 9.4 9.44\ncluster18: 9.52 9.56\ncluster19: 9.64 9.68\ncluster20: 10.0 10.04\ncluster21: 10.28 10.32\ncluster22: 10.32 10.44\ncluster23: 10.8 10.84\ncluster24: 17.2 17.24\ncluster25: 19.66 19.7\ncluster26: 19.9 19.939999999999998\ncluster27: 20.14 20.18\ncluster28: 20.42 20.46\ncluster29: 21.37 21.41\ncluster30: 21.81 21.85\ncluster31: 22.37 22.41\n"}], "srts": [{"alternatives": [], "generatedByASR": true, "id": 137299868, "length": 0.040000000000000036, "processingModels": [17], "score": 0.163, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 1.01, "word": "No."}, {"alternatives": [], "generatedByASR": true, "id": 137299904, "length": 0.040000000000000036, "processingModels": [17], "score": 0.972, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 1.9, "word": "Hello."}, {"alternatives": [], "generatedByASR": true, "id": 137299905, "length": 0.08000000000000007, "processingModels": [17], "score": 0.728, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 2.34, "word": "Hi."}, {"alternatives": [], "generatedByASR": true, "id": 137299906, "length": 0.08000000000000007, "processingModels": [17], "score": 0.325, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 2.42, "word": "Uh,"}, {"alternatives": [], "generatedByASR": true, "id": 137299907, "length": 0.1200000000000001, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 2.82, "word": "where"}, {"alternatives": [], "generatedByASR": true, "id": 137299908, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.06, "word": "are"}, {"alternatives": [], "generatedByASR": true, "id": 137299909, "length": 0.03999999999999959, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.22, "word": "you"}, {"alternatives": [], "generatedByASR": true, "id": 137299910, "length": 0.040000000000000036, "processingModels": [17], "score": 0.937, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.46, "word": "for"}, {"alternatives": [], "generatedByASR": true, "id": 137299911, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.7, "word": "the"}, {"alternatives": [], "generatedByASR": true, "id": 137299912, "length": 0.040000000000000036, "processingModels": [17], "score": 0.545, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.78, "word": "pick"}, {"alternatives": [], "generatedByASR": true, "id": 137299913, "length": 0.040000000000000036, "processingModels": [17], "score": 0.564, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 3.82, "word": "up?"}, {"alternatives": [], "generatedByASR": true, "id": 137299869, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 5.74, "word": "Right,"}, {"alternatives": [], "generatedByASR": true, "id": 137299870, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 6.06, "word": "in"}, {"alternatives": [], "generatedByASR": true, "id": 137299871, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 6.1800000000000015, "word": "front"}, {"alternatives": [], "generatedByASR": true, "id": 137299872, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 6.460000000000001, "word": "of"}, {"alternatives": [], "generatedByASR": true, "id": 137299873, "length": 0.040000000000000036, "processingModels": [17], "score": 0.217, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 6.58, "word": "Costa."}, {"alternatives": [], "generatedByASR": true, "id": 137299914, "length": 0.040000000000000036, "processingModels": [17], "score": 0.776, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 7.52, "word": "Oh,"}, {"alternatives": [], "generatedByASR": true, "id": 137299915, "length": 0.040000000000000036, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 7.84, "word": "okay."}, {"alternatives": [], "generatedByASR": true, "id": 137299916, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 8.28, "word": "Because"}, {"alternatives": [], "generatedByASR": true, "id": 137299917, "length": 0.040000000000000924, "processingModels": [17], "score": 0.666, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 8.559999999999999, "word": "I,"}, {"alternatives": [], "generatedByASR": true, "id": 137299918, "length": 0.11999999999999922, "processingModels": [17], "score": 0.999, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 8.719999999999999, "word": "I'm"}, {"alternatives": [], "generatedByASR": true, "id": 137299919, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 8.879999999999999, "word": "just"}, {"alternatives": [], "generatedByASR": true, "id": 137299920, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 9.16, "word": "here"}, {"alternatives": [], "generatedByASR": true, "id": 137299921, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 9.4, "word": "but"}, {"alternatives": [], "generatedByASR": true, "id": 137299922, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 9.52, "word": "I"}, {"alternatives": [], "generatedByASR": true, "id": 137299923, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 9.64, "word": "can't"}, {"alternatives": [], "generatedByASR": true, "id": 137299924, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 10.0, "word": "see"}, {"alternatives": [], "generatedByASR": true, "id": 137299925, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 10.28, "word": "you."}, {"alternatives": [], "generatedByASR": true, "id": 137299926, "length": 0.11999999999999922, "processingModels": [17], "score": 0.433, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 10.32, "word": "I'm"}, {"alternatives": [], "generatedByASR": true, "id": 137299927, "length": 0.03999999999999915, "processingModels": [17], "score": 0.814, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 10.8, "word": "second."}, {"alternatives": [], "generatedByASR": true, "id": 137299874, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 12.61, "word": "If"}, {"alternatives": [], "generatedByASR": true, "id": 137299875, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 12.809999999999997, "word": "you"}, {"alternatives": [], "generatedByASR": true, "id": 137299876, "length": 0.03999999999999915, "processingModels": [17], "score": 0.696, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.41, "word": "turn"}, {"alternatives": [], "generatedByASR": true, "id": 137299877, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.69, "word": "to"}, {"alternatives": [], "generatedByASR": true, "id": 137299878, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.73, "word": "turn"}, {"alternatives": [], "generatedByASR": true, "id": 137299879, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.77, "word": "to"}, {"alternatives": [], "generatedByASR": true, "id": 137299880, "length": 0.08000000000000007, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.809999999999997, "word": "your"}, {"alternatives": [], "generatedByASR": true, "id": 137299881, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 13.93, "word": "back."}, {"alternatives": [], "generatedByASR": true, "id": 137299882, "length": 0.03999999999999915, "processingModels": [17], "score": 0.592, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.73, "word": "Can"}, {"alternatives": [], "generatedByASR": true, "id": 137299883, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.77, "word": "turn"}, {"alternatives": [], "generatedByASR": true, "id": 137299884, "length": 0.040000000000000924, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.809999999999997, "word": "to"}, {"alternatives": [], "generatedByASR": true, "id": 137299885, "length": 0.08000000000000007, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.849999999999998, "word": "your"}, {"alternatives": [], "generatedByASR": true, "id": 137299886, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.929999999999998, "word": "back"}, {"alternatives": [], "generatedByASR": true, "id": 137299887, "length": 0.040000000000000924, "processingModels": [17], "score": 0.996, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 15.97, "word": "and"}, {"alternatives": [], "generatedByASR": true, "id": 137299888, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 16.09, "word": "back"}, {"alternatives": [], "generatedByASR": true, "id": 137299889, "length": 0.03999999999999915, "processingModels": [17], "score": 0.538, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 16.37, "word": "with"}, {"alternatives": [], "generatedByASR": true, "id": 137299890, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 16.490000000000002, "word": "you."}, {"alternatives": [], "generatedByASR": true, "id": 137299928, "length": 0.03999999999999915, "processingModels": [17], "score": 0.182, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 17.2, "word": "Ah."}, {"alternatives": [], "generatedByASR": true, "id": 137299891, "length": 0.03999999999999915, "processingModels": [17], "score": 0.974, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 17.5, "word": "Do"}, {"alternatives": [], "generatedByASR": true, "id": 137299892, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 17.66, "word": "you"}, {"alternatives": [], "generatedByASR": true, "id": 137299893, "length": 0.03999999999999915, "processingModels": [17], "score": 0.524, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 17.74, "word": "sense"}, {"alternatives": [], "generatedByASR": true, "id": 137299894, "length": 0.0799999999999983, "processingModels": [17], "score": 0.542, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 18.5, "word": "that?"}, {"alternatives": [], "generatedByASR": true, "id": 137299895, "length": 0.1999999999999993, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 18.74, "word": "Yeah,"}, {"alternatives": [], "generatedByASR": true, "id": 137299896, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 18.98, "word": "yeah."}, {"alternatives": [], "generatedByASR": true, "id": 137299897, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.02, "word": "They're,"}, {"alternatives": [], "generatedByASR": true, "id": 137299898, "length": 0.03999999999999915, "processingModels": [17], "score": 0.996, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.18, "word": "uh,"}, {"alternatives": [], "generatedByASR": true, "id": 137299899, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.46, "word": "in"}, {"alternatives": [], "generatedByASR": true, "id": 137299900, "length": 0.08000000000000185, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.54, "word": "front"}, {"alternatives": [], "generatedByASR": true, "id": 137299929, "length": 0.03999999999999915, "processingModels": [17], "score": 0.756, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 19.66, "word": "I'll"}, {"alternatives": [], "generatedByASR": true, "id": 137299901, "length": 0.03999999999999915, "processingModels": [17], "score": 1.0, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.78, "word": "of"}, {"alternatives": [], "generatedByASR": true, "id": 137299902, "length": 0.03999999999999915, "processingModels": [17], "score": 0.882, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 19.9, "word": "you."}, {"alternatives": [], "generatedByASR": true, "id": 137299930, "length": 0.03999999999999915, "processingModels": [17], "score": 0.717, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 19.9, "word": "go"}, {"alternatives": [], "generatedByASR": true, "id": 137299903, "length": 0.03999999999999915, "processingModels": [17], "score": 0.658, "speaker": {"channelNumber": 0, "id": 21253559, "label": "Channel 0", "name": "dummy", "no": 1}, "timestamp": 20.14, "word": "Rightly."}, {"alternatives": [], "generatedByASR": true, "id": 137299931, "length": 0.03999999999999915, "processingModels": [17], "score": 0.892, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 20.14, "word": "back"}, {"alternatives": [], "generatedByASR": true, "id": 137299932, "length": 0.03999999999999915, "processingModels": [17], "score": 0.706, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 20.42, "word": "back."}, {"alternatives": [], "generatedByASR": true, "id": 137299933, "length": 0.03999999999999915, "processingModels": [17], "score": 0.365, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 21.37, "word": "Oh,"}, {"alternatives": [], "generatedByASR": true, "id": 137299934, "length": 0.0400000000000027, "processingModels": [17], "score": 0.971, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 21.81, "word": "sorry."}, {"alternatives": [], "generatedByASR": true, "id": 137299935, "length": 0.03999999999999915, "processingModels": [17], "score": 0.967, "speaker": {"channelNumber": 1, "id": 21253560, "label": "Channel 1", "name": "dummy", "no": 2}, "timestamp": 22.37, "word": "Okay."}], "tags": [], "textURL": "", "timestamp": "2024-01-19 19:56:13.0", "title": "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-", "to": "", "url_ref": "", "userData": "{\"Recording\": {\"id\": \"283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-\", \"created\": \"2024-01-21T23:51:40.377340+00:00\", \"mediaType\": \"audio/wav\", \"fileExtension\": \".wav\", \"end\": \"2024-01-19T19:56:36+00:00\"}, \"Metadata\": {\"end\": \"2024-01-19T19:56:36+00:00\", \"start\": \"2024-01-19T19:56:13+00:00\", \"direction\": \"out\", \"channels\": [{\"id\": 0, \"deviceID\": \"+************\"}, {\"id\": 1, \"deviceID\": \"+************\"}]}, \"Additional\": {\"StartTime\": \"2024-01-19T19:56:13+00:00\", \"EndTime\": \"2024-01-19T19:56:36+00:00\", \"Direction\": \"Outbound\", \"CallId\": \"cc5c53be-b704-11ee-81f2-3789a07e81e7\", \"Duration\": 23.4, \"UserTimeZoneOffset\": \"UTC+01:00\", \"callUuid\": \"cc5c53be-b704-11ee-81f2-3789a07e81e7\", \"Caller\": \"************\", \"Dialled\": \"************\", \"RecordStatus\": \"FTP\", \"RecordName\": \"Russell MVR UK\", \"AudioFile\": {\"FileName\": \"283-cc5c53be-b704-11ee-81f2-3789a07e81e7-************-************-1705694173-ee-mo-.wav\", \"Md5Checksum\": \"98d1a57cc73c271285677ce33ecccabd\"}}}", "vox": 1, "waveform": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.01, 0.01, -0.01, 0.0, -0.01, 0.01, -0.01, 0.01, -0.01, 0.02, -0.01, 0.01, 0.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.06, 0.09, -0.31, 0.37, -0.46, 0.44, -0.56, 0.52, -0.66, 0.63, -0.59, 0.45, -0.16, 0.16, -0.03, 0.02, -0.02, 0.01, -0.01, 0.01, 0.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.01, 0.02, -0.01, 0.01, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.0, -0.57, 0.54, -0.65, 0.51, -0.67, 0.62, -0.65, 0.59, -0.56, 0.47, -0.53, 0.53, -0.48, 0.5, -0.37, 0.34, -0.49, 0.39, -0.41, 0.38, -0.44, 0.43, -0.63, 0.59, -0.62, 0.54, -0.51, 0.48, -0.59, 0.56, -0.64, 0.52, -0.59, 0.54, -0.54, 0.51, -0.55, 0.57, -0.43, 0.52, -0.53, 0.55, -0.56, 0.56, -0.52, 0.52, -0.48, 0.56, -0.48, 0.5, -0.52, 0.54, -0.41, 0.44, -0.5, 0.59, -0.51, 0.56, -0.53, 0.63, -0.43, 0.61, -0.47, 0.63, -0.53, 0.61, -0.49, 0.46, -0.43, 0.44, -0.28, 0.18, -0.24, 0.18, -0.55, 0.59, -0.49, 0.63, -0.6, 0.61, -0.62, 0.48, -0.45, 0.44, -0.31, 0.31, -0.09, 0.05, -0.02, 0.02, -0.01, 0.01, -0.02, 0.02, -0.01, 0.01, -0.03, 0.02, -0.66, 0.54, -0.53, 0.52, -0.32, 0.26, -0.36, 0.3, -0.34, 0.26, -0.05, 0.05, -0.34, 0.44, -0.62, 0.56, -0.51, 0.56, -0.1, 0.09, -0.18, 0.19, -0.57, 0.59, -0.47, 0.54, -0.47, 0.48, -0.04, 0.02, -0.01, 0.01, 0.0, 0.0, -0.01, 0.01, -0.02, 0.02, -0.02, 0.02, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.02, 0.02, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.03, 0.03, -0.29, 0.32, -0.28, 0.35, -0.2, 0.19, -0.1, 0.12, 0.0, 0.0, -0.01, 0.0, -0.04, 0.06, -0.05, 0.06, -0.01, 0.01, -0.01, 0.01, -0.03, 0.02, -0.34, 0.33, -0.34, 0.31, -0.29, 0.29, -0.02, 0.03, 0.0, 0.0, -0.03, 0.02, -0.13, 0.15, -0.03, 0.03, -0.02, 0.02, -0.01, 0.01, 0.0, 0.0, -0.03, 0.03, -0.21, 0.18, -0.32, 0.28, -0.27, 0.29, -0.02, 0.02, 0.0, 0.0, 0.0, 0.0, -0.04, 0.03, -0.14, 0.13, -0.09, 0.07, 0.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.07, 0.09, -0.65, 0.56, -0.65, 0.49, -0.6, 0.51, -0.66, 0.53, -0.47, 0.34, -0.6, 0.59, -0.46, 0.59, -0.52, 0.55, -0.34, 0.28, -0.02, 0.03, -0.18, 0.15, -0.61, 0.5, -0.62, 0.55, -0.6, 0.51, -0.63, 0.56, -0.54, 0.53, -0.59, 0.56, -0.53, 0.52, -0.45, 0.46, -0.51, 0.52, -0.1, 0.06, -0.36, 0.28, -0.53, 0.47, -0.17, 0.16, -0.55, 0.54, -0.66, 0.59, -0.62, 0.48, -0.07, 0.05, -0.64, 0.59, -0.53, 0.56, -0.4, 0.41, -0.32, 0.39, -0.51, 0.54, -0.51, 0.59, -0.36, 0.56, -0.36, 0.35, -0.36, 0.35, -0.34, 0.29, -0.13, 0.1, -0.62, 0.56, -0.66, 0.61, -0.47, 0.44, -0.04, 0.03, -0.01, 0.01, 0.0, 0.0, -0.01, 0.01, -0.24, 0.21, -0.66, 0.61, -0.57, 0.56, -0.55, 0.56, -0.49, 0.44, -0.1, 0.07, -0.38, 0.39, -0.55, 0.52, -0.47, 0.52, -0.38, 0.46, -0.55, 0.52, -0.6, 0.48, -0.38, 0.39, -0.12, 0.12, -0.03, 0.03, -0.11, 0.12, -0.49, 0.39, -0.6, 0.59, -0.43, 0.52, -0.43, 0.54, -0.43, 0.52, -0.22, 0.17, -0.21, 0.15, -0.01, 0.01, -0.01, 0.01, 0.0, 0.0, -0.01, 0.01, -0.01, 0.01, -0.04, 0.04, -0.38, 0.32, -0.45, 0.44, -0.43, 0.44, -0.3, 0.37, -0.24, 0.28, -0.24, 0.29, -0.22, 0.26, -0.21, 0.21, -0.23, 0.24, -0.35, 0.37, -0.43, 0.48, -0.53, 0.46, -0.43, 0.41, -0.36, 0.32, -0.36, 0.35, -0.34, 0.29, -0.38, 0.5, -0.39, 0.5, -0.36, 0.41, -0.17, 0.11, -0.09, 0.07, -0.06, 0.09, -0.03, 0.04, -0.32, 0.18, -0.66, 0.53, -0.71, 0.72, -0.73, 0.7, -0.55, 0.55, -0.04, 0.04, -0.54, 0.31, -0.67, 0.46, -0.59, 0.46, -0.47, 0.37, -0.29, 0.26, -0.27, 0.24, -0.18, 0.21, -0.19, 0.18, -0.07, 0.12, -0.23, 0.23, -0.31, 0.29, -0.24, 0.27, -0.02, 0.03, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.2, 0.2, -0.2, 0.19, -0.37, 0.4, -0.35, 0.28, -0.2, 0.21, -0.06, 0.06, -0.26, 0.33, -0.28, 0.36, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.15, 0.14, -0.19, 0.22, -0.17, 0.12, 0.0, 0.0, -0.09, 0.09, -0.04, 0.03, -0.02, 0.02, -0.01, 0.02, -0.02, 0.02, -0.02, 0.02, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.05, 0.07, -0.18, 0.19, -0.2, 0.2, -0.19, 0.21, -0.02, 0.02, 0.0, 0.0, -0.01, 0.01, -0.11, 0.12, -0.13, 0.15, -0.04, 0.04, -0.03, 0.03, 0.0, 0.0, 0.0, 0.0, -0.02, 0.02, -0.6, 0.56, -0.55, 0.48, -0.19, 0.18, -0.09, 0.07, -0.05, 0.02, 0.0, 0.0, 0.0, 0.0, -0.24, 0.22, -0.18, 0.18, -0.02, 0.05, -0.01, 0.0, 0.0, 0.0, -0.2, 0.17, -0.55, 0.61, -0.37, 0.44, -0.26, 0.29, -0.1, 0.09, 0.0, 0.0, -0.02, 0.01, -0.34, 0.32, -0.4, 0.45, -0.22, 0.22, -0.28, 0.3, -0.3, 0.29, -0.3, 0.32, -0.04, 0.05, -0.01, 0.01, -0.01, 0.0, -0.47, 0.43, -0.54, 0.54, -0.55, 0.44, -0.27, 0.27, -0.14, 0.13, -0.02, 0.02, -0.02, 0.02, -0.01, 0.01, 0.0, 0.01, -0.01, 0.02, -0.36, 0.35, -0.26, 0.27, -0.16, 0.18, -0.06, 0.07, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.2, 0.24, -0.32, 0.28, -0.17, 0.16, -0.03, 0.02, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, -0.05, 0.05, -0.11, 0.11, -0.12, 0.1, -0.15, 0.11, -0.13, 0.12, -0.01, 0.01, 0.0, 0.0, -0.01, 0.01, -0.37, 0.36, -0.33, 0.29, -0.22, 0.17, -0.07, 0.06, -0.01, 0.01, -0.01, 0.02, -0.31, 0.27, -0.27, 0.22, -0.22, 0.24, -0.24, 0.27, -0.17, 0.23, -0.03, 0.03, 0.0, 0.01, -0.45, 0.27, -0.5, 0.32, -0.47, 0.43, -0.38, 0.46, -0.2, 0.29, 0.0, 0.0, -0.02, 0.04, -0.29, 0.34, -0.32, 0.29, -0.2, 0.13, -0.07, 0.06, -0.07, 0.06, -0.04, 0.04, -0.06, 0.06, -0.04, 0.05, -0.03, 0.03, -0.03, 0.03, -0.02, 0.02, -0.01, 0.01, -0.57, 0.34, -0.67, 0.45, -0.57, 0.44, -0.07, 0.11, -0.01, 0.01, -0.16, 0.13, -0.21, 0.16, -0.05, 0.07, -0.05, 0.06, -0.11, 0.12, -0.12, 0.14, -0.14, 0.18, -0.13, 0.15, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.13, 0.11, -0.57, 0.56, -0.62, 0.49, -0.62, 0.47, -0.63, 0.46, -0.67, 0.51, -0.63, 0.5, -0.64, 0.47, -0.6, 0.5, -0.65, 0.48, -0.65, 0.48, -0.65, 0.49, -0.63, 0.48, -0.6, 0.5, -0.64, 0.51, -0.63, 0.49, -0.63, 0.52, -0.67, 0.52, -0.64, 0.53, -0.59, 0.5, -0.52, 0.52, -0.56, 0.49, -0.68, 0.56, -0.54, 0.6, -0.3, 0.23, -0.18, 0.17, -0.14, 0.11, -0.03, 0.03, -0.05, 0.05, -0.18, 0.22, -0.19, 0.21, -0.18, 0.2, -0.18, 0.18, -0.12, 0.13, -0.02, 0.03, -0.2, 0.21, -0.32, 0.33, -0.15, 0.18, -0.04, 0.04, -0.01, 0.02, -0.01, 0.01, -0.14, 0.14, -0.36, 0.39, -0.51, 0.5, -0.51, 0.52, -0.43, 0.43, -0.28, 0.32, -0.3, 0.35, -0.45, 0.41, -0.47, 0.56, -0.45, 0.5, -0.07, 0.03, 0.0, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.1, 0.05, -0.33, 0.35, -0.3, 0.3, -0.18, 0.19, -0.15, 0.16, -0.22, 0.24, -0.22, 0.26, -0.28, 0.32, -0.18, 0.16, -0.22, 0.17, -0.6, 0.5, -0.6, 0.51, -0.39, 0.53, -0.38, 0.52, -0.43, 0.52, -0.53, 0.52, -0.59, 0.57, -0.32, 0.45, -0.34, 0.36, -0.44, 0.41, -0.57, 0.62, -0.17, 0.16, -0.39, 0.45, -0.43, 0.53, -0.45, 0.6, -0.64, 0.53, -0.45, 0.35, -0.38, 0.32, -0.8, 0.77, -0.56, 0.65, -0.55, 0.56, -0.6, 0.55, -0.07, 0.09, -0.06, 0.06, -0.05, 0.05, -0.59, 0.51, -0.63, 0.68, -0.8, 1.0, -0.41, 0.56, -0.21, 0.18, -0.13, 0.1, -0.03, 0.03, -0.12, 0.12, -0.45, 0.51, -0.55, 0.43, -0.54, 0.47, -0.26, 0.39, -0.03, 0.02, -0.01, 0.01, -0.09, 0.04, -0.14, 0.16, -0.07, 0.09, -0.07, 0.06, -0.02, 0.02, -0.01, 0.01, -0.01, 0.01, -0.02, 0.02, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.57, 0.56, -0.66, 0.59, -0.67, 0.49, -0.59, 0.47, -0.33, 0.35, -0.6, 0.44, -0.33, 0.33, -0.23, 0.19, -0.1, 0.1, -0.03, 0.02, -0.02, 0.02, -0.03, 0.02, -0.51, 0.55, -0.46, 0.5, -0.36, 0.18, -0.57, 0.48, -0.48, 0.56, -0.51, 0.54, -0.62, 0.52, -0.53, 0.49, -0.52, 0.45, -0.21, 0.17, -0.04, 0.03, -0.01, 0.01, -0.16, 0.13, -0.34, 0.32, -0.38, 0.28, -0.04, 0.03, -0.04, 0.04, -0.4, 0.41, -0.53, 0.44, -0.38, 0.31, -0.04, 0.03, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.01, -0.01, 0.01, 0.0, 0.0, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.01, 0.01, -0.01, 0.01, -0.01, 0.01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "processing_start": "2024-02-11T00:00:41.000Z", "processing_end": "2024-02-11T01:05:23.000Z", "language_models": [{"id": 19, "modelFullName": "IntelligentVoice_x-languageid_16kHz_3_en.ja.fr_V4.1_NASRv2", "weight": 0.8}, {"id": 18, "modelFullName": "IntelligentVoice_fr_16kHz_1024_general_V1_NASRv3.1", "weight": 0.2}, {"id": 17, "modelFullName": "IntelligentVoice_en-001_16kHz_128_general_V0_NASRv4", "weight": 1.0}, {"id": 13, "modelFullName": "IntelligentVoice_ja_16kHz_60000_daiwa_V2_NASRv2", "weight": 1.0}]}