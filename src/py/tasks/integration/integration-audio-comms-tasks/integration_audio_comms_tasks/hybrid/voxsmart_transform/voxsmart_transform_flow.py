# mypy: disable-error-code="attr-defined"
import logging
import os
import pandas as pd
import shutil
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as ParamsTriggerWaveFormGenerator,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.message.voxsmart_message.static import (
    VOXSMART_MESSAGE_MAPPINGS_NAME,
    VOXSMART_MESSAGE_SOURCE_SCHEMA,
    VoxsmartMessageSourceColumns,
)
from aries_se_comms_tasks.feeds.voice.voxsmart_voice.static import (
    VOXSMART_VOICE_MAPPINGS_NAME,
    VOXSMART_VOICE_SOURCE_SCHEMA,
    VoxsmartAttachmentColumns,
    VoxsmartVoiceSourceColumns,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as ParamsGetAttachmentMetadata,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.generic.attachment.helper_functions import get_schema_attachments
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    run_link_participants,
)
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_comms_tasks.voice.generic.has_attachment import run_has_attachment
from aries_se_comms_tasks.voice.static import CallColumns, Prefixes
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as ParamsFrameColumnManipulator,
)
from aries_se_core_tasks.frame.frame_column_manipulator import (
    run_frame_column_manipulator,
)
from aries_se_core_tasks.frame.frame_concatenator import (
    Params as ParamsFrameConcatenator,
)
from aries_se_core_tasks.frame.frame_concatenator import (
    run_frame_concatenator,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import (
    Params as ParamsBatchProducer,
)
from aries_se_core_tasks.io.read.batch_producer import (
    run_batch_producer,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import (
    Params as ParamsCsvFileSplitter,
)
from aries_se_core_tasks.io.read.csv_file_splitter import (
    run_csv_file_splitter,
)
from aries_se_core_tasks.io.read.unzip import Params as ParamsUnzip
from aries_se_core_tasks.io.read.unzip import run_unzip
from aries_se_core_tasks.io.write.upload_file import Params as ParamsUploadFile
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.file_utils import delete_local_files
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_audio_comms_tasks.hybrid.voxsmart_transform.input_schema import (
    VoxsmartTransformAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.core.core_dataclasses import (
    CloudAction,
    CloudFile,
    CloudTargetResult,
    ExtractPathResult,
    FileSplitterResult,
)
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
    get_file_uri,
)
from se_data_lake.lake_path import (
    get_ingress_depository_lake_path_for_attachments,
    get_ingress_depository_lake_path_for_waveform,
    get_ingress_depository_lake_path_generic,
)
from se_elastic_schema.models import Call, Message
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from typing import List, Optional, Tuple

logger = logging.getLogger(__name__)

CALL_BATCH_SIZE: int = int(os.environ.get("CALL_BATCH_SIZE", 100))
MESSAGE_BATCH_SIZE: int = int(os.environ.get("MESSAGE_BATCH_SIZE", 1000))


def voxsmart_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    ----------------------------------------------------------
    ||                   VoxsmartTransform                  ||
    ----------------------------------------------------------
    The input to this flow might be any of the following:
    - A ZIP file containing a CSV metadata file and audio mp3 files
    - A ZIP file containing an SMS file.
    - A ZIP file containing an IM file and attachments.
    """
    # Parse and validate AriesTaskInput parameters
    task_transform_input: VoxsmartTransformAriesTaskInput = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=VoxsmartTransformAriesTaskInput
    )

    # Get realm from input file path
    bucket: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Get all the dynamic task definitions from workflow input
    # in this case it is required, "call" and "waveform"
    call_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks["call"]

    waveform_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks["waveform"]

    message_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks["message"]

    all_call_ndjson_paths: List[str] = []
    all_waveform_ndjson_paths: List[str] = []
    all_message_ndjson_paths: List[str] = []

    attachments_upload_path: str = get_ingress_depository_lake_path_for_attachments(
        workflow_name=aries_task_input.workflow.name,
        workflow_trace_id=aries_task_input.workflow.trace_id,
        task_io_params=aries_task_input.input_param.params,
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
    )

    metadata_upload_path: str = get_ingress_depository_lake_path_generic(
        workflow_name=aries_task_input.workflow.name,
        workflow_trace_id=aries_task_input.workflow.trace_id,
        task_io_params=aries_task_input.input_param.params,
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        depository_type="metadata",
    )

    timestamp = datetime.now().strftime("%Y/%m/%d")
    attachments_upload_path = f"{attachments_upload_path.strip('/')}/{timestamp}/"

    zip_local_file_path: str = run_download_file(file_url=task_transform_input.file_uri)

    # Unzip zipped file
    unzipped_file_paths: List[ExtractPathResult] = run_unzip(
        extract_result=zip_local_file_path,
        params=ParamsUnzip(multi_file=True),
    )
    # delete zip file as it is not needed anymore
    os.remove(zip_local_file_path)
    # Determine the Bucket of the tenant
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    if "calls" in task_transform_input.file_uri:
        # Process calls
        call_ndjson_paths, waveform_ndjson_paths = process_calls(
            bucket=bucket,
            tenant=tenant,
            zip_file_uri=task_transform_input.file_uri,
            cloud_provider=cloud_provider,
            chunk_size=CALL_BATCH_SIZE,
            streamed=streamed,
            metadata_upload_path=metadata_upload_path,
            attachments_upload_path=attachments_upload_path,
            unzipped_file_paths=unzipped_file_paths,
            aries_task_input=aries_task_input,
            tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
            cloud_provider_prefix=cloud_provider_prefix,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
        all_call_ndjson_paths.extend(call_ndjson_paths)
        all_waveform_ndjson_paths.extend(waveform_ndjson_paths)

    else:
        # Process Messages: SMS and different types of IM
        message_ndjson_paths = process_messages(
            bucket=bucket,
            tenant=tenant,
            cloud_provider=cloud_provider,
            chunk_size=MESSAGE_BATCH_SIZE,
            streamed=streamed,
            aries_task_input=aries_task_input,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            zip_file_uri=task_transform_input.file_uri,
            tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
            metadata_upload_path=metadata_upload_path,
            attachments_upload_path=attachments_upload_path,
            unzipped_file_paths=unzipped_file_paths,
        )

        all_message_ndjson_paths.extend(message_ndjson_paths)

    call_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in all_call_ndjson_paths],
        task=call_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": Call.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )

    waveform_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in all_waveform_ndjson_paths],
        task=waveform_output_dynamic_task_input,
        common_input_parameters={},
        workflow=aries_task_input.workflow,
    )

    message_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in all_message_ndjson_paths],
        task=message_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": Message.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.CALL: call_output,
            MetaModel.WAVEFORM: waveform_output,
            MetaModel.MESSAGE: message_output,
        },
    )


def process_calls(
    aries_task_input: AriesTaskInput,
    chunk_size: int,
    cloud_provider: CloudProviderEnum,
    zip_file_uri: str,
    unzipped_file_paths: List[ExtractPathResult],
    streamed: bool,
    tenant: str,
    bucket: str,
    attachments_upload_path: str,
    tenant_bucket_with_cloud_prefix: str,
    cloud_provider_prefix: str,
    metadata_upload_path: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> Tuple[List[str], List[str]]:
    tmp_dir: Path = tmp_directory()
    recordings_file_paths: List[Path] = [
        file.path for file in unzipped_file_paths if file.path.suffix == ".mp3"
    ]

    metadata_file_path: Path = [
        file.path for file in unzipped_file_paths if file.path.suffix == ".csv"
    ][0]

    call_ndjson_paths_list: List[str] = []
    waveform_ndjson_paths_list: List[str] = []

    # Upload recordings and create a df
    attachments_df = _upload_attachments_and_get_df(
        bucket=bucket,
        attachments_upload_path=attachments_upload_path,
        attachment_file_paths=recordings_file_paths,
        cloud_provider=cloud_provider,
    )
    metadata_file_upload_result: CloudTargetResult = run_upload_file(
        upload_target=CloudFile(
            file_path=metadata_file_path,
            bucket_name=bucket,
            key_name=f"{metadata_upload_path}{metadata_file_path.name}",
            action=CloudAction.UPLOAD,
        ),
        cloud_provider=cloud_provider,
        params=ParamsUploadFile(),
    )

    metadata_file_uri: str = get_file_uri(
        cloud_provider=cloud_provider,
        key=metadata_file_upload_result.targets[0].key_name,
        bucket=metadata_file_upload_result.targets[0].bucket_name,
    )

    batches: List[FileSplitterResult] = run_csv_file_splitter(
        params=ParamsCsvFileSplitter(
            chunksize=chunk_size,
            detect_encoding=True,
            audit_input_rows=True,
        ),
        csv_path=metadata_file_path.as_posix(),
        realm=bucket,
        sources_dir=tmp_dir.as_posix(),
        streamed=streamed,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )
    for batch_no, csv_chunk_path in enumerate(batches):
        # Read split CSV file and produce a Pandas DataFrame
        dataframe_batch = run_batch_producer(
            params=ParamsBatchProducer(source_schema=VOXSMART_VOICE_SOURCE_SCHEMA),
            file_splitter_result=csv_chunk_path,
            streamed=streamed,
            return_dataframe=True,
        )
        batch_with_attachment_uris = pair_attachment_uri_with_metadata(
            source_frame=dataframe_batch,
            attachments_df=attachments_df,
            attachment_file_name_col_name=VoxsmartAttachmentColumns.ATTACHMENT_FILENAME,
            source_file_name_col_name=VoxsmartVoiceSourceColumns.AUDIO_FILENAME,
        )
        primary_mappings_result = run_get_primary_transformations(
            source_frame=batch_with_attachment_uris,
            flow=VOXSMART_VOICE_MAPPINGS_NAME,
            realm=bucket,
            tenant=tenant,
            zip_file_uri=zip_file_uri,
            metadata_file_uri=metadata_file_uri,
            cloud_provider=cloud_provider,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
        # Get the Attachment head object details from the recording files
        attachment_head_object_df = run_get_attachment_metadata(
            source_frame=batch_with_attachment_uris,
            cloud_provider=cloud_provider,
            params=ParamsGetAttachmentMetadata(
                attachment_column=VoxsmartAttachmentColumns.ATTACHMENT_URI
            ),
        )

        # Add the 'voiceFile' prefix to the attachment columns
        attachment_with_prefix_added_df = run_frame_column_manipulator(
            source_frame=attachment_head_object_df,
            params=ParamsFrameColumnManipulator(action=Action.add, prefix=Prefixes.VOICE_FILE),
        )
        # Concatenate voice and attachment data
        concatenated_calls_result = run_frame_concatenator(
            metadata_df=primary_mappings_result,
            attachments_df=attachment_with_prefix_added_df,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )
        # Populate HasAttachment based on whether an attachment was fetched or not
        has_attachment_result = run_has_attachment(source_frame=concatenated_calls_result)

        # Get the sourceKey, this is used for the audit keys
        call_frame_with_amp_id = run_generate_record_identifiers_for_df(
            source_frame=concatenated_calls_result,
            params=GenerateRecordFileIdentifiersForDfParams(
                data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
        )

        # Enrichment: Link participants based on the created participant identifiers
        participants_result = run_link_participants(
            tenant=tenant,
            source_frame=call_frame_with_amp_id,
            params=LinkParticipantsParams(target_participants_column=CallColumns.PARTICIPANTS),
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            data_models=[Call],
            record_identifier_column="record_identifier",
        )

        # Concatenate the participants' data with the entire call and attachment data
        final_result = run_frame_concatenator(
            call_and_attachment_df=has_attachment_result,
            participants_df=participants_result,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        final_records_length: int = final_result.shape[0]

        if final_records_length > 0:
            # Create the appropriate path where the ndjson result is to be uploaded
            ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.CALL,
                suffix=batch_no,
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=final_result,
                output_filepath=ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            call_ndjson_paths_list.append(ndjson_path)

            try:
                waveform_prefix = get_ingress_depository_lake_path_for_waveform(
                    workflow_name=aries_task_input.workflow.name,
                    workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                    workflow_trace_id=aries_task_input.workflow.trace_id,
                    task_io_params=aries_task_input.input_param.params,
                )

                waveform_path = run_trigger_waveform_generator(
                    source_frame=final_result,
                    realm=bucket,
                    waveform_path=waveform_prefix,
                    params=ParamsTriggerWaveFormGenerator(
                        source_feed=VOXSMART_VOICE_MAPPINGS_NAME,
                        cloud_provider_prefix=cloud_provider_prefix,
                    ),
                )

                waveform_ndjson_paths_list.append(waveform_path)
            except (
                SkipIfMissingRequiredColumns,
                SkipIfSourceFrameEmpty,
                SkipIfFileNotUploaded,
            ) as e:
                # Reset waveform_path to None
                logger.warning(
                    "Setting waveform path to None for cases where there was an error"
                    f"in trigger waveform. Exception: {e}"
                )

    # Delete local files
    delete_local_files(files_to_delete=[file_.path for file_ in unzipped_file_paths])
    shutil.rmtree(tmp_dir)

    return call_ndjson_paths_list, waveform_ndjson_paths_list


@serializer
def pair_attachment_uri_with_metadata(
    source_frame: pd.DataFrame,
    attachments_df: pd.DataFrame,
    attachment_file_name_col_name: str,
    source_file_name_col_name: str,
    add_attachments_col_if_join_unsuccessful: bool = False,
) -> pd.DataFrame:
    """Pair the file remote uri with the metadata. NOTE that if there are
    multiple attachments for one row in the source frame, this will change the
    shape (no. of rows) of the DF.

    :param source_frame: DF containing source data
    :param attachments_df: DF containing attachment URLs and filenames
    :param attachment_file_name_col_name: Name of the file name column
    in the attachment df
    :param source_file_name_col_name: Name of the file name column in
    the source frame
    :param add_attachments_col_if_join_unsuccessful: Adds the col Message.ATTACHMENTS if True (if
    the join wasn't successful
    :return:
    """

    source_frame = source_frame.merge(
        attachments_df,
        left_on=source_file_name_col_name,
        right_on=attachment_file_name_col_name,
        how="left",
    )
    if (
        add_attachments_col_if_join_unsuccessful
        and MessageColumns.ATTACHMENTS not in source_frame.columns
    ):
        source_frame[MessageColumns.ATTACHMENTS] = pd.NA
    return source_frame


def process_messages(
    aries_task_input: AriesTaskInput,
    chunk_size: int,
    cloud_provider: CloudProviderEnum,
    zip_file_uri: str,
    unzipped_file_paths: List[ExtractPathResult],
    streamed: bool,
    tenant: str,
    bucket: str,
    attachments_upload_path: str,
    tenant_bucket_with_cloud_prefix: str,
    metadata_upload_path: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> List[str]:
    tmp_dir: Path = tmp_directory()

    metadata_file_path: Path = [
        file.path for file in unzipped_file_paths if file.path.suffix == ".csv"
    ][0]
    attachment_file_paths: List[Path] = [
        file.path for file in unzipped_file_paths if file.path.suffix != ".csv"
    ]

    message_ndjson_paths_list: List[str] = []

    metadata_file_upload_result: CloudTargetResult = run_upload_file(
        upload_target=CloudFile(
            file_path=metadata_file_path,
            bucket_name=bucket,
            key_name=f"{metadata_upload_path}{metadata_file_path.name}",
            action=CloudAction.UPLOAD,
        ),
        cloud_provider=cloud_provider,
        params=ParamsUploadFile(),
    )

    metadata_file_uri: str = get_file_uri(
        cloud_provider=cloud_provider,
        key=metadata_file_upload_result.targets[0].key_name,
        bucket=metadata_file_upload_result.targets[0].bucket_name,
    )
    # Upload attachments if present
    if attachment_file_paths:
        attachments_df = _upload_attachments_and_get_df(
            bucket=bucket,
            attachments_upload_path=attachments_upload_path,
            attachment_file_paths=attachment_file_paths,
            cloud_provider=cloud_provider,
        )

        # Create  MessageColumns.ATTACHMENTS with the list of dicts expected by the schema
        attachments_not_null_mask = attachments_df.loc[
            :, VoxsmartAttachmentColumns.ATTACHMENT_URI
        ].notnull()
        attachments_df[MessageColumns.ATTACHMENTS] = pd.NA
        attachments_df.loc[attachments_not_null_mask, MessageColumns.ATTACHMENTS] = (
            attachments_df.loc[
                attachments_not_null_mask, VoxsmartAttachmentColumns.ATTACHMENT_URI
            ].apply(lambda x: get_schema_attachments(cloud_file_uris=x))
        )
    else:
        attachments_df = pd.DataFrame(
            columns=[VoxsmartAttachmentColumns.ATTACHMENT_FILENAME, MessageColumns.ATTACHMENTS]
        )

    batches: List[FileSplitterResult] = run_csv_file_splitter(
        params=ParamsCsvFileSplitter(
            chunksize=chunk_size,
            detect_encoding=True,
            audit_input_rows=True,
        ),
        csv_path=metadata_file_path.as_posix(),
        realm=bucket,
        sources_dir=tmp_dir.as_posix(),
        streamed=streamed,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )
    for batch_no, csv_chunk_path in enumerate(batches):
        # Read split CSV file and produce a Pandas DataFrame
        dataframe_batch = run_batch_producer(
            params=ParamsBatchProducer(source_schema=VOXSMART_MESSAGE_SOURCE_SCHEMA),
            file_splitter_result=csv_chunk_path,
            streamed=streamed,
            return_dataframe=True,
        )
        batch_with_attachment_uris = pair_attachment_uri_with_metadata(
            source_frame=dataframe_batch,
            attachments_df=attachments_df,
            attachment_file_name_col_name=VoxsmartAttachmentColumns.ATTACHMENT_FILENAME,
            source_file_name_col_name=VoxsmartMessageSourceColumns.ATTACHMENT_FILENAME,
            add_attachments_col_if_join_unsuccessful=True,
        )
        mappings_result = run_get_primary_transformations(
            source_frame=batch_with_attachment_uris,
            flow=VOXSMART_MESSAGE_MAPPINGS_NAME,
            realm=bucket,
            tenant=tenant,
            zip_file_uri=zip_file_uri,
            metadata_file_uri=metadata_file_uri,
            cloud_provider=cloud_provider,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Get the sourceKey, this is used for the audit keys
        message_frame_with_amp_id = run_generate_record_identifiers_for_df(
            source_frame=mappings_result,
            params=GenerateRecordFileIdentifiersForDfParams(
                data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
        )

        # Enrichment: Link participants based on the created participant identifiers
        participants_result = run_link_participants(
            tenant=tenant,
            source_frame=message_frame_with_amp_id,
            params=LinkParticipantsParams(target_participants_column=CallColumns.PARTICIPANTS),
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            data_models=[Message],
            record_identifier_column="record_identifier",
        )

        # Concatenate the participants' data with the entire call and attachment data
        final_result = run_frame_concatenator(
            call_and_attachment_df=mappings_result,
            participants_df=participants_result,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        final_records_length: int = final_result.shape[0]

        if final_records_length > 0:
            # Create the appropriate path where the ndjson result is to be uploaded
            ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.MESSAGE,
                suffix=batch_no,
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=mappings_result,
                output_filepath=ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            message_ndjson_paths_list.append(ndjson_path)

    # Delete local files
    delete_local_files(files_to_delete=[file_.path for file_ in unzipped_file_paths])
    shutil.rmtree(tmp_dir)
    return message_ndjson_paths_list


def _upload_attachments_and_get_df(
    bucket: str,
    attachments_upload_path: str,
    attachment_file_paths: List[Path],
    cloud_provider: CloudProviderEnum,
):
    # Upload attachment files
    attachment_files_upload_result: CloudTargetResult = run_upload_file(
        upload_target=[
            CloudFile(
                file_path=file_path,
                bucket_name=bucket,
                key_name=f"{attachments_upload_path}{file_path.name}",
                action=CloudAction.UPLOAD,
            )
            for file_path in attachment_file_paths
        ],
        cloud_provider=cloud_provider,
        params=ParamsUploadFile(),
    )
    attachments_df = pd.DataFrame(
        data=[
            {
                VoxsmartAttachmentColumns.ATTACHMENT_URI: get_file_uri(
                    bucket=target.bucket_name,
                    key=target.key_name,
                    cloud_provider=cloud_provider,
                ),
                VoxsmartAttachmentColumns.ATTACHMENT_FILENAME: target.file_path.name,
            }
            for target in attachment_files_upload_result.targets
        ]
    )
    return attachments_df
