# mypy: disable-error-code="attr-defined"
import logging
import re
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as TriggerWaveFormGeneratorParams,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.voice.iv_voice.iv_vendor_combine_files import (
    Params as ParamsIvVendorCombineFiles,
)
from aries_se_comms_tasks.feeds.voice.iv_voice.iv_vendor_combine_files import (
    SkipIfPathsListIsEmpty,
    run_iv_vendor_combine_files,
)
from aries_se_comms_tasks.feeds.voice.iv_voice.static import IV_UNIVERSAL_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.iv_voice.universal.static import (
    FileUrlColumns,
    IvUniversalRegexes,
    IVUniversalSourceColumns,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as GetAttachmentMetadataParams,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.transcription.intelligent_voice.static import IVGetTranscriptsTargetFields
from aries_se_comms_tasks.utilities.translation import (
    TranslationConfigFields,
    get_translation_config,
)
from aries_se_comms_tasks.voice.static import CallColumns, Prefixes
from aries_se_core_tasks.aries.parse_batch_ndjson import run_parse_batch_ndjson
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.frame.filter_columns import Params as ParamsFilterColumns
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.generic.generate_required_amp_fields import (
    Params as ParamsGenerateAmpIdAndHash,
)
from aries_se_core_tasks.generic.generate_required_amp_fields import (
    run_generate_required_amp_fields,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.json_batch_csv_downloader import (
    Params as JsonBatchCsvDownloaderParams,
)
from aries_se_core_tasks.io.read.json_batch_csv_downloader import run_json_batch_csv_downloader
from aries_se_core_tasks.io.read.unzip import Params as ParamsUnzip
from aries_se_core_tasks.io.read.unzip import run_unzip
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config
from aries_se_core_tasks.utilities.file_utils import delete_local_files
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.voice.iv_universal_voice_transform.input_schema import (
    IvUniversalVoiceTransformAriesTaskInput,
)
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_core_tasks.io.read.unzip import FailIfEmptyArchive, SkipIfInputFileIsEmpty, SkipIfNotZip
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_ingress_depository_lake_path_for_attachments,
    get_ingress_depository_lake_path_for_waveform,
)
from se_elastic_schema.models import Call
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional

logger = logging.getLogger(__name__)


class FailIfMissingRecordingFile(TaskException):
    pass


def iv_universal_voice_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    ---------------------------------------------------------------
    ||                 IvUniversalVoiceTransform                ||
    ---------------------------------------------------------------
    """
    # Parse and validate AriesTaskInput parameters
    task_transform_input: IvUniversalVoiceTransformAriesTaskInput = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=IvUniversalVoiceTransformAriesTaskInput
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    # Extract list of zip files to download from input NDJSON
    list_of_zip_uris_to_download: List[str] = run_parse_batch_ndjson(
        transform_input=task_transform_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    file_paths = []
    local_zip_file_paths = []
    # Create recording to zip map. We need this because the metadata source client is
    # derived from the folder path. This dictionary maps from the recording local
    # file path to the original zip file path
    recording_to_zip_map = dict()
    skipped_files_audit = []
    for zipped_file_path in list_of_zip_uris_to_download:
        local_zip_file = run_download_file(file_url=zipped_file_path)

        local_zip_file_paths.append(local_zip_file)

        try:
            current_zip_file_paths: List[ExtractPathResult] = run_unzip(
                extract_result=local_zip_file,
                params=ParamsUnzip(multi_file=True, skip_if_not_zip=True),
            )
        except (SkipIfInputFileIsEmpty, FailIfEmptyArchive, SkipIfNotZip):
            skipped_files_audit.append(zipped_file_path)
            logger.warning(f"Skipping empty or invalid zip file: {zipped_file_path}")
            continue

        try:
            recording_file_url = [
                file_.path.as_posix()
                for file_ in current_zip_file_paths
                if re.search(rf"{IvUniversalRegexes.RECORDING_FILE_REGEX}", file_.path.as_posix())
            ][0]

        except IndexError:
            raise FailIfMissingRecordingFile(
                f"No recording file in foll. zip file: {zipped_file_path}"
            )

        file_paths.extend(current_zip_file_paths)
        recording_to_zip_map[recording_file_url] = zipped_file_path

    if skipped_files_audit:
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            input_data={
                k: {
                    AuditorStaticFields.SKIPPED: 1,
                    AuditorStaticFields.STATUS: ["Skipped empty or invalid zip file"],
                }
                for k in skipped_files_audit
            },
            models=[Call],
        )

    # Will store the generated output paths
    call_ndjson_path: Optional[str] = None
    transcript_ndjson_path: Optional[str] = None
    waveform_path: Optional[str] = None

    # Get the path to upload transcripts and recordings
    attachments_path = get_ingress_depository_lake_path_for_attachments(
        workflow_name=aries_task_input.workflow.name,
        workflow_trace_id=aries_task_input.workflow.trace_id,
        task_io_params=aries_task_input.input_param.params,
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
    ).lstrip("/")

    try:
        combined_df = run_iv_vendor_combine_files(
            extract_path_list=file_paths,
            params=ParamsIvVendorCombineFiles(
                common_pattern_regex=r"^.+",
                recording_file_regex=IvUniversalRegexes.RECORDING_FILE_REGEX,
                transcript_file_regex=IvUniversalRegexes.TRANSCRIPT_FILE_REGEX,
                metadata_file_regex=IvUniversalRegexes.METADATA_FILE_REGEX,
                s3_recording_url_col=IVGetTranscriptsTargetFields.RECORDING_SOURCE_KEY,
                s3_transcript_url_col=IVGetTranscriptsTargetFields.TRANSCRIPT_SOURCE_KEY,
                recording_local_file_col=FileUrlColumns.RECORDING_LOCAL_FILE_PATH,
                metadata_local_file_col=FileUrlColumns.METADATA_LOCAL_FILE_PATH,
                s3_metadata_url_col=FileUrlColumns.METADATA_FILE_URL,
                metadata_in_recording_name=False,
                transcription_status_col=IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS,
            ),
            bucket=realm,
            target_prefix=attachments_path,
            cloud_provider=cloud_provider,
        )
    except SkipIfPathsListIsEmpty:
        logger.warning("No valid files to process.")

    else:
        total_files: int = combined_df.shape[0]

        if total_files > 0:
            # Discard if only recording file paths are present
            call_metadata_result = run_get_rows_by_condition(
                params=GetRowsByConditionParams(
                    query=f"`{FileUrlColumns.METADATA_LOCAL_FILE_PATH}`.notnull()",
                    skip_on_empty=True,
                ),
                source_frame=combined_df,
            )

            # Download all JSON files and use the keys as data frame columns
            json_parsed_result = run_json_batch_csv_downloader(
                params=JsonBatchCsvDownloaderParams(
                    metadata_column=FileUrlColumns.METADATA_LOCAL_FILE_PATH,
                    dataframe_columns=IVUniversalSourceColumns.all(),
                    local_file=True,
                ),
                source_frame=call_metadata_result,
            )

            primary_mappings_result = run_get_primary_transformations(
                source_frame=json_parsed_result,
                flow=IV_UNIVERSAL_FLOW_NAME,
                realm=realm,
                tenant=tenant,
                batch_file_url=task_transform_input.file_uri,
                recording_to_zip_map=recording_to_zip_map,
                flow_name=IV_UNIVERSAL_FLOW_NAME,
            )

            # Get the Attachment head object details from the attachment url
            attachment_head_object_result = run_get_attachment_metadata(
                source_frame=json_parsed_result,
                params=GetAttachmentMetadataParams(
                    attachment_column=IVGetTranscriptsTargetFields.RECORDING_SOURCE_KEY
                ),
                cloud_provider=cloud_provider,
            )

            # Add the 'voiceFile' prefix to the attachment columns
            attachment_with_voice_file_prefix_result = run_frame_column_manipulator(
                source_frame=attachment_head_object_result,
                params=FrameColumnManipulatorParams(action=Action.add, prefix=Prefixes.VOICE_FILE),
            )

            # Concatenate voice and attachment data
            concatenated_calls_result = run_frame_concatenator(
                metadata_df=primary_mappings_result,
                attachment_df=attachment_with_voice_file_prefix_result,
                params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            )

            # Get the sourceKey, this is used for the audit keys
            call_frame_with_amp_id = run_generate_record_identifiers_for_df(
                source_frame=concatenated_calls_result,
                params=GenerateRecordFileIdentifiersForDfParams(
                    data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
            )

            # Enrichment: Link participants based on the create participant identifiers
            participants_result = run_link_participants(
                tenant=tenant,
                source_frame=call_frame_with_amp_id,
                params=LinkParticipantsParams(target_participants_column=CallColumns.PARTICIPANTS),
                streamed=streamed,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                data_models=[Call],
                record_identifier_column="record_identifier",
            )

            # Concatenate the participants' data with the entire call and attachment data
            final_result = run_frame_concatenator(
                call_and_attachment_df=concatenated_calls_result,
                participants_df=participants_result,
                params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            )

            amp_fields_df = run_generate_required_amp_fields(
                source_frame=final_result,
                params=ParamsGenerateAmpIdAndHash(data_model=MetaModel.CALL),
            )

            # Concatenate the participants' data with the entire call and attachment data
            final_df_with_amp_fields = run_frame_concatenator(
                final_df=final_result,
                amp_fields_df=amp_fields_df,
                params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            )

            # Create the appropriate path where the ndjson result is to be uploaded
            call_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.CALL,
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=final_result,
                output_filepath=call_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            # Transcript -- keep only required Call amp fields
            es_client = get_repository_by_cluster_version(resource_config=get_es_config())
            meta_transcript_df = run_filter_columns(
                params=ParamsFilterColumns(
                    action=ActionEnum.keep.value,
                    columns=[
                        es_client.meta.model,
                        es_client.meta.id,
                        es_client.meta.hash,
                    ],
                ),
                source_frame=final_df_with_amp_fields,
            )

            final_transcript_df = run_frame_concatenator(
                meta_transcript_df=meta_transcript_df,
                combined_df_batch=combined_df,
                params=FrameConcatenatorParams(
                    orient=OrientEnum.horizontal,
                    drop_columns=[
                        FileUrlColumns.RECORDING_LOCAL_FILE_PATH,
                        FileUrlColumns.METADATA_LOCAL_FILE_PATH,
                    ],
                ),
            )

            transcript_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.TRANSCRIPT,
            )
            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=final_transcript_df,
                output_filepath=transcript_ndjson_path,
                audit_output=False,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            waveform_prefix = get_ingress_depository_lake_path_for_waveform(
                workflow_name=aries_task_input.workflow.name,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_io_params=aries_task_input.input_param.params,
            )

            try:
                waveform_path = run_trigger_waveform_generator(
                    source_frame=final_result,
                    realm=realm,
                    waveform_path=waveform_prefix,
                    params=TriggerWaveFormGeneratorParams(
                        source_feed=IV_UNIVERSAL_FLOW_NAME,
                        cloud_provider_prefix=cloud_provider_prefix,
                    ),
                )
            except (
                SkipIfMissingRequiredColumns,
                SkipIfSourceFrameEmpty,
                SkipIfFileNotUploaded,
            ) as e:
                # Reset waveform_path to None
                logger.warning(
                    "Setting waveform path to None for cases where there was an error"
                    f"in trigger waveform. Exception: {e}"
                )

        # Delete all temp recording files from the container

        temp_paths = [extract_path.path for extract_path in file_paths]
        delete_local_files(files_to_delete=temp_paths)
        delete_local_files(files_to_delete=local_zip_file_paths)

    # Get translation params from config table
    translation_config: TranslationConfigFields = get_translation_config(
        tenant_name=tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )

    call_output = (
        add_nested_params(
            file_uri=call_ndjson_path,
            es_action=EsActionEnum.INDEX.value,
            data_model="se_elastic_schema.models.tenant.communication.call:Call",
            transcript_file_uri=transcript_ndjson_path,
            translation_to_be_done=translation_config.translation_enabled,
            translation_provider=translation_config.translation_provider,
            translation_source_language=translation_config.translation_source_language,
            translation_target_language=translation_config.translation_target_language,
            transcript_files_ftp=True,
        )
        if call_ndjson_path
        else None
    )

    waveform_output = (
        add_nested_params(
            file_uri=waveform_path,
        )
        if waveform_path
        else None
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.CALL: call_output,
            MetaModel.WAVEFORM: waveform_output,
        },
    )
