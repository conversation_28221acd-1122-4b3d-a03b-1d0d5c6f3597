# mypy: ignore-errors
# ruff: noqa: E501
import concurrent.futures as cf
import gc
import logging
import pandas as pd
import sqlalchemy as sa
import time
from fastapi import HTTPException
from fsspec.core import get_fs_token_paths
from fsspec.spec import AbstractFileSystem
from lexica_light.config import LEXICA_CONFIG, LexicaBaseSettingsCls
from lexica_light.lexica_config_client import SeLexicaConfigClient
from lexica_light.lexica_utils import get_version_tag_for_tenant
from lexica_matcher.lexica_matcher import LexicaMatcher
from lexica_matcher.lexica_model_storage import LexicaModelStorage
from lexica_matcher.lexica_model_wrapper import create_model
from lexica_matcher.schemas.definition import LexicaDefinition
from lexica_matcher.static import CODE_VERSION, LANG_TO_GROUP_MAP, UNIMPLEMENTED_LANGUAGE
from lexica_model_build.config import Settings
from se_db_utils.database import Database
from sqlalchemy import and_, not_, or_, select
from sqlalchemy.sql.expression import cast
from tenant_db.models.lexica.behaviours import Behaviour
from tenant_db.models.lexica.sub_behaviours import SubBehaviour
from tenant_db.models.lexica.terms import Term
from tenant_db.models.lexica.topics import Topic
from typing import Dict, List


def get_langs_with_modifications(pg_db: Database, tenant: str) -> List[str]:
    """Get the languages with modifications for a tenant. This is used to
    determine which languages to build models for.

    :param pg_db: A database connection
    :type pg_db: Database
    :param tenant: The tenant to get the languages for
    :type tenant: str
    :return: The languages with modifications for the tenant
    :rtype: List[str]
    """
    query = (
        f'SELECT DISTINCT LANGUAGE FROM "{tenant}"."Term" '
        f'INNER JOIN "{tenant}"."SubBehaviour" ON "{tenant}"."Term"."subBehaviourId" = "{tenant}"."SubBehaviour".id '
        f'INNER JOIN "{tenant}"."Behaviour" ON "{tenant}"."SubBehaviour"."behaviourId" = "{tenant}"."Behaviour".id '
        f'WHERE "{tenant}"."Behaviour".tenant = \'{tenant}\' AND "{tenant}"."SubBehaviour".tenant = \'{tenant}\' AND "{tenant}"."Term".tenant = \'{tenant}\' '
        f'AND ((("{tenant}"."Term".retired OR "{tenant}"."Term".edited) AND "{tenant}"."Behaviour"."fromSeLexica") OR NOT "{tenant}"."Behaviour"."fromSeLexica");'
    )
    with pg_db.session(tenant) as session:
        mod_langs = [lang[0] for lang in session.execute(query).fetchall()]
    return mod_langs


def get_full_terms(
    pg_db: Database, tenant: str, subscribed_languages: List[str], lowercase_all_input: bool = True
) -> List[LexicaDefinition]:
    """Get the full terms for a tenant (including behaviours and
    sub_behaviours)

    :param pg_db: A database connection
    :type pg_db: Database
    :param tenant: The tenant to get the terms for
    :type tenant: str
    :param subscribed_languages: The languages the tenant is subscribed to
    :type subscribed_languages: List[str]
    :param lowercase_all_input: Whether to lowercase all input, defaults to True
    :type lowercase_all_input: bool, optional
    :return: A list of Lexica Definitions for the tenant
    :rtype: List[LexicaDefinition]
    """
    query = select(
        Term.exclusionSearchType.label("termExclusionSearchType"),
        Term.exclusionTerms.label("termExclusions"),
        Term.exclusionTopics.label("termExclusionTopics"),
        Term.language.label("termLanguage"),
        Term.term.label("termToSearchFor"),
        cast(Term.id.label("termId"), sa.String),
        Term.termBase.label("termBase"),
        Term.termOperatorsFuzziness.label("opFuzziness"),
        Term.termOperatorsProximity.label("opProximity"),
        Term.termOperatorsStem.label("opStemming"),
        Term.termPaired.label("termPaired"),
        cast(Term.termType.label("termType"), sa.String),
        cast(SubBehaviour.behaviourId.label("behaviourId"), sa.String),
        Behaviour.behaviourName.label("behaviour"),
        cast(SubBehaviour.id.label("subBehaviourId"), sa.String),
        SubBehaviour.subBehaviourName.label("subBehaviour"),
    )
    query = query.join(
        SubBehaviour,
        sa.and_(Term.subBehaviourId == SubBehaviour.id, Term.tenant == SubBehaviour.tenant),
    )
    query = query.join(
        Behaviour,
        sa.and_(SubBehaviour.behaviourId == Behaviour.id, SubBehaviour.tenant == Behaviour.tenant),
    )
    query = query.filter(Term.tenant == tenant)
    query = query.filter(and_(~Term.retired, ~SubBehaviour.retired, ~Behaviour.retired))
    query = query.filter(
        or_(
            and_(Term.language.in_(subscribed_languages), Behaviour.fromSeLexica),
            not_(Behaviour.fromSeLexica),
        )
    )

    with pg_db.session(tenant) as session:
        results = session.execute(query).fetchall()

        response = []
        for row in results:
            obj = LexicaDefinition.validate(row)
            if lowercase_all_input:
                obj.termExclusions = (
                    [exclusion.lower() for exclusion in obj.termExclusions]
                    if obj.termExclusions
                    else obj.termExclusions
                )
            response.append(obj)

    return response


def get_topics(pg_db: Database, se_lexica_version: str, tenant: str):
    """Get the lexica topics for a version.

    :param pg_db: A database connection
    :type pg_db: Database
    :param se_lexica_version: The SE Lexica version
    :type se_lexica_version: str
    :return: The lexica topics for the version
    :rtype: Dict[str, Dict[str, List[str]]
    """
    query = select(
        Topic.language,
        sa.func.jsonb_agg(sa.func.jsonb_build_object(Topic.topic, Topic.terms)).label(
            "topic_terms"
        ),
    )
    query = query.filter(Topic.version == se_lexica_version)
    query = query.group_by(Topic.language)

    with pg_db.session(tenant) as session:
        results = session.execute(query).fetchall()

        response = {}
        for row in results:
            response[row.language] = {k: v for d in row.topic_terms for k, v in d.items()}

    return response


def build_lexica_models(
    tenant: str,
    se_lexica_version: str,
    lexica_definitions: List[LexicaDefinition],
    lexica_topics: dict,
):
    """Build the SteelEye Lexica models for a tenant.

    :param tenant: The tenant to build the models for
    :type tenant: str
    :param se_lexica_version: The SE Lexica version
    :type se_lexica_version: str
    :param lexica_definitions: List of Lexica Definitions
    :type lexica_definitions: List[LexicaDefinition]
    :param lexica_topics: The lexica topics
    :type lexica_topics: dict
    :return: The SteelEye Lexica models for the tenant
    """
    # Only pass to MP pool the appropriate language lexica_definitions
    lex_defs_by_lang_group: Dict[tuple, List[LexicaDefinition]] = {}
    languages_by_lang_group: Dict[tuple, List[str]] = {}
    for l_def in lexica_definitions:
        lang_group = LANG_TO_GROUP_MAP.get(l_def.termLanguage, UNIMPLEMENTED_LANGUAGE)
        lex_defs_by_lang_group[lang_group] = lex_defs_by_lang_group.get(lang_group, []) + [l_def]
        languages_by_lang_group[lang_group] = languages_by_lang_group.get(lang_group, []) + [
            l_def.termLanguage
        ]
    del lexica_definitions
    languages_by_lang_group = {k: list(set(v)) for k, v in languages_by_lang_group.items()}

    v_tags_by_lang_group = {}

    for lang_group in set(lex_defs_by_lang_group.keys()):
        languages = "_".join(sorted(list(set(lang_group))))
        v_tags_by_lang_group[lang_group] = f"{tenant}___{se_lexica_version}___{languages}"

    start = time.time()

    models_batch = [
        (
            l_defs,
            lexica_topics,
            lang_group,
            v_tags_by_lang_group[lang_group],
            languages_by_lang_group[lang_group],
        )
        for lang_group, l_defs in lex_defs_by_lang_group.items()
        if l_defs
    ]

    with cf.ProcessPoolExecutor(max_workers=Settings.MULTIPROCESSING_VCPU) as executor:
        lexica_models = executor.map(create_model, models_batch)

    del lex_defs_by_lang_group
    gc.collect()
    logging.info(f"\t\t MultiProcessing  took {round(time.time() - start, 3)}")

    return lexica_models


def build_tenant_models(
    pg_db: Database, tenant: str, se_lexica_config_client: SeLexicaConfigClient
) -> LexicaMatcher:
    """Build the SteelEye Lexica models for a tenant.

    :param pg_db: A database connection
    :type pg_db: Database
    :param tenant: The tenant to build the models for
    :type tenant: str
    :param se_lexica_config_client: A client for the SE Lexica Config API
    :type se_lexica_config_client: SeLexicaConfigClient
    :return: The SteelEye Lexica models for the tenant
    :rtype: LexicaMatcher
    """
    lowercase_all_input = True

    se_lexica_version = se_lexica_config_client.get_se_lexica_version_for_tenant(tenant)
    sub_langs = set(se_lexica_config_client.get_subscribed_languages(tenant))

    modified_langs = get_langs_with_modifications(pg_db, tenant)
    modified_lang_groups = []
    for lang in modified_langs:
        lang_group = LANG_TO_GROUP_MAP.get(lang, UNIMPLEMENTED_LANGUAGE)
        modified_lang_groups.extend(lang_group)

    modified_langs = list(set(modified_lang_groups).intersection(sub_langs))

    lexica_definitions = get_full_terms(pg_db, tenant, modified_langs, lowercase_all_input)
    lexica_topics = get_topics(pg_db, se_lexica_version, tenant)

    return build_lexica_models(tenant, se_lexica_version, lexica_definitions, lexica_topics)


def build_standard_models(version_folder: str):
    """Build the standard SteelEye Lexica models using pandas.

    :param version_folder: The folder containing the SE Lexica version
    :type version_folder: str
    :return: The standard SteelEye Lexica models
    """
    behaviours_path = f"{version_folder}/behaviours.csv"
    sub_behaviours_path = f"{version_folder}/sub_behaviours.csv"
    terms_path = f"{version_folder}/terms.csv"
    topics_path = f"{version_folder}/topics.csv"

    # Topics
    lexica_topics = pd.read_csv(topics_path)
    se_lexica_version = lexica_topics["version"][0]
    lexica_topics["terms"] = lexica_topics["terms"].apply(lambda x: x.split(","))
    lexica_topics = lexica_topics.groupby("language")
    lexica_topics = lexica_topics.apply(lambda x: {d["topic"]: d["terms"] for _, d in x.iterrows()})

    # Behaviours and Sub_behaviours
    behaviours = pd.read_csv(behaviours_path).rename({"id": "behaviourId"}, axis=1)
    sub_behaviours = pd.read_csv(sub_behaviours_path).rename({"id": "subBehaviourId"}, axis=1)
    sub_behaviours = sub_behaviours.merge(behaviours, on="behaviourId")

    # Terms
    rename_dict = {
        "subBehaviourId": "subBehaviourId",
        "exclusionSearchType": "termExclusionSearchType",
        "exclusionTerms": "termExclusions",
        "exclusionTopics": "termExclusionTopics",
        "language": "termLanguage",
        "term": "termToSearchFor",
        "id": "termId",
        "termBase": "termBase",
        "termOperatorsFuzziness": "opFuzziness",
        "termOperatorsProximity": "opProximity",
        "termOperatorsStem": "opStemming",
        "termPaired": "termPaired",
        "termType": "termType",
        "behaviourId": "behaviourId",
        "behaviourName": "behaviour",
        "subBehaviourName": "subBehaviour",
    }
    lexica_definitions = pd.read_csv(terms_path).rename(rename_dict, axis=1)
    lexica_definitions["termExclusions"] = lexica_definitions["termExclusions"].apply(
        lambda x: x.split(",") if isinstance(x, str) else []
    )
    lexica_definitions["termExclusionTopics"] = lexica_definitions["termExclusionTopics"].apply(
        lambda x: x.split(",") if isinstance(x, str) else []
    )
    lexica_definitions = lexica_definitions.merge(sub_behaviours, on="subBehaviourId").rename(
        rename_dict, axis=1
    )
    lexica_definitions = lexica_definitions[list(rename_dict.values())].to_dict(orient="records")

    lexica_definitions = [LexicaDefinition.validate(l_def) for l_def in lexica_definitions]

    return build_lexica_models("steeleye", se_lexica_version, lexica_definitions, lexica_topics)


def validate_model_storage(
    lexica_model_storage: LexicaModelStorage,
    pg_db: Database,
    se_lexica_config_client: SeLexicaConfigClient,
    logger: logging.Logger = logging.Logger("validate_storage"),
):
    """Validate the Lexica Tenant models in storage.

    :param lexica_model_storage: Class to handle the storage of the models
    :type lexica_model_storage: LexicaModelStorage
    :param pg_db: A database connection
    :type pg_db: Database
    :param se_lexica_config_client: A client for the SE Lexica Config API
    :type se_lexica_config_client: SeLexicaConfigClient
    :param logger: Logger, defaults to logging.Logger("validate_storage")
    :type logger: logging.Logger, optional
    :raises e: Any HTTP error in SeLexicaConfigClient is raised
    """
    # get list of tenants to be served
    list_tenants = se_lexica_config_client.get_tenant_list()
    for tenant in list_tenants:
        try:
            # Fetch the most recent version
            se_lexica_version = se_lexica_config_client.get_se_lexica_version_for_tenant(tenant)
            sub_langs = se_lexica_config_client.get_subscribed_languages(tenant)
            latest_tag_available = get_version_tag_for_tenant(
                pg_db, tenant, se_lexica_version, CODE_VERSION, sub_langs
            )
            latest_version_stored = lexica_model_storage.get_version_for_tenant(tenant)

            logger.warning(
                f"Validating tenant '{tenant}': latest_tag_available: '{latest_tag_available}' and latest_version_stored: '{latest_version_stored}' "
            )

            # Check if Stored version is latest
            if (latest_version_stored is not None) and (
                latest_tag_available in latest_version_stored
            ):
                logger.warning(f"Found up-to-date model in storage for tenant '{tenant}'!")
                #  lexica_model_storage.load(tenant, latest_version_stored)  # Uncomment to enable live model validation
            else:
                logger.warning(f"MISSING up-to-date model in storage for tenant '{tenant}'!")
                raise ValueError
        except HTTPException as e:
            if e.status_code == 404:
                logger.warning(
                    f"No SeLexica version registered for tenant '{tenant}'... Skipping it"
                )
            else:
                raise e
        except ValueError:
            # When models storage is missing, create it
            logger.warning(f"No up-to-date model in storage for tenant '{tenant}'. Creating it")
            lexica_models = build_tenant_models(pg_db, tenant, se_lexica_config_client)
            logger.warning(f"Saving models for tenant '{tenant}'...")
            lexica_model_storage.save(
                tenant,
                lexica_models,
                se_lexica_version,
                sub_langs,
                version_tag=latest_tag_available,
            )
            logger.warning(f"Saved models for tenant '{tenant}'!")

        else:
            logger.warning(
                f"Loaded model from storage for tenant '{tenant}', version '{latest_tag_available}'!"
            )


def get_se_versions_folders(config: LexicaBaseSettingsCls, logger: logging.Logger) -> List[str]:
    """Get the list of folders containing the SE Lexica versions.

    :param config: The config (requires LEXICA_STORAGE_PATH)
    :type config: Callable
    :param logger: Logger
    :type logger: logging.Logger
    :return: The list of folders containing the SE Lexica versions
    :rtype: List[str]
    """
    expected_files = set(["behaviours.csv", "sub_behaviours.csv", "terms.csv", "topics.csv"])

    fs, _, (_,) = get_fs_token_paths(config.LEXICA_STORAGE_PATH())
    fs: AbstractFileSystem = fs
    version_folders: List[str] = fs.glob(
        f"{config.LEXICA_STORAGE_PATH()}/ready_to_ingest_lexica/*___*/"
    )

    resp = []
    for v_folder in version_folders:
        v_folder = "s3://" + v_folder
        files_in_cache: List[str] = fs.glob(f"{v_folder}/*.csv")
        if (len(files_in_cache) == len(expected_files)) and (
            expected_files == set([f.split("/")[-1] for f in files_in_cache])
        ):
            resp.append(v_folder)
        else:
            logger.error(f"Found folder with invalid files: '{v_folder}' IGNORING")

    return resp


def validate_standard_models(
    lexica_model_storage: LexicaModelStorage,
    logger: logging.Logger = logging.Logger("build_models"),
):
    """Validate the Standard SteelEye Lexica models.

    :param lexica_model_storage: Class to handle the storage of the models
    :type lexica_model_storage: LexicaModelStorage
    :param logger: Logger, defaults to logging.Logger("build_models")
    :type logger: logging.Logger, optional
    """
    model_folder = f"{LEXICA_CONFIG.MASTER_DATA_MODEL_STORAGE_PATH}/{CODE_VERSION}"

    desired_version = LEXICA_CONFIG.BUILD_ONLY_THIS_VERSION

    list_version_folders = get_se_versions_folders(LEXICA_CONFIG, logger)
    for version_folder in list_version_folders:
        if desired_version and desired_version not in version_folder:
            logger.warning(
                f"Skipping version '{version_folder}' due to BUILD_ONLY_THIS_VERSION env var"
            )
            continue

        # Create models
        logger.warning(f"Creating models for '{version_folder}'")
        lexica_models = build_standard_models(version_folder)

        # Save models
        logger.warning(f"Saving models for '{version_folder}'...")

        lexica_model_storage.save_standard_models(lexica_models, model_folder)
        logger.warning(f"Saved models for '{model_folder}'!")
