import datetime
import json
import logging
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from intelligence_core_tasks.tsurv_copilot.schema import TSurvCopilotMetrics
from mock.mock import MagicMock
from pathlib import Path
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from surveillance_utils.test_mock_helpers import FakeEsRepo
from typing import List
from unittest.mock import AsyncMock

os.environ["OPENAI_API_BASE"] = ""
os.environ["OPENAI_API_KEY"] = ""
os.environ["OPENAI_API_TYPE"] = ""
os.environ["OPENAI_API_VERSION"] = ""
os.environ["COGNITO_CLIENT_ID"] = ""
os.environ["COGNITO_CLIENT_SECRET"] = ""
os.environ["COGNITO_AUTH_URL"] = ""
os.environ["ELASTIC_HOST"] = "fake_host"
os.environ["ELASTIC_API_KEY"] = "API_KEY"

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


@pytest.fixture
def caplog_fixture(caplog):
    caplog.set_level(logging.ERROR)
    return caplog


@pytest.fixture()
def fake_es_repo_instance():
    return FakeEsRepo(
        version=8,
        client_get_path="",
    )


@pytest.fixture
def sample_aries_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_trace_id",
        name="tsurv_copilot",
        stack="test-stack",
        tenant="testtenant",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            scenario_tag_file_uri="/".join(
                [str(TEST_DATA_PATH), "alerts_FRONT_RUNNING_V2_iris_dev.ndjson"]
            ),
            watch_id="test_watch_id",
            watch_execution_id="test_watch_execution_id",
            alert_type=MarketAbuseReportType.FRONT_RUNNING.value,
        )
    )
    task = TaskFieldSet(name="tsurv_copilot", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def test_alert_metrics() -> TSurvCopilotMetrics:
    test_metrics = TSurvCopilotMetrics()
    test_metrics.input_count = 0
    test_metrics.skipped_count = 0
    test_metrics.enriched_trades_alerts_count = 0

    return test_metrics


@pytest.fixture()
def openai_client_init():
    client_mock = MagicMock()
    client_mock.metrics = {"failed": 0, "success": 0, "retries": 0, "billed_tokens": 0}
    return client_mock


@pytest.fixture()
def gpt_response_side_effect() -> List[dict]:
    with open(TEST_DATA_PATH.joinpath("gpt_response_frontrunning.json"), "r") as f:
        gpt_response_side_effect = AsyncMock(side_effect=json.load(f))
    return gpt_response_side_effect
