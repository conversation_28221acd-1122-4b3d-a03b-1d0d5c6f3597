import asyncio
import copy
import json
import logging
import os
import pandas as pd
import re
from copilot_utils.client import SeOpenApiClient
from copilot_utils.config import OPENAI_CONFIG
from intelligence_core_tasks.tsurv_copilot.prompts import TradeSurveillancePromptFactory
from intelligence_core_tasks.tsurv_copilot.schema import TSurvCopilotMetrics
from intelligence_core_tasks.tsurv_copilot.static import (
    JSON_REGEX,
    AIResponseCols,
    CopilotAnalysisSourceFields,
)
from se_elastic_schema.static.case import ResolutionCategoryMarEnum, ResolutionSubCategoryMarEnum
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import List, Optional

logger = logging.getLogger(__name__)
openai_logger = logging.getLogger("openai")
openai_logger.setLevel(logging.WARNING)  # preventing spam of successful openai logs


def alerts_copilot(
    alerts_df: pd.DataFrame,
    market_abuse_alert_type: str,
    metrics: TSurvCopilotMetrics,
    local_prompt_file_path: Optional[str] = None,
    executions_df: Optional[pd.DataFrame] = pd.DataFrame(),
) -> List[Optional[dict]]:
    """Run Copilot process to call chatGPT for every behavioural alert for
    eligible tenants. For each alert:

    1. Check if the tenant is eligible for Copilot Processing and alert is of type Behavioural
    2. Construct the prompt
    3. Call Azure OpenAI chatGTP and get the response
    4. Format the response and add it to the field copilotAnalysis of the
    MarketAbuseScenarioTag model
    """
    # initialize Azure OpenAI client
    client = SeOpenApiClient()

    try:
        # run Copilot
        batch_size = int(os.environ.get("COPILOT_BATCH_SIZE", 10))
        final_alerts = []
        alerts = alerts_df.to_dict(orient="records")
        for i in range(0, len(alerts), batch_size):
            batch = alerts[i : i + batch_size]
            try:
                batch_result = asyncio.run(
                    _copilot_processing(
                        client=client,
                        alerts=copy.deepcopy(batch),
                        market_abuse_alert_type=market_abuse_alert_type,
                        executions_df=executions_df,
                        metrics=metrics,
                        local_prompt_file_path=local_prompt_file_path,
                    )
                )
                final_alerts.extend(batch_result)
            except Exception as e:
                logger.error(f"Error in batch{i}. Copilot failed. Error:{e}")
                final_alerts.extend([None] * batch_size)
        return final_alerts
    except Exception as e:
        logger.exception(f"Copilot Processing failed due to error: {e}")
        return [None] * alerts_df.shape[0]
    finally:
        # Collate LLM Metrics
        metrics.llm_success_count = client.metrics.get("success", 0)
        metrics.llm_retry_count = client.metrics.get("retries", 0)
        metrics.llm_failed_count = client.metrics.get("failed", 0)
        metrics.llm_billed_tokens = client.metrics.get("billed_tokens", 0)


async def _copilot_processing(
    client: SeOpenApiClient,
    alerts: List[dict],
    market_abuse_alert_type: str,
    metrics: TSurvCopilotMetrics,
    local_prompt_file_path: Optional[str],
    executions_df: Optional[pd.DataFrame] = pd.DataFrame(),
) -> List[Optional[dict]]:
    """For each alert construct the prompt and then call Azure OpenAI chatGPT
    asynchronously.

    :param alerts: List[dict]
    :return: List[dict]
    """
    logger.info("Starting Copilot Processing")
    openai_config = []
    prompt_factory = TradeSurveillancePromptFactory(
        market_abuse_alert_type=MarketAbuseReportType(market_abuse_alert_type),
        executions_df=executions_df,
    )
    for alert in alerts:
        try:
            prompt = prompt_factory.get_prompt_generator()(record=alert)
        except Exception as e:
            prompt = ""
            logger.warning(
                f"Error creating prompt for "
                f"Alert ID: {alert.get('scenarioId')} , slug: {alert.get('slug')}. "
                f"Error: {str(e)}"
            )

        openai_config.append(dict(alert=alert, prompt=prompt))
        if local_prompt_file_path:
            with open(local_prompt_file_path, "a") as f:
                f.write(
                    f"Alert ID: {alert.get('scenarioId')} , slug: {alert.get('slug')}"
                    f"\n{prompt}\n\n\n"
                )
    response_list = await _call_open_ai(client=client, openai_config=openai_config, metrics=metrics)
    return response_list  # type: ignore


async def _get_chat_gpt_response(
    client: SeOpenApiClient, index: int, prompt: str, metrics: TSurvCopilotMetrics
) -> Optional[dict]:
    """Call Azure OpenAI ChatGPT with the given prompt and communication and
    gets the json response within the completion response. max_tokens=16000
    because that is the max that the model(specified as engine below) allows.

    :param index: index of the alert in the list
    :param prompt: str
    :param client: SeOpenApiClient
    :param metrics: TSurvCopilotMetrics
    :return: dict
    """

    if not prompt:
        return None

    prompt_messages: list[dict[str, str]] = [
        client.construct_prompt_with_role(
            role="system", prompt_content=TradeSurveillancePromptFactory.PROMPT_SYSTEM_MESSAGE
        ),  # type: ignore[list-item]
        client.construct_prompt_with_role(
            role="user", prompt_content=TradeSurveillancePromptFactory.OUTPUT_PROMPT
        ),  # type: ignore[list-item]
        client.construct_prompt_with_role(role="user", prompt_content=prompt),  # type: ignore[list-item]
    ]
    copilot_analysis = None
    retries = 0
    while not copilot_analysis and retries < 2:
        try:
            response = await client.chat_completion_async(
                prompt_messages=prompt_messages,
                idx=index,
            )
            completion = client.parse_content_from_response(response_list=[response])[0]
            if not completion:
                logger.error("Received empty response from copilot_analysis")
                return copilot_analysis
            completion = completion.lstrip("```json").rstrip("```").replace("\n", "")
            match = re.search(JSON_REGEX, completion)
            if match:
                result = json.loads(match.group(0))
                copilot_analysis = _validate_and_enrich_result(
                    value=result, engine=OPENAI_CONFIG.OPENAI_API_MODEL
                )
            metrics.enriched_trades_alerts_count += 1
        except Exception as e:
            retries += 1
            logger.error(f"ChatGPT Error:{e}")
    return copilot_analysis


async def _call_open_ai(
    client: SeOpenApiClient, openai_config: List[dict], metrics: TSurvCopilotMetrics
):
    """Calls asynchronously the method to call OpenAI ChatGPT.

    :param openai_config: List[dict]
    :return: List[dict]
    """
    tasks = []

    for idx, config in enumerate(openai_config):
        tasks.append(
            _get_chat_gpt_response(
                client=client,
                index=idx,
                prompt=config.get("prompt", ""),
                metrics=metrics,
            )
        )
    re = await asyncio.gather(*tasks)
    return re


def _validate_and_enrich_result(value: dict, engine: str) -> dict:
    """Validates the source dict to its corresponding schema enums and adds
    additional field 'source'.

    :param value: dict
    :return: dict
    """
    # remove extra columns from AI response
    required_keys = [
        val_
        for key_, val_ in AIResponseCols.__dict__.items()
        if not key_.startswith("__") and isinstance(val_, str)
    ]
    value = {key: value for key, value in value.items() if key in required_keys}
    # score is a mandatory column according to schema. So setting default 0
    value.setdefault(AIResponseCols.SCORE, 0)

    conversion_config = [
        {
            "source_field": AIResponseCols.SUGGESTED_RESOLUTION_CATEGORY,
            "schema_enum": ResolutionCategoryMarEnum,
            "schema_type": "string",
            "default_value": None,
        },
        {
            "source_field": AIResponseCols.SUGGESTED_RESOLUTION_SUB_CATEGORIES,
            "schema_enum": ResolutionSubCategoryMarEnum,
            "schema_type": "List",
            "default_value": None,
        },
    ]
    value = _str_to_enum(value=value, conversion_config=conversion_config)
    if isinstance(value.get("otherRisks"), list):
        # sometimes response from chatGPT has empty list [] but it is an Optional[str] as per schema
        value["otherRisks"] = value["otherRisks"][0] if value.get("otherRisks") else None
    value["source"] = {
        CopilotAnalysisSourceFields.SOURCE: "AZURE OPENAI",
        CopilotAnalysisSourceFields.MODEL: OPENAI_CONFIG.OPENAI_API_MODEL,
        CopilotAnalysisSourceFields.VERSION: OPENAI_CONFIG.OPENAI_API_VERSION,
    }
    return value


def _str_to_enum(value: dict, conversion_config: List[dict]) -> dict:
    """Validates and converts the string values to schema enums.

    :param value: dict
    :param conversion_config: List[dict]
    :return: dict
    """
    for config in conversion_config:
        suggested_value = value.get(config["source_field"])
        if not suggested_value:
            value[config["source_field"]] = config["default_value"]
        elif config["schema_type"] == "List":
            allowed_values = config["schema_enum"].__dict__["_member_names_"]
            if not isinstance(value[config["source_field"]], list):
                value[config["source_field"]] = [value[config["source_field"]]]
            value[config["source_field"]] = [
                getattr(config["schema_enum"], x).value
                for x in value[config["source_field"]]
                if x in allowed_values
            ]
            if not value[config["source_field"]]:
                # check for empty list
                value[config["source_field"]] = None
        else:
            if isinstance(suggested_value, list):
                suggested_value = suggested_value[0]
            if not hasattr(config["schema_enum"], suggested_value):
                if isinstance(suggested_value, str) and hasattr(
                    config["schema_enum"], suggested_value.upper()
                ):
                    value[config["source_field"]] = getattr(
                        config["schema_enum"], suggested_value.upper()
                    ).value
                elif isinstance(suggested_value, str) and hasattr(
                    config["schema_enum"], suggested_value.replace(" ", "_").upper()
                ):
                    value[config["source_field"]] = getattr(
                        config["schema_enum"], suggested_value.replace(" ", "_")
                    ).value
                else:
                    value[config["source_field"]] = config["default_value"]
            else:
                value[config["source_field"]] = getattr(
                    config["schema_enum"], suggested_value
                ).value
    return value
