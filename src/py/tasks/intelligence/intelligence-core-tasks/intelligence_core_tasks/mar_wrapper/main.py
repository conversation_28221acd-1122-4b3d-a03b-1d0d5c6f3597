import addict
import datetime
import json
import logging
import traceback
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskR<PERSON>ult
from intelligence_core_tasks.mar_wrapper.data_source.task_data.db import (
    Repository,  # type: ignore[attr-defined]
)
from intelligence_core_tasks.mar_wrapper.email_generator import send_alert_email
from intelligence_core_tasks.mar_wrapper.schemas import MarWrapperInput
from intelligence_core_tasks.mar_wrapper.strategy import (  # type: ignore[attr-defined]
    run_insider_trading_v3,
    run_market_abuse_algorithm,
)
from intelligence_core_tasks.mar_wrapper.utils.utilities import (
    exception_handle_updating_execution,
    notify_slack,
    store_scenarios,
)
from mar_utils.algorithms.common.surveillance_watches import get_watch_filters
from mar_utils.es_filters.date_filters import get_lookback_and_event_day
from mar_utils.es_filters.general import (
    cleanup_exclusion_document,
    create_exclusion_query,
)
from mar_utils.es_filters.static import MarketAbuseReportType
from se_elastic_schema.static.surveillance import WatchConditionType
from se_enums.cloud import CloudProviderEnum
from se_es_utils.slim_record_handler import SlimRecordHandler
from surveillance_utils.config import SURVEILLANCE_SETTINGS
from surveillance_utils.deduplication import AlertDeduplication
from surveillance_utils.surveillance_initialize import initialize_surveillance_watch_run
from surveillance_utils.utils import create_output_path

RECORD_HANDLER = SlimRecordHandler()

logger = logging.getLogger(__name__)
elastic_logger = logging.getLogger("elasticsearch")
elastic_logger.setLevel(logging.WARNING)  # preventing spam of successful elastic logs
botocore_logger = logging.getLogger("botocore")
botocore_logger.setLevel(logging.WARNING)  # preventing spam of successful botocore logs


def process_event(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    tenant: str = aries_task_input.workflow.tenant
    workflow_id: str = aries_task_input.workflow.trace_id
    stack: str = aries_task_input.workflow.stack
    input_task = MarWrapperInput.validate(aries_task_input.input_param.params)

    watch_id: str = input_task.watch_id
    run_as_of: str | None = input_task.run_as_of

    execution_start = datetime.datetime.now(datetime.UTC)

    # Prepare the watch run: Fetch watch record, create watch execution, update watch record
    surv_watch, surv_watch_execution = initialize_surveillance_watch_run(
        watch_id, tenant, workflow_id, RECORD_HANDLER, execution_start
    )
    if input_task.force_lastSuccessfulExecution:
        logger.info(f"Forcing lastSuccessfulExecution {input_task.force_lastSuccessfulExecution}")
        surv_watch.executionDetails.lastSuccessfulExecution = (
            input_task.force_lastSuccessfulExecution
        )

    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider = CloudProviderEnum(cached_tenant_workflow_config.tenant.cloud)
    lake_prefix = cached_tenant_workflow_config.tenant.lake_prefix
    realm = cached_tenant_workflow_config.tenant.realm

    # Creating output_path
    output_path = create_output_path(
        aries_task_input=aries_task_input,
        cloud_provider_enum=cloud_provider,
        file_uri=lake_prefix,
        file_name_diff="alert___",
    )
    output_path_scenario = create_output_path(
        aries_task_input=aries_task_input,
        cloud_provider_enum=cloud_provider,
        file_uri=lake_prefix,
        file_name_diff="scenario___",
    )

    watch_execution_id = surv_watch_execution.id__

    event = addict.Dict(
        realm=realm,
        watch_id=watch_id,
        watch_execution_id=watch_execution_id,
        run_as_of=run_as_of,
        last_run=surv_watch.executionDetails.lastExecution,
        tenant=tenant,
        stack=stack,
        report_type=surv_watch.query.marketAbuseReportType.value,
        trace_id=workflow_id,
    )

    repository = Repository(
        realm=event.realm,
        watch_id=event.watch_id,
        watch_execution_id=event.watch_execution_id,
        watch=surv_watch,
        watch_execution=surv_watch_execution,
        record_handler=RECORD_HANDLER,
        execution_start=execution_start,
        output_path=output_path,
        output_path_scenario=output_path_scenario,
    )

    try:
        thresholds: str = surv_watch.query.thresholds
        logger.info(f"Thresholds to be applied to this execution {thresholds}.")
        deduplication = AlertDeduplication(
            tenant=tenant,
            watch_id=watch_id,
            record_handler=RECORD_HANDLER,
            max_alert_deduplication_size=SURVEILLANCE_SETTINGS.MAX_ALERT_DEDUPLICATION_SIZE,
        )
        oldest_timestamp, look_back_period_ts, event_day_starting_ts = get_lookback_and_event_day(
            tenant=tenant,
            thresholds=thresholds,
            surv_watch=surv_watch,
            execution_start=execution_start,
            record_handler=RECORD_HANDLER,
        )
        if oldest_timestamp is not None:
            exclusion_terms, exclude_doc_id, exclude_doc_parent_id = create_exclusion_query(
                deduplication=deduplication
            )

            date_range_filters, filters = get_watch_filters(
                surv_watch=surv_watch,
                execution_start=execution_start,
                exclusion_terms=exclusion_terms,
                look_back_period_ts=look_back_period_ts,
                run_as_of=input_task.run_as_of,
            )

            logger.info(f"Filters to be applied to this execution {filters}.")

            thresholds_dict: dict[str, str] = json.loads(thresholds)
            logger.info(f"Thresholds to be applied to this execution {thresholds}.")

            if surv_watch.query.marketAbuseReportType not in [
                MarketAbuseReportType.RESTRICTED_LIST
            ]:
                if surv_watch.query.marketAbuseReportType in [
                    MarketAbuseReportType.INSIDER_TRADING_V3,
                    MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV,
                ]:
                    try:
                        scenarios = run_insider_trading_v3(
                            event_day_starting_ts=event_day_starting_ts,
                            thresholds=thresholds_dict,
                            watch=surv_watch,
                            event=event,
                            filters=filters,
                            date_filters=date_range_filters,
                            look_back_period_ts=look_back_period_ts,
                            lake_prefix=lake_prefix,
                        )
                    except Exception as e:
                        raise Exception(f"Error while running insider trading v3. Error {e}")

                else:
                    try:
                        logger.info(
                            f"Executing {surv_watch.query.marketAbuseReportType}"
                            f" since {look_back_period_ts}"
                        )

                        scenarios = run_market_abuse_algorithm(
                            event=event,
                            watch=surv_watch,
                            filters=filters,
                            thresholds=thresholds_dict,
                            look_back_period_ts=look_back_period_ts,
                            lake_prefix=lake_prefix,
                        )
                    except Exception as e:
                        raise Exception(
                            f"Error while running {surv_watch.query.marketAbuseReportType}."
                            f" Error: {e}"
                        )

                try:
                    logger.info(f"strategy returned {len(scenarios)} scenario(s)")
                    # store watch execution and alerts
                    logger.info("store watch execution and alerts")
                    repository = store_scenarios(repository=repository, scenarios=scenarios)
                except Exception as e:
                    raise Exception(f"Error storing scenarios: {e.__class__.__name__} {e}")
        # removing Exclusion Document
        cleanup_exclusion_document(
            deduplication=deduplication,
            exclude_doc_id=exclude_doc_id,
            exclude_doc_parent_id=exclude_doc_parent_id,
        )
        repository.complete_watch_execution()

        condition_type = surv_watch.conditionType

        try:
            if condition_type == WatchConditionType.ON_RUN.value or (
                condition_type == WatchConditionType.ON_ALERT.value and scenarios and any(scenarios)
            ):
                send_alert_email(
                    now=execution_start,
                    realm=event.realm,
                    watch=surv_watch,
                    watch_execution_id=event.watch_execution_id,
                    scenario_count=len(scenarios),
                )
        except Exception as e:
            notify_slack(event=event, error=traceback.format_exc())
            raise Exception(f"Error sending the alert email. Error: {e.__class__.__name__} {e}")

    except Exception as e:
        # cleanup is called again here in case there were any unexpected failures
        cleanup_exclusion_document(
            deduplication=deduplication,
            exclude_doc_id=exclude_doc_id,
            exclude_doc_parent_id=exclude_doc_parent_id,
        )
        exception_handle_updating_execution(repository=repository, event=event)
        logger.info(f"task elapsed {datetime.datetime.now(datetime.UTC) - execution_start}")
        raise Exception(f"epic fail\n{traceback.format_exc()}. Error: {e}")

    output_param = IOParamFieldSet()
    output_param.params["watch_id"] = watch_id
    output_param.params["watch_execution_id"] = watch_execution_id
    output_param.params["copilot_run_flag"] = repository.get_copilot_run_flag()
    output_param.params["alert_type"] = surv_watch.query.marketAbuseReportType.value
    output_param.params["scenario_tag_file_uri"] = repository.scenario_path if scenarios else None
    return AriesTaskResult(output_param=output_param)


def run_mar_wrapper(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    return process_event(aries_task_input)
