from aries_config_api_httpschema.swarm_flow_spec import SwarmFlowSpecCreate, SwarmFlowSpecUpdate
from data_platform_config.containers import Container
from data_platform_config.services.swarm_flow_spec import SwarmFlowSpecService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

swarm_flow_spec_router = APIRouter()


@swarm_flow_spec_router.get("/{bundle_name}")
@inject
def get(
    stack_name: str,
    tenant_name: str,
    bundle_name: str,
    service: SwarmFlowSpecService = Depends(Provide[Container.swarm_flow_spec_service]),
):
    """Returns information about a Swarm Flow Spec for the given tenant.

    :param stack_name:
    :param tenant_name:
    :param bundle_name:
    :param service:
    :return:
    """
    return service.get(stack_name=stack_name, tenant_name=tenant_name, bundle_name=bundle_name)


@swarm_flow_spec_router.post("")
@inject
def add(
    stack_name: str,
    tenant_name: str,
    swarm_flow_spec_create: SwarmFlowSpecCreate,
    service: SwarmFlowSpecService = Depends(Provide[Container.swarm_flow_spec_service]),
):
    """Add a new Swarm Flow Spec for the given tenant.

    :param stack_name:
    :param tenant_name:
    :param swarm_flow_spec_create:
    :param service:
    :return:
    """
    return service.add(
        stack_name=stack_name,
        tenant_name=tenant_name,
        swarm_flow_spec_create=swarm_flow_spec_create,
    )


@swarm_flow_spec_router.put("/{bundle_name}")
@inject
def update(
    stack_name: str,
    tenant_name: str,
    bundle_name: str,
    swarm_flow_spec_update: SwarmFlowSpecUpdate,
    service: SwarmFlowSpecService = Depends(Provide[Container.swarm_flow_spec_service]),
):
    """Update a Swarm Flow Spec for the given tenant.

    :param stack_name:
    :param tenant_name:
    :param bundle_name:
    :param swarm_flow_spec_update:
    :param service:
    :return:
    """
    return service.update(
        stack_name=stack_name,
        tenant_name=tenant_name,
        bundle_name=bundle_name,
        swarm_flow_spec_update=swarm_flow_spec_update,
    )


@swarm_flow_spec_router.delete("/{bundle_name}")
@inject
def delete(
    stack_name: str,
    tenant_name: str,
    bundle_name: str,
    service: SwarmFlowSpecService = Depends(Provide[Container.swarm_flow_spec_service]),
):
    """Delete a Swarm Flow Spec for the given tenant.

    :param stack_name:
    :param tenant_name:
    :param bundle_name:
    :param service:
    :return:
    """
    return service.delete(stack_name=stack_name, tenant_name=tenant_name, bundle_name=bundle_name)
