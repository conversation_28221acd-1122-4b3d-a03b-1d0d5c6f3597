# type: ignore
import pytest
from addict import Dict
from api_sdk.models.elasticsearch import RawResult
from api_sdk.utils.utils import nested_dict_get
from dataclasses import dataclass
from se_api_svc.repository.refine.base import RefineRepository
from se_api_svc.repository.refine.register_model import ENTITIES
from se_schema.all_models import Email, Order
from typing import Union
from unittest.mock import MagicMock


@dataclass
class FakeTenancy:
    realm: str
    userId: str


@dataclass
class FakeRepo:
    tenancy: FakeTenancy

    async def user(self):
        return MagicMock()


@dataclass
class Field:
    id: str
    type: str = "STRING"


def field(field_id, field_type="STRING"):
    return {"fieldId": field_id, "type": field_type}


def fields(*fields: Union[str, Field]):
    def make_field(f):
        if type(f) is Field:
            return field(id=f.id, type=f.type)
        return field(f)

    return {"fields": [field(f) for f in fields]}


class FakeResult:
    def __init__(self, aggregations, hits=[]):
        self.aggregations = aggregations
        self.hits = hits

    def iter_bucket_agg(self, path):
        return [x for x in nested_dict_get(self.aggregations, path + ".buckets")]


async def test_repo_no_model_qs():
    hits = [
        {"key": 123, "doc_count": 30},
        {"key": 456, "doc_count": 20},
    ]

    def fake_get_aggs(*args, **kwargs):
        return FakeResult({"results": {"buckets": hits}})

    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    repo.get_aggs = MagicMock()
    repo.get_aggs.side_effect = fake_get_aggs
    refine = RefineRepository(repo)

    assert list(await refine.suggest(fields("foo"), Order, "foo")) == hits
    assert list(await refine.suggest(fields("foo"), Order, "foo", model_qs="")) == hits


@pytest.mark.asyncio
async def test_repo_with_filter():
    hits = [
        {"key": 123, "doc_count": 30},
        {"key": 456, "doc_count": 20},
    ]

    async def fake_get_aggs(*args, **kwargs):
        return FakeResult({"filter_agg": {"results": {"buckets": hits}}})

    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    repo.get_aggs = MagicMock()
    repo.get_aggs.side_effect = fake_get_aggs
    refine = RefineRepository(repo)

    assert list(await refine.suggest(fields("foo"), Order, "foo", filter="blah")) == hits


@pytest.mark.asyncio
async def test_repo_with_text():
    hits = ["blah blah blah", "yadda yadda"]
    field_name = "body.text"

    async def fake_get_many(*args, **kwargs):
        return FakeResult(
            None, Dict({"hits": [{"_source": {}, "highlight": {field_name: [x]}} for x in hits]})
        )

    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    repo.get_many = MagicMock()
    repo.get_many.side_effect = fake_get_many
    refine = RefineRepository(repo)
    assert (
        list(await refine.suggest(fields(field_name), Email, field_name, model_qs="blah")) == hits
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "field, entity, include_range, response, expected",
    [
        (
            "workflow.escalatedCount",
            "order-alert",
            True,
            {
                "took": 223,
                "timed_out": False,
                "_shards": {"total": 3, "successful": 3, "skipped": 0, "failed": 0},
                "hits": {
                    "total": {"value": 10000, "relation": "gte"},
                    "max_score": None,
                    "hits": [],
                },
                "aggregations": {
                    "OTHER": {
                        "doc_count": 686897,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 686884},
                                {"key": 1, "doc_count": 12},
                                {"key": 2, "doc_count": 1},
                            ],
                        },
                        "MAX": {"value": 2.0},
                        "MIN": {"value": 0.0},
                    },
                    "MAR": {
                        "doc_count": 364267,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 364248},
                                {"key": 1, "doc_count": 17},
                                {"key": 2, "doc_count": 1},
                                {"key": 4, "doc_count": 1},
                            ],
                        },
                        "MAX": {"value": 4.0},
                        "MIN": {"value": 0.0},
                    },
                    "MAR_ALERT": {
                        "doc_count": 963887,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 963431, "scenario_count": {"value": 99302}},
                                {"key": 1, "doc_count": 386, "scenario_count": {"value": 17}},
                                {"key": 2, "doc_count": 66, "scenario_count": {"value": 1}},
                                {"key": 4, "doc_count": 4, "scenario_count": {"value": 1}},
                            ],
                        },
                        "MAX": {"value": 4.0},
                        "MIN": {"value": 0.0},
                    },
                },
            },
            {
                "buckets": [
                    {"key": 0, "count": 1051132},
                    {"key": 1, "count": 29},
                    {"key": 2, "count": 2},
                    {"key": 4, "count": 1},
                ],
                "range": {"max": 4.0, "min": 0.0},
            },
        ),
        (
            "workflow.escalatedCount",
            "order-alert",
            False,
            {
                "took": 223,
                "timed_out": False,
                "_shards": {"total": 3, "successful": 3, "skipped": 0, "failed": 0},
                "hits": {
                    "total": {"value": 10000, "relation": "gte"},
                    "max_score": None,
                    "hits": [],
                },
                "aggregations": {
                    "OTHER": {
                        "doc_count": 686897,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 686884},
                                {"key": 1, "doc_count": 12},
                                {"key": 2, "doc_count": 1},
                            ],
                        },
                        "MAX": {"value": 2.0},
                        "MIN": {"value": 0.0},
                    },
                    "MAR": {
                        "doc_count": 364267,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 364248},
                                {"key": 1, "doc_count": 17},
                                {"key": 2, "doc_count": 1},
                                {"key": 4, "doc_count": 1},
                            ],
                        },
                        "MAX": {"value": 4.0},
                        "MIN": {"value": 0.0},
                    },
                    "MAR_ALERT": {
                        "doc_count": 963887,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {"key": 0, "doc_count": 963431, "scenario_count": {"value": 99302}},
                                {"key": 1, "doc_count": 386, "scenario_count": {"value": 17}},
                                {"key": 2, "doc_count": 66, "scenario_count": {"value": 1}},
                                {"key": 4, "doc_count": 4, "scenario_count": {"value": 1}},
                            ],
                        },
                        "MAX": {"value": 4.0},
                        "MIN": {"value": 0.0},
                    },
                },
            },
            [
                {"key": 0, "count": 1051132},
                {"key": 1, "count": 29},
                {"key": 2, "count": 2},
                {"key": 4, "count": 1},
            ],
        ),
        (
            "workflow.escalatedCount",
            "order-alert",
            True,
            {
                "took": 223,
                "timed_out": False,
                "_shards": {"total": 3, "successful": 3, "skipped": 0, "failed": 0},
                "hits": {
                    "total": {"value": 10000, "relation": "gte"},
                    "max_score": None,
                    "hits": [],
                },
                "aggregations": {
                    "OTHER": {
                        "doc_count": 686897,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [],
                        },
                        "MAX": {"value": None},
                        "MIN": {"value": None},
                    },
                    "MAR": {
                        "doc_count": 364267,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [],
                        },
                        "MAX": {"value": None},
                        "MIN": {"value": None},
                    },
                    "MAR_ALERT": {
                        "doc_count": 963887,
                        "results": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [],
                        },
                        "MAX": {"value": None},
                        "MIN": {"value": None},
                    },
                },
            },
            {"buckets": [], "range": {}},
        ),
    ],
)
async def test_repo_query_suggest_response(field, entity, include_range: bool, response, expected):
    async def fake_get_aggs(*args, **kwargs):
        return RawResult(**response)

    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    repo.get_aggs = MagicMock()
    repo.get_aggs.side_effect = fake_get_aggs
    refine = RefineRepository(repo)
    results = await refine.suggest(
        schema={"fields": [{"fieldId": field, "type": "NUMERIC"}]},
        field_id=field,
        entity_cls=ENTITIES[entity],
        include_range=include_range,
    )
    assert results == expected


def test_filter_term_without_text_analyzer():
    input_field = {"fieldId": "&hash"}
    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    refine = RefineRepository(repo)

    assert refine._filter_term(input_field, {"filter": "blah"}) == {"wildcard": {"&hash": "*blah*"}}


def test_array_obj_case_insensitive_term_aggs_filtering():
    input_field = {"fieldId": "workflow.resolutionSubCategories", "collectionType": "array"}
    repo = FakeRepo(tenancy=FakeTenancy(realm="test", userId="<EMAIL>"))
    refine = RefineRepository(repo)
    assert refine._field_term(input_field, {"filter": "blah"}) == {
        "field": "workflow.resolutionSubCategories",
        "include": ".*[bB][lL][aA][hH].*",
        "min_doc_count": 1,
        "order": {"_key": "asc"},
        "size": 20,
    }
