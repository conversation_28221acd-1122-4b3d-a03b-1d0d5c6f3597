# type: ignore
import pytest
from api_sdk.models.session import Session
from requests.exceptions import HTTPError
from se_api_svc import create_app
from se_api_svc.api.routes.account.people import ListPeopleSummaryResponse
from se_api_svc.api.routes.account.users import ListUsersResponse
from unittest.mock import Mock, patch

NON_SELF_USER_ID = "dan.wood"


def admin_session_dict() -> dict:
    return {
        "principal": "<EMAIL>",
        "&id": "769fa491689d17fa714adfaeec3d3d411ee7dfa1305c46017cf3dfceba8d5f28",
        "&version": 1,
        "roles": ["CONSUMER", "CURATOR", "ADMINISTRATOR"],
        "&ttlExpiry": *************,
        "attributes": {
            "&ID": "vaughn.devilliers_bc85cd38",
            "EMAIL": "<EMAIL>",
            "PERMISSIONS": ["ADMIN"],
            "REALM": "pinafore.dev.steeleye.co",
            "ROLES": ["CONSUMER", "CURATOR", "ADMINISTRATOR"],
            "TENANT": "pinafore",
            "USER_ID": "vaughn.devilliers_bc85cd38",
        },
        "&key": "Session:769fa491689d17fa714adfaeec3d3d411ee7dfa1305c46017cf3dfceba8d5f28:"
        "*************",
        "&hash": "4416b167049e246a477fdc0074273d2a559947a7ba070fc3f2e1e5aaed68ddf3",
        "&model": "Session",
        "&timestamp": *************,
        "&user": "swarm",
    }


def non_admin_session_dict() -> dict:
    session_dict = admin_session_dict()
    session_dict["attributes"]["PERMISSIONS"] = ["MARKET_ABUSE"]
    return session_dict


def admin_session() -> Session:
    return Session(**admin_session_dict())


def non_admin_session() -> Session:
    return Session(**non_admin_session_dict())


@pytest.mark.uses_elastic_repo
def test_lists_user_emails(app, authorized_client):
    resp = authorized_client.get(app.url_path_for("account:users:get-user-emails"))
    resp.raise_for_status()
    user_emails = ListUsersResponse.from_resp(resp.json())

    resp = authorized_client.get(app.url_path_for("account:users:get-admin-emails"))
    resp.raise_for_status()
    admin_emails = ListUsersResponse.from_resp(resp.json())

    assert len(user_emails.results) >= len(admin_emails.results)


@pytest.mark.uses_elastic_repo
def test_lists_people(app, authorized_client):
    resp = authorized_client.get(
        app.url_path_for("account:people:get-all"),
        params=dict(search="a", sort="name:desc"),
    )
    resp.raise_for_status()
    users = ListPeopleSummaryResponse.from_resp(resp.json())
    assert users
    assert len(users.results) >= 2
    assert users.results[0].name > users.results[1].name


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_lists_users_only_returns_self_with_non_admin_permissions(authorized_client):
    """Test the list users functionality when the user has non-admin
    permissions.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Get the list of users.
    3. Assert that only one user is returned, and the email of the user matches the non-admin
    user's email.
    """
    resp = authorized_client.get(authorized_client.app.url_path_for("account:users:list"))
    resp.raise_for_status()
    assert len(resp.json()["results"]) == 1
    assert resp.json()["results"][0]["email"] == non_admin_session().attributes.EMAIL


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_lists_users_works_with_admin_permissions(authorized_client):
    """Test the list users functionality when the user has admin permissions.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Get the list of users.
    3. Assert that more than one user is returned.
    """
    resp = authorized_client.get(authorized_client.app.url_path_for("account:users:list"))
    resp.raise_for_status()
    assert len(resp.json()["results"]) > 1


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_create_user_fails_with_invalid_email(authorized_client):
    """Test the create user functionality with an invalid email address.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Attempt to create a new user with an invalid email address.
    3. Assert that the API returns a 422 status code with a message indicating that the email
     address is invalid.
    """
    resp = authorized_client.post(
        authorized_client.app.url_path_for("account:users:create"),
        json={"email": "invalid-email", "password": "password", "roles": ["CONSUMER"]},
    )
    assert resp.status_code == 422
    assert resp.json() == {"detail": [{"ctx": None, "loc": None, "msg": "Invalid email address"}]}


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_create_user_fails_with_non_admin_permissions(authorized_client):
    """Test the create user functionality when the user has non-admin
    permissions.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to create a new user with non-admin permissions.
    3. Assert that an HTTPError is raised, indicating that the non-admin user cannot create a new
    user.
    """
    with pytest.raises(HTTPError):
        resp = authorized_client.post(
            authorized_client.app.url_path_for("account:users:create"),
            json={
                "mfaStatus": "DISABLED",
                "language": "English",
                "name": "Vaughn de Villiers",
                "email": "<EMAIL>",
                "mobileNumber": "",
                "countryCode": "GB",
                "permissions": ["MARKET_ABUSE", "COMMS_SURVEILLANCE"],
                "registered": *************,
                "userId": "vaughndevilliers",
            },
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_get_user_works_when_self_and_non_admin_permissions(authorized_client):
    """Test the get user functionality when the user is a non-admin and
    attempts to retrieve their own information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Get the user information for the non-admin user themself.
    3. Assert that the retrieved user's email matches the non-admin user's email.
    """
    user_id = non_admin_session().attributes.USER_ID
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    resp.raise_for_status()
    assert resp.json()["email"] == non_admin_session().attributes.EMAIL


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_get_user_fails_with_non_admin_permissions(authorized_client):
    """Test the get user functionality when the user is a non-admin and
    attempts to retrieve information about another user who is not themself.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to get the user information for a specific user ID.
    3. Assert that an HTTPError is raised, indicating that the non-admin user cannot retrieve
    information about another
       user who is not themself.
    """
    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        resp = authorized_client.get(
            authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_get_user_works_when_not_self_and_admin_permissions(authorized_client):
    """Test the get user functionality when the user is an admin and attempts
    to retrieve information about another user who is not themself.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Get the user information for a specific user ID.
    3. Assert that the retrieved user's email matches the expected email for the specified user ID.
    """
    user_id = NON_SELF_USER_ID
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    resp.raise_for_status()
    assert resp.json()["email"] == f"{user_id}@steel-eye.com"


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_get_user_works_when_self_and_admin_permissions(authorized_client):
    """Test the get user functionality when the user is an admin and attempts
    to retrieve information about another user.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Get the user information for a non-admin user.
    3. Assert that the retrieved user's email matches the non-admin user's email.
    """
    user_id = non_admin_session().attributes.USER_ID
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    resp.raise_for_status()
    assert resp.json()["email"] == non_admin_session().attributes.EMAIL


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_edit_user_works_when_self_and_admin_permissions(authorized_client):
    """Test the edit user functionality when the user is an admin and attempts
    to edit their own information.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Get the user information for a self as an admin user.
    3. Modify the user's mobile number.
    4. Edit the user's information as an admin user.
    5. Assert that the response status is "UPDATED" and the user's mobile number has been updated.
    """
    user_id = admin_session().attributes.USER_ID
    user_resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    user_resp.raise_for_status()
    user = user_resp.json()
    assert user["email"] == non_admin_session().attributes.EMAIL
    new_mobile_number = str(int(user["mobileNumber"]) + 1)
    user["mobileNumber"] = new_mobile_number

    resp = authorized_client.put(
        authorized_client.app.url_path_for("account:users:edit", user_id=user_id),
        json=user,
    )
    resp.raise_for_status()
    assert resp.json()["status"] == "UPDATED"
    assert (
        authorized_client.get(
            authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
        ).json()["mobileNumber"]
        == new_mobile_number
    )


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_edit_user_fails_when_self_and_non_admin_permissions(authorized_client):
    """Test the edit user functionality when the user is a non-admin and
    attempts to edit their own information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Get the user information for the non-admin user.
    3. Modify the user's mobile number.
    4. Attempt to edit the non-admin user's own information.
    5. Assert that an HTTPError is raised, indicating that the non-admin user cannot edit their
     own information.
    """
    user_id = non_admin_session().attributes.USER_ID
    user_resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    user_resp.raise_for_status()
    user = user_resp.json()
    assert user["email"] == non_admin_session().attributes.EMAIL
    new_mobile_number = str(int(user["mobileNumber"]) + 1)
    user["mobileNumber"] = new_mobile_number

    with pytest.raises(HTTPError):
        resp = authorized_client.put(
            authorized_client.app.url_path_for("account:users:edit", user_id=user_id),
            json=user,
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_edit_user_works_with_admin_permissions(authorized_client):
    """Test the edit user functionality when the user is an admin and attempts
    to edit another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Get the user information for a specific user ID.
    3. Modify the user's mobile number.
    4. Edit the user's information as an admin user.
    5. Assert that the response status is "UPDATED" and the user's mobile number has been updated.
    """

    user_id = NON_SELF_USER_ID
    user_resp = authorized_client.get(
        authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
    )
    user_resp.raise_for_status()
    user = user_resp.json()
    assert user["email"] == f"{user_id}@steel-eye.com"
    new_mobile_number = str(int(user.get("mobileNumber") or "***********") + 1)
    user["mobileNumber"] = new_mobile_number

    resp = authorized_client.put(
        authorized_client.app.url_path_for("account:users:edit", user_id=user_id),
        json=user,
    )
    resp.raise_for_status()
    assert resp.json()["status"] == "UPDATED"
    assert (
        authorized_client.get(
            authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
        ).json()["mobileNumber"]
        == new_mobile_number
    )


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_edit_user_fails_with_non_admin_permissions(authorized_client):
    """Test the edit user functionality when the user is not an admin and
    attempts to edit another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Get the user information for a specific user ID.
    3. Modify the user's mobile number.
    4. Attempt to edit the user's information as a non-admin user.
    5. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to edit another
       user's information.
    """
    with patch.object(
        create_app.ElasticsearchAuthBackend,
        "get_stored_session_by_token",
        new=Mock(return_value=admin_session()),
    ):
        user_id = NON_SELF_USER_ID
        user_resp = authorized_client.get(
            authorized_client.app.url_path_for("account:user:get", user_identifier=user_id)
        )

    user_resp.raise_for_status()
    user = user_resp.json()
    assert user["email"] == f"{user_id}@steel-eye.com"
    new_mobile_number = str(int(user.get("mobileNumber") or "***********") + 1)
    user["mobileNumber"] = new_mobile_number

    with pytest.raises(HTTPError):
        resp = authorized_client.put(
            authorized_client.app.url_path_for("account:users:edit", user_id=user_id),
            json=user,
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_delete_self_fails_with_non_admin_permissions(authorized_client):
    """Test the delete user functionality when the user is a non-admin and
    attempts to delete their own information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to delete the non-admin user's own information.
    3. Assert that an HTTPError is raised, indicating that the non-admin user cannot delete their
    own information.
    """
    user_id = non_admin_session().attributes.USER_ID

    with pytest.raises(HTTPError):
        resp = authorized_client.delete(
            authorized_client.app.url_path_for("account:users:delete", user_id=user_id)
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_delete_user_fails_with_non_admin_permissions(authorized_client):
    """Test the delete user functionality when the user is a non-admin and
    attempts to delete another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to delete the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to delete another
       user's information.
    """
    user_id = NON_SELF_USER_ID
    with pytest.raises(HTTPError):
        resp = authorized_client.delete(
            authorized_client.app.url_path_for("account:users:delete", user_id=user_id)
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_lock_unlock_user_fails_for_non_admin_permissions(authorized_client):
    """Test the lock user functionality when the user is not an admin and
    attempts to lock another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to lock the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to lock another
       user's information.
    4. then try the same with unlock, expecting the same error
    """
    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        user_resp = authorized_client.post(
            authorized_client.app.url_path_for(
                "account:users:lock-unlock", user_identifier=user_id, lock_status="lock"
            )
        )
        user_resp.raise_for_status()

    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        user_resp = authorized_client.post(
            authorized_client.app.url_path_for(
                "account:users:lock-unlock",
                user_identifier=user_id,
                lock_status="unlock",
            )
        )
        user_resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_deactivate_user_mfa_fails_for_non_admin_permissions(authorized_client):
    """Test the deactivate user mfa functionality when the user is a non-admin
    and attempts to deactivate mfa for another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to deactivate mfa for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
     permission to deactivate mfa for another user's information.
    """
    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        resp = authorized_client.post(
            authorized_client.app.url_path_for("account:users:deactivate-mfa", user_id=user_id)
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_register_user_mfa_fails_for_non_admin_permissions(authorized_client):
    """Test the register user mfa functionality when the user is a non-admin
    and attempts to register mfa for another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to register mfa for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to register mfa for
       another user's information.
    """
    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        resp = authorized_client.post(
            authorized_client.app.url_path_for("account:users:register-mfa", user_id=user_id)
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_resend_user_activation_fails_for_non_admin_permissions(authorized_client):
    """Test the resend user activation functionality when the user is a non-
    admin and attempts to resend activation for another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to resend activation for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
     permission to resend activation
       for another user's information.
    """
    with pytest.raises(HTTPError):
        user_id = NON_SELF_USER_ID
        resp = authorized_client.post(
            authorized_client.app.url_path_for(
                "account:users:resend-activation", user_identifier=user_id
            )
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_get_case_manager_users_fails_for_non_admin_permissions(authorized_client):
    """Test the get case manager users functionality when the user is a non-
    admin and attempts to get case manager users for another user's
    information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to get case manager users for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to get case manager
       users for another user's information.
    """
    with pytest.raises(HTTPError):
        user_resp = authorized_client.get(
            authorized_client.app.url_path_for("account:users:list-case-managers")
        )
        user_resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_get_case_manager_users_works_for_admin_permissions(authorized_client):
    """Test the get case manager users functionality when the user is an admin
    and attempts to get case manager users information.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Attempt to get case manager users for the user's information as an admin user.
    3. Assert that the response is successful.
    """
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:users:list-case-managers")
    )
    resp.raise_for_status()
    assert resp.status_code == 200
    assert len(resp.json()["results"]) > 1


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_get_admin_emails_fails_for_non_admin_permissions(authorized_client):
    """Test the get admin emails functionality when the user is a non-admin and
    attempts to get admin emails for another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to get admin emails for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
    permission to get admin
       emails for another user's information.
    """
    with pytest.raises(HTTPError):
        resp = authorized_client.get(
            authorized_client.app.url_path_for("account:users:get-admin-emails")
        )
        resp.raise_for_status()


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_get_admin_emails_works_for_admin_permissions(authorized_client):
    """Test the get admin emails functionality when the user is an admin and
    attempts to get admin emails information.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Attempt to get admin emails for the user's information as an admin user.
    3. Assert that the response is successful.
    """
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:users:get-admin-emails")
    )
    resp.raise_for_status()
    assert resp.status_code == 200
    assert len(resp.json()["results"]) > 1


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=non_admin_session()),
)
def test_get_user_emails_only_returns_self_if_non_admin_permissions(authorized_client):
    """Test the get user emails functionality when the user is a non-admin and
    attempts to get user emails for another user's information.

    This test case performs the following steps:
    1. Mock the authorization backend to return a non-admin session.
    2. Attempt to get user emails for the user's information as a non-admin user.
    3. Assert that an HTTPError is raised, indicating that the non-admin user does not have
     permission to get user
       emails for another user's information.
    """
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:users:get-user-emails")
    )
    resp.raise_for_status()
    assert resp.status_code == 200
    assert len(resp.json()["results"]) == 1
    assert resp.json()["results"][0]["email"] == non_admin_session().attributes.EMAIL


@pytest.mark.uses_elastic_repo
@patch.object(
    create_app.ElasticsearchAuthBackend,
    "get_stored_session_by_token",
    new=Mock(return_value=admin_session()),
)
def test_get_user_emails_works_for_admin_permissions(authorized_client):
    """Test the get user emails functionality when the user is an admin and
    attempts to get user emails information.

    This test case performs the following steps:
    1. Mock the authorization backend to return an admin session.
    2. Attempt to get user emails for the user's information as an admin user.
    3. Assert that the response is successful.
    """
    resp = authorized_client.get(
        authorized_client.app.url_path_for("account:users:get-user-emails")
    )
    resp.raise_for_status()
    assert resp.status_code == 200
    assert len(resp.json()["results"]) > 1
