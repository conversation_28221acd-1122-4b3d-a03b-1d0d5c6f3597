import datetime as dt
import json
import logging
import pydantic
from api_sdk.auth import Tenancy
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from api_sdk.di.request import ReqDep
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.schemas.base import APIModel
from datetime import time
from fastapi import APIRouter, Body, Depends, HTTPException
from se_api_svc.messages.surveillance.watch_execution.commands import RunWatchExecutionCommand
from se_api_svc.messages.surveillance.watch_execution.events import WatchExecuted
from se_api_svc.repository.comms_surveillance.static import DEFAULT_BYOM_WATCH
from se_api_svc.repository.surveillance.executions import WatchExecutionsRepository
from se_api_svc.schemas.surveillance.watches import SurveillanceWatchExecution, Watch
from se_elastic_schema.models.tenant.surveillance.communication_alert import (
    CommunicationAlert as CommunicationAlertEs,
)
from typing import Any, List, Optional

router = APIRouter()

log = logging.getLogger(__name__)


class CommunicationAlert(CommunicationAlertEs):
    def convert_times_to_iso(self, obj: Any) -> Any:
        if isinstance(obj, dict):
            return {k: self.convert_times_to_iso(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_times_to_iso(item) for item in obj]
        elif isinstance(obj, time):
            return obj.isoformat()
        return obj

    def to_es_dict(self):
        data = super().to_dict()
        return self.convert_times_to_iso(data)


class ExecutionPayload(APIModel):
    fileUri: Optional[str] = None
    records: Optional[List[CommunicationAlert]] = None
    createNewWatch: bool = False

    @pydantic.root_validator
    def validate_input(cls, values):
        file_uri, records = values.get("fileUri"), values.get("records")
        if not file_uri and not records:
            raise ValueError("Either 'fileUri' or 'records' must be provided.")
        return values


def extract_and_validate_records(file_uri, blob_client) -> (List[CommunicationAlert], int):
    bucket, key, _ = blob_client.extract_bucket_key_filename(file_uri)

    stream_downloader = blob_client.get_object(bucket=bucket, key=key)
    blob_content = stream_downloader.read()

    validated_records = []
    skipped_records = 0
    for record in blob_content.decode("utf-8").splitlines():
        record = json.loads(record)
        try:
            validated_records.append(CommunicationAlert(**record))
        except pydantic.ValidationError as e:
            skipped_records += 1
            log.error(f"Invalid record: {record}, error: {e}")

    log.info(f"Skipped {skipped_records} records with failed validation.")

    return validated_records, skipped_records


@router.post(
    "", name="run-watch-execution", response_model=SurveillanceWatchExecution
)  # DS-32: Endpoint for BYOM Alert creation
async def run_watch_execution(
    payload: ExecutionPayload = Body(...),
    mb: FastApiMessageBus = Depends(),
    blob_client: AbstractStorageClient = ReqDep(AbstractStorageClient),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    """
    Execute a Watch Workflow for Alert Processing.

    This endpoint processes a batch of alert records and executes a watch workflow asynchronously.

    **Request Body:**

    - ``fileUri`` (Optional[str]): The URI of a file containing alert records. (.ndjson file only)
    - ``records`` (Optional[List[CommunicationAlert]]): A list of alert records.
    - ``watch`` (Optional[Watch]): A custom watch to run workflow, BYOM watch is used by default.

    **Process Flow:**

    1. Extracts alert records from the provided ``records`` list or fetches them from ``fileUri``.
    2. Validates the extracted records against the Elasticsearch schema.
    3. Creates a watch configuration if none is provided.
    4. Creates a watch execution record with status **QUEUED**.
    5. Triggers the watch execution workflow asynchronously, which:
       - Updates execution status to **IN_PROGRESS**.
       - Enriches alert records with watch details.
       - Ingests records into Elasticsearch.
       - Updates execution status to **COMPLETED** or **ERROR** on failure.
    """

    records, skipped_records = (
        (payload.records, None)
        if payload.records
        else extract_and_validate_records(payload.fileUri, blob_client)
    )

    if not any(records):
        raise HTTPException(status_code=400, detail="No validated records found for execution.")

    watch = Watch(createdBy=tenancy.userId, createdOn=dt.datetime.utcnow(), **DEFAULT_BYOM_WATCH)

    res: WatchExecuted = await mb.request(
        RunWatchExecutionCommand(
            records=records,
            watch=watch,
            createNewWatch=payload.createNewWatch,
            skippedRecords=bool(skipped_records),
            recordModel=CommunicationAlert,
        )
    )

    return res.execution.to_dict()


@router.get(
    "/{watch_execution_id}",
    name="surveillance:watches:get-execution",
)
async def get_watch_execution(
    watch_execution_id: str,
    repo: WatchExecutionsRepository = ReqDep(WatchExecutionsRepository),
):
    """
    Retrieve a Watch Execution Record.

    This endpoint retrieves the details of a previously executed watch using its unique ID.

    **Path Parameters:**

    - ``watch_execution_id`` (str): The unique identifier of the watch execution.

    **Response:**

    - Returns the execution details of the watch, including its status, timestamps, and metadata.

    **Errors:**

    - **404 Not Found**: If no execution is found with the provided ``watch_execution_id``.
    """

    execution = await repo.get_watch_execution(watch_execution_id)

    if not execution:
        raise HTTPException(status_code=404, detail="Watch execution not found")

    return execution
