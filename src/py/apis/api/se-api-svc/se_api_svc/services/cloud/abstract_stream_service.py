import boto3
import io
import json
import logging
import numpy as np
import re
import shlex
import subprocess
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from api_sdk.cloud.client.s3_storage_client import S3StorageClient
from api_sdk.exceptions import NotFound
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.utils.utils import nested_dict_get
from boto3.s3.transfer import TransferConfig
from fastapi import HTTPException, Request, status
from fastapi.responses import StreamingResponse
from scipy import signal
from scipy.io.wavfile import read as read_wav
from se_api_svc.schemas.comms import Call
from se_api_svc.schemas.comms.meeting import Meeting
from se_elastic_schema.components.reference.file_info import FileInfo, FileLocation
from se_elastic_schema.static.comms.meeting import RecordingTypeEnum
from typing import Dict, List, Optional, Tuple, Union

log = logging.getLogger(__name__)


class MissingObjectError(Exception):
    pass


class AudioBufferGenerationError(Exception):
    pass


class LargeAudioFileException(Exception):
    pass


def get_range_header(range_header: str, file_size: int) -> Tuple[int, int]:
    def _invalid_range():
        return HTTPException(
            status.HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE,
            detail=f"Invalid request range (Range:{range_header!r})",
        )

    try:
        h = range_header.replace("bytes=", "").split("-")
        start = int(h[0]) if h[0] != "" else 0
        end = int(h[1]) if h[1] != "" else file_size - 1
    except ValueError:
        raise _invalid_range()

    if start > end or start < 0 or end > file_size - 1:
        raise _invalid_range()
    return start, end


class AbstractStreamingService:
    WAVEFORM_PATH_PREFIX = "attachments/api_waveform"
    UNKNOWN_SOURCE_FEED = "unknown_source_feed"

    EXTENSION_TO_MIME = {"wav": "audio/wav", "mp3": "audio/mpeg"}

    def __init__(self, repo: RequestBoundRepository, cloud_client: AbstractStorageClient):
        self.repo = repo
        self.cloud_client = cloud_client

    @staticmethod
    def get_file_location_call(call: Call) -> FileLocation:
        try:
            return call.voiceFile.fileInfo.location
        except AttributeError:
            raise NotFound(message="file not found")

    @staticmethod
    def get_meeting_timeline_file_location(meeting: Meeting) -> FileLocation:
        try:
            return meeting.meetingDetails.participants_timeline.location
        except AttributeError:
            raise NotFound(message="file not found")

    @staticmethod
    def get_file_location_meeting(
        meeting: Meeting, recording_type: RecordingTypeEnum
    ) -> FileLocation:
        try:
            temp = [
                recording for recording in meeting.recordings if recording.type == recording_type
            ]
            recording = temp[0]
            transform_attachment = meeting.get_key_as_attachment(recording.sourceKey)
            return transform_attachment.fileInfo.location
        except (AttributeError, IndexError):
            raise NotFound(message="file not found")

    @staticmethod
    def get_file_size_of_audio_record(meeting: Meeting) -> int:
        """Will return the audio file size in mb."""
        try:
            temp = [
                recording
                for recording in meeting.recordings
                if recording.type == RecordingTypeEnum.AUDIO_ONLY
            ]
            recording = temp[0]
            return int(recording.file.sizeInBytes / (1024 * 1024))
        except (AttributeError, IndexError):
            raise NotFound(message="file not found")

    @staticmethod
    def get_waveform_file_location(call: Call) -> FileLocation:
        try:
            return call.waveform.location
        except AttributeError:
            raise NotFound(message="file not found")

    @staticmethod
    def normalize_array(array: np.ndarray) -> List[float]:
        """
        :param array: np.ndarray
        :return: np.ndarray
        Normalizes input ndarray to a range (0-1) and returns the normalized array.
        """
        abs_array = np.abs(array)
        max_x = np.max(abs_array)
        normalized_array = np.array([x / max_x for x in abs_array])

        # if stereo audio then take data from one channel
        if np.ndim(normalized_array) > 1:
            audio_data = [x[0] for x in normalized_array]
        else:
            audio_data = normalized_array.tolist()

        return audio_data

    async def get_comm_record(self, comm_id) -> Union[Call, Meeting, None]:
        return await self.repo.get_one(record_model=[Call, Meeting], id=comm_id)

    async def get_call_record(self, call_id) -> Union[Call, None]:
        return await self.repo.get_one(record_model=[Call], id=call_id)

    def get_file_meta_data(self, bucket: str, key: str):
        return self.cloud_client.get_file_meta_data(bucket=bucket, key=key)

    def send_bytes_range_requests(self, file_location: FileLocation, start: int, end: int):
        """Send a file in chunks using Range Requests specification RFC7233.

        `start` and `end` parameters are inclusive due to specification
        """
        return self.cloud_client.get_object_bytes_range(
            bucket=file_location.bucket, key=file_location.key, start=start, end=end
        )

    async def range_requests_response_for_call(self, call_id, request: Request):
        """Returns StreamingResponse using Range Requests of a given file."""
        call = await self.get_comm_record(call_id)
        location: FileLocation = self.get_file_location_call(call)
        file_size, content_type = self.cloud_client.get_file_size_type(
            bucket=location.bucket, key=location.key
        )
        range_header = request.headers.get("range")

        headers = {
            "content-type": content_type,
            "accept-ranges": "bytes",
            "content-encoding": "identity",
            "content-length": str(file_size),
            "access-control-expose-headers": (
                "content-type, accept-ranges, content-length, content-range, content-encoding"
            ),
        }
        start = 0
        end = file_size - 1
        status_code = status.HTTP_200_OK

        if range_header is not None:
            start, end = get_range_header(range_header, file_size)
            size = end - start + 1
            headers["content-length"] = str(size)
            headers["content-range"] = f"bytes {start}-{end}/{file_size}"
            status_code = status.HTTP_206_PARTIAL_CONTENT

        return StreamingResponse(
            self.send_bytes_range_requests(file_location=location, start=start, end=end),
            headers=headers,
            status_code=status_code,
        )

    async def get_presigned_url_of_voicefile(self, call_id):
        call: Call = await self.get_comm_record(call_id)
        file_location = self.get_file_location_call(call)

        duration_of_call = (
            call.callDuration.hour * 60 + call.callDuration.minute
        ) * 60 + call.callDuration.second
        expires_in = max(60, int(duration_of_call * 1.5))
        _, content_type = self.cloud_client.get_file_size_type(
            bucket=file_location.bucket, key=file_location.key
        )

        if content_type == "binary/octet-stream":
            ext = file_location.key.split(".")[-1].lower()
            content_type = self.EXTENSION_TO_MIME.get(ext, content_type)

        return self.generate_presigned_url(file_location, expires_in, content_type)

    def generate_presigned_url(
        self, file_location: FileLocation, expires_in: int = 3600, content_type: str = None
    ):
        return self.cloud_client.get_pre_signed_url(
            bucket=file_location.bucket,
            key=file_location.key,
            expires_in=expires_in,
            content_type=content_type,
        )

    def get_audio_buffer(self, audio_file_location):
        file_obj = self.cloud_client.get_object(
            bucket=audio_file_location.bucket, key=audio_file_location.key, obj=True
        )

        try:
            sample_rate, audio_buffer = read_wav(io.BytesIO(file_obj))

        except ValueError:
            try:
                presigned_url = self.generate_presigned_url(audio_file_location, 10)
                p1 = subprocess.run(
                    shlex.split(f"ffmpeg -y -i {presigned_url} -acodec pcm_s32le -f wav -"),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                )

                sample_rate, audio_buffer = read_wav(io.BytesIO(p1.stdout))
            except Exception as e:
                log.info(
                    f"Error generating waveform for "
                    f"{audio_file_location.bucket}/{audio_file_location.key}. Error:{e}"
                )
                raise AudioBufferGenerationError

        except Exception as e:
            log.info(
                f"Error generating waveform for "
                f"{audio_file_location.bucket}/{audio_file_location.key}. Error:{e}"
            )
            raise AudioBufferGenerationError

        return audio_buffer

    def get_waveform_file_path(self, call: Dict):
        call_id = call.get("id")
        if not call_id:
            call_id = call.get("&id").split(":")[0]
        client = nested_dict_get(call, "metadata.source.client")
        if not client:
            client = self.UNKNOWN_SOURCE_FEED
        client = "-".join(re.findall(r"\w+", client.lower()))

        return f"{self.WAVEFORM_PATH_PREFIX}/{client}/{call_id}/waveform.json"

    @staticmethod
    def get_audio_stream(bucket, key, get_size: bool = False):
        s3 = boto3.client("s3")
        obj = s3.get_object(Bucket=bucket, Key=key)
        if get_size:
            return obj["ContentLength"]
        return obj["Body"]

    async def get_normalized_audio_buffer(
        self, audio_file_location: FileLocation, chunk_size: int = 65536
    ) -> np.array:
        """Get the normalize audio buffer which is generated by reading file in
        chunks.

        @param audio_file_location: FileLocation obj pointing to s3 object
        @param chunk_size: each audio_stream chunk size which defaults to 65536 ie 64kb
        @return: numpy array which is an array of waveform points
        """
        audio_stream = self.get_audio_stream(
            bucket=audio_file_location.bucket, key=audio_file_location.key
        )
        resampled_audio = []
        while True:
            audio_chunk = audio_stream.read(chunk_size)
            if not audio_chunk:
                break

            audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
            resampled_chunk = signal.resample(audio_data, 2000)
            min_value = np.min(resampled_chunk)
            max_value = np.max(resampled_chunk)

            if min_value == max_value:
                max_value = min_value + 1

            normalized_chunk = (resampled_chunk - min_value) / (max_value - min_value)

            resampled_audio.append(normalized_chunk)

        average_audio = np.mean(resampled_audio, axis=0)

        normalized_audio_buffer = (average_audio - np.min(average_audio)) / (
            np.max(average_audio) - np.min(average_audio)
        )

        return normalized_audio_buffer

    async def _handle_aws_wave_form_generation(self, call_id: str):
        call = await self.get_call_record(call_id)

        try:
            file_location: FileLocation = self.get_waveform_file_location(call)
            waveform_file_obj = self.cloud_client.get_object(
                bucket=file_location.bucket, key=file_location.key, obj=True
            )
            file_content = waveform_file_obj.decode("utf-8")
            return json.loads(file_content)

        except Exception as e:
            log.info(f"Not found waveform file, trying to generate from audio file. Error : {e}")

        audio_file_location: FileLocation = self.get_file_location_call(call)
        audio_file_size_bytes = self.get_audio_stream(
            bucket=audio_file_location.bucket, key=audio_file_location.key, get_size=True
        )
        audio_file_size_mb = audio_file_size_bytes // (1024 * 1024)

        # Raise exception if audio size > 250 mb
        # NOTE: If there's such case ask waveform
        #       generation from ingestion avoid from api
        if audio_file_size_mb > 250:
            raise LargeAudioFileException("File Size exceeds 250 MB")

        try:
            # If file size is greater than 35 MB
            # Instead of throwing error try generating waveform in chunks
            # See ENG-8618 for more info.

            if audio_file_size_mb > 35:
                audio_buffer = await self.get_normalized_audio_buffer(
                    audio_file_location=audio_file_location, chunk_size=65536
                )

            else:
                audio_buffer = self.get_audio_buffer(audio_file_location)
            # resample to limit number of samples to params.number_of_samples
            data = signal.resample(audio_buffer, 2000)
            wave_form = self.normalize_array(data)

            data_obj = {"waveform_data": wave_form}

            try:
                json_path = self.get_waveform_file_path(call.to_dict())

                config = TransferConfig(
                    multipart_threshold=1024 * 25,
                    max_concurrency=10,
                    multipart_chunksize=1024 * 25,
                    use_threads=True,
                )

                byte_data_stream = io.BytesIO(bytes(json.dumps(data_obj).encode("UTF-8")))

                with byte_data_stream as data_stream:
                    self.cloud_client.client.upload_fileobj(
                        Fileobj=data_stream,
                        Bucket=audio_file_location.bucket,
                        Key=json_path,
                        Config=config,
                    )

                call.waveform = FileInfo(
                    location=FileLocation(bucket=audio_file_location.bucket, key=json_path)
                )
                await self.repo.save_existing(call, datetime_to_str=True)
            except Exception as e:
                log.info(f"Error while saving data to s3 and updating call record : {e}")

            return data_obj
        except AudioBufferGenerationError:
            raise NotFound

    async def get_or_generate_waveform(self, call_id: str):
        if isinstance(self.cloud_client, S3StorageClient):
            return await self._handle_aws_wave_form_generation(call_id)

        call = await self.get_call_record(call_id)

        try:
            file_location: FileLocation = self.get_waveform_file_location(call)
            waveform_file_obj = self.cloud_client.get_object(
                bucket=file_location.bucket, key=file_location.key, obj=True
            )
            file_content = waveform_file_obj.decode("utf-8")
            return json.loads(file_content)

        except Exception as e:
            log.info(f"Not found waveform file, trying to generate from audio file. Error : {e}")

        audio_file_location: FileLocation = self.get_file_location_call(call)

        try:
            audio_buffer = self.get_audio_buffer(audio_file_location)

            # resample to limit number of samples to params.number_of_samples
            data = signal.resample(audio_buffer, 2000)
            wave_form = self.normalize_array(data)

            data_obj = {"waveform_data": wave_form}

            try:
                json_path = self.get_waveform_file_path(call.to_dict())
                json_obj = bytes(json.dumps(data_obj).encode("UTF-8"))
                self.cloud_client.upload_object(
                    Body=json_obj, bucket=audio_file_location.bucket, key=json_path
                )
                call.waveform = FileInfo(
                    location=FileLocation(bucket=audio_file_location.bucket, key=json_path)
                )

                await self.repo.save_existing(call, datetime_to_str=True)
            except Exception as e:
                log.info(f"Error while saving data to s3 and updating call record : {e}")

            return data_obj
        except AudioBufferGenerationError:
            raise NotFound

    async def get_presigned_url_of_meeting_records(self, meeting_id, record_id) -> Optional[str]:
        meeting: Meeting = await self.get_comm_record(meeting_id)

        for record in meeting.recordings:
            if record.file.id == record_id:
                time_duration = (
                    record.timeDuration.hour * 60 + record.timeDuration.minute
                ) * 60 + record.timeDuration.second
                expires_in = max(60, int(time_duration * 1.5))
                att = meeting.get_key_as_attachment(record.sourceKey)

                return self.generate_presigned_url(
                    file_location=att.fileInfo.location, expires_in=expires_in
                )

        return None

    async def get_or_generate_waveform_for_meeting(self, meeting_id: str):
        meeting = await self.get_comm_record(meeting_id)

        try:
            file_location: FileLocation = self.get_waveform_file_location(meeting)
            waveform_file = self.cloud_client.get_object(
                bucket=file_location.bucket, key=file_location.key, obj=True
            )
            file_content = waveform_file.decode("utf-8")
            return json.loads(file_content)

        except Exception as e:
            log.info(f"Not found waveform file, trying to generate from audio file. Error : {e}")

        audio_file_location: FileLocation = self.get_file_location_meeting(
            meeting, RecordingTypeEnum.AUDIO_ONLY
        )

        if self.get_file_size_of_audio_record(meeting) > 35:
            raise LargeAudioFileException("File Size exceeds 35 MB")

        try:
            audio_buffer = self.get_audio_buffer(audio_file_location)

            # resample to limit number of samples to params.number_of_samples
            data = signal.resample(audio_buffer, 2000)
            wave_form = self.normalize_array(data)

            data_obj = {"waveform_data": wave_form}
            json_path = f"{audio_file_location.key}.json"

            try:
                json_obj = bytes(json.dumps(data_obj).encode("UTF-8"))
                self.cloud_client.upload_object(
                    Body=json_obj, bucket=audio_file_location.bucket, key=json_path
                )

                meeting.waveform = FileInfo(
                    location=FileLocation(bucket=audio_file_location.bucket, key=json_path)
                )

                await self.repo.save_existing(meeting, datetime_to_str=True)
            except Exception as e:
                log.info(f"Error while saving data to s3 and updating call record : {e}")

            return data_obj
        except AudioBufferGenerationError:
            raise NotFound

    async def get_timeline_for_meeting(self, meeting_id: str):
        meeting = await self.get_comm_record(meeting_id)

        try:
            file_location: FileLocation = self.get_meeting_timeline_file_location(meeting)
            timeline_file = self.cloud_client.get_object(
                bucket=file_location.bucket, key=file_location.key, obj=True
            )
            file_content = timeline_file.decode("utf-8")
            return json.loads(file_content)

        except Exception as e:
            log.info(f"Not found waveform file, trying to generate from audio file. Error : {e}")
            raise NotFound
