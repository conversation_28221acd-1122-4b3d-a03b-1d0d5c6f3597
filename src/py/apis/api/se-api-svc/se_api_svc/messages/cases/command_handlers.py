# type: ignore
import datetime as dt
import logging
from api_sdk.es_dsl.utils import json_dumps
from api_sdk.messages.base import MessageBus
from api_sdk.repository.asyncronous.request_bound import TenantConfiguration
from api_sdk.utils.meta import remove_invalid_ref_meta
from email.mime.text import MIMEText
from generator.case_manager.case_manager_email_generator import (
    CaseAssignmentEmailGenerator,
    CaseClosedEmailGenerator,
    CaseReopenedEmailGenerator,
)
from se_api_svc.core.config import config
from se_api_svc.messages.cases.commands import (
    Assign<PERSON>ase<PERSON>ommand,
    DeleteCaseCommand,
    ReopenCaseCommand,
    ResolveCaseCommand,
    TransferOwnershipCaseCommand,
)
from se_api_svc.messages.cases.events import (
    CaseDeleted,
    CaseOwnershipTransferedNotification,
    CaseOwnershipTransferred,
    CaseReassigned,
    CaseReassignedNotification,
    CaseReopened,
    CaseReopenedNotification,
    CaseResolved,
    CaseResolvedNotification,
)
from se_api_svc.messages.common.command_handlers import get_surveillance_record_models
from se_api_svc.messages.registry import registry
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.repository.cases import CaseRecordRepository, CasesRepository
from se_api_svc.schemas.account import AccountUser
from se_api_svc.schemas.cases.cases import Case, CaseWorkflow, ResolveCaseIn
from se_api_svc.schemas.cases.event import SystemEvent
from se_elastic_schema.static.case import Status as CaseStatus
from se_elastic_schema.static.surveillance import AlertHitStatus
from typing import Dict, List, Tuple

log = logging.getLogger(__name__)


def get_case_recipients(case: Case) -> List[str]:
    recipients = []

    if hasattr(case, "assignee") and hasattr(case.assignee, "email"):
        recipients.append(case.assignee.email)

    if hasattr(case, "owner") and hasattr(case.assignee, "email"):
        recipients.append(case.assignee.email)

    return recipients


def generate_reopen_message(
    realm: str,
    case: Case,
    event_time: dt.datetime,
    reopener: str,
    comms_count: int,
    orders_count: int,
    trades_count: int,
) -> MIMEText:
    generator: CaseReopenedEmailGenerator = CaseReopenedEmailGenerator(realm=realm)

    return generator.generate_email(
        case_id=case.id_,
        case_name=case.name,
        case_status=case.status,
        reopened_datetime=event_time,
        opener=reopener,
        case_origination=case.origin,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
    )


def generate_resolution_message(
    realm: str,
    case: Case,
    event_time: dt.datetime,
    resolver: str,
    resolution_details: ResolveCaseIn,
    comms_count: int,
    orders_count: int,
    trades_count: int,
) -> MIMEText:
    generator: CaseClosedEmailGenerator = CaseClosedEmailGenerator(realm=realm)

    return generator.generate_email(
        case_id=case.id_,
        case_name=case.name,
        case_status=case.status,
        closed_datetime=event_time,
        closer=resolver,
        closed_by_name=resolver,
        resolution_comment=resolution_details.comment,
        resolution_category=resolution_details.category,
        case_origination=case.origin,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
    )


def generate_assignment_message(
    realm: str,
    case: Case,
    event_time: dt.datetime,
    assignee: str,
    assignee_type: str,
    assign_reason: str,
    assign_reason_comment: str,
    assigner: str,
    comms_count: int,
    orders_count: int,
    trades_count: int,
) -> MIMEText:
    generator: CaseAssignmentEmailGenerator = CaseAssignmentEmailGenerator(realm=realm)

    return generator.generate_email(
        case_id=case.id_,
        case_name=case.name,
        case_status=case.status,
        assignment_datetime=event_time,
        assignee=assignee,
        assignee_type=assignee_type,
        assign_reason=assign_reason,
        assign_reason_comment=assign_reason_comment,
        case_origination=case.origin,
        assigner=assigner,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
    )


def resolve_alerts_of_case(
    repo: CasesRepository,
    case_id: str,
    resolution_details: ResolveCaseIn,
    timestamp: dt.datetime,
    tenant_config: TenantConfiguration,
):
    index = repo.index_for_record_model(get_surveillance_record_models(tenant_config=tenant_config))
    if not index:
        log.info("No alerts to resolve. Tenant not subscribed to Csurv/Tsurv")
        return
    timestamp_ms = int(timestamp.timestamp() * 1000)

    user = repo.tenancy.userId

    status = AlertHitStatus.RESOLVED_WITH_INVESTIGATION

    if resolution_details.withBreach:
        status = AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH

    script = f"""
        ctx._source.workflow.resolutionCategory = {repr(resolution_details.category.value)};
        ctx._source.workflow.resolvedComment = {repr(resolution_details.comment)};
        ctx._source.workflow.resolvedViaCase = true;
        ctx._source.workflow.resolvedByName = {repr(repo.tenancy.user_name)};
        ctx._source.workflow.status = {repr(status.value)};
        ctx._source.workflow.updatedBy = {repr(user)};
        ctx._source['&timestamp'] = {timestamp_ms}L;
        ctx._source['&user'] = {repr(user)};
        ctx._source['resolved'] = {timestamp_ms}L;
    """

    if resolution_details.customCategory:
        script = (
            script + "ctx._source.workflow.resolutionCategoryCustom = "
            f"{repr(resolution_details.customCategory)};"
        )

    body = {
        "script": {"source": script, "lang": "painless"},
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [{"term": {"workflow.caseId": case_id}}],
            }
        },
    }

    if config.DEBUG:
        log.debug(f"Against {index}, executing query: {json_dumps(body)}")

    # The CommunicationAlert model is irrelevant here, it's only being used to
    # obtain the index for executing the update_by_query
    repo.es_client.update_by_query(
        index=index,
        body=body,
        refresh=True,
    )


def generate_system_event(
    repo: CasesRepository,
    case_id: str,
    event_repo,
    user: str,
    tenant_config: TenantConfiguration,
):
    body = {
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [{"term": {"workflow.caseId": case_id}}],
            }
        },
    }

    index = repo.index_for_record_model(get_surveillance_record_models(tenant_config=tenant_config))

    # The CommunicationAlert model is irrelevant here, it's only being used to
    # obtain the index for executing the update_by_query
    res = repo.es_client.search(
        index=index,
        body=body,
    )

    for hit in res["hits"]["hits"]:
        identifier = (
            hit.get("_source", {}).get("slug")
            if hit.get("_source", {}).get("slug")
            else hit.get("_id")
        )
        event_repo.add_system_event(
            case_id=case_id,
            system_event=SystemEvent.AlertResolved,
            description=f"Alert {identifier} resolved via case {case_id}",
            user=user,
        )


def update_alerts_of_case(
    repo: CasesRepository,
    case_id: str,
    new_alert_status: str,
    timestamp: dt.datetime,
    tenant_config: TenantConfiguration,
):
    timestamp_ms = int(timestamp.timestamp() * 1000)

    user = repo.tenancy.userId

    status = new_alert_status

    script = f"""
        ctx._source.workflow.status = {repr(status.value)};
        ctx._source['&timestamp'] = {timestamp_ms}L;
        ctx._source['&user'] = {repr(user)};
    """

    if status == AlertHitStatus.UNDER_INVESTIGATION:
        script += """
            ctx._source.workflow.resolutionCategory = null;
            ctx._source.workflow.resolvedComment = null;
            ctx._source.workflow.resolvedViaCase = null;
            ctx._source.workflow.updatedBy = null;
            ctx._source.workflow.resolutionCategoryCustom = null;
            ctx._source['resolved'] = null;
        """
    elif status == AlertHitStatus.UNRESOLVED:
        script += """
            ctx._source.workflow.caseId = null;
            ctx._source.workflow.caseSlug = null;
        """

    body = {
        "script": {"source": script, "lang": "painless"},
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [{"term": {"workflow.caseId": case_id}}],
            }
        },
    }

    index = repo.index_for_record_model(get_surveillance_record_models(tenant_config=tenant_config))

    if config.DEBUG:
        log.debug(f"Against {index}, executing query: {json_dumps(body)}")

    # The CommunicationAlert model is irrelevant here, it's only being used to
    # obtain the index for executing the update_by_query
    repo.es_client.update_by_query(
        index=index,
        body=body,
        refresh=True,
    )


def get_count(model_count: Dict) -> Tuple[int, int, int]:
    comms_count = int(
        model_count["CaseCall"]
        + model_count["CaseEmail"]
        + model_count["CaseMessage"]
        + model_count["CaseText"]
    )
    orders_count = int(model_count["CaseOrder"])
    trades_count = int(model_count["CaseOrderState"])
    return comms_count, orders_count, trades_count


@registry.command_handler
async def resolve_case(
    command: ResolveCaseCommand,
    mb: MessageBus,
    repo: CasesRepository,
    record_repo: CaseRecordRepository,
):
    model_count = record_repo.case_model_count(command.case_id)
    (comms_count, orders_count, trades_count) = get_count(model_count)

    # There will be three steps to this process:
    event_time = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

    command.case.update_from(
        command.resolution_details.to_case_update(
            timestamp=event_time,
            user_id=repo.tenancy.userId,
            user_name=repo.tenancy.user_name,
            legal_hold=command.case.legal_hold,
        )
    )

    # Step 1. Update the case with the resolution details/status
    await repo.save_existing(command.case)

    # Step 2. Update any surveillance alerts associated with this case using an update_by_query
    tenant_config: TenantConfiguration = await repo.get_one(TenantConfiguration)

    resolve_alerts_of_case(
        repo=repo,
        case_id=command.case_id,
        resolution_details=command.resolution_details,
        timestamp=event_time,
        tenant_config=tenant_config,
    )

    # Step 3. Send an email, notifying the Case "actors" (creator, assignee, owner).
    notification_message = generate_resolution_message(
        realm=repo.realm,
        case=command.case,
        event_time=event_time,
        resolver=repo.tenancy.user_name,
        resolution_details=command.resolution_details,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
    )

    await mb.publish(
        CaseResolved(
            case=command.case,
            caseSlug=command.case.slug,
            closedComment=command.case.closedComment,
            closedResolutionCategory=command.case.closedResolutionCategory,
        )
    )

    await mb.publish(
        CaseResolvedNotification(
            case=command.case,
            message=notification_message,
            recipients=get_case_recipients(command.case),
        )
    )


@registry.command_handler
async def reopen_case(
    command: ReopenCaseCommand,
    mb: MessageBus,
    repo: CasesRepository,
    record_repo: CaseRecordRepository,
):
    model_count = record_repo.case_model_count(command.case_id)
    (comms_count, orders_count, trades_count) = get_count(model_count)

    # There will be three steps to this process:
    event_time = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

    command.case.status = CaseStatus.OPEN

    # Step 1. Update the case with the resolution details/status
    await repo.save_existing(command.case)

    # Step 2. Update any surveillance alerts associated with this case using an update_by_query
    tenant_config: TenantConfiguration = await repo.get_one(TenantConfiguration)

    update_alerts_of_case(
        repo=repo,
        case_id=command.case_id,
        new_alert_status=AlertHitStatus.UNDER_INVESTIGATION,
        timestamp=event_time,
        tenant_config=tenant_config,
    )

    # TODO: confirm if the reverse of the below still needs to happen

    # Step 3. Send an email, notifying the Case "actors" (creator, assignee, owner).

    notification_message = generate_reopen_message(
        realm=repo.realm,
        case=command.case,
        event_time=event_time,
        reopener=repo.tenancy.user_name,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
    )

    await mb.publish(CaseReopened(case=command.case, case_slug=command.case.slug))

    await mb.publish(
        CaseReopenedNotification(
            case=command.case,
            message=notification_message,
            recipients=get_case_recipients(command.case),
        )
    )


@registry.command_handler
async def delete_case(command: DeleteCaseCommand, mb: MessageBus, repo: CasesRepository):
    # Update any surveillance alerts associated with this case using an update_by_query
    event_time = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

    tenant_config: TenantConfiguration = await repo.get_one(TenantConfiguration)

    update_alerts_of_case(
        repo=repo,
        case_id=command.case_id,
        new_alert_status=AlertHitStatus.UNRESOLVED,
        timestamp=event_time,
        tenant_config=tenant_config,
    )
    # Update the case with the deleteBy details
    command.case.deleted = dt.datetime.utcnow()
    command.case.deletedBy = repo.tenancy.userId
    await repo.save_existing(command.case)

    await repo.delete_existing(record=command.case)

    await mb.publish(CaseDeleted(case_id=command.case.id_, case=command.case))


@registry.command_handler
async def assign_case(
    command: AssignCaseCommand,
    mb: MessageBus,
    repo: CasesRepository,
    user_repo: UserRepository,
    record_repo: CaseRecordRepository,
):
    model_count = record_repo.case_model_count(command.case_id)
    (comms_count, orders_count, trades_count) = get_count(model_count)

    event_time = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

    # get the new assignee:
    user: AccountUser = await user_repo.get_one(AccountUser, command.assign_details.user_id)
    user = AccountUser.sanitize_account_user(user)

    command.case.assignee = user

    if not command.case.workflow:
        command.case.workflow = []

    command.case.workflow.append(
        CaseWorkflow(
            user=remove_invalid_ref_meta(await repo.user_embeddable()),
            event=CaseWorkflow.Event.ASSIGNEE_ASSIGNED,
            timestamp=int(event_time.timestamp() * 1000),
        )
    )
    notification_message = generate_assignment_message(
        realm=repo.realm,
        case=command.case,
        event_time=event_time,
        assignee=user.name,
        assignee_type="assignee",
        assigner=repo.tenancy.user_name,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
        assign_reason="NA",
        assign_reason_comment="NA",
    )

    await repo.save_existing(command.case)

    audit_description = (
        f"{user.name} has been assigned to Case, created by {command.case.owner.name} "
        f"with the id of '{command.case.slug}' by '{repo.tenancy.user_name}' "
    )
    await mb.publish(
        CaseReassigned(
            case=command.case,
            audit=dict(description=audit_description),
        )
    )

    await mb.publish(
        CaseReassignedNotification(
            case=command.case,
            message=notification_message,
            recipients=get_case_recipients(command.case),
        )
    )


@registry.command_handler
async def transfer_ownership_case(
    command: TransferOwnershipCaseCommand,
    mb: MessageBus,
    repo: CasesRepository,
    user_repo: UserRepository,
    record_repo: CaseRecordRepository,
):
    model_count = record_repo.case_model_count(command.case_id)
    (comms_count, orders_count, trades_count) = get_count(model_count)

    event_time = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

    old_owner = command.case.owner

    # get the new owner:
    user: AccountUser = await user_repo.get_one(AccountUser, command.transfer_details.user_id)
    user = AccountUser.sanitize_account_user(user)

    command.case.owner = user

    if not command.case.workflow:
        command.case.workflow = []

    command.case.workflow.append(
        CaseWorkflow(
            user=remove_invalid_ref_meta(await repo.user_embeddable()),
            event=CaseWorkflow.Event.OWNER_ASSIGNED,
            timestamp=int(event_time.timestamp() * 1000),
        )
    )

    notification_message = generate_assignment_message(
        realm=repo.realm,
        case=command.case,
        event_time=event_time,
        assignee=user.name,
        assignee_type="owner",
        assigner=repo.tenancy.user_name,
        comms_count=comms_count,
        orders_count=orders_count,
        trades_count=trades_count,
        assign_reason="NA",
        assign_reason_comment="NA",
    )

    await repo.save_existing(command.case)

    old_owner_name = old_owner.name if old_owner else "NA"
    audit_description = (
        f"Case owner has been changed to {user.name} from {old_owner_name} "
        f"in Case with the id of '{command.case_id}' by '{repo.tenancy.user_name}'"
    )

    await mb.publish(
        CaseOwnershipTransferred(
            case=command.case,
            audit=dict(description=audit_description),
        )
    )

    await mb.publish(
        CaseOwnershipTransferedNotification(
            case=command.case,
            message=notification_message,
            recipients=get_case_recipients(command.case),
        )
    )
