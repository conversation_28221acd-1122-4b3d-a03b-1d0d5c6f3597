import re
from api_sdk.models.elasticsearch import RawResult
from api_sdk.schemas.base import APIModel, Field
from api_sdk.utils.utils import StringEnum, nested_dict_get
from se_api_svc.schemas.account import Account<PERSON>erson
from se_api_svc.schemas.market import Market<PERSON>erson
from typing import Dict, List, Optional

ACCOUNT_PERSON_MODEL_NAME = AccountPerson.__config__.model_name
MARKET_PERSON_MODEL_NAME = MarketPerson.__config__.model_name


class PersonTypes(StringEnum):
    ACCOUNT = ACCOUNT_PERSON_MODEL_NAME[0:3]
    MARKET = MARKET_PERSON_MODEL_NAME[0:3]


class CommsDetail(APIModel):
    name: Optional[str]
    count: Optional[int]


class NetworkNode(APIModel):
    id: str
    depth: Optional[int] = 0
    name: str
    initials: str
    type: str
    total_comms: Optional[int] = Field(0, alias="totalComms")
    comms_detail: Optional[Dict] = Field({}, alias="commsDetail")


class NetworkLink(APIModel):
    id: str
    depth: Optional[int] = 0
    comms: int
    normalised_weight: float = Field(0.1, alias="normalisedWeight")
    source: str
    target: str


DEPTH_1_NODE_COUNT = 10
DEPTH_2_NODE_COUNT = 3
EXCLUDE_PARTICIPANTS_AGG = "FILTER_PARTICIPANTS"
LINKS_AGG = "LINKS"
NODE_AGG = "ONLY_ID"
PARTICIPANT_AGG = "PARTICIPANT"
REVERSE_AGG = "REVERSE"

KEY_REGEX = rf"^([^|]+)\|([^|]+)\|({PersonTypes.ACCOUNT.value}|{PersonTypes.MARKET.value})$"

PERSON_TYPE = {
    PersonTypes.ACCOUNT.value: ACCOUNT_PERSON_MODEL_NAME,
    PersonTypes.MARKET.value: MARKET_PERSON_MODEL_NAME,
}


def get_network_map_nodes_and_links(
    response: RawResult,
    parent_nodes: List[NetworkNode],
    existing_nodes_lookup: Dict,
    existing_links_lookup: Dict,
):
    """Parses the aggregate response query and returns the nodes and links.

    Steps:
    1. Iterate on aggregation. i.e. for each person id
    1.1. Find the parent node
    1.2. Increment the depth and find the nested network records
    1.3. For the parent record update the total comms value using the response mapping for
     parent node
    1.4. Iterate on network records. i.e. for each network entry
    1.4.1. Create a child node
    1.4.2. Find the source and target based on the order of the parent and child id
    1.4.3. Create and append links
    1.5. Iterate on each nodes
    1.5.1. Add the comms details to the parent nodes.
    1.5.2. Append to the nodes if not doesn't exists in map else add comms details to existing node
    1.6. Iterate on each links
    1.6.1. Find if link exists in existing link. If not add to existing links and result links
    1.7. Return nodes and links.
    """
    result_nodes: List[NetworkNode] = []
    result_links: List[NetworkLink] = []
    response_dict = dict(response)

    for person_agg_key in response.aggregations or []:
        parent_node: NetworkNode = next(
            (node for node in parent_nodes if node.id == person_agg_key), None
        )
        if not parent_node:
            raise ValueError(f"could not find parent node {person_agg_key}")

        depth = parent_node.depth + 1
        parent_node_id = parent_node.id

        # Get the link buckets
        network = (
            nested_dict_get(
                response_dict,
                f"aggregations.{parent_node_id}.{NODE_AGG}.{REVERSE_AGG}.{PARTICIPANT_AGG}.{EXCLUDE_PARTICIPANTS_AGG}"
                f".{LINKS_AGG}.buckets",
            )
            or []
        )

        nodes = []
        links = []

        # Total Communications for the parent node
        parent_node.total_comms = (
            nested_dict_get(
                response_dict, f"aggregations.{parent_node_id}.{NODE_AGG}.{REVERSE_AGG}.doc_count"
            )
            or 0
        )

        for network_entry in network:
            # eg: "abc|John Doe|Acc"
            match = re.search(KEY_REGEX, network_entry.key)
            if not match:
                return

            # Generate a child node for each of the link bucket and assign the comms details
            # to it for the parent node.
            child_node = NetworkNode(
                id=match[1],
                depth=depth,
                name=match[2],
                initials=get_initials_from_name(match[2]),
                type=PERSON_TYPE[match[3]],
                totalComms=0,
                commsDetail={
                    parent_node_id: CommsDetail(
                        name=parent_node.name, count=network_entry.doc_count
                    )
                },
            )

            # Find who is the source and target node
            if parent_node_id < child_node.id:
                source = parent_node
                target = child_node
            else:
                source = child_node
                target = parent_node

            # Generate a link for each of link bucket and assign source and target to the link
            link = NetworkLink(
                id=f"{source.id}--{target.id}",
                depth=depth,
                comms=network_entry.doc_count,
                normalisedWeight=0.1,
                source=source.id,
                target=target.id,
            )

            nodes.append(child_node)
            links.append(link)

        for node in nodes:
            node_id = node.id
            parent_node.comms_detail[node_id] = CommsDetail(
                name=node.name, count=node.comms_detail[parent_node_id].count
            )

            existing_node = existing_nodes_lookup.get(node_id)

            if existing_node:
                existing_node.comms_detail[parent_node_id] = node.comms_detail[parent_node_id]
            else:
                existing_nodes_lookup[node_id] = node
                result_nodes.append(node)

        for link in links:
            link_id = link.id
            existing_link = existing_links_lookup.get(link_id)
            if not existing_link:
                existing_links_lookup[link_id] = link
                result_links.append(link)

    return result_nodes, result_links


def get_initials_from_name(name: str) -> str:
    parts = list(filter(lambda n: len(n) > 0, name.split(" ")))
    parts_len = len(parts)
    if parts_len == 0:
        return ""

    if parts_len == 1:
        return parts[0][0]

    return f"{parts[0][0]}{parts[parts_len - 1][0]}"
