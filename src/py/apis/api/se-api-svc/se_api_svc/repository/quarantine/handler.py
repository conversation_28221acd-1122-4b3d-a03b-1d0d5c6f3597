import dataclasses
import datetime as dt
import logging
from api_sdk.cloud.utils import CloudProvider
from api_sdk.es_dsl.base import Exists, Not, PrefixFilter, SearchModel
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import Pagination
from api_sdk.repository.elastic import AbstractEsRepository
from api_sdk.schemas.helpers import utc_timestamp_millis
from api_sdk.schemas.inspector import find_model
from dataclasses import dataclass
from elasticsearch6 import Elasticsearch
from elasticsearch_dsl import Q
from enum import Enum, auto
from functools import cached_property  # pants: no-infer-dep
from schema_sdk.steeleye_model.gamma import SchemaGammaExtra
from se_api_svc.repository.data_provenance.quarantine import (
    QuarantinedRecordsSearch,
    QuarantineRepository,
)
from se_api_svc.repository.tr.tr_v1 import add_canc_transaction
from se_api_svc.swarm_extensions.record_handler_v2 import (
    BulkRequest,
    IndexLock,
    NoopException,
    RecordHandlerV2,
)
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import (
    Order,
    QuarantinedRTS22Transaction,
    QuarantinedTransaction,
    RTS22Transaction,
    SinkRecordAudit,
    Transaction,
    TransactionStatus,
)
from se_elastic_schema.models.tenant.mifid2.quarantined_order import QuarantinedOrder
from se_elastic_schema.static.mifid2 import ExternalTransactionStatus, TransactionDisposition
from se_elastic_schema.static.provenance import SinkRecordStatus
from typing import Dict, List, Type, TypedDict, Union

log = logging.getLogger(__name__)

MODELS_QUARANTINE_MAP = {
    Order.__name__: QuarantinedOrder.__name__,
    RTS22Transaction.__name__: QuarantinedRTS22Transaction.__name__,
    Transaction.__name__: QuarantinedTransaction.__name__,
}


class InvalidDocumentIdError(Exception):
    pass


class QuarantineFlowException(InvalidDocumentIdError):
    pass


class FullDoc(TypedDict):
    _source: Dict
    _index: str
    _id: str


class UserAction(str, Enum):
    ARCHIVE = auto()
    PROCESS = auto()


@dataclass
class QuarantineRequest:
    realm: str
    action: UserAction
    sink_record_audit_ids: List[str] = dataclasses.field(default_factory=list)
    user: str = None
    timestamp: int = None


def safe_last_prefix(s: str, delimiter=":"):
    if delimiter in s:
        return ":".join(s.split(":")[:-1])
    return ""


class QuarantineHandler:
    """This is a translation of Java code found in version 2.15 of saas-
    platform's QuarantineHandler. Some legacy documentation can be found at
    https://github.com/steeleye/schema/wiki/Quarantine-(Legacy-Doc)

    This is not a standard repository because it needs a custom
    RecordHandlerV2.
    """

    def __init__(self, repo: QuarantineRepository, dry_run=None):
        """dry_run is only respected by methods executed against the own
        record_handler.

        Repo methods have no knowledge of dry_run.
        """
        try:
            self._repo = repo.repo
        except AttributeError:
            self._repo = repo

        self._record_handler_init_vars = {
            "is_dry_run": dry_run,
            "client": self._repo.es_client,
            "tenant": self._repo.tenant,
        }
        self._record_handler = None

    @cached_property
    def record_handler(self):
        if self._record_handler is None:
            self._record_handler = RecordHandlerV2(**self._record_handler_init_vars)
        return self._record_handler

    @property
    def is_dry_run(self):
        return self.record_handler.is_dry_run

    @is_dry_run.setter
    def is_dry_run(self, value):
        self.record_handler.is_dry_run = value

    @property
    def meta(self):
        return self.record_handler.meta

    @property
    def es_client(self) -> Elasticsearch:
        return self.record_handler.client

    @property
    def tenant(self):
        return self.record_handler.tenant

    def index_for(
        self, model: Union[str, SteelEyeSchemaBaseModelES8, Type[SteelEyeSchemaBaseModelES8]]
    ):
        return self._repo.index_for_record_model(model)

    def model_name_for(
        self, model: Union[str, SteelEyeSchemaBaseModelES8, Type[SteelEyeSchemaBaseModelES8]]
    ):
        if isinstance(model, str):
            return model
        return model.__config__.schema_extra[SchemaGammaExtra.DESC]

    async def get_raw_sra_records(
        self,
        *,
        status=None,
        matched_key_prefix=None,
        record_key=None,
        record_type=None,
        bucket=None,
        **kwargs,
    ):
        kwargs.setdefault("hit_deserializer", None)  # "raw"
        if not status:
            status = SinkRecordStatus.__members__.values()
        return await self._repo.get_many(
            search_model_cls=QuarantinedRecordsSearch,
            pagination=Pagination(take=len(kwargs.get("model_id", []))),
            status=status,
            matched_key_prefix=matched_key_prefix,
            record_key=record_key,
            record_type=record_type,
            bucket=bucket,
            **kwargs,
        )

    async def get_raw_quarantined_records(self, key, **kwargs) -> RawResult:
        kwargs.setdefault("hit_deserializer", None)  # "raw"
        model_name = key.split(":")[0]
        return await self._repo.get_many(
            search_model=SearchModel(model=find_model(model_name), model_terms={"&key": key}),
            **kwargs,
        )

    async def get_raw_matched_records(self, key_prefix, **kwargs) -> RawResult:
        kwargs.setdefault("hit_deserializer", None)  # "raw"
        model_name = key_prefix.split(":")[0].replace("OrderState", "Order")
        model = find_model(model_name)
        return await self._repo.get_many(
            record_model=model,
            where=[PrefixFilter(field="&key", value=key_prefix), Not(Exists(field="&expiry"))],
            **kwargs,
        )

    def _find_one(self, *, terms=None, model=None, source_only=True, **es_client_search_kwargs):
        es_client_search_kwargs.setdefault("size", 1)
        hits = self._find_many(
            terms=terms, model=model, source_only=source_only, **es_client_search_kwargs
        )
        return hits[0]

    def _find_many(
        self,
        *,
        terms=None,
        model=None,
        source_only=True,
        not_expired=True,
        where=None,
        **es_client_search_kwargs,
    ):
        terms = terms or {}
        if model:
            terms[self.meta.model] = self.model_name_for(model)
        q = Q()
        if not_expired:
            q = q & ~Q("exists", **{"field": self.meta.expiry})
        for tt, tv in terms.items() if terms else ():
            if isinstance(tv, (tuple, list)):
                q = q & Q("terms", **{tt: tv})
            else:
                q = q & Q("term", **{tt: tv})
        if where:
            q = q & where
        res = self.es_client.search(body={"query": q.to_dict()}, **es_client_search_kwargs)
        if source_only:
            return [h["_source"] for h in res["hits"]["hits"]]
        return res["hits"]["hits"]

    async def handle_quarantine(
        self, request: QuarantineRequest, repo: AbstractEsRepository
    ) -> Dict:
        request.timestamp = request.timestamp or utc_timestamp_millis()

        cloud_provider = repo.api_config.CLOUD_PROVIDER.upper()

        sra_records = (
            await self.get_raw_sra_records(
                model_id=request.sink_record_audit_ids,
                status=SinkRecordStatus.QUARANTINED,
                bucket=request.realm if cloud_provider == CloudProvider.AWS else self.tenant,
            )
        ).as_list()

        sink_audit_map = {}

        found_ids = [sra["_id"] for sra in sra_records]
        for sra_id in request.sink_record_audit_ids:
            if sra_id not in found_ids:
                sink_audit_map[sra_id] = self.record_handler.new_bulk_request()
                sink_audit_map[sra_id].exception = QuarantineFlowException(
                    "No matching sink record audit found"
                )
                sink_audit_map[sra_id].results = {
                    "&id": sra_id,
                    "&result": "failed",
                    "failure": "No matching sink record audit found",
                }

        for sra in sra_records:
            bulk_request = self.record_handler.new_bulk_request()
            record_id = sra["_source"][self.meta.id]
            sink_audit_map[record_id] = bulk_request
            try:
                self.process_sink_record_audit(sra, request=request, bulk_request=bulk_request)
                self.update_sink_record_audit(sra, request=request, bulk_request=bulk_request)
                bulk_request.execute()
            except NoopException:
                sink_audit_map[record_id].results = {
                    "&id": record_id,
                    "&result": "noop",
                }
            except Exception as exc:
                if not isinstance(exc, InvalidDocumentIdError):
                    log.exception(exc)
                sink_audit_map[record_id].exception = exc
                sink_audit_map[record_id].results = {
                    "&id": record_id,
                    "&result": "failed",
                    "failure": str(exc)
                    if isinstance(exc, InvalidDocumentIdError)
                    else exc.__class__.__name__,
                }

        return sink_audit_map

    async def process_sink_record_audit(
        self,
        sra: FullDoc,
        request: QuarantineRequest,
        bulk_request: BulkRequest,
    ):
        assert not bulk_request.packets

        doc_id = sra["_id"]
        sra_source = sra["_source"]

        record_key = sra_source["recordKey"]

        matched_key = sra_source["matchedKey"]
        matched_prefix = safe_last_prefix(matched_key)
        model_str = matched_key.split(":", 1)[0]

        quarantined_records = await self.get_raw_quarantined_records(key=record_key).as_list()
        if not quarantined_records:
            raise QuarantineFlowException(f"quarantine record with key {record_key} not found")
        quarantine_record = quarantined_records[0]

        matched_records = await self.get_raw_matched_records(key_prefix=matched_prefix).as_list()
        if not matched_records:
            raise QuarantineFlowException(
                f"matching record with key prefix {matched_prefix} not found"
            )
        matched_record = matched_records[0]

        with self.index_lock(index=self.index_for(SinkRecordAudit), key_string=doc_id):
            if request.action == UserAction.ARCHIVE:
                self.archive_quarantine_record(
                    record=quarantine_record, request=request, bulk_request=bulk_request
                )
            else:
                if model_str == Transaction.__name__:
                    self.process_transaction(
                        quarantine_record,
                        matched_record,
                        request=request,
                        bulk_request=bulk_request,
                    )
                else:
                    self.process_other(
                        quarantine_record,
                        matched_record,
                        request=request,
                        bulk_request=bulk_request,
                    )

    def index_lock(self, *, index, key_string):
        return IndexLock(es_client=self.es_client, index=index, key_string=key_string)

    def _fetch_transaction_status(self, transaction_key: str):
        return self._find_one(
            terms={"transactionKey": transaction_key},
            doc_type=self.model_name_for(TransactionStatus),
            sort=[self.meta.key, "asc"],
            size=1,
        )

    def process_transaction(
        self,
        quarantine_record: FullDoc,
        matched_record: FullDoc,
        request: QuarantineRequest,
        bulk_request: BulkRequest,
    ):
        full_quarantine_record = quarantine_record
        quarantine_record = full_quarantine_record["_source"]

        full_matched_record = matched_record
        matched_record = full_matched_record["_source"]

        external_status = None
        disposition = matched_record.get("disposition", None)
        if TransactionDisposition.REPORTED == disposition:
            transaction_key = matched_record[self.meta.key]
            external_status = self._fetch_transaction_status(transaction_key=transaction_key).get(
                "externalStatus"
            )
            if ExternalTransactionStatus.SUBMITTED != external_status:
                raise QuarantineFlowException(f"transactionKey {transaction_key} is still pending")

        merged_record = self.record_handler._merge_old_new(
            Transaction,
            matched_record,
            quarantine_record,
            {
                "addedToReport": False,
                "disposition": (
                    TransactionDisposition.REPORTABLE.value
                    if disposition is None or TransactionDisposition.REPORTED == disposition
                    else disposition
                ),
            },
        )
        if (
            ExternalTransactionStatus.ACCEPTED == external_status
            or ExternalTransactionStatus.ARM_ACCEPTED == external_status
        ):
            add_canc_transaction(
                record_handler=self.record_handler,
                raw=full_matched_record,
                user=request.user,
                timestamp=request.timestamp,
                bulk_request=bulk_request,
            )

        bulk_request.new_update(
            model=matched_record[self.meta.model],
            doc=merged_record,
            index=full_matched_record["_index"],
            user=request.user,
            timestamp=request.timestamp,
            parent=None,
            base_version=int(matched_record[self.meta.timestamp]),
        ).build(is_strict=True, result="updated")

        self.archive_quarantine_record(
            record=full_quarantine_record,
            request=request,
            bulk_request=bulk_request,
        )

    def process_other(
        self,
        quarantine_record: FullDoc,
        matched_record: FullDoc,
        request: QuarantineRequest,
        bulk_request: BulkRequest,
    ):
        """# Translation of QuarantineHandler.processOther.

        1. Create a doc that is a merge of the quarantined record over the matched record.
        2. Create a snapshot of the previous version.
        3. Delete the previous version.
        4. Archive the quarantine record.
        """

        full_quarantine_record = quarantine_record
        quarantine_record = full_quarantine_record["_source"]

        full_matched_record = matched_record
        matched_record = full_matched_record["_source"]

        model_name = matched_record[self.meta.model]
        model = find_model(model_name)

        doc = self.record_handler._merge_old_new(model, matched_record, quarantine_record)
        bulk_request.new_update(
            model=model_name,
            doc=doc,
            index=full_matched_record["_index"],
            parent=matched_record.get(self.meta.parent),
            user=request.user,
            timestamp=request.timestamp,
            base_version=int(doc[self.meta.timestamp]),
        ).build(is_strict=True, result="updated")

        self.archive_quarantine_record(
            record=full_quarantine_record,
            request=request,
            bulk_request=bulk_request,
        )

    def archive_quarantine_record(
        self,
        record: FullDoc,
        request: QuarantineRequest,
        bulk_request: BulkRequest,
    ):
        """Archiving a quarantined record does not change or touch the matched
        record:

        1) the current version of Quarantined<Record> is deleted. 2) a
        new version of Quarantined<Record> is created with &ancestor set
        to the current version of Quarantined<Record>.
        """
        # Translation of QuarantineHandler.archiveQuarantineRecords
        full_record = record
        record = full_record["_source"]
        record_id = full_record["_id"]

        bulk_request.new_delete(
            index=full_record["_index"],
            model=record[self.meta.model],
            doc_id=record_id,
            parent=record.get(self.meta.parent),
            timestamp=request.timestamp,
            base_version=record.get(self.meta.timestamp),
            user=request.user,
        ).build(is_strict=True, result="deleted")

    def update_sink_record_audit(
        self,
        record: FullDoc,
        request: QuarantineRequest,
        bulk_request: "BulkRequest",
    ):
        """Marks the sink record audit as processed accordingly."""
        # Translation of QuarantineHandler.updateSinkAudit

        if not bulk_request.is_ok:
            # Do nothing, the processing failed
            return

        full_record = record
        record = record["_source"]

        record["status"] = "ARCHIVED" if request.action == UserAction.ARCHIVE else "IMPORTED"
        record["userProcessed"] = True
        record["processed"] = request.timestamp

        bulk_request.new_update(
            doc=record,
            index=full_record["_index"],
            model=record[self.meta.model],
            user=request.user,
            parent=record.get(self.meta.parent),
            timestamp=record[self.meta.timestamp],
            base_version=int(record[self.meta.timestamp]),
        ).build(is_strict=False, result="updated")

    def get_record_by_id(self, model, id) -> FullDoc:
        return self.record_handler.get(
            index=self.index_for(model),
            model=self.model_name_for(model),
            doc_id=id,
            source_only=False,
        )

    def _fake_quarantine(
        self, model: Type[SteelEyeSchemaBaseModelES8], id: str, updates: Dict, realm: str
    ) -> "BulkRequest":
        model_name = self.model_name_for(model)
        full_existing = self.get_record_by_id(model, id)
        existing = full_existing["_source"]

        q_model_name = MODELS_QUARANTINE_MAP[model_name]
        q_model = find_model(q_model_name)
        q_doc = self.record_handler._merge_old_new(q_model, {}, existing, updates)
        q_index = self.index_for(q_model)

        bulk_request = self.record_handler.new_bulk_request()

        q_part = bulk_request.new_create(
            doc=q_doc,
            index=q_index,
            model=q_model_name,
        ).build(result="created")

        if bulk_request.results[q_part.id]["&result"] == "noop":
            raise RuntimeError("Fake ingestion resulted in noop, no quarantine record generated")

        bulk_request.new_create(
            doc={
                "bucket": realm,
                "recordKey": q_part.source[self.meta.key],
                "recordType": model_name,
                "matchedKey": existing[self.meta.key],
                "status": "QUARANTINED",
                "userProcessed": None,
                "key": "NA",
                "dataSourceName": "NA",
                "taskStarted": dt.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "processed": None,
                "index": "0.2",
            },
            index=self.index_for(SinkRecordAudit),
            model=self.model_name_for(SinkRecordAudit),
        ).build(result="created")

        return bulk_request
