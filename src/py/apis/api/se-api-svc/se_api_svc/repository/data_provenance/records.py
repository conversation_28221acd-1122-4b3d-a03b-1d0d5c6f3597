import datetime as dt
import json
import logging
from api_sdk.es_dsl.base import (
    Exists,
    ModelFilter,
    Not,
    NotExpired,
    Or,
    PrefixFilter,
    QueryString,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.flang import <PERSON><PERSON><PERSON>ilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import (
    CASCADE_AUDIT_SEARCH_FIELDS,
    SINK_FILE_TEXT_SEARCH_FIELDS,
    SINK_RECORD_TEXT_SEARCH_FIELDS,
)
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.schemas.inspector import find_model
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.utils.utils import nested_dict_get, parse_datetime
from dateutil import parser
from functools import cached_property
from pathlib import Path
from se_api_svc.schemas.provenance import (
    CascadeAudit,
    CascadeAuditTrendChart,
    QuarantinedTransaction,
    ReconFlow,
    RecordAuditType,
    SinkAudit,
    SinkAuditTrendChart,
    SinkFileAudit,
    SinkFileAuditTrendChart,
    SinkRecordAudit,
    SinkRecordAuditTrendChart,
)
from se_api_svc.schemas.rts22 import QuarantinedRts22Transaction, Rts22Transaction
from se_api_svc.schemas.tr import Transaction
from se_api_svc.utils.es import filter_aliases
from se_elastic_schema.models.tenant.mifid2.quarantined_order import QuarantinedOrder
from se_elastic_schema.static.provenance import S3ObjStatus, SinkRecordStatus, TaskStatus
from typing import Dict, Iterable, List, Optional, Union
from xlsxwriter import Workbook

log = logging.getLogger(__name__)

AUDIT_RECORD_TREND_CHART_SPEC = {
    CascadeAuditTrendChart.ACTION: ("action", False),
    CascadeAuditTrendChart.REALM: ("realm", False),
    CascadeAuditTrendChart.STATUS: ("status", False),
    CascadeAuditTrendChart.TRIGGERED_BY: ("triggeredBy", False),
    SinkAuditTrendChart.ERROR_CODE: ("errorCode", False),
    SinkAuditTrendChart.ERROR_MESSAGE: ("errorMessage", False),
    SinkAuditTrendChart.EVENT_TYPE: ("eventType", False),
    SinkAuditTrendChart.FUNCTION: ("function", False),
    SinkAuditTrendChart.INPUT: ("input", False),
    SinkAuditTrendChart.MESSAGE: ("message", False),
    SinkAuditTrendChart.MODEL: ("model", False),
    SinkAuditTrendChart.STAGE: ("stage", False),
    SinkAuditTrendChart.TENANT: ("tenant", False),
    SinkFileAuditTrendChart.BUCKET: ("bucket", False),
    SinkFileAuditTrendChart.DATA_SOURCE: ("dataSource", False),
    SinkFileAuditTrendChart.KEY: ("key", False),
    SinkFileAuditTrendChart.STATUS: ("status", False),
    SinkRecordAuditTrendChart.BUCKET: ("bucket", False),
    SinkRecordAuditTrendChart.DATA_SOURCE_NAME: ("dataSourceName", False),
    SinkRecordAuditTrendChart.INDEX: ("index", False),
    SinkRecordAuditTrendChart.MODEL: ("recordType", False),
    SinkRecordAuditTrendChart.STATUS: ("status", False),
    SinkRecordAuditTrendChart.ERROR_MESSAGE: ("validationErrors.errorMessage", False),
    SinkRecordAuditTrendChart.ERROR_CLASS: ("validationErrors.errorClass", False),
    SinkFileAuditTrendChart.__name__: {
        SinkFileAuditTrendChart.ERROR_CLASS: ("errorClassStats.errorClass", False),
    },
    CascadeAuditTrendChart.__name__: {
        CascadeAuditTrendChart.MODEL: ("sourceModel", False),
    },
}


class AuditRecordSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None

    params: Params

    date_field = "queued"

    features = [
        NotExpired,
    ]


class QuarantinesRecordSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None

    params: Params

    features = [
        ModelFilter(model=QuarantinedOrder),
        NotExpired,
        FlangFilter.simple(param="f"),
    ]


class CascadeAuditSearch(AuditRecordSearchBase):
    class Params(AuditRecordSearchBase.Params):
        assigned_identifier: Optional[str] = None
        search: Optional[str] = None

    params: Params

    features = AuditRecordSearchBase.features + [
        ModelFilter(model=CascadeAudit),
        RangeFilter(field="queued"),
        TermFilter(name="assignedIdentifier", param="assigned_identifier"),
        FlangFilter.simple(
            param="f",
        ),
        QueryString(param="search", fields=CASCADE_AUDIT_SEARCH_FIELDS),
    ]

    default_sort_order = ["queued:desc"]

    extras = [{"_source": {"excludes": ["duration"]}}]


class AuditRecordsTimelineAgg(AuditRecordSearchBase):
    class Params(AuditRecordSearchBase.Params):
        interval: Optional[str]
        buckets: Optional[int] = None
        date_field: str
        search: Optional[str] = None

    features = AuditRecordSearchBase.features + [
        Or(
            RangeFilter(field="processed"),
            RangeFilter(field="completed"),
        ),
        FlangFilter.simple(param="f"),
        QueryString(param="search", fields=SINK_RECORD_TEXT_SEARCH_FIELDS),
    ]

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(
            self.params, min_interval=15 * 60 * 1000
        )  # 15 minutes

    def build_aggs(self):
        return {
            "TIMES": {
                "date_histogram": {
                    "field": self.params.date_field,
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
            }
        }


class SinkAuditSearch(AuditRecordSearchBase):
    features = AuditRecordSearchBase.features + [
        ModelFilter(model=SinkAudit),
        RangeFilter(field="queued"),
        FlangFilter.simple(
            param="f",
        ),
    ]

    date_field = "queued"

    default_sort_order = ["queued:desc"]


class SinkFileAuditSearch(AuditRecordSearchBase):
    class Params(AuditRecordSearchBase.Params):
        search: Optional[str] = None
        status: Optional[List[TaskStatus]] = None
        keyword_one: Optional[ReconFlow] = None
        keyword_two: Optional[ReconFlow] = None
        key: Optional[str] = None
        start_range: Optional[Union[int, dt.date, dt.datetime, str]] = None
        end_range: Optional[Union[int, dt.date, dt.datetime, str]] = None

    features = AuditRecordSearchBase.features + [
        ModelFilter(model=SinkFileAudit),
        RangeFilter(field="queued"),
        RangeFilter(field="completed", start_param="start_range", end_param="end_range"),
        TermFilter(name="status", param="status"),
        Or(
            QueryString(param="keyword_one", fields=["&id", "&key"]),
            QueryString(param="keyword_two", fields=["&id", "&key"]),
        ),
        TermFilter(param="key", name="key"),
        FlangFilter.simple(param="f", nested_paths=["recordTypeStats"]),
        QueryString(param="search", fields=SINK_FILE_TEXT_SEARCH_FIELDS),
    ]

    date_field = "queued"

    default_sort_order = ["queued:desc"]


class SinkRecordAuditSearch(AuditRecordSearchBase):
    class Params(AuditRecordSearchBase.Params):
        status: Optional[SinkRecordStatus] = None
        search: Optional[str]

    params: Params

    features = AuditRecordSearchBase.features + [
        ModelFilter(model=SinkRecordAudit),
        Or(
            RangeFilter(field="processed"),
            RangeFilter(field="taskStarted"),
        ),
        TermFilter(name="status", param="status"),
        FlangFilter.simple(param="f"),
        QueryString(param="search", fields=SINK_RECORD_TEXT_SEARCH_FIELDS),
    ]

    date_field = "processed"

    default_sort_order = ["processed:desc"]


class RecordsRepository(RepoHelpersMixin):
    async def get_cascade_audit_records(self, **params) -> RawResult:
        return await self.get_many(
            record_model=CascadeAudit, search_model_cls=CascadeAuditSearch, **params
        )

    async def get_cascade_audit_records_timeline(self, **params):
        return await self._get_records_timeline(
            record_model=CascadeAudit, search_model_cls=CascadeAuditSearch, **params
        )

    async def get_cascade_audit_records_trends_summary_by_type(
        self, trend_chart: CascadeAuditTrendChart, **params
    ):
        return await self._get_trends_summary_by_type(
            search_model_cls=CascadeAuditSearch, trend_chart=trend_chart, **params
        )

    async def get_sink_audit_records(self, **params) -> RawResult:
        return await self.get_many(
            record_model=SinkAudit, search_model_cls=SinkAuditSearch, **params
        )

    async def get_sink_audit_records_timeline(self, **params):
        return await self._get_records_timeline(
            record_model=SinkAudit, search_model_cls=SinkAuditSearch, **params
        )

    async def get_sink_audit_records_trends_summary_by_type(
        self, trend_chart: SinkAuditTrendChart, **params
    ):
        return await self._get_trends_summary_by_type(
            search_model_cls=SinkAuditSearch, trend_chart=trend_chart, **params
        )

    async def get_sink_file_audit_records(self, **params) -> RawResult:
        return await self.get_many(
            record_model=SinkFileAudit, search_model_cls=SinkFileAuditSearch, **params
        )

    async def _get_sink_file_audit_records(self, **params):
        records = [
            record
            async for record in self.repo.execute_scan(
                index=self.index_for_record_model(SinkFileAudit),
                search_model=SinkFileAuditSearch(**params),
            )
        ]
        return records

    async def get_sink_file_audit_records_scroll(self, **params):
        audit_files = await self._get_sink_file_audit_records(**params)

        files = []

        for file_record in audit_files:
            file = file_record.get("_source")
            if file.get("key"):
                file["fileName"] = file.get("key").split("/")[-1]
            if file.get("recordTypeStats"):
                temp_file = ""
                for record in file.get("recordTypeStats"):
                    temp_file += f"{record.get('recordType')}: {record.get('imported')} imported "
                file["recordTypeStats"] = temp_file
            files.append(file)

        return files

    async def _get_sink_record_audit_records(self, **params):
        return [
            record
            async for record in self.repo.execute_scan(
                index=self.index_for_record_model(SinkRecordAudit),
                search_model=SinkRecordAuditSearch(**params),
            )
        ]

    async def get_sink_record_audit_records_scroll(self, **params):
        audit_files = await self._get_sink_record_audit_records(**params)

        files = []

        for sink_record in audit_files:
            file = sink_record.get("_source")
            if file.get("key"):
                file["fileName"] = file.get("key").split("/")[-1]
            files.append(file)

        return files

    async def get_sink_file_audits(self, **params) -> RawResult:
        return await self.get_many(
            record_model=SinkFileAudit, search_model_cls=SinkFileAuditSearch, **params
        )

    async def get_sink_file_audit_records_timeline(self, **params):
        return await self._get_records_timeline(
            record_model=SinkFileAudit, search_model_cls=SinkFileAuditSearch, **params
        )

    async def get_sink_file_audit_records_trends_summary_by_type(
        self, trend_chart: SinkFileAuditTrendChart, **params
    ):
        return await self._get_trends_summary_by_type(
            search_model_cls=SinkFileAuditSearch, trend_chart=trend_chart, **params
        )

    async def get_sink_record_audit_records(self, **params) -> RawResult:
        return await self.get_many(
            record_model=SinkRecordAudit, search_model_cls=SinkRecordAuditSearch, **params
        )

    async def get_sink_record_audit_records_timeline(self, **params):
        return await self._get_records_timeline(
            record_model=SinkRecordAudit, search_model_cls=SinkRecordAuditSearch, **params
        )

    async def get_sink_record_audit_records_trends_summary_by_type(
        self, trend_chart: SinkRecordAuditTrendChart, **params
    ):
        return await self._get_trends_summary_by_type(
            search_model_cls=SinkRecordAuditSearch, trend_chart=trend_chart, **params
        )

    async def _get_records_timeline(
        self,
        record_model: Union[CascadeAudit, SinkAudit, SinkFileAudit],
        search_model_cls: Union[CascadeAuditSearch, SinkAuditSearch, SinkFileAuditSearch],
        start: dt.datetime = None,
        end: dt.datetime = None,
        buckets=None,
        interval=None,
        **params,
    ):
        if not start:
            start = await self.get_min_value(
                search_model_cls=search_model_cls,
                field=search_model_cls.date_field,
                as_string=True,
                end=end,
                **params,
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = AuditRecordsTimelineAgg(
            start=start,
            end=end,
            buckets=buckets,
            interval=interval,
            date_field=search_model_cls.date_field,
            **params,
        )

        result = await self.get_aggs(record_model=record_model, search_model=search_model)

        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                }
                for bucket in result.iter_raw_bucket_agg("TIMES")
            ],
        }

    async def _get_trends_summary_by_type(
        self,
        search_model_cls: Union[CascadeAuditSearch, SinkAuditSearch, SinkFileAuditSearch],
        trend_chart: Union[
            CascadeAuditTrendChart,
            SinkAuditTrendChart,
            SinkFileAuditTrendChart,
            SinkRecordAuditTrendChart,
        ],
        size: int = 5,
        **search_params,
    ):
        trend_chart_name = trend_chart.__class__.__name__
        field, nested_path = (
            AUDIT_RECORD_TREND_CHART_SPEC.get(trend_chart_name, {}).get(trend_chart)
            or AUDIT_RECORD_TREND_CHART_SPEC[trend_chart]
        )

        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {"SUBJECTS": {"terms": {"field": field, "size": size or 5}}},
                }
            }
        else:
            aggs = {"SUBJECTS": {"terms": {"field": field, "size": size or 5}}}

        result = await self.get_aggs(
            search_model_cls=search_model_cls,
            aggs=aggs,
            **search_params,
        )
        return list(result.iter_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS"))

    async def delete_records(self, filename: str) -> List[Dict[str, int]]:
        def filter_elastic_query(key, search):
            return {
                "query": {
                    "bool": {
                        "should": [
                            {"wildcard": {key: f"*{search}"}},
                        ]
                    }
                }
            }

        models = [
            ("Rts22Transaction", Rts22Transaction),
            ("QuarantinedRTS22Transaction", QuarantinedRts22Transaction),
            ("Transaction", Transaction),
            ("QuarantinedTransaction", QuarantinedTransaction),
        ]

        raw_result: RawResult = await self.get_sink_file_audits(key=filename)
        results = list(
            map(
                lambda x: getattr(x.s3_object, "status", S3ObjStatus.CREATED)
                == S3ObjStatus.DELETED,
                raw_result.hits.hits,
            )
        )

        is_deleted, sink_file_audits = all(results), raw_result.hits.hits

        if is_deleted:
            return []

        raw_results = []
        for model_name, model in models:
            index = self.repo.index_for_record_model(record_model=model)
            index = [index] if not isinstance(index, Iterable) else index
            index = filter_aliases(self.repo.es_client, tuple(index))
            if len(index) == 0:
                continue

            result = self.repo.es_client.delete_by_query(
                index=index, body=filter_elastic_query("sourceKey", filename), refresh=True
            )
            setattr(result, "model", model_name)
            raw_results.append(result)

        # removing records from sink record audit
        index = self.repo.index_for_record_model(SinkRecordAudit)
        result = self.repo.es_client.delete_by_query(
            index=index, body=filter_elastic_query("key", filename), refresh=True
        )
        setattr(result, "model", SinkRecordAudit.__name__)

        raw_results.append(result)

        ids = []
        for sink_file_audit in sink_file_audits:
            ids.append(sink_file_audit.id_)

        body = {
            "script": {
                "source": "if (ctx._source['s3_object'] != null) "
                "{ ctx._source['s3_object']['status'] = 'DELETED'} "
                "else {ctx._source.put('s3_object',params.s3_object)}",
                "lang": "painless",
                "params": {"s3_object": {"status": S3ObjStatus.DELETED}},
            },
            "query": {"ids": {"values": ids}},
        }
        log.info(f"update_query : {json.dumps(body)}")
        index = self.repo.index_for_record_model(SinkFileAudit)
        self.repo.es_client.update_by_query(index=index, body=body, refresh=True)

        return list(
            map(lambda x: {"deleted": x["deleted"], "model": getattr(x, "model")}, raw_results)
        )

    async def one_record_to_xlsx(self, id: str, output):
        file_audit = (await self.get_one(record_model=SinkFileAudit, id=id)).dict(by_alias=True)
        sink_records = await self.get_sink_record_audit_records(model_qs=file_audit["key"])

        workbook = Workbook(
            str(output) if isinstance(output, Path) else output, options=dict(remove_timezone=True)
        )
        ws = workbook.add_worksheet(name="File audit")
        title_format = workbook.add_format({"bold": True, "font_size": 14})
        hdr_format = workbook.add_format({"bold": True})

        rows = [
            ("bucket", "Bucket"),
            ("duplicate", "Count - Duplicate"),
            ("errored", "Count - Errored"),
            ("imported", "Count - Imported"),
            ("iterable", "Count - Iterable"),
            ("quarantined", "Count - Quarantined"),
            ("dataSource.name", "Data Source"),
            ("completed", "Date-Time Completed"),
            ("queued", "Date-Time Queued"),
            ("started", "Date-Time Started"),
            ("duration", "Duration"),
            ("key", "Source File"),
            ("status", "Status"),
            ("taskName", "Task Name"),
            ("taskVersion", "Task Version"),
            ("tenant", "Tenant"),
            ("errorClassStats.errorClass", "Error Classes"),
            ("errorClassStats.values", "Error Values"),
        ]

        for row_id, row in enumerate(rows):
            ws.write(row_id, 0, row[1], hdr_format)
            ws.write(row_id, 1, nested_dict_get(file_audit, row[0]))

        ws.write(len(rows) + 1, 0, "Errors", title_format)

        row_cursor = len(rows) + 2
        for error in file_audit.get("errorClassStats", []):
            ws.write(row_cursor, 0, error["errorClass"], hdr_format)
            row_cursor += 1
            for value in error["values"]:
                ws.write(row_cursor, 1, value)
                row_cursor += 1

        row_cursor += 1
        ws.write(row_cursor, 0, "Processed", title_format)

        processing_cols = [
            ("processed", "Date"),
            ("status", "Status"),
            ("recordType", "Record Type"),
            ("dataSourceName", "Source"),
            ("key", "File name"),
        ]

        row_cursor += 1
        for col_id, col in enumerate(processing_cols):
            ws.write(row_cursor, col_id, col[1], hdr_format)

        for record in sink_records.hits.hits:
            row_cursor += 1
            for col_id, col in enumerate(processing_cols):
                ws.write(row_cursor, col_id, nested_dict_get(record.dict(by_alias=True), col[0]))

        workbook.close()

    @staticmethod
    def get_date_range(
        start: Union[int, dt.date, dt.datetime, str] = None,
        end: Union[int, dt.date, dt.datetime, str] = None,
    ):
        """
        Get a date range based on specified or default parameters.
        Returns:
            - If 'end' is None, it is set to the current datetime.
            - If start is empty or the date range (difference between 'end' and 'start') is
             greater than 4 weeks, 'start' is adjusted to be 4 weeks before 'end'.
            - If the date range is 28 days or less, 'start' and 'end' remain as specified or
             default.
        """
        if isinstance(start, str):
            start = parser.parse(start)
        if isinstance(end, str):
            end = parser.parse(end)

        if not end:
            end = dt.datetime.now()
        if not start or (end - start).days > 28:
            start = end - dt.timedelta(days=28)
        return start, end

    async def get_feed_summary_record_type(self, record_type: RecordAuditType, **kwargs):
        """Get Feeds summary with record type current on sink file audit is
        considered.

        @param record_type:
            RecordAuditType: Default `SINK_FILE_AUDIT` # TODO Add `SINK_RECORD_AUDIT` when needed
        @return:
            Feeds Type summary with ModelType, date, dataSourceName
        """
        search_model_cls_map = {
            record_type.SINK_FILE_AUDIT: SinkFileAuditSearch,
        }
        start, end, f = kwargs.pop("start", None), kwargs.pop("end", None), kwargs.pop("f", None)
        if not (start or end) and f:
            buckets = (
                await self.get_aggs(
                    search_model_cls=search_model_cls_map[record_type],
                    aggs={
                        "min": {"min": {"field": "completed"}},
                        "max": {"max": {"field": "completed"}},
                    },
                    f=f,
                    **kwargs,
                )
            ).aggregations
            start, end = buckets["min"]["value_as_string"], buckets["max"]["value_as_string"]

        start_range, end_range = self.get_date_range(start, end)
        results = []

        if record_type == RecordAuditType.SINK_FILE_AUDIT:
            raw_results = await self.get_aggs(
                search_model_cls=search_model_cls_map[record_type],
                aggs={
                    "dataSources": {
                        "terms": {"field": "dataSource.name", "size": 50},
                        "aggs": {
                            "date": {
                                "date_histogram": {
                                    "field": "completed",
                                    "fixed_interval": "1d",
                                    "format": "yyyy-MM-dd",
                                },
                                "aggs": {
                                    "recordTypeStats": {
                                        "nested": {"path": "recordTypeStats"},
                                        "aggs": {
                                            "byRecordType": {
                                                "terms": {"field": "recordTypeStats.recordType"},
                                                "aggs": {
                                                    "importedCount": {
                                                        "sum": {"field": "recordTypeStats.imported"}
                                                    },
                                                    "duplicateCount": {
                                                        "sum": {
                                                            "field": "recordTypeStats.duplicate"
                                                        }
                                                    },
                                                    "quarantinedCount": {
                                                        "sum": {
                                                            "field": "recordTypeStats.quarantined"
                                                        }
                                                    },
                                                    "erroredCount": {
                                                        "sum": {"field": "recordTypeStats.errored"}
                                                    },
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                },
                start_range=start_range,
                end_range=end_range,
                f=f,
                **kwargs,
            )

            for feeds in raw_results.iter_raw_bucket_agg("dataSources"):
                feed_summary = {
                    "dataSourceName": feeds["key"],
                    "feedSummaryByDate": [
                        {
                            "date": date.get("key_as_string"),
                            "model": ds["key"],
                            "status": {
                                "quarantinedCount": ds["quarantinedCount"]["value"],
                                "duplicateCount": ds["duplicateCount"]["value"],
                                "erroredCount": ds["erroredCount"]["value"],
                                "importedCount": ds["importedCount"]["value"],
                            },
                        }
                        for date in feeds.get("date", {}).get("buckets", [])
                        for ds in date["recordTypeStats"]["byRecordType"]["buckets"]
                    ],
                }

                results.append(feed_summary)

        return results

    async def get_sink_record_audit_quarantined_records(self, sink_record_audit, **kwargs):
        quarantined_record_keys = ["recordKey", "matchedKey"]
        record_models, prefix_filters = [], []

        for key in quarantined_record_keys:
            key_prefix = getattr(sink_record_audit, key)
            # Removing timestamp becuase the timestamps are not aligned in workflow.
            key_prefix = ":".join(key_prefix.split(":")[:-1])
            kwargs.setdefault("hit_deserializer", None)
            model_name = key_prefix.split(":")[0].replace("OrderState", "Order")

            record_models.append(find_model(model_name))
            prefix_filters.append(PrefixFilter(field="&key", value=key_prefix))

        return (
            await self.get_many(
                record_model=record_models,
                where=[Or(*prefix_filters), Not(Exists(field="&expiry"))],
                **kwargs,
            )
        ).hits.hits

    async def get_quarantined_records(self, **kwargs):
        return await self.get_many(
            search_model=QuarantinesRecordSearchBase(**kwargs),
            record_model=[QuarantinedOrder],
        )
