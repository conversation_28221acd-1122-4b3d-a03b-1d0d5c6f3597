# type: ignore
import addict
import json
import numpy as np
import pandas as pd
from aries_config_api_httpschema.lexica import LexicaType
from aries_se_api_client.client import AriesApiClient as SeApiClient
from cachetools.func import ttl_cache
from data_platform_config_api_client.lexica import Lexi<PERSON><PERSON><PERSON>
from data_platform_config_api_client.stack import <PERSON><PERSON><PERSON><PERSON>
from fastapi import HTT<PERSON>Exception
from functools import lru_cache
from httpx import HTTPStatusError
from master_data_api_client.api_client.auth_client import AuthClient
from master_data_api_client.api_client.client import ApiClient
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.services.master_data_api import Lexica
from typing import List


def get_lexica_master_data_api_client(config: str):
    config = addict.Dict(json.loads(config))
    return Lexica(
        ApiClient(
            host=config.MASTER_DATA_API_MASTER_DATA_API_HOST,
            auth_client=AuthClient(
                client_id=config.MASTER_DATA_API_COGNITO_CLIENT_ID,
                client_secret=config.MASTER_DATA_API_COGNITO_CLIENT_SECRET,
                oauth2_url=config.MASTER_DATA_API_COGNITO_AUTH_URL,
            ),
        )
    )


def get_data_platform_config_lexica_client(config: ApiServiceConfig):
    return LexicaAPI(SeApiClient(host=config.DATA_PLATFORM_CONFIG_API_URL))


def get_data_platform_config_stack_client(config: ApiServiceConfig):
    return StackAPI(SeApiClient(host=config.DATA_PLATFORM_CONFIG_API_URL))


@ttl_cache(1, ttl=600)
def _get_se_lexica_versions(config: str) -> List[str]:
    master_lexica_client = get_lexica_master_data_api_client(config)
    return master_lexica_client.get_available_versions().content


@lru_cache(maxsize=5)
def _get_se_lexica_languages_for_version(config: str, version: str) -> List[str]:
    master_lexica_client = get_lexica_master_data_api_client(config)
    return master_lexica_client.get_available_languages_for_version(version).content


@lru_cache(maxsize=5)
def _get_se_lexica_terms_df(config: str, version: str) -> pd.DataFrame:
    master_lexica_client = get_lexica_master_data_api_client(config)
    terms_path = master_lexica_client.get_terms_by_version(version).content
    terms_df = pd.read_csv(terms_path)
    terms_df = terms_df.fillna(np.nan).replace([np.nan], [None])
    return terms_df


@lru_cache(maxsize=5)
def _get_se_lexica_topics_df(config: str, version: str) -> pd.DataFrame:
    master_lexica_client = get_lexica_master_data_api_client(config)
    topics_path = master_lexica_client.get_topics_by_version(version).content
    topics_df = pd.read_csv(topics_path)

    topics_df.iloc[:, 1:] = topics_df.iloc[:, 1:].applymap(lambda x: x.split(","))

    # TODO: remove when topic translations are handled separately
    topics_df = topics_df.rename(
        lambda x: x if not x.startswith("topic_term_") else x.replace("topic_term_", ""), axis=1
    )
    topics_df = topics_df.rename({"topic_term": "en"}, axis=1)

    return topics_df


class SteelEyeLexicaRepository:
    def __init__(self, config: ApiServiceConfig) -> None:
        self.config = config
        self.master_data_client_config = json.dumps(
            {
                "MASTER_DATA_API_MASTER_DATA_API_HOST": self.config.MASTER_DATA_API_MASTER_DATA_API_HOST,  # noqa: E501
                "MASTER_DATA_API_COGNITO_CLIENT_ID": self.config.MASTER_DATA_API_COGNITO_CLIENT_ID,
                "MASTER_DATA_API_COGNITO_CLIENT_SECRET": self.config.MASTER_DATA_API_COGNITO_CLIENT_SECRET,  # noqa: E501
                "MASTER_DATA_API_COGNITO_AUTH_URL": self.config.MASTER_DATA_API_COGNITO_AUTH_URL,
            }
        )

    def get_se_lexica_terms_df(self, version: str) -> pd.DataFrame:
        return _get_se_lexica_terms_df(self.master_data_client_config, version)

    def get_se_lexica_topics_df(self, version: str) -> pd.DataFrame:
        return _get_se_lexica_topics_df(self.master_data_client_config, version)

    def get_se_lexica_version_for_tenant(self, tenant: str, lexica_type=LexicaType.tenant) -> str:
        data_platform_config_lexica_client = get_data_platform_config_lexica_client(self.config)
        try:
            se_lexica_resp = data_platform_config_lexica_client.get_se_lexica_version(
                tenant_name=tenant, stack_name=self.config.STACK, lexica_type=lexica_type
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Tenant not found")
            raise e

        return se_lexica_resp.content.get("se_lexica_version", "")

    def get_se_lexica_version_for_id(self, tenant: str, id: str) -> str:
        version = id.split("___")[-1]
        if version in _get_se_lexica_versions(self.master_data_client_config):
            return version
        return self.get_se_lexica_version_for_tenant(tenant)

    def get_se_available_languages(self, version: str) -> List[str]:
        try:
            languages = _get_se_lexica_languages_for_version(
                self.master_data_client_config, version
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Version not found")
            raise e
        return languages

    def get_subscribed_languages(self, tenant: str, raise_errors: bool = False) -> List[str]:
        data_platform_config_lexica_client = get_data_platform_config_lexica_client(self.config)
        try:
            se_lexica_resp = data_platform_config_lexica_client.get_se_lexica_version(
                tenant_name=tenant, stack_name=self.config.STACK
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Tenant not found")
            raise e

        try:
            return se_lexica_resp.content["subscribed_translations"]
        except KeyError as e:
            if raise_errors:
                raise e
            return ["en"]

    def update_se_lexica_version(self, tenant: str, version: str, subscribed_languages: List[str]):
        data_platform_config_lexica_client = get_data_platform_config_lexica_client(self.config)
        try:
            json_body = {
                "se_lexica_version": version,
                "subscribed_translations": subscribed_languages,
            }
            data_platform_config_lexica_client.put_se_lexica_version(
                tenant_name=tenant, json_body=json_body, stack_name=self.config.STACK
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Tenant not found")
            raise e

    @lru_cache(maxsize=1)
    def get_tenant_list(self) -> List[str]:
        data_platform_config_stack_client = get_data_platform_config_stack_client(self.config)
        try:
            resp = data_platform_config_stack_client.get_by_name(stack_name=self.config.STACK)
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Stack not found")
            raise e
        tenants = resp.content.get("tenants", [])
        tenants = [
            tenant.get("name")
            for tenant in tenants
            if tenant.get("name") not in [None, self.config.STACK]
        ]
        return tenants
