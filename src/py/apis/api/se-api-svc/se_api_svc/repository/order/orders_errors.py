# type: ignore
import datetime as dt
from api_sdk.es_dsl.base import Exists, <PERSON><PERSON><PERSON><PERSON>, NotExpired, Or, SearchModel, TermFilter
from api_sdk.es_dsl.features import Nested, RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.full_text_search import (
    COMMS_TEXT_VALIDATION_ERRORS_SEARCH_FIELDS,
    get_free_text_search_filters,
)
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.search import apply_sorts, parse_sort_str
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.static import FieldType
from api_sdk.utils.intervals import get_interval_in_millis_from_params
from api_sdk.utils.utils import parse_datetime
from cached_property import cached_property  # pants: no-infer-dep
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.comms_surveillance.common import CustomClassProperty
from se_api_svc.repository.order.common import (
    ORDERS_AND_TRADES_NESTED_PATHS,
    ORDERS_TREND_CHART_SPEC,
)
from se_api_svc.schemas.order import Order
from se_api_svc.schemas.orders.common import OrdersTrendChart
from typing import Dict, Optional, Union


class OrderErrorSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        message: Optional[str]
        search: Optional[str] = None

    params: Params

    @CustomClassProperty
    def features(cls, instance):
        return [
            ModelFilter(model=Order),
            Nested(Exists(field="&validationErrors"), path="&validationErrors"),
            NotExpired,
            RangeFilter(field="timestamps.orderSubmitted"),
            Nested(
                TermFilter(param="message", name="&validationErrors.message"),
                path="&validationErrors",
            ),
            FlangFilter.simple(
                param="f",
                nested_paths=ORDERS_AND_TRADES_NESTED_PATHS,
            ),
            Or(
                *get_free_text_search_filters(
                    field_mapping={
                        FieldType.NESTED.value: {
                            "&validationErrors": COMMS_TEXT_VALIDATION_ERRORS_SEARCH_FIELDS
                        }
                    },
                    qs_param="search",
                )
            ),
        ]

    default_sort_order = ["transactionDetails.tradingDateTime"]

    sort_handlers = {
        "numValidationErrors": lambda sort: {
            "_script": {
                "type": "number",
                "script": """
                    if (!params["_source"].containsKey("&validationErrors")) return 0;
                    return params["_source"]["&validationErrors"].size();
                """,
                "order": sort.order,
            }
        },
    }


class OrderErrorTrendsTimelineAgg(OrderErrorSearchBase):
    class Params(OrderErrorSearchBase.Params):
        interval: Optional[str]
        buckets: Optional[int] = None

    params: Params

    @cached_property
    def interval_in_millis(self) -> int:
        return get_interval_in_millis_from_params(
            self.params, min_interval=15 * 60 * 1000
        )  # 15 minutes

    def build_aggs(self) -> Optional[Dict]:
        return {
            "TIMES": {
                "date_histogram": {
                    "field": "transactionDetails.tradingDateTime",
                    "fixed_interval": f"{self.interval_in_millis}ms",
                    "format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                },
            }
        }


class OrdersWithValidationErrorsAgg(OrderErrorSearchBase):
    class Params(OrderErrorSearchBase.Params):
        pass

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        return {
            "CHECKS": {
                "nested": {"path": "&validationErrors"},
                "aggs": {
                    "CATEGORIES": {
                        "terms": {
                            "field": "&validationErrors.category",
                            "size": ES_MAX_AGG_SIZE,
                        },
                        "aggs": {
                            "TRANSACTIONS": {"reverse_nested": {}},
                            "CODES": {
                                "terms": {
                                    "field": "&validationErrors.code",
                                    "size": ES_MAX_AGG_SIZE,
                                },
                                "aggs": {
                                    "TRANSACTIONS": {"reverse_nested": {}},
                                    "MESSAGES": {
                                        "terms": {
                                            "field": "&validationErrors.message",
                                            "size": ES_MAX_AGG_SIZE,
                                        },
                                        "aggs": {
                                            "TRANSACTIONS": {"reverse_nested": {}},
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            }
        }


class OrderErrorRepository(RepoHelpersMixin):
    async def get_error_orders(self, **params) -> RawResult:
        return await self.get_many(
            search_model=OrderErrorSearchBase(**params),
        )

    async def get_trends_summary_by_type(
        self,
        trend_chart: OrdersTrendChart,
        **search_params,
    ):
        field, nested_path, _ = ORDERS_TREND_CHART_SPEC[trend_chart]
        if nested_path:
            aggs = {
                "NESTED": {
                    "nested": {"path": nested_path},
                    "aggs": {"SUBJECTS": {"terms": {"field": field, "size": 5}}},
                }
            }
        else:
            aggs = {"SUBJECTS": {"terms": {"field": field, "size": 5}}}

        result = await self.get_aggs(
            search_model_cls=OrderErrorSearchBase,
            aggs=aggs,
            **search_params,
        )
        return list(result.iter_bucket_agg("NESTED.SUBJECTS" if nested_path else "SUBJECTS"))

    async def get_error_summary(self, **params):
        results = []
        sorts = parse_sort_str(
            params.pop("sort"), allowed_fields=("message", "category", "code", "count")
        )

        raw_result = await self.repo.get_aggs(
            search_model=OrdersWithValidationErrorsAgg(**params),
        )

        for cat_bucket in raw_result.iter_raw_bucket_agg("CHECKS.CATEGORIES"):
            for code_bucket in cat_bucket["CODES"]["buckets"]:
                for msg_bucket in code_bucket["MESSAGES"]["buckets"]:
                    results.append(
                        {
                            "message": msg_bucket["key"],
                            "code": code_bucket["key"],
                            "category": cat_bucket["key"],
                            "count": msg_bucket["TRANSACTIONS"]["doc_count"],
                        }
                    )
                if not code_bucket["MESSAGES"]["buckets"]:
                    results.append(
                        {
                            "message": None,
                            "code": code_bucket["key"],
                            "category": cat_bucket["key"],
                            "count": code_bucket["TRANSACTIONS"]["doc_count"],
                        }
                    )
            if not cat_bucket["CODES"]["buckets"]:
                results.append(
                    {
                        "message": None,
                        "code": None,
                        "category": cat_bucket["key"],
                        "count": cat_bucket["TRANSACTIONS"]["doc_count"],
                    }
                )
        results = apply_sorts(results, sorts)
        return results

    async def get_trends_timeline(self, **search_params):
        start = search_params.pop("start", None)
        end = search_params.pop("end", None)

        if not start:
            start = await self.get_min_value(
                search_model_cls=OrderErrorSearchBase,
                field="transactionDetails.tradingDateTime",
                as_string=True,
                **{k: v for k, v in search_params.items() if k not in ["buckets", "interval"]},
            )
        if not end:
            end = dt.datetime.utcnow().replace(tzinfo=dt.timezone.utc)

        search_model = OrderErrorTrendsTimelineAgg(
            start=start, end=end, **{k: v for k, v in search_params.items()}
        )
        agg_items = await self.get_aggs(search_model=search_model)

        return {
            "start": parse_datetime(start).isoformat() if start else None,
            "end": parse_datetime(end).isoformat() if end else None,
            "intervalInMillis": search_model.interval_in_millis,
            "buckets": [
                {
                    "timestamp": bucket["key"],
                    "datetime": bucket["key_as_string"],
                    "count": bucket["doc_count"],
                }
                for bucket in agg_items.iter_raw_bucket_agg("TIMES")
            ],
        }
