# type: ignore
import datetime as dt
import logging
import pydantic
import re
from api_sdk.es_dsl.base import (
    <PERSON><PERSON><PERSON>er,
    Not,
    NotExpiredFilter,
    Or,
    QueryString,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.flang import <PERSON>langFilter
from api_sdk.es_dsl.params import ModelParams, SearchModelParams
from api_sdk.exceptions import NotFound
from api_sdk.full_text_search import (
    CASE_TEXT_SEARCH_FIELDS,
    COMMS_TEXT_NESTED_NON_NESTED_MAPPING,
    CSURV_ALERT_TEXT_SEARCH_FIELDS,
    ORDER_TEXT_SEARCH_FIELDS,
    get_free_text_search_filters,
)
from api_sdk.models.request_params import NonPaginatedDatedListParams
from api_sdk.schemas.base import APIModel, Field, RecordModel
from api_sdk.schemas.helpers import utc_timestamp_millis
from api_sdk.utils.id import generate_id
from api_sdk.utils.utils import <PERSON><PERSON><PERSON>, make_slug
from enum import Enum, auto
from pydantic import root_validator, validator
from se_api_svc.core.constants import ES_MAX_UTF8_LENGTH
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.schemas.account import AccountUser
from se_api_svc.schemas.attachment import Attachment
from se_api_svc.utils.random import generate_random_alphanumeric
from se_elastic_schema.components.communication.timestamp import Timestamp as CommsTimestamp
from se_elastic_schema.components.mifid2.rts6_24_timestamps import Rts624Timestamps
from se_elastic_schema.models.tenant.mifid2.order import Order as SeOrder
from se_elastic_schema.static.case import AssignReason, ResolutionCategoryCommsEnum
from se_elastic_schema.static.case import Status as CaseStatus
from se_elastic_schema.static.provenance import TaskStatus
from se_elastic_schema.static.reference import CaseRiskScore
from se_elastic_schema.static.surveillance import (
    BehaviourQueryKind,
)
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

log = logging.getLogger(__name__)

BREACH_RESOLUTION_CATEGORIES = [
    ResolutionCategoryCommsEnum.EXCHANGE_BREACH,
    ResolutionCategoryCommsEnum.POLICY_BREACH,
    ResolutionCategoryCommsEnum.REGULATORY_BREACH,
]


class CaseRecordModels(StringEnum):
    CaseEmail = auto()
    CaseMessage = auto()
    CaseText = auto()
    CaseCall = auto()
    CaseMeeting = auto()
    CaseOrder = auto()


class CaseCommentAttachmentIn(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    name: str
    lastModified: Optional[int]
    lastModifiedDate: Optional[str]
    size: int
    type: str
    path: str
    base64Content: str


class CaseCommentIn(APIModel):
    comment: str
    timestamp: int = None
    attachments: List[CaseCommentAttachmentIn] = Field(default_factory=list)

    @pydantic.validator("comment")
    def check_if_comment_empty(cls, comment):
        if not comment or not comment.strip():
            raise ValueError("Comment must not be empty")

        return comment.strip()


class EventAttachment(APIModel):
    id: Optional[str]
    name: Optional[str] = Field(None)
    size: Optional[int] = Field(None)
    mimeTag: Optional[str]
    objectUri: Optional[str] = Field(default=None)

    class Config:
        orm_mode = True


class EventAttachmentIn(APIModel):
    name: Optional[str] = Field(None)
    content: str  # base-64 encoded content
    mimeTag: Optional[str]

    @root_validator
    def validate_mime_tag(cls, values):
        pattern = r"^[a-zA-Z0-9\-]+/[a-zA-Z0-9\-]+$"
        match = re.match(pattern, values["mimeTag"])
        if match is None:
            values["mimeTag"] = None
        return values

    @root_validator
    def ensure_name(cls, values):
        if values.get("name") is None:
            values["name"] = generate_random_alphanumeric()
        return values


class CaseComment(APIModel):
    # It's not really a record model, only exists under a Case

    class Config:
        extra = pydantic.Extra.allow

    user: Optional[Dict]
    comment: Optional[str]
    timestamp: Optional[int] = Field(default_factory=utc_timestamp_millis)
    attachments: List[Attachment] = Field(default_factory=list)


class CaseWorkflow(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    class Event(StringEnum):
        OPENED = auto()
        CLOSED = auto()
        REOPENED = auto()
        UNRESOLVED = auto()
        EXPORTED_RAW = auto()
        EXPORTED_EXCEL = auto()
        ASSIGNEE_ASSIGNED = auto()
        OWNER_ASSIGNED = auto()
        COMMENT_ADDED = auto()
        COMMENT_WITH_ATTACHMENT_ADDED = auto()
        RECORD_ADDED = auto()
        RECORD_REMOVED = auto()

    assignDescription: Optional[str]
    assignReason: Optional[AssignReason]
    assignReasonCustom: Optional[str]
    user: Optional[AccountUser]
    event: Optional[Event]
    timestamp: Optional[int]


# ToDo: There are two RecordIn, refactor.
class RecordIn(pydantic.BaseModel):
    """Input model only requiring an input ID and ignoring all else."""

    id_: Optional[str] = Field(default=None)
    alert_id: Optional[str] = Field(default=None, alias="alertId")
    scenario_id: Optional[str] = Field(default=None, alias="scenarioId")
    # Following field is just to do some operations on specific type of alerts.
    query_kind: Optional[BehaviourQueryKind] = Field(None, alias="queryKind")

    class Config:
        extra = pydantic.Extra.forbid

    @root_validator(pre=True)
    def check_id_or_key(cls, values):
        has_id = "id_" in values and values["id_"] is not None
        has_key = "key_" in values and values["key_"] is not None
        assert has_id or has_key
        return values


class RecordInIdOrKey(RecordIn):
    key_: Optional[str] = Field(default=None)
    data: Optional[Dict] = Field(default=None)

    @root_validator(pre=True)
    def check_id_or_key(cls, values):
        has_id = "id_" in values and values["id_"] is not None
        has_key = "key_" in values and values["key_"] is not None
        assert has_id or has_key
        return values

    @classmethod
    def from_record_in(cls, record: RecordIn):
        return cls(**record.dict(by_alias=True))

    @property
    def identifier(self):
        if self.id_:
            return self.id_
        return self.key_

    def uses_key(self):
        return self.id_ is None and self.key_ is not None


class CaseRecord(RecordModel):
    class Config:
        extra = pydantic.Extra.allow
        index_suffix = [
            "case",
            "case_email",
            "case_call",
            "case_message",
            "case_text",
            "case_meeting",
            "case_order",
        ]
        index_needs_model_filter = True
        model_name: Union[str, List[str]] = [el.value for el in CaseRecordModels]

    original_record_id: Optional[str] = Field(None, alias="originalRecordId")

    @root_validator(pre=True)
    def check_link(cls, values):
        # Ignore this rule when creating a new record.
        if "&id" not in values:
            return values

        # Older records either don't have a &link, or have it populated with a key
        if not values.get("originalRecordId"):
            values["originalRecordId"] = values["&id"]
        return values

    @classmethod
    def from_record(cls, record, case_id):
        """Instantiate a new CaseRecord from original record data. Use this
        when adding a record to case from an original record. For example:

            email = emails_repo.get_one(email_id)
            case_email = CaseEmail.from_record(email, "case-123")

        :param record: The original record to attach to a case
        :param case_id: The ID of the case this record will be bound to
        """
        data = record.to_dict()

        # Strip record meta keys -- the `&id` is preserved (duplicated) for backwards
        # compatibility.
        del data["&id"]
        del data["&traitFqn"]
        del data["&key"]
        del data["&model"]
        del data["&timestamp"]
        del data["&user"]
        del data["&validationErrors"]
        del data["&cascadeId"]
        del data["&version"]
        if "flags" in data and "TCAFlagStatus" in data["flags"]:
            # Some OrderStates have this flag, but abaci doesn't support it - stripping.
            del data["flags"]["TCAFlagStatus"]

        data["originalRecordId"] = record.id_
        data["&parent"] = case_id

        return cls(**data)

    @classmethod
    def from_case_record(cls, case_record):
        """Casts a CaseRecord into the current CaseRecord class.

        This is useful when retrieving a mixed collection of case
        records as generic CaseRecords and converting them to their
        specific classes later on.
        """
        return cls(**case_record.dict(by_alias=True))


class MyTimestamps(Rts624Timestamps, CommsTimestamp):
    class Config(Rts624Timestamps.Config):
        extra = pydantic.Extra.ignore


class CaseRecordSummary(RecordModel):
    id_: Optional[str] = Field(None, alias="&id")
    model_: Optional[str] = Field(None, alias="&model")

    class Config:
        extra = pydantic.Extra.ignore
        index_suffix = [
            "case",
            "case_email",
            "case_call",
            "case_message",
            "case_text",
            "case_meeting",
            "case_order",
        ]
        index_needs_model_filter = True
        model_name: Union[str, List[str]] = [el.value for el in CaseRecordModels]


class CaseEmail(CaseRecord):
    class Config(CaseRecord.Config):
        model_name = "CaseEmail"
        index_suffix = "case_email"


class CaseMeeting(CaseRecord):
    class Config(CaseRecord.Config):
        model_name = "CaseMeeting"
        index_suffix = "case_meeting"


class CaseMessage(CaseRecord):
    class Config(CaseRecord.Config):
        model_name = "CaseMessage"
        index_suffix = "case_message"


class CaseText(CaseRecord):
    class Config(CaseRecord.Config):
        model_name = "CaseText"
        index_suffix = "case_text"


class CaseCall(CaseRecord):
    class Config(CaseRecord.Config):
        model_name = "CaseCall"
        index_suffix = "case_call"


# Yes this is duplicated from se_schema. Sue me or delete pydantic
class CaseOrder(CaseRecord):
    class Config(SeOrder.Config):
        model_name = "CaseOrder"
        index_suffix = "case_order"
        extra = pydantic.Extra.allow


class CaseOrderState(CaseRecord):
    class Config(SeOrder.Config):
        model_name = "CaseOrderState"
        extra = pydantic.Extra.allow
        index_suffix = "case_order"


class CaseTransaction(CaseRecord):
    class Config(SeOrder.Config):
        model_name = "CaseTransaction"
        extra = pydantic.Extra.allow
        index_suffix = "case_order"


class CaseRecordSummarySearch(SearchModel):
    class Params(ModelParams):
        case_id: Union[str, List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        model_name: Optional[str] = None
        search: Optional[str] = None

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            NotExpiredFilter,
            TermFilter(name="&parent", param="case_id"),
            TermFilter(name="&model", param="model_name"),
            Or(
                RangeFilter(field="timestamps.timestampStart"),
                RangeFilter(field="timestamps.orderSubmitted"),
                RangeFilter(field="timestamps.tradingDateTime"),
            ),
            Or(
                *get_free_text_search_filters(
                    COMMS_TEXT_NESTED_NON_NESTED_MAPPING, qs_param="search"
                ),
                QueryString(
                    add_wildcards=True,
                    analyze_wildcard=True,
                    fields=CSURV_ALERT_TEXT_SEARCH_FIELDS,
                    default_operator="AND",
                    param="search",
                    allow_leading_wildcard=False,
                ),
            ),
        ]
        super().__init__(**params)


class CaseRecordSearch(SearchModel):
    class Params(ModelParams):
        f: Optional[str] = None
        search: Optional[str] = None
        case_id: Union[str, List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        original_id: Optional[str]
        record_id: Optional[Union[str, List[str]]] = None
        s3_key: Optional[Union[str, List[str]]] = None
        order_status: Optional[List[str]] = None
        not_order_status: Optional[List[str]] = None

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            NotExpiredFilter,
            TermFilter(name="&parent", param="case_id"),
            TermFilter(name="originalRecordId", param="original_id"),
            TermFilter(name="executionDetails.orderStatus", param="order_status"),
            Not(TermFilter(name="executionDetails.orderStatus", param="not_order_status")),
            TermFilter(name="&id", param="record_id"),
            Or(
                RangeFilter(field="timestamps.timestampStart"),
                RangeFilter(field="timestamps.orderSubmitted"),
                RangeFilter(field="timestamps.tradingDateTime"),
            ),
            Or(
                TermFilter(name="sourceKey", param="s3_key"),
                TermFilter(name="metadata.source.fileInfo.location.key", param="s3_key"),
            ),
            FlangFilter.simple(param="f"),
            QueryString(param="search", fields=ORDER_TEXT_SEARCH_FIELDS)
            if model == CaseOrder
            else Or(
                *get_free_text_search_filters(
                    COMMS_TEXT_NESTED_NON_NESTED_MAPPING, qs_param="search"
                ),
                TermFilter(name="&hash", param="search"),
            ),
        ]
        super().__init__(**params)


class CaseInstrumentsAggs(SearchModel):
    class Params(ModelParams):
        f: Optional[str] = None
        case_id: Union[str, List[str]]
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            NotExpiredFilter,
            TermFilter(name="&parent", param="case_id"),
        ]
        super().__init__(**params)

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {
            "INSTRUMENTS": {"terms": {"field": "instrumentDetails.instrument.instrumentIdCode"}}
        }
        return aggs_query


class CaseRecordCounts(CaseRecordSearch):
    class Params(ModelParams):
        case_id: Union[str, List[str]]
        original_id: Optional[str]

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            # TermFilter(name="&parent", param="case_id"),
            NotExpiredFilter,
        ]
        super().__init__(**params)

    @staticmethod
    def build_single_aggs_qeury(case_id) -> Dict:
        return {
            "filter": {"term": {"&parent": case_id}},
            "aggs": {
                "CASE_COUNTS": {
                    "filter": {"bool": {"must_not": {"term": {"&model": "CaseOrder"}}}},
                    "aggs": {
                        "CASE_COMMS_COUNTS": {
                            "terms": {"field": "&model"},
                        }
                    },
                },
                "CaseOrder": {"filter": {"term": {"executionDetails.orderStatus": "NEWO"}}},
                "CaseOrderState": {
                    "filter": {
                        "bool": {
                            "must_not": {"term": {"executionDetails.orderStatus": "NEWO"}},
                            "must": {"term": {"&model": "CaseOrder"}},
                        }
                    }
                },
            },
        }

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {i: self.build_single_aggs_qeury(i) for i in self.params.case_id}
        return aggs_query


class CaseRecordWithRawFileAggs(CaseRecordSearch):
    class Params(ModelParams):
        case_id: Union[str, List[str]]
        original_id: Optional[str]
        s3_key: Optional[Union[str, List[str]]] = None

    params: Params

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            TermFilter(name="&parent", param="case_id"),
            NotExpiredFilter,
            Or(
                TermFilter(name="metadata.source.fileInfo.location.key", param="s3_key"),
                TermFilter(name="sourceKey", param="s3_key"),
            ),
        ]
        super().__init__(**params)

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {
            "RECORDS_KEY": {
                "terms": {"field": "metadata.source.fileInfo.location.key"},
            },
            "RECORDS_SOURCE_KEY": {
                "terms": {"field": "sourceKey"},
            },
        }
        return aggs_query


class Applied(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    reason: Optional[str] = Field(None)
    timestamp: Optional[dt.datetime] = Field(None)
    user_id: Optional[str] = Field(None)
    user_name: Optional[str] = Field(None)


class OtherRole(APIModel):
    name: Optional[str] = Field(None)
    role: Optional[str] = Field(None)

    def __hash__(self):
        return hash((type(self),) + (self.role, self.name))


class OtherRoleEdit(APIModel):
    name: Optional[str] = Field(None)
    role: Optional[str] = Field(None)
    new_name: Optional[str] = Field(None)
    new_role: Optional[str] = Field(None)


class Released(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    category: Optional[str] = Field(None)
    reason: Optional[str] = Field(None)
    timestamp: Optional[dt.datetime] = Field(None)
    user_id: Optional[str] = Field(None)
    user_name: Optional[str] = Field(None)


class LegalHold(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    applied: Optional[Applied] = Field(None)
    is_applied: Optional[bool] = Field(None)
    # TODO: remove below field from everywhere.
    # is_released: Optional[bool] = Field(None)
    released: Optional[Released] = Field(None)


class Subject(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    id: Optional[str] = Field(None)
    name: Optional[str] = Field(None)

    def __hash__(self):
        return hash((type(self),) + (self.id, self.name))


class SubjectEdit(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    id: Optional[str] = Field(None)
    name: Optional[str] = Field(None)
    new_id: Optional[str] = Field(None)
    new_name: Optional[str] = Field(None)


class WorkerCustom(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    id: Optional[str] = Field(None)
    name: Optional[str] = Field(None)

    def __hash__(self):
        return hash((type(self),) + (self.id, self.name))


class WorkerCustomEdit(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    id: Optional[str] = Field(None)
    name: Optional[str] = Field(None)
    new_id: Optional[str] = Field(None)
    new_name: Optional[str] = Field(None)


class Case(RecordModel):
    class Config:
        model_name = "Case"
        index_suffix = "case"
        trait_fqn = "case/case"
        index_needs_model_filter = True
        extra = pydantic.Extra.allow
        use_enum_values = True  # to populate models with the value property of enums

    slug: Optional[str] = Field(default=None)
    assignee: Optional[AccountUser] = Field(None)
    countryCode: Optional[str] = Field(None)
    closed: Optional[dt.datetime] = Field(default=None)
    closedBy: Optional[str] = Field(default=None)
    closedComment: Optional[str] = Field(default=None)
    closedResolutionCategory: Optional[ResolutionCategoryCommsEnum] = Field(default=None)
    closedResolutionCategoryCustom: Optional[str] = Field(default=None)
    closedSTORRequired: Optional[bool] = Field(default=None)
    closedWithBreach: Optional[bool] = Field(default=None)
    comments: List[CaseComment] = Field(default_factory=list)
    create_reason: Optional[str] = Field(None, alias="createReason")
    created: Optional[dt.datetime] = Field(default_factory=dt.datetime.utcnow)
    createdBy: Optional[str]
    createdByAdmin: Optional[bool] = Field(default=False)
    deadline: Optional[Union[dt.date, dt.datetime]] = Field(default=None)
    deleted: Optional[dt.datetime] = Field(None)
    deletedBy: Optional[str] = Field(None)
    description: Optional[str] = Field(default=None)
    legal_hold: Optional[LegalHold] = Field(None, alias="legalHold")
    name: Optional[str] = Field(default=None)
    origin: Optional[str] = Field(default=None)
    origin_reference_ids: Optional[List[str]] = Field(None, alias="originReferenceIds")
    owner: Optional[AccountUser] = Field(None)
    otherRoles: Optional[List[OtherRole]] = Field(None)
    requested_by: Optional[str] = Field(alias="requestedBy", default=None)
    risk_score: Optional[CaseRiskScore] = Field(alias="riskScore", default=None)
    status: CaseStatus = Field(default=None)
    subjects: Optional[List[Subject]] = Field(None)
    workerCustom: Optional[List[WorkerCustom]] = Field(None)
    workflow: List[CaseWorkflow] = Field(default_factory=list)

    def find_comment_attachment(self, attachment_id) -> Tuple[CaseComment, Attachment]:
        comment = next(
            (comm for comm in self.comments if attachment_id in [a.id_ for a in comm.attachments]),
            None,
        )
        if comment is None:
            raise NotFound(model=CaseComment, id=attachment_id)
        attachment = next((att for att in comment.attachments if att.id_ == attachment_id), None)
        if attachment is None:
            raise NotFound(model=Attachment, id=attachment_id)
        return comment, attachment

    def update_from(self, *args, **kwargs):
        if not self.slug and "slug" not in kwargs:
            # When updating older Cases, back-populate their slug
            self.slug = make_slug("case")

        return super().update_from(*args, **kwargs)


class CaseSearch(SearchModel):
    class Params(ModelParams):
        case_id: str
        slug: Optional[str]

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=model),
            NotExpiredFilter,
            Or(
                TermFilter(name="&parent", param="case_id"),
                TermFilter(name="slug", param="slug"),
            ),
        ]
        super().__init__(**params)


class CaseByIdOrSlugSearch(SearchModel):
    class Params(ModelParams):
        identifier: str

    def __init__(self, *, model=None, **params):
        self.features = [
            ModelFilter(model=Case),
            NotExpiredFilter,
            Or(
                TermFilter(name="&id", param="identifier"),
                TermFilter(name="slug", param="identifier"),
            ),
        ]
        super().__init__(**params)


class CasesSearch(SearchModel):
    class Params(ModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        search: Optional[str] = None

    features = [
        ModelFilter(model=Case),
        NotExpiredFilter,
        RangeFilter(field="created"),
        FlangFilter.simple(param="f"),
        QueryString(param="search", fields=CASE_TEXT_SEARCH_FIELDS),
    ]

    def __init__(self, **params):
        super().__init__(**params)


class CasesByStatusAgg(CasesSearch):
    class Params(CasesSearch.Params):
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    features = CasesSearch.features + [RangeFilter(field="created")]

    def build_aggs(self) -> Optional[Dict]:
        return {
            "STATUSES": {
                "filters": {
                    "filters": {
                        "OVERDUE": {
                            "bool": {
                                "filter": [
                                    {"term": {"status": CaseStatus.OPEN}},
                                    {"range": {"deadline": {"lt": "now/d"}}},
                                ]
                            }
                        },
                        "DUE": {
                            "bool": {
                                "filter": [
                                    {"term": {"status": CaseStatus.OPEN}},
                                    {
                                        "range": {
                                            "deadline": {
                                                "gte": "now/d",
                                                "lte": "now+14d/d",
                                            }
                                        }
                                    },
                                ]
                            }
                        },
                        "NOT_YET_DUE": {
                            "bool": {
                                "filter": [
                                    {"term": {"status": CaseStatus.OPEN}},
                                    {"range": {"deadline": {"gt": "now+14d/d"}}},
                                ]
                            }
                        },
                        "CLOSED": {"term": {"status": CaseStatus.CLOSED}},
                        "NO_DUE_DATE": {
                            "bool": {
                                "must_not": {"exists": {"field": "deadline"}},
                                "filter": {"term": {"status": CaseStatus.OPEN}},
                            }
                        },
                    }
                }
            }
        }


class CasesGroupedBy(SearchModel):
    class Params(SearchModelParams):
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        search: Optional[str] = None
        f: Optional[str]

    params: Params
    features = CasesSearch.features

    def __init__(self, group_by="owner.userId", source_include_fields: List = None, **params):
        super().__init__(**params)
        self.group_by = group_by
        self.source_include_fields = source_include_fields

    def build_aggs(self) -> Optional[Dict]:
        # By default, return only 10 aggs.
        # If f string is there, then return 10000 max.
        size = 10 if not self.params.f else 10000

        aggs_query = {
            "GROUPED_BY": {
                "terms": {"field": self.group_by.value, "size": size},
            }
        }

        if self.source_include_fields:
            aggs_query["GROUPED_BY"]["terms"]["aggs"] = {
                "HIT_INFO": {
                    "top_hits": {
                        "size": 1,
                        "_source": {"includes": self.source_include_fields},
                    }
                }
            }
        return aggs_query


class CaseRecordsSummary(SearchModel):
    class Params(SearchModelParams):
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        search: Optional[str] = None
        f: Optional[str]
        case_id: Optional[str]

    params: Params
    features = [*CasesSearch.features, TermFilter(name="&id", param="case_id")]

    def __init__(self, case_id, **params):
        self.case_id = case_id
        super().__init__(case_id=case_id, **params)

    def build_aggs(self) -> Optional[Dict]:
        aggs_query = {
            "GROUPED_BY": {
                "terms": {"field": "case_id"},
            }
        }
        return aggs_query


class CaseAttachmentType(str, Enum):
    EMAIL = "Email"
    CALL = "Call"
    MESSAGE = "Message"
    TEXT = "Text"


class CaseAttachment(APIModel):
    id: str
    type: CaseAttachmentType


class CaseOrigin(StringEnum):
    BEST_EX = "Best Ex"
    SURVEILLANCE = "Surveillance"
    MAR = "Market Abuse"
    CASE = "Case creation"
    COMMS_MESSAGE = "Comms Message"
    # ENG-1256
    COMMS_SURVEILLANCE = "Comms Surveillance"
    TRADE_SURVEILLANCE = "Trade Surveillance"
    COMMUNICATIONS = "Communications"
    ORDERS_AND_TRADES = "Orders & Trades"
    ADHOC = "Adhoc"


class LegalHoldIn(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    reason: Optional[str] = Field(None)
    is_applied: bool = Field(None)


class UpdateCaseIn(APIModel):
    """Input data container for a Case Update."""

    name: str
    description: Optional[str]
    countryCode: Optional[str] = Field(None)
    requested_by: Optional[str] = Field(default=None, alias="requestedBy")
    create_reason: Optional[str] = Field(None, alias="createReason")
    risk_score: Optional[CaseRiskScore] = Field(alias="riskScore", default=None)
    originReferenceIds: Optional[List[str]] = Field(None)
    otherRoles: Optional[List[OtherRole]] = None
    deadline: Optional[dt.date] = Field(None)
    subjects: Optional[List[Subject]] = Field(default=None)
    assignee: Optional[str] = Field(None)
    owner: Optional[str] = Field(None)

    @root_validator
    def handle_duplicate_elements(cls, values):
        """Remove duplicate elements from the list of originReferenceIds."""
        list_fields = ["workerCustom", "subjects"]
        for f in list_fields:
            if values.get(f):
                values[f] = list(set(values[f]))

        return values


class NewCaseIn(APIModel):
    """Input data container for use when creating a new case only.

    Supports a list of records to be added when the case is created.
    """

    name: str
    description: Optional[str]
    requested_by: Optional[str] = Field(default=None, alias="requestedBy")
    create_reason: Optional[str] = Field(None, alias="createReason")
    risk_score: Optional[CaseRiskScore] = Field(default=None, alias="riskScore")
    owner: Optional[str] = Field(
        None,
        title="owner ID",
    )
    assignee: Optional[str] = Field(None, title="assignee ID")
    country_code: Optional[str] = Field(None, alias="countryCode")
    deadline: Optional[dt.date] = Field(None)
    origin: Optional[CaseOrigin] = CaseOrigin.CASE
    origin_reference_ids: Optional[List[str]] = Field(None, alias="originReferenceIds")
    other_roles: Optional[List[OtherRole]] = Field(None, alias="otherRoles")
    # records: Optional[List[RecordIn]]
    subjects: Optional[List[Subject]] = Field(default=None)

    # To generate SystemEvent.AlertGeneration.
    # While generating AlertResolved, we're going to create
    # SystemEvent.AlertResolved for all records which has SystemEvent.AlertGeneration.
    alert_ids: Optional[List[str]] = Field(None, alias="alertIds")

    @validator("description", pre=True)
    def validate_description(cls, v):
        """ES supports a max 32766 utf-8 encoded field."""
        if v and len(v.encode("utf-8")) > ES_MAX_UTF8_LENGTH:
            log.warning(
                "Description contains data with UTF-8 encoded length %s, which exceeds the "
                "maximum supported length in Elasticsearch (%s). Truncating to ensure compatibility.",  # noqa: E501
                len(v),
                ES_MAX_UTF8_LENGTH,
            )
            return v[:ES_MAX_UTF8_LENGTH]

        return v

    @staticmethod
    def sanitize_account_user(account_user: AccountUser) -> AccountUser:
        return AccountUser.sanitize_account_user(account_user)

    async def to_case(
        self,
        user: str,
        account_user: AccountUser,
        slug: str,
        user_repo: UserRepository,
        is_admin: bool = False,
    ) -> Case:
        """Converts input CaseIn into a proper Case.

        :param user: the creator's username as a string
        :param account_user: the AccountUser record of the creator
        :param is_admin: Bool value indicating if the creator is an admin
        :returns: a pre-populated Case object
        """

        # User to be set as creator
        account_user = self.sanitize_account_user(account_user)

        # User to be set as owner is passed else use account_user
        owner = (
            self.sanitize_account_user(await user_repo.get_user_by_id(self.owner))
            if self.owner
            else account_user
        )
        # User to be set as assignee is passed else use account_user
        assignee = (
            self.sanitize_account_user(await user_repo.get_user_by_id(self.assignee))
            if self.assignee
            else account_user
        )

        # Worker custom is any user who interacts with the case.
        # So, while creating the case, there will be only one user who created the case.
        first_worker = WorkerCustom.from_dict(
            {
                "id": account_user.userId,
                "name": account_user.name,
            }
        )

        return Case(
            id_=generate_id("case"),
            slug=slug,
            user_=user,
            assignee=assignee,
            countryCode=self.country_code,
            owner=owner,
            createdBy=user,
            createdByAdmin=is_admin,
            create_reason=self.create_reason,
            legalHold=None,
            name=self.name,
            description=self.description,
            requested_by=self.requested_by,
            risk_score=self.risk_score,
            deadline=self.deadline,
            status=CaseStatus.OPEN,
            subjects=self.subjects,
            origin=self.origin,
            origin_reference_ids=self.origin_reference_ids,
            workerCustom=[first_worker],
            otherRoles=self.other_roles,
        )


class GroupIn(StringEnum):
    ASSIGNEE = "assignee.userId"
    OWNER = "owner.userId"
    ORIGIN = "origin"


class LegalHoldReleaseCategory(StringEnum):
    CASE_CLOSURE = "CASE_CLOSURE"
    RELEASED_BY_USER = "RELEASED_BY_USER"


class BulkStatus(StringEnum):
    """A subset of Celery's states that are returned as a task state.

    Unfortunately Celery doesn't provide a true enum so this is
    reimplemented here.
    """

    PENDING = auto()
    STARTED = auto()
    RETRY = auto()
    FAILURE = auto()
    SUCCESS = auto()

    @classmethod
    def priorities(cls) -> Dict["BulkStatus", int]:
        return {
            BulkStatus.FAILURE: 4,
            BulkStatus.STARTED: 3,
            BulkStatus.RETRY: 2,
            BulkStatus.PENDING: 1,
            BulkStatus.SUCCESS: 0,
        }

    @classmethod
    def min_priority(cls) -> int:
        return min(cls.priorities().values())

    @classmethod
    def min_priority_status(cls) -> "BulkStatus":
        return [k for k, v in cls.priorities().items() if v == cls.min_priority()][0]

    @classmethod
    def max_priority(cls) -> int:
        return max(cls.priorities().values())

    @classmethod
    def max_priority_status(cls) -> "BulkStatus":
        return [k for k, v in cls.priorities().items() if v == cls.max_priority()][0]

    @property
    def priority(self) -> int:
        return self.priorities()[self]


class BulkTask(APIModel):
    id: str
    status: BulkStatus
    detail: Optional[List[str]]

    @classmethod
    def aggregate(cls, id: str, statuses: List[Union[BulkStatus, str]], **kwargs) -> "BulkTask":
        """Aggregate a list of task statuses into a single status."""

        # Normalise
        def normalise_status(status: Union[BulkStatus, str]) -> BulkStatus:
            if issubclass(type(status), BulkStatus):
                return status
            try:
                return BulkStatus(status)
            except AttributeError:
                return None

        # The original status is that with the lowest priority
        agg_status: BulkStatus = BulkStatus.min_priority_status()
        normalised_statuses = list(filter(None, (normalise_status(s) for s in statuses)))
        if len(normalised_statuses) == 0:
            return cls(id=id, status=BulkStatus.PENDING, **kwargs)
        for status in normalised_statuses:
            if status.priority > agg_status.priority:
                agg_status = status
            if agg_status.priority == BulkStatus.max_priority:
                break
        return cls(id=id, status=agg_status, **kwargs)


class BulkImportType(StringEnum):
    COMMS = auto()
    ORDERS = auto()


class CaseBulkFilter(pydantic.BaseModel):
    type: BulkImportType
    start: Optional[int]
    end: Optional[int]
    f: str
    search: Optional[str] = None

    def to_params(self) -> NonPaginatedDatedListParams:
        return NonPaginatedDatedListParams(
            request=None, f=self.f, start=self.start, end=self.end, search=self.search
        )


class ResolveCaseIn(APIModel):
    comment: str
    category: ResolutionCategoryCommsEnum
    customCategory: Optional[str] = None
    storRequired: Optional[bool] = False
    withBreach: Optional[bool] = False

    @validator("customCategory")
    def _validate_custom_category(cls, value, values):
        if values["category"] == ResolutionCategoryCommsEnum.OTHER and not value:
            raise ValueError("When category is OTHER, a custom category must be provided.")
        return value

    def to_case_update(
        self, timestamp: dt.datetime, user_id: str, user_name: str, legal_hold: LegalHold
    ) -> Dict:
        if legal_hold and legal_hold.is_applied:
            legal_hold.is_released = True
            legal_hold.is_applied = False
            legal_hold.released = Released(
                category=LegalHoldReleaseCategory.CASE_CLOSURE,
                reason="Case Closure",
                user_id=user_id,
                user_name=user_name,
                timestamp=timestamp,
            )

        return {
            "closed": timestamp,
            "closedBy": user_name,
            "closedComment": self.comment,
            "closedResolutionCategory": self.category,
            "closedResolutionCategoryCustom": self.customCategory,
            "closedSTORRequired": self.storRequired,
            "closedWithBreach": self.withBreach,
            "status": CaseStatus.CLOSED,
            "legal_hold": legal_hold,
        }


class CaseBulkRecordIn(pydantic.BaseModel):
    filter: Optional[CaseBulkFilter]
    records: List[RecordInIdOrKey]
    use_filter: bool = Field(alias="useFilter")
    type: Optional[BulkImportType]


class AssignCaseIn(APIModel):
    description: Optional[str]
    reason: Optional[AssignReason]
    reason_custom: Optional[str] = Field(alias="reasonCustom", default=None)
    user_id: str = Field(alias="userId")

    @validator("reason_custom", always=True)
    def _validate_custom_reason(cls, v, values):
        if values["reason"] == AssignReason.OTHER and not v:
            raise ValueError("When reason is OTHER, a custom reason must be provided.")
        return v


class CaseBulkTaskStatus(APIModel):
    id: UUID
    status: Optional[TaskStatus] = Field(default=TaskStatus.QUEUED)
    detail: Optional[str] = Field(alias="message", default=None)
    is_conductor: Optional[bool] = Field(alias="isConductor", default=False)
    createdBy: Optional[str] = None
