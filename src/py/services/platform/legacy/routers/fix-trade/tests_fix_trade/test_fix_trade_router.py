import botocore.exceptions
import httpx
from aries_se_api_client.base import ParsedR<PERSON>ponse
from aries_utils.lifecycle_handler import <PERSON><PERSON><PERSON>and<PERSON>
from fix_trade.fix_trade_router import FixTradeRouterService
from unittest.mock import ANY, DEFAULT, MagicMock, PropertyMock, patch


@patch.multiple(
    "fix_trade.fix_trade_router",
    SERVICE_CONFIG=DEFAULT,
    StackAPI=DEFAULT,
    AriesApiClient=DEFAULT,
    get_sqs_client=DEFAULT,
    get_batch_client=DEFAULT,
)
@patch.object(LifecycleHandler, "running", new_callable=PropertyMock)
class TestFixTradeRouter:
    @patch("time.sleep")
    def test_service_run_no_fix_queue_names(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        # Stack level pause is enabled, service should sleep for 900 seconds
        # before it requests for the queues again.
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["SERVICE_CONFIG"].queues_refresh_interval_s = "900"
        kwargs["StackAPI"].return_value.get_by_name.return_value = ParsedResponse(
            content=dict(paused=True, tenants=[dict(paused=True, name="foo")]),
            raw_response=httpx.Response(status_code=200),
        )
        time_mock.return_value = None
        lifecyle_mock.side_effect = [True, False]

        FixTradeRouterService().run()

        time_mock.assert_called_once_with(900)
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )

    @patch("time.sleep")
    def test_service_run_no_queue_url(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        # No queue found with the name `foo` and since it's only one name
        # to search the queues with, the service should sleep
        kwargs["SERVICE_CONFIG"].env = "uat"
        kwargs["SERVICE_CONFIG"].stack = "uat-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = ParsedResponse(
            content=dict(paused=False, tenants=[dict(paused=False, name="foo")]),
            raw_response=httpx.Response(status_code=200),
        )
        time_mock.return_value = None
        kwargs["get_sqs_client"].return_value.get_queue_url.side_effect = [
            botocore.exceptions.ClientError(
                error_response={"Error": {"Code": "AWS.SimpleQueueService.NonExistentQueue"}},
                operation_name="",
            ),
        ]
        lifecyle_mock.side_effect = [True, True, False]

        FixTradeRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="uat-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="foo_uat_steeleye_co_fix"
        )

    @patch("time.sleep")
    def test_service_run_no_message_in_queue(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "sit"
        kwargs["SERVICE_CONFIG"].stack = "sit-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = ParsedResponse(
            content=dict(paused=False, tenants=[dict(paused=False, name="foo")]),
            raw_response=httpx.Response(status_code=200),
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://foo_sit_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.return_value = dict(
            Attributes=dict(ApproximateNumberOfMessages="0")
        )
        lifecyle_mock.side_effect = [True, True, False]

        FixTradeRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="sit-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="foo_sit_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.assert_called_once_with(
            QueueUrl="https://foo_sit_steeleye_co_fix",
            AttributeNames=["ApproximateNumberOfMessages"],
        )

    @patch("time.sleep")
    def test_service_run_message_in_queue_but_no_running_batch_job(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "dev"
        kwargs["SERVICE_CONFIG"].stack = "dev-stack"
        kwargs["SERVICE_CONFIG"].batch.job_queue = "batch-queue"
        kwargs["SERVICE_CONFIG"].batch.job_definition = "batch-def"
        kwargs["StackAPI"].return_value.get_by_name.return_value = ParsedResponse(
            content=dict(paused=False, tenants=[dict(paused=False, name="foo")]),
            raw_response=httpx.Response(status_code=200),
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://foo_dev_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.return_value = dict(
            Attributes=dict(ApproximateNumberOfMessages="100")
        )
        kwargs["get_batch_client"].return_value.list_jobs.return_value = {}
        lifecyle_mock.side_effect = [True, True, False, False]

        FixTradeRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="dev-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="foo_dev_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.assert_called_once_with(
            QueueUrl="https://foo_dev_steeleye_co_fix",
            AttributeNames=["ApproximateNumberOfMessages"],
        )
        kwargs["get_batch_client"].return_value.list_jobs.assert_any_call(
            jobQueue="batch-queue", jobStatus="SUBMITTED"
        )
        # Submit job should be called since there's no batch job running
        kwargs["get_batch_client"].return_value.submit_job.assert_any_call(
            jobName="trade_sink-foo_dev_steeleye_co_fix",
            jobQueue="batch-queue",
            jobDefinition="batch-def",
            containerOverrides=ANY,
        )

    @patch("time.sleep")
    def test_service_run_message_in_queue_but_running_batch_job(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "prod"
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["SERVICE_CONFIG"].batch.job_queue = "batch-queue"
        kwargs["SERVICE_CONFIG"].batch.job_definition = "batch-def"
        kwargs["StackAPI"].return_value.get_by_name.return_value = ParsedResponse(
            content=dict(paused=False, tenants=[dict(paused=False, name="foo")]),
            raw_response=httpx.Response(status_code=200),
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://foo_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.return_value = dict(
            Attributes=dict(ApproximateNumberOfMessages="100")
        )
        kwargs["get_batch_client"].return_value.list_jobs.return_value = {
            "jobSummaryList": [{"jobName": "trade_sink-foo_steeleye_co_fix"}]
        }
        lifecyle_mock.side_effect = [True, True, False, False]

        FixTradeRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="foo_steeleye_co_fix"
        )
        kwargs["get_sqs_client"].return_value.get_queue_attributes.assert_called_once_with(
            QueueUrl="https://foo_steeleye_co_fix",
            AttributeNames=["ApproximateNumberOfMessages"],
        )
        kwargs["get_batch_client"].return_value.list_jobs.assert_any_call(
            jobQueue="batch-queue", jobStatus="SUBMITTED"
        )
        # Submit job must not be called when there's one job running already
        kwargs["get_batch_client"].return_value.submit_job.assert_not_called()
