{"mappings": {"dynamic": "strict", "properties": {"&ancestor": {"type": "keyword"}, "&cascadeId": {"type": "keyword"}, "&expiry": {"type": "date"}, "&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&invocationId": {"type": "keyword"}, "&key": {"type": "keyword"}, "&link": {"type": "keyword"}, "&model": {"type": "keyword"}, "&parent": {"type": "keyword"}, "&realm": {"type": "keyword"}, "&status": {"type": "keyword"}, "&taskId": {"type": "keyword"}, "&timestamp": {"type": "date"}, "&traitFqn": {"type": "keyword"}, "&uniqueProps": {"type": "keyword"}, "&updater": {"type": "keyword"}, "&user": {"type": "keyword"}, "&validationErrors": {"dynamic": true, "properties": {"action": {"type": "keyword"}, "category": {"type": "keyword"}, "code": {"type": "keyword"}, "fieldName": {"type": "keyword"}, "fieldPath": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "message": {"type": "keyword"}, "modulesAffected": {"type": "keyword"}, "severity": {"type": "keyword"}, "source": {"type": "keyword"}}, "type": "nested"}, "&version": {"type": "integer"}, "additionalInformation": {"type": "keyword"}, "amount": {"properties": {"amountInChf": {"type": "double"}, "amountInEur": {"type": "double"}, "amountInGbp": {"type": "double"}, "amountInJpy": {"type": "double"}, "amountInUsd": {"type": "double"}, "native": {"type": "double"}, "nativeCurrency": {"type": "keyword"}}}, "dataSourceName": {"type": "keyword"}, "date": {"type": "date"}, "direction": {"type": "keyword"}, "instrumentDetails": {"properties": {"coupon": {"type": "double"}, "grade": {"type": "keyword"}, "instrument": {"properties": {"&content": {"type": "keyword"}, "&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "bond": {"properties": {"debtSeniority": {"type": "keyword"}, "fixedRate": {"type": "double"}, "floatingRateBondIndexBenchmarkBasePointSpread": {"type": "integer"}, "floatingRateBondIndexBenchmarkId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "floatingRateBondIndexBenchmarkName": {"type": "keyword"}, "floatingRateBondIndexBenchmarkTerm": {"type": "keyword"}, "maturityDate": {"type": "date"}, "nominalUnitOrMinTradedValue": {"type": "double"}, "nominalValueCurrency": {"type": "keyword"}, "totalIssuedNominalAmount": {"type": "double"}}}, "cfiAttribute1": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute3": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiAttribute4": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiCategory": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "cfiGroup": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "commoditiesOrEmissionAllowanceDerivativeInd": {"type": "boolean"}, "commodityAndEmissionAllowances": {"properties": {"baseProduct": {"type": "keyword"}, "contractDeliveryCapacity": {"type": "keyword"}, "deliveryDaysOfTheWeek": {"type": "keyword"}, "deliveryDuration": {"type": "keyword"}, "deliveryEnd": {"type": "date"}, "deliveryInterconnectionPoint": {"type": "keyword"}, "deliveryLoadIntervals": {"type": "keyword"}, "deliveryLoadType": {"type": "keyword"}, "deliveryPointorZone": {"type": "keyword"}, "deliveryStart": {"type": "date"}, "finalPriceType": {"type": "keyword"}, "furtherSubProduct": {"type": "keyword"}, "loadDescription": {"type": "keyword"}, "pricePerTimeIntPerQty": {"type": "keyword"}, "quantityUnitOfMeasure": {"type": "keyword"}, "subProduct": {"type": "keyword"}, "transactionType": {"type": "keyword"}}}, "derivative": {"properties": {"creditAttachmentPoint": {"type": "keyword"}, "creditDetachmentPoint": {"type": "keyword"}, "creditDocClause": {"type": "keyword"}, "creditTier": {"type": "keyword"}, "dayCount": {"type": "keyword"}, "deliveryType": {"type": "keyword"}, "expiryDate": {"type": "date"}, "isUserDefinedSpread": {"type": "boolean"}, "legs": {"properties": {"legOptionDelta": {"type": "double"}, "legPrice": {"type": "double"}, "legRatioQty": {"type": "double"}, "legSecurityId": {"type": "keyword"}, "legSide": {"type": "keyword"}}}, "optionExerciseStyle": {"type": "keyword"}, "optionType": {"type": "keyword"}, "priceDisplayFactor": {"type": "double"}, "priceMultiplier": {"type": "double"}, "strikePrice": {"type": "double"}, "strikePriceCurrency": {"type": "keyword"}, "strikePricePending": {"type": "boolean"}, "underlyingIndexFactor": {"type": "integer"}, "underlyingIndexName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingIndexSeries": {"type": "keyword"}, "underlyingIndexTerm": {"type": "keyword"}, "underlyingIndexTermValue": {"type": "keyword"}, "underlyingIndexVersion": {"type": "keyword"}, "underlyingInstrumentIdCodeFarLeg": {"type": "keyword"}, "underlyingInstrumentIdCodeNearLeg": {"type": "keyword"}, "underlyingInstruments": {"properties": {"underlyingInstrumentClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingInstrumentCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingIssuer": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}}}, "ext": {"properties": {"additionalIdentifiers": {"dynamic": "true", "type": "object"}, "additionalNames": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "aii": {"properties": {"daily": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "dailyWithoutStrike": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "exchangeProductCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingSymbolExpiryCode": {"type": "keyword"}, "withoutStrike": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "alternativeInstrumentIdentifier": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "bestExAssetClassMain": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "bestExAssetClassSub": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "cmeMifirEligible": {"type": "boolean"}, "compositeFigi": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "emirEligible": {"type": "boolean"}, "exchangeSymbol": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "exchangeSymbolBbg": {"type": "keyword"}, "exchangeSymbolESignal": {"type": "keyword"}, "exchangeSymbolLocal": {"type": "keyword"}, "exchangeSymbolRoot": {"type": "keyword"}, "figi": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "instrumentIdCodeType": {"type": "keyword"}, "instrumentUniqueIdentifier": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "issuerName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "lei": {"properties": {"&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "entity": {"properties": {"associatedEntity": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "businessRegisterEntityID": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "entityExpirationDate": {"type": "date"}, "entityExpirationReason": {"type": "keyword"}, "entityStatus": {"type": "keyword"}, "headquartersAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "line1": {"type": "keyword"}, "line2": {"type": "keyword"}, "line3": {"type": "keyword"}, "line4": {"type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "region": {"type": "keyword"}}}, "legalAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "line1": {"type": "keyword"}, "line2": {"type": "keyword"}, "line3": {"type": "keyword"}, "line4": {"type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "region": {"type": "keyword"}}}, "legalForm": {"type": "keyword"}, "legalJurisdiction": {"type": "keyword"}, "legalName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "otherAddress": {"properties": {"city": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "country": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "line1": {"type": "keyword"}, "line2": {"type": "keyword"}, "line3": {"type": "keyword"}, "line4": {"type": "keyword"}, "postalCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "region": {"type": "keyword"}}}, "otherEntityName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "successorEntity": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "lei": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "registration": {"properties": {"initialRegistrationDate": {"type": "date"}, "lastUpdateDate": {"type": "date"}, "managingLOU": {"type": "keyword"}, "nextRenewalDate": {"type": "date"}, "registrationStatus": {"type": "keyword"}, "validationSources": {"type": "keyword"}}}}}, "localCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mifirEligible": {"type": "boolean"}, "notionalCurrency2Type": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "onFIRDS": {"type": "boolean"}, "otcFlag": {"type": "boolean"}, "priceNotation": {"type": "keyword"}, "pricingReferences": {"dynamic": "true", "type": "object"}, "quantityNotation": {"type": "keyword"}, "spot": {"type": "boolean"}, "strikePriceType": {"type": "keyword"}, "toTv": {"type": "boolean"}, "tradedIn": {"dynamic": "true", "type": "object"}, "tradingLocation": {"type": "keyword"}, "tradingVenueCountry": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "tradingVenueCountryCode": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "uToTv": {"type": "boolean"}, "underlyingIndexClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingIndexId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingIndexTermId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingInstruments": {"dynamic": "true", "type": "object"}, "underlyingOnFIRDS": {"type": "boolean"}, "underlyingOtcFlag": {"type": "boolean"}, "underlyingTradingVenueCountry": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}}, "type": "keyword"}, "underlyingTradingVenueCountryCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "underlyingVenueInEEA": {"type": "boolean"}, "underlyingVenueOutsideEEA": {"type": "boolean"}, "venueIn": {"dynamic": "true", "type": "object"}, "venueInEEA": {"type": "boolean"}, "venueName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "venueOutsideEEA": {"type": "boolean"}, "venueType": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "fxDerivatives": {"properties": {"fxType": {"type": "keyword"}, "notionalCurrency2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "instrumentClassification": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentClassificationEMIRAssetClass": {"type": "keyword"}, "instrumentClassificationEMIRContractType": {"type": "keyword"}, "instrumentClassificationEMIRProductType": {"type": "keyword"}, "instrumentFullName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "instrumentIdCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "interestRateDerivatives": {"properties": {"irContractTerm": {"type": "keyword"}, "leg1FixedRate": {"type": "double"}, "leg1FixedRateDayCount": {"type": "keyword"}, "leg1FloatingReferenceRate": {"type": "keyword"}, "leg1FloatingReferenceRateTerm": {"type": "keyword"}, "leg1FloatingReferenceRateTermValue": {"type": "integer"}, "leg1IndexSource": {"type": "keyword"}, "leg1PaymentFrequencyTerm": {"type": "keyword"}, "leg1PaymentFrequencyTermValue": {"type": "integer"}, "leg1ReferenceRate": {"type": "keyword"}, "leg1ReferenceRateISO": {"type": "keyword"}, "leg1ResetFrequencyTerm": {"type": "keyword"}, "leg1ResetFrequencyTermValue": {"type": "integer"}, "leg2FixedRate": {"type": "double"}, "leg2FixedRateDayCount": {"type": "keyword"}, "leg2FloatingRate": {"type": "keyword"}, "leg2FloatingReferenceRateTerm": {"type": "keyword"}, "leg2FloatingReferenceRateTermValue": {"type": "integer"}, "leg2IRContractTerm": {"type": "keyword"}, "leg2IndexSource": {"type": "keyword"}, "leg2NotionalCurrency2": {"type": "keyword"}, "leg2PaymentFrequencyTerm": {"type": "keyword"}, "leg2PaymentFrequencyTermValue": {"type": "integer"}, "leg2ReferenceRate": {"type": "keyword"}, "leg2ReferenceRateISO": {"type": "keyword"}, "leg2ResetFrequencyTerm": {"type": "keyword"}, "leg2ResetFrequencyTermValue": {"type": "integer"}, "notionalCurrency2": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "referenceRate": {"type": "keyword"}, "startDate": {"type": "date"}}}, "isCreatedThroughFallback": {"type": "boolean"}, "issuerOrOperatorOfTradingVenueId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "notionalCurrency1": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "paymentFrequency": {"type": "keyword"}, "sourceIndex": {"type": "keyword"}, "sourceKey": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "systematicInternaliserCalculations": {"properties": {"from": {"type": "date"}, "sourceKey": {"type": "keyword"}, "to": {"type": "date"}, "turnoverExecutedInEurope": {"type": "double"}, "turnoverExecutedInEuropeEUR": {"type": "double"}}}, "transparencyCalculations": {"properties": {"averageDailyTransactionValue": {"type": "double"}, "averageDailyTransactionValueCurrency": {"type": "keyword"}, "averageDailyTurnover": {"type": "double"}, "averageDailyTurnoverCurrency": {"type": "keyword"}, "averageNumberOfTransactions": {"type": "double"}, "averageVolumeOfTransactions": {"type": "double"}, "from": {"type": "date"}, "liquid": {"type": "keyword"}, "liquidBand": {"type": "keyword"}, "methodology": {"type": "keyword"}, "mostRelevantMIC": {"type": "keyword"}, "mostRelevantMICAverageNumberOfDailyTransactions": {"type": "double"}, "post": {"properties": {"lisThresholdAmount": {"type": "double"}, "lisThresholdCurrency": {"type": "keyword"}, "sstiThresholdAmount": {"type": "double"}, "sstiThresholdCurrency": {"type": "keyword"}}}, "pre": {"properties": {"lisThresholdAmount": {"type": "double"}, "lisThresholdCurrency": {"type": "keyword"}, "sstiThresholdAmount": {"type": "double"}, "sstiThresholdCurrency": {"type": "keyword"}}}, "sourceKey": {"type": "keyword"}, "to": {"type": "date"}}}, "venue": {"properties": {"admissionToTradingApprovalDate": {"type": "date"}, "admissionToTradingOrFirstTradeDate": {"type": "date"}, "admissionToTradingRequestDate": {"type": "date"}, "financialInstrumentShortName": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "issuerRequestForAdmissionToTrading": {"type": "boolean"}, "terminationDate": {"type": "date"}, "tradingVenue": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "venueDetails": {"properties": {"city": {"type": "keyword"}, "country": {"type": "keyword"}, "countryCode": {"type": "keyword"}, "created": {"type": "date"}, "inEEA": {"type": "boolean"}, "lei": {"type": "keyword"}, "name": {"type": "keyword"}, "nca": {"type": "keyword"}, "operatingMic": {"type": "keyword"}, "type": {"type": "keyword"}}}}}, "rating": {"type": "keyword"}, "risk": {"type": "keyword"}}}, "level": {"type": "keyword"}, "marketIdentifiers": {"properties": {"labelId": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "path": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "type": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "parties": {"properties": {"fileIdentifier": {"type": "keyword"}, "type": {"type": "keyword"}, "value": {"properties": {"&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "client": {"properties": {"accountCurrency": {"type": "keyword"}, "assetsCategory": {"type": "keyword"}, "contactPreferences": {"type": "keyword"}, "dateAccountClosed": {"type": "date"}, "dateAccountOpened": {"type": "date"}, "isAggregatedClientAccount": {"type": "boolean"}, "lastRiskAssessment": {"type": "date"}, "metFaceToFace": {"type": "boolean"}, "riskScore": {"type": "keyword"}, "status": {"type": "keyword"}}}, "communications": {"properties": {"emails": {"fields": {"rfc5322": {"analyzer": "rfc5322", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "imAccounts": {"properties": {"id": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"type": "keyword"}}, "type": "nested"}, "phoneNumbers": {"properties": {"dialingCode": {"type": "keyword"}, "extension": {"fields": {"num": {"analyzer": "numonly", "type": "text"}}, "type": "keyword"}, "isValid": {"type": "boolean"}, "label": {"type": "keyword"}, "number": {"fields": {"num": {"analyzer": "numonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "counterparty": {"properties": {"&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "client": {"properties": {"accountCurrency": {"type": "keyword"}, "assetsCategory": {"type": "keyword"}, "contactPreferences": {"type": "keyword"}, "dateAccountClosed": {"type": "date"}, "dateAccountOpened": {"type": "date"}, "isAggregatedClientAccount": {"type": "boolean"}, "lastRiskAssessment": {"type": "date"}, "metFaceToFace": {"type": "boolean"}, "riskScore": {"type": "keyword"}, "status": {"type": "keyword"}}}, "details": {"properties": {"clientMandate": {"type": "keyword"}, "decisionMaker": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "executionWithinFirm": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "firmNCA": {"type": "keyword"}, "firmSize": {"type": "keyword"}, "firmStatus": {"type": "keyword"}, "firmType": {"type": "keyword"}, "inEEA": {"type": "boolean"}, "industry": {"type": "keyword"}, "isEmirDelegatedReporting": {"type": "boolean"}, "legalType": {"type": "keyword"}, "leiRegistrationStatus": {"type": "keyword"}, "mifidEnrollmentDate": {"type": "date"}, "mifidRegistered": {"type": "boolean"}, "orgType": {"type": "keyword"}, "parentOfCollectiveInvestmentSchema": {"type": "boolean"}, "positionHolderInFirm": {"type": "keyword"}, "retailOrProfessional": {"type": "keyword"}, "smcrFirmType": {"type": "keyword"}, "tradableInstruments": {"type": "keyword"}}}, "emirDetails": {"properties": {"corporateSector": {"type": "keyword"}, "delegatedReporting": {"type": "keyword"}, "isClearingThreshold": {"type": "boolean"}, "natureOfFirm": {"type": "keyword"}}}, "fileIdentifier": {"type": "keyword"}, "firmCommunications": {"properties": {"chatDomains": {"type": "keyword"}, "domainNames": {"type": "keyword"}, "emails": {"fields": {"rfc5322": {"analyzer": "rfc5322", "type": "text"}}, "type": "keyword"}, "phoneNumberPrefixes": {"type": "keyword"}, "phoneNumbers": {"properties": {"dialingCode": {"type": "keyword"}, "extension": {"fields": {"num": {"analyzer": "numonly", "type": "text"}}, "type": "keyword"}, "isValid": {"type": "boolean"}, "label": {"type": "keyword"}, "number": {"fields": {"num": {"analyzer": "numonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "firmIdentifiers": {"properties": {"bic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "bloombergCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "branchCountry": {"type": "keyword"}, "deaAccess": {"type": "boolean"}, "ecbCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "internalRefNo": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "isIsda": {"type": "boolean"}, "kycApproved": {"type": "boolean"}, "lei": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "nationalBusinessIdentityNo": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "firmLocation": {"properties": {"registeredAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}, "tradingAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}}}, "isCreatedThroughPartyFallback": {"type": "boolean"}, "name": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "sinkIdentifiers": {"properties": {"orderFileIdentifiers": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}, "tradeFileIdentifiers": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "sourceIndex": {"type": "keyword"}, "sourceKey": {"type": "keyword"}, "uniqueIds": {"type": "keyword"}}}, "details": {"properties": {"clientMandate": {"type": "keyword"}, "decisionMaker": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "executionWithinFirm": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "firmNCA": {"type": "keyword"}, "firmSize": {"type": "keyword"}, "firmStatus": {"type": "keyword"}, "firmType": {"type": "keyword"}, "inEEA": {"type": "boolean"}, "industry": {"type": "keyword"}, "isEmirDelegatedReporting": {"type": "boolean"}, "legalType": {"type": "keyword"}, "leiRegistrationStatus": {"type": "keyword"}, "mifidEnrollmentDate": {"type": "date"}, "mifidRegistered": {"type": "boolean"}, "orgType": {"type": "keyword"}, "parentOfCollectiveInvestmentSchema": {"type": "boolean"}, "positionHolderInFirm": {"type": "keyword"}, "retailOrProfessional": {"type": "keyword"}, "smcrFirmType": {"type": "keyword"}, "tradableInstruments": {"type": "keyword"}}}, "emirDetails": {"properties": {"corporateSector": {"type": "keyword"}, "delegatedReporting": {"type": "keyword"}, "isClearingThreshold": {"type": "boolean"}, "natureOfFirm": {"type": "keyword"}}}, "employeeStatus": {"type": "keyword"}, "fileIdentifier": {"type": "keyword"}, "firmCommunications": {"properties": {"chatDomains": {"type": "keyword"}, "domainNames": {"type": "keyword"}, "emails": {"fields": {"rfc5322": {"analyzer": "rfc5322", "type": "text"}}, "type": "keyword"}, "phoneNumberPrefixes": {"type": "keyword"}, "phoneNumbers": {"properties": {"dialingCode": {"type": "keyword"}, "extension": {"fields": {"num": {"analyzer": "numonly", "type": "text"}}, "type": "keyword"}, "isValid": {"type": "boolean"}, "label": {"type": "keyword"}, "number": {"fields": {"num": {"analyzer": "numonly", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "firmIdentifiers": {"properties": {"bic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "bloombergCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "branchCountry": {"type": "keyword"}, "deaAccess": {"type": "boolean"}, "ecbCode": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "internalRefNo": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "isIsda": {"type": "boolean"}, "kycApproved": {"type": "boolean"}, "lei": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mic": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "nationalBusinessIdentityNo": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}}}, "firmLocation": {"properties": {"registeredAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}, "tradingAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}}}, "isCreatedThroughPartyFallback": {"type": "boolean"}, "location": {"properties": {"homeAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}, "officeAddress": {"properties": {"address": {"type": "keyword"}, "city": {"type": "keyword"}, "country": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "countryCode": {"type": "keyword"}, "postalCode": {"type": "keyword"}, "state": {"type": "keyword"}, "street": {"type": "keyword"}}}}}, "name": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "officialIdentifiers": {"properties": {"branchCountry": {"type": "keyword"}, "clientMandate": {"type": "keyword"}, "concatId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "employeeId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "fcaNo": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mifirId": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "mifirIdSubType": {"type": "keyword"}, "mifirIdType": {"type": "keyword"}, "nationalIds": {"properties": {"id": {"fields": {"alphanum": {"analyzer": "alphanum", "type": "text"}}, "type": "keyword"}, "label": {"type": "keyword"}}, "type": "nested"}, "passports": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}, "traderIds": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "personalDetails": {"properties": {"dob": {"type": "date"}, "firstName": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "lastName": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "middleName": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "nationality": {"type": "keyword"}}}, "retailOrProfessional": {"type": "keyword"}, "sinkIdentifiers": {"properties": {"orderFileIdentifiers": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}, "tradeFileIdentifiers": {"properties": {"id": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "label": {"fields": {"alpha": {"analyzer": "alphaonly", "type": "text"}, "alphanum": {"analyzer": "alphanum", "type": "text"}, "search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}, "type": "nested"}}}, "sourceIndex": {"type": "keyword"}, "sourceKey": {"type": "keyword"}, "structure": {"properties": {"client": {"properties": {"accountCurrency": {"type": "keyword"}, "annualIncome": {"type": "keyword"}, "assetsCategory": {"type": "keyword"}, "contactPreferences": {"type": "keyword"}, "dateAccountClosed": {"type": "date"}, "dateAccountOpened": {"type": "date"}, "electiveProfessional": {"type": "boolean"}, "employmentStatus": {"type": "keyword"}, "isAggregatedClientAccount": {"type": "boolean"}, "lastRiskAssessment": {"type": "date"}, "metFaceToFace": {"type": "boolean"}, "riskScore": {"type": "keyword"}, "savingsAndInvestment": {"type": "keyword"}, "status": {"type": "keyword"}}}, "decisionMaker": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "decisionMakerProducts": {"type": "keyword"}, "department": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "desks": {"properties": {"id": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "name": {"type": "keyword"}}, "type": "nested"}, "executionWithinFirm": {"properties": {"branchCountry": {"type": "keyword"}, "dob": {"type": "date"}, "firstName": {"type": "keyword"}, "id": {"type": "keyword"}, "idSubType": {"type": "keyword"}, "idType": {"type": "keyword"}, "role": {"type": "keyword"}, "roleQualifier": {"type": "integer"}, "source": {"type": "keyword"}, "surname": {"type": "keyword"}, "uniqueId": {"type": "keyword"}}}, "instruments": {"properties": {"ableToTradeBestExAssetClass": {"type": "keyword"}, "ableToTradeInstrumentId": {"type": "keyword"}}}, "isDecisionMaker": {"type": "boolean"}, "isPersonalTradingAccount": {"type": "boolean"}, "role": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "smcr": {"properties": {"functionCategory": {"type": "keyword"}, "functions": {"properties": {"function": {"type": "keyword"}, "functionType": {"type": "keyword"}}, "type": "nested"}, "reviewDate": {"type": "date"}}}, "type": {"type": "keyword"}}}, "uniqueIds": {"type": "keyword"}}}}}, "pnlAmount": {"properties": {"amountInChf": {"type": "double"}, "amountInEur": {"type": "double"}, "amountInGbp": {"type": "double"}, "amountInJpy": {"type": "double"}, "amountInUsd": {"type": "double"}, "native": {"type": "double"}, "nativeCurrency": {"type": "keyword"}}}, "quantity": {"type": "double"}, "quantityNotation": {"type": "keyword"}, "sourceIndex": {"type": "keyword"}, "sourceKey": {"type": "keyword"}}}, "settings": {"gc_deletes": 0, "mapping.ignore_malformed": true, "mapping.nested_fields.limit": 250, "mapping.total_fields.limit": 10000, "max_inner_result_window": 1000, "number_of_replicas": 0, "number_of_shards": 1, "refresh_interval": -1, "analysis": {"analyzer": {"all": {"filter": ["lowercase", "asciifolding"], "tokenizer": "whitespace", "type": "custom"}, "alphanum": {"char_filter": ["alphanum_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "alphaonly": {"char_filter": ["alpha_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "content_en": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph"], "tokenizer": "whitespace", "type": "custom"}, "content_en_stemmed": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph", "english_stop", "english_stemmer"], "tokenizer": "whitespace", "type": "custom"}, "default": {"type": "standard"}, "norm": {"filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "numonly": {"char_filter": ["digit_only_filter", "zero_remove_filter"], "tokenizer": "keyword", "type": "custom"}, "rfc5322": {"char_filter": ["rfc5322_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}}, "char_filter": {"alpha_only_filter": {"pattern": "[^\\pL]", "replacement": "", "type": "pattern_replace"}, "alphanum_only_filter": {"pattern": "[^(\\d|\\pL)]", "replacement": "", "type": "pattern_replace"}, "digit_only_filter": {"pattern": "[^\\d]", "replacement": "", "type": "pattern_replace"}, "rfc5322_only_filter": {"pattern": "[^(\\d\\pL\\Q!#$%&*+-/=?^_{|}~@.\\E)]", "replacement": "", "type": "pattern_replace"}, "zero_remove_filter": {"pattern": "\\b0+", "replacement": "", "type": "pattern_replace"}}, "filter": {"autocomplete_filter": {"max_gram": "20", "min_gram": "1", "type": "edge_ngram"}, "email": {"patterns": ["([^@]+)", "(\\p{L}+)", "(\\d+)", "@(.+)"], "preserve_original": true, "type": "pattern_capture"}, "english_stemmer": {"language": "english", "type": "stemmer"}, "english_stop": {"stopwords": "_english_", "type": "stop"}, "word_splitter": {"catenate_all": true, "generate_number_parts": false, "preserve_original": true, "split_on_numerics": false, "type": "word_delimiter_graph"}}}}}