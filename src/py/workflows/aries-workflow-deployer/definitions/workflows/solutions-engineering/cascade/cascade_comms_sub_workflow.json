{"name": "cascade_comms_sub_workflow", "outputParameters": {"sw_output": "${cascade_update_comms_join.output}"}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"process_comms": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "cascade_process_comms", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "cascade_process_comms", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"dynamicTasks": "${cascade_process_comms.output.io_param.params.dynamicTasks}", "dynamicTasksInput": "${cascade_process_comms.output.io_param.params.dynamicTaskInputs}"}, "loopCondition": null, "name": "cascade_update_comms_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "cascade_update_comms_fork", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "cascade_update_comms_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "cascade_update_comms_join", "type": "JOIN"}]}, "defaultCase": [], "description": "Decide whether to process comms or skip", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () {\n  if ($.is_eligible != true) {\n    return 'default';\n  } else {\n    return 'process_comms';\n  }\n})();\n", "inputParameters": {"is_eligible": "${workflow.input.io_param.params.eligible_modules.comms}"}, "loopCondition": null, "name": "process_comms_if_eligible", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "process_comms_if_eligible", "type": "SWITCH"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "workflowStatusListenerEnabled": false}