{"inputTemplate": {"workflow": {"name": "elastic_index_restorer"}}, "name": "elastic_index_restorer", "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "elastic_index_restorer", "optional": false, "startDelay": 0, "taskReferenceName": "elastic_index_restorer", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}