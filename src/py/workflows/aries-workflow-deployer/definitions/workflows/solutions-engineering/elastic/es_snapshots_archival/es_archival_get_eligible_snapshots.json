{"failureWorkflow": "aries_workflow_failure_oma_event", "name": "es_archival_get_eligible_snapshots", "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "archival_get_eligible_snapshots", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "archival_get_eligible_snapshots", "type": "SIMPLE"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "workflowStatusListenerEnabled": false}