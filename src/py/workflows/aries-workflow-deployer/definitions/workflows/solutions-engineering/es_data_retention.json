{"failureWorkflow": "aries_workflow_failure_oma_event", "inputTemplate": {"workflow": {"name": "es_data_retention"}}, "name": "es_data_retention", "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "apply_es_data_retention", "optional": false, "startDelay": 0, "taskReferenceName": "apply_es_data_retention", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}