{"accessPolicy": {}, "inputParameters": [], "inputTemplate": {"workflow": {"name": "o2_sms_subworkflow"}}, "name": "o2_sms_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "o2_sms_message_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "o2_sms_message_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"analytics": [{"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "inputParameters": {"io_param": "${o2_sms_message_transform.output.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "language_detection", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "language_detection", "type": "SIMPLE"}], [{"asyncComplete": false, "inputParameters": {"io_param": "${o2_sms_message_transform.output.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "lexica_processor", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lexica_processor", "type": "SIMPLE"}]], "inputParameters": {}, "name": "comms_analytics_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["language_detection", "lexica_processor"], "name": "comms_analytics_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"language_detection": "${comms_analytics_join.output.language_detection.io_param.params}", "lexica_processor": "${comms_analytics_join.output.lexica_processor.io_param.params}"}}, "workflow": "${workflow.input.workflow}"}, "name": "merge_comms_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "merge_comms_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${merge_comms_analytics.output.io_param}", "task": "${merge_comms_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_message_with_lexica", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_message_with_lexica", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_message_with_lexica", "type": "WAIT"}], "non-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${o2_sms_message_transform.output.io_param}", "task": "${o2_sms_message_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_message_non_lexica", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_message_non_lexica", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_message_non_lexica", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_message", "type": "WAIT"}], "description": "Decide whether to apply Comms Analytics", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri != null) {\n    return $.is_lexica_enabled ? \"analytics\" : \"non-analytics\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();", "inputParameters": {"file_uri": "${o2_sms_message_transform.output.io_param.params.file_uri}", "is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "name": "switch_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_analytics", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}