{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "ms_teams_chat"}}, "name": "ms_teams_chat", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "ms_teams_chat_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "ms_teams_chat_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"analytics": [{"asyncComplete": false, "inputParameters": {"ignore_attachments_size": "${workflow.input.io_param.params.ignore_attachments_size}", "io_param": "${ms_teams_chat_transform.output.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "elastic_ingestion_with_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion_with_analytics"}, "taskReferenceName": "elastic_ingestion_with_analytics_subworkflow_ref", "type": "SUB_WORKFLOW"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${wait_es__apply_meta_with_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_with_analytics", "type": "WAIT"}], "empty_file_uri": [{"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${ms_teams_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_empty_file_uri", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_empty_file_uri", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_empty_file_uri", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${ms_teams_chat_transform.output.io_param}", "task": "${ms_teams_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_without_analytics", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${wait_es__apply_meta_without_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_without_analytics", "type": "WAIT"}], "description": "Decide whether to apply Comms Analytics", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri != null) {\n    return $.is_lexica_enabled ? \"analytics\" : \"non-analytics\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();", "inputParameters": {"file_uri": "${workflow.input.io_param.params.file_uri}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "switch_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_analytics", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}