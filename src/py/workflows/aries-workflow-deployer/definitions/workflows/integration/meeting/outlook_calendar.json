{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "outlook_calendar"}}, "name": "outlook_calendar", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "outlook_calendar_meetings", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "outlook_calendar_meetings", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"with_analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${outlook_calendar_meetings.output.io_param.params.Meeting}", "task": "${outlook_calendar_meetings.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "elastic_ingestion_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion_with_analytics"}, "taskReferenceName": "elastic_ingestion_with_analytics_ref", "type": "SUB_WORKFLOW"}]}, "defaultCase": [{"asyncComplete": false, "description": "Elastic ingestion without analytics", "inputParameters": {"io_param": "${outlook_calendar_meetings.output.io_param.params.Meeting}", "task": "${outlook_calendar_meetings.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "elastic_ingestion", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion"}, "taskReferenceName": "elastic_ingestion_ref", "type": "SUB_WORKFLOW"}], "evaluatorType": "javascript", "expression": "$.is_lexica_enabled ? 'with_analytics' : 'without_analytics'", "inputParameters": {"is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_task", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}