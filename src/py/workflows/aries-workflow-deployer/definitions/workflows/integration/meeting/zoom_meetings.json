{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "zoom_meetings"}}, "name": "zoom_meetings", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "zoom_meetings_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "zoom_meetings_transform", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"with-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Message}", "workflow": "${workflow.input.workflow}"}, "name": "message_elastic_ingestion_with_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion_with_analytics"}, "taskReferenceName": "message_elastic_ingestion_with_analytics_ref", "type": "SUB_WORKFLOW"}], "without-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Message}", "workflow": "${workflow.input.workflow}"}, "name": "message_elastic_ingestion_without_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion"}, "taskReferenceName": "message_elastic_ingestion_without_analytics_ref", "type": "SUB_WORKFLOW"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "no_op_message", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "no_op_message", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "$.inputValue != null ? ($.is_lexica_enabled ? 'with-analytics' : 'without-analytics') : null", "inputParameters": {"inputValue": "${zoom_meetings_transform.output.io_param.params.Message}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_message_subworkflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_ingestion_message", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_message_ref", "type": "JOIN"}], [{"asyncComplete": false, "decisionCases": {"with-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Meeting}", "workflow": "${workflow.input.workflow}"}, "name": "meeting_elastic_ingestion_with_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion_with_analytics"}, "taskReferenceName": "meeting_elastic_ingestion_with_analytics_ref", "type": "SUB_WORKFLOW"}], "without-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Meeting}", "workflow": "${workflow.input.workflow}"}, "name": "meeting_elastic_ingestion_without_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion"}, "taskReferenceName": "meeting_elastic_ingestion_without_analytics_ref", "type": "SUB_WORKFLOW"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "no_op_meeting", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "no_op_meeting", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "$.inputValue != null ? ($.is_lexica_enabled ? 'with-analytics' : 'without-analytics') : null", "inputParameters": {"inputValue": "${zoom_meetings_transform.output.io_param.params.Meeting}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_meeting_subworkflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_ingestion_meeting", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_meeting_ref", "type": "JOIN"}], [{"asyncComplete": false, "decisionCases": {"without-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Transcript}", "workflow": "${workflow.input.workflow}"}, "name": "transcript_elastic_ingestion_without_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion"}, "taskReferenceName": "transcript_elastic_ingestion_without_analytics_ref", "type": "SUB_WORKFLOW"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "no_op_transcript", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "no_op_transcript", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'without-analytics' : null", "inputParameters": {"inputValue": "${zoom_meetings_transform.output.io_param.params.Transcript}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_transcript_subworkflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_ingestion_transcript", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_transcript_ref", "type": "JOIN"}], [{"asyncComplete": false, "decisionCases": {"waveform": [{"asyncComplete": false, "inputParameters": {"io_param": "${zoom_meetings_transform.output.io_param.params.Waveform}", "workflow": "${workflow.input.workflow}"}, "name": "waveform_transform", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "waveform_transform_ref", "type": "SIMPLE"}]}, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'waveform' : null", "inputParameters": {"inputValue": "${zoom_meetings_transform.output.io_param.params.Waveform}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_waveform_should_run", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_waveform_transform_ref", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_waveform_transform_ref", "type": "JOIN"}]], "inputParameters": {}, "name": "zoom_meetings_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "zoom_meetings_fork_ref", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["wait_for_ingestion_message_ref", "wait_for_ingestion_meeting_ref", "wait_for_ingestion_transcript_ref", "wait_for_waveform_transform_ref"], "name": "zoom_meetings_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "zoom_meetings_join_ref", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}