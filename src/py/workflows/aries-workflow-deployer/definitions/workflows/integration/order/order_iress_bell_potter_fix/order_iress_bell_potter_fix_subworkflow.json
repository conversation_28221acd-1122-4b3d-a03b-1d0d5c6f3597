{"accessPolicy": {}, "inputParameters": [], "inputTemplate": {"workflow": {"name": "order_iress_bell_potter_fix_subworkflow"}}, "name": "order_iress_bell_potter_fix_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "order_iress_bellp_fix_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_iress_bellp_fix_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_orders", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_iress_bellp_fix_transform.output.io_param.params.orders}", "task": "${order_iress_bellp_fix_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_orders", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_orders", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${wait_es__apply_meta_orders.output.io_param}", "task": "${wait_es__apply_meta_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "instrument_mapper", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "instrument_mapper_orders", "type": "SIMPLE"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_iress_bellp_fix_transform.output.io_param.params.orders.params.file_uri}"}, "name": "is_input_orders_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_orders_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_orders", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}