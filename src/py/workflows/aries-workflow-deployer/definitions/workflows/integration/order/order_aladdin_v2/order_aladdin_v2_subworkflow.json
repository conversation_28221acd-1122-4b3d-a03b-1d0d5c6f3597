{"inputParameters": [], "inputTemplate": {"workflow": {"name": "order_aladdin_v2_subworkflow"}}, "name": "order_aladdin_v2_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "order_aladdin_v2", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_aladdin_v2", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_synthetic_newo", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_aladdin_v2.output.io_param.params.synthetic_newo}", "task": "${order_aladdin_v2.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_synthetic_newo", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_synthetic_newo", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_synthetic_newo", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${wait_es__apply_meta_synthetic_newo.output.io_param}", "task": "${wait_es__apply_meta_synthetic_newo.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "instrument_mapper", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "instrument_mapper_synthetic_newo", "type": "SIMPLE"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_aladdin_v2.output.io_param.params.synthetic_newo.params.file_uri}"}, "name": "is_input_synthetic_newos_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_synthetic_newos_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_synthetic_newo", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_orders", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_aladdin_v2.output.io_param.params.orders}", "task": "${order_aladdin_v2.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_orders", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_orders", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${wait_es__apply_meta_orders.output.io_param}", "task": "${wait_es__apply_meta_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "instrument_mapper", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "instrument_mapper_orders", "type": "SIMPLE"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_aladdin_v2.output.io_param.params.orders.params.file_uri}"}, "name": "is_input_orders_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_orders_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_orders", "type": "WAIT"}]], "inputParameters": {}, "name": "order_aladdin_v2_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_aladdin_v2_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["lazy_merge_synthetic_newo", "lazy_merge_orders"], "name": "order_aladdin_v2_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_aladdin_v2_join", "type": "JOIN"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}