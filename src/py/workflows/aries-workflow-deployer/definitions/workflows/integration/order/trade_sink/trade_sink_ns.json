{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "trade_sink_ns"}}, "name": "trade_sink_ns", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"file_uri": "${workflow.input.io_param.params.file_uri}", "preserve_directory_structure": true}}, "workflow": "${workflow.input.workflow}"}, "name": "gpg_decryptor", "optional": false, "startDelay": 0, "taskReferenceName": "gpg_decryptor", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${gpg_decryptor.output.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "trade_sink_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "trade_sink_transform", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_orders", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${trade_sink_transform.output.io_param.params.Order}", "task": "${trade_sink_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_orders", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_orders", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${wait_es__apply_meta_orders.output.io_param}", "task": "${wait_es__apply_meta_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "instrument_mapper", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "instrument_mapper_orders", "type": "SIMPLE"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${trade_sink_transform.output.io_param.params.Order.params.file_uri}"}, "name": "is_input_orders_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_orders_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_orders", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_quarantined_orders", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${trade_sink_transform.output.io_param.params.QuarantinedOrder}", "task": "${trade_sink_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_quarantined_orders", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_quarantined_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_quarantined_orders", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${trade_sink_transform.output.io_param.params.QuarantinedOrder.params.file_uri}"}, "name": "is_input_quarantined_orders_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_quarantined_orders_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_quarantined_orders", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_sink_record_audit", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${trade_sink_transform.output.io_param.params.SinkRecordAudit}", "task": "${trade_sink_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_sink_record_audit", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_sink_record_audit", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_sink_record_audit", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${trade_sink_transform.output.io_param.params.SinkRecordAudit.params.file_uri}"}, "name": "is_input_sink_record_audit_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_sink_record_audit_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_sink_record_audit", "type": "WAIT"}]], "inputParameters": {}, "name": "trade_sink_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "trade_sink_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["lazy_merge_orders", "lazy_merge_quarantined_orders", "lazy_merge_sink_record_audit"], "name": "trade_sink_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "trade_sink_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}