{"inputParameters": [], "inputTemplate": {"workflow": {"name": "tr_bbg_emsi_orders_subworkflow"}}, "name": "tr_bbg_emsi_orders_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "tr_bbg_emsi_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "tr_bbg_emsi_orders", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_rts22_transactions", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${tr_bbg_emsi_orders.output.io_param.params.RTS22Transaction}", "task": "${tr_bbg_emsi_rts22_transactions.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_rts22_transactions", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_rts22_transactions", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_rts22_transactions", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${tr_bbg_emsi_orders.output.io_param.params.RTS22Transaction.params.file_uri}"}, "name": "is_input_rts22_transactions_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_rts22_transactions_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_rts22_transactions", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_quarantined_rts22_transactions", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${tr_bbg_emsi_orders.output.io_param.params.QuarantinedRTS22Transaction}", "task": "${tr_bbg_emsi_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_quarantined_rts22_transactions", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_quarantined_rts22_transactions", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_quarantined_rts22_transactions", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${tr_bbg_emsi_orders.output.io_param.params.QuarantinedRTS22Transaction.params.file_uri}"}, "name": "is_input_quarantined_rts22_transactions_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_quarantined_rts22_transactions_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_quarantined_rts22_transactions", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_sink_record_audit", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${tr_bbg_emsi_orders.output.io_param.params.SinkRecordAudit}", "task": "${tr_bbg_emsi_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_sink_record_audit", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_sink_record_audit", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_sink_record_audit", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${tr_bbg_emsi_orders.output.io_param.params.SinkRecordAudit.params.file_uri}"}, "name": "is_input_sink_record_audit_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_sink_record_audit_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_sink_record_audit", "type": "WAIT"}]], "inputParameters": {}, "name": "tr_bbg_emsi_orders_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "tr_bbg_emsi_orders_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["lazy_merge_rts22_transactions", "lazy_merge_quarantined_rts22_transactions", "lazy_merge_sink_record_audit"], "name": "tr_bbg_emsi_orders_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "tr_bbg_emsi_orders_join", "type": "JOIN"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}