{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "tr_arm_no_response_email"}}, "name": "tr_arm_no_response_email", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "tr_arm_no_response_email", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "tr_arm_no_response_email", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}