{"inputParameters": [], "inputTemplate": {"workflow": {"name": "order_tr_fidessa_eod_subworkflow"}}, "name": "order_tr_fidessa_eod_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "order_tr_fidessa_eod", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_tr_fidessa_eod", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_rts22_transactions", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_tr_fidessa_eod.output.io_param.params.RTS22Transaction}", "task": "${order_tr_fidessa_eod.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_rts22_transactions", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_rts22_transactions", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_rts22_transactions", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_tr_fidessa_eod.output.io_param.params.RTS22Transaction.params.file_uri}"}, "name": "is_input_rts22_transactions_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_rts22_transactions_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_rts22_transactions", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_quarantined_rts22_transactions", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_tr_fidessa_eod.output.io_param.params.QuarantinedRTS22Transaction}", "task": "${order_tr_fidessa_eod.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_quarantined_rts22_transactions", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_quarantined_rts22_transactions", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_quarantined_rts22_transactions", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_tr_fidessa_eod.output.io_param.params.QuarantinedRTS22Transaction.params.file_uri}"}, "name": "is_input_quarantined_rts22_transactions_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_quarantined_rts22_transactions_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_quarantined_rts22_transactions", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_orders", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_tr_fidessa_eod.output.io_param.params.orders}", "task": "${order_tr_fidessa_eod.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_orders", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_orders", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_orders", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${wait_es__apply_meta_orders.output.io_param}", "task": "${wait_es__apply_meta_orders.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "instrument_mapper", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "instrument_mapper_orders", "type": "SIMPLE"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_tr_fidessa_eod.output.io_param.params.orders.params.file_uri}"}, "name": "is_input_orders_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_orders_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_orders", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_sink_record_audit", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${order_tr_fidessa_eod.output.io_param.params.SinkRecordAudit}", "task": "${order_tr_fidessa_eod.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_sink_record_audit", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_sink_record_audit", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_sink_record_audit", "type": "WAIT"}], "description": "Decide if there is an NDJSON to process", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${order_tr_fidessa_eod.output.io_param.params.SinkRecordAudit.params.file_uri}"}, "name": "is_input_sink_record_audit_ndjson_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "is_input_sink_record_audit_ndjson_empty", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_sink_record_audit", "type": "WAIT"}]], "inputParameters": {}, "name": "order_tr_fidessa_eod_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_tr_fidessa_eod_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["lazy_merge_rts22_transactions", "lazy_merge_quarantined_rts22_transactions", "lazy_merge_sink_record_audit", "lazy_merge_orders"], "name": "order_tr_fidessa_eod_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "order_tr_fidessa_eod_join", "type": "JOIN"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}