{"inputParameters": [], "inputTemplate": {"workflow": {"name": "csv_to_record_subworkflow"}}, "name": "csv_to_record_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "csv_to_record", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "csv_to_record", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${csv_to_record.output.io_param}", "task": "${csv_to_record.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}