{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "csv_to_record"}}, "name": "csv_to_record", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"base": {"params": {"dynamic_tasks": {"dynamic_task": {"name": "csv_to_record_subworkflow", "task_reference_name": "csv_to_record_subworkflow", "type": "SUB_WORKFLOW"}}}}, "io_param": "${workflow.input.io_param}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "io_param_params_concatenator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${io_param_params_concatenator.output.result.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "file_slice_generator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "file_slice_generator", "type": "SIMPLE"}, {"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${file_slice_generator.output.io_param.params.dynamicTasks}", "dynamicTasksInput": "${file_slice_generator.output.io_param.params.dynamicTaskInputs}"}, "name": "csv_to_record_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "csv_to_record_fork", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "csv_to_record_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "csv_to_record_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}