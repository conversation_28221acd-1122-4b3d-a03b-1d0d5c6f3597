{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "telemessage"}}, "name": "telemessage", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "description": "Telemessage Transform flow", "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "telemessage_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "telemessage_transform", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "decisionCases": {"with-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${telemessage_transform.output.io_param.params.Message}", "workflow": "${workflow.input.workflow}"}, "name": "message_elastic_ingestion_with_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion_with_analytics"}, "taskReferenceName": "message_elastic_ingestion_with_analytics_ref", "type": "SUB_WORKFLOW"}], "without-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${telemessage_transform.output.io_param.params.Message}", "workflow": "${workflow.input.workflow}"}, "name": "message_elastic_ingestion_without_analytics_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "elastic_ingestion"}, "taskReferenceName": "message_elastic_ingestion_without_analytics_ref", "type": "SUB_WORKFLOW"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "no_op_message", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "no_op_message", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "(function () {if ($.file_uri != null) {if ($.is_lexica_enabled === true) return 'with-analytics'; else return 'without-analytics';} else return null;})();", "inputParameters": {"file_uri": "${telemessage_transform.output.io_param.params.Message.params.file_uri}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}"}, "name": "process_message", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "process_message", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_message", "type": "WAIT"}], [{"asyncComplete": false, "decisionCases": {"call": [{"asyncComplete": false, "decisionCases": {"comms_voice": [{"asyncComplete": false, "inputParameters": {"base": {"params": {"is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "tenant_config_feature_flags": "${analytics_controller.output.io_param.params.tenant_config_feature_flags}", "transcription_limit": "${analytics_controller.output.io_param.params.transcription_limit}", "transcription_provider": "${analytics_controller.output.io_param.params.transcription_provider}"}}, "io_param": "${telemessage_transform.output.io_param.params.Call}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "comms_sub_workflow_io_param_params_concatenator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_sub_workflow_io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${comms_sub_workflow_io_param_params_concatenator.output.result.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "comms_voice_sub_workflow"}, "taskReferenceName": "comms_voice_sub_workflow", "type": "SUB_WORKFLOW"}]}, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'comms_voice' : null", "inputParameters": {"inputValue": "${telemessage_transform.output.io_param.params.Call}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_comms_voice_sub_workflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "decisionCases": {"waveform": [{"asyncComplete": false, "inputParameters": {"io_param": "${telemessage_transform.output.io_param.params.Waveform}", "workflow": "${workflow.input.workflow}"}, "name": "waveform_transform", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "waveform_transform", "type": "SIMPLE"}]}, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'waveform' : null", "inputParameters": {"inputValue": "${telemessage_transform.output.io_param.params.Waveform}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_waveform_should_run", "type": "SWITCH"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_empty_call", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "$.file_uri != null ? 'call' : null", "inputParameters": {"file_uri": "${telemessage_transform.output.io_param.params.Call.params.file_uri}"}, "name": "process_call", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "process_call", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "lazy_merge", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lazy_merge_call", "type": "WAIT"}]], "inputParameters": {}, "name": "telemessage_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "telemessage_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["lazy_merge_call", "lazy_merge_message"], "name": "telemessage_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "telemessage_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}