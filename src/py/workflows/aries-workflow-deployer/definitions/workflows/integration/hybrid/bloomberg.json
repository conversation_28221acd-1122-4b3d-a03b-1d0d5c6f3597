{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "bloomberg"}}, "name": "bloomberg", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "sink_file_audit_init", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "analytics_controller", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "bloomberg_transform", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "bloomberg_transform", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  if ($.is_lexica_enabled) {\n    var subworkflow_name = \"elastic_ingestion_with_analytics\";\n  } else {\n    var subworkflow_name = \"elastic_ingestion\";\n  }\n  $.io_params_list = $.io_params_list ? $.io_params_list : []; // convert to empty list if null\n  for (var index = 0; index < $.io_params_list.length; index++) {\n    var sub_workflow_ref = subworkflow_name + \"_\" + $.model + \"_\" + index;\n    var subworkflow_param = {\n      subWorkflowParam: {\n        name: subworkflow_name,\n      },\n      type: \"SUB_WORKFLOW\",\n      taskReferenceName: sub_workflow_ref,\n    };\n    dynamicTasks.push(subworkflow_param);\n    dynamicTasksInput[sub_workflow_ref] = $.io_params_list[index];\n    dynamicTasksInput[sub_workflow_ref][\"workflow\"] = $.workflow_input;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();\n", "io_params_list": "${bloomberg_transform.output.io_param.params.Email.io_params_list}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "model": "email", "workflow_input": "${workflow.input.workflow}"}, "loopCondition": null, "name": "initiate_dynamic_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "initiate_dynamic_fork_email", "type": "INLINE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"dynamicTasks": "${initiate_dynamic_fork_email.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_dynamic_fork_email.output.result.dynamicTasksInput}"}, "loopCondition": null, "name": "email_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "email_ingestion", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_for_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_for_email_ingestion", "type": "JOIN"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  if ($.is_lexica_enabled) {\n    var subworkflow_name = \"elastic_ingestion_with_analytics\";\n  } else {\n    var subworkflow_name = \"elastic_ingestion\";\n  }\n  $.io_params_list = $.io_params_list ? $.io_params_list : []; // convert to empty list if null\n  for (var index = 0; index < $.io_params_list.length; index++) {\n    var sub_workflow_ref = subworkflow_name + \"_\" + $.model + \"_\" + index;\n    var subworkflow_param = {\n      subWorkflowParam: {\n        name: subworkflow_name,\n      },\n      type: \"SUB_WORKFLOW\",\n      taskReferenceName: sub_workflow_ref,\n    };\n    dynamicTasks.push(subworkflow_param);\n    dynamicTasksInput[sub_workflow_ref] = $.io_params_list[index];\n    dynamicTasksInput[sub_workflow_ref][\"workflow\"] = $.workflow_input;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();\n", "io_params_list": "${bloomberg_transform.output.io_param.params.Message.io_params_list}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "model": "message", "workflow_input": "${workflow.input.workflow}"}, "loopCondition": null, "name": "initiate_dynamic_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "initiate_dynamic_fork_msg", "type": "INLINE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"dynamicTasks": "${initiate_dynamic_fork_msg.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_dynamic_fork_msg.output.result.dynamicTasksInput}"}, "loopCondition": null, "name": "message_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "message_ingestion", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_for_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_for_msg_ingestion", "type": "JOIN"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  var subworkflow_name = \"elastic_ingestion\";\n  $.io_params_list = $.io_params_list ? $.io_params_list : []; // convert to empty list if null\n  for (var index = 0; index < $.io_params_list.length; index++) {\n    var sub_workflow_ref = subworkflow_name + \"_\" + $.model + \"_\" + index;\n    var subworkflow_param = {\n      subWorkflowParam: {\n        name: subworkflow_name,\n      },\n      type: \"SUB_WORKFLOW\",\n      taskReferenceName: sub_workflow_ref,\n    };\n    dynamicTasks.push(subworkflow_param);\n    dynamicTasksInput[sub_workflow_ref] = $.io_params_list[index];\n    dynamicTasksInput[sub_workflow_ref][\"workflow\"] = $.workflow_input;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();\n", "io_params_list": "${bloomberg_transform.output.io_param.params.ChatEvent.io_params_list}", "model": "chat_event", "workflow_input": "${workflow.input.workflow}"}, "loopCondition": null, "name": "initiate_dynamic_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "initiate_dynamic_fork_chat_event", "type": "INLINE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"dynamicTasks": "${initiate_dynamic_fork_chat_event.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_dynamic_fork_chat_event.output.result.dynamicTasksInput}"}, "loopCondition": null, "name": "chat_event_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "chat_event_ingestion", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_for_ingestion", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_for_chat_event_ingestion", "type": "JOIN"}]], "inputParameters": {"io_param": "${bloomberg_transform.output.io_param}", "task": "${bloomberg_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "bloomberg_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "bloomberg_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_for_email_ingestion", "wait_for_msg_ingestion", "wait_for_chat_event_ingestion"], "loopCondition": null, "name": "blomberg_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "blomberg_join", "type": "JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "sink_file_audit_finalize", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__sink_file_audit_finalize", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}