{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "gamma_horizon_voice"}}, "name": "gamma_horizon_voice", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "gamma_horizon_voice_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "gamma_horizon_voice_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"comms_voice": [{"asyncComplete": false, "inputParameters": {"base": {"params": {"is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "tenant_config_feature_flags": "${analytics_controller.output.io_param.params.tenant_config_feature_flags}", "transcription_limit": "${analytics_controller.output.io_param.params.transcription_limit}", "transcription_provider": "${analytics_controller.output.io_param.params.transcription_provider}"}}, "io_param": "${gamma_horizon_voice_transform.output.io_param.params.Call}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "comms_sub_workflow_io_param_params_concatenator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_sub_workflow_io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${comms_sub_workflow_io_param_params_concatenator.output.result.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "comms_voice_sub_workflow"}, "taskReferenceName": "comms_voice_sub_workflow", "type": "SUB_WORKFLOW"}]}, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'comms_voice' : null", "inputParameters": {"inputValue": "${gamma_horizon_voice_transform.output.io_param.params.Call}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_comms_voice_sub_workflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "decisionCases": {"waveform": [{"asyncComplete": false, "inputParameters": {"io_param": "${gamma_horizon_voice_transform.output.io_param.params.Waveform}", "workflow": "${workflow.input.workflow}"}, "name": "waveform_transform", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "waveform_transform", "type": "SIMPLE"}]}, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'waveform' : null", "inputParameters": {"inputValue": "${gamma_horizon_voice_transform.output.io_param.params.Waveform}"}, "name": "switch_task", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "check_if_waveform_should_run", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}