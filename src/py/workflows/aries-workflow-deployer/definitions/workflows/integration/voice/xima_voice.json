{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "xima_voice"}}, "name": "xima_voice", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "sink_file_audit_init", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "analytics_controller", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "xima_voice_transform", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "xima_voice_transform", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"comms_voice": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"base": {"params": {"is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "tenant_config_feature_flags": "${analytics_controller.output.io_param.params.tenant_config_feature_flags}", "transcription_limit": "${analytics_controller.output.io_param.params.transcription_limit}", "transcription_provider": "${analytics_controller.output.io_param.params.transcription_provider}"}}, "io_param": "${xima_voice_transform.output.io_param.params.Call}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "loopCondition": null, "name": "comms_sub_workflow_io_param_params_concatenator", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "comms_sub_workflow_io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${comms_sub_workflow_io_param_params_concatenator.output.result.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "comms_voice_sub_workflow", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": {"name": "comms_voice_sub_workflow", "taskToDomain": null, "version": null, "workflowDefinition": null}, "taskDefinition": null, "taskReferenceName": "comms_voice_sub_workflow", "type": "SUB_WORKFLOW"}]}, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'comms_voice' : null", "inputParameters": {"inputValue": "${xima_voice_transform.output.io_param.params.Call}"}, "loopCondition": null, "name": "switch_task", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "check_if_comms_voice_sub_workflow_should_run", "type": "SWITCH"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"waveform": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${xima_voice_transform.output.io_param.params.Waveform}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "waveform_transform", "optional": true, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "waveform_transform", "type": "SIMPLE"}]}, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "$.inputValue != null ? 'waveform' : null", "inputParameters": {"inputValue": "${xima_voice_transform.output.io_param.params.Waveform}"}, "loopCondition": null, "name": "switch_task", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "check_if_waveform_should_run", "type": "SWITCH"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "sink_file_audit_finalize", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__sink_file_audit_finalize", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}