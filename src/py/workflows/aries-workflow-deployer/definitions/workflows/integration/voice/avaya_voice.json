{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "avaya_voice"}}, "name": "avaya_voice", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"base": {"params": {"dynamic_tasks": {"call": {"input_parameters": {"is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "tenant_config_feature_flags": "${analytics_controller.output.io_param.params.tenant_config_feature_flags}", "transcription_limit": "${analytics_controller.output.io_param.params.transcription_limit}", "transcription_provider": "${analytics_controller.output.io_param.params.transcription_provider}"}, "name": "comms_voice_sub_workflow", "task_reference_name": "comms_voice_sub_workflow_ref", "type": "SUB_WORKFLOW"}, "waveform": {"name": "waveform_transform", "task_params": {"optional": true}, "task_reference_name": "waveform_transform_ref", "type": "SIMPLE"}}}}, "io_param": "${workflow.input.io_param}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "io_param_params_concatenator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${io_param_params_concatenator.output.result.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "avaya_voice_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "avaya_voice_transform", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${avaya_voice_transform.output.io_param.params.Call.dynamicTasks}", "dynamicTasksInput": "${avaya_voice_transform.output.io_param.params.Call.dynamicTaskInputs}"}, "name": "comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_voice_sub_workflow_ref", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_comms_voice_sub_workflow_ref", "type": "JOIN"}], [{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${avaya_voice_transform.output.io_param.params.Waveform.dynamicTasks}", "dynamicTasksInput": "${avaya_voice_transform.output.io_param.params.Waveform.dynamicTaskInputs}"}, "name": "waveform_transform", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "waveform_transform_ref", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_ingestion_waveform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_waveform_ref", "type": "JOIN"}]], "inputParameters": {}, "name": "fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "fork_ref", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["wait_for_comms_voice_sub_workflow_ref", "wait_for_ingestion_waveform_ref"], "name": "join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "join_ref", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}