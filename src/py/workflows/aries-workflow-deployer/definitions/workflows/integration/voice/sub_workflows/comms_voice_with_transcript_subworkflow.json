{"inputParameters": [], "inputTemplate": {"workflow": {"name": "ice_chat"}}, "name": "comms_voice_with_transcript_subworkflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.Call}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta", "type": "WAIT"}, {"asyncComplete": false, "decisionCases": {"with-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.Call}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "lexica_processor", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lexica_processor", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"with-transcription-copilot": [{"asyncComplete": false, "inputParameters": {"io_param": {"params": {"Call": "${lexica_processor.output.io_param}", "Transcript": "${workflow.input.io_param.params.Transcript}", "tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}}, "task": "${lexica_processor.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "aries_transcription_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "copilot_transcription_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "inputParameters": {"io_param": "${copilot_transcription_analytics.output.io_param.params.Transcript}", "task": "${copilot_transcription_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_transcript_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_transcript_with_analytics_and_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_transcript_with_analytics_and_copilot", "type": "WAIT"}], [{"asyncComplete": false, "inputParameters": {"io_param": "${copilot_transcription_analytics.output.io_param.params.Call}", "task": "${copilot_transcription_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_update_calls_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_update_calls_with_analytics_and_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_update_calls_with_analytics_and_copilot", "type": "WAIT"}]], "inputParameters": {}, "name": "calls_transcripts_with_analytics_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_with_analytics_and_copilot", "wait_es__apply_meta_update_calls_with_analytics_and_copilot"], "name": "calls_transcripts_with_analytics_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_join", "type": "JOIN"}], "without-transcription-copilot": [{"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.Transcript}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_transcript_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_transcript_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_transcript_with_analytics", "type": "WAIT"}], [{"asyncComplete": false, "inputParameters": {"io_param": "${lexica_processor.output.io_param}", "task": "${lexica_processor.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_update_calls_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_update_calls_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_update_calls_with_analytics", "type": "WAIT"}]], "inputParameters": {}, "name": "calls_transcripts_with_analytics_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "calls_transcripts_with_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_with_analytics", "wait_es__apply_meta_update_calls_with_analytics"], "name": "calls_transcripts_with_analytics_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "calls_transcripts_with_analytics_join", "type": "JOIN"}]}, "description": "Decide whether to run Transcription Co-Pilot for DeepGram based Transcript on the analytics_controller", "evaluatorType": "javascript", "expression": "(function () { if ($.tenant_config_feature_flags.indexOf('azure-processing') > -1) { return 'with-transcription-copilot'; } else { return 'without-transcription-copilot'; } })();", "inputParameters": {"tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}, "name": "switch_transcription_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_transcription_copilot", "type": "SWITCH"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.Transcript}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_transcript_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_transcript_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_transcript_without_analytics", "type": "WAIT"}], "evaluatorType": "javascript", "expression": "(function () { if ($.hasTranscriptions && $.is_lexica_enabled === true) { return 'with-analytics'; }})();", "inputParameters": {"hasTranscriptions": "${workflow.input.io_param.params.Transcript}", "is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "name": "switch_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_analytics", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}