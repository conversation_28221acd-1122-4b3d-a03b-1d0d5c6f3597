{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "email"}}, "name": "email", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "email_transform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "email_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${email_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_transform_empty_file_uri", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_transform_empty_file_uri", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_transform_empty_file_uri", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${email_transform.output.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${email_transform.output.io_param}", "task": "${email_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "deduplicate_by_meta_id", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "deduplicate_by_meta_id", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"deduplicate_by_meta_id": "${deduplicate_by_meta_id.output.io_param}", "ignore_attachments_size": "${workflow.input.io_param.params.ignore_attachments_size}", "is_lexica_enabled": "${analytics_controller.output.io_param.params.is_lexica_enabled}", "sink_file_audit_init": "${sink_file_audit_init.output.io_param}"}}, "task": "${deduplicate_by_meta_id.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "email_ingestion_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "subWorkflowParam": {"name": "email_ingestion_with_analytics"}, "taskReferenceName": "email_ingestion_with_analytics", "type": "SUB_WORKFLOW"}], "description": "Decide whether to skip the workflow if file_uri is empty", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri != null) {\n    return \"default\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${email_transform.output.io_param.params.file_uri}"}, "name": "skip_on_empty_file_uri", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_on_empty_file_uri", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}