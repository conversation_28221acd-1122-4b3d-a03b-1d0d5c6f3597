{"accessPolicy": {}, "inputParameters": [], "inputTemplate": {"workflow": {"name": "email_ingestion_with_analytics"}}, "name": "email_ingestion_with_analytics", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "decisionCases": {"analytics": [{"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.deduplicate_by_meta_id}", "workflow": "${workflow.input.workflow}"}, "name": "language_detection", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "language_detection", "type": "SIMPLE"}], [{"asyncComplete": false, "inputParameters": {"base": "${workflow.input.io_param.params.deduplicate_by_meta_id}", "io_param": {"params": {"ignore_attachments_size": "${workflow.input.io_param.params.ignore_attachments_size}"}}, "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "lexica_processor_input_params", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lexica_processor_input_params", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${lexica_processor_input_params.output.result.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "lexica_processor", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lexica_processor", "type": "SIMPLE"}]], "inputParameters": {}, "name": "comms_analytics_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["language_detection", "lexica_processor"], "name": "comms_analytics_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"language_detection": "${comms_analytics_join.output.language_detection.io_param.params}", "lexica_processor": "${comms_analytics_join.output.lexica_processor.io_param.params}"}}, "workflow": "${workflow.input.workflow}"}, "name": "merge_comms_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "merge_comms_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${merge_comms_analytics.output.io_param}", "task": "${merge_comms_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_with_analytics", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.sink_file_audit_init}", "task": "${wait_es__apply_meta_with_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_with_analytics", "type": "WAIT"}], "empty_file_uri": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.sink_file_audit_init}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_empty_file_uri", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_empty_file_uri", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_empty_file_uri", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.deduplicate_by_meta_id}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_without_analytics", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param.params.sink_file_audit_init}", "task": "${wait_es__apply_meta_without_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize_without_analytics", "type": "WAIT"}], "description": "Decide whether to apply Comms Analytics", "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri != null) {\n    return $.is_lexica_enabled ? \"analytics\" : \"non-analytics\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();", "inputParameters": {"file_uri": "${workflow.input.io_param.params.deduplicate_by_meta_id.params.file_uri}", "is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "name": "switch_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_analytics", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}