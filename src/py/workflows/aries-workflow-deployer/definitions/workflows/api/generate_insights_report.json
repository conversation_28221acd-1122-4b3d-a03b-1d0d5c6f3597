{"failureWorkflow": "aries_workflow_failure_oma_event", "inputParameters": [], "inputTemplate": {}, "name": "generate_insights_report", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "fetch_insights_report", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "fetch_insights_report", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  var task_name = \"insights_report_generator\";\n  $.io_params_list = $.io_params_list ? $.io_params_list : []; // convert to empty list if null\n  for (var index = 0; index < $.io_params_list.length; index++) {\n    var task_ref = task_name + \"_\" + index;\n    var task_param = {\n      name: task_name,\n      taskReferenceName: task_ref,\n    };\n    dynamicTasks.push(task_param);\n    dynamicTasksInput[task_ref] = {\"io_param\": {\"params\": {\"watch_id\": $.io_params_list[index]}}};\n    dynamicTasksInput[task_ref][\"workflow\"] = $.workflow_input;\n    dynamicTasksInput[\"task\"] = $.task;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();", "io_params_list": "${fetch_insights_report.output.io_param.params.watch_ids}", "task": "${fetch_insights_report.output.task}", "workflow_input": "${workflow.input.workflow}"}, "loopCondition": null, "name": "initiate_dynamic_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "initiate_dynamic_fork_insights", "type": "INLINE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"dynamicTasks": "${initiate_dynamic_fork_insights.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_dynamic_fork_insights.output.result.dynamicTasksInput}"}, "loopCondition": null, "name": "insights_report_generator", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "insights_report_generator", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "insights_report_generator_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "insights_report_generator_join", "type": "JOIN"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}