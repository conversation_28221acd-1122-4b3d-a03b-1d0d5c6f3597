{"failureWorkflow": "aries_workflow_failure_oma_event", "inputParameters": [], "inputTemplate": {"workflow": {"name": "api_task_trigger"}}, "name": "api_task_trigger", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "api_task_trigger", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "api_task_trigger", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}