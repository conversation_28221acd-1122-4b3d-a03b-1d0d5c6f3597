{"failureWorkflow": "aries_workflow_failure_oma_event", "inputParameters": [], "inputTemplate": {"workflow": {"name": "case_bulk"}}, "name": "case_bulk", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "bulk_records_processor", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "bulk_records_processor", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  $.io_params_list = $.io_params_list ? $.io_params_list : []; // convert to empty list if null\n  for (var index = 0; index < $.io_params_list.length; index++) {\n    var sub_workflow_ref = $.sub_workflow_name + \"_\" + index;\n    var subworkflow_param = {\n      subWorkflowParam: {\n        name: $.sub_workflow_name,\n      },\n      type: \"SUB_WORKFLOW\",\n      taskReferenceName: sub_workflow_ref,\n    };\n    dynamicTasks.push(subworkflow_param);\n    dynamicTasksInput[sub_workflow_ref] = {\n io_param: $.io_params_list[index] };\n    dynamicTasksInput[sub_workflow_ref][\"workflow\"] = $.workflow_input;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();", "io_params_list": "${bulk_records_processor.output.io_param.params.case_records}", "sub_workflow_name": "case_bulk_sub_workflow", "workflow_input": "${workflow.input.workflow}"}, "name": "initiate_dynamic_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "initiate_dynamic_fork_case_records", "type": "INLINE"}, {"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${initiate_dynamic_fork_case_records.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_dynamic_fork_case_records.output.result.dynamicTasksInput}"}, "name": "case_records_validation_and_insertion", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "case_records_validation_and_insertion_case_records", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "processing_validation_and_insertion", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "processing_validation_and_insertion_case_records", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"status": "PROCESSED", "task_name": "api-task-status-update"}}, "workflow": "${workflow.input.workflow}"}, "name": "api_task_trigger", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "api_task_status_update", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}