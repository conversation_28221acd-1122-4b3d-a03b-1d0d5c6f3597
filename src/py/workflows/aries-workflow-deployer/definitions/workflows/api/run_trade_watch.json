{"failureWorkflow": "surveillance_workflow_failure", "inputParameters": [], "inputTemplate": {}, "name": "run_trade_watch", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "trade_watch_detect", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "trade_watch_detect", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"hits_found": [{"asyncComplete": false, "inputParameters": {"io_param": "${trade_watch_detect.output.io_param}", "task": "${trade_watch_detect.output.task}", "workflow": "${trade_watch_detect.input.workflow}"}, "name": "trade_watch_enrich", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "trade_watch_enrich", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${trade_watch_enrich.output.io_param}", "task": "${trade_watch_enrich.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta", "type": "WAIT"}]}, "description": "Switch on whether hits were found", "evaluatorType": "javascript", "expression": "(function () { return $.flow_path })();", "inputParameters": {"flow_path": "${trade_watch_detect.output.io_param.params.flow_path}"}, "name": "switch_hits_found", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_hits_found", "type": "SWITCH"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"merge_output": {"elastic_connector": {"io_param": "${wait_es__apply_meta.output.io_param}", "task": {"name": "elastic_connector"}}}, "upper_bound_date": "${trade_watch_detect.output.io_param.params.upper_bound_date}", "watch_execution_id": "${trade_watch_detect.output.io_param.params.watch_execution_id}", "watch_id": "${trade_watch_detect.output.io_param.params.watch_id}"}}, "task": "${trade_watch_detect.output.task}", "workflow": "${trade_watch_detect.input.workflow}"}, "name": "surveillance_watch_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "surveillance_watch_finalize", "type": "SIMPLE"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}