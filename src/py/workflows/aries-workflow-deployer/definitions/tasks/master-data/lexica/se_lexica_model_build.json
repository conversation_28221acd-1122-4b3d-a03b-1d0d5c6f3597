{"backoffScaleFactor": 1, "concurrentExecLimit": null, "executionNameSpace": null, "inputTemplate": {}, "isolationGroupId": null, "name": "se_lexica_model_build", "ownerEmail": "<EMAIL>", "pollTimeoutSeconds": 43200, "rateLimitFrequencyInSeconds": 1, "rateLimitPerFrequency": 0, "responseTimeoutSeconds": 300, "retryCount": 1, "retryDelaySeconds": 120, "retryLogic": "EXPONENTIAL_BACKOFF", "timeoutPolicy": "RETRY", "timeoutSeconds": 3600}