{"backoffScaleFactor": 1, "concurrentExecLimit": 1, "executionNameSpace": null, "inputTemplate": {}, "isolationGroupId": null, "name": "watches_schedule_sync", "ownerEmail": "<EMAIL>", "pollTimeoutSeconds": 43200, "rateLimitFrequencyInSeconds": 1, "rateLimitPerFrequency": 0, "responseTimeoutSeconds": 300, "retryCount": 1, "retryDelaySeconds": 120, "retryLogic": "EXPONENTIAL_BACKOFF", "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 600}