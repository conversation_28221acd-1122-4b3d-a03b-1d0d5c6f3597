{"backoffScaleFactor": 1, "concurrentExecLimit": 1, "executionNameSpace": null, "inputTemplate": {}, "isolationGroupId": null, "name": "lexica_model_build", "ownerEmail": "<EMAIL>", "pollTimeoutSeconds": 43200, "rateLimitFrequencyInSeconds": 1, "rateLimitPerFrequency": 0, "responseTimeoutSeconds": 300, "retryCount": 1, "retryDelaySeconds": 120, "retryLogic": "EXPONENTIAL_BACKOFF", "timeoutPolicy": "RETRY", "timeoutSeconds": 1800}