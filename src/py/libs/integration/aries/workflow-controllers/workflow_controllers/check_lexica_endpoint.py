import logging
from aries_config_cached_client.lexica import CachedLexicaAPIClient

logger = logging.getLogger(__name__)


def is_lexica_enabled_for_tenant(tenant: str, stack: str) -> bool:
    """This method calls the lexica API endpoint for the required stack/tenant.

    :param tenant: task input `tenant`
    :param stack: task input `stack`
    :return: bool indicating whether the tenant has lexica pre-processing enabled or not
    """

    # the default option skips lexica pre-processing
    result = False

    try:
        lexica_result = CachedLexicaAPIClient.get_se_lexica_version(
            tenant_name=tenant, stack_name=stack
        )

        logger.info(f"Lexica response for tenant '{tenant}': {lexica_result}")
        # this event action triggers lexica
        if lexica_result.get("se_lexica_version"):
            result = True

    except Exception:
        logger.error(f"Lexica not found for tenant '{tenant}'", exc_info=True)

    return result
