import json
import pandas as pd
from aries_se_core_tasks.io.read.json_batch_csv_downloader import (  # type: ignore[attr-defined]
    Params,
    run_json_batch_csv_downloader,
)
from pathlib import Path
from se_core_tasks.io.read import json_batch_csv_downloader
from se_elastic_schema.models import Message
from se_enums.cloud import CloudProviderEnum
from se_io_utils.json_utils import write_named_temporary_json
from unittest.mock import patch

AUDIT_PATH = write_named_temporary_json(content={}, output_filename="audit.json")
APP_METRICS_PATH = write_named_temporary_json(
    content={"errored_count": 0}, output_filename="app_metrics.json"
)


SCRIPT_PATH: Path = Path(__file__).parent
VALID_JSON = Path(__file__).parent.joinpath("data", "json", "file_2.json").as_posix()
INVALID_JSON = (
    Path(__file__).parent.joinpath("data", "invalid_json", "invalid_format.json").as_posix()
)


class TestJsonBatchCsvDownloader:
    @patch.object(
        json_batch_csv_downloader,
        "run_download_file",
        side_effect=[VALID_JSON, INVALID_JSON],
    )
    def test_audits_json_batch_csv_downloader(self, mock_content):
        source_frame = pd.DataFrame(
            {
                "metadata_column": [
                    "file_1.json",
                    "invalid_format.json",
                ],
            }
        )
        result = run_json_batch_csv_downloader(
            params=Params(
                metadata_column="metadata_column",
                dataframe_columns=["meta"],
            ),
            source_frame=source_frame,
            streamed=True,
            cloud_provider=CloudProviderEnum.AWS,
            model=Message,
            should_audit_failures=True,
            audit_path=AUDIT_PATH,
            app_metrics_path=APP_METRICS_PATH,
        )
        result = result.frame()
        data_columns = result.columns.difference(["metadata_column"])
        assert result.shape[0] == 1
        assert result[data_columns].isnull().all(axis=1).sum() == 0
        with open(APP_METRICS_PATH) as f:
            app_metrics = json.loads(f.read())
        with open(AUDIT_PATH) as f:
            audits = json.loads(f.read())

        assert audits == {
            "input_files": {
                "invalid_format.json": {
                    "Message": {
                        "created": 0,
                        "errored": 1,
                        "skipped": 0,
                        "duplicate": 0,
                        "updated": 0,
                        "status": ["Failed to load json content"],
                    }
                }
            },
            "workflow_status": [],
        }

        assert app_metrics == {"errored_count": 1}
