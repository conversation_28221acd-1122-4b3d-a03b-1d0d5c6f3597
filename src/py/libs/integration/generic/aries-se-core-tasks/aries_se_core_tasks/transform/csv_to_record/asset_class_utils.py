# type: ignore
from aries_se_core_tasks.transform.csv_to_record.data_util import empty_if_none, is_empty
from se_trades_tasks.order_and_tr.instrument.fallback.static import BestExAssetClassSub
from typing import Optional, Tuple


class BestExAssetClassMain:
    COMMODITIES_DERIVATIVES_AND_EMISSION_ALLOWANCES_DERIVATIVES = (
        "Commodities derivatives and emission allowances Derivatives"
    )
    CREDIT_DERIVATIVES = "Credit Derivatives"
    CONTRACTS_FOR_DIFFERENCE = "Contracts for Difference"
    CURRENCY_DERIVATIVES = "Currency Derivatives"
    DEBT_INSTRUMENTS = "Debt Instruments"
    EMISSION_ALLOWANCES = "Emission Allowances"
    EQUITY = "Equity"
    EQUITY_DERIVATIVES = "Equity Derivatives"
    EXCHANGE_TRADED_PRODUCTS = "Exchange Traded Products"
    INTEREST_RATE_DERIVATIVES = "Interest Rate Derivatives"
    # NOTE: OTHER_INSTRUMENTS_+ variables kept. Currently regulation says Other Instruments
    # to be generic, but it might change in the future.
    OTHER_INSTRUMENTS = "Other Instruments"
    OTHER_INSTRUMENTS_COMMODITY_SPOT = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_FX_SPOT = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_OTHER = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_CIVS = OTHER_INSTRUMENTS
    STRUCTURED_FINANCE_INSTRUMENTS = "Structured Finance Instruments"


def asset_class(record):
    ext = record.ext
    ext.bestExAssetClassMain, ext.bestExAssetClassSub = get_asset_class(record)


def get_asset_class(record: dict) -> Tuple[str, Optional[str]]:
    cfi = empty_if_none(record.get("instrumentClassification")).upper()

    if is_empty(cfi):
        return BestExAssetClassMain.OTHER_INSTRUMENTS, None

    best_asset_class_main = get_best_ex_asset_class_main(cfi=cfi)

    best_asset_class_sub = get_best_ex_asset_class_sub(
        best_ex_asset_class_main=best_asset_class_main, cfi=cfi
    )
    return best_asset_class_main, best_asset_class_sub


def get_best_ex_asset_class_main(cfi: str) -> str:
    (
        cfi_category,
        cfi_group,
        cfi_attribute_1,
        cfi_attribute_2,
        cfi_attribute_3,
        cfi_attribute_4,  # noqa: F841
    ) = [*cfi]

    cfi_cat_and_group = cfi_category + cfi_group
    cfi_cat_and_attr_1 = cfi_category + cfi_attribute_1
    cfi_cat_and_attr_2 = cfi_category + cfi_attribute_2
    cfi_cat_and_attr_3 = cfi_category + cfi_attribute_3
    cfi_cat_and_group_and_attr_1 = cfi_category + cfi_group + cfi_attribute_1

    # Debt Instrument
    if (
        (cfi_cat_and_group == "MB")
        or (cfi_cat_and_attr_1 == "TD")
        or (cfi_category == "D" and cfi_group not in ["E", "S"])
        or (cfi_category == "E" and cfi_group in ["B", "W", "T", "N"])
    ):
        return BestExAssetClassMain.DEBT_INSTRUMENTS

    # Other Instruments - Commodity Spot
    if (cfi_cat_and_group == "IT") or (cfi_cat_and_attr_1 == "TT"):
        return BestExAssetClassMain.OTHER_INSTRUMENTS_COMMODITY_SPOT

    # Other Instruments - FX Spot
    if cfi_cat_and_group in ["TC", "IF"]:
        return BestExAssetClassMain.OTHER_INSTRUMENTS_FX_SPOT

    # Other Instruments - Other
    if cfi_cat_and_group == "HM":
        return BestExAssetClassMain.OTHER_INSTRUMENTS_OTHER

    # Other Instruments - CIVs
    if (cfi_category == "C" and cfi_group != "E") or (
        cfi_cat_and_attr_1 == "TF" and cfi_group != "D"
    ):
        return BestExAssetClassMain.OTHER_INSTRUMENTS_CIVS

    # Interest Rate Derivatives
    if (
        (cfi_cat_and_attr_2 == "ON")
        or (cfi_cat_and_attr_1 == "FN" and cfi_group != "C")
        or (cfi_cat_and_attr_1 == "RD" and cfi_group in ["F", "W"])
        or (cfi_cat_and_group in ["TR", "HR", "SR", "HH", "JR", "KR"])
    ):
        return BestExAssetClassMain.INTEREST_RATE_DERIVATIVES

    # Commodities derivatives and emission allowances Derivatives
    if (
        (cfi_cat_and_attr_2 == "OT")
        or (cfi_cat_and_attr_1 == "RT")
        or (cfi_cat_and_group in ["FC", "JT", "HT", "KT", "ST"])
    ):
        return BestExAssetClassMain.COMMODITIES_DERIVATIVES_AND_EMISSION_ALLOWANCES_DERIVATIVES

    # Credit Derivatives
    if (
        (cfi_cat_and_group_and_attr_1 in ["RSB", "RPB"])
        or (cfi_cat_and_attr_1 == "FD")
        or (cfi_cat_and_attr_2 == "OD")
        or (cfi_cat_and_group in ["HC", "JC", "KC", "SC"])
    ):
        return BestExAssetClassMain.CREDIT_DERIVATIVES

    # Currency Derivatives
    if (
        (cfi_cat_and_attr_2 == "OC")
        or (cfi_cat_and_group in ["TT", "SF", "HF", "JF", "KF"])
        or (cfi_cat_and_attr_1 == "FC")
        or (cfi_cat_and_group_and_attr_1 in ["RWC", "RFC"])
    ):
        return BestExAssetClassMain.CURRENCY_DERIVATIVES

    # Structured Finance Instruments
    if (
        (cfi_category == "L")
        or (cfi_cat_and_group in ["EY", "DS", "DE"])
        or (cfi_category == "M" and cfi_attribute_1 in ["R", "I", "T"])
    ):
        return BestExAssetClassMain.STRUCTURED_FINANCE_INSTRUMENTS

    # Equity Derivatives
    if (
        (cfi_category == "O" and cfi_attribute_2 in ["B", "I", "S"])
        or (cfi_category == "F" and cfi_attribute_1 in ["B", "I", "S", "V"] and cfi_group != "C")
        or (cfi_cat_and_group in ["HE", "JE", "KE", "RA", "RD", "SE", "TI"])
        or (cfi_cat_and_group in ["RS", "RP"] and cfi_attribute_1 != "B")
        or (
            cfi_cat_and_group_and_attr_1 in ["MCS", "RFB", "RFI", "RFS", "RWB", "RWI", "RWS", "TBI"]
        )
    ):
        return BestExAssetClassMain.EQUITY_DERIVATIVES

    # Contracts for Difference
    if (cfi_cat_and_attr_3 == "JC") or (cfi_cat_and_attr_1 == "SC"):
        return BestExAssetClassMain.CONTRACTS_FOR_DIFFERENCE

    # Exchange Traded Products
    if cfi_cat_and_group == "CE":
        return BestExAssetClassMain.EXCHANGE_TRADED_PRODUCTS

    # Emission Allowances
    if cfi_cat_and_group_and_attr_1 == "MMN":
        return BestExAssetClassMain.EMISSION_ALLOWANCES

    # Equity
    if (
        (cfi_category == "E" and cfi_group != "Y")
        or (cfi_cat_and_group in ["TD", "EC", "ED"])
        or (cfi_cat_and_attr_2 == "RT")
        or (cfi_cat_and_attr_1 == "TE")
    ):
        return BestExAssetClassMain.EQUITY

    return BestExAssetClassMain.OTHER_INSTRUMENTS


def get_best_ex_asset_class_sub(best_ex_asset_class_main: str, cfi: str) -> Optional[str]:
    cfi_category = cfi[0]
    cfi_group = cfi[1]

    if best_ex_asset_class_main == BestExAssetClassMain.DEBT_INSTRUMENTS:
        return (
            BestExAssetClassSub.MONEY_MARKET_INSTRUMENTS
            if cfi_group == "Y"
            else BestExAssetClassSub.BONDS
        )

    if best_ex_asset_class_main == BestExAssetClassMain.INTEREST_RATE_DERIVATIVES:
        return (
            BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED
            if cfi_category in ["O", "F"]
            else BestExAssetClassSub.SWAPS_FORWARDS_INTEREST_RATES
        )

    if best_ex_asset_class_main == BestExAssetClassMain.CREDIT_DERIVATIVES:
        return (
            BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED
            if cfi_category in ["O", "F"]
            else BestExAssetClassSub.OTHER_CREDIT_DERIVATIVES
        )

    if best_ex_asset_class_main == BestExAssetClassMain.CURRENCY_DERIVATIVES:
        return (
            BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED
            if cfi_category in ["O", "F"]
            else BestExAssetClassSub.SWAPS_FORWARDS_CURRENCY_DERIVATIVES
        )

    if best_ex_asset_class_main == BestExAssetClassMain.EQUITY_DERIVATIVES:
        return (
            BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED
            if cfi_category in ["O", "F"]
            else BestExAssetClassSub.SWAPS_EQUITY_DERIVATIVES
        )

    if (
        best_ex_asset_class_main
        == BestExAssetClassMain.COMMODITIES_DERIVATIVES_AND_EMISSION_ALLOWANCES_DERIVATIVES
    ):
        return (
            BestExAssetClassSub.FUTURE_OPTIONS_ADMITTED
            if cfi_category in ["O", "F"]
            else BestExAssetClassSub.OTHER_COMMODITIES_DERIVATIVES
        )
    return None
