import fsspec
import json
import logging
import pandas as pd
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput
from typing import Optional

logger_ = logging.getLogger(__name__)


class FailIfInvalidInput(TaskException):
    pass


class FailIfInputIsNotAnNDJSON(TaskException):
    pass


class FailIfDestinationPathIsMissingFileName(TaskException):
    pass


class FailIfReadingNDJSONFailed(TaskException):
    pass


class FailIfNDJSONContentIsInvalid(TaskException):
    pass


class DataframeFromBatchNDJSON(IntegrationTask):
    """
    Streamed Workflows, such as Email, use the Batching Service to collect
    records into sizeable chunks to be processed downstream.
    The output of that service is an NDJSON file with a specific structure:

    {
        "sequence": 1, "
        trace_id": "8cGTXER0j_xsGf_oqh7to",
        "file_uri": "s3://storey.dev.steeleye.co/feeds/eml/
        steel-eye.com/2ll9no2oan3fq98o0cn956gcd24j6va6b8uqr4o1"
    }

    The goal of this Task, is to parse an IntegrationAriesTaskInput object
    (that contains the IoParamFieldSet content of the input to a Conductor Aries Task)
    and its NDJSON reference, to obtain a dataframe with all the file-uri in a col.
    """

    def _run(
        self,
        transform_input: IntegrationAriesTaskInput,
        file_url_col: str,
        **kwargs,
    ) -> pd.DataFrame:
        try:
            ndjson_path = transform_input.file_uri

        except (KeyError, AttributeError):
            raise FailIfInvalidInput(
                "Input object must extend from IntegrationAriesTaskInput "
                "and contain a `file_uri` field"
            )

        if not ndjson_path.endswith(".ndjson"):
            raise FailIfInputIsNotAnNDJSON(f"filepath is not an ndjson: {ndjson_path}")

        data = []

        try:
            output_file = fsspec.open(ndjson_path, mode="r")
            with output_file as f:
                for line in f:
                    data.append(json.loads(line))

        except IsADirectoryError as e:
            logger_.exception("Failed to read file. Destination path is missing file name.")
            raise FailIfDestinationPathIsMissingFileName(
                f"an error occurred while fetching: {ndjson_path} - {e}"
            )

        except Exception as e:
            logger_.exception(e)
            raise FailIfReadingNDJSONFailed(
                f"an error occurred while fetching: {ndjson_path} - {e}"
            )

        try:
            cloud_fs, _, (_,) = fsspec.get_fs_token_paths(ndjson_path)
            input_files = []
            for file_ in data:
                if cloud_fs.isfile(file_["file_uri"]):
                    input_files.append(file_["file_uri"])
        except KeyError:
            logger_.exception(
                "Batch NDJSON is not formatted correctly. It should contain the `file_uri` key"
            )
            raise FailIfNDJSONContentIsInvalid(f"an error occurred while processing: {ndjson_path}")

        self.update_app_metrics(field=GenericAppMetricsEnum.INPUT_COUNT, value=len(input_files))

        return pd.DataFrame(input_files, columns=[file_url_col])


def run_dataframe_from_batch_ndjson(
    transform_input: IntegrationAriesTaskInput,
    file_url_col: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> pd.DataFrame:
    task = DataframeFromBatchNDJSON(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(transform_input=transform_input, file_url_col=file_url_col, **kwargs)  # type: ignore
