import logging
import os
from pathlib import Path
from typing import List, Union

logger = logging.getLogger(__name__)


def delete_local_files(files_to_delete: Union[List[Path], List[str]]) -> None:
    """Deletes a list of temp files whose paths are given in the
    files_to_delete param."""

    num_files = len(files_to_delete)
    logger.info(f"{num_files} local files will be deleted")
    deleted_files_count = 0

    for file_ in files_to_delete:
        try:
            os.remove(file_)
            deleted_files_count += 1
        except FileNotFoundError as fe:
            logger.warning(f"File {file_} isn't present and can't be deleted. {fe}")

        except OSError as oe:
            logger.warning(f"File {file_} not deleted due to exception: {oe}")
            return

    logger.info(f"{deleted_files_count} out of {num_files} local files were successfully deleted")


def get_batched_file_name(file_path: str, batch_num: int) -> str:
    """
    This method simply adds a batch number to the file name.
    For Example:
        If the file_path is s3://bucket_name/ndjson_file.ndjson
        This method would return : s3://bucket_name/ndjson_file_1.ndjson
        if batch_num is 1
    Note: This works for both S3 as well as local file paths
    :param file_path: path of the file
    :param batch_num: batch number
    :return: batched file name
    """
    output_dir, file_name = file_path.rsplit("/", 1)
    file_name_without_extension = Path(file_name).stem
    extension = Path(file_name).suffix
    batch_file_name = f"{file_name_without_extension}_{batch_num}{extension}"
    return f"{output_dir}/{batch_file_name}"
