import csv
import fsspec
import logging
import nanoid
import pandas as pd
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from aries_se_core_tasks.io.read.utils import evaluate_csv_delimiter, unquote_list_of_strings
from chardet import UniversalDetector
from dataclasses import dataclass
from pydantic import Field
from se_core_tasks.utils.data_manipulation import snif_csv_delimiter
from typing import Optional

logger_ = logging.getLogger(__name__)


@dataclass
class CsvFileExtractorResult:
    csv_file_path: Optional[str]
    dataframe: Optional[pd.DataFrame]
    encoding: str


class FailIfDuplicateColumns(TaskException):
    pass


class Params(TaskParams):
    encoding: Optional[str] = Field(
        default=None,
        description="Encoding to be passed into pandas.read_csv. Only populate this"
        "parameter if you are sure that the input file will always "
        "have the same encoding i.e. `utf-8`",
    )
    skiprows: int = Field(
        default=...,
        description="`skiprows` to be passed into pandas.read_csv. This mandatory"
        "parameter represents how many rows will NOT be read from the input CSV file, "
        "starting from its first line .i.e. `10` means that the Header row + "
        "the first 9 rows will be excluded. `1` means that only the Header row will "
        "be excluded",
    )
    nrows: int = Field(
        default=...,
        description="`nrows` to be passed into pandas.read_csv. This mandatory "
        "parameter represents how many rows WILL be read fom the input CSV file, "
        "starting from the row number `skiprows`",
    )
    delimiter: str = Field(
        default=",",
        description="`delimiter` of the input CSV file that is needed to format the output"
        "extracted CSV/DataFrame correctly",
    )
    return_dataframe: bool = Field(
        default=False,
        description="Flag that controls if the output of this task is a string "
        "referencing the location of the extracted CSV file or a DataFrame",
    )
    populate_input_count_metrics: bool = Field(
        default=False,
        description="Flag that controls if the input count metrics should be populated",
    )
    unquote_header: bool = Field(
        default=True, description="Removes double quote characters from the header as well as \r"
    )
    allow_duplicated_column_in_header: bool = Field(
        default=False, description="Allow duplicated columns in the header of the CSV file"
    )
    remove_duplicated_header_columns: bool = Field(
        default=False,
        description="If True, removes any duplicated header columns in a file while"
        "reading the file. Note that the first occurrence of a column is always"
        "considered to be the original column, while the other occurrences"
        "are considered to be duplicates.",
    )
    sniff_delimiter: bool = Field(
        default=False,
        description="Determines the csv file delimiter automatically and overrides the "
        "`params.delimiter`. By default it's `False`",
    )
    use_custom_sniffer: bool = Field(
        default=False,
        description="Uses SteelEye's custom sniffer logic instead of csv's. "
        "By default it's `False`",
    )


class CsvFileExtractor(IntegrationTask):
    def _run(
        self,
        csv_file_uri: str,
        target_dir: str,
        params: Params,
        **kwargs,
    ) -> CsvFileExtractorResult:
        """This Task is responsible for extracting a specific set of rows from
        a CSV file and producing a new CSV file from it. This Task returns the
        absolute path of the new CSV file as a string.

        The `params.skiprows` parameter indicates how many rows will be skipped
        from the beginning of the file, and the `params.nrows` parameter indicates
        how many rows will be extracted from that location.

        File encoding can be parameterised or auto-detected.

        NOTE: The Flow that invokes this Task is responsible for deleting
        the `target_dir` once the Flow is finished

        :param csv_file_uri: Absolute path of the CSV file to be extracted, as string
        :param target_dir: Absolute path of the directory where the target-extracted CSV
            file will be stored at, as string
        :param params: Task Parameters
        :return: Absolute path of the subset CSV file, as string
        """
        input_encoding = params.encoding
        detector = UniversalDetector()
        number_of_selected_rows = 0

        with fsspec.open(csv_file_uri, "rb") as file:
            first_line = file.readline()
            number_of_selected_rows += 1

            if not params.encoding:
                detector.feed(first_line)

                # Iterate over every irrelevant line, as per
                # `params.skiprows` and store the first relevant line
                i = 1  # the first line (header) has been read already
                for line in file:
                    if i == params.skiprows:
                        number_of_selected_rows += 1
                        detector.feed(line)
                        break
                    i += 1

                # As soon as we have skipped all irrelevant lines,
                # feed `nrows` number of lines to the encoding detector
                # note that the first relevant line has already been saved, thus i = 1
                i = 1
                for line in file:
                    if i == params.nrows:
                        break
                    number_of_selected_rows += 1
                    detector.feed(line)

                    # don't read any more lines if we have enough data already to
                    # detect the encoding
                    if detector.done:
                        break

                    i += 1
                detector.close()
                detected_encoding: str = detector.result.get("encoding", "utf-8") or "utf-8"

                logger_.info(
                    f"Isolated {number_of_selected_rows} lines from the input "
                    f"CSV file, including the header"
                    f" and detected the {detected_encoding} encoding"
                )

        encoding = input_encoding or detected_encoding

        with fsspec.open(csv_file_uri, "rb") as file:
            first_line = file.readline().decode(encoding=encoding).strip()

        delimiter = params.delimiter
        if params.sniff_delimiter:
            logger_.info("Attempting to detect CSV delimiter")
            try:
                # if params.use_custom_sniffer is True use evaluate_csv_delimiter (custom)
                # else use snif_csv_delimiter (csv.Sniffer)
                delimiter = (
                    evaluate_csv_delimiter if params.use_custom_sniffer else snif_csv_delimiter
                )(csv_file_uri, encoding)

                logger_.info(f"Detected delimiter as: {delimiter}")
            except csv.Error:
                logger_.exception(f"Failed to detect delimiter, going with {delimiter}")

        header_line = first_line.split(delimiter)
        # Remove quotes and carriage returns from the header
        if params.unquote_header:
            header_line = unquote_list_of_strings(list_str=header_line)
        header_df = pd.DataFrame(columns=header_line)

        try:
            if params.allow_duplicated_column_in_header:
                header_df = pd.read_csv(
                    filepath_or_buffer=csv_file_uri,
                    encoding=encoding,
                    delimiter=delimiter,
                    nrows=0,
                    dtype=str,
                )

            extracted_df: pd.DataFrame = pd.read_csv(  # type: ignore
                filepath_or_buffer=csv_file_uri,
                encoding=encoding,
                delimiter=delimiter,
                skiprows=params.skiprows,
                nrows=params.nrows,
                names=header_df.columns,
                dtype=str,
            )

        except ValueError as e:
            # If the header has duplicate columns. pd.read_csv() will raise
            # an exception due to the `names` argument
            # i.e. ValueError('Duplicate names are not allowed.')
            header_columns = header_df.columns.to_list()
            column_count_dict = {x: header_columns.count(x) for x in header_columns}
            duplicate_columns_count_dict = {
                x: f"{y}x" for x, y in column_count_dict.items() if y > 1
            }
            # i.e. {'Column1': '2x', 'Column2': '2x'}

            if not duplicate_columns_count_dict:
                # If pd.read_csv() raised a ValueError
                # for a different reason other than duplicate columns,
                # we want to re-raise the exception and
                # not provide a misleading message
                raise e

            if params.remove_duplicated_header_columns:
                # When there are multiple columns with the same name,
                # we get the index of the duplicated columns by creating
                # a mask. Note that we consider the first occurrence as
                # the original column).
                # We then read the CSV without headers and use
                # the mask created above to ignore all duplicate columns.
                unique_columns_mask = ~header_df.columns.duplicated(keep="first")
                unique_headers = header_df.columns[unique_columns_mask]
                # Read the CSV without headers
                extracted_df: pd.DataFrame = pd.read_csv(  # type: ignore
                    filepath_or_buffer=csv_file_uri,
                    encoding=encoding,
                    delimiter=delimiter,
                    skiprows=params.skiprows,
                    nrows=params.nrows,
                    dtype=str,
                    header=None,
                )
                extracted_df = extracted_df.loc[:, unique_columns_mask]
                extracted_df.columns = unique_headers

            else:
                raise FailIfDuplicateColumns(
                    f"The input file has duplicate columns: {duplicate_columns_count_dict}. "
                    f"Please select the right data points you "
                    f"want to ingest, remove the duplicate columns, and try again."
                )

        if params.populate_input_count_metrics:
            self.update_app_metrics(field=GenericAppMetricsEnum.INPUT_COUNT, value=params.nrows)

        if params.return_dataframe:
            return CsvFileExtractorResult(
                csv_file_path=None, encoding=encoding, dataframe=extracted_df.fillna(pd.NA)
            )

        target_file_path: str = f"{target_dir}/{nanoid.generate(size=8)}.csv"
        extracted_df.to_csv(path_or_buf=target_file_path, encoding=encoding, index=False)

        return CsvFileExtractorResult(
            csv_file_path=target_file_path, encoding=encoding, dataframe=None
        )


def run_csv_file_extractor(
    csv_file_uri: str,
    target_dir: str,
    params: Params,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> CsvFileExtractorResult:
    task = CsvFileExtractor(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(  # type: ignore
        csv_file_uri=csv_file_uri,
        params=params,
        target_dir=target_dir,
        **kwargs,
    )
