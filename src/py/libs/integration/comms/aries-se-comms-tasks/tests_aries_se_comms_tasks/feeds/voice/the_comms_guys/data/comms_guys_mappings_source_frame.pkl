���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�2023-03-14_11-19-03��2023-03-14_11-19-03��2023-03-14_15-42-10�et�b�builtins��slice���K KK��R�K��R�hhhK ��h��R�(KKK��h�]�(�+34934920030��34934920030��01412450029�et�bh%KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�02031502841��02031502841��02031502841�et�bh%KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�	275301001��	275301001��	275301001�et�bh%KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(��s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_11-19-03_+34934920030_02031502841_275301001.mp3���s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_11-19-03_34934920030_02031502841_275301001.mp3���s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_15-42-10_01412450029_02031502841_275301001.mp3�et�bh%KKK��R�K��R�hhhK ��h��R�(KKK��h�O8�����R�(KhNNNJ����J����K?t�b�]�(��s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_11-19-03_+34934920030_02031502841_275301001.mp3���s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_11-19-03_34934920030_02031502841_275301001.mp3���s3://pinafore.dev.steeleye.co/aries/ingress/streamed/evented/the_comms_guys_voice/2023/05/29/record_2023-03-14_15-42-10_01412450029_02031502841_275301001.mp3�et�bh%KKK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���hq�Index���}�(�data�hhK ��h��R�(KK��h�O8�����R�(KhNNNJ����J����K?t�b�]�(�__datetime__��__source_number__��__called_number__��__destination_number__��__source_key__��file_url�et�b�name�Nu��R�hs�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.