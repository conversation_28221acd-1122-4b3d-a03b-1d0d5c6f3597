import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.message.refinitiv_fxt_chat.pre_process_refinitiv_fxt_messages import (  # noqa: E501
    SkipIfSourceFrameEmpty,
    run_pre_process_refinitiv_fxt_messages,
)
from pathlib import Path

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which has been created after parsing the source xml file
SOURCE_PICKLE_FILE = TEST_FILES_DIR.joinpath("source_frame_preprocess.pkl")
EXPECTED_RESULT_PICKLE_FILE = TEST_FILES_DIR.joinpath("expected_result_preprocess.pkl")


@pytest.fixture()
def source_frame():
    df = pd.read_pickle(SOURCE_PICKLE_FILE)
    return df


@pytest.fixture()
def expected_result():
    df = pd.read_pickle(EXPECTED_RESULT_PICKLE_FILE)
    return df


class TestPreProcessRefinitivFXTMessages:
    """Integration test for PreProcessRefinitivFXTMessages."""

    def test_empty_source_frame(self):
        with pytest.raises(SkipIfSourceFrameEmpty) as e:
            run_pre_process_refinitiv_fxt_messages(source_frame=pd.DataFrame())
        assert e.match("Messages source frame empty")

    def test_end_to_end(self, source_frame, expected_result):
        result = run_pre_process_refinitiv_fxt_messages(source_frame=source_frame)
        result = pd.read_pickle(result.pickle_path)
        assert not pd.testing.assert_frame_equal(  # type: ignore
            left=result, right=expected_result, check_dtype=False
        )
