# ruff: noqa: E501
import json
import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.message.slack_chat.slack_dataclasses import (
    ChannelInfo,
    SlackDataframes,
    SlackParserOutput,
)
from pathlib import Path

SCRIPT_PATH = Path(__file__).parent
DATA_PATH = SCRIPT_PATH.joinpath("data")
PICKLE_PATH = DATA_PATH.joinpath("pickles")
JSON_PATH = DATA_PATH.joinpath("json")

# --------------------------------------- Fixtures for slack_json_parser ---------------------------------------


@pytest.fixture()
def slack_messages_edits_deletes_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser Both messages and edits are non-empty."""
    file_path = JSON_PATH.joinpath("parser_input").joinpath("messages_edits_and_deletes.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_messages_formatting_files_gifs_chat_events_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser There are no edits, but there are messages with
    files attached, complex formatting, gifs and chat events."""
    file_path = JSON_PATH.joinpath("parser_input").joinpath(
        "messages_formatting_and_files_chat_events.json"
    )

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_mentions_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser There are no edits, there are only messages with
    mentions within them."""
    file_path = JSON_PATH.joinpath("parser_input").joinpath("mentions.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_bot_channel_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser There are no edits, there are only messsages,
    all of which are from a bot channel.

    In other words, there is a bot id rather than a message id for all
    messages
    """
    file_path = JSON_PATH.joinpath("parser_input").joinpath("bot_channel_messages.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_variety_of_chat_events_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser There are no edits, there are only messages.

    All of these messages are chat events of different types
    """
    file_path = JSON_PATH.joinpath("parser_input").joinpath("lots_of_chat_events.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_no_messages_only_delete_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser messages is empty, but there is one delete under
    edits[]"""
    file_path = JSON_PATH.joinpath("parser_input").joinpath("no_messages_only_delete.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_no_messages_only_edit_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser messages is empty, but there is one edit under
    edits[]"""
    file_path = JSON_PATH.joinpath("parser_input").joinpath("no_messages_only_edit.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_no_messages_only_deleted_files() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser messages is empty, but there are edits and
    deletes.

    All edits and deletes have deleted files
    """
    file_path = JSON_PATH.joinpath("parser_input").joinpath("deleted_files_in_elastic.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_parent_of_threads_deleted_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser messages and edits are both non-empty, and have
    messages/edits where the parent of a thread is deleted (either in or not in
    Elastic)"""
    file_path = JSON_PATH.joinpath("parser_input").joinpath("parent_of_thread_deleted.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_deleted_gifs_json() -> dict:
    """Dictionary with 4 keys 'messages', 'edits', 'info', 'members', used as
    input to slack_json_parser messages is empty, but there are 2 edits.

    Both these edits are deletes where a GIF is deleted
    """
    file_path = JSON_PATH.joinpath("parser_input").joinpath("deleted_gifs.json")

    with open(file_path, "r") as fp:
        json_content: dict = json.load(fp)

    return json_content


@pytest.fixture()
def slack_test_channel_info() -> ChannelInfo:
    """
    Used in all tests, this is a ChannelInfo instance giving info about
    a channel called 'slack-test'
    :return: ChannelInfo
    """
    return ChannelInfo(
        channel_id="C04DX1D9JLX",
        channel_name="slack-test",
        im_indicator=False,
        mpim_indicator=False,
        private_indicator=False,
    )


@pytest.fixture()
def messages_df_with_edited_messages() -> pd.DataFrame:
    """Messages df for the test with edits, deletes and new messages.

    This is one of the expected output dfs
    of SlackJsonParser, and one of the input dfs of SlackEditsAndDeletes.
    :return: pd.DataFrame
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": [
                    {"name": "greenlight", "users": ["U01PGFLH47Q"], "count": 1},
                    {"name": "boom", "users": ["U01PGFLH47Q"], "count": 1},
                ],
                "message_subtype": pd.NA,
                "message_text": "<@U01PGFLH47Q> just a mention",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237210.352089",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:20:10.352088Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "<https://steeleye.slack.com/archives/C04DX1D9JLX/p1672237210352089>\nQuote Reply test",
                "message_thread_ts": pd.NA,
                "message_ts": "1672821580.230529",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-01-04T08:39:40.230529Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": "C04DX1D9JLX|1672237210.352089",
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "test. Edited — mention removed.",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237152.847909",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:19:12.847908Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Yet another new message. Edit 1. Edit 2. Edit 3.",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237063.712929",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Another new message - <@U01PGFLH47Q> <!subteam^S01FNMD2BSA|@dataplatform> EDIT: mentions added.",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1672237053.342359",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:17:33.342359Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def multiple_edits_and_deletes_df() -> pd.DataFrame:
    """Edits and deletes df with multiple edits and deletes. This is one of the
    expected output dfs of SlackJsonParser, and one of the input dfs of
    SlackEditsAndDeletes.

    :return: pd.DataFrame
    """
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1670915267.000000",
                "message_text": "Deleting an edited message. EDIT",
                "message_original_ts": "1670915260.316689",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Deleting an edited message.",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-13T07:07:47.000000Z",
                "message_original_date_time": "2022-12-13T07:07:40.316688Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1670915270.000000",
                "message_text": pd.NA,
                "message_original_ts": "1670915260.316689",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Deleting an edited message. EDIT",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-13T07:07:50.000000Z",
                "message_original_date_time": "2022-12-13T07:07:40.316688Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1670915278.000000",
                "message_text": pd.NA,
                "message_original_ts": "1670915275.953489",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Normal delete test",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-13T07:07:58.000000Z",
                "message_original_date_time": "2022-12-13T07:07:55.953489Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672236964.000000",
                "message_text": pd.NA,
                "message_original_ts": "1671630400.056729",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "New message which will be deleted",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:16:04.000000Z",
                "message_original_date_time": "2022-12-21T13:46:40.056729Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237012.000000",
                "message_text": "Reply 2. Edited a threaded message",
                "message_original_ts": "1671630436.270169",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Reply 2",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:16:52.000000Z",
                "message_original_date_time": "2022-12-21T13:47:16.270169Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237072.000000",
                "message_text": "Yet another new message. Edit 1",
                "message_original_ts": "1672237063.712929",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Yet another new message.",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:17:52.000000Z",
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237079.000000",
                "message_text": "Yet another new message. Edit 1. Edit 2.",
                "message_original_ts": "1672237063.712929",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Yet another new message. Edit 1",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:17:59.000000Z",
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237086.000000",
                "message_text": "Yet another new message. Edit 1. Edit 2. Edit 3.",
                "message_original_ts": "1672237063.712929",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Yet another new message. Edit 1. Edit 2.",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:18:06.000000Z",
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237103.000000",
                "message_text": pd.NA,
                "message_original_ts": "1672237098.468469",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "I’m going to delete this.",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:18:23.000000Z",
                "message_original_date_time": "2022-12-28T14:18:18.468468Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237131.000000",
                "message_text": "New message. EDITED.",
                "message_original_ts": "1671630355.409639",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "New message",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:18:51.000000Z",
                "message_original_date_time": "2022-12-21T13:45:55.409638Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237171.000000",
                "message_text": "test. Edited — mention removed.",
                "message_original_ts": "1672237152.847909",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "<@U01PGFLH47Q> test.",
                "previous_mentions": ["U01PGFLH47Q"],
                "message_date_time": "2022-12-28T14:19:31.000000Z",
                "message_original_date_time": "2022-12-28T14:19:12.847908Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237194.000000",
                "message_text": "Another new message - <@U01PGFLH47Q> <!subteam^S01FNMD2BSA|@dataplatform> EDIT: mentions added.",  # noqa E501
                "message_original_ts": "1672237053.342359",
                "message_subtype": "message_changed",
                "mentions": ["U01PGFLH47Q"],
                "previous_files": pd.NA,
                "previous_text": "Another new message",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:19:54.000000Z",
                "message_original_date_time": "2022-12-28T14:17:33.342359Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_parse_slack_messages_edits_deletes(
    messages_df_with_edited_messages: pd.DataFrame,
    multiple_edits_and_deletes_df: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has multiple
    edits, deletes and new messages, but no chat events.

    :param messages_df_with_edited_messages: messages_df containing new messages
    :param multiple_edits_and_deletes_df: edits and deletes df
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=messages_df_with_edited_messages,
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=multiple_edits_and_deletes_df,
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def edits_df_with_single_delete() -> pd.DataFrame:
    """A data frame containing a single delete, required for testing
    SlackJsonParser with no messages and only a delete. This is one of the
    expected output dfs of SlackJsonParser.

    :return: pd.DataFrame
    """
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1671780645.000000",
                "message_text": pd.NA,
                "message_original_ts": "1670839985.065929",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Message 2",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-23T07:30:45.000000Z",
                "message_original_date_time": "2022-12-12T10:13:05.065928Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_parse_slack_single_delete_no_messages(
    edits_df_with_single_delete: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has a single
    delete and no messages/chat events.

    :param edits_df_with_single_delete: Edits df with 1 row
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=pd.DataFrame(),
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=edits_df_with_single_delete,
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def edits_df_with_single_edit() -> pd.DataFrame:
    """A data frame containing a single edit, required for testing
    SlackJsonParser with no messages and only an edit. This is one of the
    expected output dfs of SlackJsonParser.

    :return: pd.DataFrame
    """
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237072.000000",
                "message_text": "Yet another new message. Edit 1",
                "message_original_ts": "1672237063.712929",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Yet another new message.",
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:17:52.000000Z",
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_parse_slack_single_edit_no_messages(
    edits_df_with_single_edit: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has a single
    edit and no messages/chat events.

    :param edits_df_with_single_edit: Edits df with 1 row
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=pd.DataFrame(),
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=edits_df_with_single_edit,
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def messages_df_with_deleted_files() -> pd.DataFrame:
    """
    Returns a dataframe containing multiple
    :return:
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Text + 2 files",
                "message_thread_ts": pd.NA,
                "message_ts": "1677499415.404639",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-27T12:03:35.404638Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Text + 1 file",
                "message_thread_ts": pd.NA,
                "message_ts": "1677499401.790729",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-27T12:03:21.790728Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def edits_df_with_multiple_deleted_files() -> pd.DataFrame:
    """Returns a data frame containing multiple edits and deletes, required for
    testing SlackJsonParser with deleted files. This is one of the expected
    output dfs of SlackJsonParser.

    :return: pd.DataFrame
    """
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1672237072.000000",
                "message_text": "Yet another new message. Edit 1",
                "message_original_ts": "1672237063.712929",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": "Yet another new message.",
                "previous_files": pd.NA,
                "previous_mentions": pd.NA,
                "message_date_time": "2022-12-28T14:17:52.000000Z",
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067753.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991255.368469",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [
                    {"id": "F04Q532G0FR", "mode": "tombstone"},
                    {"id": "F04Q5330WMV", "mode": "tombstone"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:13.000000Z",
                "message_original_date_time": "2023-02-21T14:54:15.368468Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067758.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991255.368469",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [
                    {"id": "F04Q532G0FR", "mode": "tombstone"},
                    {"id": "F04Q5330WMV", "mode": "tombstone"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:18.000000Z",
                "message_original_date_time": "2023-02-21T14:54:15.368468Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067763.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991307.655509",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [
                    {"id": "F04QD18H38W", "mode": "tombstone"},
                    {"id": "F04QGLT0JN9", "mode": "snippet"},
                    {"id": "F04Q538T0MD", "mode": "tombstone"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:23.000000Z",
                "message_original_date_time": "2023-02-21T14:55:07.655508Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067767.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991307.655509",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [
                    {"id": "F04QD18H38W", "mode": "tombstone"},
                    {"id": "F04QGLT0JN9", "mode": "snippet"},
                    {"id": "F04Q538T0MD", "mode": "tombstone"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:27.000000Z",
                "message_original_date_time": "2023-02-21T14:55:07.655508Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067775.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991341.492359",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [
                    {"id": "F04QKK16J4S", "mode": "hosted"},
                    {"id": "F04QY8DLEAD", "mode": "snippet"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:35.000000Z",
                "message_original_date_time": "2023-02-21T14:55:41.492358Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067781.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991370.241829",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [{"id": "F04R9A3HP7A", "mode": "tombstone"}],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:41.000000Z",
                "message_original_date_time": "2023-02-21T14:56:10.241828Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067786.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991460.170589",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_text": pd.NA,
                "previous_files": [{"id": "F04QGMBG05T", "mode": "tombstone"}],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:46.000000Z",
                "message_original_date_time": "2023-02-21T14:57:40.170588Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067794.000000",
                "message_text": pd.NA,
                "message_original_ts": "1676991478.517679",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_text": "Add 2 files with text, delete the entire message",
                "previous_files": [
                    {"id": "F04QD1WKC4W", "mode": "snippet"},
                    {"id": "F04QD1WS8KY", "mode": "snippet"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:54.000000Z",
                "message_original_date_time": "2023-02-21T14:57:58.517679Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067799.000000",
                "message_text": "Add 2 files with text, delete 1 file",
                "message_original_ts": "1676991502.448499",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": "Add 2 files with text, delete 1 file",
                "previous_files": [
                    {"id": "F04QKG62QN7", "mode": "snippet"},
                    {"id": "F04QY8XEBPT", "mode": "tombstone"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:09:59.000000Z",
                "message_original_date_time": "2023-02-21T14:58:22.448498Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677067808.000000",
                "message_text": "Add 1 file with text, delete the file",
                "message_original_ts": "1676991522.925069",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": "Add 1 file with text, delete the file",
                "previous_files": [{"id": "F04QKG8KV99", "mode": "tombstone"}],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-22T12:10:08.000000Z",
                "message_original_date_time": "2023-02-21T14:58:42.925069Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677499419.000000",
                "message_text": "Text + 1 file",
                "message_original_ts": "1677499401.790729",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": "Text + 1 file",
                "previous_files": [{"id": "F04S3QFUH1N", "mode": "tombstone"}],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-27T12:03:39.000000Z",
                "message_original_date_time": "2023-02-27T12:03:21.790728Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1677499422.000000",
                "message_text": "Text + 2 files",
                "message_original_ts": "1677499415.404639",
                "message_subtype": "message_changed",
                "mentions": pd.NA,
                "previous_text": "Text + 2 files",
                "previous_files": [
                    {"id": "F04RSP0JJ8H", "mode": "tombstone"},
                    {"id": "F04RE0NC3R9", "mode": "hosted"},
                ],
                "previous_mentions": pd.NA,
                "message_date_time": "2023-02-27T12:03:42.000000Z",
                "message_original_date_time": "2023-02-27T12:03:35.404638Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_parse_slack_deleted_files(
    messages_df_with_deleted_files: pd.DataFrame,
    edits_df_with_multiple_deleted_files: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has multiple
    edits/deletes and no messages/chat events. Each edit/delete consists of
    deleted files.

    :param messages_df_with_deleted_files: Messages df containing messages in which
    files are deleted
    :param edits_df_with_multiple_deleted_files: Edits df containing messages with
    deleted files
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=messages_df_with_deleted_files,
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=edits_df_with_multiple_deleted_files,
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def messages_df_with_formatting_files_and_gifs() -> pd.DataFrame:
    """Messages df returned by the SlackJsonParser when there are multiple
    messages with complex formatting (bold, italics, strikethrough, blockquote
    etc), GIFs, and attached files."""
    return pd.DataFrame(
        [
            {
                "message_bot_id": "B4ZCTCGRM",
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Surprised",
                "message_thread_ts": pd.NA,
                "message_ts": "1670844557.255509",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g",  # noqa E501
                        "fileName": "Surprised_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 3369642,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Surprised_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 3369642,
                            "versionId": None,
                        },
                    }
                ],
                "message_date_time": "2022-12-12T11:29:17.255508Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": [{"name": "bang-head", "users": ["U02BKE4MRSN"], "count": 1}],
                "message_subtype": pd.NA,
                "message_text": pd.NA,
                "message_thread_ts": "1669723693.753359",
                "message_ts": "1669723693.753359",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "F04CWA4HUHZ",
                        "fileName": "Screenshot 2022-11-25 at 6.23.14 PM.png",
                        "fileType": ".png",
                        "sizeInBytes": 1207653,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Screenshot 2022-11-25 at 6.23.14 PM.png",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 1207653,
                            "versionId": None,
                        },
                        "mimeTag": "image/png",
                    }
                ],
                "message_date_time": "2022-11-29T12:08:13.753359Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "test 1",
                "message_thread_ts": pd.NA,
                "message_ts": "1669723678.216119",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "F04CWD5RCCS",
                        "fileName": "drawio-test.pdf",
                        "fileType": ".pdf",
                        "sizeInBytes": 64699,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/drawio-test.pdf",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 64699,
                            "versionId": None,
                        },
                        "mimeTag": "application/pdf",
                    }
                ],
                "message_date_time": "2022-11-29T12:07:58.216119Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Formatting Test\nNew _line._ Another *_sentence._ Yet another* one\n_*~Strike through,~ no strike though,*_ ~once again~ ",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1670245388.549839",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-05T13:03:08.549839Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "• ba\n• bas",
                "message_thread_ts": pd.NA,
                "message_ts": "1670005560.012279",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-02T18:26:00.012279Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "&gt; adsfadfsd",
                "message_thread_ts": pd.NA,
                "message_ts": "1670005538.973099",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-02T18:25:38.973099Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "<https://api.slack.com/enterprise/discovery/methods>",
                "message_thread_ts": pd.NA,
                "message_ts": "1670844786.475029",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-12T11:33:06.475028Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "<https://api.slack.com/enterprise/discovery/methods>",
                "message_thread_ts": pd.NA,
                "message_ts": "1670844786.475029",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-12T11:33:06.475028Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "1. asa\n2. asf\n",
                "message_thread_ts": pd.NA,
                "message_ts": "1670005523.079969",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-02T18:25:23.079969Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": ":wohoo:",
                "message_thread_ts": pd.NA,
                "message_ts": "1672996330.962909",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-01-06T09:12:10.962908Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def chat_events_for_formatted_messages() -> pd.DataFrame:
    """
    Chat events for the test with files attached, GIFs, formatting, chat events and
    :return: Data frame for chat events
    """
    return pd.DataFrame(
        [
            {
                "message_subtype": "channel_join",
                "message_text": "<@U02AMERNUFM> has joined the channel",
                "message_ts": "1670839953.584059",
                "message_user": "U02AMERNUFM",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2022-12-12T10:12:33.584058Z",
            },
            {
                "message_subtype": "channel_leave",
                "message_text": "<@U024XCUUY8Y> has left the channel",
                "message_ts": "1660839953.584012",
                "message_user": "U024XCUUY8Y",
                "to_ids": ["U01PGFLH47Q", "U02AMERNUFM"],
                "message_date_time": "2022-08-18T16:25:53.584012Z",
            },
        ]
    )


@pytest.fixture()
def expected_result_parse_slack_messages_with_formatting_gifs_files(
    messages_df_with_formatting_files_and_gifs: pd.DataFrame,
    chat_events_for_formatted_messages: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has multiple new
    messages with complex formatting, attached files, GIFs and chat events.
    There are no edits, however.

    :param messages_df_with_formatting_files_and_gifs: Messages df
    :param chat_events_for_formatted_messages: chat events df
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=messages_df_with_formatting_files_and_gifs,
        chat_events_df=chat_events_for_formatted_messages,
        edits_and_deletes_df=pd.DataFrame(),
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def slack_messages_df_for_mentions_test() -> pd.DataFrame:
    """Messages df returned by the SlackJsonParser when there are multiple
    mentions in each message."""
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "```cx```\n<@U01PGFLH47Q>\n&gt; sdfasdfs <@U01DFSQNYNB> \n1. <@UR71HD9UZ> <@U02BKE4MRSN> \n2. <@U03G5R5PJRM> \n• <@U01TDJ03WHW> <@U04LEV1U8AK> \n• <@U013W0HT6KW> <@U024XCUUY8Y> \n`@here @storey` ",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1676646726.765829",
                "message_user": "U01PGFLH47Q",
                "mentions": [
                    "UR71HD9UZ",
                    "U02BKE4MRSN",
                    "U04LEV1U8AK",
                    "U024XCUUY8Y",
                    "U01TDJ03WHW",
                    "U01PGFLH47Q",
                    "U013W0HT6KW",
                    "U03G5R5PJRM",
                    "U01DFSQNYNB",
                ],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T15:12:06.765829Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Formatting Test\nNew _line._ Another *_sentence <!here>._ Yet another <@U023KEU40SZ>* *<@U01AWB8GG1F>* one <@U024XCUUY8Y>\n~_*Strike through,  <@U8X0K6K7E>*_~ _*no strike though <@U03S4FD0KU1>,*_ ~once again~",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1676643173.987229",
                "message_user": "U01PGFLH47Q",
                "mentions": [
                    "U8X0K6K7E",
                    "U024XCUUY8Y",
                    "U01AWB8GG1F",
                    "U023KEU40SZ",
                    "U03S4FD0KU1",
                ],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:12:53.987229Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "• <@U01PGFLH47Q> <@UR71HD9UZ> <@U02BKE4MRSN> \n• who else should I include <@U4PA3EVJT> <@UGH78B1PG> <@UUKKV7332> \n• nobody here",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1676643084.608199",
                "message_user": "U01PGFLH47Q",
                "mentions": [
                    "UR71HD9UZ",
                    "U02BKE4MRSN",
                    "UGH78B1PG",
                    "UUKKV7332",
                    "U4PA3EVJT",
                    "U01PGFLH47Q",
                ],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:11:24.608198Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "1. <@U01DFSQNYNB> \n2. <@U03KBQ0PP5Z> and <@U03SLP52804> \n3. <@U04LEV1U8AK> ",
                "message_thread_ts": pd.NA,
                "message_ts": "1676642570.429039",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U03KBQ0PP5Z", "U03SLP52804", "U01DFSQNYNB", "U04LEV1U8AK"],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:02:50.429039Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "`@user1`  <@U03G5R5PJRM>",
                "message_thread_ts": pd.NA,
                "message_ts": "1676642531.113579",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U03G5R5PJRM"],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:02:11.113579Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "```@user1 @user2 code```",
                "message_thread_ts": pd.NA,
                "message_ts": "1676642497.419389",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:01:37.419388Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "&gt; <@U01PGFLH47Q> <@UR71HD9UZ> test block quote",
                "message_thread_ts": pd.NA,
                "message_ts": "1676642480.433069",
                "message_user": "U01PGFLH47Q",
                "mentions": ["UR71HD9UZ", "U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:01:20.433069Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Normal mentions <@U01PGFLH47Q> <@U01AMPACW4Q> <@U01TDJ03WHW> _adfjs <@U01DFSQNYNB>_ ",
                "message_thread_ts": pd.NA,
                "message_ts": "1676642427.042019",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01DFSQNYNB", "U01AMPACW4Q", "U01TDJ03WHW", "U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-17T14:00:27.042018Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_mentions_test(
    slack_messages_df_for_mentions_test: pd.DataFrame, slack_test_channel_info: ChannelInfo
) -> SlackParserOutput:
    """Expected result for SlackJsonParser for a complex mentions test.

    :param slack_messages_df_for_mentions_test: Messages df for the mentions test
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=slack_messages_df_for_mentions_test,
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=pd.DataFrame(),
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def parser_output_variety_of_chat_events_df() -> pd.DataFrame:
    """
    Chat events df for multiple chat events (joined, left, description changed, name changed,
    archive, unarchive)
    :return:
    """
    return pd.DataFrame(
        [
            {
                "message_subtype": "channel_join",
                "message_text": "<@U01PGFLH47Q> has joined the channel",
                "message_ts": "1677658196.420689",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:09:56.420689Z",
            },
            {
                "message_subtype": "channel_leave",
                "message_text": "<@U01PGFLH47Q> has left the channel",
                "message_ts": "1677658188.011489",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:09:48.011489Z",
            },
            {
                "message_subtype": "channel_unarchive",
                "message_text": "un-archived the channel",
                "message_ts": "1677657982.074129",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:06:22.074129Z",
            },
            {
                "message_subtype": "channel_join",
                "message_text": "<@U01PGFLH47Q> has joined the channel",
                "message_ts": "1677657982.032839",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:06:22.032839Z",
            },
            {
                "message_subtype": "channel_archive",
                "message_text": "archived the channel",
                "message_ts": "1677657955.987359",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:05:55.987358Z",
            },
            {
                "message_subtype": "channel_name",
                "message_text": 'has renamed the channel from "slack-deleted-files-tests" to "slack-more-tests"',
                "message_ts": "1677657922.603019",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:05:22.603019Z",
            },
            {
                "message_subtype": "channel_purpose",
                "message_text": "set the channel description: Testing stuff using the Slack API. Initially created for testing deleted files.",  # noqa E501
                "message_ts": "1677657665.620079",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:01:05.620079Z",
            },
            {
                "message_subtype": "channel_topic",
                "message_text": "set the channel topic: A second Slack channels for tests. 1st one: slack_test",
                "message_ts": "1677657642.994209",
                "message_user": "U01PGFLH47Q",
                "to_ids": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:00:42.994209Z",
            },
        ]
    )


@pytest.fixture()
def expected_result_parser_chat_events(
    parser_output_variety_of_chat_events_df: pd.DataFrame, slack_test_channel_info: ChannelInfo
) -> SlackParserOutput:
    """Expected result for SlackJsonParser for the case where there are
    multiple chat events of different types.

    :param parser_output_variety_of_chat_events_df: Chat events df with different types of events
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=pd.DataFrame(),
        chat_events_df=parser_output_variety_of_chat_events_df,
        edits_and_deletes_df=pd.DataFrame(),
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def bot_messages_df() -> pd.DataFrame:
    """
    Dataframe returned by SlackJsonParser for the case where all the messages are bot messages
    :return: Dataframe with bot messages
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": "B013H935ZRP",
                "message_reactions": pd.NA,
                "message_subtype": "bot_message",
                "message_text": "Flow Runner Failed",
                "message_thread_ts": pd.NA,
                "message_ts": "1677552313.074649",
                "message_user": pd.NA,
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-28T02:45:13.074649Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": "B013H935ZRP",
                "message_reactions": pd.NA,
                "message_subtype": "bot_message",
                "message_text": "Flow Runner Failed",
                "message_thread_ts": pd.NA,
                "message_ts": "1677229894.727219",
                "message_user": pd.NA,
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-24T09:11:34.727219Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": "B03NJUL7XJ9",
                "message_reactions": pd.NA,
                "message_subtype": "bot_message",
                "message_text": "<@U01PGFLH47Q> got heads :arrow_up:",
                "message_thread_ts": pd.NA,
                "message_ts": "1677657822.669129",
                "message_user": pd.NA,
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-01T08:03:42.669128Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_parser_bot_messages(
    bot_messages_df: pd.DataFrame, slack_test_channel_info: ChannelInfo
) -> SlackParserOutput:
    """Expected result for SlackJsonParser for the case where there bot
    messages and no edits.

    :param bot_messages_df: Messages df with bot messages
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=bot_messages_df,
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=pd.DataFrame(),
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def messages_df_thread_parent_deleted() -> pd.DataFrame:
    """Messages Dataframe returned by SlackJsonParser for the case where
    parents of threads are deleted, both in the current and previous
    batches."""
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Reply 32",
                "message_thread_ts": "1678360465.109019",
                "message_ts": "1678360472.477299",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-09T11:14:32.477298Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Reply 31",
                "message_thread_ts": "1678360465.109019",
                "message_ts": "1678360470.004459",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-09T11:14:30.004459Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
            },
        ]
    )


@pytest.fixture()
def edits_and_deletes_thread_parent_deleted() -> pd.DataFrame:
    """Edits and deletes Dataframe returned by SlackJsonParser for the case
    where parents of threads are deleted, both in the current and previous
    batches."""
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1678360452.000000",
                "message_text": pd.NA,
                "message_original_ts": "1678353506.007819",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Thread 1",
                "previous_mentions": pd.NA,
                "message_date_time": "2023-03-09T11:14:12.000000Z",
                "message_original_date_time": "2023-03-09T09:18:26.007819Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1678360485.000000",
                "message_text": pd.NA,
                "message_original_ts": "1678360465.109019",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "New thread",
                "previous_mentions": pd.NA,
                "message_date_time": "2023-03-09T11:14:45.000000Z",
                "message_original_date_time": "2023-03-09T11:14:25.109019Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": "1678360465.109019",
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_json_parser_delete_thread_parent(
    messages_df_thread_parent_deleted: pd.DataFrame,
    edits_and_deletes_thread_parent_deleted: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has cases where
    parent messages of threads are deleted, both in the current and previous
    batches.

    :param messages_df_thread_parent_deleted: messages_df containing a new thread with 3 messages, in
    which the parent is deleted
    :param edits_and_deletes_thread_parent_deleted: edits and deletes df with parents of a current
    thread and an older thread deleted
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=messages_df_thread_parent_deleted,
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=edits_and_deletes_thread_parent_deleted,
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def edits_and_deletes_df_deleted_gifs() -> pd.DataFrame:
    """
    Returns the edits_and_deletes_df for SlackJsonPareser for the case where there are 2 GIFs
    deleted
    :return:
    """
    return pd.DataFrame(
        [
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1678432913.000000",
                "message_text": pd.NA,
                "message_original_ts": "1670844557.255509",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Surprised",
                "previous_mentions": pd.NA,
                "previous_external_attachments": [
                    {
                        "fileName": "Surprised_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 3369642,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Surprised_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 3369642,
                            "versionId": None,
                        },
                        "fileId": "https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g",  # noqa E501
                        "isDeleted": True,
                    }
                ],
                "message_date_time": "2023-03-10T07:21:53.000000Z",
                "message_original_date_time": "2022-12-12T11:29:17.255508Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
            },
            {
                "message_user": "U01PGFLH47Q",
                "message_ts": "1678471198.000000",
                "message_text": pd.NA,
                "message_original_ts": "1678432838.479629",
                "message_subtype": "message_deleted",
                "mentions": pd.NA,
                "previous_files": pd.NA,
                "previous_text": "Dog",
                "previous_mentions": pd.NA,
                "previous_external_attachments": [
                    {
                        "fileName": "Dog_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 5031568,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Dog_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 5031568,
                            "versionId": None,
                        },
                        "fileId": "https://media1.giphy.com/media/kiBcwEXegBTACmVOnE/giphy-downsized.gif?cid=6104955eb9w9ytvh3p5y0nil0cfwyl3oer6mebsvl8h73xvf&rid=giphy-downsized.gif&ct=g",  # noqa E501
                        "isDeleted": True,
                    }
                ],
                "message_date_time": "2023-03-10T17:59:58.000000Z",
                "message_original_date_time": "2023-03-10T07:20:38.479629Z",
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_json_parser_deleted_gifs(
    edits_and_deletes_df_deleted_gifs: pd.DataFrame,
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Expected result for SlackJsonParser when the input file has 2 GIFs which
    have been deleted.

    :param edits_and_deletes_df_deleted_gifs: edits_and_deletes_df containing 2 deleted GIFs
    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=pd.DataFrame(),
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=edits_and_deletes_df_deleted_gifs,
        channel_info=slack_test_channel_info,
    )


# ------------------------------------Fixtures for SlackEditsAndDeletes------------------------------------


@pytest.fixture()
def messages_to_create_df_edits_and_deletes_output():
    """
    Output messages_to_create_df when there are multiple edits and deletes both in
    Elasticsearch and in the current df
    :return:
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": [
                    {"name": "greenlight", "users": ["U01PGFLH47Q"], "count": 1},
                    {"name": "boom", "users": ["U01PGFLH47Q"], "count": 1},
                ],
                "message_subtype": pd.NA,
                "message_text": "<@U01PGFLH47Q> just a mention",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237210.352089",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:20:10.352088Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "message_original_date_time": pd.NA,
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "<https://steeleye.slack.com/archives/C04DX1D9JLX/p1672237210352089>\nQuote Reply test",
                "message_thread_ts": pd.NA,
                "message_ts": "1672821580.230529",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-01-04T08:39:40.230529Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": "C04DX1D9JLX|1672237210.352089",
                "edits": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "message_original_date_time": pd.NA,
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "test. Edited — mention removed.",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237152.847909",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:19:31.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "<@U01PGFLH47Q> test.",
                        "timestampEdited": "2022-12-28T14:19:12.847908Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:19:12.847908Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Yet another new message. Edit 1. Edit 2. Edit 3.",
                "message_thread_ts": pd.NA,
                "message_ts": "1672237063.712929",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:18:06.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message.",
                        "timestampEdited": "2022-12-28T14:17:43.712929Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message. Edit 1",
                        "timestampEdited": "2022-12-28T14:17:52.000000Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message. Edit 1. Edit 2.",
                        "timestampEdited": "2022-12-28T14:17:59.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Another new message - <@U01PGFLH47Q> <!subteam^S01FNMD2BSA|@dataplatform> EDIT: mentions added.",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_ts": "1672237053.342359",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-28T14:19:54.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Another new message",
                        "timestampEdited": "2022-12-28T14:17:33.342359Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:17:33.342359Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_ts": "1670915260.316689",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-13T07:07:50.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleting an edited message.",
                        "timestampEdited": "2022-12-13T07:07:40.316688Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleting an edited message. EDIT",
                        "timestampEdited": "2022-12-13T07:07:47.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "is_deleted": True,
                "is_edited": True,
                "message_original_date_time": "2022-12-13T07:07:40.316688Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_ts": "1670915275.953489",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2022-12-13T07:07:58.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Normal delete test",
                        "timestampEdited": "2022-12-13T07:07:55.953489Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2022-12-13T07:07:55.953489Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def messages_to_update_df_edits_and_deletes_output():
    """
    Output update_df when 4 records are fetched from Elastic and updated
    (either deleted or edited)
    :return:
    """
    return pd.DataFrame(
        [
            {
                # Message in Elastic which has now been deleted. This message already had body.edits[].
                # 'Deleting' this message sets text to pd.NA and appends to the body.edits[] list.
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1671630400.056729",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-28T14:16:04.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:16:04.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message",
                        "timestampEdited": "2022-12-28T14:18:51.000000Z",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message which will be deleted",
                        "timestampEdited": "2022-08-09T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            },
            # Message in Elastic which has now been deleted
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1672237098.468469",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-28T14:18:23.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:18:23.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "I’m going to delete this.",
                        "timestampEdited": "2022-08-09T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            },
            # Message in Elastic which has been edited. This is the first edit for this message.
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1671630355.409639",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-28T14:18:51.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:18:51.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "New message. EDITED.",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            },
            # Message in Elastic which has been edited. This already has existing edits
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1671630436.270169",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-28T14:16:52.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:16:52.000000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "Reply 2. Edited a threaded message",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Reply 1",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Reply 2",
                        "timestampEdited": "2022-12-27T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            },
        ]
    )


@pytest.fixture()
def messages_from_elasticsearch_for_multiple_edits_and_deletes() -> pd.DataFrame:
    """Mock for records from Elastic for multiple edits and deletes.

    This is used to get the expected
    result expected_result_slack_edits_and_deletes_for_edits_deletes_and_new_messages
    :return: Dataframe containing mock elastic results
    """
    return pd.DataFrame(
        [
            # Record 1: present in Elastic, will be set to isDeleted=True as it is deleted, and is already in ES
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1672237098.468469",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-08-09T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-08-09T23:39:19.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "I'm going to delete this",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
            },
            # Record 2: Delete when body.edits already exists
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1671630400.056729",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-08-09T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-08-09T23:39:19.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "New message which will be deleted",
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message",
                        "timestampEdited": "2022-12-28T14:18:51.000000Z",
                    }
                ],
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
            },
            # Record 3: edit where the Elastic record already has body.edits[] populated
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1671630436.270169",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-27T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-12-27T23:39:19.000000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "Reply 2",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Reply 1",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    }
                ],
            },
            # Record 4: edit where the original message hasn't been edited before
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1671630355.409639",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-21T13:45:55.000000Z",
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "New message",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_edits_deletes_and_new_messages(
    messages_to_update_df_edits_and_deletes_output: pd.DataFrame,
    messages_to_create_df_edits_and_deletes_output: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df has multiple
    edits and deletes, both in the current file and in Elastic.

    :param messages_to_update_df_edits_and_deletes_output: expected df for update records, contains
    complete Elastic records as per the schema
    :param messages_to_create_df_edits_and_deletes_output: messages in current file -- create and
    deletes. Edits are joined to the new message records. This is ALSO used as input to the message
    mappings task
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_edits_and_deletes_output,
        messages_to_update_df=messages_to_update_df_edits_and_deletes_output,
    )


@pytest.fixture()
def messages_to_create_df_for_formatting_files_gifs():
    """Output messages_to_create_df when there are ZERO edits/deletes and only
    only new messages.

    This is also used as input to the mappings test.
    :return: Data frame with new messages
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": "B4ZCTCGRM",
                "message_ts": "1670844557.255509",
                "message_text": "Surprised",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g",  # noqa E501,
                        "fileName": "Surprised_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 3369642,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Surprised_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 3369642,
                            "versionId": None,
                        },
                    }
                ],
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-12T11:29:17.255508Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1669723693.753359",
                "message_text": pd.NA,
                "message_thread_ts": "1669723693.753359",
                "message_subtype": pd.NA,
                "message_reactions": [{"name": "bang-head", "users": ["U02BKE4MRSN"], "count": 1}],
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "F04CWA4HUHZ",
                        "fileName": "Screenshot 2022-11-25 at 6.23.14 PM.png",
                        "fileType": ".png",
                        "sizeInBytes": 1207653,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Screenshot 2022-11-25 at 6.23.14 PM.png",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 1207653,
                            "versionId": None,
                        },
                        "mimeTag": "image/png",
                    }
                ],
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-11-29T12:08:13.753359Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1669723678.216119",
                "message_text": "test 1",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": [
                    {
                        "fileId": "F04CWD5RCCS",
                        "fileName": "drawio-test.pdf",
                        "fileType": ".pdf",
                        "sizeInBytes": 64699,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/drawio-test.pdf",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 64699,
                            "versionId": None,
                        },
                        "mimeTag": "application/pdf",
                    }
                ],
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-11-29T12:07:58.216119Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670245388.549839",
                "message_text": "Formatting Test\nNew _line._ Another *_sentence._ Yet another* one\n_*~Strike through,~ no strike though,*_ ~once again~ ",  # noqa E501
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-05T13:03:08.549839Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670005560.012279",
                "message_text": "• ba\n• bas",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-02T18:26:00.012279Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670005538.973099",
                "message_text": "&gt; adsfadfsd",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-02T18:25:38.973099Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670844786.475029",
                "message_text": "<https://api.slack.com/enterprise/discovery/methods>",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-12T11:33:06.475028Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670844786.475029",
                "message_text": "<https://api.slack.com/enterprise/discovery/methods>",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-12T11:33:06.475028Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670005523.079969",
                "message_text": "1. asa\n2. asf\n",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2022-12-02T18:25:23.079969Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_ts": "1672996330.962909",
                "message_text": ":wohoo:",
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2023-01-06T09:12:10.962908Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_messages_no_edits_deletes(
    messages_to_create_df_for_formatting_files_gifs: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df has multiple
    edits and deletes, both in the current file and in Elastic.

    :param messages_to_create_df_for_formatting_files_gifs: messages in current file -- create and
    deletes. Edits are joined to the new message records. This is ALSO used as input to the message
    mappings task
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_for_formatting_files_gifs,
        messages_to_update_df=pd.DataFrame(),
    )


@pytest.fixture()
def messages_to_update_df_single_delete_which_is_in_elastic() -> pd.DataFrame:
    """messages_to_update_df when there is a single delete which is found in
    Elastic.

    There are
    no edits
    :return: Updates dataframe
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "644702a2fbad79427c18ede5d5b057f48151ef8e51d6a7d2ce67a63df77b85a"
                "64b2a042a65c956407fa7b28f0e671b970ceed9ee12f09707d9f091cadfd0c177",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1670839985.065929",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-23T07:30:45.000000Z",
                "timestamps.timestampStart": "2022-12-23T07:30:45.000000Z",
                "timestamps.created": "2022-12-12T10:13:05.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Message 2",
                        "timestampEdited": "2022-12-12T10:13:05.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            }
        ]
    )


@pytest.fixture()
def messages_from_elasticsearch_for_single_delete():
    """Mock for records from Elastic for a single delete.

    This is used to get the expected
    results in expected_result_slack_edits_and_deletes_for_delete_in_elastic_no_messages
    :return:
    """
    return pd.DataFrame(
        [
            # Record 1: present in Elastic, will be set to isDeleted=True as it is deleted, and is already in ES
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "644702a2fbad79427c18ede5d5b057f48151ef8e51d6a7d2ce67a63df77b85a"
                "64b2a042a65c956407fa7b28f0e671b970ceed9ee12f09707d9f091cadfd0c177",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1670839985.065929",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-12T10:13:05.000000Z",
                "timestamps.timestampStart": "2022-12-12T10:13:05.000000Z",
                "timestamps.created": "2022-12-12T10:13:05.000000Z",
                "body.text": "Message 2",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_delete_in_elastic_no_messages(
    messages_to_update_df_single_delete_which_is_in_elastic: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df has a single
    delete.

    :param messages_to_update_df_single_delete_which_is_in_elastic: expected df for update records, contains
    1 complete Elastic record as per the schema
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=pd.DataFrame(),
        messages_to_update_df=messages_to_update_df_single_delete_which_is_in_elastic,
    )


@pytest.fixture()
def messages_to_create_df_single_delete_not_in_elastic() -> pd.DataFrame:
    """messages_to_create_df when there is a single delete which is NOT found
    in Elastic.

    There are
    no edits.
    :return: Creates dataframe
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670839985.065929",
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Message 2",
                        "timestampEdited": "2022-12-12T10:13:05.065928Z",
                        "type": "MARKDOWN",
                    }
                ],
                "file_attachments": pd.NA,
                "is_deleted": True,
                "is_edited": False,
                "mentions": pd.NA,
                "message_date_time": "2022-12-23T07:30:45.000000Z",
                "message_original_date_time": "2022-12-12T10:13:05.065928Z",
                "reply_to_id": pd.NA,
                "to_ids": ["U02AMERNUFM"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_delete_not_in_elastic_no_messages(
    messages_to_create_df_single_delete_not_in_elastic: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df has a single
    delete.

    :param messages_to_create_df_single_delete_not_in_elastic: expected df for create records
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_single_delete_not_in_elastic,
        messages_to_update_df=pd.DataFrame(),
    )


@pytest.fixture()
def messages_to_update_df_single_edit_in_elastic(
    expected_result_parse_slack_single_edit_no_messages: SlackParserOutput,
):
    """messages_to_update_df when there is a single edit which is found in
    Elastic.

    There are
    no deletes.
    :return: Updates dataframe
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "452e2c44ff02cc9e444b7e4ddcd18f1763a2ce1c4444a195f55ed4511ec"
                "13bbdb7af620ddd5df04d05aa00092c54fa9f30df23ebfc343e5daad3c6cb26503ded",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1672237063.712929",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2022-12-28T14:17:52.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:17:52.000000Z",
                "timestamps.created": "2022-12-28T14:17:52.000000Z",
                "body.text": "Yet another new message. Edit 1",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message.",
                        "timestampEdited": "2022-12-28T14:17:52.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": pd.NA,
                "identifiers.mentionedIds": pd.NA,
            }
        ]
    )


@pytest.fixture()
def messages_from_elasticsearch_for_single_edit():
    """Mock for records from Elastic for a single edit.

    This is used to get the expected
    results in expected_result_slack_edits_and_deletes_for_edit_in_elastic_no_messages
    :return:
    """
    return pd.DataFrame(
        [
            # Record 1: present in Elastic, will be set to isDeleted=True as it is deleted, and is already in ES
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "452e2c44ff02cc9e444b7e4ddcd18f1763a2ce1c4444a195f55ed4511ec"
                "13bbdb7af620ddd5df04d05aa00092c54fa9f30df23ebfc343e5daad3c6cb26503ded",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1672237063.712929",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-28T14:17:52.000000Z",
                "timestamps.timestampStart": "2022-12-28T14:17:52.000000Z",
                "timestamps.created": "2022-12-28T14:17:52.000000Z",
                "body.text": "Yet another new message",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_edit_in_elastic_no_messages(
    messages_to_update_df_single_edit_in_elastic: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df has a single
    edit which is in ELastic.

    :param messages_to_update_df_single_edit_in_elastic: expected df for update records, contains
    1 complete Elastic record as per the schema
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=pd.DataFrame(),
        messages_to_update_df=messages_to_update_df_single_edit_in_elastic,
    )


@pytest.fixture()
def empty_messages_and_edits_input_edits_and_deletes(
    slack_test_channel_info: ChannelInfo,
) -> SlackParserOutput:
    """Invalid input to SlackEditsAndDeletes. Both messages_df and
    edits_and_deletes_df are empty.

    :param slack_test_channel_info: ChannelInfo instance
    :return: SlackParserOutput
    """
    return SlackParserOutput(
        messages_df=pd.DataFrame(),
        chat_events_df=pd.DataFrame(),
        edits_and_deletes_df=pd.DataFrame(),
        channel_info=slack_test_channel_info,
    )


@pytest.fixture()
def messages_to_create_df_deleted_files_not_in_elastic() -> pd.DataFrame:
    """messages_to_create_df when there is a single deleted message with a
    deleted file which is NOT found in Elastic.

    There are also 2 edits where there are deleted files.
    no edits.
    :return: Creates dataframe
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Text + 2 files",
                "message_thread_ts": pd.NA,
                "message_ts": "1677499415.404639",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-27T12:03:42.000000Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Text + 2 files | Deleted file(s): ['F04RSP0JJ8H']",
                        "timestampEdited": "2023-02-27T12:03:35.404638Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2023-02-27T12:03:35.404638Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Text + 1 file",
                "message_thread_ts": pd.NA,
                "message_ts": "1677499401.790729",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-27T12:03:39.000000Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Text + 1 file | Deleted file(s): ['F04S3QFUH1N']",
                        "timestampEdited": "2023-02-27T12:03:21.790728Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2023-02-27T12:03:21.790728Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_ts": "1676991460.170589",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-02-22T12:09:46.000000Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s): ['F04QGMBG05T']",
                        "timestampEdited": "2023-02-21T14:57:40.170588Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2023-02-21T14:57:40.170588Z",
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": "message_changed",
                "message_text": "Yet another new message. Edit 1",
                "message_thread_ts": None,
                "message_ts": "1672237063.712929",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:17:52.000000Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message.",
                        "timestampEdited": "2022-12-28T14:17:43.712929Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
        ]
    )


@pytest.fixture()
def messages_to_update_df_deleted_files_in_elastic() -> pd.DataFrame:
    """messages_to_create_df when there is a single deleted message with a
    deleted file which is NOT found in Elastic.

    There are
    no edits.
    :return: Creates dataframe
    """
    return pd.DataFrame(
        [
            {
                # Record 1
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991255.368469",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:18.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:18.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": True,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04Q532G0FR', 'F04Q5330WMV']",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04Q532G0FR', 'F04Q5330WMV']",
                        "timestampEdited": "2023-02-22T12:09:13.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "attachments": [
                    {
                        "fileId": "F04Q532G0FR",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/w.pdf",
                            },
                        },
                        "fileName": "w.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                    {
                        "fileId": "F04Q5330WMV",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/v.pdf",
                            },
                        },
                        "fileName": "v.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 2
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991341.492359",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:35.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:35.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04QKK16J4S', 'F04QY8DLEAD']",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": [
                    {
                        "fileId": "F04QKK16J4S",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/h.pdf",
                            },
                        },
                        "fileName": "h.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                    {
                        "fileId": "F04QY8DLEAD",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/i.pdf",
                            },
                        },
                        "fileName": "i.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 3
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991370.241829",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:41.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:41.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04R9A3HP7A']",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": [
                    {
                        "fileId": "F04R9A3HP7A",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/g.pdf",
                            },
                        },
                        "fileName": "g.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    }
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 4
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991478.517679",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:54.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:54.000000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Add 2 files with text, delete the entire message | Deleted file(s):['F04QD1WKC4W', 'F04QD1WS8KY']",  # noqa E501
                        "timestampEdited": "2022-12-27T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": [
                    {
                        "fileId": "F04QD1WKC4W",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/c.pdf",
                            },
                        },
                        "fileName": "c.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                    {
                        "fileId": "F04QD1WS8KY",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/d.pdf",
                            },
                        },
                        "fileName": "d.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 5
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991307.655509",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:27.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:27.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04Q538T0MD', 'F04QD18H38W']",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleted file(s):['F04Q538T0MD', 'F04QD18H38W']",
                        "timestampEdited": "2023-02-22T12:09:23.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "attachments": [
                    {
                        "fileId": "F04QD18H38W",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/x.pdf",
                            },
                        },
                        "fileName": "x.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                    {
                        "fileId": "F04QGLT0JN9",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/y.pdf",
                            },
                        },
                        "fileName": "y.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04Q538T0MD",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/z.pdf",
                            },
                        },
                        "fileName": "z.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 6
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "C04DX1D9JLX|1676991502.448499",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:09:59.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:09:59.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "Add 2 files with text, delete 1 file",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Add 2 files with text, delete 1 file | Deleted file(s):['F04QY8XEBPT']",
                        "timestampEdited": "2022-08-09T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": [
                    {
                        "fileId": "F04QKG62QN7",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/a.pdf",
                            },
                        },
                        "fileName": "a.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04QY8XEBPT",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/b.pdf",
                            },
                        },
                        "fileName": "b.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    },
                ],
                "identifiers.mentionedIds": pd.NA,
            },
            # Record 7
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": True,
                "metadata.messageId": "C04DX1D9JLX|1676991522.925069",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "U02AMERNUFM"}],
                "timestamps.timestampEnd": "2023-02-22T12:10:08.000000Z",
                "timestamps.timestampStart": "2023-02-22T12:10:08.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "Add 1 file with text, delete the file",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Add 1 file with text, delete the file | Deleted file(s):['F04QKG8KV99']",
                        "timestampEdited": "2022-08-09T23:39:19.000000Z",
                        "type": "MARKDOWN",
                    }
                ],
                "attachments": [
                    {
                        "fileId": "F04QKG8KV99",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/drawio-test.pdf",
                            },
                        },
                        "fileName": "drawio-test.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                        "isDeleted": True,
                    }
                ],
                "identifiers.mentionedIds": pd.NA,
            },
        ]
    )


@pytest.fixture()
def messages_from_elasticsearch_for_deleted_files():
    """Mock for records from Elastic for deleted files.

    This Elastic data frame is used to get
    the results in expected_result_slack_edits_and_deletes_for_deleted_files
    :return:
    """
    return pd.DataFrame(
        [
            # Record 1: 1 file with text. Only file is deleted
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": True,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991522.925069",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-08-09T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-08-09T23:39:19.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "Add 1 file with text, delete the file",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
                "attachments": [
                    {
                        "fileId": "F04QKG8KV99",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/drawio-test.pdf",
                            },
                        },
                        "fileName": "drawio-test.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    }
                ],
            },
            # Record 2: 2 files with text. Delete one file
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991502.448499",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-08-09T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-08-09T23:39:19.000000Z",
                "timestamps.created": "2022-08-09T23:39:19.000000Z",
                "body.text": "Add 2 files with text, delete 1 file",
                "body.edits": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "attachments": [
                    {
                        "fileId": "F04QKG62QN7",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/a.pdf",
                            },
                        },
                        "fileName": "a.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04QY8XEBPT",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/b.pdf",
                            },
                        },
                        "fileName": "b.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
            # Record 3: 2 files exist with text, entire message deleted
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991478.517679",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-27T23:39:19.000000Z",
                "timestamps.timestampStart": "2022-12-27T23:39:19.000000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "Add 2 files with text, delete the entire message",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "attachments": [
                    {
                        "fileId": "F04QD1WKC4W",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/c.pdf",
                            },
                        },
                        "fileName": "c.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04QD1WS8KY",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/d.pdf",
                            },
                        },
                        "fileName": "d.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
            # Record 4: Delete file/message when there is one file without any text
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991370.241829",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-21T13:45:55.000000Z",
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
                "attachments": [
                    {
                        "fileId": "F04R9A3HP7A",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/g.pdf",
                            },
                        },
                        "fileName": "g.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
            # Record 5: delete message when there are 2 files and no text
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991341.492359",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-21T13:45:55.000000Z",
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
                "attachments": [
                    {
                        "fileId": "F04QKK16J4S",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/h.pdf",
                            },
                        },
                        "fileName": "h.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04QY8DLEAD",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/i.pdf",
                            },
                        },
                        "fileName": "i.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
            # Record 6: delete 2 files when there are 3 files and no text
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991307.655509",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-21T13:45:55.000000Z",
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
                "attachments": [
                    {
                        "fileId": "F04QD18H38W",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/x.pdf",
                            },
                        },
                        "fileName": "x.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04QGLT0JN9",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/y.pdf",
                            },
                        },
                        "fileName": "y.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04Q538T0MD",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/z.pdf",
                            },
                        },
                        "fileName": "z.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
            # Record 7: delete 2 files one at a time when there is no text
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "&id": "e835970e75e278a22de584e4cdc30b0c38fe64",
                "&uniqueProps": ["U01PGFLH47Q", "U02AMERNUFM"],
                "&key": "Message:e835970e75e278a22de584e4cdc30b0c38fe64429efb8d3461df03f0952d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "81a2e9deb2d33fb65ab72dff7d46f827bba155674390348992efecd6dad00866",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "C04DX1D9JLX|1676991255.368469",
                "metadata.source.client": "Slack - Chat",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["U01PGFLH47Q", "U02AMERNUFM"],
                "identifiers.fromId": "U01PGFLH47Q",
                "identifiers.fromIdAddlInfo.raw": "U01PGFLH47Q",
                "identifiers.toIds": ["U02AMERNUFM"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "U02AMERNUFM"},
                ],
                "timestamps.timestampEnd": "2022-12-21T13:45:55.000000Z",
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": pd.NA,
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "body.edits": pd.NA,
                "attachments": [
                    {
                        "fileId": "F04Q532G0FR",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/w.pdf",
                            },
                        },
                        "fileName": "w.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                    {
                        "fileId": "F04Q5330WMV",
                        "fileInfo": {
                            "contentLength": 64699,
                            "lastModified": "2023-02-20T12:23:39",
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/v.pdf",
                            },
                        },
                        "fileName": "v.pdf",
                        "fileType": ".pdf",
                        "mimeTag": "application/pdf",
                        "sizeInBytes": 64699,
                    },
                ],
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_deleted_files(
    messages_to_create_df_deleted_files_not_in_elastic: pd.DataFrame,
    messages_to_update_df_deleted_files_in_elastic: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df hs multiple
    messages with deleted files, in Elastic or NOT in elastic.

    :param messages_to_create_df_deleted_files_not_in_elastic: expected df for create records.
    This contains records which for which there is a message and edit in the input, and the edit
    deletes one of the files in the message
    :param messages_to_update_df_deleted_files_in_elastic: expected df for update records. This
    contains records which are in Elastic, and have files deleted from them
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_deleted_files_not_in_elastic,
        messages_to_update_df=messages_to_update_df_deleted_files_in_elastic,
    )


@pytest.fixture()
def messages_to_create_df_for_bot_messages() -> pd.DataFrame:
    """
    Messages to create df for the case where there are only bot messages
    :return: Dataframe with messages to create
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": "B013H935ZRP",
                "message_ts": "1677552313.074649",
                "message_text": "Flow Runner Failed",
                "message_thread_ts": pd.NA,
                "message_subtype": "bot_message",
                "message_reactions": pd.NA,
                "message_user": pd.NA,
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2023-02-28T02:45:13.074649Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": "B013H935ZRP",
                "message_ts": "1677229894.727219",
                "message_text": "Flow Runner Failed",
                "message_thread_ts": pd.NA,
                "message_subtype": "bot_message",
                "message_reactions": pd.NA,
                "message_user": pd.NA,
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": pd.NA,
                "message_date_time": "2023-02-24T09:11:34.727219Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": "B03NJUL7XJ9",
                "message_ts": "1677657822.669129",
                "message_text": "<@U01PGFLH47Q> got heads :arrow_up:",
                "message_thread_ts": pd.NA,
                "message_subtype": "bot_message",
                "message_reactions": pd.NA,
                "message_user": pd.NA,
                "edits": pd.NA,
                "file_attachments": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "mentions": ["U01PGFLH47Q"],
                "message_date_time": "2023-03-01T08:03:42.669128Z",
                "message_original_date_time": pd.NA,
                "reply_to_id": pd.NA,
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def only_edits_df_json_parser_result() -> SlackParserOutput:
    return SlackParserOutput(
        channel_info=ChannelInfo(
            channel_id="C04QKF93YJX",
            channel_name="slack-test",
            im_indicator=False,
            mpim_indicator=False,
            private_indicator=False,
        ),
        chat_events_df=pd.DataFrame(),
        messages_df=pd.DataFrame(),
        edits_and_deletes_df=pd.DataFrame(
            # Dataframe with only edits
            data={
                "message_user": ["U01PGFLH47Q", "U01PGFLH47Q", "U04LEV1U8AK", "U04LEV1U8AK"],
                "message_ts": [
                    "1706009135.000000",
                    "1706009639.000000",
                    "1706009647.000000",
                    "1706009652.000000",
                ],
                "message_text": [
                    "Message B Edited 23 Jan 2004",
                    None,
                    "Something extra edited",
                    None,
                ],
                "message_original_ts": [
                    "1678178518.460879",
                    "1706009636.248329",
                    "1706009638.266759",
                    "1706009638.266759",
                ],
                "message_subtype": [
                    "message_changed",
                    "message_deleted",
                    "message_changed",
                    "message_deleted",
                ],
                "mentions": [None, None, None, None],
                "previous_files": [None, None, None, None],
                "previous_text": [
                    "Message B",
                    "Delete test",
                    "Something extra",
                    "Something extra edited",
                ],
                "previous_mentions": [None, None, None, None],
                "previous_external_attachments": [None, None, None, None],
                "message_date_time": [
                    "2024-01-23T11:25:35.000000Z",
                    "2024-01-23T11:33:59.000000Z",
                    "2024-01-23T11:34:07.000000Z",
                    "2024-01-23T11:34:12.000000Z",
                ],
                "message_original_date_time": [
                    "2023-03-07T08:41:58.460879Z",
                    "2024-01-23T11:33:56.248328Z",
                    "2024-01-23T11:33:58.266758Z",
                    "2024-01-23T11:33:58.266758Z",
                ],
                "to_ids": [
                    ["U02AMERNUFM", "U04GCDHMM5X", "U04LEV1U8AK", "U023KEU40SZ"],
                    ["U02AMERNUFM", "U04GCDHMM5X", "U04LEV1U8AK", "U023KEU40SZ"],
                    ["U01PGFLH47Q", "U02AMERNUFM", "U04GCDHMM5X", "U023KEU40SZ"],
                    ["U01PGFLH47Q", "U02AMERNUFM", "U04GCDHMM5X", "U023KEU40SZ"],
                ],
                "thread_ts_deleted_parent": [None, None, None, None],
            }
        ),
    )


@pytest.fixture()
def expected_edits_only_result() -> pd.DataFrame:
    return pd.DataFrame(
        data=[
            {
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Delete test",
                        "timestampEdited": "2024-01-23T11:33:56.248328Z",
                        "type": "MARKDOWN",
                    }
                ],
                "file_attachments": None,
                "is_deleted": True,
                "is_edited": False,
                "mentions": None,
                "message_bot_id": None,
                "message_date_time": "2024-01-23T11:33:59.000000Z",
                "message_original_date_time": "2024-01-23T11:33:56.248328Z",
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1706009636.248329",
                "message_user": "U01PGFLH47Q",
                "previous_external_attachments": None,
                "reply_to_id": None,
                "thread_ts_deleted_parent": None,
                "to_ids": ["U02AMERNUFM", "U04GCDHMM5X", "U04LEV1U8AK", "U023KEU40SZ"],
            },
            {
                "edits": [
                    {
                        "editedBy": "U04LEV1U8AK",
                        "text": "Something extra",
                        "timestampEdited": "2024-01-23T11:33:58.266758Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U04LEV1U8AK",
                        "text": "Something extra edited",
                        "timestampEdited": "2024-01-23T11:34:07.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "file_attachments": None,
                "is_deleted": True,
                "is_edited": True,
                "mentions": None,
                "message_bot_id": None,
                "message_date_time": "2024-01-23T11:34:12.000000Z",
                "message_original_date_time": "2024-01-23T11:33:58.266758Z",
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1706009638.266759",
                "message_user": "U04LEV1U8AK",
                "previous_external_attachments": None,
                "reply_to_id": None,
                "thread_ts_deleted_parent": None,
                "to_ids": ["U01PGFLH47Q", "U02AMERNUFM", "U04GCDHMM5X", "U023KEU40SZ"],
            },
            {
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Message B",
                        "timestampEdited": "2023-03-07T08:41:58.460879Z",
                        "type": "MARKDOWN",
                    }
                ],
                "file_attachments": None,
                "is_deleted": False,
                "is_edited": True,
                "mentions": None,
                "message_bot_id": None,
                "message_date_time": "2024-01-23T11:25:35.000000Z",
                "message_original_date_time": "2023-03-07T08:41:58.460879Z",
                "message_reactions": None,
                "message_subtype": "message_changed",
                "message_text": "Message B Edited 23 Jan 2004",
                "message_thread_ts": None,
                "message_ts": "1678178518.460879",
                "message_user": "U01PGFLH47Q",
                "previous_external_attachments": None,
                "reply_to_id": None,
                "thread_ts_deleted_parent": None,
                "to_ids": ["U02AMERNUFM", "U04GCDHMM5X", "U04LEV1U8AK", "U023KEU40SZ"],
            },
        ]
    )


@pytest.fixture()
def expected_result_edits_inside_thread_not_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        data=[
            {
                "message_bot_id": None,
                "message_reactions": [
                    {"name": "greenlight", "users": ["U01PGFLH47Q"], "count": 1},
                    {"name": "boom", "users": ["U01PGFLH47Q"], "count": 1},
                ],
                "message_subtype": None,
                "message_text": "<@U01PGFLH47Q> just a mention",
                "message_thread_ts": None,
                "message_ts": "1672237210.352089",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:20:10.352088Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": None,
                "is_deleted": None,
                "is_edited": None,
                "message_original_date_time": None,
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": "<https://steeleye.slack.com/archives/C04DX1D9JLX/p1672237210352089>\nQuote Reply test",
                "message_thread_ts": None,
                "message_ts": "1672821580.230529",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2023-01-04T08:39:40.230529Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": "C04DX1D9JLX|1672237210.352089",
                "edits": None,
                "is_deleted": None,
                "is_edited": None,
                "message_original_date_time": None,
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": "test. Edited — mention removed.",
                "message_thread_ts": None,
                "message_ts": "1672237152.847909",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:19:31.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "<@U01PGFLH47Q> test.",
                        "timestampEdited": "2022-12-28T14:19:12.847908Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:19:12.847908Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": "Yet another new message. Edit 1. Edit 2. Edit 3.",
                "message_thread_ts": None,
                "message_ts": "1672237063.712929",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:18:06.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message.",
                        "timestampEdited": "2022-12-28T14:17:43.712929Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message. Edit 1",
                        "timestampEdited": "2022-12-28T14:17:52.000000Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Yet another new message. Edit 1. Edit 2.",
                        "timestampEdited": "2022-12-28T14:17:59.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:17:43.712929Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": "Another new message - <@U01PGFLH47Q> <!subteam^S01FNMD2BSA|@dataplatform> EDIT: mentions added.",
                "message_thread_ts": None,
                "message_ts": "1672237053.342359",
                "message_user": "U01PGFLH47Q",
                "mentions": ["U01PGFLH47Q"],
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:19:54.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Another new message",
                        "timestampEdited": "2022-12-28T14:17:33.342359Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-28T14:17:33.342359Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1670915260.316689",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-13T07:07:50.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleting an edited message.",
                        "timestampEdited": "2022-12-13T07:07:40.316688Z",
                        "type": "MARKDOWN",
                    },
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Deleting an edited message. EDIT",
                        "timestampEdited": "2022-12-13T07:07:47.000000Z",
                        "type": "MARKDOWN",
                    },
                ],
                "is_deleted": True,
                "is_edited": True,
                "message_original_date_time": "2022-12-13T07:07:40.316688Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1670915275.953489",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-13T07:07:58.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Normal delete test",
                        "timestampEdited": "2022-12-13T07:07:55.953489Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2022-12-13T07:07:55.953489Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1671630400.056729",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:16:04.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message which will be deleted",
                        "timestampEdited": "2022-12-21T13:46:40.056729Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2022-12-21T13:46:40.056729Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": None,
                "message_text": None,
                "message_thread_ts": None,
                "message_ts": "1672237098.468469",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:18:23.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "I’m going to delete this.",
                        "timestampEdited": "2022-12-28T14:18:18.468468Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2022-12-28T14:18:18.468468Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": "message_changed",
                "message_text": "New message. EDITED.",
                "message_thread_ts": None,
                "message_ts": "1671630355.409639",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:18:51.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New message",
                        "timestampEdited": "2022-12-21T13:45:55.409638Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-21T13:45:55.409638Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
            {
                "message_bot_id": None,
                "message_reactions": None,
                "message_subtype": "message_changed",
                "message_text": "Reply 2. Edited a threaded message",
                "message_thread_ts": None,
                "message_ts": "1671630436.270169",
                "message_user": "U01PGFLH47Q",
                "mentions": None,
                "file_attachments": None,
                "message_date_time": "2022-12-28T14:16:52.000000Z",
                "to_ids": ["U02AMERNUFM"],
                "reply_to_id": None,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Reply 2",
                        "timestampEdited": "2022-12-21T13:47:16.270169Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": False,
                "is_edited": True,
                "message_original_date_time": "2022-12-21T13:47:16.270169Z",
                "thread_ts_deleted_parent": None,
                "previous_external_attachments": None,
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_for_bot_messages(
    messages_to_create_df_for_bot_messages: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df contains bot
    messages.

    :param messages_to_create_df_for_bot_messages: expected df for create records.
    This contains bot messages
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_for_bot_messages,
        messages_to_update_df=pd.DataFrame(),
    )


@pytest.fixture()
def messages_to_create_df_for_parent_of_thread_deleted() -> pd.DataFrame:
    """Messages to create df for the case where the parent of threaded
    messages.

    There are 2 new messages (replies to a thread) and a deleted message
    (the parent of the aforementioned replies
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Reply 32",
                "message_thread_ts": "1678360465.109019",
                "message_ts": "1678360472.477299",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-09T11:14:32.477298Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "message_original_date_time": pd.NA,
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": "Reply 31",
                "message_thread_ts": "1678360465.109019",
                "message_ts": "1678360470.004459",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-09T11:14:30.004459Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": pd.NA,
                "is_deleted": pd.NA,
                "is_edited": pd.NA,
                "message_original_date_time": pd.NA,
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            },
            {
                "message_bot_id": pd.NA,
                "message_reactions": pd.NA,
                "message_subtype": pd.NA,
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_ts": "1678360465.109019",
                "message_user": "U01PGFLH47Q",
                "mentions": pd.NA,
                "file_attachments": pd.NA,
                "message_date_time": "2023-03-09T11:14:45.000000Z",
                "to_ids": ["U01PGFLH47Q"],
                "reply_to_id": pd.NA,
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "New thread",
                        "timestampEdited": "2023-03-09T11:14:25.109019Z",
                        "type": "MARKDOWN",
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "message_original_date_time": "2023-03-09T11:14:25.109019Z",
                "thread_ts_deleted_parent": "1678360465.109019",
                "previous_external_attachments": pd.NA,
            },
        ]
    )


@pytest.fixture()
def messages_to_update_df_for_parent_of_thread_deleted() -> pd.DataFrame:
    """Messages to update df for the case where the parent of threaded
    messages.

    There is 1 deleted message. This message is an (existing) parent of
    existing threaded messages in Elastic.
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://ashwath.dev.steeleye.co/lake/2023-03-09.ndjson",
                "&id": "0eda272afcd350b20c74002c30c753632968a8441ce5fc41f7b9fdc89cb8e1d",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "chatType": "Channel - Public",
                "metadata.messageId": "C04DX1D9JLX|1678353506.007819",
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "lake/2023-03-09.ndjson",
                "metadata.subThreadId": "C04DX1D9JLX|1678353506.007819",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["u01pgflh47q"],
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo.raw": "u01pgflh47q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "u01pgflh47q"}],
                "timestamps.created": "2023-03-09T09:18:26.007818Z",
                "timestamps.timestampEnd": "2023-03-09T11:14:12.000000Z",
                "timestamps.timestampStart": "2023-03-09T11:14:12.000000Z",
                "body.text": pd.NA,
                "body.type": "MARKDOWN",
                "attachments": pd.NA,
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Thread 1",
                        "timestampEdited": "2023-03-09T09:18:26.007818Z",
                        "type": "MARKDOWN",
                    }
                ],
                "identifiers.mentionedIds": pd.NA,
                "metadata.isDeleted": True,
                "metadata.isEdited": False,
            }
        ]
    )


@pytest.fixture()
def messages_from_elastic_for_parent_of_thread_deleted() -> pd.DataFrame:
    """Mocks a single message fetched from Elastic for deletion of a parent
    message of a thread.

    Used to get the result in expected_result_slack_edits_and_deletes_parent_of_thread_deleted
    :return: Dataframe with one result
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://ashwath.dev.steeleye.co/lake/2023-03-09.ndjson",
                "&id": "0eda272afcd350b20c74002c30c753632968a8441ce5fc41f7b9fdc89cb8e1d",
                "&uniqueProps": ["u01pgflh47q"],
                "&key": "Message:0eda272afcd350b20c74002c30c753632968a8441ce5fc41f7b9fdc89cb8e1d:1678353907536",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "7e449d2c5bbc9101c223bee91363edef75f3517fb1df35d4750f0d54db89deb2",
                "&timestamp": 1678353907536,
                "&user": "system",
                "chatType": "Channel - Public",
                "metadata.messageId": "C04DX1D9JLX|1678353506.007819",
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "lake/2023-03-09.ndjson",
                "metadata.subThreadId": "C04DX1D9JLX|1678353506.007819",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["u01pgflh47q"],
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo.raw": "u01pgflh47q",
                "identifiers.toIds": ["u01pgflh47q"],
                "identifiers.toIdsAddlInfo": [{"raw": "u01pgflh47q"}],
                "timestamps.created": "2023-03-09T09:18:26.007818Z",
                "timestamps.timestampEnd": "2023-03-09T09:18:26.007818Z",
                "timestamps.timestampStart": "2023-03-09T09:18:26.007818Z",
                "body.text": "Thread 1",
                "body.type": "MARKDOWN",
            },
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_parent_of_thread_deleted(
    messages_to_create_df_for_parent_of_thread_deleted: pd.DataFrame,
    messages_to_update_df_for_parent_of_thread_deleted: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df contains
    threaded messages whose parent message is deleted.

    :param messages_to_create_df_for_parent_of_thread_deleted: expected df for create records (1 parent of
    thread deleted in current batch, 2 replies to this thread)
    :param messages_to_update_df_for_parent_of_thread_deleted: expected df for update records (1 parent
    of thread deleted)
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_for_parent_of_thread_deleted,
        messages_to_update_df=messages_to_update_df_for_parent_of_thread_deleted,
    )


@pytest.fixture()
def messages_to_create_df_for_deleted_gifs() -> pd.DataFrame:
    """
    Messages_to_update_df returned by slack_edits_and_deletes for the case where there
    are deleted GIFs (1 of which matches with a message in Elastic)
    :return:
    """
    return pd.DataFrame(
        [
            {
                "message_bot_id": pd.NA,
                "message_ts": "1670844557.255509",
                "message_text": pd.NA,
                "message_thread_ts": pd.NA,
                "message_subtype": pd.NA,
                "message_reactions": pd.NA,
                "message_user": "U01PGFLH47Q",
                "edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Surprised | Deleted file(s): ['https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g']",
                        "timestampEdited": "2022-12-12T11:29:17.255508Z",
                        "type": "MARKDOWN",
                    }
                ],
                "file_attachments": [
                    {
                        "fileName": "Surprised_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 3369642,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Surprised_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-07-21T16:59:38",
                            "contentLength": 3369642,
                            "versionId": None,
                        },
                        "fileId": "https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g",
                        "isDeleted": True,
                    }
                ],
                "is_deleted": True,
                "is_edited": False,
                "mentions": pd.NA,
                "message_date_time": "2023-03-10T07:21:53.000000Z",
                "message_original_date_time": "2022-12-12T11:29:17.255508Z",
                "reply_to_id": pd.NA,
                "to_ids": ["U01PGFLH47Q"],
                "thread_ts_deleted_parent": pd.NA,
                "previous_external_attachments": pd.NA,
            }
        ]
    )


@pytest.fixture()
def messages_to_update_df_for_deleted_gifs() -> pd.DataFrame:
    """
    Messages_to_update_df returned by slack_edits_and_deletes for the case where there
    are deleted GIFs (1 of which matches with a message in Elastic)
    :return:
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://ashwath.dev.steeleye.co/lake/2023-03-10.ndjson",
                "&id": "958fc6b841510adfba8ff4b1cc668a07bcf1b3fc7d61f531e119adc5d6c72f3091b7e707",
                "attachments": [
                    {
                        "fileName": "Dog_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 5031568,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Dog_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-03-09T19:29:45",
                            "contentLength": 5031568,
                            "versionId": None,
                        },
                        "fileId": "https://media1.giphy.com/media/kiBcwEXegBTACmVOnE/giphy-downsized.gif?cid=6104955eb9w9ytvh3p5y0nil0cfwyl3oer6mebsvl8h73xvf&rid=giphy-downsized.gif&ct=g",
                        "isDeleted": True,
                    }
                ],
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": True,
                "chatType": "Channel - Public",
                "metadata.messageId": "C04DX1D9JLX|1678432838.479629",
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "lake/2023-03-10.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["u01pgflh47q"],
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo.raw": "u01pgflh47q",
                "identifiers.toIds": ["U01PGFLH47Q"],
                "identifiers.toIdsAddlInfo": [{"raw": "u01pgflh47q"}],
                "timestamps.created": "2023-03-10T07:20:38.479629Z",
                "timestamps.timestampEnd": "2023-03-10T17:59:58.000000Z",
                "timestamps.timestampStart": "2023-03-10T17:59:58.000000Z",
                "body.text": pd.NA,
                "body.type": "MARKDOWN",
                "body.edits": [
                    {
                        "editedBy": "U01PGFLH47Q",
                        "text": "Dog | Deleted file(s): ['https://media1.giphy.com/media/kiBcwEXegBTACmVOnE/giphy-downsized.gif?cid=6104955eb9w9ytvh3p5y0nil0cfwyl3oer6mebsvl8h73xvf&rid=giphy-downsized.gif&ct=g']",
                        "timestampEdited": "2023-03-10T07:20:38.479629Z",
                        "type": "MARKDOWN",
                    }
                ],
                "identifiers.mentionedIds": pd.NA,
                "metadata.isDeleted": True,
                "metadata.isEdited": False,
            }
        ]
    )


@pytest.fixture()
def messages_from_elastic_for_deleted_gifs() -> pd.DataFrame:
    """Mocks a single message fetched from Elastic for deleted gifs.

    Used to get the result
    in expected_result_slack_edits_and_deletes_deleted_gifs
    :return: Dataframe with one result
    """
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://ashwath.dev.steeleye.co/lake/2023-03-10.ndjson",
                "&id": "958fc6b841510adfba8ff4b1cc668a07bcf1b3fc7d61f531e119adc5d6c72f3091b7e707",
                "&uniqueProps": ["u01pgflh47q"],
                "attachments": [
                    {
                        "fileName": "Dog_2023-07-21T16:59:38.gif",
                        "fileType": ".gif",
                        "sizeInBytes": 5031568,
                        "fileInfo": {
                            "location": {
                                "bucket": "test.dev.steeleye.co",
                                "key": "slack/test/C04DX1D9JLX/Dog_2023-07-21T16:59:38.gif",
                            },
                            "lastModified": "2023-03-09T19:29:45",
                            "contentLength": 5031568,
                            "versionId": None,
                        },
                        "fileId": "https://media1.giphy.com/media/kiBcwEXegBTACmVOnE/giphy-downsized.gif?cid=6104955eb9w9ytvh3p5y0nil0cfwyl3oer6mebsvl8h73xvf&rid=giphy-downsized.gif&ct=g",  # noqa E501
                    }
                ],
                "&key": "Message:958fc6b841510adfba8ff4b1cc668a07bcf1b3fc7d61f531e119adc5d6c72f3091b7e707:1678470746043",
                "roomId": "C04DX1D9JLX",
                "roomName": "slack-test",
                "&model": "Message",
                "hasAttachment": True,
                "&version": 1,
                "&hash": "a4a189d146b997819e0537e27f84efb9bc489eb95d4085a93eab1fe7a8380979",
                "&timestamp": 1678470746043,
                "&user": "system",
                "chatType": "Channel - Public",
                "metadata.messageId": "C04DX1D9JLX|1678432838.479629",
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "lake/2023-03-10.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "identifiers.allIds": ["u01pgflh47q"],
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo.raw": "u01pgflh47q",
                "identifiers.toIds": ["u01pgflh47q"],
                "identifiers.toIdsAddlInfo": [{"raw": "u01pgflh47q"}],
                "timestamps.created": "2023-03-10T07:20:38.479629Z",
                "timestamps.timestampEnd": "2023-03-10T07:20:38.479629Z",
                "timestamps.timestampStart": "2023-03-10T07:20:38.479629Z",
                "body.text": "Dog",
                "body.type": "MARKDOWN",
            }
        ]
    )


@pytest.fixture()
def expected_result_slack_edits_and_deletes_deleted_gifs(
    messages_to_create_df_for_deleted_gifs: pd.DataFrame,
    messages_to_update_df_for_deleted_gifs: pd.DataFrame,
) -> SlackDataframes:
    """Expected result for SlackEditsAndDeletes when the input df contains
    deleted GIFs.

    :param messages_to_create_df_for_deleted_gifs: expected df for create records. 1 GIF in the
    current batch deleted
    :param messages_to_update_df_for_deleted_gifs: expected df for update records. 1 GIF in Elastic
    deleted
    :return: SlackDataFrames
    """
    return SlackDataframes(
        messages_to_create_df=messages_to_create_df_for_deleted_gifs,
        messages_to_update_df=messages_to_update_df_for_deleted_gifs,
    )


# ------------------------------------ Fixtures for Chat Event Mappings ------------------------------------
@pytest.fixture()
def expected_result_chat_event_mappings() -> pd.DataFrame:
    """
    Expected result for SlackChatEventMappings with multiple chat events
    :return: Chat event mappings dat frame
    """
    return pd.DataFrame(
        [
            {
                "eventType": "ENTERED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:09:56.420689Z",
                "timestamps.timestampStart": "2023-03-01T08:09:56.420689Z",
                "timestamps.timestampEnd": "2023-03-01T08:09:56.420689Z",
            },
            {
                "eventType": "LEFT",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:09:48.011489Z",
                "timestamps.timestampStart": "2023-03-01T08:09:48.011489Z",
                "timestamps.timestampEnd": "2023-03-01T08:09:48.011489Z",
            },
            {
                "eventType": "ROOM_UNARCHIVED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:06:22.074129Z",
                "timestamps.timestampStart": "2023-03-01T08:06:22.074129Z",
                "timestamps.timestampEnd": "2023-03-01T08:06:22.074129Z",
            },
            {
                "eventType": "ENTERED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:06:22.032839Z",
                "timestamps.timestampStart": "2023-03-01T08:06:22.032839Z",
                "timestamps.timestampEnd": "2023-03-01T08:06:22.032839Z",
            },
            {
                "eventType": "ROOM_CLOSE",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:05:55.987358Z",
                "timestamps.timestampStart": "2023-03-01T08:05:55.987358Z",
                "timestamps.timestampEnd": "2023-03-01T08:05:55.987358Z",
            },
            {
                "eventType": "ROOM_NAME_UPDATED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:05:22.603019Z",
                "timestamps.timestampStart": "2023-03-01T08:05:22.603019Z",
                "timestamps.timestampEnd": "2023-03-01T08:05:22.603019Z",
            },
            {
                "eventType": "ROOM_PURPOSE_UPDATED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:01:05.620079Z",
                "timestamps.timestampStart": "2023-03-01T08:01:05.620079Z",
                "timestamps.timestampEnd": "2023-03-01T08:01:05.620079Z",
            },
            {
                "eventType": "ROOM_TOPIC_UPDATED",
                "identifiers.fromId": "u01pgflh47q",
                "identifiers.fromIdAddlInfo": {"raw": "u01pgflh47q", "countryCode": None},
                "identifiers.allIds": ["u01pgflh47q"],
                "metadata.source.client": "Slack Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "metadata.threadId": "C04DX1D9JLX",
                "roomId": "C04DX1D9JLX",
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/slack_chat/2023/04/19/test.ndjson",
                "timestamps.created": "2023-03-01T08:00:42.994209Z",
                "timestamps.timestampStart": "2023-03-01T08:00:42.994209Z",
                "timestamps.timestampEnd": "2023-03-01T08:00:42.994209Z",
            },
        ]
    )
