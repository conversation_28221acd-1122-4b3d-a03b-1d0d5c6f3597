# ruff: noqa: E501
import numpy as np
import pandas as pd
import pytest


@pytest.fixture()
def input_chat_events_df():
    return pd.DataFrame(
        data={
            "type": {
                0: "participantentered",
                1: "participantentered",
                2: "participantentered",
                3: "participantentered",
                4: "participantentered",
                5: "participantentered",
                6: "participantleft",
            },
            "username": {
                0: "jhaz",
                1: "dste17",
                2: "ama",
                3: "dsteph",
                4: "smang",
                5: "dgallagh",
                6: "dgallagh",
            },
            "IMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
                5: "<EMAIL>",
                6: "<EMAIL>",
            },
            "email": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
                5: "<PERSON><PERSON><PERSON><PERSON><PERSON>@steel-eye.com",
                6: "D<PERSON><EMAIL>",
            },
            "datetime": {
                0: "2022-02-10T02:50:00",
                1: "2022-02-10T02:50:00",
                2: "2022-02-10T02:50:00",
                3: "2022-02-10T02:50:00",
                4: "2022-02-10T02:50:00",
                5: "2022-02-10T02:50:00",
                6: "2022-02-10T02:50:15",
            },
            "conferencename": {
                0: "Socar / GFI JKM",
                1: "Socar / GFI JKM",
                2: "Spectron - Socar",
                3: "Spectron - Socar",
                4: "Spectron - Socar",
                5: "Prebon/SOCAR LNG",
                6: "Prebon/SOCAR LNG",
            },
            "conferencetype": {
                0: "ICE_PERM",
                1: "ICE_PERM",
                2: "ICE_PERM",
                3: "ICE_PERM",
                4: "ICE_PERM",
                5: "ICE_PERM",
                6: "ICE_PERM",
            },
        }
    )


@pytest.fixture()
def only_individual_messages_df() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "__swarm_raw_index__": {0: 0, 1: 1, 2: 2},
            "network": {0: "YJ", 1: "YJ", 2: "YJ"},
            "direction": {0: "outbound", 1: "inbound", 2: "outbound"},
            "senderIMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
            },
            "senderEmail": {
                0: "<EMAIL>",
                1: np.nan,
                2: "<EMAIL>",
            },
            "senderUsername": {0: "fchevrier1", 1: np.nan, 2: "fchevrier1"},
            "recipientIMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
            },
            "recipientEmail": {0: np.nan, 1: "<EMAIL>", 2: np.nan},
            "recipientUsername": {0: np.nan, 1: "fchevrier1", 2: np.nan},
            "datetime": {
                0: "2023-09-02T11:01:16",
                1: "2023-09-02T11:01:16",
                2: "2023-09-02T11:01:16",
            },
            "content": {
                0: "SOCAR Trading retains and monitors Instant Messages within the bounds of law, ensuring compliance with internal policies, regulatory requirements and for other legitimate business related reasons.",
                1: "Bonjour et merci",
                2: "Away",
            },
            "__chat_type__": {
                0: "Individual Message",
                1: "Individual Message",
                2: "Individual Message",
            },
        }
    )


@pytest.fixture()
def only_conference_logs_messages() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "senderIMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: np.nan,
            },
            "senderUsername": {0: "jhaz", 1: "jhaz", 2: "jhaz", 3: "rsmid", 4: np.nan},
            "senderEmail": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: np.nan,
            },
            "datetime": {
                0: "2022-02-10T07:41:33",
                1: "2022-02-10T07:42:01",
                2: "2022-02-10T07:54:19",
                3: "2022-02-10T16:49:17",
                4: np.nan,
            },
            "message": {
                0: "gd morning",
                1: "{{APR / 0.85}}",
                2: "Some message",
                3: "have a good weekend guys i am off tomorrow  the guys will be here",
                4: np.nan,
            },
            "conferencename": {
                0: "Socar / GFI JKM",
                1: "Socar / GFI JKM",
                2: "Socar / GFI JKM",
                3: "Spectron - Socar",
                4: "Prebon/SOCAR LNG",
            },
            "conferencetype": {
                0: "ICE_PERM",
                1: "ICE_PERM",
                2: "ICE_PERM",
                3: "ICE_PERM",
                4: "ICE_PERM",
            },
            "__chat_type__": {
                0: "Conference Message",
                1: "Conference Message",
                2: "Conference Message",
                3: "Conference Message",
                4: "Conference Message",
            },
            "chatEvents": {
                0: [
                    {
                        "type": "participantentered",
                        "username": "jhaz",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                    {
                        "type": "participantentered",
                        "username": "dste17",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                ],
                1: [
                    {
                        "type": "participantentered",
                        "username": "jhaz",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                    {
                        "type": "participantentered",
                        "username": "dste17",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                ],
                2: [
                    {
                        "type": "participantentered",
                        "username": "jhaz",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                    {
                        "type": "participantentered",
                        "username": "dste17",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Socar / GFI JKM",
                        "conferencetype": "ICE_PERM",
                    },
                ],
                3: [
                    {
                        "type": "participantentered",
                        "username": "ama",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Spectron - Socar",
                        "conferencetype": "ICE_PERM",
                    },
                    {
                        "type": "participantentered",
                        "username": "dsteph",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Spectron - Socar",
                        "conferencetype": "ICE_PERM",
                    },
                    {
                        "type": "participantentered",
                        "username": "smang",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Spectron - Socar",
                        "conferencetype": "ICE_PERM",
                    },
                ],
                4: [
                    {
                        "type": "participantentered",
                        "username": "dgallagh",
                        "IMID": "<EMAIL>",
                        "email": "<EMAIL>",
                        "datetime": "2022-02-10T02:50:00",
                        "conferencename": "Prebon/SOCAR LNG",
                        "conferencetype": "ICE_PERM",
                    }
                ],
            },
            0: {0: np.nan, 1: np.nan, 2: np.nan, 3: np.nan, 4: np.nan},
        }
    )


@pytest.fixture()
def only_desk_logs_messages() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "senderIMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
            },
            "senderEmail": {0: None, 1: None, 2: None, 3: None, 4: None},
            "senderUsername": {0: None, 1: None, 2: None, 3: None, 4: None},
            "recipientIMID": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
            },
            "recipientEmail": {0: None, 1: None, 2: None, 3: None, 4: None},
            "recipientUsername": {0: None, 1: None, 2: None, 3: None, 4: None},
            "datetime": {
                0: "2024-05-08T01:51:56",
                1: "2024-05-08T01:53:14",
                2: "2024-05-08T01:53:47",
                3: "2024-05-08T02:46:52",
                4: "2024-05-08T01:51:56",
            },
            "content": {
                0: "jan px 1015.5 bid\njan px/mopj 392 bid",
                1: "jan px 1016/1017.5\njan px/mopj 392/394",
                2: "jan px 1016/1017\njan px/mopj 393/394",
                3: "cal25 MEG 538 / 544\ncal25 MEG / mopj -70 / -64",
                4: "jan px 1015.5 bid\njan px/mopj 392 bid",
            },
            "DeskIMIDYJ": {
                0: "ERA_CommoditiesTrading",
                1: "ERA_CommoditiesTrading",
                2: "ERA_CommoditiesTrading",
                3: "ERA_CommoditiesTrading",
                4: "ERA_TradingSomething",
            },
            "DeskMemberUsername": {
                0: ["chclarke", "rcoleman4", "wcranston3", "cnelson15"],
                1: ["chclarke", "rcoleman4", "wcranston3", "cnelson15"],
                2: ["chclarke", "rcoleman4", "wcranston3", "cnelson15"],
                3: ["chclarke", "rcoleman4", "wcranston3", "cnelson15"],
                4: ["nallnutt2", "myoung17"],
            },
            "DeskEmails": {
                0: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                1: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                2: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                3: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                4: ["<EMAIL>", "<EMAIL>"],
            },
            "__chat_type__": {
                0: "Desk Message",
                1: "Desk Message",
                2: "Desk Message",
                3: "Desk Message",
                4: "Desk Message",
            },
        }
    )


@pytest.fixture()
def only_individual_messages_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "body.text": {
                0: "SOCAR Trading retains and monitors Instant Messages within the bounds of law, ensuring compliance with internal policies, regulatory requirements and for other legitimate business related reasons.",
                1: "Bonjour et merci",
                2: "Away",
            },
            "body.type": {0: "PLAIN", 1: "PLAIN", 2: "PLAIN"},
            "body.displayText": {
                0: "SOCAR Trading retains and monitors Instant Messages within the bounds of law, ensuring compliance with internal policies, regulatory requirements and for other legitimate business related reasons.",
                1: "Bonjour et merci",
                2: "Away",
            },
            "hasAttachment": {0: False, 1: False, 2: False},
            "identifiers.allIds": {
                0: ["<EMAIL>", "<EMAIL>"],
                1: ["<EMAIL>", "<EMAIL>"],
                2: ["<EMAIL>", "<EMAIL>"],
            },
            "identifiers.fromId": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
            },
            "identifiers.fromIdAddlInfo": {
                0: {"raw": "<EMAIL>", "countryCode": None},
                1: {"raw": "<EMAIL>", "countryCode": None},
                2: {"raw": "<EMAIL>", "countryCode": None},
            },
            "identifiers.fromUserId": {0: "fchevrier1", 1: np.nan, 2: "fchevrier1"},
            "identifiers.toIds": {
                0: ["<EMAIL>"],
                1: ["<EMAIL>"],
                2: ["<EMAIL>"],
            },
            "identifiers.toIdsAddlInfo": {
                0: [{"raw": "<EMAIL>", "countryCode": None}],
                1: [{"raw": "<EMAIL>", "countryCode": None}],
                2: [{"raw": "<EMAIL>", "countryCode": None}],
            },
            "identifiers.toUserId": {0: np.nan, 1: "fchevrier1", 2: np.nan},
            "metadata.source.client": {0: "ICE Chat", 1: "ICE Chat", 2: "ICE Chat"},
            "metadata.threadId": {
                0: "<EMAIL>,<EMAIL>",
                1: "<EMAIL>,<EMAIL>",
                2: "<EMAIL>,<EMAIL>",
            },
            "metadata.source.fileInfo.location.bucket": {
                0: "foo.steeleye.co",
                1: "foo.steeleye.co",
                2: "foo.steeleye.co",
            },
            "metadata.source.fileInfo.location.key": {
                0: "folder/file.xml",
                1: "folder/file.xml",
                2: "folder/file.xml",
            },
            "sourceKey": {
                0: "s3://foo.steeleye.co/folder/file.xml",
                1: "s3://foo.steeleye.co/folder/file.xml",
                2: "s3://foo.steeleye.co/folder/file.xml",
            },
            "roomId": {
                0: "<EMAIL>,<EMAIL>",
                1: "<EMAIL>,<EMAIL>",
                2: "<EMAIL>,<EMAIL>",
            },
            "roomName": {
                0: "fchevrier|jcharmoy1",
                1: "fchevrier|jcharmoy1",
                2: "fchevrier|jcharmoy1",
            },
            "chatType": {0: "Individual Message", 1: "Individual Message", 2: "Individual Message"},
            "timestamps.localTimestampEnd": {
                0: "2023-09-02T11:01:16.000000Z",
                1: "2023-09-02T11:01:16.000000Z",
                2: "2023-09-02T11:01:16.000000Z",
            },
            "timestamps.localTimestampStart": {
                0: "2023-09-02T11:01:16.000000Z",
                1: "2023-09-02T11:01:16.000000Z",
                2: "2023-09-02T11:01:16.000000Z",
            },
            "timestamps.timestampEnd": {
                0: "2023-09-02T11:01:16.000000Z",
                1: "2023-09-02T11:01:16.000000Z",
                2: "2023-09-02T11:01:16.000000Z",
            },
            "timestamps.timestampStart": {
                0: "2023-09-02T11:01:16.000000Z",
                1: "2023-09-02T11:01:16.000000Z",
                2: "2023-09-02T11:01:16.000000Z",
            },
            "metadata.messageId": {
                0: "<EMAIL>|2023-09-02T11:01:16.000000Z",
                1: "<EMAIL>|2023-09-02T11:01:16.000000Z",
                2: "<EMAIL>|2023-09-02T11:01:16.000000Z",
            },
        }
    )


@pytest.fixture()
def only_conference_logs_messages_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "body.text": {
                0: "gd morning",
                1: "{{APR / 0.85}}",
                2: "Some message",
                3: "have a good weekend guys i am off tomorrow  the guys will be here",
                4: np.nan,
            },
            "body.type": {0: "PLAIN", 1: "PLAIN", 2: "PLAIN", 3: "PLAIN", 4: "PLAIN"},
            "body.displayText": {
                0: "gd morning",
                1: "{{APR / 0.85}}",
                2: "Some message",
                3: "have a good weekend guys i am off tomorrow  the guys will be here",
                4: np.nan,
            },
            "hasAttachment": {0: False, 1: False, 2: False, 3: False, 4: False},
            "identifiers.allIds": {
                0: ["<EMAIL>", "<EMAIL>"],
                1: ["<EMAIL>", "<EMAIL>"],
                2: ["<EMAIL>", "<EMAIL>"],
                3: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                4: None,
            },
            "identifiers.fromId": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: None,
            },
            "identifiers.fromIdAddlInfo": {
                0: {"raw": "<EMAIL>", "countryCode": None},
                1: {"raw": "<EMAIL>", "countryCode": None},
                2: {"raw": "<EMAIL>", "countryCode": None},
                3: {"raw": "<EMAIL>", "countryCode": None},
                4: None,
            },
            "identifiers.fromUserId": {0: "jhaz", 1: "jhaz", 2: "jhaz", 3: "rsmid", 4: np.nan},
            "identifiers.toIds": {
                0: ["<EMAIL>", "<EMAIL>"],
                1: ["<EMAIL>", "<EMAIL>"],
                2: ["<EMAIL>", "<EMAIL>"],
                3: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                4: None,
            },
            "identifiers.toIdsAddlInfo": {
                0: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                1: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                2: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                3: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                4: None,
            },
            "identifiers.toUserId": {
                0: "dste17, jhaz",
                1: "dste17, jhaz",
                2: "dste17, jhaz",
                3: "ama, dsteph, smang",
                4: "",
            },
            "metadata.source.client": {
                0: "ICE Chat",
                1: "ICE Chat",
                2: "ICE Chat",
                3: "ICE Chat",
                4: "ICE Chat",
            },
            "metadata.threadId": {
                0: "Socar / GFI JKM|Conference",
                1: "Socar / GFI JKM|Conference",
                2: "Socar / GFI JKM|Conference",
                3: "Spectron - Socar|Conference",
                4: "Prebon/SOCAR LNG|Conference",
            },
            "metadata.source.fileInfo.location.bucket": {
                0: "foo.steeleye.co",
                1: "foo.steeleye.co",
                2: "foo.steeleye.co",
                3: "foo.steeleye.co",
                4: "foo.steeleye.co",
            },
            "metadata.source.fileInfo.location.key": {
                0: "folder/file.xml",
                1: "folder/file.xml",
                2: "folder/file.xml",
                3: "folder/file.xml",
                4: "folder/file.xml",
            },
            "sourceKey": {
                0: "s3://foo.steeleye.co/folder/file.xml",
                1: "s3://foo.steeleye.co/folder/file.xml",
                2: "s3://foo.steeleye.co/folder/file.xml",
                3: "s3://foo.steeleye.co/folder/file.xml",
                4: "s3://foo.steeleye.co/folder/file.xml",
            },
            "roomId": {
                0: "Socar / GFI JKM|Conference",
                1: "Socar / GFI JKM|Conference",
                2: "Socar / GFI JKM|Conference",
                3: "Spectron - Socar|Conference",
                4: "Prebon/SOCAR LNG|Conference",
            },
            "roomName": {
                0: "Socar / GFI JKM|Conference",
                1: "Socar / GFI JKM|Conference",
                2: "Socar / GFI JKM|Conference",
                3: "Spectron - Socar|Conference",
                4: "Prebon/SOCAR LNG|Conference",
            },
            "chatType": {
                0: "Conference Message",
                1: "Conference Message",
                2: "Conference Message",
                3: "Conference Message",
                4: "Conference Message",
            },
            "timestamps.localTimestampEnd": {
                0: "2022-02-10T07:41:33.000000Z",
                1: "2022-02-10T07:42:01.000000Z",
                2: "2022-02-10T07:54:19.000000Z",
                3: "2022-02-10T16:49:17.000000Z",
                4: None,
            },
            "timestamps.localTimestampStart": {
                0: "2022-02-10T07:41:33.000000Z",
                1: "2022-02-10T07:42:01.000000Z",
                2: "2022-02-10T07:54:19.000000Z",
                3: "2022-02-10T16:49:17.000000Z",
                4: None,
            },
            "timestamps.timestampEnd": {
                0: "2022-02-10T07:41:33.000000Z",
                1: "2022-02-10T07:42:01.000000Z",
                2: "2022-02-10T07:54:19.000000Z",
                3: "2022-02-10T16:49:17.000000Z",
                4: None,
            },
            "timestamps.timestampStart": {
                0: "2022-02-10T07:41:33.000000Z",
                1: "2022-02-10T07:42:01.000000Z",
                2: "2022-02-10T07:54:19.000000Z",
                3: "2022-02-10T16:49:17.000000Z",
                4: None,
            },
            "metadata.messageId": {
                0: "<EMAIL>|2022-02-10T07:41:33.000000Z",
                1: "<EMAIL>|2022-02-10T07:42:01.000000Z",
                2: "<EMAIL>|2022-02-10T07:54:19.000000Z",
                3: "<EMAIL>|2022-02-10T16:49:17.000000Z",
                4: pd.NA,
            },
        }
    )


@pytest.fixture()
def only_desk_logs_messages_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "body.text": {
                0: "jan px 1015.5 bid\njan px/mopj 392 bid",
                1: "jan px 1016/1017.5\njan px/mopj 392/394",
                2: "jan px 1016/1017\njan px/mopj 393/394",
                3: "cal25 MEG 538 / 544\ncal25 MEG / mopj -70 / -64",
                4: "jan px 1015.5 bid\njan px/mopj 392 bid",
            },
            "body.type": {0: "PLAIN", 1: "PLAIN", 2: "PLAIN", 3: "PLAIN", 4: "PLAIN"},
            "body.displayText": {
                0: "jan px 1015.5 bid\njan px/mopj 392 bid",
                1: "jan px 1016/1017.5\njan px/mopj 392/394",
                2: "jan px 1016/1017\njan px/mopj 393/394",
                3: "cal25 MEG 538 / 544\ncal25 MEG / mopj -70 / -64",
                4: "jan px 1015.5 bid\njan px/mopj 392 bid",
            },
            "hasAttachment": {0: False, 1: False, 2: False, 3: False, 4: False},
            "identifiers.allIds": {
                0: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                1: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                2: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                3: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                4: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            },
            "identifiers.fromId": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
            },
            "identifiers.fromIdAddlInfo": {
                0: {"raw": "<EMAIL>", "countryCode": None},
                1: {"raw": "<EMAIL>", "countryCode": None},
                2: {"raw": "<EMAIL>", "countryCode": None},
                3: {"raw": "<EMAIL>", "countryCode": None},
                4: {"raw": "<EMAIL>", "countryCode": None},
            },
            "identifiers.fromUserId": {0: None, 1: None, 2: None, 3: None, 4: None},
            "identifiers.toIds": {
                0: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                1: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                2: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                3: [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                4: ["<EMAIL>", "<EMAIL>"],
            },
            "identifiers.toIdsAddlInfo": {
                0: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                1: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                2: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                3: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
                4: [
                    {"raw": "<EMAIL>", "countryCode": None},
                    {"raw": "<EMAIL>", "countryCode": None},
                ],
            },
            "identifiers.toUserId": {
                0: "chclarke, rcoleman4, wcranston3, cnelson15",
                1: "chclarke, rcoleman4, wcranston3, cnelson15",
                2: "chclarke, rcoleman4, wcranston3, cnelson15",
                3: "chclarke, rcoleman4, wcranston3, cnelson15",
                4: "nallnutt2, myoung17",
            },
            "metadata.source.client": {
                0: "ICE Chat",
                1: "ICE Chat",
                2: "ICE Chat",
                3: "ICE Chat",
                4: "ICE Chat",
            },
            "metadata.threadId": {
                0: "ERA_CommoditiesTrading|Desk",
                1: "ERA_CommoditiesTrading|Desk",
                2: "ERA_CommoditiesTrading|Desk",
                3: "ERA_CommoditiesTrading|Desk",
                4: "ERA_TradingSomething|Desk",
            },
            "metadata.source.fileInfo.location.bucket": {
                0: "foo.steeleye.co",
                1: "foo.steeleye.co",
                2: "foo.steeleye.co",
                3: "foo.steeleye.co",
                4: "foo.steeleye.co",
            },
            "metadata.source.fileInfo.location.key": {
                0: "folder/file.xml",
                1: "folder/file.xml",
                2: "folder/file.xml",
                3: "folder/file.xml",
                4: "folder/file.xml",
            },
            "sourceKey": {
                0: "s3://foo.steeleye.co/folder/file.xml",
                1: "s3://foo.steeleye.co/folder/file.xml",
                2: "s3://foo.steeleye.co/folder/file.xml",
                3: "s3://foo.steeleye.co/folder/file.xml",
                4: "s3://foo.steeleye.co/folder/file.xml",
            },
            "roomId": {
                0: "ERA_CommoditiesTrading|Desk",
                1: "ERA_CommoditiesTrading|Desk",
                2: "ERA_CommoditiesTrading|Desk",
                3: "ERA_CommoditiesTrading|Desk",
                4: "ERA_TradingSomething|Desk",
            },
            "roomName": {
                0: "ERA_CommoditiesTrading|Desk",
                1: "ERA_CommoditiesTrading|Desk",
                2: "ERA_CommoditiesTrading|Desk",
                3: "ERA_CommoditiesTrading|Desk",
                4: "ERA_TradingSomething|Desk",
            },
            "chatType": {
                0: "Desk Message",
                1: "Desk Message",
                2: "Desk Message",
                3: "Desk Message",
                4: "Desk Message",
            },
            "timestamps.localTimestampEnd": {
                0: "2024-05-08T01:51:56.000000Z",
                1: "2024-05-08T01:53:14.000000Z",
                2: "2024-05-08T01:53:47.000000Z",
                3: "2024-05-08T02:46:52.000000Z",
                4: "2024-05-08T01:51:56.000000Z",
            },
            "timestamps.localTimestampStart": {
                0: "2024-05-08T01:51:56.000000Z",
                1: "2024-05-08T01:53:14.000000Z",
                2: "2024-05-08T01:53:47.000000Z",
                3: "2024-05-08T02:46:52.000000Z",
                4: "2024-05-08T01:51:56.000000Z",
            },
            "timestamps.timestampEnd": {
                0: "2024-05-08T01:51:56.000000Z",
                1: "2024-05-08T01:53:14.000000Z",
                2: "2024-05-08T01:53:47.000000Z",
                3: "2024-05-08T02:46:52.000000Z",
                4: "2024-05-08T01:51:56.000000Z",
            },
            "timestamps.timestampStart": {
                0: "2024-05-08T01:51:56.000000Z",
                1: "2024-05-08T01:53:14.000000Z",
                2: "2024-05-08T01:53:47.000000Z",
                3: "2024-05-08T02:46:52.000000Z",
                4: "2024-05-08T01:51:56.000000Z",
            },
            "metadata.messageId": {
                0: "<EMAIL>|2024-05-08T01:51:56.000000Z",
                1: "<EMAIL>|2024-05-08T01:53:14.000000Z",
                2: "<EMAIL>|2024-05-08T01:53:47.000000Z",
                3: "<EMAIL>|2024-05-08T02:46:52.000000Z",
                4: "<EMAIL>|2024-05-08T01:51:56.000000Z",
            },
        }
    )


@pytest.fixture()
def expected_chat_event_df():
    return pd.DataFrame(
        data={
            "eventType": {
                0: "ENTERED",
                1: "ENTERED",
                2: "ENTERED",
                3: "ENTERED",
                4: "ENTERED",
                5: "ENTERED",
                6: "LEFT",
            },
            "identifiers.allIds": {
                0: ["<EMAIL>"],
                1: ["<EMAIL>"],
                2: ["<EMAIL>"],
                3: ["<EMAIL>"],
                4: ["<EMAIL>"],
                5: ["<EMAIL>"],
                6: ["<EMAIL>"],
            },
            "identifiers.fromId": {
                0: "<EMAIL>",
                1: "<EMAIL>",
                2: "<EMAIL>",
                3: "<EMAIL>",
                4: "<EMAIL>",
                5: "<EMAIL>",
                6: "<EMAIL>",
            },
            "identifiers.fromUserId": {
                0: "jhaz",
                1: "dste17",
                2: "ama",
                3: "dsteph",
                4: "smang",
                5: "dgallagh",
                6: "dgallagh",
            },
            "metadata.source.client": {
                0: "ICE Chat",
                1: "ICE Chat",
                2: "ICE Chat",
                3: "ICE Chat",
                4: "ICE Chat",
                5: "ICE Chat",
                6: "ICE Chat",
            },
            "metadata.source.fileInfo.location.bucket": {
                0: "foo.steeleye.co",
                1: "foo.steeleye.co",
                2: "foo.steeleye.co",
                3: "foo.steeleye.co",
                4: "foo.steeleye.co",
                5: "foo.steeleye.co",
                6: "foo.steeleye.co",
            },
            "metadata.source.fileInfo.location.key": {
                0: "folder/file.xml",
                1: "folder/file.xml",
                2: "folder/file.xml",
                3: "folder/file.xml",
                4: "folder/file.xml",
                5: "folder/file.xml",
                6: "folder/file.xml",
            },
            "metadata.threadId": {
                0: "Socar / GFI JKM|Conference",
                1: "Socar / GFI JKM|Conference",
                2: "Spectron - Socar|Conference",
                3: "Spectron - Socar|Conference",
                4: "Spectron - Socar|Conference",
                5: "Prebon/SOCAR LNG|Conference",
                6: "Prebon/SOCAR LNG|Conference",
            },
            "sourceKey": {
                0: "s3://foo.steeleye.co/folder/file.xml",
                1: "s3://foo.steeleye.co/folder/file.xml",
                2: "s3://foo.steeleye.co/folder/file.xml",
                3: "s3://foo.steeleye.co/folder/file.xml",
                4: "s3://foo.steeleye.co/folder/file.xml",
                5: "s3://foo.steeleye.co/folder/file.xml",
                6: "s3://foo.steeleye.co/folder/file.xml",
            },
            "roomId": {
                0: "Socar / GFI JKM|Conference",
                1: "Socar / GFI JKM|Conference",
                2: "Spectron - Socar|Conference",
                3: "Spectron - Socar|Conference",
                4: "Spectron - Socar|Conference",
                5: "Prebon/SOCAR LNG|Conference",
                6: "Prebon/SOCAR LNG|Conference",
            },
            "timestamps.localTimestampEnd": {
                0: "2022-02-10T02:50:00.000000Z",
                1: "2022-02-10T02:50:00.000000Z",
                2: "2022-02-10T02:50:00.000000Z",
                3: "2022-02-10T02:50:00.000000Z",
                4: "2022-02-10T02:50:00.000000Z",
                5: "2022-02-10T02:50:00.000000Z",
                6: "2022-02-10T02:50:15.000000Z",
            },
            "timestamps.localTimestampStart": {
                0: "2022-02-10T02:50:00.000000Z",
                1: "2022-02-10T02:50:00.000000Z",
                2: "2022-02-10T02:50:00.000000Z",
                3: "2022-02-10T02:50:00.000000Z",
                4: "2022-02-10T02:50:00.000000Z",
                5: "2022-02-10T02:50:00.000000Z",
                6: "2022-02-10T02:50:15.000000Z",
            },
            "timestamps.timestampEnd": {
                0: "2022-02-10T02:50:00.000000Z",
                1: "2022-02-10T02:50:00.000000Z",
                2: "2022-02-10T02:50:00.000000Z",
                3: "2022-02-10T02:50:00.000000Z",
                4: "2022-02-10T02:50:00.000000Z",
                5: "2022-02-10T02:50:00.000000Z",
                6: "2022-02-10T02:50:15.000000Z",
            },
            "timestamps.timestampStart": {
                0: "2022-02-10T02:50:00.000000Z",
                1: "2022-02-10T02:50:00.000000Z",
                2: "2022-02-10T02:50:00.000000Z",
                3: "2022-02-10T02:50:00.000000Z",
                4: "2022-02-10T02:50:00.000000Z",
                5: "2022-02-10T02:50:00.000000Z",
                6: "2022-02-10T02:50:15.000000Z",
            },
        }
    )
