import boto3
import logging
import math
import pytest
import shutil
from aries_se_comms_tasks.recording.extract_segment_from_recording import (
    InvalidDurationException,
    InvalidOffsetException,
    run_extract_segment_from_recording,
)
from dataclasses import dataclass
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from moto import mock_aws
from pathlib import Path
from pydub import AudioSegment
from se_io_utils.tempfile_utils import tmp_directory

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
DATA_PATH = Path(__file__).parent.joinpath("data")

LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)
MP3_SAMPLE_PATH: Path = LOCAL_BUCKET_PATH.joinpath("media", "audio-sample.mp3")
MP4_SAMPLE_PATH: Path = LOCAL_BUCKET_PATH.joinpath("media", "audio-sample.mp4")
WAV_SAMPLE_PATH: Path = LOCAL_BUCKET_PATH.joinpath("media", "audio-sample.wav")
OPUS_SAMPLE_PATH: Path = LOCAL_BUCKET_PATH.joinpath("media", "audio-sample.opus")


mock_aiobotocore_convert_to_response_dict()


@dataclass
class ExtractCase:
    offset: int | float
    duration: int | float
    expected_name: str
    path: Path


class TestExtractSegmentFromRecording:
    @pytest.mark.parametrize(
        "case",
        [
            ExtractCase(
                offset=0,
                duration=2,
                expected_name="audio-sample_segment_0_2000.mp3",
                path=MP3_SAMPLE_PATH,
            ),
            ExtractCase(
                offset=1,
                duration=1.5,
                expected_name="audio-sample_segment_1000_2500.mp4",
                path=MP4_SAMPLE_PATH,
            ),
            ExtractCase(
                offset=1.75,
                duration=2.5,
                expected_name="audio-sample_segment_1750_4250.wav",
                path=WAV_SAMPLE_PATH,
            ),
            ExtractCase(
                offset=2.1,
                duration=1,
                expected_name="audio-sample_segment_2100_3100.opus",
                path=OPUS_SAMPLE_PATH,
            ),
        ],
    )
    def test_it_can_extract_segment_from_recording_from_local_file(self, caplog, case: ExtractCase):
        caplog.set_level(logging.INFO)

        tmp_dir: Path = tmp_directory()

        # copy file to tmp directory
        sample_path = shutil.copy(case.path, tmp_dir)

        result = run_extract_segment_from_recording(
            path=sample_path,
            offset=case.offset,
            duration=case.duration,
            tmp_dir=tmp_dir,
        )

        assert result.exists()
        assert result.name == case.expected_name

        duration = AudioSegment.from_file(result).duration_seconds
        # duration does not match exactly, but it should be close enough
        # as it may fluctuate due to the conversion process
        assert math.isclose(duration, case.duration, abs_tol=0.25), (
            f"{duration} and {case.duration} are not close enough"
        )

        shutil.rmtree(tmp_dir)

    @mock_aws
    def test_it_can_extract_segment_from_recording_from_remote_file(self, caplog):
        caplog.set_level(logging.INFO)

        tmp_dir: Path = tmp_directory()

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        result = run_extract_segment_from_recording(
            path=f"s3://{BUCKET_NAME}/media/audio-sample.mp3",
            offset=0,
            duration=2.5,
            tmp_dir=tmp_dir,
        )

        assert result.exists()
        assert result.name == "audio-sample_segment_0_2500.mp3"

        shutil.rmtree(tmp_dir)

    def test_it_raises_an_exception_when_offset_is_negative(self, caplog):
        caplog.set_level(logging.INFO)

        tmp_dir: Path = tmp_directory()

        # copy file to tmp directory
        opus_sample_path = shutil.copy(OPUS_SAMPLE_PATH, tmp_dir)

        with pytest.raises(InvalidOffsetException) as e:
            run_extract_segment_from_recording(
                path=opus_sample_path,
                offset=-1,
                duration=3,
                tmp_dir=tmp_dir,
            )

        assert str(e.value) == "Offset cannot be negative: -1"

        shutil.rmtree(tmp_dir)

    def test_it_can_extract_segment_from_recording_when_duration_is_greater_than_file_duration(
        self, caplog
    ):
        """This test case will test ensures that:

        - even though original file contains around 4.5 seconds of audio, the extraction
        will not fail if the duration is set to 6 seconds, it will simply get the audio
        till the end of the file.
        - it can handle different audio formats like mp3, mp4, wav, opus
        """

        caplog.set_level(logging.INFO)

        tmp_dir: Path = tmp_directory()

        # copy file to tmp directory
        sample_path = shutil.copy(OPUS_SAMPLE_PATH, tmp_dir)

        result = run_extract_segment_from_recording(
            path=sample_path,
            offset=0,
            duration=6,
            tmp_dir=tmp_dir,
        )

        assert result.exists()
        assert result.name == "audio-sample_segment_0_6000.opus"

        shutil.rmtree(tmp_dir)

    @pytest.mark.parametrize("duration", [-1, 0])
    def test_it_raises_an_exception_when_duration_has_invalid_value(self, caplog, duration: int):
        caplog.set_level(logging.INFO)

        tmp_dir: Path = tmp_directory()

        # copy file to tmp directory
        opus_sample_path = shutil.copy(OPUS_SAMPLE_PATH, tmp_dir)

        with pytest.raises(InvalidDurationException) as e:
            run_extract_segment_from_recording(
                path=opus_sample_path,
                offset=0,
                duration=duration,
                tmp_dir=tmp_dir,
            )

        assert str(e.value) == f"Duration must be greater than 0. Got: {duration}"

        shutil.rmtree(tmp_dir)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
