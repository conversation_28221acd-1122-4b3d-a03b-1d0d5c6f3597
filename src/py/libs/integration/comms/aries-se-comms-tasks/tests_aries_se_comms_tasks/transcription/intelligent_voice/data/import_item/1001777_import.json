{"error_code": 0, "file": "/var/jumpto/cache/1/1001777/original-recording.wav", "id": 97518, "importMetadata": {"error_code": 0, "filetype": 4, "id": 97516, "import_id": 97560, "items_count": 1}, "item_id": 1001777, "linenumber": 0, "metadata_id": 97516, "processingOptions": {"asrOptions": {"beamSearchBeamSize": 128, "beamSize": 15.0, "bertPunctuator": "OFF", "cpuNumThreads": 1, "frameShift": 2, "fusion": 0.83, "fusionLang": 0.85, "gpuProcesses": 3, "inverseTextNormalization": "OFF", "languageDetection": "COUNT", "latticeBeam": 4.0, "maxActive": 7000, "minLangWords": 5, "minSegment": 12, "onlinebin": "ON", "phoneticDecoding": "OFF", "phoneticLattice": "OFF", "rescoring": "ON", "retainData": false, "secondPass": "ON", "suppressTokens": "OFF", "tritonPunctuatorModelId": 10}, "crackerOptions": {"retainData": false}, "diarizationOptions": {"biometricsEnabled": false, "diarizationEnabled": true, "retainData": false}, "dynamicNormalizationEnabled": false, "models": [{"id": 66, "modelFullName": "IntelligentVoice_en-001_16kHz_24_trader_V1_NASRv5.1"}, {"id": 77, "modelFullName": "IntelligentVoice_es-001_16kHz_24_general_V1_NASRv5.1"}, {"id": 63, "modelFullName": "IntelligentVoice_x-languageid_16kHz_2_en.es_V1.1_NASRv2"}], "ocrOptions": {"ocrEnabled": false, "retainData": false}, "sendImportNotifications": false, "sentimentOptions": {"tritonSentimentModelId": 7}, "summarisationOptions": {"retainData": false, "summarisationEnabled": true}, "taggerOptions": {"retainData": false, "taggerEnabled": false}, "treatAsSingleChannel": true, "vadOptions": {"retainData": false}}, "processing_end": "2023-08-24T12:56:27.000Z", "processing_start": "2023-08-24T12:31:24.000Z"}