import base64
import cgi
import datetime
import email
import fsspec
import logging
import nanoid
import pandas as pd
import quopri
import re
from aries_se_comms_tasks.feeds.email.app_metrics_enum import EmailTransformAppMetricsEnum
from aries_se_comms_tasks.feeds.email.transform.config.security_checks import (
    SecurityChecksConfiguration,
    SecurityChecksConfigurationModel,
)
from aries_se_comms_tasks.feeds.email.transform.eml_header_security_checks import (
    EmlFailed<PERSON>ustomHeaderCheck,
    EmlFailedSpamCheck,
    EmlFailedVirusCheck,
    check_if_eml_is_a_virus,
    check_if_eml_is_spam,
    check_if_eml_matches_client_custom_header,
)
from aries_se_comms_tasks.feeds.email.transform.static import (
    AttachmentColumns,
    BpVodaphoneO365Email,
    CloudPrefixToSourceClientMappings,
    DevColumns,
    EmailColumns,
    IdentifiersKeys,
    Models,
)
from aries_se_comms_tasks.feeds.email.transform.utils import (
    InvalidEmailIDException,
    encode_headers,
    format_date,
    get_charset_with_override,
    get_email_address_domain,
    is_empty,
    listify_and_split,
    msg_to_string,
    normalize_email_id,
    process_mail_id_headers,
    split_email_identifier,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.io.utils import FileInfo, get_file_info
from aries_se_core_tasks.utilities.data_utils import get_text_from_html, object2string, string
from aries_se_core_tasks.utilities.s3_utils import (
    S3Response,
    get_es_model_bucket_key_from_url,
    get_s3_client,
)
from aries_task_link.models import AriesTaskInput
from collections import defaultdict
from email.header import decode_header
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from pathlib import Path
from se_boltons.decorators import retry
from se_data_lake.cloud_utils import get_cloud_provider_prefix
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_attachments
from se_elastic_schema.models import Email
from se_enums.cloud import CloudProviderEnum
from typing import Any, Dict, List, Optional, Union

logger_ = logging.getLogger(__name__)

REGEX_EMAIL_ADDRESS = re.compile(
    r"<?(([.0-9a-z_+-=]+)@(([0-9a-z-]+\.)+[0-9a-z]{2,9}))>?", re.M | re.S | re.I
)
REGEX_FIRST_ID = re.compile(r"[a-zA-Z0-9\.]+@[a-zA-Z0-9\.]+[^@\.]")
REPLY_TAGS = [
    "RE:",
    "Re:",
    "re:",
    "FWD:",
    "Fwd:",
    "fwd:",
    "FW:",
    "Fw:",
    "fw:",
    "DELIVERY FAILURE:",
    "Read:",
    "Not read:",
    "AW:",
    "Aw:",
    "aw:",
    "WG:",
    "Wg:",
    "wg:",
    "TR:",
    "Tr:",
    "tr:",
]

TRADE_FILE_KEY_PREFIX = "feeds/eml/trade/"


class FeatureFlag:
    ADDITIONAL_COMMS_IDS = "additionalCommsIdsEnabled"


SURVEILLANCE_IGNORE_FILE_FLAG = "resource/eml/ignore_surv"

CONTENT_TYPE_MAPPING = {
    None: "HTML",
    "text/html": "HTML",
    "text/x-server-parsed-html": "HTML",
    "text/calendar": "HTML",
    "text/richtext": "HTML",
    "text/enriched": "HTML",
    "text/css": "HTML",
    "application/rtf": "RTF",
    "application/x-rtf": "RTF",
    "text/plain": "PLAIN",
    "application/plain": "PLAIN",
}

IDENTIFIER_TO_HEADER_MAPPINGS = dict(
    fromId="From",
    toIds="To",
    ccIds="Cc",
    bccIds="Bcc",
    onBehalfOf="Behalf",
)


class FailIfSourceFrameIsEmpty(TaskException):
    pass


class FailIfNoEmails(TaskException):
    pass


class FailIfMissingColumns(TaskException):
    pass


class FailIfMandatoryAttributesAreNull(TaskException):
    pass


class FailIfAllAttachmentsFailedToBeUploaded(TaskException):
    pass


class TransformEml(IntegrationTask):
    """transform raw eml file content to email information."""

    def __init__(
        self,
        aries_task_input: AriesTaskInput,
        tenant_bucket: str,
        cloud_provider: CloudProviderEnum,
        header_column_list: List[str] | None = None,
        force_decode_attachments: bool = False,
        **kwargs,
    ):
        """Initialize the transform eml task. The security check configuration
        class is a singleton, thus the first time it is called it will be
        initialized and the configuration will be preloaded for future
        executions.

        :param workflow_name: Name of the Conductor Workflow running the task
        :param tenant_bucket: Tenant bucket name i.e. s3://pinafore.dev.steeleye.co
        """
        super().__init__(**kwargs)
        self.aries_task_input = aries_task_input
        self.security_check_configuration: SecurityChecksConfigurationModel = (
            SecurityChecksConfiguration(
                workflow_name=self.aries_task_input.workflow.name, tenant_bucket=tenant_bucket
            ).configuration
        )
        self.cloud_provider = cloud_provider

        # Populate member variable `header_column_list` which is used
        # to fetch data from raw email header. Remove duplicates from this list if any
        self.header_column_list = header_column_list if header_column_list else []
        self.header_column_list.append("From")
        self.header_column_list.append("from")
        self.header_column_list = list(set(self.header_column_list))

        # If this is set to True we force decode all the base64 encoded
        # attachments of the email
        self.force_decode_attachments = force_decode_attachments

        # List of emails (file paths) that failed transform
        self.emails_that_failed_transform_and_error_message: Dict[str, str] = {}

        # List of emails that were skipped due to being marked as spam
        self.skipped_spam_emails: List[str] = []
        self.skipped_spam_emails_message: str = ""

        # List of emails that were skipped due to being marked as a virus
        self.skipped_virus_emails: List[str] = []
        self.skipped_virus_emails_message: str = ""

        self.total_attachment_count = 0
        self.failed_to_upload_attachments_for_urls: List[str] = []

        # List of emails that were skipped due to failing a custom header check
        self.skipped_custom_header_emails: List[str] = []
        self.skipped_custom_header_emails_message: str = ""

    @property
    def spam_check_is_enabled(self) -> bool:
        return self.security_check_configuration.spam

    @property
    def virus_check_is_enabled(self) -> bool:
        return self.security_check_configuration.virus

    @property
    def custom_header_check(self) -> Dict[str, str | List]:
        return self.security_check_configuration.custom_header_check

    def _run(
        self,
        pre_processed_df: pd.DataFrame,
        download_eml_df: pd.DataFrame,
    ) -> pd.DataFrame:
        return self.process(pre_processed_df=pre_processed_df, download_eml_df=download_eml_df)

    def process(
        self, pre_processed_df: pd.DataFrame, download_eml_df: pd.DataFrame
    ) -> pd.DataFrame:
        if pre_processed_df.empty or download_eml_df.empty:
            raise FailIfSourceFrameIsEmpty("Cannot execute the task with empty inputs")

        source_frame = pd.concat([pre_processed_df, download_eml_df], axis=1)
        # `download_eml_df` may contain rows for which we failed to download the eml content
        # those rows and the associated eml files have already been audited in `DownloadEmlFile`
        # thus we want to discard them here, as there is no point in trying to transform them,
        # and we don't want to risk auditing them twice affecting the audit metrics
        source_frame.dropna(subset=[DevColumns.LOCAL_FILE_PATH], inplace=True)

        target_frame = pd.DataFrame(index=source_frame.index)
        target_frame[DevColumns.EMAIL] = pd.NA

        required_columns = [
            DevColumns.FILE_URL,
            DevColumns.LOCAL_FILE_PATH,
            DevColumns.CLOUD_RESPONSE,
            DevColumns.TENANT_CONFIGURATION,
        ]
        missing_columns = [i for i in required_columns if i not in source_frame.columns]
        if missing_columns:
            error = f"missing following columns in source frame - {missing_columns}"
            logger_.error(error)
            raise FailIfMissingColumns(error)

        if source_frame.empty:
            raise FailIfSourceFrameIsEmpty("Received empty source frame")

        mask = (
            source_frame[DevColumns.FILE_URL].notnull()
            & source_frame[DevColumns.LOCAL_FILE_PATH].notnull()
            & source_frame[DevColumns.CLOUD_RESPONSE].notnull()
            & source_frame[DevColumns.TENANT_CONFIGURATION].notnull()
        )

        if not mask.any():
            error_msg = (
                "Failed to transform emails since null "
                "values in mandatory attributes of source frame"
            )
            logger_.exception(error_msg)
            raise FailIfMandatoryAttributesAreNull(error_msg)

        target_frame.loc[mask, DevColumns.EMAIL] = source_frame.loc[mask].apply(  # type: ignore
            lambda x: self._transform_eml(record=x), axis=1
        )

        # Audit emails that errored or were skipped in this Task
        self.audit_excluded_records()

        if self.total_attachment_count and (
            self.total_attachment_count - len(self.failed_to_upload_attachments_for_urls) == 0
        ):
            fail_message = "Upload of all attachments failed"
            logger_.exception(fail_message)
            raise FailIfAllAttachmentsFailedToBeUploaded(fail_message)

        if self.failed_to_upload_attachments_for_urls:
            # We do not update app metrics or audits for errored attachments
            # because the ProcessEmails Task will reconcile all Email attachments with the result
            # of this task, therefore we do not want to count them twice
            # Partial Fail Audit
            logger_.warning(
                f"Attachment files could not be uploaded for the"
                f" following file_urls: {self.failed_to_upload_attachments_for_urls}"
            )

        return target_frame.dropna()

    def audit_excluded_records(self) -> None:
        """Audit emails that failed transform or were skipped due to spam,
        virus or custom header check."""

        # Audit EMLs that failed transformation
        if self.emails_that_failed_transform_and_error_message:
            self.update_app_metrics(
                field=GenericAppMetricsEnum.ERRORED_COUNT,
                value=len(self.emails_that_failed_transform_and_error_message),
            )
            upsert_audit(
                audit_path=self.audit_path,
                streamed=True,
                input_data={
                    eml_url: {
                        AuditorStaticFields.ERRORED: 1,
                        AuditorStaticFields.STATUS: [
                            self.emails_that_failed_transform_and_error_message[eml_url]
                        ],
                    }
                    for eml_url in self.emails_that_failed_transform_and_error_message
                },
                models=[Email],
            )

        # Audit EMLs that were skipped due to spam, virus or custom header check
        for eml_list, metric, error_message in [
            (
                self.skipped_spam_emails,
                EmailTransformAppMetricsEnum.SKIPPED_IF_SPAM_COUNT,
                self.skipped_spam_emails_message,
            ),
            (
                self.skipped_virus_emails,
                EmailTransformAppMetricsEnum.SKIPPED_IF_VIRUS_COUNT,
                self.skipped_virus_emails_message,
            ),
            (
                self.skipped_custom_header_emails,
                EmailTransformAppMetricsEnum.SKIPPED_IF_FAILED_CUSTOM_CHECK_COUNT,
                self.skipped_custom_header_emails_message,
            ),
        ]:
            if eml_list:
                self.update_app_metrics(field=metric, value=len(eml_list))
                upsert_audit(
                    audit_path=self.audit_path,
                    streamed=True,
                    input_data={
                        eml_url: {
                            AuditorStaticFields.SKIPPED: 1,
                            AuditorStaticFields.STATUS: [error_message],
                        }
                        for eml_url in eml_list
                    },
                    models=[Email],
                )

    def _transform_eml(self, record: "pd.Series[Any]") -> Optional[Dict[str, Any]]:
        """gets pandas series with file details and returns transformed email
        content.

        :param record: dataframe row
        :return: dict
        """
        try:
            email_content = self._process_email(
                file_url=record[DevColumns.FILE_URL],
                email_local_path=record[DevColumns.LOCAL_FILE_PATH],
                cloud_response=record[DevColumns.CLOUD_RESPONSE],
                realm=record[DevColumns.REALM],
            )
        # Email is marked as spam
        except EmlFailedSpamCheck as e:
            self.skipped_spam_emails.append(record[DevColumns.FILE_URL])
            self.skipped_spam_emails_message = e.message
            return pd.NA  # type: ignore

        # Email is marked as a virus
        except EmlFailedVirusCheck as e:
            self.skipped_virus_emails.append(record[DevColumns.FILE_URL])
            self.skipped_virus_emails_message = e.message
            return pd.NA  # type: ignore

        # Email failed custom header check provided by the client
        except EmlFailedCustomHeaderCheck as e:
            self.skipped_custom_header_emails.append(record[DevColumns.FILE_URL])
            self.skipped_custom_header_emails_message = e.message
            return pd.NA  # type: ignore

        # Email failed to transform due to an unforeseen error
        except Exception as e:
            error_message = f"Error processing email: {str(e)}"
            self.emails_that_failed_transform_and_error_message[record[DevColumns.FILE_URL]] = (
                error_message
            )
            logger_.exception(error_message)
            email_content = pd.NA  # type: ignore

        return email_content

    def _process_email(
        self, file_url: str, email_local_path: str, cloud_response, realm: str
    ) -> Dict[str, Any]:
        # file_url is complete url, for example - s3://bucket/path/file.ext
        bucket, key = get_es_model_bucket_key_from_url(file_url)

        file_info = self.eml_file_info(bucket, key, cloud_response)

        _email = self.transform(email_local_path=email_local_path, email_key=key, realm=realm)

        _email = self.append_fileinfo_to_email(_email, file_info)

        _email = append_aggregate_identifiers(_email)

        return _email

    def eml_file_info(
        self, bucket: str, key: str, metadata: FileInfo | Dict[str, Any]
    ) -> Dict[str, Any]:
        """parse response and get version id, content length and return file
        info dict.

        :param bucket: bucket
        :param key: key
        :param metadata: response for get object
        :return: dict
        """
        if self.cloud_provider == CloudProviderEnum.AWS:
            last_modified = metadata.get(S3Response.LAST_MODIFIED)  # type: ignore
            if last_modified:
                last_modified = last_modified.strftime(EmailColumns.TIMESTAMP_FORMAT)
            file_info = dict(
                location=dict(bucket=bucket, key=key),
                lastModified=last_modified,
                versionId=metadata.get(S3Response.VERSION_ID),  # type: ignore
                contentLength=metadata.get(S3Response.CONTENT_LENGTH),  # type: ignore
            )
        else:
            last_modified = metadata.last_modified  # type: ignore
            if last_modified:
                last_modified = last_modified.strftime(EmailColumns.TIMESTAMP_FORMAT)
            file_info = dict(
                location=dict(bucket=bucket, key=key),
                lastModified=last_modified,
                contentLength=metadata.size,  # type: ignore
            )
        return file_info

    @staticmethod
    def append_fileinfo_to_email(
        _email: Dict[str, Any], file_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        _email["metadata"]["source"]["fileInfo"] = file_info
        return _email

    def transform(self, email_local_path: str, email_key: str, realm: str) -> Dict[str, Any]:
        msg: SEMessage = email.message_from_binary_file(  # type: ignore
            open(email_local_path, "rb"), _class=SEMessage
        )

        # https://steeleye.atlassian.net/browse/EU-10709
        # Security check must run before unpack_office_journal_envelope since
        # many SES related headers which may contain headers critical to security checks
        # gets dropped during unpack_office_journal_envelope.
        self.apply_security_checks_on_header(msg=msg)

        # EU-9341: This field is added specifically for Citic as they follow different formatting
        # We would be displaying journal_msg_id if it's available otherwise we switch back to
        # msg_id (Defined below)
        # Update 2: Identified that journal_msg_id field can exist for different clients too, so
        # metadata.messageId would contain journal_msg_id (If available)
        journal_msg_id = msg.header_string("Message-ID")
        if isinstance(journal_msg_id, str):
            journal_msg_id = normalize_email_id(journal_msg_id)

        if msg.is_exchange_journal():
            msg = msg.unpack_office_journal_envelope()

        # https://steeleye.atlassian.net/browse/ERD-626
        # replace any dots in header fields with hyphen as schema type of header
        # is "filed_name : Primitive" and dots in field name are treated as composite fields
        header = get_headers(message=msg)

        identifiers = msg.get_identifiers()

        timestamps = msg.get_timestamps()

        body, attachments = msg.extract_body_attachment(
            force_decode_attachments=self.force_decode_attachments
        )

        self.upload_and_create_attachment_component(
            attachments=attachments, email_key=email_key, realm=realm
        )

        msg_size = msg.header_string("x-ms-exchange-organization-originalsize", None)

        msg_size = int(msg_size) if is_int(msg_size) else msg_size  # type: ignore

        msg_id = msg.header_string("message-id")
        if isinstance(msg_id, str):
            msg_id = normalize_email_id(msg_id)

        identifiers = msg.populate_header_values_in_identifieres(
            identifiers=identifiers, headers=header
        )

        subject = msg.header_string("subject")

        client = msg.get_client(email_key=email_key, identifiers=identifiers)

        thread_id = msg.get_thread_id()

        # By default, we only fetch `From` header
        header_data = {col: header.get(col) for col in self.header_column_list if header.get(col)}

        return dict(
            messageId=msg_id,
            inReplyToMessageId=msg.in_reply_to_msg_id(),
            referenceMessageIds=msg.ref_msg_ids(),
            subject=subject,
            body=body,
            attachments=attachments,
            hasAttachment=len(attachments) > 0,
            identifiers=identifiers,
            metadata=dict(
                messageId=journal_msg_id or msg_id,
                encodingType=get_charset_with_override(msg.get_content_charset()),  # type: ignore
                threadId=thread_id,
                sizeInBytes=msg_size,
                header=header_data,
                source=dict(
                    client=client,
                ),
            ),
            timestamps=timestamps,
            sourceKey=self.aries_task_input.input_param.params.get("file_uri"),
        )

    # def apply_security_checks_on_header(self, header: Dict[str, str]) -> None:
    def apply_security_checks_on_header(self, msg) -> None:
        header = get_headers(msg)
        if not header:
            return

        if self.spam_check_is_enabled:
            check_if_eml_is_spam(header=header)

        if self.virus_check_is_enabled:
            check_if_eml_is_a_virus(header=header)

        check_if_eml_matches_client_custom_header(
            header=header, custom_header_check=self.custom_header_check
        )

    def upload_and_create_attachment_component(self, attachments: List, email_key: str, realm: str):
        for attachment in attachments:
            self._apply_func(email_key=email_key, attachment=attachment, realm=realm)

    def _apply_func(self, email_key: str, attachment: dict, realm: str):
        """Function which gets the attachment key and then uploads attachments.

        :param email_key: path of the email
        :param attachment: attachment to upload
        :param realm: realm
        :return None, the attachment is updated in place
        """
        file_key = None
        self.total_attachment_count += 1
        try:
            # Removes any special characters from the filename
            # Being done to avoid any issues while uploading the file to cloud
            # because of special characters limitations of cloud storage
            original_filename = attachment[AttachmentColumns.FILENAME].rstrip(".")
            # rstrip is needed as some cloud systems do not accept dot-trailing file names

            filename = Path(original_filename).stem
            extension = Path(original_filename).suffix

            # It will only keep alphanumeric characters, hyphens, underscores, open and
            # close parenthesis, dots and whitespaces, everything else will be removed.
            # note: this will also work for foreign characters as they are not removed
            filename = re.sub(r"[^\w\d\-_.() ]", "", filename) + extension

            file_key = self._get_attachment_key(
                email_file_name=Path(email_key).name,
                attachment_file_name=filename,
            )
            self._process_attachment(file_key=file_key, attachment=attachment, realm=realm)
            # Since the attachment is now uploaded, the content can now safely be discarded from mem
            attachment.pop(AttachmentColumns.CONTENT)
        except Exception as e:
            error_message = (
                f"Failed to upload attachment to {self.cloud_provider} Realm: "
                f"{realm}, Key: {file_key} failed with {e}"
            )
            # Add the source file url to failed_to_map_urls dict
            self.failed_to_upload_attachments_for_urls.append(
                f"{get_cloud_provider_prefix(self.cloud_provider)}{realm}/{email_key}"
            )
            logger_.exception(error_message)

    def _get_attachment_key(self, email_file_name: str, attachment_file_name: str) -> str:
        """get key of attachment for given email.

        :param email_file_name: email file name
        :param attachment_file_name: attachment file name
        :return: key for given attachment
        """

        lake_ingest_path = get_ingress_depository_lake_path_for_attachments(
            workflow_name=self.aries_task_input.workflow.name,
            workflow_trace_id=self.aries_task_input.workflow.trace_id,
            task_io_params=self.aries_task_input.input_param.params,
            workflow_start_timestamp=self.aries_task_input.workflow.start_timestamp,
        )
        return f"{lake_ingest_path}{email_file_name}/{attachment_file_name}"

    @retry(backoff_factor=0.4, max_backoff=1, max_retry=3)
    def _process_attachment(self, file_key: str, attachment: dict, realm) -> None:
        """upload attachment and updates fields in given pandas series.

        :param file_key: key for give attachment
        :param row: row of df with attachment details
        :return: No return value, updated attachment by reference
        """
        if self.cloud_provider == CloudProviderEnum.AWS:
            s3_client = get_s3_client()
            response = s3_client.put_object(
                Body=attachment[AttachmentColumns.CONTENT],
                Bucket=realm,
                Key=file_key,
            )
            attachment[AttachmentColumns.FILEINFO_LOCATION_BUCKET] = realm
            attachment[AttachmentColumns.FILEINFO_LOCATION_KEY] = file_key
            attachment[AttachmentColumns.FILEINFO_CONTENTLENGTH] = attachment[
                AttachmentColumns.SIZEINBYTES
            ]
            attachment[AttachmentColumns.FILEINFO_VERSIONID] = response[S3Response.VERSION_ID]
            attachment[AttachmentColumns.FILEINFO_PROCESSED] = (
                datetime.datetime.utcnow().isoformat()
            )
            attachment[DevColumns.META_MODEL] = Models.ATTACHMENT
        else:
            file_uri = f"{get_cloud_provider_prefix(self.cloud_provider)}{realm}/{file_key}".rstrip(
                "."
            )
            fs, _, _ = fsspec.get_fs_token_paths(file_uri)

            with fs.open(file_uri, "w") as output_file:
                output_file.write(attachment[AttachmentColumns.CONTENT])

            metadata = get_file_info(path=file_uri)
            attachment[AttachmentColumns.FILEINFO_LOCATION_BUCKET] = realm
            attachment[AttachmentColumns.FILEINFO_LOCATION_KEY] = file_key
            attachment[AttachmentColumns.FILEINFO_CONTENTLENGTH] = attachment[
                AttachmentColumns.SIZEINBYTES
            ]
            attachment[AttachmentColumns.FILEINFO_PROCESSED] = metadata.last_modified
            attachment[DevColumns.META_MODEL] = Models.ATTACHMENT


class SEMessage(email.message.Message):
    def header_string(self, header_name: str, default_value=None, encoding="utf-8") -> str:
        """Return formatted header value."""
        header_value = self.get(header_name, failobj=default_value)
        if isinstance(header_value, str):
            header_decoded_parts = email.header.decode_header(header_value)
            header_value, encoding = encode_headers(header_decoded_parts)
        return object2string(header_value, encoding=encoding)  # type: ignore

    def header_string_all(
        self,
        header_name: str,
        default_value=None,
        extract_addresses=False,
        encoding="utf-8",
    ) -> List[str]:
        """Return formatted header value list."""
        header_values = self.get_all(header_name, failobj=default_value)
        return_values = []
        enc = encoding

        for value in header_values:
            header_decoded_parts = email.header.decode_header(value)
            decoded_value = ""
            # each header part may have its own encoding
            for part in header_decoded_parts:
                enc = get_charset_with_override(part[1]) or encoding  # type: ignore
                try:
                    decoded_value += part[0].decode(enc) if isinstance(part[0], bytes) else part[0]

                except LookupError:  # unknown encoding from input email
                    # fallback to parametrized encoding
                    enc = encoding
                    decoded_value += part[0].decode(enc) if isinstance(part[0], bytes) else part[0]

                except UnicodeDecodeError:  # input or parametrized encoding is not correct
                    decoded_value += str(part[0]) if isinstance(part[0], bytes) else part[0]

            value = object2string(decoded_value, encoding=enc)
            if extract_addresses:
                value = ",".join([x.group() for x in re.finditer(REGEX_EMAIL_ADDRESS, value)])
            return_values.append(value)

        return return_values

    def is_exchange_journal(self) -> bool:
        exchange_source = "X-MS-Exchange-Generated-Message-Source"
        if "journal agent" in self.header_string(exchange_source, "").lower():
            return True
        valid_journals = [
            "X-MS-Exchange-Organization-Processed-By-Journaling",
            "X-MS-Exchange-Organization-Journal-Report",
            "X-MS-Journal-Report",
        ]
        for journal in valid_journals:
            if self.get(journal, None) is not None:
                return True
        return False

    def unpack_office_journal_envelope(self):
        try:
            b = self.get_payload(1).get_payload(0).as_bytes()  # type: ignore
            return email.message_from_bytes(b, _class=self.__class__)
        except Exception:
            return self

    @property
    def is_steeleye_app_email(self):
        domain = get_email_address_domain(self.header_string("Return-Path", ""))
        from_id = self.header_string("From", "")
        return "<EMAIL>" == from_id and "amazonses.com" in domain

    def get_identifiers(self):
        # process SteelEye app email
        if self.is_steeleye_app_email:
            addresses = {
                field.upper(): listify_and_split(self.header_string(f"se_{field}_id"))
                for field in "from to cc bcc".split()
            }
            addresses["BEHALF"] = (
                self.header_string("se_on_behalf_of_id") or addresses["FROM"]  # type: ignore
            )

        # process any other email
        # In some cases, we can get [''], we filter out these empty strings
        else:
            addresses = dict(
                TO=list(filter(None, self._lambda_proc_function("to", True))),
                CC=list(filter(None, self._lambda_proc_function("cc", True))),
                BCC=list(
                    filter(
                        None,
                        set(
                            self._lambda_proc_function("bcc", False)
                            + self._lambda_proc_function("X-MS-Exchange-Organization-BCC", False)
                            + self._lambda_proc_function(
                                "X-MS-Exchange-Organization-OriginalEnvelopeRecipients",
                                False,
                            )
                        ),
                    )
                ),
            )
            sender = process_mail_id_headers(self.get_all("Sender") or [])
            from_id = process_mail_id_headers(self.get_all("From") or [])

            if len(sender) == 1 and sender != from_id:
                addresses["FROM"] = sender
                addresses["BEHALF"] = from_id
            else:
                addresses["FROM"] = from_id or sender

        # collect local parts and domains per participant type
        domains = defaultdict(set)
        local_parts = defaultdict(set)
        for participant_type, email_ids in addresses.items():
            for email_id in email_ids:
                try:
                    local_part, domain = split_email_identifier(email_id)
                except InvalidEmailIDException:
                    continue

                local_parts[local_part].add(participant_type)
                domains[domain].add(participant_type)

        local_parts = [
            dict(value=local_part, types=list(participant_types))
            for local_part, participant_types in local_parts.items()  # type: ignore
        ]
        domains = [
            dict(value=domain, types=list(participant_types))
            for domain, participant_types in domains.items()  # type: ignore
        ]

        # format identifiers
        identifiers = dict(
            fromId=addresses.get("FROM")[0] if addresses.get("FROM", []) else None,  # type: ignore
            toIds=addresses.get("TO"),
            ccIds=addresses.get("CC"),
            bccIds=addresses.get("BCC"),
            onBehalfOf=(
                addresses.get("BEHALF")[0] if addresses.get("BEHALF", []) else None  # type: ignore
            ),
            domains=domains,
            localParts=local_parts,
        )

        return identifiers

    def _lambda_proc_function(self, x, extr):
        return process_mail_id_headers(
            self.header_string_all(x, [""], extract_addresses=extr)  # type: ignore
        )

    @staticmethod
    def populate_header_values_in_identifieres(
        identifiers: Dict[str, Any], headers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Populates raw header values in identifers when we do not have
        values.

        :param headers: dict containing headers.
        :param identifiers: dict containing identifiers.

        :returns: modified identifiers dict
        """
        for identifier_key, header_key in IDENTIFIER_TO_HEADER_MAPPINGS.items():
            header_value = headers.get(header_key)
            if not identifiers.get(identifier_key) and header_value:
                identifiers[identifier_key] = (
                    header_value.split(",")
                    if identifier_key not in ["fromId", "onBehalfOf"]
                    else header_value
                )

        return identifiers

    def get_timestamps(self) -> Dict[str, Union[str, None]]:
        if self.is_steeleye_app_email:
            date_start = date_end = self.header_string("se_sent_date", "")
        else:
            date_start = self.header_string("Date")
            date_end = self.header_string("Received", "").split(";")[-1]

        timestamps = dict(
            timestampStart=format_date(date=date_start),
            timestampEnd=format_date(date=date_end),
        )
        return timestamps

    def get_client(self, email_key: str, identifiers: Dict[str, Any]) -> str:
        """Returns the metadata source client based on the email headers/key or
        identifiers.

        :param email_key: The key, which is used to populate the metadata source client
                       in some cases
        :param identifiers: dict containing identifiers. These are used to populate the
                            metadta source client in some cases

        :returns: the metadata source client
        """
        # If prefix matches (startswith) with any from the mappings, use a custom Source Client
        bp_voda_source_client = self._bp_vodaphone_sms_source_client(identifiers=identifiers)
        if bp_voda_source_client:
            return bp_voda_source_client
        prefix_to_source_client_mappings = CloudPrefixToSourceClientMappings.mappings
        if email_key:
            for prefix in prefix_to_source_client_mappings:
                if email_key.startswith(prefix):
                    return prefix_to_source_client_mappings.get(prefix)  # type: ignore
        if "X-Google-DKIM-Signature" in self.items():  # type: ignore
            return "G-Suite"
        # Files in a Symphony ZIP file have the following header
        elif "x-symphony-StreamType" in self.keys():
            return "Symphony Chat"
        else:
            return "Microsoft Exchange"

    @staticmethod
    def _bp_vodaphone_sms_source_client(identifiers: Dict[str, Any]) -> Optional[str]:
        """Returns the source client "O365 – Vodaphone – SMS" based on the
        value of identifiers.toIds and identifiers.fromId. Note that BP want
        this to be 'Vodaphone', not 'Vodafone', so the naming is intentional.

        If the to and from ids are not the required values, None is returned

        :param identifiers: Identifiers dictionary

        :returns The source client "O365 – Vodaphone – SMS"
        based on the values of identifiers.fromId
        and identifiers.toIds OR None.
        """
        from_id = identifiers.get(IdentifiersKeys.FROM_ID)
        if (
            from_id
            and from_id.lower() == BpVodaphoneO365Email.FROM_ID
            and len(
                [
                    id_
                    for id_ in identifiers.get(IdentifiersKeys.TO_IDS)  # type: ignore
                    if id_ and id_.lower() in BpVodaphoneO365Email.TO_IDS
                ]
            )
            > 0
        ):
            return BpVodaphoneO365Email.METADATA_SOURCE_CLIENT

        return None

    def get_thread_id(self):
        thread_id = self.header_string("thread-topic", None)
        if not is_empty(thread_id):
            return thread_id
        subject = self.header_string("subject", "")
        for x in REPLY_TAGS:
            if not subject:
                break
            subject = subject.replace(x, "")
        subject = subject.strip()
        return self.get_msg_id() if is_empty(subject) else subject

    def get_msg_id(self):
        msg_id = self.header_string("message-id")
        if isinstance(msg_id, str):
            msg_id = normalize_email_id(msg_id)
        return msg_id

    def in_reply_to_msg_id(self):
        msg_id = self.header_string("in-reply-to")
        if isinstance(msg_id, str):
            msg_id = normalize_email_id(msg_id)
        return msg_id

    def ref_msg_ids(self):
        msg_ids = self.header_string("references")
        if isinstance(msg_ids, str):
            msg_ids = re.split(r"\s|,|;|<|>", msg_ids)  # type: ignore
            msg_ids = [normalize_email_id(x) for x in msg_ids]  # type: ignore
            msg_ids = [i for i in msg_ids if i != ""]  # type: ignore
        return msg_ids

    def extract_body_attachment(self, force_decode_attachments: bool = False):
        root_content_type = self.get_content_type()
        attachments = list()
        display_type = display_text = None
        hidden_attachment_count = 0

        if root_content_type.startswith("text/"):
            display_type = root_content_type
            display_text = object2string(
                self.get_payload(decode=True),
                encoding=get_charset_with_override(self.get_content_charset()),  # type: ignore
            )

        else:
            if self.is_multipart():
                payloads_to_process = self.get_payload()
            else:
                payloads_to_process = [self]

            # process each child payload item, and update return values incrementally
            for child_item in payloads_to_process:
                result = process_payload_item(
                    root_content_type,
                    child_item,
                    hidden_attachment_count,
                    display_text,
                    force_decode_attachments=force_decode_attachments,
                )
                hidden_attachment_count = result.get("hidden_attachment_count", 0)
                if result["display_type"]:
                    display_type = result["display_type"]
                display_text = result["display_text"]
                attachments += result["attachments"]

        content_type = CONTENT_TYPE_MAPPING[display_type]
        if content_type == "HTML" and display_text:
            text = get_text_from_html(display_text)
        else:
            text = display_text  # type: ignore

        # forcing utf-8 encoding, drops malformed data
        if display_text:
            display_text = force_utf8_encoding(display_text)
        if text:
            text = force_utf8_encoding(text)

        body = dict(displayText=display_text, text=text, type=content_type)

        return body, attachments


def get_headers(message: SEMessage) -> Dict[str, str]:
    """Returns the email Headers and sanitize them if needed. If a header key
    is a Digit then it attempts to extract the key and value from the value
    itself. For example:

        {'0': 'A: B'}
    `A` is the original key and `B` is value.

    See https://steeleye.atlassian.net/browse/EU-5993
    for more information

    :param message: The SEMessage object from which the header should
                    be extracted

    :returns: Header values in dictionary
    """
    header = {key.replace(".", "-"): object2string(value) for key, value in message.items()}

    sanitized_headers = header.copy()
    for key, value in header.items():
        # Apply sanitization only and only when the value is there
        # and key is digit, if value isn't there then it will be
        # dropped in downstream task
        if key.isdigit() and value is not None:
            del sanitized_headers[key]
            data = value.split(":", maxsplit=1)
            sanitized_headers[data[0].strip()] = data[1].strip() if len(data) > 1 else None

    return sanitized_headers  # type: ignore


def process_payload_item(
    root_cnt_type,
    payload_item,
    hidden_attach_count,
    current_text,
    force_decode_attachments: bool = False,
):
    # init return values
    display_type = None
    updated_text = current_text
    attachments: List[Any] = []
    attach_count = hidden_attach_count

    # if it's a text payload item ->
    #   return  updated display type, display text (sometimes this is a noop)
    # if it's a multipart payload item,
    #   incrementally process child payload items, and return single result set
    # if it's an inline/hidden calendar item or mail, or a file,
    #   add to attachments list
    if isinstance(payload_item, str):
        updated_text = empty_if_none(current_text) + payload_item
        return dict(
            hidden_attach_count=attach_count,
            display_type=display_type,
            display_text=updated_text,
            attachments=attachments,
        )

    content_type = (
        payload_item.get_content_type().strip() if payload_item.get_content_type() else ""
    )

    if content_type.startswith("multipart/"):
        payloads = payload_item.get_payload()
        payloads = [payloads] if isinstance(payloads, str) else payloads
        prefer_html = any(payload_type(pl) == "text/html" for pl in payloads)
        for child_item in payloads:
            result = process_payload_item(
                root_cnt_type,
                child_item,
                attach_count,
                updated_text,
                force_decode_attachments=force_decode_attachments,
            )
            attach_count = result.get("hidden_attach_count", 0)
            display_type = (
                display_type if result.get("display_type") is None else result.get("display_type")
            )
            attachments += result.get("attachments")
            if prefer_html and payload_type(child_item) != "text/html":
                # ignore display text from non html payloads
                continue

            updated_text = result.get("display_text")
    else:
        content_disposition = payload_item.get_content_disposition()
        content_disposition = "" if content_disposition is None else content_disposition

        content_id = payload_item.get("content-id")
        if isinstance(content_id, email.header.Header):
            content_id = string(content_id)
        content_id = "" if content_id is None else content_id

        x_content_id = payload_item.get("x-content-id")
        x_content_id = "" if x_content_id is None else x_content_id

        content_id = content_id if content_id.isspace() else x_content_id
        file_name = payload_item.get_param("filename", None, "content-disposition")

        name_param = payload_item.get_param("name", None)
        if name_param:
            # For cases where we have names like =?iso-8859-1?Q?image001.?==?iso-8859-1?Q?png?=,
            # we need to decode the attachment name to get the correct name.
            name_param = decode_attachment_name(encoded_name=name_param)
        if file_name is None:
            file_name = name_param
        if file_name is not None:
            if isinstance(file_name, tuple):
                collapsed_value = email.utils.collapse_rfc2231_value(file_name)
                file_name = collapsed_value.strip() if collapsed_value else file_name

            # For cases where we have names like =?iso-8859-1?Q?image001.?==?iso-8859-1?Q?png?=,
            # we need to decode the attachment name to get the correct name.
            file_name = decode_attachment_name(encoded_name=file_name)
            if (not content_id.isspace()) or content_disposition.startswith("attachment"):
                attachments.append(
                    make_attachment(
                        file_name, payload_item, force_decode_attachments=force_decode_attachments
                    )
                )
        else:
            calendar = "text/calendar"
            csv = "text/csv"
            mail_hdr = "text/rfc822-headers"
            if content_type.startswith("text/") and content_type not in [
                calendar,
                csv,
                mail_hdr,
            ]:
                display_type, updated_text = process_text(root_cnt_type, payload_item, updated_text)
            else:
                file_name = f"Mail_Attachment_{nanoid.generate(size=4)}"
                if hidden_attach_count > 0:
                    file_name += "_" + string(hidden_attach_count)  # type: ignore
                attachments.append(
                    make_attachment(
                        file_name, payload_item, force_decode_attachments=force_decode_attachments
                    )
                )
                attach_count += 1

    return dict(
        hidden_attach_count=attach_count,
        display_type=display_type,
        display_text=updated_text,
        attachments=attachments,
    )


def decode_attachment_name(encoded_name: str) -> str:
    """We get strange attachment names from the source system when there are
    characters like ó in the file names. In these cases, the EML client
    generates a name like.

    =?iso-8859-1?Q?image001.?==?iso-8859-1?Q?png?=

    This causes issues downstream as these filenames are invalid, and
    the files cannot be downloaded from the front-end/from S3.

    This function handles this by fixing the filenames using the
    email library's decode_header function.
    For the above example, the file name becomes image001.png
    after decoding.

    NOTE that this function does not do anything to valid
    file names. So, image003.png stays image003.png

    :param encoded_name: File name (encoded)
    :returns Decoded name without accented characters and without special
    characters such as =? and ?=
    For =?iso-8859-1?Q?image001.?==?iso-8859-1?Q?png?=, the returned
    value is image001.png
    """
    # decode_header returns a list with a single tuple
    try:
        decoded_bytes, charset = decode_header(encoded_name)[0]
        # For valid file names, we can get strings instead of bytes
        if isinstance(decoded_bytes, str):
            return encoded_name
        if charset:
            decoded_name = decoded_bytes.decode(charset)
        else:
            decoded_name = decoded_bytes.decode()
        if not decoded_name:
            decoded_name = encoded_name
    except Exception as e:
        # Broad exception clause used so that we don't fail
        # the flow, and just use the invalid attachment name
        # if anything goes wrong.
        logger_.warning(
            f"Something went wrong while decoding the attachment name."
            f"Using the base attachment name. Exception: {e}"
        )
        decoded_name = encoded_name
    return decoded_name  # type: ignore[no-any-return]


def make_attachment(file_name, part, force_decode_attachments: bool = False):
    """initialise metadata from headers."""
    file_type = file_name.split(".")[-1] if (file_name and "." in file_name) else ""
    encoding = (
        part.get("Content-Transfer-Encoding").lower().strip()
        if part.get("Content-Transfer-Encoding")
        else None
    )
    content_type = part.get_content_type()
    content_type = "" if content_type is None else content_type
    content_disp = part.get_content_disposition()
    cd_value, cd_params = cgi.parse_header(content_disp) if content_disp else (None, dict())
    mime_tag, ct_params = cgi.parse_header(content_type)
    mime_tag = mime_tag.strip() if mime_tag else content_type

    """infer useful info about this attachment"""
    base64_encoded = encoding and "base64" in encoding
    quoted_pri_encoded = encoding and "quoted-printable" in encoding
    needs_base64_encoding = (not encoding) or (not base64_encoded)
    message_object = mime_tag.startswith("message") or content_type.startswith("message")

    """compute content"""
    content = msg_to_string(part) if message_object else part.get_payload()

    if quoted_pri_encoded:
        content = string(quopri.decodestring(content))
    if needs_base64_encoding:
        content = string(base64.b64encode(content.encode("utf-8")), "utf-8")

    # This is required for telemessage email transform where we have
    # call recordings as attachments which are encoded. We need to decode
    # these types of files as downstream transcription cannot work on encoded files.
    if not needs_base64_encoding and force_decode_attachments:
        content = base64.b64decode(content)

    """compute content_len"""
    content_len = (
        int(cd_params.get("size"))  # type: ignore
        if "size" in cd_params
        else (len(content) if content else None)
    )

    """compute mime_tag and file_type"""
    if quoted_pri_encoded:
        mime_tag = "quoted-printable"
    elif "text/calendar" in [mime_tag, content_type]:
        mime_tag = "application/calendar"
        file_type = file_type if file_type else "ics"
    elif "message/rfc822" in [mime_tag, content_type]:
        mime_tag = "message/rfc822"
        file_type = file_type if file_type else "eml"

    """add file_type to file_name if file_name doesn't contain"""
    if file_type is not None and "." not in file_name:
        file_name += "." + file_type
    attachment = {
        AttachmentColumns.FILENAME: file_name,
        AttachmentColumns.FILETYPE: file_type.lower() if file_type else file_type,
        AttachmentColumns.MIME_TAG: mime_tag,
        AttachmentColumns.SIZEINBYTES: content_len,
        AttachmentColumns.CONTENT: content,
        AttachmentColumns.FILEINFO_LOCATION_BUCKET: None,
        AttachmentColumns.FILEINFO_LOCATION_KEY: None,
        AttachmentColumns.FILEINFO_CONTENTLENGTH: None,
        AttachmentColumns.FILEINFO_VERSIONID: None,
        AttachmentColumns.FILEINFO_PROCESSED: None,
    }
    return attachment


def get_all_domains(identifiers: Dict[str, Any]) -> List[Any]:
    """collect the list of unique email address domains from identifiers non
    email address identifiers e.g. PG42357 will not be included."""
    result = []

    # string types
    for id_type in ["fromId", "onBehalfOf"]:
        result.append(get_email_address_domain(identifiers.get(id_type)))  # type: ignore

    # list types
    for id_type in ["toIds", "ccIds", "bccIds"]:
        result.extend(map(get_email_address_domain, identifiers.get(id_type, [])))

    # remove invalid results and normalize the output
    result = map(str.lower, filter(bool, result))  # type: ignore
    return sorted(list(set(result)))


def get_all_ids(identifiers: Dict[str, Any]) -> List[Any]:
    """collect the list of unique list of participant identifiers (local part
    for email addresses)"""
    result = []

    # string types
    for id_type in ["fromId", "onBehalfOf"]:
        result.append(identifiers.get(id_type))

    # list types
    for id_type in ["toIds", "ccIds", "bccIds"]:
        result.extend(identifiers.get(id_type, []))

    # remove invalid results and normalize the output
    result = map(str.lower, filter(bool, result))  # type: ignore
    return sorted(list(set(result)))


def append_aggregate_identifiers(_email: Dict[str, Any]) -> Dict[str, Any]:
    """update the input email with identifiers.allDomains &
    identifiers.allIds."""
    identifiers = _email.get("identifiers", {})
    all_domains = get_all_domains(identifiers)
    all_identifiers = get_all_ids(identifiers)

    identifiers["allDomains"] = all_domains
    identifiers["allIds"] = all_identifiers
    _email["identifiers"] = identifiers
    return _email


# We are starting with the premise that we append body content for search
# If/when we notice redundant content, we will need to fix that
# We have a display component if the content_type has text in it
# We append texts unless it's an 'alternative' mail.
# If it's an alternative mail we overwrite when we come across an alternative
# content_type =text/plain if we encounter only one text/plain content_type
# Else it is text/html
def process_text(root_type, payload_item, current_text):
    content_type = payload_item.get_content_type()
    display_type = content_type
    updated_text = current_text

    if current_text is None:
        updated_text = get_display_text(payload_item)
    elif (content_type == "text/plain") and ("mixed" in root_type or "alternative" in root_type):
        display_type = None
    elif "alternative" in root_type or root_type == "multipart/mixed":
        updated_text = get_display_text(payload_item)
    else:
        updated_text += get_display_text(payload_item)
    return display_type, updated_text


def get_display_text(part):
    return string(
        part.get_payload(decode=True),
        encoding=get_charset_with_override(part.get_content_charset()),
    )


def empty_if_none(inp):
    return "" if inp is None else string(inp)


def payload_type(payload_item) -> str:
    content_type = payload_item.get_content_type() if not isinstance(payload_item, str) else None
    return content_type.strip() if content_type else ""  # type: ignore


def is_int(value: str) -> bool:
    """check if given string has integer value.

    :param value: string value to check
    :return: True/False
    """
    # noinspection PyBroadException
    try:
        if value is not None:
            int(value)
            return True
    except Exception:
        pass
    return False


def force_utf8_encoding(body_text: str) -> str:
    """forces utf8 encoding onto a string, drops malformed characters."""
    body_text = body_text.encode("UTF-8", errors="ignore")  # type: ignore
    body_text = body_text.decode("UTF-8", errors="ignore")  # type: ignore
    return body_text


def run_transform_eml(
    pre_processed_df: pd.DataFrame,
    download_eml_df: pd.DataFrame,
    aries_task_input: AriesTaskInput,
    cloud_provider: CloudProviderEnum,
    tenant_bucket: str,
    app_metrics_path: str,
    audit_path: str,
    header_column_list: List[str] | None = None,
    force_decode_attachments: bool = False,
):
    task = TransformEml(
        aries_task_input=aries_task_input,
        tenant_bucket=tenant_bucket,
        cloud_provider=cloud_provider,
        header_column_list=header_column_list,
        force_decode_attachments=force_decode_attachments,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )

    return task.run(
        pre_processed_df=pre_processed_df,
        download_eml_df=download_eml_df,
    )
