import logging
import pandas as pd
from abc import abstractmethod
from aries_se_comms_tasks.voice.static import CallColumns
from se_core_tasks.abstractions.transformations.abstract_transformations import (
    AbstractTransformations,
)
from typing import Optional


class AbstractVoiceTransformations(AbstractTransformations):  # type: ignore
    """This is the abstract base class for all Call Primary Transformations. It contains
    a process, pre_process and post_process methods, along with methods corresponding to
    each field in the Call schema.
    pre_process(): used to populate temp columns (columns which are used in multiple
                     places) in the pre_process_df data frame
    process(): Used to call the public methods for each of the fields in the Call
                 schema. At the end of process(), target_df should be filled with values for the
                 schema fields.
    post_process(): Used to populate target_df with temp columns, drop columns, and for any
                      other post processing.
    Field-specific methods:
    There are 2 methods for each schema field -- a public method and a private method.

    Private Schema Field methods:
    The private methods for schema fields are abstract and need to be implemented
    in the child class.
    These private methods should always return a Pandas Series (if the schema column is required)
    or be passed (if the schema column isn't required).
    Typically, private methods call tasks or populate static values.

    Public Schema Field methods:

    These methods actually populate the Call schema fields by calling the appropriate private
    methods. Private methods and public methods for a field have the same name.
    The only difference is that the private method has a '_' prefix.
    Public methods should NOT be implemented in the child classes.

    E.g.: self.metadata_source_client() -> populates metadata.source.client in the
    target_df by concatenating the existing target df with the data frame returned
    by the private method
    _metadata_source_client(). _metadata_source_client() might call a task like
    MapStatic to implement the actual logic.
    """

    def __init__(
        self,
        source_frame: pd.DataFrame,
        logger: logging.Logger,
        realm: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(source_frame=source_frame, logger=logger)
        self.realm = realm

    def pre_process(self) -> None:
        """Used for pre-processing.

        It simply calls the private method _pre_process
        """
        self._pre_process()

    @abstractmethod
    def _pre_process(self) -> None:
        """This pre-processing method should be used to populate temporary
        columns in pre_process_df.

        pre_process_df should in general only be populated for temporary
        columns which might be required to populate at least 2 other
        fields.
        """

    def process(self) -> pd.DataFrame:
        """All the schema target columns which need to be populated are
        populated in self.target_df by the public methods called by process().
        The process() method should generally be overridden by child classes by
        including only the public methods that are required for that particular
        flow in the appropriate order.

        process() always calls pre_process() at the beginning and
        post_process() at the end and returns self.target_df.
        """
        self.pre_process()

        self.attachments()

        self.call_duration()
        self.call_duration_speaking()

        self.call_type()
        self.charge()
        self.conference_call()
        self.connected()
        self.direction()
        self.fault()
        self.has_attachment()

        self.id()
        self.identifiers_all_domains()
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_bcc_ids()
        self.identifiers_cc_ids()
        self.identifiers_from_device_id()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_from_ip()
        self.identifiers_from_user_id()
        self.identifiers_host_id()
        self.identifiers_on_behalf_of()
        self.identifiers_to_device_id()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()
        self.identifiers_to_ip()
        self.identifiers_to_user_id()

        self.internal()

        self.is_dealer_board()
        self.is_multi_channel()

        self.join_reason()

        self.meta_model()

        self.metadata_content_type()
        self.metadata_encoding_type()
        self.metadata_header()
        self.metadata_in_reply_to()
        self.metadata_message_id()
        self.metadata_reference_id()
        self.metadata_size_in_bytes()
        self.metadata_source_client()
        self.metadata_source_device_type()

        self.participants()

        self.source_index()
        self.source_key()

        self.timestamps_created()
        self.timestamps_duration_unit()
        self.timestamps_duration_value()
        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_connected()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_start()

        self.transcribed()

        # This is disabled by default as we do not want all
        # Voice feeds to populate this with null values
        # as that would fail in ApplyMeta while the schema is not yet updated

        # self.is_transcription_excluded()
        # self.transcription_exclusion_reason()

        self.voice_file()

        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()

        self.post_process()

        self.target_df: pd.DataFrame
        return self.target_df

    def post_process(self) -> None:
        """Used for post-processing.

        It simply calls the private method _post_process
        """
        self._post_process()

    @abstractmethod
    def _post_process(self) -> None:
        """The post-processing method should be used to populate temporary
        columns in target_df, and any other post-processing tasks.

        E.g. if col a is used in identifiers() and is required in
        downstream tasks, it should be populated in pre_process_df in
        _pre_process() and copied over to target_df in _post_process()
        """

    def attachments(self):
        """Populates CallColumns.ATTACHMENTS in target_df by calling
        _attachments()"""
        self.target_df.loc[:, CallColumns.ATTACHMENTS] = self._attachments()

    @abstractmethod
    def _attachments(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.ATTACHMENTS."""

    def call_duration(self):
        """Populates CallColumns.CALL_DURATION in target_df by calling
        _call_duration()"""
        self.target_df.loc[:, CallColumns.CALL_DURATION] = self._call_duration()

    @abstractmethod
    def _call_duration(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CALL_DURATION."""

    def call_duration_speaking(self):
        """Populates CallColumns.CALL_DURATION_SPEAKING in target_df by calling
        _call_duration_speaking()"""
        self.target_df.loc[:, CallColumns.CALL_DURATION_SPEAKING] = self._call_duration_speaking()

    @abstractmethod
    def _call_duration_speaking(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CALL_DURATION_SPEAKING."""

    def call_type(self):
        """Populates CallColumns.CALL_TYPE in target_df by calling
        _call_type()"""
        self.target_df.loc[:, CallColumns.CALL_TYPE] = self._call_type()

    @abstractmethod
    def _call_type(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CALL_TYPE."""

    def charge(self):
        """Populates CallColumns.CHARGE in target_df by calling _charge()"""
        self.target_df.loc[:, CallColumns.CHARGE] = self._charge()

    @abstractmethod
    def _charge(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CHARGE."""

    def conference_call(self):
        """Populates CallColumns.CONFERENCE_CALL in target_df by calling
        _conference_call()"""
        self.target_df.loc[:, CallColumns.CONFERENCE_CALL] = self._conference_call()

    @abstractmethod
    def _conference_call(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CONFERENCE_CALL."""

    def connected(self):
        """Populates CallColumns.CONNECTED in target_df by calling
        _connected()"""
        self.target_df.loc[:, CallColumns.CONNECTED] = self._connected()

    @abstractmethod
    def _connected(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.CONNECTED."""

    def _connected__std(self) -> pd.Series:
        """Standard implementation of the connected field."""
        if CallColumns.CALL_DURATION in self.target_df.columns:
            result: pd.Series = pd.Series(
                data=self.target_df[CallColumns.CALL_DURATION] > 0,
                index=self.source_frame.index,
            )
            return result
        else:
            default_result: pd.Series = pd.Series(
                data=pd.NA,
                index=self.source_frame.index,
            )
            return default_result

    def direction(self):
        """Populates CallColumns.DIRECTION in target_df by calling
        _direction()"""
        self.target_df.loc[:, CallColumns.DIRECTION] = self._direction()

    @abstractmethod
    def _direction(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.DIRECTION."""

    def fault(self):
        """Populates CallColumns.FAULT in target_df by calling _fault()"""
        self.target_df.loc[:, CallColumns.FAULT] = self._fault()

    @abstractmethod
    def _fault(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.FAULT."""

    def has_attachment(self):
        """Populates CallColumns.HAS_ATTACHMENT in target_df by calling
        _has_attachment()"""
        self.target_df.loc[:, CallColumns.HAS_ATTACHMENT] = self._has_attachment()

    @abstractmethod
    def _has_attachment(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.HAS_ATTACHMENT."""

    def id(self):
        """Populates CallColumns.ID in target_df by calling _id()"""
        self.target_df.loc[:, CallColumns.ID] = self._id()

    @abstractmethod
    def _id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.ID."""

    def identifiers_all_country_codes(self):
        self.target_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES] = (
            self._identifiers_all_country_codes()
        )

    @abstractmethod
    def _identifiers_all_country_codes(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES."""

    def identifiers_all_domains(self):
        """Populates CallColumns.IDENTIFIERS_ALL_DOMAINS in target_df by
        calling _identifiers_all_domains()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_ALL_DOMAINS] = self._identifiers_all_domains()

    @abstractmethod
    def _identifiers_all_domains(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_ALL_DOMAINS."""

    def identifiers_all_ids(self):
        """Populates CallColumns.IDENTIFIERS_ALL_IDS in target_df by calling
        _identifiers_all_ids()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS] = self._identifiers_all_ids()

    @abstractmethod
    def _identifiers_all_ids(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_ALL_IDS."""

    def identifiers_bcc_ids(self):
        """Populates CallColumns.IDENTIFIERS_BCC_IDS in target_df by calling
        _identifiers_bcc_ids()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_BCC_IDS] = self._identifiers_bcc_ids()

    @abstractmethod
    def _identifiers_bcc_ids(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_BCC_IDS."""

    def identifiers_cc_ids(self):
        """Populates CallColumns.IDENTIFIERS_CC_IDS in target_df by calling
        _identifiers_cc_ids()"""

    @abstractmethod
    def _identifiers_cc_ids(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_CC_IDS."""

    def identifiers_from_device_id(self):
        """Populates CallColumns.IDENTIFIERS_FROM_DEVICE_ID in target_df by
        calling _identifiers_from_device_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_FROM_DEVICE_ID] = (
            self._identifiers_from_device_id()
        )

    @abstractmethod
    def _identifiers_from_device_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_FROM_DEVICE_ID."""

    def identifiers_from_id(self):
        """Populates CallColumns.IDENTIFIERS_FROM_ID in target_df by calling
        _identifiers_from_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID] = self._identifiers_from_id()

    @abstractmethod
    def _identifiers_from_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_FROM_ID."""

    def identifiers_from_id_addl_info(self):
        """Populates CallColumns.IDENTIFIERS_FROM_ADDL_INFO in target_df by
        calling _identifiers_from_id_addl_info()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO] = (
            self._identifiers_from_id_addl_info()
        )

    @abstractmethod
    def _identifiers_from_id_addl_info(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_FROM_ADDL_INFO."""

    def identifiers_from_ip(self):
        """Populates CallColumns.IDENTIFIERS_FROM_IP in target_df by calling
        _identifiers_from_ip()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_FROM_IP] = self._identifiers_from_ip()

    @abstractmethod
    def _identifiers_from_ip(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_FROM_IP."""

    def identifiers_from_user_id(self):
        """Populates CallColumns.IDENTIFIERS_FROM_USER_ID in target_df by
        calling _identifiers_from_user_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_FROM_USER_ID] = (
            self._identifiers_from_user_id()
        )

    @abstractmethod
    def _identifiers_from_user_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_FROM_USER_ID."""

    def identifiers_host_id(self):
        """Populates CallColumns.IDENTIFIERS_HOST_ID in target_df by calling
        _identifiers_host_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_HOST_ID] = self._identifiers_host_id()

    @abstractmethod
    def _identifiers_host_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_HOST_ID."""

    def identifiers_on_behalf_of(self):
        """Populates CallColumns.IDENTIFIERS_ON_BEHALF_OF in target_df by
        calling _identifiers_on_behalf_of()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_ON_BEHALF_OF] = (
            self._identifiers_on_behalf_of()
        )

    @abstractmethod
    def _identifiers_on_behalf_of(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_ON_BEHALF_OF."""

    def identifiers_to_device_id(self):
        """Populates CallColumns.IDENTIFIERS_TO_DEVICE_ID in target_df by
        calling _identifiers_to_device_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_TO_DEVICE_ID] = (
            self._identifiers_to_device_id()
        )

    @abstractmethod
    def _identifiers_to_device_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_TO_IDS."""

    def identifiers_to_ids(self):
        """Populates CallColumns.IDENTIFIERS_TO_DEVICE_ID in target_df by
        calling _identifiers_to_ids()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS] = self._identifiers_to_ids()

    @abstractmethod
    def _identifiers_to_ids(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_TO_IDS."""

    def identifiers_to_ids_addl_info(self):
        """Populates CallColumns.IDENTIFIERS_TO_ADDL_INFO in target_df by
        calling identifiers_to_ids_addl_info."""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO] = (
            self._identifiers_to_ids_addl_info()
        )

    @abstractmethod
    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_TO_ADDL_INFO."""

    def identifiers_to_ip(self):
        """Populates CallColumns.IDENTIFIERS_TO_IP in target_df by calling
        _identifiers_to_ip()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_TO_IP] = self._identifiers_to_ip()

    @abstractmethod
    def _identifiers_to_ip(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_TO_IP."""

    def identifiers_to_user_id(self):
        """Populates CallColumns.IDENTIFIERS_TO_USER_ID in target_df by calling
        _identifiers_to_user_id()"""
        self.target_df.loc[:, CallColumns.IDENTIFIERS_TO_USER_ID] = self._identifiers_to_user_id()

    @abstractmethod
    def _identifiers_to_user_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IDENTIFIERS_TO_USER_ID."""

    def internal(self):
        """Populates CallColumns.INTERNAL in target_df by calling
        _internal()"""
        self.target_df.loc[:, CallColumns.INTERNAL] = self._internal()

    @abstractmethod
    def _internal(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.INTERNAL."""

    def is_multi_channel(self):
        """Populates CallColumns.IS_MULTI_CHANNEL in target_df by calling
        _is_multi_channel()"""
        self.target_df.loc[:, CallColumns.IS_MULTI_CHANNEL] = self._is_multi_channel()

    @abstractmethod
    def _is_multi_channel(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IS_MULTI_CHANNEL."""

    def is_dealer_board(self):
        """Populates CallColumns.IS_DEALER_BOARD in target_df by calling
        _is_dealer_board()"""
        self.target_df.loc[:, CallColumns.IS_DEALER_BOARD] = self._is_dealer_board()

    @abstractmethod
    def _is_dealer_board(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IS_DEALER_BOARD."""

    def join_reason(self):
        """Populates CallColumns.JOIN_REASON in target_df by calling
        _join_reason()"""
        self.target_df.loc[:, CallColumns.JOIN_REASON] = self._join_reason()

    @abstractmethod
    def _join_reason(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.JOIN_REASON."""

    def meta_model(self):
        """Populates __meta_model__ in target_df by calling _meta_model()"""
        self.target_df.loc[:, CallColumns.META_MODEL] = self._meta_model()

    @abstractmethod
    def _meta_model(self) -> pd.Series:
        """Abstract Method which needs to be implemented in the child class to
        populate __meta_model__ __meta_model__ is a temporary column required
        for AssignMeta and has to be dropped downstream."""

    def metadata_content_type(self):
        """Populates CallColumns.METADATA_CONTENT_TYPE in target_df by calling
        _metadata_content_type()"""
        self.target_df.loc[:, CallColumns.METADATA_CONTENT_TYPE] = self._metadata_content_type()

    @abstractmethod
    def _metadata_content_type(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_CONTENT_TYPE."""

    def metadata_encoding_type(self):
        """Populates CallColumns.METADATA_ENCODING_TYPE in target_df by calling
        _metadata_encoding_type()"""
        self.target_df.loc[:, CallColumns.METADATA_ENCODING_TYPE] = self._metadata_encoding_type()

    @abstractmethod
    def _metadata_encoding_type(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_ENCODING_TYPE."""

    def metadata_header(self):
        """Populates CallColumns.METADATA_HEADER in target_df by calling
        _metadata_header()"""
        self.target_df.loc[:, CallColumns.METADATA_HEADER] = self._metadata_header()

    @abstractmethod
    def _metadata_header(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_HEADER."""

    def metadata_in_reply_to(self):
        """Populates CallColumns.METADATA_IN_REPLY_TO in target_df by calling
        _metadata_in_reply_to()"""
        self.target_df.loc[:, CallColumns.METADATA_IN_REPLY_TO] = self._metadata_in_reply_to()

    @abstractmethod
    def _metadata_in_reply_to(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_IN_REPLY_TO."""

    def metadata_message_id(self):
        """Populates CallColumns.METADATA_MESSAGE_ID in target_df by calling
        _metadata_message_id()"""
        self.target_df.loc[:, CallColumns.METADATA_MESSAGE_ID] = self._metadata_message_id()

    @abstractmethod
    def _metadata_message_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_MESSAGE_ID."""

    def metadata_reference_id(self):
        """Populates CallColumns.METADATA_REFERENCE_ID in target_df by calling
        _metadata_reference_id()"""
        self.target_df.loc[:, CallColumns.METADATA_REFERENCE_ID] = self._metadata_reference_id()

    @abstractmethod
    def _metadata_reference_id(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_REFERENCE_ID."""

    def metadata_size_in_bytes(self):
        """Populates CallColumns.METADATA_SIZE_IN_BYTES in target_df by calling
        _metadata_size_in_bytes()"""
        self.target_df.loc[:, CallColumns.METADATA_SIZE_IN_BYTES] = self._metadata_size_in_bytes()

    @abstractmethod
    def _metadata_size_in_bytes(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_SIZE_IN_BYTES."""

    def metadata_source_client(self):
        """Populates CallColumns.METADATA_SOURCE_CLIENT in target_df by calling
        _metadata_source_client()"""
        self.target_df.loc[:, CallColumns.METADATA_SOURCE_CLIENT] = self._metadata_source_client()

    @abstractmethod
    def _metadata_source_client(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_SOURCE_CLIENT."""

    def metadata_source_device_type(self):
        """Populates CallColumns.METADATA_SOURCE_DEVICE_TYPE in target_df by
        calling _metadata_source_device_type()"""
        self.target_df.loc[:, CallColumns.METADATA_SOURCE_DEVICE_TYPE] = (
            self._metadata_source_device_type()
        )

    @abstractmethod
    def _metadata_source_device_type(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.METADATA_SOURCE_DEVICE_TYPE."""

    def metadata_source_file_info_location_bucket(self):
        """Populates CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET in
        target_df by calling _metadata_source_file_info_location_bucket()"""
        self.target_df.loc[:, CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET] = (
            self._metadata_source_file_info_location_bucket()
        )

    @abstractmethod
    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column
        CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET."""

    def metadata_source_file_info_location_key(self):
        """Populates CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY in
        target_df by calling _metadata_source_file_info_location_key()"""
        self.target_df.loc[:, CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY] = (
            self._metadata_source_file_info_location_key()
        )

    @abstractmethod
    def _metadata_source_file_info_location_key(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column
        CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY."""

    def participants(self):
        """Populates CallColumns.PARTICIPANTS in target_df by calling
        _participants()"""
        self.target_df.loc[:, CallColumns.PARTICIPANTS] = self._participants()

    @abstractmethod
    def _participants(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.PARTICIPANTS."""

    def rate(self):
        """Populates CallColumns.RATE in target_df by calling _rate()"""
        self.target_df.loc[:, CallColumns.RATE] = self._rate()

    @abstractmethod
    def _rate(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.Rate."""

    def source_index(self):
        """Populates sourceIndex in target_df by calling _source_index()"""
        self.target_df.loc[:, CallColumns.SOURCE_INDEX] = self._source_index()

    @abstractmethod
    def _source_index(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the mandatory column sourceKey."""

    def source_key(self):
        """Populates sourceKey in target_df by calling _source_key()"""
        self.target_df.loc[:, CallColumns.SOURCE_KEY] = self._source_key()

    @abstractmethod
    def _source_key(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the mandatory column sourceKey."""

    def timestamps_created(self):
        self.target_df.loc[:, CallColumns.TIMESTAMPS_CREATED] = self._timestamps_created()

    @abstractmethod
    def _timestamps_created(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_CREATED in target_df by calling
        _timestamps_created()"""

    def timestamps_duration_unit(self):
        self.target_df.loc[:, CallColumns.TIMESTAMPS_DURATION_UNIT] = (
            self._timestamps_duration_unit()
        )

    @abstractmethod
    def _timestamps_duration_unit(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_DURATION_UNIT in target_df by
        calling _timestamps_duration_unit()"""

    def timestamps_duration_value(self):
        self.target_df.loc[:, CallColumns.TIMESTAMPS_DURATION_VALUE] = (
            self._timestamps_duration_value()
        )

    @abstractmethod
    def _timestamps_duration_value(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_DURATION_VALUE in target_df by
        calling _timestamps_duration_value()"""

    def timestamps_local_timestamp_end(self):
        """Populates CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END in target_df by
        calling _timestamps_local_timestamp_end()"""
        self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END] = (
            self._timestamps_local_timestamp_end()
        )

    @abstractmethod
    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END."""

    def timestamps_local_timestamp_start(self):
        """Populates CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START in target_df
        by calling _timestamps_local_timestamp_start()"""
        self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START] = (
            self._timestamps_local_timestamp_start()
        )

    @abstractmethod
    def _timestamps_local_timestamp_start(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START."""

    def timestamps_timestamp_connected(self):
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED in target_df by
        calling _timestamps_timestamp_connected()"""
        self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED] = (
            self._timestamps_timestamp_connected()
        )

    @abstractmethod
    def _timestamps_timestamp_connected(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED."""

    def timestamps_timestamp_end(self):
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_END in target_df by
        calling _timestamps_timestamp_end()"""
        self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_END] = (
            self._timestamps_timestamp_end()
        )

    @abstractmethod
    def _timestamps_timestamp_end(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TIMESTAMPS_TIMESTAMP_END."""

    def timestamps_timestamp_start(self):
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_START in target_df by
        calling _timestamps_timestamp_start()"""
        self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_START] = (
            self._timestamps_timestamp_start()
        )

    @abstractmethod
    def _timestamps_timestamp_start(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TIMESTAMPS_TIMESTAMP_START."""

    def transcribed(self):
        """Populates CallColumns.TRANSCRIBED in target_df by calling
        _transcribed()"""
        self.target_df.loc[:, CallColumns.TRANSCRIBED] = self._transcribed()

    @abstractmethod
    def _transcribed(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TRANSCRIBED."""

    def voice_file(self):
        """Populates CallColumns.VOICE_FILE in target_df by calling
        _voice_file()"""
        self.target_df.loc[:, CallColumns.VOICE_FILE] = self._voice_file()

    @abstractmethod
    def _voice_file(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.VOICE_FILE."""

    def waveform_file_info_location_bucket(self):
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET in
        target_df by calling _waveform_file_info_location_bucket()"""
        self.target_df.loc[:, CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET] = (
            self._waveform_file_info_location_bucket()
        )

    @abstractmethod
    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET."""

    def waveform_file_info_location_key(self):
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY in target_df
        by calling _waveform_file_info_location_bucket()"""
        self.target_df.loc[:, CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY] = (
            self._waveform_file_info_location_key()
        )

    @abstractmethod
    def _waveform_file_info_location_key(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""

    def is_transcription_excluded(self):
        """Populates CallColumns.IS_TRANSCRIPTION_EXCLUDED in target_df by
        calling _is_transcription_excluded()"""
        self.target_df.loc[:, CallColumns.IS_TRANSCRIPTION_EXCLUDED] = (
            self._is_transcription_excluded()
        )

    @abstractmethod
    def _is_transcription_excluded(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.IS_TRANSCRIPTION_EXCLUDED."""

    def transcription_exclusion_reason(self):
        """Populates CallColumns.TRANSCRIPTION_EXCLUSION_REASON in target_df by
        calling _transcription_exclusion_reason()"""
        self.target_df.loc[:, CallColumns.TRANSCRIPTION_EXCLUSION_REASON] = (
            self._transcription_exclusion_reason()
        )

    @abstractmethod
    def _transcription_exclusion_reason(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column CallColumns.TRANSCRIPTION_EXCLUSION_REASON."""
