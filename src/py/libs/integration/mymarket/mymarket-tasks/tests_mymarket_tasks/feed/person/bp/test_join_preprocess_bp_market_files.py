import pandas as pd
import pytest
from mymarket_tasks.feeds.person.bp.join_preprocess_bp_market_files import (
    run_join_bp_preprocess_market_files,
)
from mymarket_tasks.feeds.person.bp.static import BpEmailAliasSourceColumns, BpMarketDerivedColumns
from pathlib import Path

CURRENT_PATH = Path(__file__).parent
DATA_PATH = Path(__file__).parent.joinpath("data")


@pytest.fixture()
def bpist_source_df() -> pd.DataFrame:
    return pd.read_csv(DATA_PATH.joinpath("bpist_source_df.csv"), dtype="str")


class TestJoinPreprocessBpMarketFiles:
    """Test Suite for JoinPreprocessBpMarketFiles"""

    def test_end_to_end(
        self,
        mrl_df: pd.DataFrame,
        bpist_source_df: pd.DataFrame,
        expected_result_join_preprocess: pd.DataFrame,
    ):
        result = run_join_bp_preprocess_market_files(
            mrl_df=mrl_df, bp_source_df=bpist_source_df, skip_serializer=True
        )
        result = self._sort_list_columns(df=result)
        expected_result_join_preprocess = self._sort_list_columns(
            df=expected_result_join_preprocess
        )
        pd.testing.assert_frame_equal(left=result, right=expected_result_join_preprocess)

    @staticmethod
    def _sort_list_columns(df: pd.DataFrame):
        for col in [BpEmailAliasSourceColumns.EMAIL_ALIASES, BpMarketDerivedColumns.ALL_EMAILS]:
            notnull_mask = df[col].notnull()
            df.loc[notnull_mask, col] = df.loc[notnull_mask, col].apply(sorted)
        return df
