import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_trades_tasks.order_and_tr.party.link_parties import Params
from se_trades_tasks.order_and_tr.party.link_parties import run_link_parties as run_tasks_lib
from typing import Any


class LinkParties(IntegrationTask):
    def _run(
        self,
        tenant: str,
        source_frame: pd.DataFrame,
        params: Params = None,
        es_client: Any = None,
        **kwargs,
    ) -> pd.DataFrame:
        res: pd.DataFrame = run_tasks_lib(
            source_frame=source_frame,
            params=params,
            es_client=es_client,
            tenant=tenant,
            **kwargs,
        )
        return res


def run_link_parties(
    tenant: str,
    source_frame: pd.DataFrame,
    params: Params | None = None,
    es_client: Any = None,
    app_metrics_path: str | None = None,
    audit_path: str | None = None,
    **kwargs,
):
    task = LinkParties(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(
        tenant=tenant,
        source_frame=source_frame,
        params=params,
        es_client=es_client,
        **kwargs,
    )
