from datetime import datetime
from pydantic import BaseModel, Field, root_validator
from se_elastic_schema.models.tenant.surveillance.communication_alert import CommunicationAlert
from typing import Optional


class AlertValidator(CommunicationAlert):  # type: ignore[misc]
    @root_validator(pre=True)
    def remove_legacy_topic(cls, values: dict):
        """Removal of topic in analytics for some legacy comms (needed to
        create valid alerts)

        :param values: values to be tested to check if they can be put in the pydantic model
        :return: dict with the value to populate the model
        """
        values.get("hit", {}).get("analytics", {}).pop("topic", {})

        return values


class SurveillanceWatchFinalizeTaskInput(BaseModel):
    merge_output: dict = Field(..., description="The output of the merge task")
    upper_bound_date: datetime = Field(..., description="Upper bound date of the watch run, in UTC")
    watch_execution_id: str = Field(..., description="Watch execution id")
    watch_id: str = Field(..., description="Watch id")
    exclude_doc_id: Optional[str] = Field(
        default=None, description="Exclusion Doc ID To be deleted"
    )
    exclusion_doc_parent_id: Optional[str] = Field(
        default=None, description="Exclusion Doc Parent ID To be deleted"
    )
