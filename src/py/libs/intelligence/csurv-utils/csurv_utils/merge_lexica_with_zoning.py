def merge_zones_with_lexica_triggers(analytics: dict) -> dict:
    """Merge zoning analytics with lexica. For each trigger, add the zone class
    and value to the trigger if it is within the zone limits.

    :param analytics: The analytics to merge
    :type analytics: dict
    :return: The merged analytics
    :rtype: dict
    """
    zones = {
        (zone["start"], zone["end"]): zone
        for zone in analytics.get("zoning", {}).get("predictionOffsets", [])
    }
    if zones:
        for lex_hit in analytics.get("lexica", {}):
            if lex_hit["source"] != "body_text":
                continue
            for trigger in lex_hit["triggers"]:
                for zone_limits in zones.keys():
                    if zone_limits[0] <= trigger["triggerStartChar"] < zone_limits[1]:
                        trigger["zone_class"] = zones[zone_limits]["class_"]
                        trigger["zone_value"] = zones[zone_limits]["value"]
                        break
    return analytics
