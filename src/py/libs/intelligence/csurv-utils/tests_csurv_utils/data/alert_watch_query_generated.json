{"query": {"function_score": {"query": {"bool": {"must_not": [{"exists": {"field": "&expiry"}}, {"term": {"&seriesRefFqn": "ignore"}}, {"terms": {"&id": {"index": "fake-tenant-surveillance_exclude_by_id-alias", "id": "e938a0ce-b88e-4edb-a740-022377e3fd87", "path": "excludes"}}}, {"exists": {"field": "underlyingAlertId"}}], "filter": [{"terms": {"&model": ["Communication<PERSON><PERSON>t"]}}, {"bool": {"must_not": [{"nested": {"path": "hit.identifiers.domains", "query": {"bool": {"must": [{"terms": {"hit.identifiers.domains.value.text": ["unit21.ai"]}}, {"terms": {"hit.identifiers.domains.types": ["FROM"]}}]}}}}]}}, {"terms": {"workflow.status": ["RESOLVED_WITH_BREACH", "RESOLVED_WITH_DISMISSAL", "RESOLVED_WITH_INVESTIGATION", "RESOLVED_WITH_INVESTIGATION_WITH_BREACH"]}}, {"range": {"&timestamp": {"gt": 1704212094000, "lte": 1704292260843}}}]}}, "random_score": {"seed": 1693840407144, "field": "&id"}, "boost_mode": "replace"}}, "size": 50}