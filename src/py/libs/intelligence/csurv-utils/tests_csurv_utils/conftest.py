import os
import pytest
from pathlib import Path
from surveillance_utils.test_mock_helpers import FakeSlimRecordHandler

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"
os.environ["ELASTIC_HOST"] = "fake_host"
TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


@pytest.fixture()
def fake_slim_record_handler_alerts():
    return FakeSlimRecordHandler(
        version=8,
        search_repo_path=[str(TEST_DATA_PATH.joinpath("alert_watch.json"))],
        client_get_path=str(TEST_DATA_PATH.joinpath("alert_watch.json")),
    )


@pytest.fixture()
def fake_slim_record_handler_comms():
    return FakeSlimRecordHandler(
        version=8,
        search_repo_path=[str(TEST_DATA_PATH.joinpath("behaviour_watch.json"))],
        client_get_path=str(TEST_DATA_PATH.joinpath("behaviour_watch.json")),
    )
