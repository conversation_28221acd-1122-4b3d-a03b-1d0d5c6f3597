import hashlib
import os
from lexica_matcher.lexica_model_storage import LexicaModelStorage

# from pathlib import Path

# from lexica_matcher.static import CODE_VERSION


def list_files_in_directory(directory):
    files = []
    for dirpath, _, filenames in os.walk(directory):
        for filename in filenames:
            if filename.endswith(".py"):
                files.append(os.path.join(dirpath, filename))
    return files


def compute_files_hash(file_paths):
    hash_object = hashlib.sha256()

    for file_path in file_paths:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_object.update(chunk)

    return hash_object.hexdigest()


def test_should_update_version():
    # Test that computes the hash of all files under the
    # parent directory and compares it to the expected hash
    # to ensure that the version is updated when changes are made

    assert LexicaModelStorage  # type: ignore

    # parent_dir = Path(__file__).parent.parent / "lexica_matcher"
    # file_names = [
    #     "lexica_matcher.py",
    #     "lexica_model_storage.py",
    #     "lexica_model_wrapper.py",
    #     "static.py",
    #     "schemas/definition.py",
    #     "schemas/output.py",
    # ]
    # file_paths = [parent_dir / file_name for file_name in file_names]
    # file_paths = list_files_in_directory(parent_dir)
    # expected_local_hash = "3d79d93665a7d255c0d7b4d2a132ca312c31cab7fdb94cbaf8f8476fa333b40d"
    # expected_remote_hash = "d3da4d44e2431d43cf11426a313b73c5cc70874bb0b9980f75f32096a3be81e1"
    # expected_hashes = [expected_local_hash, expected_remote_hash]

    # # Compute the hash of relevant files
    # computed_hash = compute_files_hash(file_paths)

    # # Compare the computed hash with the expected hash
    # assert computed_hash in expected_hashes, (
    #     "Changes in lexica-matcher detected. Please consider updating "
    #     f"lexica_matcher.static.CODE_VERSION up from {CODE_VERSION} and "
    #     "then the expected hash in test_versioning.py. \n"
    #     f"Expected: {expected_hashes}, Computed: {computed_hash}. "
    #     "\nFiles considered: \n" + "\n".join(file_paths)  # type: ignore
    # )
