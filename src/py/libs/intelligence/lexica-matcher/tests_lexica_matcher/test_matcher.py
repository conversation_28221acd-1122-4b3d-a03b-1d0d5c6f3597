# type: ignore
import concurrent.futures as cf
import fsspec  # noqa
from lexica_matcher.lexica_model_storage import LexicaMatcher, load_lexica_model
from lexica_matcher.lexica_model_wrapper import create_model, save_lexica_model_wrapper_to_buffer
from lexica_matcher.schemas.definition import LexicaDefinition, SearchType
from lexica_matcher.static import LANG_TO_GROUP_MAP, UNIMPLEMENTED_LANGUAGE
from pathlib import Path
from se_elastic_schema.static.comms.analytics import AnalyticsTermTypeEnum
from typing import Dict, List
from uuid import uuid4


def test_evaluate_different_languages():
    lang_terms = [
        ("af", "Die kat het op die mat gesit"),
        ("ar", "القطة جلست على السجادة"),
        ("da", "Katten sad på måtten"),
        ("de", "Die Katze saß auf der Matte"),
        ("en", "The cat sat on the mat"),
        ("es", "El gato se sentó en la alfombra"),
        ("fi", "Kissa istui matolla"),
        ("fr", "Le chat était assis sur le tapis"),
        ("hi", "बिल्ली चटाई पर बैठी"),
        ("id", "Kucing itu duduk di atas tikar"),
        ("is", "Kötturinn sat á mottunni"),
        ("it", "Il gatto era seduto sul tappeto"),
        ("ja.kj", "猫がマットの上に座った"),
        ("ko", "고양이가 매트에 앉았다"),
        ("ms", "Kucing duduk di atas tikar"),
        ("nl", "De kat zat op de mat"),
        ("no", "Katten satt på matten"),
        ("pl", "Kot usiadł na macie"),
        ("pt", "ato ato"),
        ("ru", "Кошка сидела на коврике"),
        ("sv", "Katten satt på mattan"),
        ("sw", "Paka aliketi kwenye zulia"),
        ("th", "แมวนั่งบนเสื่อ"),
        ("vi", "Con mèo ngồi trên thảm"),
        ("zh.cn", "隱藏"),
        ("zh.tw", "貓坐在墊子上"),
        ("zu", "Ikati lihlala phezu komata"),
    ]
    lang_group = [lang[0] for lang in lang_terms]
    # topics for all languages implemented are mandatory
    lang_group.extend(list(LANG_TO_GROUP_MAP["af"]))
    lang_group = list(set(lang_group))
    lexica_topics = {lang: {"COVID": ["covid19", "covid"]} for lang in lang_group}

    lexica_definitions = []
    for lang, term in lang_terms:
        sample_definition = {k: "" for k in LexicaDefinition.__fields__.keys()}

        sample_definition["termExclusions"] = ["excluded__term"]
        sample_definition["termExclusionTopics"] = ["COVID"]
        sample_definition["termExclusionSearchType"] = SearchType.OR
        sample_definition["termToSearchFor"] = term
        sample_definition["behaviour"] = f"behaviour {term}"
        sample_definition["subBehaviour"] = f"subBehaviour {term}"
        sample_definition["behaviourId"] = uuid4().__str__()
        sample_definition["subBehaviourId"] = uuid4().__str__()
        sample_definition["termId"] = uuid4().__str__()
        sample_definition["opFuzziness"] = 0
        sample_definition["opProximity"] = 0
        sample_definition["opStemming"] = False
        sample_definition["termLanguage"] = lang
        sample_definition["termType"] = AnalyticsTermTypeEnum.PHRASE

        lexica_definitions.append(LexicaDefinition(**sample_definition))

    lexica_models = build_lexica_models_replica(
        "fake-tenant", "v0", lexica_definitions, lexica_topics
    )

    test_wrapper_paths = []
    for lexica_model in lexica_models:
        model_path = Path(__file__).parent / f"test_1678897283___{lexica_model.version_tag}.pkl"
        test_wrapper_paths.append(model_path)
        with fsspec.open(model_path, "wb") as f:
            f.write(save_lexica_model_wrapper_to_buffer(lexica_model).getvalue())

    del lexica_models

    test_wrappers = [load_lexica_model(str(wrapper_path)) for wrapper_path in test_wrapper_paths]
    lexica_matcher = LexicaMatcher(test_wrappers)

    #  Evaluate text for different languages
    for lang, term in lang_terms:
        text = "something here " + term + " more text"
        result = lexica_matcher.evaluate_text(text)
        assert result.hits[0].term == term
        assert result.hits[0].termLanguage == lang

    # If we got termExclusions in the text, no hits returned
    result = lexica_matcher.evaluate_text("The cat sat on the mat excluded__term")
    assert len(result.hits) == 0
    assert len(result.topics) == 0

    # Validate Removing overlapping hits
    # with term "ato ato" from lang pt we should have 2 hits overlapped
    # making sure we only export 1
    result = lexica_matcher.evaluate_text("ato ato ato")
    assert len(result.hits) == 1
    assert result.hits[0].triggers[0].triggerStartChar == 0
    assert result.hits[0].triggers[0].triggerEndChar == 7


def build_lexica_models_replica(
    tenant: str,
    se_lexica_version: str,
    lexica_definitions: List[LexicaDefinition],
    lexica_topics: dict,
):
    """Build the SteelEye Lexica models for a tenant.

    :param tenant: The tenant to build the models for
    :type tenant: str
    :param se_lexica_version: The SE Lexica version
    :type se_lexica_version: str
    :param lexica_definitions: List of Lexica Definitions
    :type lexica_definitions: List[LexicaDefinition]
    :param lexica_topics: The lexica topics
    :type lexica_topics: dict
    :return: The SteelEye Lexica models for the tenant
    """
    # Only pass to MP pool the appropriate language lexica_definitions
    lex_defs_by_lang_group: Dict[tuple, List[LexicaDefinition]] = {}
    languages_by_lang_group: Dict[tuple, List[str]] = {}
    for l_def in lexica_definitions:
        lang_group = LANG_TO_GROUP_MAP.get(l_def.termLanguage, UNIMPLEMENTED_LANGUAGE)
        lex_defs_by_lang_group[lang_group] = lex_defs_by_lang_group.get(lang_group, []) + [l_def]
        languages_by_lang_group[lang_group] = languages_by_lang_group.get(lang_group, []) + [
            l_def.termLanguage
        ]
    del lexica_definitions
    languages_by_lang_group = {k: list(set(v)) for k, v in languages_by_lang_group.items()}

    v_tags_by_lang_group = {}
    for lang_group in set(lex_defs_by_lang_group.keys()):
        languages = "_".join(sorted(list(set(lang_group))))
        v_tags_by_lang_group[lang_group] = f"{tenant}___{se_lexica_version}___{languages}"

    models_batch = [
        (
            l_defs,
            lexica_topics,
            lang_group,
            v_tags_by_lang_group[lang_group],
            languages_by_lang_group[lang_group],
        )
        for lang_group, l_defs in lex_defs_by_lang_group.items()
        if l_defs
    ]

    with cf.ProcessPoolExecutor(max_workers=1) as executor:
        lexica_models = executor.map(create_model, models_batch)

    return lexica_models
