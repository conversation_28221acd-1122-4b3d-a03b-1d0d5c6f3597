import boto3
import datetime
import fsspec
import io
import json
import logging
import os
import time
from cachetools import TTLCache, cached
from concurrent.futures import ThreadPoolExecutor
from fsspec.core import get_fs_token_paths
from fsspec.spec import AbstractFileSystem
from functools import lru_cache
from lexica_light.config import LEXICA_CONFIG
from lexica_matcher.config import LEXICA_MATCHER_CONFIG
from lexica_matcher.lexica_matcher import LexicaMatcher
from lexica_matcher.lexica_model_wrapper import (
    LexicaModelWrapper,
    load_lexica_model_wrapper,
    save_lexica_model_wrapper_to_buffer,
)
from lexica_matcher.static import CODE_VERSION, LANG_TO_GROUP_MAP
from typing import Dict, List, Union

"""
The Index structure is:
{
  "_tenant_": {
      "version_tag": "s_XXXX___l1_l2___t_XXXX",
      "models": ["_PATH_TO_MODEL_", "_PATH_TO_MODEL_2_"]
  }
}
Where:
    _tenant_ is the the name of the tenant;
    s_XXXX___l1_l2___t_XXXX is the version, where:
        s_XXX is steeleye lexica version;
        l1 and l2 are the subscribed languages;
        t_XXX is the latest edit timestamp;
    _PATH_TO_MODEL_ is the path to lexica processed terms stored as a pickle;

An example of a version_tag is: 'v6___en_fr___1681306631'
"""

_save_thread_pool = ThreadPoolExecutor(max_workers=1)


class LexicaModelStorage:
    def __init__(self) -> None:
        self.model_index_path = f"{LEXICA_CONFIG.LEXICA_STORAGE_PATH()}/model_index.json"

    @staticmethod
    def create_client(lexica_storage_path: str) -> AbstractFileSystem:
        fs, _, (_,) = get_fs_token_paths(lexica_storage_path)
        return fs

    def get_client(self):
        return self.create_client(LEXICA_CONFIG.LEXICA_STORAGE_PATH())

    def load(self, tenant: str):
        model_files = self.get_model_index(tenant).get("models", [])

        lexica_models = []
        for model_path in model_files:
            lexica_models.append(load_lexica_model(model_path))

        return LexicaMatcher(lexica_models)

    def remove(self, tenant: str):
        """Removes the tenant information from the file storage.

        :param tenant: The tenant name to be removed from the file storage
        :return:
        """
        fs = self.get_client()
        tenant_model_files: List[str] = fs.glob(
            f"{LEXICA_CONFIG.LEXICA_STORAGE_PATH()}/{tenant}___*.pkl"
        )
        self.get_client().rm(tenant_model_files)

    def save_standard_models(self, lexica_models: List[LexicaModelWrapper], model_folder: str):
        for lexica_model in lexica_models:
            model_path = f"{model_folder}/standard___{lexica_model.version_tag}.pkl"
            with fsspec.open(model_path, "wb") as f:
                f.write(save_lexica_model_wrapper_to_buffer(lexica_model).getvalue())
            logging.warning(f"Saved model for language group: '{lexica_model.lang_group}'")

    def save(
        self,
        tenant: str,
        lexica_models: List[LexicaModelWrapper],
        se_lexica_version: str,
        subscribed_languages: List[str],
        version_tag: str,
    ):
        """Save LexicaModelWrapper to file storage."""
        build_time = int(time.time())
        saved_models = {}
        for lexica_model in lexica_models:
            model_path = f"{LEXICA_CONFIG.LEXICA_STORAGE_PATH()}/{tenant}___{build_time}___{lexica_model.version_tag}.pkl"  # noqa
            with fsspec.open(model_path, "wb") as f:
                f.write(save_lexica_model_wrapper_to_buffer(lexica_model).getvalue())
            saved_models[lexica_model.lang_group] = model_path
            logging.warning(f"Saved model for language group: '{lexica_model.lang_group}'")

        self.update_model_index(
            tenant, saved_models, se_lexica_version, subscribed_languages, version_tag
        )

    def update_model_index(
        self,
        tenant: str,
        saved_models: Dict[tuple[str, ...], str],
        se_lexica_version: str,
        subscribed_languages: List[str],
        version_tag: str,
    ):
        try:
            model_index: dict = json.loads(read_file(self.model_index_path) or "{}")
        except FileNotFoundError:
            logging.error(
                f"Model Index missing from {self.model_index_path}... Defaulting to empty index"
            )
            model_index = {}

        lang_group_to_path: Dict[tuple[str, ...], str] = {
            LANG_TO_GROUP_MAP[
                lang
            ]: f"{LEXICA_CONFIG.MASTER_DATA_MODEL_STORAGE_PATH}/{CODE_VERSION}/standard___steeleye___{se_lexica_version}___{'_'.join(sorted(list(LANG_TO_GROUP_MAP[lang])))}.pkl"  # noqa
            for lang in subscribed_languages
        }
        lang_group_to_path.update(saved_models)

        model_index[tenant] = {
            "version_tag": version_tag,
            "models": list(lang_group_to_path.values()),
        }
        with fsspec.open(self.model_index_path, "w") as f:
            f.write(json.dumps(model_index))

    def get_model_index(self, tenant) -> Dict[str, Union[str, List[str]]]:
        try:
            model_index: dict = load_model_index_ttl(self.model_index_path)
        except FileNotFoundError:
            logging.error(
                f"Model Index missing from {self.model_index_path}... Defaulting to empty index"
            )
            model_index = {}
        return model_index.get(tenant, {})  # type: ignore

    def get_version_for_tenant(self, tenant: str) -> str:
        """Retrieves the saved version for a given tenant.

        :param tenant: name of the tenant to get the hash version
        :return: Version string
        """
        available_version: str = self.get_model_index(tenant).get("version_tag")  # type: ignore
        logging.warning(f"{available_version} version is present for tenant: {tenant}")
        return available_version

    @staticmethod
    def get_s3_client(region_name: str):
        params = {}
        if LEXICA_CONFIG.SE_S3_ARN_ROLE is not None:
            sts_client = boto3.client("sts")
            session_name = f"SeS3Session-{datetime.datetime.now().timestamp()}"
            assumed_role_object = sts_client.assume_role(
                RoleArn=LEXICA_CONFIG.SE_S3_ARN_ROLE, RoleSessionName=session_name
            )
            credentials = assumed_role_object["Credentials"]
            params = dict(
                aws_access_key_id=credentials["AccessKeyId"],
                aws_secret_access_key=credentials["SecretAccessKey"],
                aws_session_token=credentials["SessionToken"],
                region_name=region_name,
            )
        return boto3.client("s3", **params)


def read_file(file_path: str):
    """Load from File Storage."""
    try:
        if file_path.startswith("s3://masterdata.steeleye.co"):
            bucket_name = "masterdata.steeleye.co"
            key = file_path.split("s3://masterdata.steeleye.co/")[1]
            return (
                LexicaModelStorage.get_s3_client(region_name="eu-west-1")
                .get_object(Bucket=bucket_name, Key=key)["Body"]
                .read()
            )
        else:
            return LexicaModelStorage.create_client(file_path).open(file_path, mode="rb").read()
    except FileNotFoundError:
        raise FileNotFoundError(f"There is no file: {file_path}")
    except EOFError:
        logging.info(
            f"Bool to check if the file exists: {os.path.isfile(file_path)} for {file_path}"
        )
        raise EOFError(f"Possibly reading an empty file: {file_path}")


@lru_cache(maxsize=LEXICA_MATCHER_CONFIG.MAX_SIZE_MODEL_CACHING)
def load_lexica_model(model_path: str) -> LexicaModelWrapper:
    logging.warning(f"Loading model: '{model_path}'")
    return load_lexica_model_wrapper(io.BytesIO(read_file(model_path)))


@cached(cache=TTLCache(maxsize=1, ttl=120))
def load_model_index_ttl(index_file_name: str) -> dict:
    return dict(json.loads(read_file(index_file_name) or "{}"))
