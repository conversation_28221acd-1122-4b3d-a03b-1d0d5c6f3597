import logging
from collections import Counter, defaultdict
from lexica_matcher.lexica_model_wrapper import LexicaModelWrapper
from lexica_matcher.schemas.definition import LexicaDefinition, SearchType
from lexica_matcher.schemas.output import LexicaTopic, MatcherOutput
from se_schema.components.comms.analytics.lexica_hit import LexicaHit
from spacy.tokens import Doc, Span
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)


class LexicaMatcher:
    lowercase_all_input: bool
    term2fullformat: Dict[str, LexicaDefinition]
    nlp_models: Dict[Tuple[str, ...], LexicaModelWrapper]

    def __init__(self, list_parts_path: List[LexicaModelWrapper]):
        self.lowercase_all_input = True
        self.nlp_models = {}
        for part in list_parts_path:
            self.nlp_models[part.lang_group] = part

    ### Evaluators

    def evaluate_text(self, text: str) -> MatcherOutput:
        if self.lowercase_all_input:
            text = text.lower()

        hits, topics = [], []
        for lang_group, parts in self.nlp_models.items():
            tokenized_text = parts.nlp.tokenizer(text)
            hits.extend(self.evaluate_lexica(lang_group=lang_group, msg=tokenized_text))
            topics.extend(self.evaluate_topics(lang_group=lang_group, msg=tokenized_text))

        return MatcherOutput(
            hits=hits,
            topics=topics,
        )

    def evaluate_text_batch(self, text_batch: List[str]) -> List[MatcherOutput]:
        """Evaluate a list of messages."""
        if self.lowercase_all_input:
            text_batch = [t.lower() for t in text_batch]

        list_hits: List[List[LexicaHit]] = [[]] * len(text_batch)
        list_topics: List[List[LexicaTopic]] = [[]] * len(text_batch)
        for lang_group, parts in self.nlp_models.items():
            tokenized_text_batch = list(parts.nlp.tokenizer.pipe(text_batch))
            for i, tokenized_text in enumerate(tokenized_text_batch):
                list_hits[i].extend(self.evaluate_lexica(lang_group=lang_group, msg=tokenized_text))
                list_topics[i].extend(
                    self.evaluate_topics(lang_group=lang_group, msg=tokenized_text)
                )

        return [MatcherOutput(hits=h, topics=t) for h, t in zip(list_hits, list_topics)]

    ### Evaluators - lexica
    def evaluate_lexica(self, lang_group: Tuple[str, ...], msg: Doc) -> List[LexicaHit]:
        """Evaluate a single message."""
        matches_found = self.nlp_models[lang_group].all_lexica_matcher(msg)
        results: Dict[str, List[Span]] = defaultdict(list)
        for match in matches_found:
            term_ix, tok_start, tok_end = match
            term_id = self.nlp_models[lang_group].nlp.vocab.strings[term_ix]
            if not self.check_exclusions(lang_group, term_id, msg):
                trigger = msg[tok_start:tok_end]
                if results.get(term_id, {}):
                    if (
                        results[term_id][0].start_char
                        < trigger.start_char
                        < results[term_id][0].end_char
                        or trigger.start_char < results[term_id][0].start_char < trigger.end_char
                    ):
                        logger.info("Skipping overlapping hit")
                        continue

                results[term_id].append(trigger)
        return self.format_matches(results)

    def check_exclusions(self, lang_group: Tuple[str, ...], term_id: str, msg: Doc) -> bool:
        e = self.nlp_models[lang_group].term2exclusion.get(term_id, "")
        if e == "":
            return False

        exclusion_matcher = self.nlp_models[lang_group].exclusion2matcher[e]  # type: ignore
        exclusions_found = exclusion_matcher(msg, as_spans=True)

        if (
            self.nlp_models[lang_group].term2fullformat[term_id].termExclusionSearchType
            == SearchType.AND
        ):
            return set(e) == set(exclusions_found)

        return len(exclusions_found) > 0

    def format_matches(self, terms_triggers: Dict[str, List[Span]]) -> List[LexicaHit]:
        matches = list()
        parts = self.nlp_models.values()
        for term_id, triggers in terms_triggers.items():
            for part in parts:
                match = part.term2fullformat.get(term_id)
                if match is not None:  # we found the match
                    matched = match.dict(exclude_none=True)
                    matched["term"] = matched["termToSearchFor"]
                    matched["triggers"] = [
                        {
                            "trigger": trigger.text,
                            "triggerStartChar": trigger.start_char,
                            "triggerEndChar": trigger.end_char,
                        }
                        for trigger in triggers
                    ]
                    matched = LexicaHit.parse_obj(
                        {k: v for k, v in matched.items() if k in LexicaHit.__fields__.keys()}
                    )

                    matches.append(matched)
                    break  # we found and processed the match, we can move on.
        return matches

    ### Evaluators - topics
    def evaluate_topics(self, lang_group: Tuple[str, ...], msg: Doc) -> List[LexicaTopic]:
        """Find topics in a single message."""
        topic_matches = self.nlp_models[lang_group].all_topic_matcher(msg)
        topics = []
        for t in topic_matches:
            term_ix = t[0]
            topic = self.nlp_models[lang_group].nlp.vocab.strings[term_ix]
            topics.append(topic)
        return [LexicaTopic(topic=t, termCount=c) for t, c in Counter(topics).items()]
