python_sources(
    sources=["lexica_matcher/**/*.py"],
    dependencies=[
        "//:lexica_models#da-core-news-sm",
        "//:lexica_models#de-core-news-sm",
        "//:lexica_models#en-core-web-sm",
        "//:lexica_models#es_core_news_sm",
        "//:lexica_models#fi-core-news-sm",
        "//:lexica_models#fr-core-news-sm",
        "//:lexica_models#it-core-news-sm",
        "//:lexica_models#ja-core-news-sm",
        "//:lexica_models#ko-core-news-sm",
        "//:lexica_models#nb-core-news-sm",
        "//:lexica_models#nl-core-news-sm",
        "//:lexica_models#pl-core-news-sm",
        "//:lexica_models#pt-core-news-sm",
        "//:lexica_models#ru-core-news-sm",
        "//:lexica_models#sv-core-news-sm",
        "//:lexica_models#xx-ent-wiki-sm",
        "//:lexica_models#zh-core-web-sm",
        "//:3rdparty#pandas",
        "//:3rdparty#pytz",
    ],
    tags=["amd_only"],
)

python_tests(
    name="tests",
    sources=["tests_lexica_matcher/test_*.py"],
    tags=["amd_only"],
)
