from enum import Enum
from typing import Dict, List


class InstrumentTypeGroup(str, Enum):
    # NOTE: CollatetizedMortgageObligations is correct. It is a typo in refinitiv side
    COLLATETIZED_MORTGAGE_OBLIGATIONS = "CollatetizedMortgageObligations"
    COMMODITIES = "Commodities"
    EQUITIES = "Equities"
    FUNDS = "Funds"
    FUTURES_AND_OPTIONS = "FuturesAndOptions"
    GOV_CROP = "GovCorp"
    MONEY = "Money"
    MORTGAGE_BACKED_SECURITIES = "MortgageBackedSecurities"
    MUNICIPALS = "Municipals"


class FuturesOrOptionsTypeEnum(str, Enum):
    FUTURES = "Futures"
    OPTIONS = "Options"


class IdentifierTypeEnum(str, Enum):
    ISIN = "Isin"
    RIC = "RIC"
    RIC_ROOT = "RICRoot"


class MappingIdentifierType(str, Enum):
    RIC = "Ric"
    ISIN = "Isin"
    CUSIP = "Cusip"


class RefinitivCredentials:
    LEVEL_1_USER = "REFINITIV_1_USERNAME"
    LEVEL_1_PASSWORD = "REFINITIV_1_PASSWORD"
    LEVEL_2_USER = "REFINITIV_2_USERNAME"
    LEVEL_2_PASSWORD = "REFINITIV_2_PASSWORD"


class RefinitivProduct(str, Enum):
    ELEKTRON = "ELEKTRON"
    EOD_PRICING = "EOD_PRICING"
    TICK = "TICK"
    OBD = "OBD"


ELEKTRON_EOD_MAP = {
    "Open Interest": "Open Interest",
    "Open": "Open Price",
    "High": "High Price",
    "Low": "Low Price",
    "Low Ask": "Ask Low",
    "Low Bid": "Bid Low",
}

CONTENT_FIELD_NAMES_MAP: Dict[RefinitivProduct, List[str]] = {
    RefinitivProduct.EOD_PRICING: [
        "Open Price",
        "Universal Close Price",
        "RIC",
        "Exchange Code",
        "Trade Date",
        "Currency Code",
        "Volume",
        "Open Interest",
        "Universal Ask Price",
        "Universal Bid Price",
        "High Price",
        "Ask High",
        "Bid High",
        "Ask Low",
        "Bid Low",
        "Low Price",
        "VWAP Price",
        "Instrument ID",
    ],
    RefinitivProduct.ELEKTRON: [
        "RIC",
        "Exchange Code",
        "Currency Code",
        "Trade Date",
        "Volume",
        "Open Interest",
        "Open",
        "Universal Close Price",
        "Universal Ask Price",
        "Universal Bid Price",
        "High",
        "Ask High",
        "Bid High",
        "Low",
        "Low Ask",
        "Low Bid",
        "Market VWAP",
        "VWAP",
        "Instrument ID",
    ],
    RefinitivProduct.TICK: [
        "Trade - Price",
        "Trade - Volume",
        "Trade - Market VWAP",
        "Quote - Bid Price",
        "Quote - Ask Price",
        "Quote - Exchange Time",
        "Trade - Exchange Time",
        "Trade - Open",
        "Trade - High",
        "Trade - Low",
        "Trade - Mid Price",
        "Quote - Mid Price",
        "Trade - Trade Price Currency",
        "Trade - Date",
        "Quote - Bid Size",
        "Quote - Accumulated Bid Order",
        "Quote - Accumulated Ask Order",
        "Quote - Ask Size",
        "Auction - Ask Price",
        "Auction - Ask Size",
        "Auction - Bid Price",
        "Auction - BId Size",
        "Auction - Exchange Time",
        "Auction - Price",
        "Auction - Qualifiers",
        "Auction - Sequence Number",
        "Auction - Volume",
        "Trade - Indicative Auction Price",
        "Trade - Indicative Auction Volume",
    ],
    RefinitivProduct.OBD: [
        "Ask Price",
        "Ask Size",
        "Bid Price",
        "Bid Size",
        "Exchange Time",
        "Number of Buyers",
        "Number of Sellers",
    ],
}
