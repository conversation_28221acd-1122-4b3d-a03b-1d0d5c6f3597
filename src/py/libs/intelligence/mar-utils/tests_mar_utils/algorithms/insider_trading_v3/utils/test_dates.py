from mar_utils.algorithms.insider_trading_v3.utils.dates import determine_behavior_periods
from mar_utils.data_repository.type import RangeResult
from pandas import Timestamp


class TestDates:
    def test_determine_behavior_periods(self) -> None:
        result = determine_behavior_periods(
            observation_period_range=RangeResult(
                start=Timestamp(2023, 8, 21), end=Timestamp(2023, 8, 22)
            ),
            observation_period=2,
            behaviour_period=1,
        )
        assert result == [
            RangeResult(
                start=Timestamp("2023-08-17 00:00:00"), end=Timestamp("2023-08-18 00:00:00")
            )
        ]
