import json
import uuid
from mar_utils.alerting.utils import scenario_itv3_meta_id_generator
from pathlib import Path
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


def get_surveillance_watch(watch_filename: str) -> SurveillanceWatch:
    sample_surveillance_watch = json.loads(TEST_DATA_PATH.joinpath(watch_filename).read_text())
    return SurveillanceWatch.validate(sample_surveillance_watch)


def test_scenario_id_retained_when_historical_executions_disabled():
    watch = get_surveillance_watch(
        watch_filename="surveillance_watch_without_historical_execution.json"
    )
    scenario = {"scenario_id": "1234", "raw": "{}"}

    result = scenario_itv3_meta_id_generator(scenario, watch)

    assert result == "1234"


def test_scenario_id_generated_when_historical_executions_enabled():
    watch = get_surveillance_watch(watch_filename="surveillance_watch_w_historical_execution.json")

    raw_data = {
        "records": {"key1": "value1", "key2": "value2"},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Monday", "time": "12:00"}}},
    }
    scenario = {"scenario_id": "1234", "raw": json.dumps(raw_data)}

    result = scenario_itv3_meta_id_generator(scenario, watch)

    assert isinstance(result, str) and len(result) == 36
    assert result != "1234"


def test_scenario_id_generation_deterministic():
    watch = get_surveillance_watch(watch_filename="surveillance_watch_w_historical_execution.json")

    raw_data = {
        "records": {"key1": "value1", "key2": "value2"},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Monday", "time": "12:00"}}},
    }

    scenario = {"scenario_id": "12345", "raw": json.dumps(raw_data)}

    raw_data = {
        "records": {"key2": "value2", "key1": "value1"},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Monday", "time": "12:00"}}},
    }
    scenario_2 = {"scenario_id": "123", "raw": json.dumps(raw_data)}

    result1 = scenario_itv3_meta_id_generator(scenario, watch)
    result2 = scenario_itv3_meta_id_generator(scenario_2, watch)

    assert result1 == result2


def test_scenario_id_generation_changes_with_different_data():
    watch = get_surveillance_watch(watch_filename="surveillance_watch_w_historical_execution.json")

    raw_data1 = {
        "records": {"key1": "value1"},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Monday"}}},
    }
    raw_data2 = {
        "records": {"key1": "value1", "key3": "value3"},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Tuesday"}}},
    }
    id1 = "1234"
    id2 = "12345"
    scenario1 = {"scenario_id": id1, "raw": json.dumps(raw_data1)}
    scenario2 = {"scenario_id": id2, "raw": json.dumps(raw_data2)}

    result1 = scenario_itv3_meta_id_generator(scenario1, watch)
    result2 = scenario_itv3_meta_id_generator(scenario2, watch)

    assert result1 != result2
    assert result1 != id1 and result2 != id1


def test_scenario_id_consistent_with_different_key_order_and_empty_values():
    watch = get_surveillance_watch(watch_filename="surveillance_watch_w_historical_execution.json")

    raw_data1 = {
        "records": {
            "key1": "value1",
            "key2": "value2",
            "empty_string": "",
        },
        "additionalFields": {
            "topLevel": {
                "eventDayDetails": {
                    "extra_list": [{"key2": {"nested_dict": "value1"}, "key3": ""}],
                    "day": "Monday",
                    "time": "12:00",
                    "none_value": None,
                }
            }
        },
    }

    raw_data2 = {
        "records": {"key2": "value2", "key1": "value1", "key3": ""},
        "additionalFields": {
            "topLevel": {
                "eventDayDetails": {
                    "time": "12:00",
                    "day": "Monday",
                    "extra_list": [{"key2": {"nested_dict": "value1"}}, "", []],
                    "extraList": [],
                },
                "extraField": [],
            }
        },
    }

    scenario1 = {"scenario_id": "1234", "raw": json.dumps(raw_data1)}
    scenario2 = {"scenario_id": "12345", "raw": json.dumps(raw_data2)}

    result1 = scenario_itv3_meta_id_generator(scenario1, watch)
    result2 = scenario_itv3_meta_id_generator(scenario2, watch)

    assert result1 == result2


def test_scenario_id_generation_with_large_input():
    watch = get_surveillance_watch(watch_filename="surveillance_watch_w_historical_execution.json")

    # simulates a large number os orders within the scenario
    large_input_of_records = "Long Key" * 10_000_000
    raw_data = {
        "records": {"key": large_input_of_records},
        "additionalFields": {"topLevel": {"eventDayDetails": {"day": "Monday"}}},
    }
    scenario = {"scenario_id": "1234", "raw": json.dumps(raw_data)}

    result = scenario_itv3_meta_id_generator(scenario, watch)

    assert isinstance(result, str) and len(result) == 36
    assert uuid.UUID(result).version == 5
