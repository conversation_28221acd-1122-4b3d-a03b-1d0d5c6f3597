class SkipIfNoParquetFile(Exception):
    """Exception raised when there are no OrderFiles present in the catalog for
    a given range."""


class FailIfNoRics(Exception):
    """Exception raised when there are no RICs returned from the master-data-
    api."""


class FailIfConnectionError(Exception):
    """Exception raised on connection error."""


class FailOnUnexpectedError(Exception):
    """Generic Exception which is raised on any `unexpected` error run during
    the execution."""


class FailIfNoSelerityData(Exception):
    """Exception raise when there's no selerity news data."""


class FailIfInvalidIdentifierType(Exception):
    """Exception raise when the identifier type is not either ISIN or LEI."""


class FailIfInvalidIdentifierValue(Exception):
    """Exception raise when the identifier value - None"""


class FailIfNoRestrictedListId(Exception):
    """Exception raise when there isn't any restrictedListId."""


class FailIfLastTimeItWasExecuted(Exception):
    """Exception raise when there isn't the last time it was executed."""


class FailIfNoInstrumentType(Exception):
    """Exception raise when there isn't any instrument or instrument type."""
