import json
import logging
import uuid
from aries_io_event.app_metric import AppMetricFieldSet
from mar_utils.algorithms.common.surveillance_watches import check_historical_executions
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_elastic_schema.models import SurveillanceWatch

logger = logging.getLogger(__name__)


def _remove_unneeded_meta_from_hit(
    hit_source: dict,
):
    meta_fields = GammaMetaFields()

    meta_fields_to_remove = [
        meta_fields._ancestor,
        meta_fields._user,
        meta_fields._parent,
        meta_fields._hash,
        meta_fields._version,
        meta_fields._timestamp,
        meta_fields._model,
        meta_fields._validationErrors,
    ]

    hit = {k: hit_source[k] for k in hit_source if k not in meta_fields_to_remove}

    return hit


def update_app_metrics(
    aries_task_input_name: str,
    alerts_detected: int,
    alerted_orders: int,
    time_for_algo_execution: int,
    time_for_alert_creation: int,
) -> AppMetricFieldSet:
    app_metrics = AppMetricFieldSet(
        metrics={
            "dig": {
                aries_task_input_name: {
                    "alerts_detected": alerts_detected,
                    "orders_alerted": alerted_orders,
                    "time_for_algo_execution": time_for_algo_execution,
                    "time_for_alert_creation": time_for_alert_creation,
                }
            }
        }
    )
    logger.info(json.dumps(app_metrics.dict()))
    return app_metrics


def drop_empty_keys(data: dict) -> dict:
    """Recursively removes keys with empty or null values from a dictionary. An
    empty value is defined as:

    - None
    - An empty string: ""
    - An empty list: []
    - A sub-dictionary that itself is empty after processing
    """
    result = {}
    for key, value in data.items():
        if isinstance(value, dict):
            child_result = drop_empty_keys(value)
            if child_result:
                result[key] = child_result
        elif isinstance(value, list):
            filtered_list = []
            for item in value:
                if isinstance(item, dict):
                    child_result = drop_empty_keys(item)
                    if child_result:
                        filtered_list.append(child_result)
                elif item:
                    filtered_list.append(item)
            if filtered_list:
                result[key] = filtered_list
        elif value:
            result[key] = value

    return result


def scenario_itv3_meta_id_generator(scenario: dict, watch: SurveillanceWatch) -> str:
    """Generates a uuid based on the Scenario records and eventDayDetails Only
    used when historicalExecutions is enabled.

    Otherwise, it keeps the uuid it already has
    """

    if not check_historical_executions(watch):
        return scenario["scenario_id"]
    logger.debug(
        "Using `scenario_itv3_meta_id_generator` to generate scenarioId. Creating new scenarioId."  # noqa: E501
    )

    tag_text_dict = json.loads(scenario["raw"])

    records = drop_empty_keys(tag_text_dict.get("records", {}))
    event_day_details = drop_empty_keys(
        tag_text_dict.get("additionalFields", {}).get("topLevel", {}).get("eventDayDetails", {})
    )

    records_str = json.dumps(records, sort_keys=True)
    event_day_details_str = json.dumps(event_day_details, sort_keys=True)

    normalized_string = "".join(f"{records_str}{event_day_details_str}".split())

    # Generate a deterministic UUID using uuid5 with a fixed namespace
    namespace = uuid.NAMESPACE_DNS
    result_uuid = uuid.uuid5(namespace, normalized_string)
    return str(result_uuid)
