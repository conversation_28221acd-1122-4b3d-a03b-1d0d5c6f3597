# type: ignore

import datetime
import json
import logging
import pandas as pd
import uuid
from mar_utils.alerting.alerts_handling import get_alert_watch_detail
from mar_utils.alerting.schema import (
    MarketAbuseAdditionalFields,
    MarketAbuseAlgoResult,
    MarketAbuseCommonThresholds,
    MarketAbuseRecordsResult,
)
from mar_utils.static.order_fields import OrderFields
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_elastic_schema.components.surveillance.alert_watch_detail import AlertWatchDetail
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.surveillance import AlertHitStatus
from surveillance_utils.static import DT_FMT
from typing import Optional

logger = logging.getLogger(__name__)


class MarketAbuseScenario:
    def __init__(
        self,
        alert_data: pd.DataFrame,
        list_of_additional_fields: list,
        thresholds: Optional[MarketAbuseCommonThresholds] = None,
    ):
        self.alert_data = alert_data
        self.additional_fields = list_of_additional_fields
        self.model_thresholds = thresholds
        self._scenario_id = uuid.uuid4().__str__()

    def get_scenario_id(self):
        return self._scenario_id

    def build_thresholds(self) -> dict:
        return self.model_thresholds.dict()

    def build_additional_fields(self) -> MarketAbuseAdditionalFields:
        return

    def build_records(self) -> dict:
        if OrderFields.EXC_DTL_ORD_STATUS not in self.alert_data:
            newos = set(
                [
                    key
                    for key in self.alert_data[OrderFields.KEY].to_list()
                    if OrderStatus.NEWO in key
                ]
            )
            executions = set(self.alert_data[OrderFields.KEY].to_list()).difference(newos)

        else:
            newos = set(
                self.alert_data[
                    self.alert_data[OrderFields.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO
                ][OrderFields.KEY]
            )
            executions = set(
                self.alert_data[
                    self.alert_data[OrderFields.EXC_DTL_ORD_STATUS].isin(
                        [OrderStatus.FILL, OrderStatus.PARF]
                    )
                ][OrderFields.KEY]
            )

        records = {"orders": list(newos), "executions": list(executions)}
        return MarketAbuseRecordsResult(**records).dict()

    def build_scenario_tag(self) -> dict:
        """Create tagText str to be used in the scenario model."""

        tag_text = {
            "thresholds": self.build_thresholds(),
            "records": self.build_records(),
            "additionalFields": self.build_additional_fields(),
        }

        return MarketAbuseAlgoResult(**tag_text).dict()

    def create_scenario(
        self, surveillance_watch: SurveillanceWatch, surveillance_watch_execution_id: str
    ) -> dict:
        """
        :param surveillance_watch:
        :param surveillance_watch_execution_id:
        :return:
        """

        alert_details: AlertWatchDetail = get_alert_watch_detail(
            surveillance_watch=surveillance_watch
        )

        meta_fields = GammaMetaFields()

        scenario_dict = {
            meta_fields._parent: surveillance_watch_execution_id,
            # meta_fields._hash: hit_source.get(meta_fields._hash),
            # meta_fields._user: hit_source.get(meta_fields._user),
            # meta_fields._version: hit_source.get(meta_fields._version),
            # meta_fields._timestamp: hit_source.get(meta_fields._timestamp),
            "detail": alert_details,
            "detected": datetime.datetime.now().strftime(DT_FMT),
            "scenarioId": self._scenario_id,
            "workflow": {
                "status": AlertHitStatus.UNRESOLVED.value,
                "assigneeId": surveillance_watch.defaultAssigneeId,
                "assigneeName": surveillance_watch.defaultAssigneeName,
            },
            "tagText": json.dumps(self.build_scenario_tag()),
            "watchExecutionId": surveillance_watch_execution_id,
        }

        return scenario_dict

    def _get_list_data_from_alert(self, column_to_check: str):
        return (
            self.alert_data[column_to_check].dropna().unique().tolist()
            if column_to_check in self.alert_data.columns
            else None
        )

    def _get_sum_data_from_alert(self, column_to_check: str):
        return (
            self.alert_data[column_to_check].sum()
            if column_to_check in self.alert_data.columns
            else None
        )
