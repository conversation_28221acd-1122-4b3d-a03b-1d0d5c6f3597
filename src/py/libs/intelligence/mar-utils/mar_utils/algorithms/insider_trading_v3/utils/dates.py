import pandas as pd
from mar_utils.data_repository.type import RangeResult
from pandas.tseries.offsets import BusinessDay


def get_event_dates() -> list[pd.Timestamp]:
    # TODO: Implement f-lang https://steeleye.atlassian.net/browse/ENG-3045
    return [(pd.Timestamp.today().date() - BusinessDay())]


def determine_behavior_periods(
    observation_period_range: RangeResult,
    observation_period: int,
    behaviour_period: int,
) -> list[RangeResult]:
    """Calculates the tuples with starting and ending timestamps for each
    behaviour periods.

    :param observation_period_range: Tuple with observation period date range
    :param behaviour_period: threshold defined by the user
    :param observation_period: threshold defined by the user
    :return: list of tuples with period timestamps
    """

    behavior_periods_list = []
    for i in range(1, behaviour_period + 1):
        behaviour_time_from = observation_period_range.start - BusinessDay(observation_period * i)
        behaviour_time_to = observation_period_range.end - BusinessDay(observation_period * i)
        behaviour_period_range = RangeResult(start=behaviour_time_from, end=behaviour_time_to)
        behavior_periods_list.append(behaviour_period_range)

    return behavior_periods_list
