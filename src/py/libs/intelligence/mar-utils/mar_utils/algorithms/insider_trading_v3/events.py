import pandas as pd
from abc import ABC
from mar_utils.algorithms.insider_trading_v3.static import EventDirection
from mar_utils.data_repository.type import RangeResult
from mar_utils.static.eod_parquet_schema_fields import EoDStatsColumns
from typing import Optional


class BaseEvent(ABC):
    def __init__(self, date: pd.Timestamp, direction: EventDirection):
        self._date = date
        self._direction = direction

    def get_direction(self):
        return self._direction

    def get_event_day(self):
        return self._date


class MarketDataEvent(BaseEvent):
    def __init__(
        self,
        date: pd.Timestamp,
        direction: EventDirection,
        event_day_close_price: float,
        market_data: pd.DataFrame,
        close_price_variation: float,
        allowed_price_range: RangeResult,
    ):
        super().__init__(date=date, direction=direction)
        self._allowed_price_range = allowed_price_range
        self._event_day_close_price = event_day_close_price
        self._market_data = market_data
        self._price_close_variation = close_price_variation

    def get_close_price(self):
        return self._event_day_close_price

    def get_price_variation(self):
        return self._price_close_variation

    def get_12days_market_data(self):
        return self._market_data

    def get_price_range(self):
        return self._allowed_price_range

    def get_market_data_currency(self) -> Optional[str]:
        return self._market_data.loc[:, EoDStatsColumns.Currency].unique()[0]  # type: ignore


class NewsFeedEvent(BaseEvent):
    def __init__(
        self,
        date: pd.Timestamp,
        direction: EventDirection,
        news_frame: pd.DataFrame,
    ):
        super().__init__(date=date, direction=direction)
        self._news_frame = news_frame

    def get_news_frame(self):
        return self._news_frame
