import pandas as pd
from typing import Optional


def refactor_date_column(
    data: pd.DataFrame,
    time_series_column: str,
    date_format: Optional[str] = "%Y-%m-%d",
    sort: bool = False,
) -> pd.DataFrame:
    """Refactor the date column: set the date column as index after set it to
    ISO format and then sort the dataframe by the index (that should be a date
    column)

    :param data: Pandas DataFrame. It has all market data
    :param time_series_column: str. Datetime column name to set as index. Default is None
    :param date_format: bool. <PERSON>olean to set the datetime to isoformat. Default is True
    :param sort: bool. <PERSON><PERSON><PERSON> to check if it necessary to set the column to datetime. Default False

    :return: Pandas Dataframe with the Date column in datetime type
    """
    if date_format:
        data.loc[:, time_series_column] = pd.to_datetime(
            data[time_series_column], format=date_format
        )
    else:
        data.loc[:, time_series_column] = pd.to_datetime(data[time_series_column])

    data = data.set_index(time_series_column)

    if sort:
        data = data.sort_index()

    return data
