import backoff
import httpx
import json
import logging
import pandas as pd
import uuid
from datetime import datetime
from mar_utils.static.selerity_entity_lookup import (
    SelerityColumns,
    SelerityEndpoints,
    SelerityQlikColumns,
)
from pandas import json_normalize
from typing import Any

log = logging.getLogger(__name__)


class SelerityDataRepository:
    __slots__ = ("api_url", "api_key", "cache_expiry_seconds", "__news_sources")

    def __init__(self, api_url: str, api_key: str, cache_expiry_seconds: int) -> None:
        self.api_url: str = api_url
        self.api_key: str = api_key
        self.cache_expiry_seconds = cache_expiry_seconds
        self.__news_sources: list[str] | None = None

    @property
    def get_news_sources(self):
        return self.__news_sources

    def set_news_sources(self) -> None:
        log.info("Fetching news sources from the selerity API")
        try:
            if self.__news_sources:
                return

            self.__news_sources = self._do_request(
                endpoint=SelerityEndpoints.Sources,
                query_data={"apiKey": self.api_key, "requestSent": datetime.now().isoformat()},
            ).get("sources")
            log.info("Successfully fetched and set Selerity News Sources...")
        except Exception as e:  # pragma: no cover
            log.exception(f"Failed to fetch news sources with exception: {e}")

    def get_selerity_data(
        self,
        event_date_start: str,
        event_date_end: str,
        entity_id: str,
        relevance: float,
        query_type: str,
        override_news_sources: list[str] | None = None,
    ) -> pd.DataFrame:
        try:
            selerity_query = {
                SelerityColumns.BatchID: SelerityQlikColumns.EntityId,
                SelerityColumns.QueryString: f"""
                                    AND(BETWEEN({event_date_start}, {event_date_end}),
                                    AND(CONCEPT({entity_id}, {relevance}, 1.0),{query_type}
                                """,
            }
            # Create redis key and check cache, if found return
            # Will be done in https://steeleye.atlassian.net/browse/ENG-3914

            news_data = self._do_request(
                endpoint=SelerityEndpoints.Query,
                query_data=self.get_selerity_query(
                    batch_queries=selerity_query, override_news_sources=override_news_sources
                ),
            )["recommendationBatches"]

            # set cache
            return json_normalize(
                data=news_data,
                record_path="recommendations",
                meta=SelerityColumns.BatchID,
            )
        except Exception as e:  # pragma: no cover
            log.exception(f"Failed to fetch selerity news data with exception: {e}")
            return pd.DataFrame()

    def get_selerity_query(
        self,
        batch_queries: dict[str, Any],
        override_news_sources: list[str] | None = None,
    ) -> dict[str, Any]:
        return {
            "apiKey": self.api_key,
            "sessionID": f"SteelEye-{str(uuid.uuid4())}",
            "requestSent": datetime.now().isoformat(),
            "parameters": {
                "queryType": "RECOMMENDATION",
                "queryMode": "INITIAL",
                "language": "en",
                "numItems": 21,
                "contributionMode": "ALL",
                "showContributionSource": True,
                "sources": override_news_sources or self.__news_sources,
            },
            "interests": {
                "batchQuery": {
                    "batchQueries": [batch_queries],
                    "mergeResults": False,
                    "omitEmptyBatches": True,
                }
            },
        }

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def _do_request(
        self,
        endpoint: str,
        query_data: dict[str, Any],
    ) -> dict[str, Any]:  # pragma: no cover
        response = httpx.post(
            url=f"{self.api_url}/{endpoint}",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            content=f"json={json.dumps(query_data)}",
        )

        response.raise_for_status()
        return response.json()  # type: ignore
