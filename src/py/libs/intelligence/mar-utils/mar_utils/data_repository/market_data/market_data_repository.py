import addict
import datetime
import itertools
import logging
import pandas as pd
from market_data_sdk.client import MarketDataAPI  # pants: no-infer-dep
from market_data_sdk.obd_client import OBDMarketDataAPI  # pants: no-infer-dep
from market_data_sdk.recipe.statistics import (
    calculate_exponential_moving_average,  # pants: no-infer-dep; pants: no-infer-dep
    calculate_volatility,  # pants: no-infer-dep; pants: no-infer-dep
)
from market_data_sdk.recipe.utilities import (
    get_data_based_on_window,  # pants: no-infer-dep; pants: no-infer-dep
    get_integer_from_time_window,  # pants: no-infer-dep; pants: no-infer-dep
)
from market_data_sdk.schema.api.components import (
    APIResponse,  # pants: no-infer-dep
    APIResponseStatus,  # pants: no-infer-dep; pants: no-infer-dep
)
from market_data_sdk.schema.parquet import (
    EoDStatsColumns,  # pants: no-infer-dep; pants: no-infer-dep
    StatsColumns,  # pants: no-infer-dep; pants: no-infer-dep
    TradeStatsColumns,  # pants: no-infer-dep; pants: no-infer-dep
)
from market_data_sdk.schema.refinitiv import (
    RefinitivEventType,  # pants: no-infer-dep
    RefinitivExtractColumns,  # pants: no-infer-dep; pants: no-infer-dep
)
from market_data_sdk.store import (
    MarketDataStoreError,  # pants: no-infer-dep
    MasterDataApiClientStore,  # pants: no-infer-dep; pants: no-infer-dep
)
from master_data_api_client.api_client.auth_client import AuthClient as MasterDataAuthClient
from master_data_api_client.api_client.client import ApiClient
from se_api_client.base import ParsedResponse
from typing import Any, List, Optional, Union

logger = logging.getLogger(__name__)


class MarketDataRepository:
    __slots__ = (
        "master_data_api_host",
        "cognito_client_id",
        "cognito_client_secret",
        "cognito_auth_url",
        "market_data_storage_bucket",
    )

    def __init__(
        self,
        master_data_api_host: str,
        cognito_client_id: str,
        cognito_client_secret: str,
        cognito_auth_url: str,
        market_data_storage_bucket: str,
    ) -> None:
        self.master_data_api_host = master_data_api_host
        self.cognito_client_id = cognito_client_id
        self.cognito_client_secret = cognito_client_secret
        self.cognito_auth_url = cognito_auth_url
        self.market_data_storage_bucket = market_data_storage_bucket

    @property
    def _master_client(self) -> ApiClient:
        """Returns master-data-api-client."""
        return self._get_api_client()

    @property
    def _master_store(self) -> MasterDataApiClientStore:
        """Returns master-data-api Ric api client."""
        return self._initialize_master_data_store()

    @property
    def _market_client(self) -> MarketDataAPI:
        """Returns master-data-api Ric api client."""
        return self._get_market_data_client()

    @property
    def _obd_client(self) -> OBDMarketDataAPI:
        """Returns master-data-api Ric api client."""
        return self._get_obd_client()

    def _get_market_data_client(self):
        try:
            return MarketDataAPI(store=self._master_store)
        except MarketDataStoreError as e:
            raise ConnectionError(f"Cannot initialize MarketDataAPI: {str(e.exception)}")

    def _get_api_client(self) -> ApiClient:
        """Returns the default master-data-api API client which can be used in
        the other APIs of it. For example:

        If you want to use `Selerity` endpoints of master-data-api you can simply
        do:

        from master_data_api_client.api.selerity import Selerity
        selerity_client = Selerity(self.__master_data_api_client())
        """

        try:
            return ApiClient(
                host=self.master_data_api_host,
                auth_client=MasterDataAuthClient(
                    client_id=self.cognito_client_id,
                    client_secret=self.cognito_client_secret,
                    oauth2_url=self.cognito_auth_url,
                ),
            )
        except MarketDataStoreError as e:
            raise ConnectionError(f"Cannot initialize API client: {str(e.exception)}")

    def _get_obd_client(self):
        try:
            return self._market_client.refinitiv_client
        except MarketDataStoreError as e:
            raise ConnectionError(f"Cannot get OBD Client: {str(e.exception)}")

    def _initialize_master_data_store(self):
        try:
            return MasterDataApiClientStore(api_client=self._master_client)
        except MarketDataStoreError as e:
            raise ConnectionError(f"Cannot initialize market data store: {str(e.exception)}")

    def get_local_ric_map(
        self,
        list_id_venue: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
    ) -> Optional[dict]:
        """Builds a mapping between the input instrumentUniqueIdentifiers and
        their corresponding Local RIC value. Bulk fetches the Local RIC from
        master-data-api and build a dictionary with the mapping. Should any
        error occur while fetching or processing the RICs, it is captured and
        returned inside a pandas DataFrame.

        :param list_id_venue: A list of OrderField.INST_ID_VENUE_KEY
        :type list_id_venue: List[str]
        :param instrument_combinations: A list of lists containing the id_venue combinations
        :type instrument_combinations: List
        :return: Either a mapping between id_venues and RICs or a DataFrame containing the
        failure reason
        :rtype: Union[dict, pd.DataFrame]
        """
        if not list_id_venue:
            if not instrument_combinations:
                logger.error("No data provided")
                return None
            else:
                list_id_venue = list(itertools.chain(*instrument_combinations))

        try:
            response: ParsedResponse = self._obd_client.bulk_get_local_ric(
                list(set(list_id_venue))
            ).content["results"]

            result_dict = {}
            for doc in response:
                ric = doc.get("instrumentIdentification", {}).get("ric", None)
                if ric:
                    result_dict[doc["key"]] = ric

            if len(result_dict) == 0:
                logger.error("No RIC data available")
                raise MarketDataStoreError(status=APIResponseStatus.MISSING_RIC)

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch Local RICs in bulk for {list_id_venue}. "
                f"Error: {str(e.exception)}",
                exc_info=True,
            )
            return {}

        except ConnectionError as e:
            logger.warning(
                msg=f"Connection error when trying to fetch fetch "
                f"RICs in bulk for {list_id_venue}."
                f"Error: {str(e)}",
                exc_info=True,
            )
            return {}

        except Exception as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_id_venue}. Error: {str(e)}",
                exc_info=True,
            )
            return {}

        return result_dict

    def get_ric_map(
        self,
        list_inst_unique_id: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
        use_prefix_query: Optional[bool] = False,
    ) -> Union[dict, None]:
        """Builds a mapping between the input instrumentUniqueIdentifiers and
        their corresponding RIC value. Bulk fetches the RIC from master-data-
        api and build a dictionary with the mapping. Should any error occur
        while fetching or processing the RICs, it is captured and returned
        inside a pandas DataFrame.

        :param list_inst_unique_id: A list of OrderField.INST_EXT_UNIQUE_IDENT
        :type list_inst_unique_id: List[str]
        :return: Either a mapping between instrumentUniqueIdentifiers and
        RICs or a DataFrame containing the failure reason
        :rtype: Union[dict, pd.DataFrame]
        """
        if not list_inst_unique_id:
            if not instrument_combinations:
                logger.error("No data provided")
                return None
            else:
                list_inst_unique_id = list(itertools.chain(*instrument_combinations))

        try:
            response: ParsedResponse = (
                self._master_store._rics_api.post_bulk_rics_by_instrument_ids(
                    list(set(list_inst_unique_id)), use_prefix_query=use_prefix_query
                ).content["hits"]
            )

            result_dict = {}
            for doc in response:
                ric = self._master_store.select_ric(addict.Dict(doc))
                if isinstance(ric, str):
                    result_dict[doc["instrumentUniqueIdentifier"]] = ric

            if len(result_dict) == 0:
                logger.error("No RIC data available")
                raise MarketDataStoreError(status=APIResponseStatus.MISSING_RIC)

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_inst_unique_id}. "
                f"Error: {str(e.exception)}",
                exc_info=True,
            )
            return None

        except ConnectionError as e:
            logger.warning(
                msg=f"Connection error when trying to fetch fetch RICs "
                f"in bulk for {list_inst_unique_id}. "
                f"Error: {str(e)}",
                exc_info=True,
            )
            return None

        except Exception as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_inst_unique_id}. Error: {str(e)}",
                exc_info=True,
            )
            return None

        return result_dict

    def get_market_data_stats(
        self,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        currency: Optional[str] = None,
    ) -> pd.DataFrame:
        """Fetches trade stats and fetches quotes stats when trade stats are
        not available.

        :param instrument_ric: ric mapped to a given instrument
        :param instrument_unique_identifier: instrument id
        :param currency: optional selection of data by a specific currency
        :return: dataframe with trade or quote data
        """
        try:
            if instrument_ric is None:
                ric_lookup, ric_selected = self._master_store.fetch_ric_lookup(
                    instrument_id=instrument_unique_identifier
                )
            else:
                ric_selected = instrument_ric

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to get RIC for instrument {instrument_unique_identifier}. "
                f"Error: {str(e.status)}."
            )
            return pd.DataFrame()

        try:
            data: pd.DataFrame = self._obd_client.rolling_eod_stats(
                ric=ric_selected,
            )

            if not data.empty:
                data = self._process_market_data_stats(
                    df=data,
                    currency=currency,
                )

            if data.empty:
                logger.warning(
                    msg=f"No Elektron data for instrument {instrument_unique_identifier}",
                    exc_info=True,
                )
                return pd.DataFrame()

            return data

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch market data with event_type: {RefinitivEventType.ELEKTRON}. "
                f"RIC: {ric_selected}."
                f"Error: {str(e.exception)}",
                exc_info=True,
            )
            return pd.DataFrame()

        except ConnectionError as error:
            logger.warning(
                msg="Connection error when trying to fetch market data "
                f"with event_type: {RefinitivEventType.ELEKTRON}. "
                f"Error: {str(error)}",
                exc_info=True,
            )

            return pd.DataFrame()

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch market data with event_type: "
                f"{RefinitivEventType.ELEKTRON}. "
                f"RIC: {ric_selected}."
                f"Error: {str(e.exception)}",
                exc_info=True,
            )
            return pd.DataFrame()

        except ConnectionError as error:
            logger.warning(
                msg=f"Connection error when trying to fetch market data "
                f"with event_type: {RefinitivEventType.ELEKTRON}. "
                f"Error: {str(error)}",
                exc_info=True,
            )

            return pd.DataFrame()

    def get_ticks_timeframe(
        self,
        instrument_unique_identifier: str,
        from_date: datetime.datetime,
        to_date: datetime.datetime,
        event_type: RefinitivEventType = RefinitivEventType.TRADE,
    ) -> Any:
        """Returns the ticks for the requested instrument from `from_date` to
        `to_date`.

        Args:
            instrument_unique_identifier (str): The instrument for which to retrieve
            ticks
            from_date (datetime.datetime): The beginning of the time window
            for which to retrieve ticks
            to_date (datetime.datetime): The end of the time window for which to retrieve ticks
            event_type (RefinitivEventType, optional): The type of ticks
            to retrieve. Defaults to RefinitivEventType.TRADE.

        Returns:
            pd.DataFrame: The dataframe containint the ticks for the requested instrument
            within the requested time window
        """
        response = self._market_client.ticks_between_timestamps_for_instrument_id(
            instrument_id=instrument_unique_identifier,
            from_timestamp=from_date,
            to_timestamp=to_date,
            event_type=event_type,
        )

        return self._get_data_from_api_response(response=response)

    def get_ticks(
        self,
        dates: Union[datetime.datetime, List[datetime.datetime]],
        event_type: RefinitivEventType,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ) -> Any:
        if isinstance(dates, datetime.datetime):
            dates = [dates]

        results = []

        for date in dates:
            try:
                if instrument_ric is None:
                    response: APIResponse = self._market_client.ticks_for_instrument_id(
                        instrument_id=instrument_unique_identifier,
                        date=date,
                        event_type=event_type,
                    )
                else:
                    response: APIResponse = (  # type: ignore[no-redef]
                        self._market_client.ticks_for_ric(
                            ric=instrument_ric,
                            date=date,
                            event_type=event_type,
                        )
                    )

                data = self._get_data_from_api_response(response=response)

                if data.empty:
                    continue

                results.append(data)

            except MarketDataStoreError as e:
                logger.error(
                    f"Cannot get ticks for instrument "
                    f"{instrument_unique_identifier}: {str(e.exception)}"
                )
                continue

            except ConnectionError as error:
                logger.warning(
                    msg=f"Connection error when trying to fetch ticks "
                    f"for instrument id: {instrument_unique_identifier}. "
                    f"Error: {str(error)}",
                    exc_info=True,
                )
                return pd.DataFrame()

        if not results:
            return pd.DataFrame()

        result: pd.DataFrame = pd.concat(results, ignore_index=True)

        return result

    def get_market_data_volatility(
        self,
        volatility_col_name: str,
        min_periods: int,
        volatility_window: int,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        currency: Optional[str] = None,
    ) -> pd.DataFrame:
        """Uses instrument unique identifier as basis to fetch market data
        stats and calculate volatility.

        :param volatility_col_name: string with name of the column of the volatility
        :param volatility_window: number of days to be used on window to calculate volatility
        :param min_periods: minimum number of days to calculate volatility
        :param instrument_unique_identifier: string with instrument unique identifier
        :param instrument_ric: string with instrument ric
        :param currency: optional field to fetch market data with a specific currency
        :return: dataframe with market data stats and volatility
        """

        data = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
            currency=currency,
        )

        if data.empty:
            return data

        data[volatility_col_name] = calculate_volatility(
            market_data=data,
            data_column=RefinitivExtractColumns.CLOSE_PRICE,
            volatility_window=volatility_window,
            min_periods=min_periods,
        )

        return data

    def get_market_data_adv(
        self,
        adv_col_name: str,
        window: int,
        min_periods: int = 2,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ):
        data = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
        )

        if data.empty:
            return data

        data[adv_col_name] = calculate_exponential_moving_average(
            market_data=data,
            data_column=TradeStatsColumns.TRADE_VOLUME,
            alpha=0.7,
            window=window,
            min_periods=min_periods,
        )

        return data

    def get_avg_daily_trading_volume(
        self,
        look_back_period: Union[int, str],
        adtv_column: str,
        time_series_col: str,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ) -> pd.DataFrame:
        """
        Calculate average daily traded volume for a certain instrument
        NOTE: this is only used for SLOV v2 & price ramping v2
        :param instrument_unique_identifier: str. instrument id
        :param look_back_period: Int or Str. Look back period
        :param adtv_column: str. Name of ADTV column to populate
        :param time_series_col: str. Datetime column name to set as index.
        :param instrument_ric: string with instrument ric
        :param instrument_unique_identifier: str. instrument id
        :return: pd.DataFrame. DataFrame with ADTV calculated
        """

        data: pd.DataFrame = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
        )

        if data.empty:
            return data

        data[StatsColumns.DATE] = pd.to_datetime(data[StatsColumns.DATE])

        data: pd.DataFrame = data.loc[data[StatsColumns.DATE].dt.dayofweek < 5]  # type: ignore[no-redef]

        avg_col = self.calculate_average_daily_trading_volume(
            market_data=data,
            data_column=TradeStatsColumns.TRADE_VOLUME,
            look_back_period=look_back_period,
            time_series=time_series_col,
        )

        if avg_col.empty:
            return pd.DataFrame()

        data.loc[:, adtv_column] = avg_col.reset_index()[
            TradeStatsColumns.AVERAGE_DAILY_TRADED_VOLUME
        ]
        return data

    @staticmethod
    def calculate_average_daily_trading_volume(
        market_data: pd.DataFrame,
        data_column: RefinitivExtractColumns,
        look_back_period: Union[int, str],
        min_periods: Optional[int] = 1,
        time_series: str = "",
        decimal_precision: int = 8,
        adtv_column: str = TradeStatsColumns.AVERAGE_DAILY_TRADED_VOLUME,
    ) -> pd.Series:
        """Method to calculate the average daily trading volume. Retrieve the
        Daily Trading Volume for each business day in
        (EventDate-1)-look_back_period, divided by number of business days
        used.

        https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/876871681/Algo+Abstractions#a.%5BADTV%5D
        :param market_data: Pandas DataFrame. Market data to calculate the ADV
        :param data_column: RefinitivExtractColumns. Needs to be a column defined on the scope
        :param look_back_period: Int or Str. Look back period
        :param min_periods: Int. Minimum Observations to calculate the ADTV
        :param decimal_precision: int. Decimal precision of volatility. Default: 8
        :param time_series: str. Datetime column name to set as index. Default is None
        :param adtv_column: str

        :return: Pandas Series. A new column with the ADTV calculated
        """
        data: pd.Series = get_data_based_on_window(
            window=look_back_period,
            data=market_data,
            data_column=data_column,
            time_series_column=time_series,
        )

        window = get_integer_from_time_window(look_back_period)

        data_offset = data.shift(1)

        adtv: pd.Series[float] = (
            data_offset.rolling(window=window, min_periods=min_periods)
            .apply(lambda x: x.dropna().sum() / x.dropna().shape[0])
            .round(decimals=decimal_precision)
        )

        # Set series name
        adtv.name = adtv_column

        if adtv.isnull().all():
            return pd.Series()

        return adtv

    def get_nearest_tick_data(
        self,
        timestamps: pd.Series,
        event_type: RefinitivEventType = RefinitivEventType.TRADE,
        single_tick: bool = False,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ) -> Any:
        """Get the nearest tick data for each timestamp in `timestamps` (in
        UTC)

        :param instrument_unique_identifier:
        :param instrument_ric:
        :param timestamps:
        :param event_type:
        :param single_tick: (bool) If True, selects the first tick fetched only
        :return:
        """

        results = []

        for timestamp in timestamps:
            data = self._fetch_nearest_tick_data(
                instrument_unique_identifier=instrument_unique_identifier,
                instrument_ric=instrument_ric,
                timestamp=timestamp,
                event_type=event_type,
            )

            if data.empty:
                continue

            if single_tick:
                # FR Note: used .iloc[0:1] to keep it as a dataframe instead of a series
                data = data.iloc[0:1]

            data[timestamps.name] = timestamp

            results.append(data)

        if results:
            return pd.concat(results, ignore_index=True)

        return pd.DataFrame()

    def _fetch_nearest_tick_data(
        self,
        timestamp: datetime.datetime,
        event_type: RefinitivEventType,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ) -> Any:
        try:
            response: APIResponse = (
                self._market_client.nearest_tick_from_timestamp_for_instrument_id(
                    instrument_id=instrument_unique_identifier,
                    ric=instrument_ric,
                    timestamp=timestamp,
                    event_type=event_type,
                )
            )

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to get RIC for instrument {instrument_unique_identifier}. "
                f"Error: {str(e.status)}."
            )
            return pd.DataFrame()

        try:
            data = self._get_data_from_api_response(response=response)
            return data

        except MarketDataStoreError as e:
            logger.error(f"Cannot fetch nearest tick data:{str(e.exception)}.")
            return pd.DataFrame()

        except ConnectionError as error:
            logger.warning(
                msg=f"Connection error when trying to fetch nearest ticket "
                f"data for id: {instrument_unique_identifier}. "
                f"Error: {str(error)}",
                exc_info=True,
            )
            return pd.DataFrame()

    @staticmethod
    def _get_data_from_api_response(response: APIResponse) -> Any:
        data = response.data if response.status == APIResponseStatus.SUCCESS else pd.DataFrame()

        if data.empty:
            if response.status == APIResponseStatus.UNKNOWN_ERROR:
                raise Exception(
                    f"Unknown error from Market Data API response: {str(response.exception)}"
                )

        return data

    def get_resampled_ticks(
        self,
        instrument_unique_identifier: str,
        from_datetime: datetime.datetime,
        to_datetime: datetime.datetime,
        event_type: RefinitivEventType,
        resampling_nanosecond_interval: int,
    ) -> Any:
        """Create resampled dataframe of tick data using the input
        `resampling_nanosecond_interval` to decide what intervals, with extra
        resampled columns of `TOTAL_VOLUME` & `AVG_PRICE` for trades and for
        quotes `AVG_BID` & `AVG_ASK`

        *** NOTE ***
        - resampling_nanosecond_interval is in nano seconds, to get a
        resampled dataframe of seconds please pass
        `int(1e9)` and for micro seconds `int(1e6)` and for minutes `int(60 * 1e9)`
        -  Will choose the last timestamp row in range specified, e.g.
        (12:34:55.122, 12:34:56.123) is considered for
        timestamp label 12:34:56.123
        - Do *not* send timezone aware datetime variables. Pandas `date_range`
        feature cannot use them
        :param instrument_unique_identifier:
        :param from_datetime:
        :param to_datetime:
        :param event_type:
        :param resampling_nanosecond_interval:
        :return:
        """
        try:
            response = self._market_client.resampled_ticks_for_instrument_id(
                instrument_unique_identifier=instrument_unique_identifier,
                from_timestamp=from_datetime,
                to_timestamp=to_datetime,
                event_type=event_type,
                resampling_nanosecond_interval=resampling_nanosecond_interval,
            )

            data = self._get_data_from_api_response(response=response)

            return data

        except MarketDataStoreError as e:
            logger.error(
                f"Cannot get resampled ticks for instrument "
                f"id {instrument_unique_identifier}: {str(e.exception)}"
            )
            return pd.DataFrame()

        except ConnectionError as error:
            logger.warning(
                msg=f"Connection error when trying to fetch "
                f"nearest ticket data for id: {instrument_unique_identifier}."
                f"Error: {str(error)}",
                exc_info=True,
            )
            return pd.DataFrame()

    @staticmethod
    def _process_market_data_stats(
        df: pd.DataFrame, currency: Optional[str] = None
    ) -> pd.DataFrame:
        """Process market data stats.

        :param df: dataframe with raw market data stats
        :param currency: optional selection of currency
        :return: dataframe with processed market data stats
        """
        if EoDStatsColumns.DATE not in df.columns:
            return pd.DataFrame()

        df.loc[:, EoDStatsColumns.DATE] = pd.to_datetime(df[EoDStatsColumns.DATE].fillna(pd.NaT))

        if EoDStatsColumns.CURRENCY not in df.columns:
            return pd.DataFrame()

        # to return normal dataframe when currency is not provided
        if not currency:
            return df

        if df.loc[:, EoDStatsColumns.CURRENCY].isna().any():
            # fills the NaNs with currency with the known RIC currency
            if df.loc[:, EoDStatsColumns.CURRENCY].nunique() == 1:
                df.loc[:, EoDStatsColumns.CURRENCY] = (
                    df.loc[:, EoDStatsColumns.CURRENCY].dropna().unique()[0]
                )

            # if there is more than one currency it replaces every
            # currency for the latest one (Checked with Storey)
            elif df.loc[:, EoDStatsColumns.CURRENCY].nunique() >= 1:
                df.loc[:, EoDStatsColumns.CURRENCY] = df.iloc[
                    -1, df.columns.get_loc(EoDStatsColumns.CURRENCY)  # type: ignore
                ]
        df = df.loc[
            (df[EoDStatsColumns.CURRENCY] == currency) | (df[EoDStatsColumns.CURRENCY].isna())
        ]

        return df

    def get_trade_tick_data(
        self,
        instrument_unique_identifier: str,
        from_date: datetime.datetime,
        to_date: datetime.datetime,
    ) -> Any:
        """Fetches the tick data between the timestamps required.

        :param instrument_unique_identifier: Instrument unique identifier.
        :param from_date: datetime. The timestamp to fetch ticks from.
        :param to_date: datetime. The timestamp to fetch ticks until.
        :return: pd.DataFrame. Tick Data
        """
        response = self._market_client.ticks_between_timestamps_for_instrument_id(
            instrument_id=instrument_unique_identifier,
            from_timestamp=from_date,
            to_timestamp=to_date,
            event_type=RefinitivEventType.TRADE,
        )

        return self._get_data_from_api_response(response=response)
