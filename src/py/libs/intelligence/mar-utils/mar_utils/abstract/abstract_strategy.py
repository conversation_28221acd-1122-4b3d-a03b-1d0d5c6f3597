import logging
from abc import ABC, abstractmethod
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskResult
from se_elastic_schema.components.mar.strategy.base import BaseStrategyThresholds
from typing import Any, Generic, Type, TypeVar

T = TypeVar("T", bound=BaseStrategyThresholds)

log = logging.getLogger(__name__)


class AbstractStrategy(Generic[T], ABC):
    """Abstract class for all the Market Abuse Algorithms strategies."""

    def __init__(
        self,
        cloud_provider: str,
        tenant: str,
        stack: str,
        strategy_name: str,
        thresholds_class: Type[T],
        thresholds: dict[str, Any],
        filters: dict[str, Any] | None = None,
    ):
        self._cloud_provider = cloud_provider
        self._tenant = tenant
        self._stack = stack
        self._strategy_name = strategy_name
        self._thresholds_class = thresholds_class
        self._filters = filters
        self._thresholds = self._evaluate_thresholds(thresholds_dict=thresholds)

        self._scenarios: list = []

    @property
    def thresholds(self) -> T:
        return self._thresholds

    @property
    def strategy_name(self) -> str:
        """Current applied stragey name."""
        return self._strategy_name

    @property
    def scenarios(self) -> list[Any]:
        """Returns records for which the alerts will be generated."""
        return self._scenarios

    @abstractmethod
    def _apply_strategy(self):
        return NotImplementedError()

    def run(self):
        """Invokes the apply strategy method for now and returns the possible
        scenarios."""
        exception = None
        try:
            self.apply_strategy()
            return self.scenarios
        except Exception as e:
            exception = e
        finally:
            if exception:
                raise exception

    def _evaluate_thresholds(self, thresholds_dict: dict[str, Any]) -> T:
        """Validates the thresholds with the base thresholds class and raises
        exception if required."""
        log.info(f"Evaluating Thresholds: {thresholds_dict}")
        log.info(f"Thresholds type: {type(thresholds_dict)}")

        if type(thresholds_dict) is not dict:  # noqa: E721
            raise ValueError(f"Unknown threshold type: {type(thresholds_dict)}")

        unknown_thresholds = set(thresholds_dict.keys()) - set(
            self._thresholds_class.schema()["properties"].keys()
        )

        if unknown_thresholds:
            raise ValueError(f"Unknown threshold(s): {unknown_thresholds}")

        thresholds: T = self._thresholds_class.validate(thresholds_dict)
        log.info("Thresholds evaluated successfully")
        return thresholds

    def apply_strategy(self) -> None:
        """Invokes the abstract stragey method.

        In future as we go it will have more of mar audits stuff
        """
        log.info(f"Applying {self.strategy_name} Strategy")
        self._apply_strategy()

    def generate_no_alert_aries_task_result(self) -> AriesTaskResult:
        """Generates the AriesTaskResult when there's no alerts to be written
        into ES."""
        return AriesTaskResult(
            output_param=IOParamFieldSet(
                params=dict(
                    file_uri=None,
                    streamed=False,
                    ignore_empty_file_uri=True,
                    cloud_provider=self._cloud_provider,
                )
            )
        )
