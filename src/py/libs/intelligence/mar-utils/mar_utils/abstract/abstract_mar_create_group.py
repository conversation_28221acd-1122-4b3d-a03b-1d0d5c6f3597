import fsspec
import logging
import polars as pl
from abc import ABC, abstractmethod
from aries_task_link.models import AriesTaskInput
from dataclasses import dataclass
from datetime import datetime
from mar_utils.auditor.generic_mar_audit import GenericMarAudit
from mar_utils.auditor.mar_auditor import MA<PERSON><PERSON>tor
from mar_utils.create_group_utils.static import (
    GROUP_DF_INDEX_COL,
    MAX_ROWS_BY_PARTITION,
    MarWatchExecutionMetadata,
)
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.repository.sdp.es_sdp import (  # type: ignore[attr-defined]
    SDP,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery  # type: ignore[attr-defined]
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import Optional, Type

log = logging.getLogger(__name__)


@dataclass
class MarCreateGroupOutput:
    group_file_uri: str | None
    total_groups: int | None


class AbstractMarCreateGroup(ABC):
    def __init__(
        self,
        aries_task_input: AriesTaskInput,
        event: dict[str, str],
        market_abuse_report_type: MarketAbuseReportType,
        watch: SurveillanceWatch,
        thresholds: dict[str, str],
        filters: dict,
        lake_prefix: str,
        look_back_period_ts: str,
        watch_execution_metadata_path: str,
        thresholds_cls,
        execution_start: datetime,
        queries_cls: Optional[Type[BaseQuery]] = None,
        mar_auditor_cls: Type[GenericMarAudit] | None = None,
        **kwargs,
    ):
        self.market_abuse_report_type = market_abuse_report_type.value
        self._event = event
        self._thresholds = thresholds_cls.validate(thresholds)
        self._filters = filters
        self._watch = watch
        self._lake_prefix = lake_prefix
        self.look_back_period_ts = look_back_period_ts
        self._watch_execution_metadata_path = watch_execution_metadata_path
        self._context = self.get_context()
        self.context.thresholds = self._thresholds
        self.execution_start = execution_start
        self._es_client = SDP(tenant=self._context.tenant)
        self._auditor = self._get_mar_auditor(
            mar_auditor_cls=mar_auditor_cls,
            lake_prefix=lake_prefix,
            aries_task_input=aries_task_input,
        )
        self._audit = Audit(
            context=self._context,
            strategy_name=self.market_abuse_report_type,
            es_client=self._es_client,
            report_type=market_abuse_report_type,
        )
        self._queries = (
            queries_cls(context=self._context, audit=self._audit)
            if queries_cls is not None
            else None
        )
        # Since thresholds is converted to a dict when context is instantiated
        # we'll overwrite it to be the validated pydantic obj

    @staticmethod
    def _get_mar_auditor(
        mar_auditor_cls: Type[GenericMarAudit] | None,
        lake_prefix: str,
        aries_task_input: AriesTaskInput,
    ) -> MARAuditor | None:
        if not mar_auditor_cls:
            log.warning("MAR Auditor class not provided. Audits will not be written.")
            return None
        else:
            return MARAuditor(
                identifier="create_group",
                lake_prefix=lake_prefix,
                aries_task_input=aries_task_input,
                mar_auditor_cls=mar_auditor_cls,
            )

    def get_groups(self, market_abuse_groups_base_path: str, **kwargs) -> MarCreateGroupOutput:
        groups = self._get_groups(
            market_abuse_groups_base_path=market_abuse_groups_base_path, **kwargs
        )
        if self.auditor:
            self.auditor.write_market_abuse_audit(
                context=self.context,
                surv_watch=self.watch,
                start=self.execution_start,
            )
            self.auditor.write_audit_to_cloud()
        return groups

    @abstractmethod
    def _get_groups(self, market_abuse_groups_base_path: str, **kwargs) -> MarCreateGroupOutput:
        raise NotImplementedError

    @property
    def event(self):
        return self._event

    @property
    def thresholds(self):
        return self._thresholds

    @property
    def filters(self):
        return self._filters

    @property
    def watch(self):
        return self._watch

    @property
    def lake_prefix(self):
        return self._lake_prefix

    @property
    def watch_execution_metadata_path(self):
        return self._watch_execution_metadata_path

    @property
    def queries(self):
        return self._queries

    @property
    def context(self):
        return self._context

    @property
    def audit(self):
        return self._audit

    @property
    def auditor(self):
        return self._auditor

    @staticmethod
    def _write_groups(market_abuse_groups_base_path: str, group_df: pl.DataFrame):
        group_file_uri = f"{market_abuse_groups_base_path}groups.parquet"
        fs, _, _ = fsspec.get_fs_token_paths(group_file_uri)
        with fs.open(group_file_uri, mode="wb") as output_group:
            group_df.with_row_index(GROUP_DF_INDEX_COL).write_parquet(
                output_group, row_group_size=MAX_ROWS_BY_PARTITION
            )
        log.info(f"Groups written to {group_file_uri}")
        return group_file_uri

    def write_watch_execution_metadata(self):
        watch_execution_metadata = MarWatchExecutionMetadata(context=self.context)
        with fsspec.open(self.watch_execution_metadata_path, "w") as f:
            f.write(watch_execution_metadata.json())
            log.info(f"Watch execution metadata written to {self.watch_execution_metadata_path}")

    def get_context(self):
        return StrategyContext(
            realm=self.event.get("realm"),
            tenant=self.event.get("tenant"),
            stack=self.event.get("stack"),
            thresholds=self.thresholds,
            filters=self.filters,
            requested_by="MAR Create Group",
            created_by=self.watch.createdBy,
            watch_id=self.event.get("watch_id"),
            watch_execution_id=self.event.get("watch_execution_id"),
            watch_execution_type=self.event.get("watch_execution_type"),
            watch_name=self.watch.name,
            look_back_period_ts=self.look_back_period_ts,
            lake_prefix=self.lake_prefix,
        )
