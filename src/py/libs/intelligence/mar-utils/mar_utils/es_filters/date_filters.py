import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from mar_utils.es_filters.static import (
    DATE_FORMAT,
    DateRangeParameters,
    LookBackPeriodEnum,
    ThresholdsEnum,
)
from mar_utils.es_filters.utils import convert_epoch_to_string, get_timestamp_for_full_backtest
from pandas.tseries.offsets import BDay
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import (
    MarketAbuseReportType,
    WatchBacktestPeriodType,
    WatchBacktestType,
)
from typing import Optional

logger = logging.getLogger(__name__)


def calculate_backtest_last_execution(
    surveillance_watch: SurveillanceWatch, start: datetime, oldest_timestamp_on_tenant: str
):
    """Calculate the last execution for scheduled backtest.

    :param oldest_timestamp_on_tenant: str with the date of the oldest order for the tenant
    :param surveillance_watch: Surveillance watch
    :param start: datetime with start of execution date
    :return:
    """
    backtest_type = surveillance_watch.backtest

    if backtest_type == WatchBacktestType.FULL.value:
        return oldest_timestamp_on_tenant

    if backtest_type == WatchBacktestType.PARTIAL.value:
        backtest_period_type = (
            surveillance_watch.backtestPeriod or WatchBacktestPeriodType.LAST_WEEK.value
        )
        offset = 7

        if backtest_period_type == WatchBacktestPeriodType.LAST_MONTH.value:
            offset = 30
        elif backtest_period_type == WatchBacktestPeriodType.LAST_THREE_MONTHS.value:
            offset = 90

        return (start - timedelta(days=offset)).date().strftime(DATE_FORMAT)

    return (
        start.strftime(DATE_FORMAT)
        if not surveillance_watch.createdOn
        else surveillance_watch.createdOn.strftime(DATE_FORMAT)
    )


def get_last_run_daily_watch(event_last_run: str, look_back_period: int) -> str:
    """get the last execution when it's a daily watch.

    :param event_last_run: str with the last time it ran
    :param look_back_period: int number of days to lookback
    :return:
    """
    start_datetime = convert_epoch_to_string(epoch_to_convert=event_last_run)

    return (pd.Timestamp(start_datetime) - BDay(look_back_period)).isoformat()


def get_timestamp_dict(
    oldest_timestamp_on_tenant: str, surveillance_watch: SurveillanceWatch, start: datetime
) -> dict:
    """Determines the lookback period ts and the event day if it is insider
    trading v3 running For algos that required lookback it applies 10 days
    except for insider trading v3 that is dependent on thresholds.

    :param oldest_timestamp_on_tenant: str with timestamp from oldest record on tenant
    :param surveillance_watch: dict w watch
    :param start: current time
    :return: str with the last execution to be used, meaning to fetch data from
    """
    if (
        surveillance_watch.query.marketAbuseReportType
        != MarketAbuseReportType.RESTRICTED_LIST_V2.value
    ):
        try:
            thresholds = json.loads(surveillance_watch.query.thresholds.__str__())
        except Exception:
            raise Exception(
                f"Failed to get the thresholds for "
                f"{surveillance_watch.query.marketAbuseReportType} algorithm. "
                f"For watch {surveillance_watch.id__}"
            )
    else:
        thresholds = {}

    event_day_starting_ts = None

    last_successful_execution_dt: datetime = (
        surveillance_watch.executionDetails.lastSuccessfulExecution
    )

    if not last_successful_execution_dt:
        look_back_period_ts, event_day_starting_ts = run_first_execution(
            watch=surveillance_watch,
            start=start,
            oldest_date=oldest_timestamp_on_tenant,
            thresholds=thresholds,
        )

    else:
        last_successful_execution: str = str(int(last_successful_execution_dt.timestamp() * 1000))

        last_successful_execution_datetime = convert_epoch_to_string(
            epoch_to_convert=last_successful_execution
        )

        look_back_period_ts = last_successful_execution_datetime

        if look_back_period_ts is None:
            raise ValueError("Last Successful execution should be populated")

        look_back_period = None

        if surveillance_watch.query.marketAbuseReportType in [
            MarketAbuseReportType.INSIDER_TRADING,
            MarketAbuseReportType.INSIDER_TRADING_V2,
            MarketAbuseReportType.INSIDER_TRADING_NEWS,
            MarketAbuseReportType.INSIDER_TRADING_NEWS_REFINITIV,
        ]:
            look_back_period_ts = get_last_run_daily_watch(
                event_last_run=last_successful_execution_datetime, look_back_period=10
            )

        if surveillance_watch.query.marketAbuseReportType == MarketAbuseReportType.POTAM:
            look_back_period = thresholds.get(LookBackPeriodEnum.POTAM)

        if surveillance_watch.query.marketAbuseReportType == MarketAbuseReportType.PARKING:
            look_back_period = thresholds.get(LookBackPeriodEnum.PARKING)

        if surveillance_watch.query.marketAbuseReportType in [
            MarketAbuseReportType.INSIDER_TRADING_V3,
            MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV,
        ]:
            observation_period_days: int = thresholds.get(
                ThresholdsEnum.ACTIVITY_OBSERVATION_PERIOD.value
            )

            behaviour_period_days: int = thresholds.get(
                ThresholdsEnum.ACTIVITY_BEHAVIOUR_PERIOD.value
            )

            event_day_starting_ts = pd.Timestamp(look_back_period_ts)

            if observation_period_days is None or behaviour_period_days is None:
                raise ValueError("Observation period and/or behaviour periods are not populated")

            look_back_period = observation_period_days * (behaviour_period_days + 1)

        if look_back_period:
            event_last_run = (
                last_successful_execution
                if last_successful_execution is not None
                else last_successful_execution_datetime
            )

            look_back_period_ts = get_last_run_daily_watch(
                event_last_run=event_last_run,
                look_back_period=look_back_period,
            )

    res_dict = {
        "lookBackPeriodTS": look_back_period_ts,
        "eventDayStartingTS": event_day_starting_ts,
    }

    return res_dict


def run_first_execution(
    watch: SurveillanceWatch, start: datetime, oldest_date: str, thresholds: dict
) -> tuple[str, Optional[pd.Timestamp]]:
    """calculate the look back period for the first time the surveillance watch
    is executed.

    :param watch: Surveillance watch
    :param oldest_date: str with timestamp from oldest record on tenant
    :param start: current time
    :param thresholds: dictionary with the thresholds from the watch
    :return: str with the look back period and a optional timestamp if the surveillance
     watch is a ITV3 full back test
    """
    look_back_period_ts: str = calculate_backtest_last_execution(
        surveillance_watch=watch,
        start=start,
        oldest_timestamp_on_tenant=oldest_date,
    )

    event_day_starting_ts = None

    if watch.query.marketAbuseReportType in [
        MarketAbuseReportType.INSIDER_TRADING_V3,
        MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV,
    ]:
        event_day_starting_ts = determine_event_day_starting_ts_itv3_full_backtest(
            look_back_period_ts=look_back_period_ts, thresholds=thresholds
        )

    return look_back_period_ts, event_day_starting_ts


def determine_event_day_starting_ts_itv3_full_backtest(
    look_back_period_ts: str, thresholds: dict
) -> pd.Timestamp:
    """determines the starting timestamp from where insider trading v3 can
    start to run.

    :param look_back_period_ts: timestamp of the last run
    :param thresholds: algorithm thresholds set by the used for the watch
    :return: timestamp to starting date range of the algo
    """
    observation_period_days: int | None = thresholds.get(
        ThresholdsEnum.ACTIVITY_OBSERVATION_PERIOD.value
    )
    behaviour_period: int | None = thresholds.get(ThresholdsEnum.ACTIVITY_BEHAVIOUR_PERIOD.value)
    if not observation_period_days:
        raise ValueError("Observation period is not populated")

    # setting the start ts to determine the event days after the required date range for observation
    # and behaviour periods
    if not behaviour_period:
        event_day_starting_ts = pd.Timestamp(look_back_period_ts) + BDay(
            observation_period_days - 1
        )
    else:
        event_day_starting_ts = pd.Timestamp(look_back_period_ts) + BDay(
            observation_period_days * (behaviour_period + 1) - 1
        )
    return event_day_starting_ts


def determine_events_days_list(
    event_day_starting_ts: pd.Timestamp,
    end_date: pd.Timestamp,
    date_range_filters: Optional[dict] = None,
) -> list[datetime]:
    """determines the list of event days for the model to run using a starting
    date and the end date.

    :param event_day_starting_ts: start date for the events days
    :param end_date: end date for the event dates
    :param date_range_filters: date range filters to be used on elastic search
    :return:
    """
    # ENG-4022 Remove the 30day restriction if date range filter are sent

    if date_range_filters:
        datetimes_event_days = (
            pd.bdate_range(
                start=date_range_filters.get(DateRangeParameters.START.value).tz_localize(None),
                end=date_range_filters.get(DateRangeParameters.END.value).tz_localize(None),
            )
            .to_pydatetime()
            .tolist()
        )
        return [pd.Timestamp(event_day) for event_day in datetimes_event_days]

    if event_day_starting_ts <= end_date:
        fallback_timedelta: pd.Timedelta = end_date - event_day_starting_ts

        if fallback_timedelta > pd.Timedelta(30, unit="day"):
            event_day_starting_ts = end_date - pd.Timedelta(30, unit="day")

        datetimes_event_days = (
            pd.bdate_range(start=event_day_starting_ts, end=end_date).to_pydatetime().tolist()
        )
        return [pd.Timestamp(event_day) for event_day in datetimes_event_days]

    datetimes_event_days = (
        pd.bdate_range(start=datetime.utcnow().date() - BDay(1), end=end_date)
        .to_pydatetime()
        .tolist()
    )

    return [pd.Timestamp(event_day) for event_day in datetimes_event_days]


def get_lookback_and_event_day(
    tenant: str,
    thresholds: str,
    surv_watch: SurveillanceWatch,
    execution_start: datetime,
    record_handler,
):
    oldest_timestamp = get_timestamp_for_full_backtest(record_handler=record_handler, tenant=tenant)
    logger.info(f"Thresholds to be applied to this execution {thresholds}.")

    if not oldest_timestamp:
        return None, None, None
    try:
        ts_dict: dict = get_timestamp_dict(
            oldest_timestamp_on_tenant=oldest_timestamp.strftime(DATE_FORMAT),
            surveillance_watch=surv_watch,
            start=execution_start,
        )

        look_back_period_ts = ts_dict["lookBackPeriodTS"]
        event_day_starting_ts = ts_dict["eventDayStartingTS"]

    except Exception as e:
        raise Exception(
            "Error while determining look"
            f" back period timestamp and/or starting event day timestamp. Error {e}"
        )
    return oldest_timestamp, look_back_period_ts, event_day_starting_ts
