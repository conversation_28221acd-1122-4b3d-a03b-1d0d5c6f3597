from se_elastic_schema.static.surveillance import MarketAbuseReportType


class IOParamNames:
    Instrument = "instrument"
    InstrumentColumnName = "instrument_column_name"
    ParquetFileUrls = "parquet_file_urls"
    RIC = "RIC"
    SelerityEntityId = "selerity_entity_id"


COPILOT_SUPPORTED_ALGOS = [
    MarketAbuseReportType.FRONT_RUNNING_V2,
    MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV,
    MarketAbuseReportType.LAYERING_V2,
    MarketAbuseReportType.PAINTING_THE_TAPE_V2,
    MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2,
    MarketAbuseReportType.WASH_TRADING,
]
