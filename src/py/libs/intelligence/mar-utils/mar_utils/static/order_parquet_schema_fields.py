class OrderParquetSchemaFields:
    ClientIdentifiersClientName = "clientIdentifiers.client.name"
    CounterpartyName = "counterparty.name"
    CounterpartyFileIdentifier = "counterpartyFileIdentifier"
    Date = "date"
    ExecutionDetailsBuySellIndicator = "executionDetails.buySellIndicator"
    ExecutionDetailsOrderStatus = "executionDetails.orderStatus"
    ExecutionDetailsOrderType = "executionDetails.orderType"
    InstrumentDetailsDerivativeUnderlyingInstruments = (
        "instrumentDetails.instrument.derivative.underlyingInstruments"
    )
    InstrumentDetailsExtUniqueIdentifier = (
        "instrumentDetails.instrument.ext.instrumentUniqueIdentifier"
    )
    InstrumentDetailsInstrumentIdCode = "instrumentDetails.instrument.instrumentIdCode"
    InstrumentDetailsInstrumentFullName = "instrumentDetails.instrument.instrumentFullName"
    InstrumentDetailsExtAlternativeIdentifier = (
        "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier"
    )
    InstrumentDetailsExtUnderlyingInstrumentsInstrumentIdCode = (
        "instrumentDetails.instrument.ext.underlyingInstruments.instrumentIdCode"
    )
    InstrumentDetailsFullName = "instrumentDetails.instrument.instrumentFullName"
    OrderIdentifiersOrderIdCode = "orderIdentifiers.orderIdCode"
    PriceFormingDataTradedQuantity = "priceFormingData.tradedQuantity"
    PriceFormingDataPrice = "priceFormingData.price"
    ReportDetailsExecutingEntityName = "reportDetails.executingEntity.name"
    TraderAlgosWaiversIndicatorsInvestmentDecisionWithinFirmStructureDesksName = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name"
    )

    TraderAlgosWaiversIndicatorsInvestmentDecisionWithinFirmName = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name"
    )
    TimestampsTradingDateTime = "timestamps.tradingDateTime"
    TimestampsOrderSubmitted = "timestamps.orderSubmitted"
    TraderFileIdentifier = "traderFileIdentifier"
