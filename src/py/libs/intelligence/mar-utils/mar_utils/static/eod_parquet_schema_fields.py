from se_enums.core import BaseStrEnum


class EoDStatsColumns(BaseStrEnum):
    CloseAskPrice = "Close Ask Price"
    CloseBidPrice = "Close Bid Price"
    ClosePrice = "Close Price"
    Currency = "Currency"
    Date = "Date"
    ExchangeCode = "Exchange Code"
    HighAskPrice = "High Ask Price"
    HighBidPrice = "High Bid Price"
    HighPrice = "High Price"
    MarketVWAO = "Market VWAP"
    VWAP = "VWAP"
    LowPrice = "Low Price"
    LowAskPrice = "Low Ask Price"
    LowBidPrice = "Low Bid Price"
    OpenAskPrice = "Open Ask Price"
    OpenBidPrice = "Open Bid Price"
    OpenInterest = "Open Interest"
    OpenPrice = "Open Price"
    RIC = "#RIC"
    TradeVolume = "Trade Volume"
