import datetime
import fsspec
import importlib
import logging
import orj<PERSON>
import uuid
from mar_utils.create_group_utils.static import MarWatchExecutionMetadata
from market_abuse_algorithms.mar_audit.mar_audit import (  # type: ignore[attr-defined]
    DATETIME_FORMAT,
)

logger_ = logging.getLogger(__name__)


def get_watch_context_from_file(file_path: str):
    with fsspec.open(file_path, "r") as file_:
        watch_context = orjson.loads(file_.read())
    return MarWatchExecutionMetadata(**watch_context).context


def get_start_time_and_unique_id():
    return datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT), str(uuid.uuid4())


def get_apply_strategy_cls(market_abuse_report_type: str):
    apply_strategy_module_path = f"{market_abuse_report_type.lower()}_apply_strategy.apply_strategy"  # noqa: E501
    return getattr(importlib.import_module(apply_strategy_module_path), "ApplyStrategy")
