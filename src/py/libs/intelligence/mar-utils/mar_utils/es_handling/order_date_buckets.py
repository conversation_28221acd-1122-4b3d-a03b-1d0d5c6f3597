import logging
from se_elastic_schema.models import Order
from se_es_utils.slim_record_handler import <PERSON>RecordHandler
from surveillance_utils.schema import DateTimeRange
from typing import List

log = logging.getLogger(__name__)


class OrderDateBuckets:
    def execute(
        self,
        date_range: DateTimeRange,
        tenant: str,
        record_handler: <PERSON>RecordHand<PERSON>,
        group_filter: List,
        filters: dict,
        **kwargs,
    ) -> List[str]:
        """Queries tenant order index to get unique list of dates where order
        exists based off of filters from watch and date_range.

        :param watch:
        :param date_range:
        :param tenant:

        :return: List of dates
        """
        query = self._elastic_date_buckets_query(record_handler=record_handler, filters=filters)

        if group_filter:
            try:
                query["query"]["bool"]["filter"].extend(group_filter)
            except Exception:
                query["query"]["bool"]["filter"] = group_filter

        log.info(query)
        try:
            result = record_handler.search_records_by_query(model=Order, query=query, tenant=tenant)
        except Exception as e:
            raise Exception(f"Error querying for date buckets {e}")

        dates = [x.get("key_as_string") for x in result["aggregations"]["orders_by_day"]["buckets"]]

        if not dates:
            log.warning(f"Found no Orders for {date_range}")

        return dates

    @staticmethod
    def _elastic_date_buckets_query(record_handler: SlimRecordHandler, filters: dict) -> dict:
        """Build query based off of watch record. If the watch is a
        backtestReplaces any range filter in the watch with range filter built
        off of upstream task `DetermineDateRange`

        :param watch_record:
        :param date_range:

        :return: dict
        """

        body = {
            "size": 0,
            "query": filters,
            "aggs": {"orders_by_day": {"terms": {"field": "date", "size": 10000}}},
        }

        if body.get("query", {}).get("bool", {}).get("must_not", None):  # type: ignore[attr-defined]
            body["query"]["bool"]["must_not"].append(  # type: ignore[index]
                {"exists": {"field": record_handler.meta.expiry}}
            )
        else:
            body["query"]["bool"]["must_not"] = [{"exists": {"field": record_handler.meta.expiry}}]  # type: ignore[index]

        return body
