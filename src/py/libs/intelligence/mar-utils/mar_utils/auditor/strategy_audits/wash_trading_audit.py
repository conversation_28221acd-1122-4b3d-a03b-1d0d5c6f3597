from mar_utils.auditor.generic_mar_audit import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from mar_utils.auditor.mar_audit_models import <PERSON><PERSON>gg<PERSON><PERSON><PERSON><PERSON>, MarAudit
from se_elastic_schema.static.mar_audit import DropReason, StepType


class WashTradingAggregationAuditName:
    CHECK_BUY_SELL = "buy_sell_exists"
    FETCHING_RIC = "fetching_ric"
    CREATING_TIME_WINDOW_GROUP = "creating_time_window_group"
    FILTERING_BY_MAX_PRICE_DIFFERENCE = "filtering_by_max_price_difference"
    FETCHING_MARKET_DATA = "fetching_market_data"
    CHECKING_DAY_TRADED_NOTIONAL_COLUMN = "checking_day_traded_notional_column"
    EXTRACTING_DAY_TRADED_NOTIONAL_VALUE = "extracting_day_traded_notional_value"
    APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD = "applying_day_traded_notional_threshold"
    APPLYING_MINIMUM_NOTIONAL_THRESHOLD = "apply_minimum_notional_threshold"
    APPLY_MIN_TRADED_QUANTITY_THRESHOLD = "apply_min_traded_quantity_threshold"
    APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD = "apply_max_volume_difference_threshold"


class WashTradingAuditName:
    BUY_SELL_EXISTS = "buy_sell_exists"
    BUY_SELL_MISSING = "buy_sell_missing"
    MISSING_RIC = "missing_ric"
    FETCHED_RIC = "fetched_ric"
    EMPTY_TIME_WINDOW_GROUP = "empty_time_window_group"
    CREATE_TIME_WINDOW_GROUP = "created_time_window_group"
    EMPTY_MAX_PRICE_DIFFERENCE = "empty_max_price_difference"
    FILTERED_MAX_PRICE_DIFFERENCE = "filtered_max_price_difference"
    MISSING_MARKET_DATA = "missing_market_data"
    FETCHED_MARKET_DATA = "fetched_market_data"
    CHECKED_DAY_TRADED_NOTIONAL_COLUMN = "check_day_traded_notional_column"
    MISSING_DAY_TRADED_NOTIONAL_COLUMN = "missing_data_traded_notional_column"
    EXTRACTED_DAY_TRADED_NOTIONAL_VALUE = "extracted_day_traded_notional_value"
    MISSING_DAY_TRADED_NOTIONAL_VALUE = "missing_day_traded_notional_value"
    DROPPED_DAY_TRADED_NOTIONAL_THRESHOLD = "dropped_day_traded_notional_threshold"
    APPLIED_DAY_TRADED_NOTIONAL_THRESHOLD = "applied_day_traded_notional_threshold"
    DROPPED_MINIMUM_NOTIONAL_THRESHOLD = "dropped_minimum_notional_threshold"
    APPLIED_MINIMUM_NOTIONAL_THRESHOLD = "applied_minimum_notional_threshold"
    DROPPED_MINIMUM_TRADED_QUANTITY_THRESHOLD = "dropped_minimum_traded_quantity_threshold"
    APPLIED_MINIMUM_TRADED_QUANTITY_THRESHOLD = "applied_minimum_traded_quantity_threshold"
    DROPPED_MAX_VOLUME_DIFFERENCE_THRESHOLD = "dropped_max_volume_difference_threshold"
    APPLIED_MAX_VOLUME_DIFFERENCE_THRESHOLD = "applied_max_volume_difference_threshold"


class WashTradingAggregationAudit:
    """
    1. Ensure both buy/sell exists for instrument
    """

    agg_audits = {
        WashTradingAggregationAuditName.CHECK_BUY_SELL: MarAggregationAudit(
            step_number=1,
            step_name="Ensure both buy/sell exists for instrument",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.FETCHING_RIC: MarAggregationAudit(
            step_number=2,
            step_name="Fetching RIC",
            step_type=StepType.MARKET_DATA_RIC,
            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
        ),
        WashTradingAggregationAuditName.FETCHING_MARKET_DATA: MarAggregationAudit(
            step_number=3,
            step_name="Fetching market data for daily traded notional",
            step_type=StepType.MARKET_DATA_OBD,
            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
        ),
        WashTradingAggregationAuditName.CHECKING_DAY_TRADED_NOTIONAL_COLUMN: MarAggregationAudit(
            step_number=4,
            step_name="Checking day traded notional columns",
            step_type=StepType.MARKET_DATA_OBD,
            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
        ),
        WashTradingAggregationAuditName.EXTRACTING_DAY_TRADED_NOTIONAL_VALUE: MarAggregationAudit(
            step_number=5,
            step_name="Extracting day traded notional values",
            step_type=StepType.MARKET_DATA_OBD,
            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
        ),
        WashTradingAggregationAuditName.APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD: MarAggregationAudit(
            step_number=6,
            step_name="Applying day traded notional threshold",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.CREATING_TIME_WINDOW_GROUP: MarAggregationAudit(
            step_number=7,
            step_name="Creating time window group",
            step_type=StepType.GROUPING,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.FILTERING_BY_MAX_PRICE_DIFFERENCE: MarAggregationAudit(
            step_number=8,
            step_name="Filtering by max price difference",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.APPLYING_MINIMUM_NOTIONAL_THRESHOLD: MarAggregationAudit(
            step_number=9,
            step_name="Filtering by minimum notional threshold",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.APPLY_MIN_TRADED_QUANTITY_THRESHOLD: MarAggregationAudit(
            step_number=10,
            step_name="Filtering by minimum traded quantity threshold",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        WashTradingAggregationAuditName.APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD: MarAggregationAudit(
            step_number=11,
            step_name="Filtering by maximum volume difference threshold",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
    }


class WashTradingAudit(GenericMarAudit):
    agg_audits = WashTradingAggregationAudit.agg_audits
    audit_name = WashTradingAuditName
    agg_audit_name = WashTradingAggregationAuditName

    def get_all_audits(self):
        all_audits = {
            WashTradingAuditName.BUY_SELL_EXISTS: MarAudit(
                msg="BUY SELL exists for instrument",
                aggregation=self.agg_audits[self.agg_audit_name.CHECK_BUY_SELL],
                aggregation_name=self.agg_audit_name.CHECK_BUY_SELL,
            ),
            WashTradingAuditName.BUY_SELL_MISSING: MarAudit(
                msg="BUY SELL missing for instrument",
                aggregation=self.agg_audits[self.agg_audit_name.CHECK_BUY_SELL],
                aggregation_name=self.agg_audit_name.CHECK_BUY_SELL,
            ),
            WashTradingAuditName.MISSING_RIC: MarAudit(
                msg="Missing RIC for instrument",
                aggregation=self.agg_audits[self.agg_audit_name.FETCHING_RIC],
                aggregation_name=self.agg_audit_name.FETCHING_RIC,
            ),
            WashTradingAuditName.FETCHED_RIC: MarAudit(
                msg="Fetched RIC for instrument",
                aggregation=self.agg_audits[self.agg_audit_name.FETCHING_RIC],
                aggregation_name=self.agg_audit_name.FETCHING_RIC,
            ),
            WashTradingAuditName.MISSING_MARKET_DATA: MarAudit(
                msg="Missing market data for ric {ric}",
                aggregation=self.agg_audits[self.agg_audit_name.FETCHING_MARKET_DATA],
                aggregation_name=self.agg_audit_name.FETCHING_MARKET_DATA,
            ),
            WashTradingAuditName.FETCHED_MARKET_DATA: MarAudit(
                msg="Fetched market data for ric {ric}",
                aggregation=self.agg_audits[self.agg_audit_name.FETCHING_MARKET_DATA],
                aggregation_name=self.agg_audit_name.FETCHING_MARKET_DATA,
            ),
            WashTradingAuditName.CHECKED_DAY_TRADED_NOTIONAL_COLUMN: MarAudit(
                msg="Checked day traded notional columns for ric {ric}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.CHECKING_DAY_TRADED_NOTIONAL_COLUMN
                ],
                aggregation_name=self.agg_audit_name.CHECKING_DAY_TRADED_NOTIONAL_COLUMN,
            ),
            WashTradingAuditName.MISSING_DAY_TRADED_NOTIONAL_COLUMN: MarAudit(
                msg="Missing day traded notional columns for ric {ric}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.CHECKING_DAY_TRADED_NOTIONAL_COLUMN
                ],
                aggregation_name=self.agg_audit_name.CHECKING_DAY_TRADED_NOTIONAL_COLUMN,
            ),
            WashTradingAuditName.EXTRACTED_DAY_TRADED_NOTIONAL_VALUE: MarAudit(
                msg="Extracted day traded notional value for ric {ric}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.EXTRACTING_DAY_TRADED_NOTIONAL_VALUE
                ],
                aggregation_name=self.agg_audit_name.EXTRACTING_DAY_TRADED_NOTIONAL_VALUE,
            ),
            WashTradingAuditName.MISSING_DAY_TRADED_NOTIONAL_VALUE: MarAudit(
                msg="Missing day traded notional value for ric {ric}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.EXTRACTING_DAY_TRADED_NOTIONAL_VALUE
                ],
                aggregation_name=self.agg_audit_name.EXTRACTING_DAY_TRADED_NOTIONAL_VALUE,
            ),
            WashTradingAuditName.APPLIED_DAY_TRADED_NOTIONAL_THRESHOLD: MarAudit(
                msg="Applied day traded notional threshold value of {threshold_value} "
                "for ric {ric}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD,
            ),
            WashTradingAuditName.DROPPED_DAY_TRADED_NOTIONAL_THRESHOLD: MarAudit(
                msg="Dropped records due to day traded notional value {notional_value}"
                " for ric {ric} not passing threshold {threshold_value}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLYING_DAY_TRADED_NOTIONAL_THRESHOLD,
            ),
            WashTradingAuditName.EMPTY_TIME_WINDOW_GROUP: MarAudit(
                msg="Empty time window group for execution {execution} for instrument {instrument}",
                aggregation=self.agg_audits[self.agg_audit_name.CREATING_TIME_WINDOW_GROUP],
                aggregation_name=self.agg_audit_name.CREATING_TIME_WINDOW_GROUP,
            ),
            WashTradingAuditName.CREATE_TIME_WINDOW_GROUP: MarAudit(
                msg="Created time window group based on execution {execution}",
                aggregation=self.agg_audits[self.agg_audit_name.CREATING_TIME_WINDOW_GROUP],
                aggregation_name=self.agg_audit_name.CREATING_TIME_WINDOW_GROUP,
            ),
            WashTradingAuditName.EMPTY_MAX_PRICE_DIFFERENCE: MarAudit(
                msg="Empty group after applying max price difference based"
                " on execution {execution}",
                aggregation=self.agg_audits[self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE],
                aggregation_name=self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE,
            ),
            WashTradingAuditName.FILTERED_MAX_PRICE_DIFFERENCE: MarAudit(
                msg="Group after applying max price difference based on execution {execution}",
                aggregation=self.agg_audits[self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE],
                aggregation_name=self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE,
            ),
            WashTradingAuditName.DROPPED_MINIMUM_NOTIONAL_THRESHOLD: MarAudit(
                msg="Dropping group {execution} as sum of notional in the group is "
                "{notional_sum} is below the minimum notional threshold {minimum_threshold}",
                aggregation=self.agg_audits[self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE],
                aggregation_name=self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE,
            ),
            WashTradingAuditName.APPLIED_MINIMUM_NOTIONAL_THRESHOLD: MarAudit(
                msg="Applied the minimum notional threshold {minimum_threshold} "
                "to execution {execution} with "
                "sum of notional in the group being {notional_sum}"
                "the minimum notional threshold {minimum_threshold}",
                aggregation=self.agg_audits[self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE],
                aggregation_name=self.agg_audit_name.FILTERING_BY_MAX_PRICE_DIFFERENCE,
            ),
            WashTradingAuditName.DROPPED_MINIMUM_TRADED_QUANTITY_THRESHOLD: MarAudit(
                msg="Dropping group {execution} as buys volume: {buys_volume} "
                "and/or sells volume: {sells_volume} are"
                "below minimum traded quantity threshold: {volume_threshold}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLY_MIN_TRADED_QUANTITY_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLY_MIN_TRADED_QUANTITY_THRESHOLD,
            ),
            WashTradingAuditName.APPLIED_MINIMUM_TRADED_QUANTITY_THRESHOLD: MarAudit(
                msg="Applied minimum traded quantity threshold: "
                "{volume_threshold} to execution: {execution} "
                "with buys volume: {buys_volume} and sells volume: {sells_volume}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLY_MIN_TRADED_QUANTITY_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLY_MIN_TRADED_QUANTITY_THRESHOLD,
            ),
            WashTradingAuditName.DROPPED_MAX_VOLUME_DIFFERENCE_THRESHOLD: MarAudit(
                msg="Dropping group {execution} as volume percentage difference: "
                "{volume_difference} is above max volume difference threshold: {volume_threshold}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD,
            ),
            WashTradingAuditName.APPLIED_MAX_VOLUME_DIFFERENCE_THRESHOLD: MarAudit(
                msg="Applied max volume difference threshold: {volume_threshold} to execution"
                ": {execution} with volume difference of {volume_difference}",
                aggregation=self.agg_audits[
                    self.agg_audit_name.APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD
                ],
                aggregation_name=self.agg_audit_name.APPLY_MAX_VOLUME_DIFFERENCE_THRESHOLD,
            ),
        }

        return all_audits

    def get_all_agg_audits(self) -> dict[str, MarAggregationAudit]:
        return self.agg_audits
