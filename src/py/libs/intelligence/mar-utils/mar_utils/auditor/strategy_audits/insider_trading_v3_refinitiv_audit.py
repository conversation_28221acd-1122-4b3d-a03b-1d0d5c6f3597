from mar_utils.auditor.generic_mar_audit import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from mar_utils.auditor.mar_audit_models import <PERSON><PERSON>gg<PERSON><PERSON><PERSON><PERSON>, Mar<PERSON><PERSON>t
from mar_utils.auditor.refinitv_audit_static import RefinitivAggregatedAudit, RefinitivAuditName
from se_elastic_schema.static.mar_audit import DropReason, StepType


class InsiderTradingV3RefinitivAuditName:
    """The class to hold all the step audit names for the
    InsiderTradingV3Refinitiv."""

    # Create Group State
    RIC_MISSING = "ric_missing"

    NO_EXECUTION = "no_execution"
    EXECUTION_FOUND = "execution_found"
    BUY_SELL_MISSING = "buy_sell_missing"
    BUY_SELL_PRESENT = "buy_sell_present"

    # Market data / News Event related audits
    MARKET_DATA_MISSING_FOR_RIC = "market_data_missing_for_ric"
    MARKET_DATA_INVALID_CLOSE_PRICE = "market_data_invalid_close_price"
    MARKET_DATA_VOLUME_VARIATION = "market_data_volume_variation"
    MARKET_DATE_OUTSIDE_MAX_DAYS = "market_date_outside_max_days"
    CURRENCY_CONVERSION_FAILED = "currency_conversion_failed"
    MARKET_DATA_FOUND = "market_data_found"

    MARKET_NEWS_EVENTS = "market_news_events"
    INVALID_CLOSE_PRICE_VARIATION = "invalid_close_price_variation"
    MIN_PERCENTAGE_PRICE_VARIATION = "min_percentage_price_variation"
    CLOSE_PRICE_VARIATION_OUTSIDE_RANGE = "close_price_variation_outside_range"
    CLOSE_PRICE_VARIATION_OPP_DIRECTION = "close_price_variation_opp_direction"
    PNL_CALCULATION_MARKET_DATA_MISSING = "pnl_calculation_md_missing"

    PARENT_ORDER_ID_MISSING = "parent_order_id_missing"
    PARENT_ORDER_MISSING = "parent_order_missing"
    PARENT_ORDER_DATE_MISSING = "parent_order_date_missing"
    PARENT_ORDER_OUTSIDE_OBSERVATION_PERIOD = "parent_order_outside_observation_period"
    INSTRUMENT_FIELD_MISSING = "instrument_field_missing"
    ORDER_EXCLUSION_NO_DROP = "order_exclusion_no_drop"

    CONVERT_PRICE_SUCCESS = "convert_price_success"
    CURRENCY_FIELD_MISSING = "currency_field_missing"
    CURRENCY_MISALIGNED = "price_currency_misaligned"
    EVALUATION_TYPE_COL_MISSING = "evaluation_type_col_missing"

    EVALUATION_TYPE_COL_EXISTS = "evaluation_type_col_exists"
    GROUPING_DATA_MISSING = "grouping_data_missing"
    GROUPING_DATA_EXISTS = "grouping_data_exists"

    OBSERVATION_PERIOD_TRADE_AMOUNT = "observation_period_trade_amount"
    OBSERVATION_PERIOD_TRADE_BELOW_THRESHOLD = "observation_period_trade_below_threshold"
    BEHAVIOUR_DATA_MISSING = "behaviour_data_missing"
    MIN_TRADE_AMOUNT_PASSED = "min_trade_amount_passed"

    OUTSIDE_BEHAVIOUR_CONFIDENCE = "outside_behaviour_confidence"
    ACTIVITY_BEHAVIOUR_PERIOD_PASSED = "activity_behaviour_period_passed"
    ACTIVITY_PNL_PASSED = "activity_pnl_passed"
    PNL_CALCULATION_COLUMN_MISSING = "pnl_calculation_column_missing"
    PNL_BELOW_THRESHOLD = "pnl_below_threshold"
    TRANSACTION_VOLUME_CALCULATION = "transaction_volume_calculation"
    NEWS_SENTIMENT_BELOW_THRESHOLD = "news_sentiment_below_threshold"
    NULL_EVENT_DIRECTION = "null_event_direction"
    EVENT_DIRECTION_ANALYSIS = "event_direction_analysis"
    SENTIMENT_THRESHOLD_PASSED = "sentiment_threshold_passed"

    POSITIONS_COLUMN_MISSING = "positions_column_missing"
    NO_POSITIONS_DATA = "no_positions_data"
    RELATIVE_ACTIVITY_THRESHOLD = "relative_activity_threshold"
    RELATIVE_ACTIVITY_PASSED = "relative_activity_passed"

    ACTIVITY_CONFIDENCE_INTERVAL = "activity_confidence_interval"
    ACTIVITY_CONFIDENCE_INTERVAL_PASSED = "activity_confidence_interval_passed"


class InsiderTradingV3RefinitivAggAuditName:
    """The class to hold all the aggregation audit names for the
    InsiderTradingV3Refinitiv."""

    FETCH_RIC = "fetch_ric"
    FETCH_EXECUTION = "fetch_execution"
    BUY_SELL_CHECK = "buy_sell_check"
    FETCH_REFINITIV_NEWS = "fetch_refinitiv_news"
    FETCH_MARKET_DATA = "market_data_missing"

    MARKET_NEWS_EVENT = "market_news_event"
    ORDER_EXCLUSION = "order_exclusion"
    ORDER_OUTSIDE_OBSERVATION_PERIOD = "parent_order_outside_observation_period"
    INSTRUMENT_FIELD_MISSING = "instrument_field_missing"
    CONVERT_PRICE = "convert_price"
    EVALUATION_TYPE_COL_CHECK = "evaluation_type_col_check"
    GROUPING_DATA_CHECK = "grouping_data_check"
    ACTIVITY_MINIMUM_TRADE_AMOUNT = "activity_minimum_trade_amount"
    ACTIVITY_BEHAVIOUR_PERIOD = "activity_behaviour_period"
    ACTIVITY_MINIMUM_PNL = "activity_minimum_pnl"
    SENTIMENT_THRESHOLD = "sentiment_threshold"
    RELATIVE_ACTIVITY = "relative_activity"
    ACTIVITY_CONFIDENCE_INTERVAL = "activity_confidence_interval"


class InsiderTradingV3RefinitivAggregationAudit:
    """These are all the aggregation audits for ITv3, as would be seen in the
    UI."""

    agg_audits = {
        InsiderTradingV3RefinitivAggAuditName.FETCH_RIC: MarAggregationAudit(
            step_number=1,
            step_name="Fetching RIC for given instrument",
            step_type=StepType.MARKET_DATA_RIC,
            drop_reason=DropReason.RECORDS_DROPPED,
        ),
        InsiderTradingV3RefinitivAggAuditName.FETCH_EXECUTION: MarAggregationAudit(
            step_number=2,
            step_name="Fetching executions for given instrument",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
        ),
        InsiderTradingV3RefinitivAggAuditName.FETCH_REFINITIV_NEWS: RefinitivAggregatedAudit.FETCH_REFINITV_NEWS.update_step_number(  # noqa: E501
            3
        ),
        InsiderTradingV3RefinitivAggAuditName.FETCH_MARKET_DATA: MarAggregationAudit(
            step_number=4,
            step_name="Get Market data",
            step_type=StepType.FETCH_TENANT_DATA,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MARKET_DATA,
        ),
        InsiderTradingV3RefinitivAggAuditName.MARKET_NEWS_EVENT: MarAggregationAudit(
            step_number=5,
            step_name="Check for Market/News events based on selected Event Creation threshold",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_MARKET_NEWS_EVENTS,
        ),
        InsiderTradingV3RefinitivAggAuditName.BUY_SELL_CHECK: MarAggregationAudit(
            step_number=6,
            step_name="Check that buy/sell is present in the order state dataset for"
            "event day {event_day}",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.ORDER_EXCLUSION: MarAggregationAudit(
            step_number=7,
            step_name="Orders dropped because of the threshold: Order Exclusion",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.INSTRUMENT_FIELD_MISSING: MarAggregationAudit(
            step_number=8,
            step_name="Key instrument filed {field} missing from order dataset",
            step_type=StepType.FILTERS,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.CONVERT_PRICE: MarAggregationAudit(
            step_number=9,
            step_name="Price Conversion for given currency for order states",
            step_type=StepType.MARKET_DATA,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.EVALUATION_TYPE_COL_CHECK: MarAggregationAudit(
            step_number=10,
            step_name="Check whether Evaluation Type column is present in the order state dataset",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.GROUPING_DATA_CHECK: MarAggregationAudit(
            step_number=11,
            step_name="Check whether Instrument and Evaluation type exists for"
            "Event Day: {event_day}",
            step_type=StepType.GROUPING,
            drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
        ),
        InsiderTradingV3RefinitivAggAuditName.ACTIVITY_MINIMUM_TRADE_AMOUNT: MarAggregationAudit(
            step_number=12,
            step_name="Dropped because of the threshold: Activity Minimum Trade Amount",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
        InsiderTradingV3RefinitivAggAuditName.ACTIVITY_BEHAVIOUR_PERIOD: MarAggregationAudit(
            step_number=13,
            step_name="Dropped because of the threshold: Activity Behaviour Period",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
        InsiderTradingV3RefinitivAggAuditName.ACTIVITY_MINIMUM_PNL: MarAggregationAudit(
            step_number=14,
            step_name="Dropped because of the threshold: Activity Minimum PNL",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
        InsiderTradingV3RefinitivAggAuditName.SENTIMENT_THRESHOLD: MarAggregationAudit(
            step_number=15,
            step_name="Dropped because of the threshold: Sentiment Threshold",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
        InsiderTradingV3RefinitivAggAuditName.RELATIVE_ACTIVITY: MarAggregationAudit(
            step_number=16,
            step_name="Dropped because of the threshold: Relative Activity",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
        InsiderTradingV3RefinitivAggAuditName.ACTIVITY_CONFIDENCE_INTERVAL: MarAggregationAudit(
            step_number=17,
            step_name="Dropped because of the threshold: Activity Confidence Interval",
            step_type=StepType.THRESHOLD_CALCULATION,
            drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
        ),
    }


class InsiderTradingV3RefinitivAudit(GenericMarAudit):
    # Aliases for longer verbose names
    agg_audits = InsiderTradingV3RefinitivAggregationAudit.agg_audits
    audit_name = InsiderTradingV3RefinitivAuditName
    agg_audit_name = InsiderTradingV3RefinitivAggAuditName

    def get_all_audits(self):
        all_audits = {
            self.audit_name.RIC_MISSING: MarAudit(
                msg="The instrument {instrument} doesn't have RIC. Won't be able to fetch"
                "news data. Event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_RIC],
                aggregation_name=self.agg_audit_name.FETCH_RIC,
            ),
            self.audit_name.NO_EXECUTION: MarAudit(
                msg="No executions found for instrument {instrument} for the event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_EXECUTION],
                aggregation_name=self.agg_audit_name.FETCH_EXECUTION,
            ),
            self.audit_name.EXECUTION_FOUND: MarAudit(
                msg="Executions found for instrument {instrument} for the event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_EXECUTION],
                aggregation_name=self.agg_audit_name.FETCH_EXECUTION,
            ),
            self.audit_name.BUY_SELL_MISSING: MarAudit(
                msg="Buy/Sell column is missing in the order state dataset for event day "
                "{event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.BUY_SELL_CHECK],
                aggregation_name=self.agg_audit_name.BUY_SELL_CHECK,
            ),
            self.audit_name.BUY_SELL_PRESENT: MarAudit(
                msg="Buy/Sell column is present in the order state dataset for event day "
                "{event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.BUY_SELL_CHECK],
                aggregation_name=self.agg_audit_name.BUY_SELL_CHECK,
            ),
            RefinitivAuditName.INVALID_RELEVANCE: MarAudit(
                msg=self.refinitiv_audits[RefinitivAuditName.INVALID_RELEVANCE],
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_REFINITIV_NEWS],
                aggregation_name=self.agg_audit_name.FETCH_REFINITIV_NEWS,
            ),
            RefinitivAuditName.MISSING_VALUES: MarAudit(
                msg=self.refinitiv_audits[RefinitivAuditName.MISSING_VALUES],
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_REFINITIV_NEWS],
                aggregation_name=self.agg_audit_name.FETCH_REFINITIV_NEWS,
            ),
            RefinitivAuditName.NO_NEWS_TO_PARSE: MarAudit(
                msg=self.refinitiv_audits[RefinitivAuditName.NO_NEWS_TO_PARSE],
                aggregation_name=self.agg_audit_name.FETCH_REFINITIV_NEWS,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_REFINITIV_NEWS],
            ),
            RefinitivAuditName.NEWS_FOUND: MarAudit(
                msg=self.refinitiv_audits[RefinitivAuditName.NEWS_FOUND],
                aggregation_name=self.agg_audit_name.FETCH_REFINITIV_NEWS,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_REFINITIV_NEWS],
            ),
            self.audit_name.MARKET_DATA_MISSING_FOR_RIC: MarAudit(
                msg="No Market data was found for Instrument/Ric {ric} on {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MARKET_DATA_INVALID_CLOSE_PRICE: MarAudit(
                msg="Market data found for Instrument/RIC {ric} on Event Day: {event_day}"
                "did not hav valid CLOSE PRICE",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MARKET_DATA_VOLUME_VARIATION: MarAudit(
                msg="The volume variation is lower than Market Data Event - Daily Volume Variation"
                " Threshold {threshold} for instrument {instrument} on event day {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MARKET_DATE_OUTSIDE_MAX_DAYS: MarAudit(
                msg="The market data has more than 5 business days difference for RIC {ric} "
                "on event day {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.CURRENCY_CONVERSION_FAILED: MarAudit(
                msg="Currency conversion using ECB REF rates from market data currency"
                "{market_data_currency} to order currency {order_currency} failed",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.INVALID_CLOSE_PRICE_VARIATION: MarAudit(
                msg="The close price for RIC {ric} is empty or there isn't a close price "
                "variation. Close price for event day {event_day} : {close_price}"
                "Close price for previous day {prev_close_price}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MIN_PERCENTAGE_PRICE_VARIATION: MarAudit(
                msg="Close Price Variation : {variation} is less than Minimum Price"
                " Variation Threshold: {threshold} for event day {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.CLOSE_PRICE_VARIATION_OUTSIDE_RANGE: MarAudit(
                msg="Close price variation {variation} is outside the allowed range "
                "{allowed_range} for event day {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MARKET_DATA_FOUND: MarAudit(
                msg="Market data found for Instrument/RIC {ric} on Event Day: {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.CLOSE_PRICE_VARIATION_OPP_DIRECTION: MarAudit(
                msg="Direction {direction} and Close price variations {variation} "
                "are in opposite directions for event_day {event_day}",
                aggregation_name=self.agg_audit_name.FETCH_MARKET_DATA,
                aggregation=self.agg_audits[self.agg_audit_name.FETCH_MARKET_DATA],
            ),
            self.audit_name.MARKET_NEWS_EVENTS: MarAudit(
                msg="Instruments do not have relevant market/news events for event day {event_day}"
                "for the selected EVENT CREATION threshold {event_creation}",
                aggregation=self.agg_audits[self.agg_audit_name.MARKET_NEWS_EVENT],
                aggregation_name=self.agg_audit_name.MARKET_NEWS_EVENT,
            ),
            self.audit_name.PNL_CALCULATION_COLUMN_MISSING: MarAudit(
                msg="Orders are missing field(s) {field} required for PNL Calculation",
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_PNL],
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_PNL,
            ),
            self.audit_name.PARENT_ORDER_ID_MISSING: MarAudit(
                msg="The required parent order id is missing but Order "
                "Exclusion threshold is set to True for event day: {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.PARENT_ORDER_MISSING: MarAudit(
                msg="The required parent orders could not be found but Order Exclusion "
                "threshold is set to True for event day: {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.PARENT_ORDER_DATE_MISSING: MarAudit(
                msg="The parent order are missing date column {field} for event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.PARENT_ORDER_OUTSIDE_OBSERVATION_PERIOD: MarAudit(
                msg="No executions after removing parent order outside of observation period "
                "{observation_period} for event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.ORDER_EXCLUSION_NO_DROP: MarAudit(
                msg="No orders were dropped because of the Order Exclusion threshold",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.INSTRUMENT_FIELD_MISSING: MarAudit(
                msg="Instrument Field {field} for order state dataset for event day {event_day}",
                aggregation=self.agg_audits[self.agg_audit_name.ORDER_EXCLUSION],
                aggregation_name=self.agg_audit_name.ORDER_EXCLUSION,
            ),
            self.audit_name.CURRENCY_FIELD_MISSING: MarAudit(
                msg="The column {field} is missing for event day {event_day} which is "
                "required for price conversion",
                aggregation_name=self.agg_audit_name.CONVERT_PRICE,
                aggregation=self.agg_audits[self.agg_audit_name.CONVERT_PRICE],
            ),
            self.audit_name.CURRENCY_MISALIGNED: MarAudit(
                msg="There is a misalignment between Transaction Details Price Currency and "
                "Transaction Volume Native currency for event day {event_day}",
                aggregation_name=self.agg_audit_name.CONVERT_PRICE,
                aggregation=self.agg_audits[self.agg_audit_name.CONVERT_PRICE],
            ),
            self.audit_name.CONVERT_PRICE_SUCCESS: MarAudit(
                msg="Price conversion successful for event day {event_day}",
                aggregation_name=self.agg_audit_name.CONVERT_PRICE,
                aggregation=self.agg_audits[self.agg_audit_name.CONVERT_PRICE],
            ),
            self.audit_name.EVALUATION_TYPE_COL_MISSING: MarAudit(
                msg="The column {field} is missing for event day {event_day} which is "
                "required for selected Evaluation Type",
                aggregation_name=self.agg_audit_name.EVALUATION_TYPE_COL_CHECK,
                aggregation=self.agg_audits[self.agg_audit_name.EVALUATION_TYPE_COL_CHECK],
            ),
            self.audit_name.EVALUATION_TYPE_COL_EXISTS: MarAudit(
                msg="The evaluation columns exists for event day {event_day} which is required for"
                "selected Evaluation Type",
                aggregation_name=self.agg_audit_name.EVALUATION_TYPE_COL_CHECK,
                aggregation=self.agg_audits[self.agg_audit_name.EVALUATION_TYPE_COL_CHECK],
            ),
            self.audit_name.GROUPING_DATA_MISSING: MarAudit(
                msg="Evaluation type and instrument are both missing for {event_day}",
                aggregation_name=self.agg_audit_name.GROUPING_DATA_CHECK,
                aggregation=self.agg_audits[self.agg_audit_name.GROUPING_DATA_CHECK],
            ),
            self.audit_name.GROUPING_DATA_EXISTS: MarAudit(
                msg="Grouping successful:  {event_day}",
                aggregation_name=self.agg_audit_name.GROUPING_DATA_CHECK,
                aggregation=self.agg_audits[self.agg_audit_name.GROUPING_DATA_CHECK],
            ),
            self.audit_name.OBSERVATION_PERIOD_TRADE_AMOUNT: MarAudit(
                msg="The observation period trade amount is None for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT],
            ),
            self.audit_name.OBSERVATION_PERIOD_TRADE_BELOW_THRESHOLD: MarAudit(
                msg="The observation period trade amount {amount} is below the "
                "activity min trade threshold {threshold_amount} for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT],
            ),
            self.audit_name.BEHAVIOUR_DATA_MISSING: MarAudit(
                msg="No behaviour period amount data is available for event day {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT],
            ),
            self.audit_name.MIN_TRADE_AMOUNT_PASSED: MarAudit(
                msg="Activity Minimum Trade Amount passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_TRADE_AMOUNT],
            ),
            self.audit_name.OUTSIDE_BEHAVIOUR_CONFIDENCE: MarAudit(
                msg="Observational net trade amount {observation_amount} is not outside the "
                "allowed range interval {behaviour_confidence}. Event day {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_BEHAVIOUR_PERIOD,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_BEHAVIOUR_PERIOD],
            ),
            self.audit_name.ACTIVITY_BEHAVIOUR_PERIOD_PASSED: MarAudit(
                msg="Activity Behaviour Period passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_BEHAVIOUR_PERIOD,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_BEHAVIOUR_PERIOD],
            ),
            self.audit_name.PNL_BELOW_THRESHOLD: MarAudit(
                msg="The PNL value {pnl} is below the threshold {threshold}",
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_PNL],
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_PNL,
            ),
            self.audit_name.ACTIVITY_PNL_PASSED: MarAudit(
                msg="Activity Minimum PNL passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_MINIMUM_PNL,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_MINIMUM_PNL],
            ),
            self.audit_name.TRANSACTION_VOLUME_CALCULATION: MarAudit(
                msg="The transaction volume is None for event day {event_day}",
                aggregation_name=self.agg_audit_name.SENTIMENT_THRESHOLD,
                aggregation=self.agg_audits[self.agg_audit_name.SENTIMENT_THRESHOLD],
            ),
            self.audit_name.NEWS_SENTIMENT_BELOW_THRESHOLD: MarAudit(
                msg="No news has sentiment above {sentiment_threshold} for instrument "
                "{instrument} on event_day {event_day}",
                aggregation_name=self.agg_audit_name.SENTIMENT_THRESHOLD,
                aggregation=self.agg_audits[self.agg_audit_name.SENTIMENT_THRESHOLD],
            ),
            self.audit_name.NULL_EVENT_DIRECTION: MarAudit(
                msg="No event direction to analyze for {instrument} on event day {event_day}",
                aggregation_name=self.agg_audit_name.SENTIMENT_THRESHOLD,
                aggregation=self.agg_audits[self.agg_audit_name.SENTIMENT_THRESHOLD],
            ),
            self.audit_name.EVENT_DIRECTION_ANALYSIS: MarAudit(
                msg="{analysis_msg} Observation amount: {observation_amount}. "
                "Event direction: {direction}"
                "Behaviour mean: {behaviour_mean}. Event day {event_day}",
                aggregation_name=self.agg_audit_name.SENTIMENT_THRESHOLD,
                aggregation=self.agg_audits[self.agg_audit_name.SENTIMENT_THRESHOLD],
            ),
            self.audit_name.SENTIMENT_THRESHOLD_PASSED: MarAudit(
                msg="Sentiment threshold passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.SENTIMENT_THRESHOLD,
                aggregation=self.agg_audits[self.agg_audit_name.SENTIMENT_THRESHOLD],
            ),
            self.audit_name.POSITIONS_COLUMN_MISSING: MarAudit(
                msg="Missing fields {fields} required to fetch positions data for event day"
                " {event_day}",
                aggregation_name=self.agg_audit_name.RELATIVE_ACTIVITY,
                aggregation=self.agg_audits[self.agg_audit_name.RELATIVE_ACTIVITY],
            ),
            self.audit_name.NO_POSITIONS_DATA: MarAudit(
                msg="No positions data found for ISIN {isin}, currency {currency}"
                "and evaluation type {evaluation} for event day {event_day}",
                aggregation_name=self.agg_audit_name.RELATIVE_ACTIVITY,
                aggregation=self.agg_audits[self.agg_audit_name.RELATIVE_ACTIVITY],
            ),
            self.audit_name.RELATIVE_ACTIVITY_THRESHOLD: MarAudit(
                msg="Relative activity {relative_activity} is below the threshold {threshold} for"
                " event day {event_day}",
                aggregation_name=self.agg_audit_name.RELATIVE_ACTIVITY,
                aggregation=self.agg_audits[self.agg_audit_name.RELATIVE_ACTIVITY],
            ),
            self.audit_name.RELATIVE_ACTIVITY_PASSED: MarAudit(
                msg="Relative activity passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.RELATIVE_ACTIVITY,
                aggregation=self.agg_audits[self.agg_audit_name.RELATIVE_ACTIVITY],
            ),
            self.audit_name.ACTIVITY_CONFIDENCE_INTERVAL: MarAudit(
                msg="The net traded amount is not within the allowed range "
                "{allowed_range} for event_day {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_CONFIDENCE_INTERVAL,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_CONFIDENCE_INTERVAL],
            ),
            self.audit_name.ACTIVITY_CONFIDENCE_INTERVAL_PASSED: MarAudit(
                msg="Activity confidence interval passed for event day: {event_day}",
                aggregation_name=self.agg_audit_name.ACTIVITY_CONFIDENCE_INTERVAL,
                aggregation=self.agg_audits[self.agg_audit_name.ACTIVITY_CONFIDENCE_INTERVAL],
            ),
        }

        return all_audits

    def get_all_agg_audits(self) -> dict[str, MarAggregationAudit]:
        return self.agg_audits
