import fsspec
import logging
import or<PERSON><PERSON>
import os
import shutil
import tempfile
import uuid
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from mar_utils.auditor.generic_mar_audit import GenericMarAudit
from mar_utils.auditor.mar_audit_models import <PERSON><PERSON><PERSON>t
from market_abuse_algorithms.mar_audit.mar_audit import (  # type: ignore[attr-defined]
    MarketAbuseAudit,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from pathlib import Path
from se_data_lake.lake_path import get_task_params_sha256, get_workflow_audit_directory
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import WatchExecutionStatus
from se_fsspec_utils import file_utils
from typing import Type

log = logging.getLogger("auditor")


class MARAuditor:
    def __init__(
        self,
        /,
        lake_prefix: str,
        aries_task_input: AriesTaskInput,
        mar_auditor_cls: Type[GenericMarAudit],
        identifier: str | None = None,
    ):
        self._cloud_base_audit_path = get_workflow_audit_directory(
            workflow_name=aries_task_input.workflow.name,
            workflow_trace_id=aries_task_input.workflow.trace_id,
            lake_prefix=lake_prefix,
            audit_type="mar_audit",
        )
        self._local_base_audit_path = Path(tempfile.gettempdir()).joinpath(
            aries_task_input.workflow.trace_id
        )
        self.identifier = identifier
        self._mar_audit_cls = mar_auditor_cls()
        self._audits_key_files: dict[str, Path] = {}
        self._check_step_audit_and_create_audit_file_path(
            mar_audit_class=self._mar_audit_cls, io_params=aries_task_input.input_param.params
        )

    def _check_step_audit_and_create_audit_file_path(
        self, mar_audit_class: GenericMarAudit, io_params: dict
    ):
        # Verify that no step audit has the step number as -1. If it does,
        # then fast fail as the -1 step number is reserved for the default, MUST be changed
        # always in algo.
        for audit_key, audit in mar_audit_class.all_audits.items():
            if audit.aggregation.step_number == -1:
                raise ValueError(f"Step number is not set for audit {audit.aggregation.step_name}.")
            parent_dir = self._local_base_audit_path.joinpath(f"{audit.aggregation_name}")
            Path(parent_dir).mkdir(parents=True, exist_ok=True)
            if self.identifier:
                self._audits_key_files[audit_key] = parent_dir.joinpath(
                    f"{self.identifier}_{audit.aggregation_name}.ndjson"
                )
            else:
                self._audits_key_files[audit_key] = parent_dir.joinpath(
                    f"{get_task_params_sha256(io_params)}_{audit_key}.ndjson"
                )

        logging.debug("Audit files created for all the audits. Files: %s", self._audits_key_files)

    def step_audit(self, audit_key: str, audit_data: dict, **kwargs):
        try:
            with open(self._audits_key_files[audit_key], "a") as f:
                mar_audit = self._mar_audit_cls.get_mar_audit(audit_key=audit_key, **audit_data)
                audit_model = StepAudit(
                    reason=mar_audit.msg, step_name=mar_audit.aggregation.step_name, **kwargs
                )
                f.write(orjson.dumps(audit_model.dict()).decode())
                f.write("\n")
        except Exception as e:
            log.error(
                f"Error writing audit for {audit_key}. Error: {e}",
            )

    def write_market_abuse_audit(
        self,
        context: StrategyContext,
        surv_watch: SurveillanceWatch,
        start: datetime,
    ):
        audit = MarketAbuseAudit(
            id=str(uuid.uuid4()),
            start=start,
            filters=orjson.dumps(context.filters).decode(),
            number_of_alerts=0,
            records_analysed=0,
            records_skipped=0,
            report_type=surv_watch.query.marketAbuseReportType,
            requested_by=context.requested_by,
            status=WatchExecutionStatus.IN_PROGRESS,
            thresholds=context.thresholds.json(),  # type: ignore
            watch_execution_id=context.watch_execution_id,
            watch_id=context.watch_id,
            watch_execution_type=context.watch_execution_type,
            watch_name=surv_watch.name,
            query=surv_watch.query.refineOptions,
        )
        market_abuse_audit_path = (
            f"{self._cloud_base_audit_path}/market_abuse_audit/market_abuse_audit.json"
        )
        fs, _, _ = fsspec.get_fs_token_paths(market_abuse_audit_path)
        with fs.open(market_abuse_audit_path, "w") as f:
            f.write(orjson.dumps(audit.dict()).decode())

    def write_audit_to_cloud(self):
        log.info("Writing %s audit files to cloud.", len(self._audits_key_files))
        step_audit_base_path = f"{self._cloud_base_audit_path}/step_audit/"
        for audit_key, audit_file in self._audits_key_files.items():
            # If the audit file does not exist, it means no audit was written for that audit key
            if not audit_file.exists():
                logging.debug("Audit file %s does not exist, skipping", audit_file)
                continue

            cloud_file_path = os.path.join(
                step_audit_base_path,
                audit_file.parent.relative_to(self._local_base_audit_path),
                audit_file.name,
            )
            log.debug(
                "Writing audit local file to cloud. Local -- %s, Cloud -- %s",
                audit_file,
                cloud_file_path,
            )
            fs, _, _ = fsspec.get_fs_token_paths(cloud_file_path)
            file_utils.upload(
                fs=fs,
                file_path=audit_file.as_posix(),
                save_path=cloud_file_path,
            )
            log.debug("Audit file written to cloud.")

        log.info("All audit files written to cloud.")

        shutil.rmtree(self._local_base_audit_path, ignore_errors=True)
