python_sources(
    name="surveillance_utils",
    sources=["surveillance_utils/**/*.py"],
)

python_tests(
    name="tests",
    sources=["tests_surveillance_utils/**/test_*.py"],
    dependencies=[
        ":surveillance_utils_test_resources",
        ":utils",
    ],
)

resources(
    name="surveillance_utils_test_resources",
    sources=[
        "tests_surveillance_utils/data/*.json",
        "tests_surveillance_utils/data/*.ndjson",
    ],
)
python_test_utils(
    name="utils",
    sources=[
        "tests_surveillance_utils/conftest.py",
    ],
)
