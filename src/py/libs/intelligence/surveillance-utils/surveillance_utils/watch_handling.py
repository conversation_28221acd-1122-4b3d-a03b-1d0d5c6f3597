import logging
from datetime import datetime
from se_elastic_schema.components.surveillance.watch_execution_details import WatchExecutionDetails
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.static.surveillance import WatchStatusType
from se_es_utils.slim_record_handler import SlimR<PERSON>ord<PERSON>andler
from surveillance_utils.exceptions import FailIfNoWatchRecord
from surveillance_utils.static import ExecutionDetailsFields, WatchFields

logger = logging.getLogger(__name__)


def fetch_watch_record(
    watch_id: str, tenant: str, record_handler: SlimRecordHandler
) -> SurveillanceWatch:
    """Fetches a Watch Record from elastic.

    :param watch_id: id of the SurveillanceWatch
    :param tenant: Name of the tenant to which the record belongs
    :param es_client: ElasticSearch client
    :return:
    """
    try:
        watch_record = record_handler.get_record_by_id(watch_id, tenant, SurveillanceWatch)[
            "_source"
        ]
    except Exception as e:
        raise FailIfNoWatchRecord(f"No Watch record for the ID: {watch_id} in index. Error: {e}")

    surveillance_watch = SurveillanceWatch.validate(watch_record)
    logger.info(f"Fetched SurveillanceWatch with id {watch_id}")

    return surveillance_watch


def update_last_execution(
    tenant: str,
    watch_id: str,
    field_to_update: ExecutionDetailsFields,
    surveillance_watch: SurveillanceWatch,
    record_handler: SlimRecordHandler,
    upper_bound_date: datetime = datetime.utcnow(),
):
    """Updates the field of ExecutionDetails to know when it was accessed.

    :param watch_id: Watch ID of the record to be updated
    :param tenant: Name of the tenant to which the record belongs
    :param field_to_update: Name of the field where the record will be updated
    :param record_handler: ElasticSearch client
    :param surveillance_watch:  SurveillanceWatch Record to be updated
    :param upper_bound_date: Upper bound date of the watch query
    :return:
    """
    if surveillance_watch.executionDetails is None:
        surveillance_watch.executionDetails = WatchExecutionDetails()

    doc = {WatchFields.EXECUTION_DETAILS: {field_to_update.value: upper_bound_date}}

    try:
        response = record_handler.update(
            doc_id=watch_id,
            partial_doc=doc,
            tenant=tenant,
            model=type(surveillance_watch),
        )
    except IndexError:
        raise IndexError(f"No matching record found for watch_id: {watch_id} in index")

    if response.get("result") != "updated":
        logger.warning(f"Failed to update watchRecord. Response was {response.get('result')}")
    else:
        setattr(surveillance_watch.executionDetails, field_to_update.value, upper_bound_date)
        logger.info(f"Updated {field_to_update.value} to: {upper_bound_date}")


def update_watch_status(
    tenant: str,
    watch_id: str,
    surveillance_watch: SurveillanceWatch,
    record_handler: SlimRecordHandler,
    watch_status: WatchStatusType,
):
    """Updates the field of status of the watch.

    :param tenant: Name of the tenant to which the record belongs
    :param watch_id: Watch ID of the record to be updated
    :param surveillance_watch:  SurveillanceWatch Record to be updated
    :param record_handler: ElasticSearch client
    :param watch_status: Status to update the watch
    :return:
    """

    assert watch_status in WatchStatusType.list(), f"{watch_status} is not a valid WatchStatusType"
    doc = {"status": watch_status}

    try:
        response = record_handler.update(
            doc_id=watch_id,
            partial_doc=doc,
            tenant=tenant,
            model=type(surveillance_watch),
        )
    except IndexError:
        raise IndexError(f"No matching record found for watch_id: {watch_id} in index")

    if response.get("result") != "updated":
        logger.warning(f"Failed to update watchRecord. Response was {response.get('result')}")
    else:
        setattr(surveillance_watch, "status", watch_status)
        logger.info(f"Updated {watch_id} to: {watch_status}")
