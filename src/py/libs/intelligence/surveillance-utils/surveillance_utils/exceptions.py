class FailIfNoWatchRecord(Exception):
    """Exception raise when there's no watch record for a given ID."""


class FailIfNoWatchExecutionRecord(Exception):
    """Exception raise when there's no watch execution record for a given
    ID."""


class FailIfNoThresholds(Exception):
    """Exception raise when there's no thresholds for a given watch.

    Without them, we can't run the algorithm.
    """


class FailIfNoValidWatchRecord(Exception):
    """Exception raise when there are errors on the watch record for a given
    ID."""


class FailIfDuplicatedWatchIds(Exception):
    """Exception raise when there more than 1 watch with the same id, since it
    should be unique."""


class FailIfNoValidWatchExecutionRecord(Exception):
    """Exception raise when there are errors on the watch execution record for
    a given ID."""
