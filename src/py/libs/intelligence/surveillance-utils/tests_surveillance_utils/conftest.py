import pytest
from pathlib import Path
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from surveillance_utils.test_mock_helpers import FakeSequenceService, FakeSlimRecordHandler

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")


@pytest.fixture()
def fake_slim_record_handler():
    return FakeSlimRecordHandler(
        version=8,
    )


@pytest.fixture()
def fake_sequence_service():
    return FakeSequenceService


@pytest.fixture
def surv_watch():
    return SurveillanceWatch.validate(
        {
            "&id": "a_id",
            "&key": "a_key",
            "&hash": "a_hash",
            "&timestamp": 1634160000,
            "name": "test",
            "query": {"marketAbuseReportType": "INSIDER_TRADING", "name": "test"},
        }
    )
