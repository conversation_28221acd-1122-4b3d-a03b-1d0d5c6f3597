import addict
from se_elastic_schema.models.tenant.surveillance.communication_alert import <PERSON><PERSON><PERSON>t
from se_es_utils import search_after
from surveillance_utils.deduplication import AlertDeduplication


def test_alert_deduplication_es6(monkeypatch, fake_slim_record_handler):
    def search_after_query_yield(*args, **kwargs):
        return [[{"hit": {"&id": str(idx)}}] for idx in range(111, 115)]

    monkeypatch.setattr(search_after, "search_after_query_yield", search_after_query_yield)

    alert_dedup = AlertDeduplication(
        tenant="fake-tenant",
        watch_id="watch_id",
        record_handler=fake_slim_record_handler,
    )

    surv_query = addict.Dict()
    surv_query = alert_dedup.update_query(surv_query, models=[CommunicationAlert])

    # Added 2 fields to the must_not clause
    assert len(surv_query.query.bool.must_not) == 2

    # ID excluded in query was actually created in ES
    excluded_id = list(
        fake_slim_record_handler.client.internal_storage[
            "fake-tenant-surveillance_exclude_by_id-alias"
        ].keys()
    )[0]
    assert surv_query.query.bool.must_not[1]["terms"]["&id"]["id"] == excluded_id

    # IDs stored in ES to be excluded match expected
    assert sorted(
        fake_slim_record_handler.client.internal_storage[
            "fake-tenant-surveillance_exclude_by_id-alias"
        ][excluded_id]["excludes"]
    ) == ["111", "112", "113", "114"]


def test_deduplication_alert_sampling(monkeypatch, fake_slim_record_handler):
    def search_after_query_yield(*args, **kwargs):
        return [[{"underlyingAlertId": str(idx)}] for idx in range(111, 115)]

    monkeypatch.setattr(search_after, "search_after_query_yield", search_after_query_yield)

    alert_dedup = AlertDeduplication(
        tenant="fake-tenant",
        watch_id="watch_id",
        record_handler=fake_slim_record_handler,
        alert_sampling=True,
    )
    surv_query = addict.Dict()
    surv_query = alert_dedup.update_query(surv_query, models=[CommunicationAlert])

    excluded_id = list(
        fake_slim_record_handler.client.internal_storage[
            "fake-tenant-surveillance_exclude_by_id-alias"
        ].keys()
    )[0]

    assert surv_query.query.bool.must_not[1]["terms"]["&id"]["id"] == excluded_id

    # IDs stored in ES to be excluded match expected
    assert sorted(
        fake_slim_record_handler.client.internal_storage[
            "fake-tenant-surveillance_exclude_by_id-alias"
        ][excluded_id]["excludes"]
    ) == ["111", "112", "113", "114"]
