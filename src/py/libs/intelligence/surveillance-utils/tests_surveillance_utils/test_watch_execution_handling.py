from se_elasticsearch.repository.helpers import Meta
from se_es_utils.slim_record_handler import <PERSON>RecordHandler
from surveillance_utils.watch_execution_handling import create_watch_execution


class FakeRecordHandler(SlimRecordHandler):
    def __new__(cls, meta):
        return object.__new__(cls)

    def __init__(self, meta):
        self.meta = meta

    def create(*args, **kwargs):
        return "fake_id"


def test_create_watch_execution(surv_watch):
    out = create_watch_execution("test", surv_watch, FakeRecordHandler(Meta()), "id")
    assert out.id__ == "fake_id"
