import backoff
import httpx
import json
import logging
from datetime import date
from news_sdk.api_endpoints import RefinitivNewsAPI
from news_sdk.helpers import counted, get_ssm_parameter
from news_sdk.rdp_token import RDPToken
from typing import Any, Dict, Generator, List, Optional

logging.basicConfig(level=logging.INFO)


class NewsDataClient:
    def __init__(self) -> None:
        self.credentials = json.loads(
            get_ssm_parameter(parameter_name="REFINITIV_NEWS_API_CREDENTIALS", decrypt=True)
        )
        self.token_client = RDPToken(credentials=self.credentials)

    @counted
    @backoff.on_exception(backoff.expo, httpx.HTTPStatusError, max_tries=4)
    def get_perm_ids(self, rics: List[str]) -> List[Dict[str, Optional[str]]]:
        headers = {
            "Authorization": f"Bearer {self.token_client.token}",
            "Content-Type": "application/json",
        }
        cols = ["OrganizationId", "CommonName", "OrganizationSubType"]
        body = {"universe": rics, "to": cols}
        raw_response = httpx.post(
            RefinitivNewsAPI.PERM_ID_URL,
            headers=headers,
            data=json.dumps(body),  # type: ignore
            timeout=60,
        )
        raw_response.raise_for_status()
        response = raw_response.json()
        res: List[List[Optional[str]]] = response.get("data", [])
        return [dict(zip(["RIC"] + cols, d)) for d in res]

    @counted
    @backoff.on_exception(backoff.expo, httpx.HTTPStatusError, max_tries=4)
    def get_headlines(
        self,
        rics: List[str],
        date_from: Optional[date],
        date_to: Optional[date],
    ) -> Generator:
        if len(rics) > 5000:
            raise ValueError("Cannot get news data for more than 1k RICs")
        headers = {"Authorization": f"Bearer {self.token_client.token}"}
        params = RefinitivNewsAPI.get_headlines_params(
            rics=rics, date_from=date_from, date_to=date_to, limit=100
        )
        try:
            raw_response = httpx.get(RefinitivNewsAPI.HEADLINES_URL, params=params, headers=headers)
            raw_response.raise_for_status()
            response = raw_response.json()
            yield response["data"]
        except Exception as e:
            self.token_client.refresh_token()
            logging.exception(e)
            raise e

        while response["meta"]["count"] == response["meta"]["pageLimit"]:
            response = self._get_next_page_headlines(cursor=response["meta"]["next"])
            yield response["data"]
        return

    @counted
    @backoff.on_exception(backoff.expo, httpx.HTTPStatusError, max_tries=4)
    def _get_next_page_headlines(self, cursor: str) -> List[Dict[str, Any]]:
        params = {"cursor": cursor}
        headers = {"Authorization": f"Bearer {self.token_client.token}"}
        try:
            raw_response = httpx.get(RefinitivNewsAPI.HEADLINES_URL, params=params, headers=headers)
            raw_response.raise_for_status()
            response: List[Dict[str, Any]] = raw_response.json()
            return response
        except Exception as e:
            self.token_client.refresh_token()
            logging.exception(e)
            raise e

    def get_stories(self, story_ids: List[Dict[str, Any]]) -> Any:
        for story_id in story_ids:
            try:
                yield self.get_story(story_id=story_id["storyId"])
            except Exception as e:
                logging.exception(f"failed to get {story_id['storyId']} {str(e)}")

    @counted
    @backoff.on_exception(backoff.expo, httpx.HTTPStatusError, max_tries=4)
    def get_story(self, story_id: str) -> Optional[httpx.Response]:
        headers = {"Authorization": f"Bearer {self.token_client.token}"}
        request = None
        try:
            request = httpx.get(RefinitivNewsAPI.STORY_URL + f"/{story_id}", headers=headers)
            request.raise_for_status()
        except Exception as e:
            logging.exception(e)
            return request
        return request
