from datetime import date
from typing import Any, Dict, List, Optional, Union


class RefinitivNewsAPI:
    BASE_URL = "https://api.refinitiv.com"

    AUTH_CATEGORY = "/auth/oauth2"
    RDP_VERSION = "/v1"
    TOKEN = "/token"

    # SUBSCRIPTION_CATEGORY = "/message-services"
    SUBSCRIPTION_CATEGORY = "/alerts"
    HEADLINE_SUBSCRIPTION = "/news-headlines/subscriptions"
    STORIES_SUBSCRIPTION = "/news-stories-subscriptions"

    NEWS_DATA = "/data/news"
    HEADLINE_DATA = "/headlines"
    STORIES_DATA = "/stories"
    PERM_ID_DATA = "/data/symbology/beta1/convert"

    CLOUD_CREDENTIALS = "/auth/cloud-credentials"

    PERM_ID_URL = BASE_URL + PERM_ID_DATA
    TOKEN_URL = BASE_URL + AUTH_CATEGORY + RDP_VERSION + TOKEN
    CLOUD_CREDENTIALS_URL = BASE_URL + CLOUD_CREDENTIALS + RDP_VERSION + "/"
    SUB_HEADLINES_URL = BASE_URL + SUBSCRIPTION_CATEGORY + RDP_VERSION + HEADLINE_SUBSCRIPTION
    SUB_STORIES_URL = BASE_URL + SUBSCRIPTION_CATEGORY + RDP_VERSION + STORIES_SUBSCRIPTION
    HEADLINES_URL = BASE_URL + NEWS_DATA + RDP_VERSION + HEADLINE_DATA
    STORY_URL = BASE_URL + NEWS_DATA + RDP_VERSION + STORIES_DATA

    @staticmethod
    def get_headlines_params(
        rics: Union[List[str], str],
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        limit: int = 100,
    ) -> Dict[str, Any]:
        if isinstance(rics, str):
            rics = [rics]

        query = " or ".join(rics) if len(rics) > 1 else rics[0]
        params = {"query": query, "sort": "newToOld", "limit": limit}

        if date_from:
            params["dateFrom"] = date_from.strftime("%Y-%m-%d")
        if date_to:
            params["dateTo"] = date_to.strftime("%Y-%m-%d")

        return params
