import boto3
from typing import Any, Callable


def get_ssm_parameter(
    parameter_name: str, decrypt: bool = False, region_name: str = "eu-west-1"
) -> str:
    ssm_client = boto3.client("ssm", region_name=region_name)
    parameter = ssm_client.get_parameter(Name=parameter_name, WithDecryption=decrypt)
    val: str = parameter["Parameter"]["Value"]
    return val


def counted(f) -> Callable:
    def wrapped(*args, **kwargs) -> Any:
        wrapped.calls += 1  # type: ignore
        return f(*args, **kwargs)

    wrapped.calls = 0  # type: ignore

    return wrapped
