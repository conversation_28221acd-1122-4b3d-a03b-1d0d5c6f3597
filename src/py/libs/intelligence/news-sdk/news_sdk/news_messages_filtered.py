import backoff
import httpx
import json
from botocore.exceptions import Client<PERSON>rror
from news_sdk.api_endpoints import RefinitivNewsAPI
from news_sdk.consume_sqs_events import SQSNewsConsumer
from news_sdk.helpers import get_ssm_parameter
from news_sdk.rdp_token import R<PERSON><PERSON>oken
from typing import Any, Dict, List, Optional


class NewsSubscription:
    def __init__(self, token_client: Optional[RDPToken] = None) -> None:
        api_credentials = json.loads(
            get_ssm_parameter(
                parameter_name="REFINITIV_NEWS_API_CREDENTIALS",
                decrypt=True,
            )
        )
        if token_client is None:
            self.token_client = RDPToken(credentials=api_credentials)
        else:
            self.token_client = token_client
        self.endpoint = json.loads(
            get_ssm_parameter(parameter_name="REFINITIV_NEWS_QUEUE_INFO", decrypt=True)
        )["endpoint"]

        self.cloud_credentials = self.get_cloud_credentials()
        self.sqs_consumer = SQSNewsConsumer(
            access_id=self.cloud_credentials["access_id"],
            secret_key=self.cloud_credentials["secret_key"],
            session_token=self.cloud_credentials["session_token"],
        )

    def subscribe_to_news(self, headlines: bool = False) -> Dict[str, str]:
        """

        :param headlines:
        :return:
        """
        resource_endpoint = (
            RefinitivNewsAPI.SUB_HEADLINES_URL if headlines else RefinitivNewsAPI.SUB_STORIES_URL
        )

        request_data = {
            "transport": {"transportType": "AWS-SQS"},
            "filter": {"type": "language", "value": "L:en"},
            "payloadVersion": "2.0",
        }

        headers = {
            "Authorization": "Bearer " + self.token_client.token,
            "Content-Type": "application/json",
        }

        resp = httpx.post(resource_endpoint, headers=headers, data=request_data)
        if resp.status_code != 200:
            raise ValueError(f"Unable to subscribe. Code {resp.status_code}, Message: {resp.text}")
        else:
            json_resp = resp.json()
            return {
                "endpoint": json_resp["transportInfo"]["endpoint"],
                "crypto_key": json_resp["transportInfo"]["cryptographyKey"],
                "subscription_id": json_resp["subscriptionID"],
            }

    @backoff.on_exception(backoff.expo, httpx.HTTPStatusError, max_tries=4)
    def get_cloud_credentials(self) -> Dict[str, str]:
        request_data = {"endpoint": self.endpoint}

        # get the latest access token
        headers = {"Authorization": "Bearer " + self.token_client.token}
        resp = httpx.get(
            RefinitivNewsAPI.CLOUD_CREDENTIALS_URL, headers=headers, params=request_data, timeout=45
        )
        resp.raise_for_status()

        json_resp = resp.json()
        return {
            "access_id": json_resp["credentials"]["accessKeyId"],
            "secret_key": json_resp["credentials"]["secretKey"],
            "session_token": json_resp["credentials"]["sessionToken"],
        }

    def start_news_subscription(self, headlines: bool = True) -> Dict[str, str]:
        return self.subscribe_to_news(headlines=headlines)

    def remove_message(self, receipt_handle: str, retry: bool = False) -> Any:
        try:
            return self.sqs_consumer.remove_single(
                receipt_handle,
                endpoint=self.endpoint,
            )
        except ClientError as e:
            if not retry:
                self.cloud_credentials = self.get_cloud_credentials()
                self.sqs_consumer = SQSNewsConsumer(
                    access_id=self.cloud_credentials["access_id"],
                    secret_key=self.cloud_credentials["secret_key"],
                    session_token=self.cloud_credentials["session_token"],
                )
                return self.remove_message(receipt_handle, retry=True)
            raise Exception(f"Cloud credentials expired, ClientError message: {e}")

    def batch_remove_message(self, entries: List[Dict[str, str]], retry: bool = False) -> Any:
        try:
            return self.sqs_consumer.remove_batch(endpoint=self.endpoint, entries=entries)
        except ClientError as e:
            if not retry:
                self.cloud_credentials = self.get_cloud_credentials()
                self.sqs_consumer = SQSNewsConsumer(
                    access_id=self.cloud_credentials["access_id"],
                    secret_key=self.cloud_credentials["secret_key"],
                    session_token=self.cloud_credentials["session_token"],
                )
                return self.batch_remove_message(entries=entries, retry=True)
            raise Exception(f"Cloud credentials expired, ClientError message: {e}")

    def batch_consume_messages(
        self, batch_size: int = 10, retry: bool = False
    ) -> List[Dict[str, Any]]:
        try:
            return self.sqs_consumer.retrieve_batch(
                endpoint=self.endpoint,
                batch_size=batch_size,
            )
        except ClientError as e:
            if not retry:
                self.cloud_credentials = self.get_cloud_credentials()
                self.sqs_consumer = SQSNewsConsumer(
                    access_id=self.cloud_credentials["access_id"],
                    secret_key=self.cloud_credentials["secret_key"],
                    session_token=self.cloud_credentials["session_token"],
                )
                return self.batch_consume_messages(retry=True)
            raise Exception(f"Cloud credentials expired, ClientError message: {e}")

    def get_attribs(self) -> Any:
        try:
            return self.sqs_consumer.get_attributes(
                self.endpoint,
            )
        except ClientError:
            return {}


if __name__ == "__main__":
    news = NewsSubscription()
    news.get_attribs().get("Attributes", {}).get("ApproximateNumberOfMessages", "UNKNOWN")
