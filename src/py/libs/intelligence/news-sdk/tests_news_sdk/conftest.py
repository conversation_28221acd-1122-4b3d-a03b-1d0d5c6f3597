import boto3
import httpx
import news_sdk.rdp_token
import pytest
from unittest.mock import MagicMock


@pytest.fixture(autouse=True)
def mock_httpx(monkeypatch):
    m = MagicMock()
    m.post.return_value = m
    m.status_code = 200
    monkeypatch.setattr(httpx, "get", m.get)
    monkeypatch.setattr(httpx, "put", m.put)
    monkeypatch.setattr(httpx, "post", m.post)
    return m


@pytest.fixture(autouse=True)
def mock_boto(monkeypatch):
    f = MagicMock()
    f.get_parameter.return_value = {
        "Parameter": {
            "Value": (
                '{"endpoint": "this is an endpoint", "cryptographyKey": "this is a crypto key"}'
            )
        }
    }

    def get_fake_client(*args, **kwargs):
        return f

    monkeypatch.setattr(
        boto3,
        "client",
        get_fake_client,
    )
    return f


@pytest.fixture(autouse=True)
def mock_boto_session(monkeypatch):
    f = MagicMock()
    f.client.return_value = f
    f.receive_message.return_value = {
        "Messages": [
            {
                "MessageId": "this is the message id",
                "ReceiptHandle": "this is the receipt handle",
                "MD5OfBody": "this is the md5 of the body",
                "Body": "this body is encrypted ooooo",
            }
        ]
    }

    def get_fake_session(*args, **kwargs):
        return f

    monkeypatch.setattr(
        boto3,
        "Session",
        get_fake_session,
    )
    return f


@pytest.fixture()
def mock_rdp_token(monkeypatch):
    t = MagicMock()

    def get_fake_rdp(*args, **kwargs):
        return t

    monkeypatch.setattr(news_sdk.rdp_token, "RDPToken", get_fake_rdp)
