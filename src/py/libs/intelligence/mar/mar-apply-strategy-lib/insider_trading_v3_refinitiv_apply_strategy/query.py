# type: ignore
# ruff: noqa: E501
import addict
import datetime
import logging
import pandas as pd
from insider_trading_v3_refinitiv_apply_strategy.static import (
    ThresholdsNames,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.query_utils import (
    select_instrument_for_ric_fetch,
)
from mar_utils.auditor.strategy_audits.insider_trading_v3_refinitiv_audit import (
    InsiderTradingV3RefinitivAuditName,
)
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.query.srp.positions import PositionsQuery
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.query.utils import (
    remove_date_range_from_iris_filters,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.repository.news.refinitiv_news import (
    get_refinitiv_client,
)
from market_abuse_algorithms.data_source.static.news.refinitiv_news import (
    RefinitivNewsRelevance,
)
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField, OrderStatus
from market_abuse_algorithms.data_source.static.srp.positions import PositionsField
from market_abuse_algorithms.data_source.static.utility import DFColumns
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.errors import NoAvailableDataToAnalyse
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.utils.dates import (
    add_date_range_filter,
    get_order_state_date_range,
)
from market_abuse_algorithms.utils.filters import filter_non_business_days
from market_abuse_algorithms.utils.processment import (
    new_instrument_ric_map,
    process_executions_results,
)
from se_elastic_schema.components.mar.strategy.insider_trading_v3.thresholds import (
    EventCreation,
)
from se_elastic_schema.static.mar_audit import StepType
from typing import Dict, List, Optional, Tuple, Union

INSTRUMENTS_INCLUDE_FIELDS = [
    OrderField.INST_ID_CODE,
    OrderField.INST_CLASSIFICATION,
    OrderField.INST_EXT_UNDER_INST_INST_CODE,
    OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
    OrderField.INST_EXT_UNIQUE_IDENT,
    OrderField.TRX_DTL_PC_CCY,
    OrderField.INST_VENUE,
    NewColumns.VENUE,
]

INCLUDE_OBSERVATION_BEHAVIOUR_FIELDS = [
    *INSTRUMENTS_INCLUDE_FIELDS,
    OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE,
    OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE,
    OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY,
    OrderField.BEST_EXC_DATA_ORD_VOL,
    OrderField.BEST_EXC_DATA_TRX_VOL,
    OrderField.CLIENT_FILE_IDENTIFIER,
    OrderField.EXC_DTL_BUY_SELL_IND,
    OrderField.INST_DERIV_OPTION_TYPE,
    OrderField.INST_DERIV_PRICE_MULTIPLIER,
    OrderField.INST_DERIV_STRIKE_PRICE,
    OrderField.META_KEY,
    OrderField.META_PARENT,
    OrderField.PC_FD_PRICE,
    OrderField.PC_FD_TRD_QTY,
    OrderField.PC_FD_INIT_QTY,
    OrderField.TRADER_FILE_IDENTIFIER,
    OrderField.TRD_ALGO_FIRM_DESKS_ID,
    OrderField.TRX_DTL_PC_NOTATION,
    OrderField.TRX_DTL_QUANTITY_NOTATION,
    OrderField.TS_TRADING_DATE_TIME,
    OrderField.TRD_ALGO_FILE_IDENTIFIER,
    OrderField.BEST_EXC_DATA_ORD_VOL_ECB_REF_RATE,
]

POSITIONS_REQUIRED_FIELDS = [
    PositionsField.AMOUNT,
    PositionsField.DATE,
    PositionsField.DIRECTION,
    PositionsField.LEVEL,
    PositionsField.PARTIES,
]

POSITIONS_INCLUDE_FIELDS = [
    OrderField.META_TIMESTAMP,
    PositionsField.AMOUNT,
    PositionsField.DATE,
    PositionsField.AMOUNT_NATIVE_CURRENCY,
    PositionsField.DIRECTION,
    PositionsField.LEVEL,
    PositionsField.SOURCE_INDEX,
    PositionsField.SOURCE_KEY,
    PositionsField.PARTIES,
    PositionsField.PARTIES_TYPE,
    PositionsField.PARTIES_VALUE,
    PositionsField.PARTIES_VALUE_ID,
    PositionsField.PARTIES_VALUE_NAME,
    PositionsField.PARTIES_VALUE_KEY,
    PositionsField.PARTIES_VALUE_PROFESSIONAL,
    PositionsField.QUANTITY,
    PositionsField.QUANTITY_NOTATION,
    OrderField.INST_EXT_UNIQUE_IDENT,
    OrderField.INST_ID_CODE,
]

REQUIRED_FIELDS = [
    OrderField.META_KEY,
    OrderField.PC_FD_PRICE,
    OrderField.PC_FD_TRD_QTY,
    OrderField.TS_TRADING_DATE_TIME,
    OrderField.TRX_DTL_PC_CCY,
]


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)
        self._th_currency_filter: float = self.context.thresholds.dict().get(
            ThresholdsNames.CURRENCY_FILTER
        )
        self._th_event_creation: float = self.context.thresholds.dict().get(
            ThresholdsNames.EVENT_CREATION
        )
        self._th_intraday_surveillance: Optional[bool] = self.context.thresholds.dict().get(
            ThresholdsNames.INTRADAY_SURVEILLANCE
        )
        if self._th_event_creation != EventCreation.MARKET_DATA:
            self._th_news_feed_event_news_relevance: RefinitivNewsRelevance = (
                self.context.thresholds.dict().get(ThresholdsNames.NEWS_FEED_EVENT_NEWS_RELEVANCE)
            )
            self._th_news_feed_event_news_subject: Optional[List[str]] = (
                self.context.thresholds.dict().get(ThresholdsNames.NEWS_FEED_EVENT_NEWS_SUBJECT)
            )

            self.refinitiv_news_client = get_refinitiv_client(
                relevance=self._th_news_feed_event_news_relevance
            )
            self._refinitiv_subjects = self.get_news_subjects()

        self._market_data_client = get_market_client(tenant=context.tenant)

        self._filters = remove_date_range_from_iris_filters(filters=self.context.filters)

        self._logger = logging.getLogger(__name__)

    def get_cases_to_analyse(
        self, date_range_filter: addict.Dict, event_day: Optional[pd.Timestamp] = None
    ):
        """
        Fetched the instrument list to be used in the algo
        :return: Yields list of instruments (strings)
        """
        query_executions_to_get_instruments: OrderExecutionsQuery = (
            self._create_order_executions_query(
                date_day_range=date_range_filter, instruments_query=True
            )
        )
        used_instruments = set()
        try:
            for batch in self._sdp_repository.search_after_query_yield_dataframe(
                query_executions_to_get_instruments
            ):
                if batch.empty:
                    self._logger.info(
                        "This batch of instruments retrieved from ES is empty. Continuing to the next batch"
                    )
                    continue

                start, method_id = self.get_start_time_and_unique_id()
                instrument_list_to_checked = list(
                    set(batch[NewColumns.INSTRUMENT_CODE].dropna().tolist())
                )
                self._total_records_analyzed += batch.shape[0]

                batch = process_executions_results(
                    raw_query_df=batch,
                    initial_query=True,
                )

                if batch.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_instruments=instrument_list_to_checked,
                            reason=f"No instruments to be analysed after process_executions_results. "
                            f"Event day {event_day}",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Filtering the instruments based on if it derivative or non-derivative",
                            step_type=StepType.FETCH_TENANT_DATA,
                        )
                    )
                    self._logger.info(
                        "No instruments to be analysed after"
                        "filtering the instruments based on if it derivative or non-derivative."
                        "Method applied: process_executions_results"
                    )
                    continue

                batch = batch.drop_duplicates(subset=DFColumns.INSTRUMENT)

                start, method_id = self.get_start_time_and_unique_id()
                # mask to check which instruments in the batch we already processed previously
                used_instrument_mask = batch[DFColumns.INSTRUMENT].isin(used_instruments)
                # exclude instruments we already processed
                batch = batch[~used_instrument_mask]

                if batch.empty:
                    self._logger.info(
                        "No instruments to be analysed after removing the ones already surveilled."
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_instruments=instrument_list_to_checked,
                            reason=f"No instruments to be analysed after removing the ones already surveilled. "
                            f"Event day {event_day}",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="No instruments to be analysed after removing the ones already surveilled",
                            step_type=StepType.FETCH_TENANT_DATA,
                        )
                    )
                    continue

                instruments_ids: List[str] = batch.apply(
                    select_instrument_for_ric_fetch, axis=1
                ).tolist()

                start, method_id = self.get_start_time_and_unique_id()

                instrument_id_ric_mapping: Union[dict, None] = self._market_data_client.get_ric_map(
                    list_inst_unique_id=instruments_ids, use_prefix_query=True
                )

                if instrument_id_ric_mapping is None:
                    self._logger.info(
                        f"The event creation requires Market Data and none of instruments {instruments_ids}"
                        f" have a RIC mapped to."
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_instruments=instruments_ids,
                            reason=f"No RIC mapped for all instruments {instruments_ids}. Event day {event_day}",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Getting RICs for instruments",
                            step_type=StepType.MARKET_DATA_RIC,
                        )
                    )
                    continue

                instrument_ric_map = new_instrument_ric_map(
                    instruments_ids=[inst for inst in instruments_ids if pd.notna(inst)],
                    instrument_id_ric_mapping=instrument_id_ric_mapping,
                )

                batch[DFColumns.RIC] = batch[OrderField.INST_EXT_UNIQUE_IDENT].map(
                    dict(instrument_ric_map)
                )

                empty_rics_mask = batch[DFColumns.RIC].isna()

                batch.loc[empty_rics_mask, DFColumns.RIC] = batch[DFColumns.INSTRUMENT].map(
                    dict(instrument_ric_map)
                )

                used_instruments = used_instruments.union(
                    batch[DFColumns.INSTRUMENT][~used_instrument_mask].values
                )

                yield batch

        except NoAvailableDataToAnalyse:
            start_ts = date_range_filter.get(DateRangeParameters.START).isoformat()
            end_ts = date_range_filter.get(DateRangeParameters.END).isoformat()
            self._logger.info(f"No data was found between {start_ts} and {end_ts}", exc_info=True)

            yield pd.DataFrame()

    def _create_order_executions_query(
        self,
        date_day_range: addict.Dict,
        instrument_list: Optional[List[str]] = None,
        instruments_query: bool = False,
    ) -> Optional[OrderExecutionsQuery]:
        """order states query for behaviour and observation periods.

        :param instrument_list: list of instruments to be fetched
        :param date_day_range: dict, with start/end date to fetch data
        :param instruments_query: bool, to check if we need to use the INSTRUMENTS_INCLUDE_FIELDS list or the
        INCLUDE_OBSERVATION_BEHAVIOUR_FIELDS, basically if we want the query to get the instruments or the query to
        get the executions
        :return: query for instruments or the query to get the executions
        """

        if not date_day_range.get(DateRangeParameters.START) or not date_day_range.get(
            DateRangeParameters.END
        ):
            self._logger.info(
                "No dates in event range dict, going to drop as we don't date infoto proceed."
            )

            return None

        include_fields = (
            INSTRUMENTS_INCLUDE_FIELDS
            if instruments_query
            else INCLUDE_OBSERVATION_BEHAVIOUR_FIELDS
        )

        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.includes(include_fields)

        self.add_default_conditions_to_query(query)

        # We remove the date range in the beginning to avoid duplicate use and only add it when necessary and
        # we know that  there isn't any duplication of filters, which is the case here.

        query.add_iris_filters(add_date_range_filter(date_day_range=date_day_range))

        for field in REQUIRED_FIELDS:
            query.exists(field=field)

        query.instrument_id(instrument_list, check_underlying=True)

        return query

    def get_order_states(
        self,
        instrument_list: List[str],
        event_day: pd.Timestamp,
        observation_period: int,
        behaviour_period: Optional[int] = None,
        method_id: Optional[str] = None,
    ) -> pd.DataFrame:
        """Generates query, fetches and processes order states for behaviour
        and observation periods.

        :param method_id: uuid to identify the method on the audit
        :param instrument_list: list of required instruments
        :param event_day: timestamp with event day
        :param behaviour_period: threshold defined by the user
        :param observation_period: threshold defined by the user
        :return: processed order states records
        """

        orders_state_date_range: addict.Dict = get_order_state_date_range(
            event_day=event_day,
            observation_period=observation_period,
            behaviour_period=behaviour_period,
            intraday_surveillance=self._th_intraday_surveillance,
        )

        query: OrderExecutionsQuery = self._create_order_executions_query(
            instrument_list=instrument_list, date_day_range=orders_state_date_range
        )

        if query is None:
            return pd.DataFrame()

        executions_data: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        executions_data_processed: pd.DataFrame = process_executions_results(
            raw_query_df=executions_data, method_id=method_id
        )

        if executions_data_processed.empty:
            return pd.DataFrame()

        executions_data_filtered: pd.DataFrame = filter_non_business_days(
            date_col=OrderField.TS_TRADING_DATE_TIME, data=executions_data_processed
        )

        return executions_data_filtered

    def get_executions_parents(
        self,
        executions_to_search_with: pd.DataFrame,
        event_day: Optional[pd.Timestamp] = None,
        method_id: Optional[str] = None,
    ) -> Tuple[pd.DataFrame, str | None, dict | None]:
        """With the parent_ids get the parent meta_keys.

        :param event_day: event day to log in step audit
        :param method_id: uuid to identify the method on the audit
        :param executions_to_search_with: dataframe with executions
        :return: Dict for each parent id the correspondent key
        """
        audit_data = dict(event_day=event_day)
        if OrderField.META_PARENT not in executions_to_search_with.columns:
            audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_ID_MISSING
            self._logger.info(
                f"Executions dataframe doesn't have the field {OrderField.META_PARENT}, so we can't "
                f"proceed to exclude any order based on the parent IDs. Event day {event_day}",
                exc_info=True,
            )
            return pd.DataFrame(), audit_key, audit_data

        parent_ids: List[str] = (
            executions_to_search_with.loc[:, OrderField.META_PARENT].dropna().unique().tolist()
        )

        if not parent_ids:
            audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_ID_MISSING
            self._logger.info("No parent IDs where provided to be queried", exc_info=True)
            return pd.DataFrame(), audit_key, audit_data

        query = OrderQuery()

        query.add_condition(mode="filter", conditions=[{"terms": {OrderField.META_ID: parent_ids}}])

        include_fields = [
            OrderField.META_ID,
            OrderField.META_KEY,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TS_ORD_UPDATED,
        ]

        query.includes(include_fields)

        results = self._sdp_repository.search_after_query(query=query)

        if results.empty:
            audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_MISSING
            self._logger.info(
                f"There are no parents for this given set of executions. Event day {event_day}",
                exc_info=True,
            )
            return pd.DataFrame(), audit_key, audit_data

        return results, None, None

    def get_market_data_by_instrument_or_ric(
        self, instrument: Optional[str] = None, mapped_ric: Optional[str] = None
    ) -> pd.DataFrame:
        """Get market data.

        :param instrument: str
        :param mapped_ric: str, ric from a given instrument

        :return:
        """
        if instrument is None and mapped_ric is None:
            self._logger.error(
                "Instrument and RIC are None, so we are not able to get market data."
                "Method: get_market_data_by_instrument_or_ric"
            )
            return pd.DataFrame()

        try:
            market_data_stats: pd.DataFrame = self._market_data_client.get_market_data_stats(
                instrument_unique_identifier=instrument,
                instrument_ric=mapped_ric,
                start_date=self.look_back_period_ts,
                end_date=self.market_data_end_date,
            )
            return market_data_stats
        except Exception as e:
            self._logger.error(f"Fetching of market data states failed with the error: {e}")
            return pd.DataFrame()

    def get_positions_record(
        self,
        instrument_id: str,
        currency: str,
        evaluation_type: Optional[dict],
        behaviour_period: Dict,
    ) -> pd.DataFrame:
        """

        :param behaviour_period: dict with behaviour period, start & end
        :param instrument_id: string with the instrument ID
        :param currency: string with the filter currency
        :param evaluation_type: dict with the evaluation type & value of it. It only has 1 eval type (or should)
        example -> {"Trader": "Trader 1"}
        :return:
        """

        behaviour_period_last_day = behaviour_period.get(DateRangeParameters.END)

        if behaviour_period_last_day is None:
            self._logger.info(
                f"We don't have the last day of observation period. Here is the "
                f"observation period {behaviour_period}."
                f" No position model to retrieve."
            )
            return pd.DataFrame()

        position_query: PositionsQuery = self.get_position_query(
            instrument_id=instrument_id,
            currency=currency,
            evaluation_type=evaluation_type,
            behaviour_last_day=behaviour_period_last_day,
        )

        if position_query is None:
            return pd.DataFrame()

        results: pd.DataFrame = self._sdp_repository.search_after_query(query=position_query)
        return results

    def get_position_query(
        self,
        instrument_id: str,
        currency: str,
        evaluation_type: Optional[dict],
        behaviour_last_day: pd.Timestamp,
    ) -> Optional[PositionsQuery]:
        """Get positions query.

        :param instrument_id:
        :param currency:
        :param evaluation_type:
        :param behaviour_last_day:
        :return:
        """
        position_query = PositionsQuery()
        position_query.isin(instrument=instrument_id)
        position_query.currency(currency=currency)

        try:
            behaviour_period_last_day_date = behaviour_last_day.date()
        except Exception as e:
            self._logger.info(
                f"No possible to convert the last day of observation period which is a timestamp object to date."
                f" So we  don't have the last day of observation period."
                f" Error: {e}"
                f" No position model to retrieve."
            )
            return None

        position_query.observation_day(date=behaviour_period_last_day_date)

        if POSITIONS_REQUIRED_FIELDS:
            for field in POSITIONS_REQUIRED_FIELDS:
                position_query.exists(field=field)

        position_query.includes(POSITIONS_INCLUDE_FIELDS)

        if evaluation_type is None:
            self._logger.info(
                "Evaluation type is None, so we won't be using it for the Positions search."
            )
            return position_query

        position_query.evaluation_type(evaluation_type=list(evaluation_type.keys())[0])

        position_value: str = list(evaluation_type.values())[0]

        if ":" in position_value:
            position_value: str = position_value.split(":")[1]

        position_query.evaluation_type_value(evaluation_type_value=position_value)
        return position_query

    # TODO Finish the method when file is available
    def get_sector_index_stats(self, instrument: str) -> pd.DataFrame:
        sector_index_stats = self._market_data_client.get_trade_surveillance_data("foo.csv")
        return sector_index_stats

    def get_news_subjects(self) -> Dict:
        """Queries the available news subjects on refinitiv and removes the
        subjects not available.

        Returns a dict with a key saying if we want all subjects present
        in refinitiv and another key with the
        """
        refinitiv_subjects = self.refinitiv_news_client.get_refinitiv_subjects()

        if self._th_news_feed_event_news_subject is None:
            return {"all_subjects": True, "subjects": refinitiv_subjects}

        sources_to_use: List[str] = [
            source
            for source in self._th_news_feed_event_news_subject
            if source in refinitiv_subjects
        ]

        if set(self._th_news_feed_event_news_subject) == set(refinitiv_subjects):
            return {"all_subjects": True, "subjects": refinitiv_subjects}

        return {"all_subjects": False, "subjects": sources_to_use}

    def fetch_refinitiv_news(
        self,
        ric: str,
        date_range: Dict,
        orders_per_instrument: List,
        method_id_audit: str | None = None,
    ):
        """Get the refinitiv news based on ric; date and sources.

        :param orders_per_instrument:
        :param ric:
        :param date_range:
        :param method_id_audit:
        :return:
        """
        all_subjects: bool = self._refinitiv_subjects.get("all_subjects")  # type: ignore
        subjects: List = self._refinitiv_subjects.get("subjects", [])

        return self.refinitiv_news_client.get_refinitiv_news(
            ric=ric,
            start_date=date_range.get("start"),
            end_date=date_range.get("end"),
            all_sources=True,
            method_id=method_id_audit,
            all_subjects=all_subjects,
            orders_for_ric=orders_per_instrument,
            subjects=subjects,
            update_australian_tz_offset=True,
        )

    @property
    def total_records_analyzed(self):
        return self._total_records_analyzed
