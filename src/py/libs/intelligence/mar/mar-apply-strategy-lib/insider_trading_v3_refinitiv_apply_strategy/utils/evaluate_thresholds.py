# type: ignore
# ruff: noqa: E501
import addict
import logging
import pandas as pd
from insider_trading_v3_refinitiv_apply_strategy.static import (
    EventDirection,
    EventResultDict,
)
from mar_utils.auditor.strategy_audits.insider_trading_v3_refinitiv_audit import (
    InsiderTradingV3RefinitivAuditName,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.srp.positions import PositionsField
from market_abuse_algorithms.data_source.static.utility import CommonColumns, PNLColumns
from market_abuse_algorithms.utils import formulas
from se_market_data_utils.schema.parquet import EoDStatsColumns
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


def check_event_daily_variation(
    market_data_day: pd.DataFrame,
    market_data_day_minus_one: pd.DataFrame,
    market_data_event_daily_vol_variation_th,
    orders_data_per_instrument: list,
    method_id: Optional[str] = None,
    instrument: Optional[str] = None,
) -> bool:
    """Check if volume variation is lower then the threshold
    daily_volume_variation.

    :param orders_data_per_instrument:
    :param instrument:
    :param method_id: uuid to identify the method on the audit
    :param market_data_day:
    :param market_data_day_minus_one:
    :param market_data_event_daily_vol_variation_th:
    :return:
    """
    volume_event_day: float = market_data_day.loc[:, EoDStatsColumns.TRADE_VOLUME].values[0]
    volume_event_day_minus_1: float = market_data_day_minus_one.loc[
        :, EoDStatsColumns.TRADE_VOLUME
    ].values[0]

    volume_variation: float = (
        abs(volume_event_day - volume_event_day_minus_1) / volume_event_day_minus_1
    )

    if volume_variation < market_data_event_daily_vol_variation_th:
        return False

    return True


def check_confidence_interval(
    price_variations: pd.Series,
    close_price_variation: float,
    price_variation_mkt_data: float,
    method_id: Optional[str] = None,
    instrument: Optional[str] = None,
) -> Dict:
    """evaluate confidence interval.

    :param instrument:
    :param method_id: uuid to identify the method on the audit
    :param price_variations:
    :param close_price_variation:
    :param price_variation_mkt_data:
    :return:
    """

    confidence_interval = formulas.calculate_confidence_interval(
        value_series=price_variations,
        alpha=price_variation_mkt_data,
    )

    if (
        confidence_interval.get(DateRangeParameters.START)
        <= close_price_variation
        <= confidence_interval.get(DateRangeParameters.END)
    ):
        return {}

    return confidence_interval


def check_direction_condition(
    direction: str,
    price_variation: float,
    interval: dict,
    method_id: Optional[str] = None,
    instrument: Optional[str] = None,
) -> bool:
    """Checks if direction and the close_price_variation are in the same
    orientation.

    :param instrument:
    :param interval:
    :param price_variation:
    :param method_id: uuid to identify the method on the audit
    :param direction: str, value of the direction (up or down)
    :return: bool returns false if not in the same orientation
    """
    if (
        direction == EventDirection.UP
        and not price_variation > interval.get(DateRangeParameters.END)
        and interval.get(DateRangeParameters.END) > 0
    ) or (
        direction == EventDirection.DOWN
        and not price_variation < interval.get(DateRangeParameters.START)
        and interval.get(DateRangeParameters.START) < 0
    ):
        return False
    else:
        return True


def check_net_trade_amount_threshold(
    segment_amount_list: List,
    observation_period_amount: float,
    activity_allowed_range_from_profile_th: float,
    list_of_order_ids: List = [],
    list_of_instruments: List = [],
    method_id: str | None = None,
    event_day: Optional[pd.Timestamp] = None,
) -> Dict:
    """calculate the behavior confidence interval based on the alpha threshold
    and if the observation period is within the range.

    :param event_day: event day to log in StepAudit
    :param method_id: uuid for StepAudit
    :param list_of_order_ids: list of orders for StepAudit
    :param list_of_instruments: list of instruments for StepAudit
    :param segment_amount_list: list of segments
    :param observation_period_amount: float
    :param activity_allowed_range_from_profile_th: activity_allowed_range_from_profile threshold
    :return: dict with behaviour confidence interval
    """
    behaviour_confidence_interval: Dict = formulas.calculate_confidence_interval(
        value_series=pd.Series(segment_amount_list),
        alpha=activity_allowed_range_from_profile_th,
    )

    if (
        behaviour_confidence_interval.get(DateRangeParameters.START)
        <= observation_period_amount
        <= behaviour_confidence_interval.get(DateRangeParameters.END)
    ):
        return {}

    return behaviour_confidence_interval


def check_minimum_trade_amount(
    order_states_data: pd.DataFrame,
    observation_date_range: Dict,
    behaviour_period_segments: List[Dict],
    min_trade_amount_threshold: int,
    method_id: Optional[str] = None,
    event_day: Optional[pd.Timestamp] = None,
) -> addict.Dict | Tuple[addict.Dict, str | None, Dict | None]:
    """Calculates the net trade amount of the observation period and behaviour
    period segments After check if it passes the min_trade_amount_threshold.

    :param event_day: event day to log in StepAudit
    :param method_id: uuid to identify the method on the audit
    :param behaviour_period_segments: list of dicts with timestamps for each behaviour period segment
    :param observation_date_range: date range of observation period
    :param order_states_data: dataframe with amounts for each trade
    :param min_trade_amount_threshold: activity minimum trade amount threshold

    :return: tuple with first value being the net amount of the observation period and a list with net amount of behaviour segments
    """

    result: addict.Dict = formulas.calculate_net_trade_amount_periods(
        order_states_data=order_states_data,
        observation_date_range=observation_date_range,
        behaviour_period_segments=behaviour_period_segments,
    )

    if result.get(CommonColumns.OBSERVATION_PERIOD_AMOUNT) is None:
        audit_key = InsiderTradingV3RefinitivAuditName.OBSERVATION_PERIOD_TRADE_AMOUNT
        audit_data = dict(event_day=event_day)
        logger.info(f"The observation period trade amount is None. Event day {event_day}")
        if method_id:
            return addict.Dict({})
        return addict.Dict({}), audit_key, audit_data

    if abs(result.get(CommonColumns.OBSERVATION_PERIOD_AMOUNT)) < min_trade_amount_threshold:
        audit_key = InsiderTradingV3RefinitivAuditName.OBSERVATION_PERIOD_TRADE_BELOW_THRESHOLD
        audit_data = dict(
            event_day=event_day,
            amount=abs(result.get(CommonColumns.OBSERVATION_PERIOD_AMOUNT)),
            threshold_amount=min_trade_amount_threshold,
        )
        logger.info(
            "The observation period trade amount is below the minimum trade threshold. Event day {event_day}"
        )
        if method_id:
            return addict.Dict({})
        return addict.Dict({}), audit_key, audit_data

    if behaviour_period_segments and not result.get(CommonColumns.SEGMENT_AMOUNT_LIST):
        # If behavior period is null as well, we shouldn't return an empty dict
        # But if behavior period is not null, but result doesn't have any segment amount,
        # we should return an empty dict'
        audit_key = InsiderTradingV3RefinitivAuditName.BEHAVIOUR_DATA_MISSING
        audit_data = dict(event_day=event_day)
        logger.info(f"No data the behaviour period. Event day {event_day}", exc_info=True)
        if method_id:
            return addict.Dict({})
        return addict.Dict({}), audit_key, audit_data
    if method_id:
        return addict.Dict(result)
    return addict.Dict(result), None, None


def calculate_and_check_pnl(
    observation_data: pd.DataFrame,
    event_day_close_price: float,
    filter_currency: str,
    pnl_threshold: int,
    data_currency: str,
    method_id: Optional[str] = None,
) -> Tuple[pd.DataFrame, str | None, str | None]:
    """Select price, calculates PNL and checks against the threshold.

    :param filter_currency: filter currency set up by the user
    :param observation_data: dataframe with executions for the observation period
    :param event_day_close_price: market close price for the event day
    :param market_data_currency: market data currency
    :param pnl_threshold: minimum PNL threshold
    :param method_id: uuid to identify the method on the audit

    :return: dataframe with PNL if above threshold or empty dataframe with below threshold
    """
    if method_id:
        pnl_result = formulas.calculate_final_pnl(
            observation_data=observation_data,
            event_day_close_price=event_day_close_price,
            filter_currency=filter_currency,
            data_currency=data_currency,
            method_id=method_id,
        )
    else:
        pnl_result, audit_key, audit_data = formulas.calculate_final_pnl(
            observation_data=observation_data,
            event_day_close_price=event_day_close_price,
            filter_currency=filter_currency,
            data_currency=data_currency,
            method_id=method_id,
        )

    pnl_value = pnl_result[PNLColumns.PNL_VALUE].unique().tolist()[0]

    if pnl_threshold is not None:
        if pd.isna(pnl_value):
            return pd.DataFrame(), audit_key, audit_data
        if pnl_value <= 0 or pnl_value <= pnl_threshold:
            audit_key = InsiderTradingV3RefinitivAuditName.PNL_BELOW_THRESHOLD
            audit_data = dict(pnl=pnl_value, threshold=pnl_threshold)
            logger.info(
                f"The group didn't pass the PNL check."
                f" PNL Value: {pnl_value}"
                f" PNL Threshold: {pnl_threshold}"
            )
            if method_id:
                return pd.DataFrame()
            return pd.DataFrame(), audit_key, audit_data
    if method_id:
        return pnl_result
    return pnl_result, None, None


def analyse_event_direction(
    market_data_events,
    news_feed_events,
    behaviour_series,
    observation_amount: float,
    events_direction: List,
    event_direction_default: Dict,
) -> Dict:
    """Check the event direction based on market data/news feed events;
    behaviour and observation values.

    :param market_data_events: list of market data events
    :param news_feed_events: list of news feed events
    :param behaviour_series: series with the values of net trade amount for each behaviour segment
    :param observation_amount: value of the net trade amount for the observation period
    :param events_direction: list of events direction
    :param event_direction_default: default result dict to be returned in case no alerts should be generated
    :return:
    """
    if len(events_direction) > 1:
        logger.info(
            "Multiple directions were found in the events, using direction of the first market data event",
            exc_info=True,
        )
        if market_data_events:
            event_direction = market_data_events[0].get_direction()
        else:
            event_direction = news_feed_events[0].get_direction()

    else:
        event_direction = events_direction[0]

    event_direction_default[EventResultDict.EVENT_DIRECTION] = event_direction

    if event_direction == EventDirection.UP:
        if observation_amount <= 0:
            logger.info(
                "Observation amount must be positive when event impact is positive",
                exc_info=True,
            )
            event_direction_default[EventResultDict.MESSAGE] = EventResultDict.EVENT_UP_OBS_DOWN

            return event_direction_default
        if not behaviour_series.empty and observation_amount < behaviour_series.mean():
            logger.info(
                "Observation amount must be larger than the behaviour mean",
                exc_info=True,
            )
            event_direction_default[EventResultDict.MESSAGE] = (
                EventResultDict.EVENT_UP_OBS_DOWN_MEAN
            )

            return event_direction_default

    if event_direction == EventDirection.DOWN:
        if observation_amount >= 0:
            logger.info(
                "Observation amount must be negative when event impact is negative",
                exc_info=True,
            )
            event_direction_default[EventResultDict.MESSAGE] = EventResultDict.EVENT_DOWN_OBS_UP

            return event_direction_default
        if not behaviour_series.empty and observation_amount > behaviour_series.mean():
            logger.warning(
                "Observation amount must be larger than the behaviour mean",
                exc_info=True,
            )
            event_direction_default[EventResultDict.MESSAGE] = (
                EventResultDict.EVENT_DOWN_OBS_UP_MEAN
            )

            return event_direction_default

    event_direction_default[EventResultDict.CREATE_ALERT] = True
    event_direction_default[EventResultDict.MESSAGE] = EventResultDict.AlERT_CREATED

    return event_direction_default


def check_net_trade_out_of_allowed_range(
    behaviour_confidence_interval: Dict[str, float],
    observation_period_amount: float,
) -> bool:
    """checks if the Net Traded Value in the Observation is out of the Allowed
    Traded Amount Range.

    :param behaviour_confidence_interval: dict
    :param observation_period_amount: float
    :return: bool returns true if out of allowed range
    """

    lower_bound = behaviour_confidence_interval.get(DateRangeParameters.START)
    upper_bound = behaviour_confidence_interval.get(DateRangeParameters.END)

    if pd.isna(lower_bound) or pd.isna(upper_bound):
        return True

    if (observation_period_amount > 0 and observation_period_amount > upper_bound) or (
        observation_period_amount < 0 and observation_period_amount < lower_bound
    ):
        return True

    return False


def global_pnl_evaluation(
    data_in_analyse: pd.DataFrame,
    event_day_close_price: float,
    event_day_close_price_converted: float,
    market_data_currency: str,
    observation_period_range: Dict,
    data_in_obs_period: pd.DataFrame,
    filter_currency: str,
    pnl_threshold: int,
    method_id: Optional[str] = None,
    event_day: Optional[pd.Timestamp] = None,
) -> Tuple[Dict | None, str | None, str | None]:
    """Evaluate PNL, return Dict w values (close price, PNL dataset) or None.

    :param event_day: event day to log in StepAudit
    :param method_id: uuid to identify the method on the audit
    :param pnl_threshold: int, minimum PNL threshold
    :param filter_currency:  str, currency filter
    :param data_in_obs_period: dataframe, dataset within the obs period
    :param observation_period_range: dict, with start & end of observation period
    :param data_in_analyse: pd.Dataframe, group dataset with order state value
    :param event_day_close_price: float, close price of event day
    :param event_day_close_price_converted: float, close price converted to order currency
    :param market_data_currency: str, currency of market data
    :return: Dict, close price & PNL dataset
    """
    order_currency: str = data_in_analyse[OrderField.TRX_DTL_PC_CCY].unique().tolist()[0]

    if market_data_currency == order_currency:
        close_price_to_use = event_day_close_price
    else:
        close_price_to_use = event_day_close_price_converted
    if method_id:
        observation_data_pnl = calculate_and_check_pnl(
            observation_data=data_in_obs_period,
            event_day_close_price=close_price_to_use,
            filter_currency=filter_currency,
            data_currency=order_currency,
            pnl_threshold=pnl_threshold,
            method_id=method_id,
        )
    else:
        observation_data_pnl, audit_key, audit_data = calculate_and_check_pnl(
            observation_data=data_in_obs_period,
            event_day_close_price=close_price_to_use,
            filter_currency=filter_currency,
            data_currency=order_currency,
            pnl_threshold=pnl_threshold,
            method_id=method_id,
        )

    if observation_data_pnl.empty:
        logger.info(
            f"The data between the times {observation_period_range.get(DateRangeParameters.START)} "
            f"and {observation_period_range.get(DateRangeParameters.END)} "
            f"didn't pass the PNL threshold of {pnl_threshold}. Event day {event_day}"
        )
        if method_id:
            return None
        return None, audit_key, audit_data

    pnl_value = observation_data_pnl[PNLColumns.PNL_VALUE].unique().tolist()[0]

    data_in_analyse[PNLColumns.PNL_VALUE] = pnl_value
    data_in_analyse[PNLColumns.PNL_VALUE_ORIGINAL] = (
        observation_data_pnl[PNLColumns.PNL_VALUE_ORIGINAL].unique().tolist()[0]
    )
    pnl_eval = {
        "close_price": event_day_close_price,
        "close_price_converted": event_day_close_price_converted,
        "pnl": data_in_analyse,
    }
    if method_id:
        return pnl_eval
    return pnl_eval, None, None


def calculate_and_check_relative_amount(
    position_data: pd.DataFrame,
    relative_threshold: float,
    currency_threshold: str,
    instrument_id: str,
    currency_original: str,
    evaluation_type_val: str,
    observation_net_value: float,
    method_id: Optional[str] = None,
    event_day: Optional[pd.Timestamp] = None,
) -> Tuple[Optional[Tuple], str | None, Dict | None]:
    """Calculate the relative amount.

    :param event_day: event day to log in StepAudit
    :param position_data: position record
    :param relative_threshold:
    :param method_id: uuid to identify the method on the audit
    :param currency_threshold:
    :param instrument_id:
    :param currency_original:
    :param evaluation_type_val:
    :param observation_net_value: float
    :return: Tuple, relative threshold calculated/positions record
    """
    if PositionsField.DIRECTION not in position_data.columns:
        audit_key = InsiderTradingV3RefinitivAuditName.POSITIONS_COLUMN_MISSING
        audit_data = dict(event_day=event_day, field=PositionsField.DIRECTION)
        logger.info(
            f"There isn't any Direction in the Position model for ISIN - {instrument_id} -, "
            f" currency - {currency_original} -,"
            f" and evaluation type {evaluation_type_val}. Event day {event_day}"
            f" Skipping this threshold result."
        )
        return (
            (pd.NA, pd.NA, pd.NA),
            audit_key,
            audit_data,
        )

    amount_currency: str = PositionsField.get_amount_ccy(currency=currency_threshold)

    if amount_currency not in position_data.columns:
        audit_key = InsiderTradingV3RefinitivAuditName.POSITIONS_COLUMN_MISSING
        audit_data = dict(event_day=event_day, field=amount_currency)
        logger.info(
            f"There isn't any Amount field in the Position model for ISIN - {instrument_id} -, "
            f" currency - {currency_original} -,"
            f" and evaluation type {evaluation_type_val}. Event day {event_day}"
            f" Skipping this threshold result."
        )
        return (
            (pd.NA, pd.NA, pd.NA),
            audit_key,
            audit_data,
        )

    position_currency: float = position_data[amount_currency].unique().tolist()[0]

    relative_activity_value = observation_net_value / position_currency

    if abs(relative_activity_value) > relative_threshold:
        return (
            (
                relative_activity_value,
                position_currency,
                position_data.loc[:, PositionsField.DATE].values[0],
            ),
            None,
            None,
        )
    audit_key = InsiderTradingV3RefinitivAuditName.RELATIVE_ACTIVITY_THRESHOLD
    audit_data = dict(
        relative_activity=abs(relative_activity_value),
        threshold=relative_threshold,
        event_day=event_day,
    )
    return None, audit_key, audit_data
