# type: ignore
import logging
import pandas as pd
import re
from insider_trading_v3_refinitiv_apply_strategy.static import (
    RegexPatterns,
)
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.data_source.static.utility import DFColumns

logger = logging.getLogger(__name__)


def select_instrument_column(row: pd.Series) -> str:
    """Select instrument for news event creation for itv3 https://steeleye.atla
    ssian.net/wiki/spaces/PRODUCT/pages/2213843470/Insider+Trading+-

    +V3#%E2%9C%8D--Algo-Logic.

    :param row: pandas Series of an order state
    :return: string of the selected instrument
    """
    if NewColumns.UNDERLYING_INSTRUMENT_CODE in row.index:
        if pd.notnull(row[NewColumns.UNDERLYING_INSTRUMENT_CODE]):
            if pd.isna(row[OrderField.INST_CLASSIFICATION]):
                return row[NewColumns.UNDERLYING_INSTRUMENT_CODE]

            if re.fullmatch(
                RegexPatterns.EQUITY_DEPOSITARY_AND_BONDS,
                row[OrderField.INST_CLASSIFICATION],
            ):
                return row[OrderField.INST_ID_CODE]

            return row[NewColumns.UNDERLYING_INSTRUMENT_CODE]

        return row[OrderField.INST_ID_CODE]  # type: ignore

    return row[OrderField.INST_ID_CODE]  # type: ignore


def select_instrument_for_ric_fetch(row: pd.Series) -> str:
    """Returns list of Instrument Unique Identifiers or Underlying ISINs
    acording to specs.

    :param row: contains multiple instrument fields
    """
    if NewColumns.UNDERLYING_INSTRUMENT_CODE not in row.index:
        return row[OrderField.INST_EXT_UNIQUE_IDENT]

    if row[DFColumns.INSTRUMENT] == row[NewColumns.UNDERLYING_INSTRUMENT_CODE]:
        return row[DFColumns.INSTRUMENT]

    else:
        return row[OrderField.INST_EXT_UNIQUE_IDENT]
