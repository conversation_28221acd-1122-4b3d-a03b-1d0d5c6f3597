import addict
import datetime
import logging
import pandas as pd
from insider_trading_v3_refinitiv_apply_strategy import alerts
from insider_trading_v3_refinitiv_apply_strategy.alerts import InsiderTradingV3RefinitivScenario
from insider_trading_v3_refinitiv_apply_strategy.events import MarketDataEvent, NewsFeedEvent
from insider_trading_v3_refinitiv_apply_strategy.query import (  # type: ignore[attr-defined]
    Queries,
)
from insider_trading_v3_refinitiv_apply_strategy.static import (
    AlertColumnsEnum,
    EventDayDetailsEnum,
    EventDirection,
    EventKeys,
    EventResultDict,
    RefinitivNewsColumns,
)
from insider_trading_v3_refinitiv_apply_strategy.utils import (
    evaluate_thresholds,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.evaluate_thresholds import (  # type: ignore[attr-defined]  # noqa: E501
    analyse_event_direction,
)
from insider_trading_v3_refinitiv_apply_strategy.utils.event_utils import (
    filter_event_instruments,
)
from mar_utils.abstract.abstract_mar_apply_strategy import AbstractMarApplyStrategy
from mar_utils.auditor.strategy_audits.insider_trading_v3_refinitiv_audit import (
    InsiderTradingV3RefinitivAudit,
    InsiderTradingV3RefinitivAuditName,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OptionType, OrderField
from market_abuse_algorithms.data_source.static.utility import (
    CommonColumns,
    DFColumns,
    OrderStateValueColumns,
    PNLColumns,
)
from market_abuse_algorithms.mar_audit.mar_audit import (  # type: ignore[attr-defined]
    DATETIME_FORMAT,
)
from market_abuse_algorithms.strategy.base.currency import ExistingCurrencies
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.strategy import (  # type: ignore[attr-defined]
    market_abuse_audit_object,
)
from market_abuse_algorithms.utils import dates, formulas, processment
from market_abuse_algorithms.utils.data import filter_df_by_timestamps  # type: ignore[attr-defined]
from market_abuse_algorithms.utils.dates import get_day_timedelta  # type: ignore[attr-defined]
from market_abuse_algorithms.utils.filters import (  # type: ignore[attr-defined]
    discard_invalid_market_data,
)
from market_abuse_algorithms.utils.formulas import (  # type: ignore[attr-defined]
    calculate_net_amount,
)
from pandas._libs.missing import NAType
from se_elastic_schema.components.mar.strategy.insider_trading_v3.thresholds_refinitiv import (
    EventCreation,
    InsiderTradingV3RefinitivThresholds,
)
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elastic_schema.models.tenant.surveillance.surveillance_watch_execution import (
    SurveillanceWatchExecution,
)
from se_market_data_utils.schema.parquet import EoDStatsColumns
from se_market_data_utils.utils import currency_inversion_check, get_ric_for_currency_conversion
from typing import Dict, List, Optional, Set, Tuple, Union

logger_ = logging.getLogger(__name__)


class ApplyStrategy(AbstractMarApplyStrategy):
    def __init__(
        self,
        group_file_path: str,
        context: StrategyContext,
        group_index: int,
        surv_watch: SurveillanceWatch,
        surv_watch_execution: SurveillanceWatchExecution,
        **kwargs,
    ):
        super().__init__(
            group_file_path=group_file_path,
            context=context,
            group_index=group_index,
            surv_watch=surv_watch,
            surv_watch_execution=surv_watch_execution,
            scenario_cls=InsiderTradingV3RefinitivScenario,
            thresholds_cls=InsiderTradingV3RefinitivThresholds,
            queries_cls=Queries,
            mar_auditor_cls=InsiderTradingV3RefinitivAudit,
            **kwargs,
        )
        self.market_data_events: list = []
        self.news_feed_events: list = []

        self.instruments_with_news_feed_events: set = set()
        self.instruments_with_market_data_events: set = set()

        self.START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

    def reset_events(self):
        """
        Reset market data/news feed events
        :return:
        """
        self.instruments_with_news_feed_events = set()
        self.instruments_with_market_data_events = set()
        self.market_data_events = []
        self.news_feed_events = []

    def _apply_strategy(self):
        group_data_pl = self.get_group()
        # This is required to preserve the timezone in a different col for reassignment below
        event_days_col = [
            [pd.Timestamp(dt) for dt in inner_list]
            for inner_list in group_data_pl["event_days"].to_list()
        ]
        row_data = group_data_pl.to_pandas()
        # Direct conversion to pandas converts it to an object
        row_data[DFColumns.EVENT_DAYS] = event_days_col
        # since it's a single row, we can get the first element
        event_days = set(row_data.loc[:, "event_days"].explode().to_list())
        logger_.info(
            f"Running apply strategy for {row_data[DFColumns.INSTRUMENT].iloc[0]} for event_days:"
            f"{event_days}"
        )
        for event_day in event_days:
            observation_period: Dict = dates.determine_observation_period(  # type: ignore[attr-defined]
                event_day=event_day,
                observation_period=self.thresholds.activityObservationPeriod,
                intraday_surveillance=self.thresholds.intraDaySurveillance,
            )
            itv3_result = self._run_insider_trading_v3_refinitiv(
                instruments_from_obs_period=row_data,
                event_day=event_day,
                observation_period=observation_period,
            )
            # reset events, to avoid running with events from other dates.
            self.reset_events()

            if itv3_result.get("alerts_total_counter") == 0:
                logger_.warning(
                    f"No alerts for event day: {event_day.isoformat()} for instruments"
                    f" {(row_data[DFColumns.INSTRUMENT], row_data[DFColumns.RIC])}"
                )
                continue

    def _run_insider_trading_v3_refinitiv(
        self,
        instruments_from_obs_period: pd.DataFrame,
        event_day: pd.Timestamp,
        observation_period: Dict,
    ) -> Dict:
        """Run the insider trading v3 refinitiv strategy."""
        alerts_list = []
        itv3_result = {
            "input_orders": 0,
            "batch_alerts_counter": 0,
            "alerts_total_counter": 0,
        }
        # Since each apply strategy runs on a single instrument-ric
        instrument_ric_map = instruments_from_obs_period[
            [DFColumns.INSTRUMENT, DFColumns.RIC]
        ].apply(tuple, axis=1)[0]

        order_states_df: pd.DataFrame = self.queries.get_order_states(
            instrument_list=[instrument_ric_map[0]],
            event_day=event_day,
            observation_period=self.thresholds.activityObservationPeriod,
            behaviour_period=self.thresholds.activityBehaviourPeriod,
        )
        market_abuse_audit_object.records_analysed += len(order_states_df)

        itv3_result["input_orders"] = len(order_states_df)

        if order_states_df.empty:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.NO_EXECUTION,
                audit_data=dict(event_day=event_day, instrument=instrument_ric_map[0]),
                list_of_instruments=[instrument_ric_map[0]],
                list_of_order_ids=[],
                number_of_input_orders=0,
                number_of_resulting_orders=0,
            )
            logger_.info(
                f"Weren't found any executions for the day {event_day} for this list of "
                f"instruments: {[instrument_ric_map[0]]}."
            )
            return itv3_result
        self.auditor.step_audit(
            audit_key=InsiderTradingV3RefinitivAuditName.EXECUTION_FOUND,
            audit_data=dict(event_day=event_day, instrument=instrument_ric_map[0]),
            number_of_input_orders=len(order_states_df),
            number_of_resulting_orders=len(order_states_df),
        )
        # ToDo: check if this is required, this check is redundant
        instruments_data_to_check = instruments_from_obs_period.loc[
            instruments_from_obs_period[DFColumns.INSTRUMENT].isin(
                order_states_df[DFColumns.INSTRUMENT].unique().tolist()
            )
        ]

        filtered_instruments: List[str] = self.create_events_and_filters_instruments_with_events(
            data_to_analyse=instruments_data_to_check,
            event_day=event_day,
            order_states_data=order_states_df,
        )
        end = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        if not order_states_df[DFColumns.INSTRUMENT].isin(filtered_instruments).any():
            logger_.info(
                f"Weren't found any events (Market Data/News) for the day {event_day} for this"
                f" list of instruments: {[instrument_ric_map[0]]}."
            )
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_NEWS_EVENTS,
                audit_data=dict(
                    event_day=event_day,
                    event_creation=self.thresholds.eventCreation,
                ),
                list_of_order_ids=order_states_df.loc[:, OrderField.META_KEY].tolist(),
                list_of_instruments=[instrument_ric_map[0]],
                number_of_input_orders=order_states_df.shape[0],
                number_of_resulting_orders=0,
            )

            itv3_result["dropped_orders"] = len(order_states_df)
            return itv3_result

        orders_states_with_events = order_states_df.loc[
            order_states_df[DFColumns.INSTRUMENT].isin(filtered_instruments)
        ]
        if len(orders_states_with_events) != len(order_states_df):
            # Audit those orders which have instruments other than filtered instrument
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_NEWS_EVENTS,
                audit_data=dict(
                    event_day=event_day,
                    event_creation=self.thresholds.eventCreation,
                ),
                list_of_order_ids=order_states_df[
                    ~order_states_df[DFColumns.INSTRUMENT].isin(filtered_instruments)
                ]
                .loc[:, OrderField.META_KEY]
                .tolist(),
                list_of_instruments=[instrument_ric_map[0]],
                number_of_input_orders=order_states_df.shape[0],
                number_of_resulting_orders=len(orders_states_with_events),
            )
        else:
            # Remove the list of order ids/ instrument ids since this should
            # just be used for aggregation and not step audits for each order id
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_NEWS_EVENTS,
                audit_data=dict(
                    event_day=event_day,
                    event_creation=self.thresholds.eventCreation,
                ),
                number_of_input_orders=len(orders_states_with_events),
                number_of_resulting_orders=len(orders_states_with_events),
            )

        self.START_TIME = end

        itv3_result["dropped_orders"] = len(order_states_df) - len(orders_states_with_events)

        if (
            OrderField.EXC_DTL_BUY_SELL_IND not in orders_states_with_events.columns
            and self.thresholds.activityMinimumPNL is not None
        ):
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.BUY_SELL_MISSING,
                audit_data=dict(
                    event_day=event_day,
                ),
                list_of_order_ids=orders_states_with_events[OrderField.META_KEY].tolist(),
                number_of_input_orders=len(orders_states_with_events),
                number_of_resulting_orders=0,
            )
            self.START_TIME = end
            logger_.info(
                f"The data between the times {observation_period.get(DateRangeParameters.START)} "
                f"and {observation_period.get(DateRangeParameters.END)} "
                f"doesn't have the field {OrderField.EXC_DTL_BUY_SELL_IND}.  Event day {event_day}"
            )

            itv3_result["dropped_orders"] = itv3_result["input_orders"]
            return itv3_result
        self.auditor.step_audit(
            audit_key=InsiderTradingV3RefinitivAuditName.BUY_SELL_PRESENT,
            audit_data=dict(
                event_day=event_day,
            ),
            number_of_input_orders=len(orders_states_with_events),
            number_of_resulting_orders=len(orders_states_with_events),
        )
        if self.thresholds.activityBehaviourPeriod:
            behaviour_period_segments = dates.determine_behavior_periods(  # type: ignore[attr-defined]
                date_range=observation_period,
                observation_period=self.thresholds.activityObservationPeriod,
                behaviour_period=self.thresholds.activityBehaviourPeriod,
            )
        else:
            behaviour_period_segments = list()

        self.START_TIME = end

        if self.thresholds.ordersExclusion:
            order_state_parent_df, audit_key, audit_data = self.queries.get_executions_parents(
                executions_to_search_with=orders_states_with_events,
                event_day=event_day,
            )
            if order_state_parent_df.empty:
                self.auditor.step_audit(
                    audit_key=audit_key,
                    audit_data=audit_data,
                    list_of_order_ids=orders_states_with_events[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(orders_states_with_events),
                    number_of_resulting_orders=0,
                )

                itv3_result["dropped_orders"] = itv3_result["input_orders"]
                return itv3_result

            original_order_state_len = len(orders_states_with_events)
            ids_before_exclusion = set(orders_states_with_events[OrderField.META_KEY].tolist())

            orders_states_with_events, audit_key, audit_data = processment.exclude_orders(  # type: ignore[attr-defined]
                dataset_to_exclude=orders_states_with_events,
                execution_parents=order_state_parent_df,
                observation_period=observation_period,
                event_day=event_day,
            )
            self.auditor.step_audit(
                audit_key=audit_key,
                audit_data=audit_data,
                list_of_order_ids=list(
                    ids_before_exclusion
                    - set(orders_states_with_events[OrderField.META_KEY].tolist())
                ),
                number_of_input_orders=original_order_state_len,
                number_of_resulting_orders=orders_states_with_events.shape[0],
            )
            if orders_states_with_events.empty:
                itv3_result["dropped_orders"] = itv3_result["input_orders"]
                return itv3_result

        orders_states_with_events[DFColumns.RIC] = orders_states_with_events[
            DFColumns.INSTRUMENT
        ].map(dict([instrument_ric_map]))

        order_states_w_order_st_value, audit_key, audit_data = formulas.get_order_state_values(  # type: ignore[attr-defined]
            order_states_df=orders_states_with_events,
            currency_filter_threshold=self.thresholds.currencyFilter,
            event_day=event_day,
        )
        if order_states_w_order_st_value.empty:
            logger_.warning(
                "There isn't any data after getting the order state values converted. "
                "Maybe due to missing fields in the data.",
                exc_info=True,
            )
            itv3_result["dropped_orders"] = itv3_result["input_orders"]
            self.auditor.step_audit(
                audit_key=audit_key,
                audit_data=audit_data,
                list_of_order_ids=orders_states_with_events[OrderField.META_KEY].tolist(),
                number_of_input_orders=len(orders_states_with_events),
                number_of_resulting_orders=len(order_states_w_order_st_value),
            )
            return itv3_result
        if audit_key:
            self.auditor.step_audit(
                audit_key=audit_key,
                audit_data=audit_data,
                list_of_order_ids=list(
                    set(orders_states_with_events[OrderField.META_KEY].tolist())
                    - set(order_states_w_order_st_value[OrderField.META_KEY].tolist())
                ),
                number_of_input_orders=len(orders_states_with_events),
                number_of_resulting_orders=len(order_states_w_order_st_value),
            )
        else:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.CONVERT_PRICE_SUCCESS,
                audit_data=dict(event_day=event_day),
                number_of_input_orders=len(orders_states_with_events),
                number_of_resulting_orders=len(order_states_w_order_st_value),
            )

        self.START_TIME = end

        grouping_columns: Dict = processment.get_grouping_columns(  # type: ignore[attr-defined]
            evaluation_type=self.thresholds.evaluationType,
            data=order_states_w_order_st_value,
            directionality=self.thresholds.directionality,
        )
        groupings_dropped = 0
        if not grouping_columns:
            # https://steeleye.atlassian.net/browse/MM-96 ignore orders which are
            # missing selected evaluationType
            logger_.info(
                f"No grouping columns found for the evaluation type {self.thresholds.eventCreation}"
            )
            audit_key = InsiderTradingV3RefinitivAuditName.EVALUATION_TYPE_COL_MISSING
            audit_data = dict(event_day=event_day, field=self.thresholds.evaluationType)
            self.auditor.step_audit(
                audit_key=audit_key,
                audit_data=audit_data,
                list_of_order_ids=order_states_w_order_st_value[OrderField.META_KEY].tolist(),
                number_of_input_orders=len(order_states_w_order_st_value),
                number_of_resulting_orders=0,
            )
            return itv3_result
        self.auditor.step_audit(
            audit_key=InsiderTradingV3RefinitivAuditName.EVALUATION_TYPE_COL_EXISTS,
            audit_data=dict(event_day=event_day),
            number_of_input_orders=len(order_states_w_order_st_value),
            number_of_resulting_orders=len(order_states_w_order_st_value),
        )

        for group_values, group in order_states_w_order_st_value.groupby(
            list(grouping_columns.values())
        ):
            instrument_value, evaluation_grp_val, side = (
                processment.set_instrument_value_evaluation_grp_val(group_values=group_values)  # type: ignore[attr-defined] # noqa: E501
            )

            # Since grouping by side is optional, we don't need to check if it's present
            if not instrument_value and not evaluation_grp_val:
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.GROUPING_DATA_MISSING,
                    audit_data=dict(
                        event_day=event_day,
                    ),
                    list_of_order_ids=group[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=0,
                )

                itv3_result["dropped_orders"] += len(group)

                continue
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.GROUPING_DATA_EXISTS,
                audit_data=dict(event_day=event_day),
                number_of_input_orders=len(group),
                number_of_resulting_orders=len(group),
            )
            amount_dict, audit_key, audit_data = evaluate_thresholds.check_minimum_trade_amount(  # type: ignore[attr-defined]
                order_states_data=group,
                observation_date_range=observation_period,
                behaviour_period_segments=behaviour_period_segments,
                min_trade_amount_threshold=self.thresholds.activityMinimumTradeAmount,
                event_day=event_day,
            )
            if not amount_dict:
                self.auditor.step_audit(
                    audit_key=audit_key,
                    audit_data=audit_data,
                    list_of_order_ids=group[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=0,
                )

                logger_.warning(
                    f"The group with the instrument {instrument_value} didn't pass the Minimum"
                    f" Notional Value test (step 14). The sum of Net trades in the observation "
                    f"period is not bigger than the Minimum Trade Amount threshold.",
                    exc_info=True,
                )
                itv3_result["dropped_orders"] += len(group)

                continue
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MIN_TRADE_AMOUNT_PASSED,
                audit_data=dict(event_day=event_day),
                number_of_input_orders=len(group),
                number_of_resulting_orders=len(group),
            )

            observation_period_amount: float = amount_dict.get(
                CommonColumns.OBSERVATION_PERIOD_AMOUNT
            )
            segment_amount_list: List = amount_dict.get(CommonColumns.SEGMENT_AMOUNT_LIST, [])
            if self.thresholds.activityBehaviourPeriod:
                behaviour_confidence_interval: Dict = evaluate_thresholds.check_net_trade_amount_threshold(  # type: ignore[attr-defined]  # noqa: E501
                    segment_amount_list=segment_amount_list,
                    observation_period_amount=observation_period_amount,
                    activity_allowed_range_from_profile_th=self.thresholds.activityAllowedRangeFromProfile,
                )

                if not behaviour_confidence_interval:
                    self.auditor.step_audit(
                        audit_key=InsiderTradingV3RefinitivAuditName.OUTSIDE_BEHAVIOUR_CONFIDENCE,
                        audit_data=dict(
                            observation_amount=observation_period_amount,
                            behaviour_confidence=behaviour_confidence_interval,
                            event_day=event_day,
                        ),
                        list_of_order_ids=group[OrderField.META_KEY].tolist(),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                    )
                    logger_.info(
                        "Observational net trade amount is not outside the allowed range interval",
                        exc_info=True,
                    )
                    itv3_result["dropped_orders"] += len(group)
                    continue
            else:
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.ACTIVITY_BEHAVIOUR_PERIOD_PASSED,
                    audit_data=dict(event_day=event_day),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=len(group),
                )
                behaviour_confidence_interval = {}

            observation_data: pd.DataFrame = filter_df_by_timestamps(
                data=group,
                timestamp_column=OrderField.TS_TRADING_DATE_TIME,
                date_range=observation_period,
            )

            (
                close_price_for_event_day,
                close_price_for_event_day_converted,
                market_data_currency_for_event_day,
            ) = self.get_market_close_price_for_pnl(
                data_in_analyse=group,
                instrument_val=instrument_value,
                day_of_event=event_day,
                list_of_market_data_events=self.market_data_events,
            )

            self.START_TIME = end

            pnl_result, audit_key, audit_data = evaluate_thresholds.global_pnl_evaluation(  # type:ignore[attr-defined]
                data_in_analyse=group,
                event_day_close_price=close_price_for_event_day,
                event_day_close_price_converted=close_price_for_event_day_converted,
                market_data_currency=market_data_currency_for_event_day,
                observation_period_range=observation_period,
                data_in_obs_period=observation_data,
                filter_currency=self.thresholds.currencyFilter,
                pnl_threshold=self.thresholds.activityMinimumPNL,
                event_day=event_day,
            )

            if pnl_result is None and self.thresholds.activityMinimumPNL is not None:
                self.auditor.step_audit(
                    audit_key=audit_key,
                    audit_data=audit_data,
                    list_of_order_ids=group[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=0,
                )
                itv3_result["dropped_orders"] += len(group)
                continue
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.ACTIVITY_PNL_PASSED,
                audit_data=dict(event_day=event_day),
                number_of_input_orders=len(group),
                number_of_resulting_orders=len(group),
            )

            pnl_value = pnl_result.get("pnl")[PNLColumns.PNL_VALUE].unique().tolist()[0]
            event_day_close_price = pnl_result.get("close_price")
            event_day_close_price_converted = pnl_result.get("close_price_converted")

            transaction_volume = self.calculate_transaction_volume(
                data=group, currency=market_data_currency_for_event_day
            )

            if (
                self.thresholds.eventCreation == EventCreation.BOTH
                or self.thresholds.eventCreation == EventCreation.NEWS_FEED
            ) and (transaction_volume is None or transaction_volume == 0):
                groupings_dropped += 1
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.TRANSACTION_VOLUME_CALCULATION,
                    audit_data=dict(event_day=event_day),
                    list_of_order_ids=group[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=0,
                )

                logger_.info(
                    "Couldn't calculate the transaction volume. Skipping group.",
                    exc_info=True,
                )
                continue

            check_events_result, audit_key, audit_data = self.check_events_order_states_direction(
                transaction_volume=transaction_volume,
                instrument=instrument_value,
                observation_amount=observation_period_amount,
                behaviour_series=pd.Series(segment_amount_list),
                event_day=event_day,
            )

            if not check_events_result.get(EventResultDict.CREATE_ALERT):
                self.auditor.step_audit(
                    audit_key=audit_key,
                    audit_data=audit_data,
                    list_of_order_ids=group[OrderField.META_KEY].tolist(),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=0,
                )
                logger_.info(
                    f"After analysing the events direction for the group with the instrument "
                    f"{instrument_value}, "
                    f"there isn't any alert to be created."
                )
                continue
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.SENTIMENT_THRESHOLD_PASSED,
                audit_data=dict(event_day=event_day),
                number_of_input_orders=len(group),
                number_of_resulting_orders=len(group),
            )

            relative_positions_data: Tuple | None = None
            if self.thresholds.relativeActivity is not None:
                relative_positions_data, audit_key, audit_data = self.get_and_check_position_record(  # type: ignore # noqa: E501
                    alert_data_to_check=group,
                    grouping_columns_dict=grouping_columns,
                    eval_grp_val=evaluation_grp_val,
                    behaviour_date_range=dates.get_full_behaviour_period(  # type: ignore[attr-defined]
                        list_of_behaviour_periods=behaviour_period_segments
                    )
                    if behaviour_period_segments
                    else observation_period,
                    observation_period_net_amount=observation_period_amount,
                )

                if pd.isnull(relative_positions_data):  # type: ignore
                    self.auditor.step_audit(
                        audit_key=audit_key,
                        audit_data=audit_data,
                        list_of_order_ids=group[OrderField.META_KEY].tolist(),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                    )
                    logger_.info(
                        f"This instrument - {instrument_value} - dataset didn't "
                        f"pass the relative activity threshold."
                        f" there isn't any alert to be created."
                    )
                    continue
                if audit_key:
                    # Some audit worthy information, but no need to drop the group
                    self.auditor.step_audit(
                        audit_key=audit_key,
                        audit_data=audit_data,
                        list_of_order_ids=group[OrderField.META_KEY].tolist(),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=len(group),
                    )
                else:
                    self.auditor.step_audit(
                        audit_key=InsiderTradingV3RefinitivAuditName.RELATIVE_ACTIVITY_PASSED,
                        audit_data=dict(event_day=event_day),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=len(group),
                    )

            if behaviour_confidence_interval:
                if not evaluate_thresholds.check_net_trade_out_of_allowed_range(  # type: ignore[attr-defined]
                    behaviour_confidence_interval=behaviour_confidence_interval,
                    observation_period_amount=observation_period_amount,
                ):
                    self.auditor.step_audit(
                        audit_key=InsiderTradingV3RefinitivAuditName.ACTIVITY_CONFIDENCE_INTERVAL,
                        audit_data=dict(
                            allowed_range=behaviour_confidence_interval,
                            observation_amount=observation_period_amount,
                            event_day=event_day,
                        ),
                        list_of_order_ids=group[OrderField.META_KEY].tolist(),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                    )

                    continue
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.ACTIVITY_CONFIDENCE_INTERVAL_PASSED,
                    audit_data=dict(event_day=event_day),
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=len(group),
                )

            alert: Dict = self.create_alert(
                alert_data=group,
                event_day=event_day,
                event_day_close_price=event_day_close_price,
                event_day_close_price_converted=event_day_close_price_converted,
                market_close_currency=market_data_currency_for_event_day,
                instrument=instrument_value,
                behaviour_confidence_interval=behaviour_confidence_interval,
                event_type=self.thresholds.eventCreation.value,
                evaluation_id=evaluation_grp_val,
                observation_period_net_amount=observation_period_amount,
                pnl=pnl_value,
                positions_data=relative_positions_data,
                news_feed_events=check_events_result.get("news_feed_events", []),
                market_data_events=check_events_result.get("market_data_events", []),
            )

            alerts_list.append(alert)
            itv3_result["batch_alerts_counter"] += 1
            itv3_result["alerts_total_counter"] += 1
            if itv3_result["batch_alerts_counter"] == 100:
                self._write_result_to_ndjson(alerts_df=pd.DataFrame(alerts_list))
                alerts_list = []
                itv3_result["batch_alerts_counter"] = 0

        # Write the remaining alerts to the ndjson file
        if alerts_list:
            self._write_result_to_ndjson(alerts_df=pd.DataFrame(alerts_list))
        return itv3_result

    def get_and_check_position_record(
        self,
        alert_data_to_check: pd.DataFrame,
        grouping_columns_dict: Dict,
        eval_grp_val: str,
        behaviour_date_range: Dict,
        observation_period_net_amount: float,
        event_day: Optional[pd.Timestamp] = None,
    ) -> Optional[Tuple[Optional[Tuple], str | None, Dict | None]]:
        """Evaluate positions threshold. Get position record and then check if
        inline with the relativity threshold.

        :param event_day: event day to log in StepAudit
        :param behaviour_date_range: dict with min/max behaviour period
        :param observation_period_net_amount: float net trade amount during obs period
        :param alert_data_to_check: dataframe with
        :param grouping_columns_dict: grouping fields - evaluation type and instrument
        :param eval_grp_val: str, value of the evaluation type
        :return: bool, true generates alerts; false doesn't generate alerts
        """

        positions_cols = [DFColumns.INSTRUMENT, OrderField.TRX_DTL_PC_CCY]
        evaluation_type = None

        if "evaluationGrouping" in grouping_columns_dict.keys() and eval_grp_val is not None:
            evaluation_type = {self.thresholds.evaluationType: eval_grp_val}

        missing_cols = set(positions_cols) - set(alert_data_to_check.columns)

        if missing_cols:
            audit_key = InsiderTradingV3RefinitivAuditName.POSITIONS_COLUMN_MISSING
            audit_data = dict(
                event_day=event_day,
                missing_columns=missing_cols,
            )
            logger_.info(
                f"There are missing columns important to get the positions record."
                f" These are the columns: {missing_cols}."
                f" Skipping this threshold result. Event day {event_day}"
            )
            return (
                (
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ),
                audit_key,
                audit_data,
            )

        isin = alert_data_to_check[DFColumns.INSTRUMENT].unique().tolist()[0]
        currency = alert_data_to_check[OrderField.TRX_DTL_PC_CCY].unique().tolist()[0]

        positions_record: pd.DataFrame = self.queries.get_positions_record(
            instrument_id=isin,
            currency=currency,
            evaluation_type=evaluation_type,
            behaviour_period=behaviour_date_range,
        )

        if positions_record.empty:
            audit_key = InsiderTradingV3RefinitivAuditName.NO_POSITIONS_DATA
            audit_data = dict(
                isin=isin,
                currency=currency,
                event_day=event_day,
                evaluation=eval_grp_val,  # type: ignore
            )
            logger_.info(
                f"No Positions model available for ISIN - {isin} -, currency - {currency} -,"
                f" and evaluation type {eval_grp_val}. Event day {event_day}"
                f" No alert will be generated."
            )
            return (
                None,
                audit_key,
                audit_data,
            )

        positions_record = dates.get_latest_positions_record(  # type: ignore[attr-defined]
            positions_data=positions_record
        )

        relative_data, audit_key, audit_data = (
            evaluate_thresholds.calculate_and_check_relative_amount(  # type: ignore[attr-defined] # noqa: E501
                position_data=positions_record,
                relative_threshold=self.thresholds.relativeActivity,
                currency_threshold=self.thresholds.currencyFilter,
                instrument_id=isin,
                currency_original=currency,
                evaluation_type_val=eval_grp_val,
                observation_net_value=observation_period_net_amount,
                event_day=event_day,
            )
        )

        return (
            relative_data,
            audit_key,
            audit_data,
        )

    def create_events_and_filters_instruments_with_events(
        self,
        data_to_analyse: pd.DataFrame,
        event_day: pd.Timestamp,
        order_states_data: pd.DataFrame,
    ) -> List[str]:
        """creates events and returns the list of instruments with events.

        :param order_states_data:
        :param data_to_analyse: instrument data used to create the events
        :param event_day: event day timestamp
        :return: list of instruments with events
        """
        instrument_ric_map = data_to_analyse[[DFColumns.INSTRUMENT, DFColumns.RIC]].apply(
            tuple, axis=1
        )[0]

        instruments = self.create_events_and_get_respective_instruments(
            instrument_ric=instrument_ric_map,
            event_day=event_day,
            order_states_data=order_states_data,
        )

        if not instruments:
            logger_.info("No instruments have created market or news events", exc_info=True)
            return []

        filtered_instruments: List = processment.filter_instruments(  # type: ignore[attr-defined]
            list_of_instruments=instruments, data_to_be_analysed=data_to_analyse
        )

        return filtered_instruments

    def create_events_and_get_respective_instruments(
        self,
        instrument_ric: Tuple,
        event_day: pd.Timestamp,
        order_states_data: pd.DataFrame,
    ) -> Union[List, Set]:
        """Create events and filter the instruments in that events.

        :param order_states_data:
        :param event_day:
        :param instrument_ric: instrument-RIC to create events for
        :return:
        """
        if self.thresholds.eventCreation != EventCreation.MARKET_DATA:
            self.create_news_feeds_events(
                instrument_ric=instrument_ric,
                event_day=event_day,
                order_states_data=order_states_data,
            )

        if self.thresholds.eventCreation != EventCreation.NEWS_FEED:
            self.create_market_data_events(
                instrument_ric=instrument_ric,
                event_day=event_day,
                order_states_data=order_states_data,
            )

        filtered_instruments: Set = filter_event_instruments(
            event_creation_threshold=self.thresholds.eventCreation,
            instruments_with_news_feed_events=self.instruments_with_news_feed_events,
            instruments_with_market_data_events=self.instruments_with_market_data_events,
        )

        if filtered_instruments:
            return list(filtered_instruments)

        return filtered_instruments

    def create_news_feeds_events(
        self,
        instrument_ric: tuple,
        event_day: pd.Timestamp,
        order_states_data: pd.DataFrame,
    ) -> None:
        """Uses the list of instruments and rics to fetch and create news feed
        events by querying refinitiv.

        :param order_states_data: dataframe with all the orders states
        :param event_day: event day timestamp
        :param instrument_ric: instrument, ric to be queried in refinitiv
        """
        if not instrument_ric:
            logger_.info("No instrument list was provided", exc_info=True)
            return
        instrument, ric = instrument_ric
        orders_per_instrument = (
            (
                order_states_data.loc[
                    order_states_data.loc[:, DFColumns.INSTRUMENT] == instrument, :
                ][OrderField.META_KEY]
                .unique()
                .tolist()
            )
            if not order_states_data.empty
            else []
        )

        if ric is None:
            logger_.info(
                f"The instrument {instrument} doesn't have a RIC mapped. Won't be able to"
                f" fetch news data. Event day {event_day}",
                exc_info=True,
            )
            return

        refinitiv_data, audit_key, audit_data = self.queries.fetch_refinitiv_news(
            ric=ric,
            date_range={
                "start": event_day,
                "end": event_day + get_day_timedelta(),  # search entire day
            },
            orders_per_instrument=orders_per_instrument,
        )

        if refinitiv_data.empty:
            # Audit just the fact the refiniv data isn't available. The records arent' dropped yet
            self.auditor.step_audit(
                audit_key=audit_key,
                audit_data=audit_data,
                list_of_instruments=[instrument],
                number_of_input_orders=len(orders_per_instrument),
                number_of_resulting_orders=len(orders_per_instrument),
            )
            logger_.info(
                f"The instrument {instrument} doesn't have refinitiv NEWS. Event day {event_day}",
                exc_info=True,
            )

            return
        self.auditor.step_audit(
            audit_key=audit_key,
            audit_data=audit_data,
            number_of_input_orders=len(orders_per_instrument),
            number_of_resulting_orders=len(orders_per_instrument),
        )

        positive_sentiment_news = refinitiv_data[
            (refinitiv_data[RefinitivNewsColumns.SENTIMENT_POSITIVE] > self.thresholds.sentiment)
        ]

        negative_sentiment_news = refinitiv_data[
            (refinitiv_data[RefinitivNewsColumns.SENTIMENT_NEGATIVE] > self.thresholds.sentiment)
        ]
        if not positive_sentiment_news.empty:
            self.news_feed_events.append(
                NewsFeedEvent(
                    date=event_day,
                    instrument=instrument,
                    ric=ric,
                    news_feed=positive_sentiment_news,
                    direction=EventDirection.UP,
                )
            )
            self.instruments_with_news_feed_events.add(instrument)

        if not negative_sentiment_news.empty:
            self.news_feed_events.append(
                NewsFeedEvent(
                    date=event_day,
                    instrument=instrument,
                    ric=ric,
                    news_feed=negative_sentiment_news,
                    direction=EventDirection.DOWN,
                )
            )
            self.instruments_with_news_feed_events.add(instrument)

    def create_market_data_events(
        self,
        instrument_ric: tuple,
        event_day: pd.Timestamp,
        order_states_data: pd.DataFrame,
    ) -> None:
        """generates market data events using EOD stats for the input
        instruments.

        :param order_states_data: dataframe with all the orders states
        :param event_day: event day timestamp
        :param instrument_ric: isin-ric to generate events
        """
        instrument, ric = instrument_ric
        orders_per_instrument = (
            (
                order_states_data.loc[
                    order_states_data.loc[:, DFColumns.INSTRUMENT] == instrument, :
                ][OrderField.META_KEY]
                .unique()
                .tolist()
            )
            if not order_states_data.empty
            else []
        )

        # Since we're not dropping orders at this stage, the step audit data
        # related to orders is common in this entire method
        common_step_audit_field = dict(
            list_of_order_ids=orders_per_instrument,
            list_of_instruments=[instrument],
            number_of_input_orders=len(orders_per_instrument),
            number_of_resulting_orders=len(orders_per_instrument),
        )
        if ric is None:
            logger_.info(
                f"The instrument {instrument} doesn't have a RIC mapped. Won't be able to"
                f" fetch market data. Event day {event_day}",
                exc_info=True,
            )
            return

        market_data: pd.DataFrame = self.queries.get_market_data_by_instrument_or_ric(
            instrument=instrument, mapped_ric=ric
        )

        if market_data.empty:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_MISSING_FOR_RIC,
                audit_data=dict(
                    event_day=f"Event Day: {event_day}",
                    ric=ric,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"No market data was found for Instrument/Ric "
                f"{instrument}/{ric}. Event day {event_day}",
                exc_info=True,
            )
            return

        market_data = discard_invalid_market_data(market_data=market_data)

        if market_data.empty:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_INVALID_CLOSE_PRICE,
                audit_data=dict(
                    event_day=event_day,
                    ric=ric,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"No market data was found for Instrument/Ric "
                f"{instrument}/{ric}. Event day {event_day} did not had valid CLOSE_PRICE",
                exc_info=True,
            )
            return

        event_day_df = processment.get_market_data_for_day(  # type: ignore[attr-defined]
            market_data=market_data, day_of_event=event_day
        )
        event_day_minus_1_df = processment.get_market_data_for_day(  # type: ignore[attr-defined]
            market_data=market_data,
            day_of_event=event_day - pd.tseries.offsets.BDay(1),
            backward_looking=True,
        )

        if event_day_minus_1_df.empty or event_day_df.empty:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_MISSING_FOR_RIC,
                audit_data=dict(
                    event_day=f"{event_day} or day before",
                    ric=ric,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"No market data found for event day {event_day} or the day before for RIC {ric}",
                exc_info=True,
            )
            return

        if self.thresholds.marketDataEventDailyVolumeVariation:
            if not evaluate_thresholds.check_event_daily_variation(  # type: ignore[attr-defined]
                market_data_day=event_day_df,
                market_data_day_minus_one=event_day_minus_1_df,
                market_data_event_daily_vol_variation_th=self.thresholds.marketDataEventDailyVolumeVariation,
                orders_data_per_instrument=orders_per_instrument,
                instrument=instrument,
            ):
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_VOLUME_VARIATION,
                    audit_data=dict(
                        threshold=self.thresholds.marketDataEventDailyVolumeVariation,
                        instrument=instrument,
                        event_day=event_day,
                    ),
                    **common_step_audit_field,
                )
                logger_.info(
                    "Volume variation is lower than the volume variation threshold",
                    exc_info=True,
                )
                return

        index_event_day: int = event_day_df.index.values[0]

        market_data_12_days_df: pd.DataFrame = market_data.iloc[
            index_event_day - 11 : index_event_day + 1, :
        ]

        if market_data_12_days_df.empty:
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_MISSING_FOR_RIC,
                audit_data=dict(
                    event_day=f"the previous 12 days from event day {event_day}",
                    ric=ric,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"No market data found on the previous 12 days from {event_day} for the RIC {ric}",
                exc_info=True,
            )
            return

        # CHECK 5BD days diferrence

        if self.check_5b_days_difference(market_data_12_days_df):
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATE_OUTSIDE_MAX_DAYS,
                audit_data=dict(
                    ric=ric,
                    event_day=event_day,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"Market data has more than 5 business "
                f"days difference for {event_day} and RIC {ric}",
                exc_info=True,
            )
            return

        close_price_event_day: float = processment.get_close_price_to_analyse(  # type: ignore[attr-defined]
            data_to_fetch_from=event_day_df
        )

        orders_data = order_states_data.loc[
            order_states_data.loc[:, DFColumns.INSTRUMENT] == instrument, :
        ]
        market_currency = market_data_12_days_df.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        conversion_rate = self.get_conversion_rate(
            group=orders_data,
            market_data_currency=market_currency,
            event_day=event_day,
        )

        if pd.isna(conversion_rate):
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.CURRENCY_CONVERSION_FAILED,
                audit_data=dict(
                    market_data_currency=market_currency,
                    order_currency=orders_data[OrderField.TRX_DTL_PC_CCY].unique().tolist()[0],
                ),
                **common_step_audit_field,
            )
            return

        close_price_event_day_converted = close_price_event_day * conversion_rate

        close_price_event_day_minus_1: float = processment.get_close_price_to_analyse(  # type: ignore[attr-defined]
            data_to_fetch_from=event_day_minus_1_df
        )

        close_price_variation: float = formulas.calc_close_price_variation(  # type: ignore[attr-defined]
            close_price_day=close_price_event_day,
            close_price_day_minus_one=close_price_event_day_minus_1,
        )

        if (
            pd.isna(close_price_variation)
            or pd.isnull(close_price_variation)
            or close_price_variation is None
            or close_price_variation == 0
        ):
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.INVALID_CLOSE_PRICE_VARIATION,
                audit_data=dict(
                    ric=ric,
                    event_day=event_day,
                    close_price=close_price_event_day,
                    prev_close_price=close_price_event_day_minus_1,
                ),
                **common_step_audit_field,
            )
            logger_.info(
                f"Either close price of RIC {ric} "
                f"for the event day {event_day} is empty or there isn't"
                f" close price variation. Close price variation: {close_price_variation}."
                f" Close price for event day {event_day}: {close_price_event_day}."
                f" Close price for event day -1: {close_price_event_day_minus_1}.",
                exc_info=True,
            )
            return

        if self.thresholds.minimumPercentagePriceVariation is not None:
            if abs(close_price_variation) <= self.thresholds.minimumPercentagePriceVariation:
                self.auditor.step_audit(
                    audit_key=InsiderTradingV3RefinitivAuditName.MIN_PERCENTAGE_PRICE_VARIATION,
                    audit_data=dict(
                        threshold=self.thresholds.minimumPercentagePriceVariation,
                        variation=abs(close_price_variation),
                        event_day=event_day,
                    ),
                    **common_step_audit_field,
                )
                logger_.info(
                    f"Close Price Variation ({abs(close_price_variation)})"
                    f" is less than Minimum Price "
                    f"Variation threshold ({self.thresholds.minimumPercentagePriceVariation})."
                    f" Event day {event_day}"
                )
                return

        price_variations_11_days: pd.Series = formulas.calc_price_variation_for_11_days(  # type: ignore[attr-defined]
            market_data=market_data_12_days_df
        )

        if self.thresholds.marketDataEventSectorIndexComparisonImpact:
            sector_index_data_df: pd.DataFrame = self.queries.get_sector_index_stats(
                instrument=instrument
            )

            price_variations_11_days = price_variations_11_days - (
                sector_index_data_df * self.thresholds.marketDataEventSectorIndexComparisonImpact
            )

        try:
            confidence_interval: Dict = evaluate_thresholds.check_confidence_interval(  # type: ignore[attr-defined]
                price_variations=price_variations_11_days,
                close_price_variation=close_price_variation,
                price_variation_mkt_data=self.thresholds.marketDataEventPriceVariation,
                instrument=instrument,
            )
        except Exception:
            logger_.info(f"check_confidence_interval failed for {instrument}/{ric}")
            confidence_interval = {}

        if not confidence_interval:
            range_start = confidence_interval.get(DateRangeParameters.START)
            range_end = confidence_interval.get(DateRangeParameters.END)
            allowed_range = f"{range_start} - {range_end}" if range_start and range_end else ""
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.CLOSE_PRICE_VARIATION_OUTSIDE_RANGE,
                audit_data=dict(
                    variation=close_price_variation,
                    allowed_range=allowed_range,
                    event_day=event_day,
                ),
                **common_step_audit_field,
            )

            logger_.info(
                f"Close price variations {close_price_variation} is not"
                f" outside the allowed range "
                f"interval. Event day {event_day}",
                exc_info=True,
            )
            return

        direction = EventDirection.UP if close_price_variation > 0 else EventDirection.DOWN

        if not evaluate_thresholds.check_direction_condition(  # type: ignore
            direction=direction,
            price_variation=close_price_variation,
            interval=confidence_interval,
            instrument=instrument,
        ):
            self.auditor.step_audit(
                audit_key=InsiderTradingV3RefinitivAuditName.CLOSE_PRICE_VARIATION_OPP_DIRECTION,
                audit_data=dict(
                    direction=direction, variation=close_price_variation, event_day=event_day
                ),
                **common_step_audit_field,
            )

            logger_.info(
                "Direction and Close price variations do not follow the same orientation",
                exc_info=True,
            )
            return

        self.market_data_events.append(
            MarketDataEvent(
                date=event_day,
                instrument=instrument,
                direction=direction,
                event_day_close_price=close_price_event_day,
                ric=event_day_df.loc[:, EoDStatsColumns.RIC].values[0],
                market_data=market_data_12_days_df,
                close_price_variation=close_price_variation,
                event_day_close_price_converted=close_price_event_day_converted,
                allowed_price_range=confidence_interval,
            )
        )
        self.auditor.step_audit(
            audit_key=InsiderTradingV3RefinitivAuditName.MARKET_DATA_FOUND,
            audit_data=dict(
                event_day=event_day,
                ric=ric,
            ),
            number_of_input_orders=len(orders_per_instrument),
            number_of_resulting_orders=len(orders_per_instrument),
        )

        self.instruments_with_market_data_events.add(instrument)

    @staticmethod
    def check_5b_days_difference(
        market_data_slice: pd.DataFrame,
    ) -> bool:
        """Check if between the market data rows exists more than 5 Bday
        difference.

        :param market_data_slice: the slice of market data selected

        return: True if the market_data_slice is invalid, False otherwise
        """

        # check row by row if the difference between the dates is not bigger than 5 BD
        for idx in range(len(market_data_slice) - 1):
            cur_date = market_data_slice.iloc[idx][EoDStatsColumns.DATE]
            next_date = market_data_slice.iloc[idx + 1][EoDStatsColumns.DATE]

            if (
                pd.date_range(start=cur_date, end=next_date, freq="B").shape[0] > 6
            ):  # greater than 6 because the next_date is also consider for the date_range and only if bigger than 5  # noqa: E501
                return True

        return False

    def get_market_close_price_for_pnl(
        self,
        data_in_analyse: pd.DataFrame,
        instrument_val: str,
        day_of_event: pd.Timestamp,
        list_of_market_data_events: List[MarketDataEvent],
    ):
        """Get the close price and the currency for market data.

        :param data_in_analyse: pd.Dataframe, group dataset with order state value
        :param instrument_val: str, ISIN of instrument
        :param day_of_event: pd.Timestamp, event day
        :param list_of_market_data_events: list of MarketEvents
        :return:
        """

        market_data_currency: Optional[str] = (
            formulas.get_market_data_currency_using_market_data_events(  # type: ignore[attr-defined]
                instrument_id=instrument_val,
                event_day=day_of_event,
                market_data_events=list_of_market_data_events,
            )
        )

        (
            event_day_close_price,
            event_day_close_price_converted,
        ) = formulas.get_event_day_close_price_using_market_data_events(  # type: ignore[attr-defined]
            instrument_id=instrument_val,
            event_day=day_of_event,
            market_data_events=list_of_market_data_events,
        )

        if pd.isna(event_day_close_price):
            logger_.info(
                f"The close price for the day {day_of_event} is NULL. "
                f"This could mean that there aren't any market data events created, but could be "
                f"normal. Let's try to fetch market data as a fallback.",
                exc_info=True,
            )

            ric = data_in_analyse[DFColumns.RIC].unique().tolist()[0]

            (
                event_day_close_price,
                market_data_currency,
            ) = self.get_market_close_price_and_currency_fallback(
                ric=ric, instrument=instrument_val, event_day=day_of_event
            )

        return (
            event_day_close_price,
            event_day_close_price_converted,
            market_data_currency,
        )

    def get_conversion_rate(
        self,
        group: pd.DataFrame,
        market_data_currency: str,
        event_day: pd.Timestamp,
    ) -> float | NAType:
        """Get the conversion rate for the close price to be used in the pnl
        calculation.

        :param group: pd.Dataframe, dataframe with the orders that are being processed
        :param market_data_currency: str, currency of the market data
        :param event_day: pd.Timestamp, timestamp of the event day being processed

        :return: pd.NA or float
        """
        order_currency = group[OrderField.TRX_DTL_PC_CCY].unique().tolist()[0]
        if market_data_currency != order_currency and market_data_currency is not None:
            if (
                market_data_currency in ExistingCurrencies.get_currencies()
                and order_currency in ExistingCurrencies.get_currencies()
            ):
                logger_.info(
                    f"Using ECB REF rates for conversion: {market_data_currency}->{order_currency}"
                )

                conversion_rate = self.get_ecb_conv_rate(
                    order_df=group, ric_curr=market_data_currency
                )

                return conversion_rate

            else:
                logger_.info(
                    f"Using market_data fallback for conversion. Conversion date: {event_day} from"
                    f" {market_data_currency} (market currency) -> "
                    f"{order_currency} (order currency)"
                )

                converted_ric_marketdata_12b_days = self.get_market_data_for_pnl_conversion(
                    ric_currency=market_data_currency,
                    order_currency=order_currency,
                    event_day=event_day,
                )

                if converted_ric_marketdata_12b_days.empty:
                    logger_.warning(
                        f"Fallback using market_data for conversion "
                        f"has failed for Conversion date: {event_day}"
                    )
                    conversion_rate = pd.NA

                else:
                    date_mask = (
                        converted_ric_marketdata_12b_days[EoDStatsColumns.DATE].dt.date
                        <= event_day.date()
                    )
                    final_date = converted_ric_marketdata_12b_days.loc[
                        date_mask, EoDStatsColumns.DATE
                    ].max()

                    if pd.isna(final_date):
                        conversion_rate = pd.NA

                    else:
                        conversion_rate = converted_ric_marketdata_12b_days.loc[
                            converted_ric_marketdata_12b_days[EoDStatsColumns.DATE] == final_date,
                            EoDStatsColumns.CLOSE_PRICE,
                        ].values[0]

                return conversion_rate

        else:
            return 1

    def get_ecb_conv_rate(self, order_df: pd.DataFrame, ric_curr: str) -> float | NAType:
        """Get the conversion rate using ecb ref rates present in the order
        data.

        :param order_df: order DataFrame
        :param ric_curr: currency of the RIC for which conversion rate has to be fetched
        :return: conversion rate for ric_curr -> order_curr
        """
        if (
            OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE in order_df.columns
            and ".".join([OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE, ric_curr])
            in order_df.columns
        ):
            tx_vol_order_curr = order_df[OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE].values[0]
            tx_vol_ric_curr = order_df[
                ".".join([OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE, ric_curr])
            ].values[0]

            if pd.isna(tx_vol_ric_curr) or tx_vol_ric_curr == 0:
                logger_.warning(
                    f"{'.'.join([OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE, ric_curr])} "
                    f"has a value equal to 0 or null"
                )
                return pd.NA

            return tx_vol_order_curr / tx_vol_ric_curr  # type: ignore
        else:
            return pd.NA

    def get_market_data_for_pnl_conversion(
        self, ric_currency: str, order_currency: str, event_day: pd.Timestamp
    ) -> pd.DataFrame:
        curr_conv_ric = get_ric_for_currency_conversion(ccy_1=ric_currency, ccy_2=order_currency)
        converted_ric_marketdata: pd.DataFrame = self.get_curr_conv_market_data(
            base_curr=ric_currency, quote_curr=order_currency, conv_ric=curr_conv_ric
        )
        if not converted_ric_marketdata.empty:
            event_day_df = processment.get_market_data_for_day(  # type: ignore[attr-defined]
                market_data=converted_ric_marketdata, day_of_event=event_day, backward_looking=True
            )
            converted_ric_marketdata = converted_ric_marketdata.iloc[
                event_day_df.index.values[0] - 11 : event_day_df.index.values[0] + 1, :
            ]

        return converted_ric_marketdata

    def get_curr_conv_market_data(
        self, base_curr: str, quote_curr: str, conv_ric: str
    ) -> pd.DataFrame:
        """Fetch currency conversion market data.

        :param base_curr: currency to be converted from
        :param quote_curr: currency to be converted to
        :param conv_ric: ric for currency conversion
        :return: DataFrame containing columns required for fetching conversion rates
        """
        required_cols = [EoDStatsColumns.DATE, EoDStatsColumns.CLOSE_PRICE]
        market_data = self.get_market_data_for_currency_conversion(
            base_ccy=base_curr, filter_currency=quote_curr, conv_ric=conv_ric
        )

        if pd.Series(required_cols).isin(market_data.columns).all():
            return market_data.loc[:, required_cols]
        else:
            return pd.DataFrame()

    def get_market_data_for_currency_conversion(
        self, base_ccy: str, filter_currency: str, conv_ric: str
    ) -> pd.DataFrame:
        """Get the market data for currency conversion, the RIC is different.

        :param base_ccy: base currency from which we want to convert
        :param filter_currency: currency that we want to convert to
        :return:
        """
        inverted_rate: bool = currency_inversion_check(
            base_currency=base_ccy, quote_currency=filter_currency
        )

        try:
            market_data_stats: pd.DataFrame = self.queries.get_market_data_by_instrument_or_ric(
                mapped_ric=conv_ric
            )

            if inverted_rate:
                market_data_stats[EoDStatsColumns.CLOSE_PRICE] = (
                    1 / market_data_stats[EoDStatsColumns.CLOSE_PRICE]
                )

        except Exception:
            logger_.info(f"Fetch Market data for {conv_ric} failed")
            return pd.DataFrame()

        return market_data_stats

    def get_market_close_price_and_currency_fallback(
        self, ric: str, instrument: str, event_day: pd.Timestamp
    ) -> Tuple[float | NAType, str]:
        """Get close price and market data currency as fallback if market data
        events are empty.

        :param event_day:
        :param instrument: str, ISIN of instrument
        :param ric:
        :return: tuple with close price & currency with this order
        """

        fallback_market_data: pd.DataFrame = self.queries.get_market_data_by_instrument_or_ric(
            mapped_ric=ric, instrument=instrument
        )

        if fallback_market_data.empty:
            logger_.info(f"There isn't market data available for instrument {instrument}.")

            return pd.NA, ""

        market_data_for_event_day: pd.DataFrame = processment.get_market_data_for_day(  # type: ignore[attr-defined]
            market_data=fallback_market_data,
            day_of_event=event_day.tz_localize(None),
            backward_looking=True,
        )

        if market_data_for_event_day.empty:
            logger_.info(f"There isn't market data available for day {event_day}.")

            return pd.NA, ""

        close_price: float = processment.get_close_price_to_analyse(  # type: ignore[attr-defined]
            data_to_fetch_from=market_data_for_event_day
        )

        currency: str = fallback_market_data.loc[:, EoDStatsColumns.CURRENCY].unique()[0]

        return close_price, currency

    def create_alert(
        self,
        alert_data: pd.DataFrame,
        event_day: pd.Timestamp,
        event_day_close_price: float,
        event_day_close_price_converted: float,
        market_close_currency: str,
        instrument: str,
        behaviour_confidence_interval: Dict,
        event_type: str,
        observation_period_net_amount: float,
        pnl: float,
        evaluation_id: str | None = None,
        positions_data: Tuple | None = None,
        news_feed_events: List[NewsFeedEvent] = [],
        market_data_events: List[MarketDataEvent] = [],
    ) -> Dict:
        """Uses alert data and other inputs to generate alerts.

        :param event_day_close_price:
        :param event_day: timestamp with the event day for the alert
        :param alert_data: dataframe with order states of the alert
        :param market_close_currency: currency used at market close
        :param instrument: instrument id code or underlying isin
        :param behaviour_confidence_interval: dict with lower, upper bounds of the behaviour period
        :param event_type: Type of event that created the alert (Market, News or Both)
        :param observation_period_net_amount: float of the amount traded in the observation period
        :param pnl: float of pnl for the observation period
        :param evaluation_id: evaluation id that was used for grouping
        :param positions_data: tuple with positions and relative values of positions
        :param news_feed_events: list of news feed events which led to alert creation
        :param market_data_events: list of market data events which led to alert creation
        :return: does not return just append a scenario to a class var
        """

        event_day_details = {
            EventDayDetailsEnum.DATE: event_day.date(),
            EventDayDetailsEnum.PRICE_OF_EVENT_DAY: event_day_close_price,
        }

        alert_data = alert_data.reset_index(drop=True)
        alert: Dict = alert_data.to_dict(orient="list")

        if event_type != EventCreation.MARKET_DATA.value:
            alert, event_day_details = alerts.create_alert_news_feed_events_fields(  # type: ignore
                alert=alert,
                event_day_details=event_day_details,  # type: ignore
                instrument=instrument,
                news_feed_events=news_feed_events,
            )

        if event_type != EventCreation.NEWS_FEED.value:
            alert, event_day_details = alerts.create_alert_market_data_events_fields(  # type: ignore
                alert=alert,
                event_day_details=event_day_details,  # type: ignore
                instrument=instrument,
                market_data_events=market_data_events,
            )

        alert[AlertColumnsEnum.MARKET_CLOSE_PRICE] = event_day_close_price
        alert[AlertColumnsEnum.MARKET_CLOSE_PRICE_CONVERTED] = event_day_close_price_converted
        alert[AlertColumnsEnum.MARKET_CLOSE_PRICE_CONVERSION_RATE] = (
            event_day_close_price_converted / event_day_close_price
        )

        alert[AlertColumnsEnum.MARKET_CLOSE_CURRENCY] = market_close_currency

        alert[AlertColumnsEnum.EVENT_DAY] = event_day

        alert[AlertColumnsEnum.EVENT_DATE] = event_day.date()
        run_date = event_day + pd.Timedelta(1, unit="day")
        alert[AlertColumnsEnum.RUN_DATE] = run_date.date()

        order_state_range = dates.get_order_state_date_range(  # type: ignore[attr-defined]
            event_day=event_day,
            observation_period=self.thresholds.activityObservationPeriod,
            behaviour_period=self.thresholds.activityBehaviourPeriod,
            intraday_surveillance=self.thresholds.intraDaySurveillance,
        )

        observation_date_range: Dict[str, pd.Timestamp] = dates.determine_observation_period(  # type: ignore[attr-defined]  #noqa: E501
            event_day=event_day,
            observation_period=self.thresholds.activityObservationPeriod,
            intraday_surveillance=self.thresholds.intraDaySurveillance,
        )

        alert[AlertColumnsEnum.BEHAVIOUR_PERIOD_START] = order_state_range.get(
            DateRangeParameters.START
        )

        alert[AlertColumnsEnum.BEHAVIOUR_PERIOD_END] = observation_date_range.get(  # type: ignore
            DateRangeParameters.START
        ) - pd.Timedelta(1, unit="s")

        alert[AlertColumnsEnum.OBSERVATION_PERIOD_START] = observation_date_range.get(
            DateRangeParameters.START
        )

        alert[AlertColumnsEnum.OBSERVATION_PERIOD_END] = observation_date_range.get(
            DateRangeParameters.END
        )
        if behaviour_confidence_interval:
            alert[AlertColumnsEnum.BEHAVIOUR_PERIOD] = tuple(
                (
                    behaviour_confidence_interval.get(DateRangeParameters.START),
                    behaviour_confidence_interval.get(DateRangeParameters.END),
                )
            )
        if AlertColumnsEnum.CURRENCY_CONVERSION_RATE in alert_data.columns:
            alert[AlertColumnsEnum.CURRENCY_CONVERSION_RATE] = (
                alert_data[AlertColumnsEnum.CURRENCY_CONVERSION_RATE].unique().tolist()[0]
            )

        alert[AlertColumnsEnum.CURRENCY] = self.thresholds.currencyFilter

        alert[AlertColumnsEnum.EVALUATION_ID] = evaluation_id

        alert[AlertColumnsEnum.EVALUATION_TYPE] = self.thresholds.evaluationType.value

        if event_type == EventCreation.ANY.value:
            if (
                AlertColumnsEnum.MARKET_CLOSE_PRICE_VARIATION in alert.keys()
                and AlertColumnsEnum.NEWS_HEADLINE not in alert.keys()
            ):
                alert[AlertColumnsEnum.EVENT_TYPE] = EventCreation.MARKET_DATA.value
            elif (
                AlertColumnsEnum.MARKET_CLOSE_PRICE_VARIATION not in alert.keys()
                and AlertColumnsEnum.NEWS_HEADLINE in alert.keys()
            ):
                alert[AlertColumnsEnum.EVENT_TYPE] = EventCreation.NEWS_FEED.value
            else:
                alert[AlertColumnsEnum.EVENT_TYPE] = EventCreation.BOTH.value

        else:
            alert[AlertColumnsEnum.EVENT_TYPE] = event_type

        alert[AlertColumnsEnum.EXECUTION_DETAILS] = alert_data.loc[
            :, [OrderField.META_KEY, OrderStateValueColumns.ORDER_STATE_VALUE]
        ].to_dict(orient="records")

        alert[AlertColumnsEnum.EVENT_DAY_DETAILS] = event_day_details

        alert[AlertColumnsEnum.OBSERVATION_PERIOD] = observation_period_net_amount

        observation_data: pd.DataFrame = filter_df_by_timestamps(
            data=alert_data,
            timestamp_column=OrderField.TS_TRADING_DATE_TIME,
            date_range=observation_date_range,
        )

        # When intra day is activated and there are news, the records need to be separated
        # before and after the earliest of piece of news
        if self.thresholds.intraDaySurveillance and alert.get("newsHeadline", None) is not None:
            news_data = alert.get("newsHeadline", None)

            earliest_timestamp = min(
                pd.Timestamp(item["storyCreated"]).replace(microsecond=0, tzinfo=None)
                for item in news_data
            )

            after_earliest_news_mask = (
                alert_data.loc[:, OrderField.TS_TRADING_DATE_TIME] > earliest_timestamp
            )

            alert[AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS_POST_EVENT] = (
                observation_data.loc[after_earliest_news_mask, OrderField.META_KEY]
                .drop_duplicates()
                .tolist()
            )

            alert[AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS] = (
                observation_data.loc[~after_earliest_news_mask, OrderField.META_KEY]
                .drop_duplicates()
                .tolist()
            )
        else:
            alert[AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS_POST_EVENT] = []

            alert[AlertColumnsEnum.OBSERVATION_PERIOD_EXECUTIONS] = (
                observation_data.loc[:, OrderField.META_KEY].drop_duplicates().tolist()
            )
        observation_period_executions_keys_mask: pd.Series = alert_data.loc[
            :, OrderField.META_KEY
        ].isin(observation_data.loc[:, OrderField.META_KEY].drop_duplicates().tolist())

        alert[AlertColumnsEnum.BEHAVIOUR_PERIOD_EXECUTIONS] = (
            alert_data.loc[~observation_period_executions_keys_mask, OrderField.META_KEY]
            .drop_duplicates()
            .tolist()
        )

        alert[AlertColumnsEnum.PNL] = pnl

        if PNLColumns.PNL_VALUE_ORIGINAL in alert_data.columns:
            alert[AlertColumnsEnum.PNL_ORIGINAL] = (
                alert_data[PNLColumns.PNL_VALUE_ORIGINAL].unique().tolist()[0]
            )

        if OrderStateValueColumns.ORDER_STATE_VALUE in alert_data.columns:
            alert[AlertColumnsEnum.ORIGINAL_ORDER_STATE_VALUE] = calculate_net_amount(
                data=observation_data, column_name=OrderStateValueColumns.ORDER_STATE_VALUE
            )

        if OrderStateValueColumns.CONVERTED_ORDER_STATE_VALUE in alert_data.columns:
            alert[AlertColumnsEnum.ORDER_STATE_VALUE] = calculate_net_amount(
                data=observation_data,
                column_name=OrderStateValueColumns.CONVERTED_ORDER_STATE_VALUE,
            )

        alert[AlertColumnsEnum.UNDERLYING_ISIN] = instrument

        alert[AlertColumnsEnum.RIC] = None
        if AlertColumnsEnum.RIC in alert_data.columns:
            rics = alert_data[AlertColumnsEnum.RIC].unique()
            alert[AlertColumnsEnum.RIC] = rics[0] if len(rics) > 0 else None

        if positions_data is not None:
            relative_val, positions_val, positions_date = positions_data
            alert[AlertColumnsEnum.POSITIONS_VALUE] = positions_val
            alert[AlertColumnsEnum.RELATIVE_ACTIVITY] = relative_val
            alert[AlertColumnsEnum.POSITIONS_DATE] = positions_date

        result: dict = dict(
            (key, alert[key]) for key in AlertColumnsEnum.get_cols_to_alert() if key in alert
        )

        return result

    def calculate_transaction_volume(self, data: pd.DataFrame, currency: str) -> Optional[float]:
        """Method to calculate the transaction volume to check with sentiment
        threshold.

        :param data: pd.DataFrame with data to calculate transaction volume
        :param currency: currency to get the transaction volume column
        :return:
        """
        transaction_volume_column = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(currency)
        if transaction_volume_column not in data.columns:
            logger_.info(f"{transaction_volume_column} field not in columns.")
            return None

        put_options_flag = False

        # Verifies if there is PUT options in the dataframe
        if OrderField.INST_DERIV_OPTION_TYPE in data.columns:
            put_options_mask = data.loc[:, OrderField.INST_DERIV_OPTION_TYPE] == OptionType.PUT
            put_options_flag = any(put_options_mask)

        # If there is PUTs, the PUTs records need to be inverted amounts when
        # compared to other asset types
        if put_options_flag:
            buy_put_options: float = (
                data.loc[
                    put_options_mask & (data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY),
                    transaction_volume_column,
                ].sum()
                * -1
            )  # Invert value as it is a PUT buy

            sell_put_options: float = data.loc[
                put_options_mask & (data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL),
                transaction_volume_column,
            ].sum()  # Doesn't invert as other Sells as it is a PUT

            buys_volume: float = data.loc[
                ~put_options_mask & (data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY),
                transaction_volume_column,
            ].sum()
            sells_volume: float = (
                data.loc[
                    ~put_options_mask & (data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL),
                    transaction_volume_column,
                ].sum()
                * -1  # invert the sells so that we end up with a net volume
            )

            return buys_volume + sells_volume + buy_put_options + sell_put_options

        # Normal transaction volume logic when no PUTs are being surveilled
        else:
            buys_volume = data.loc[
                data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY,
                transaction_volume_column,
            ].sum()
            sells_volume = (
                data.loc[
                    data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL,
                    transaction_volume_column,
                ].sum()
                * -1  # invert the sells so that we end up with a net volume
            )

            return buys_volume + sells_volume

    def check_events_order_states_direction(
        self,
        transaction_volume: float | None,
        instrument: str,
        observation_amount: float,
        behaviour_series: pd.Series,
        event_day: Optional[pd.Timestamp] = None,
    ) -> Tuple[Dict, str | None, Dict | None]:
        """Verifies if the direction of the events is the same of the
        observation and behaviour periods.

        :param event_day: event day to log in StepAudit
        :param list_of_order_ids: list of orders for StepAudit
        :param list_of_instruments: list of instruments for StepAudit
        :param transaction_volume: transaction volume of the instrument
        :param instrument: instrument that is creating the alert
        :param observation_amount: value of the net trade amount for the observation period
        :param behaviour_series: series with values of net trade amount for each behaviour segment
        :return: dict with 2 keys, one alertCreation that says if it is to create alert and
        another with event direction
        """

        # this list of events will be instrument specific and can
        # have a maximum number of one event of each type
        market_data_events = []
        news_feed_events = []
        news_feed_events_to_remove = []
        audit_key, audit_data = None, None

        events_direction_result: Dict = addict.Dict(
            {
                EventResultDict.CREATE_ALERT: False,
                EventResultDict.EVENT_DIRECTION: None,
                EventResultDict.MESSAGE: None,
            }
        )

        if instrument in self.instruments_with_market_data_events:
            market_data_events = [
                event for event in self.market_data_events if event.get_instrument() == instrument
            ]

        if instrument in self.instruments_with_news_feed_events:
            if transaction_volume is not None and transaction_volume != 0:
                sentiment_column = (
                    RefinitivNewsColumns.SENTIMENT_NEGATIVE
                    if transaction_volume < 0
                    else RefinitivNewsColumns.SENTIMENT_POSITIVE
                )
                for event in self.news_feed_events:
                    if event.get_instrument() == instrument:
                        news_data = event.get_news()

                        news_filter_sentiment = news_data[
                            news_data[sentiment_column] > self.thresholds.sentiment
                        ]

                        if not news_filter_sentiment.empty:
                            event._news_feed = news_filter_sentiment
                            news_feed_events.append(event)
                        else:
                            logger_.info(
                                f"News event for stories "
                                f"{news_data[RefinitivNewsColumns.STORY_ID].tolist()} did not pass "
                                f"the sentiment threshold. Removing from news events."
                                f" Sentiment threshold: {self.thresholds.sentiment}."
                                f" News sentiments:"
                                f" {news_data[sentiment_column].unique().tolist()}. "
                                f"Event day {event_day}"
                            )
                            news_feed_events_to_remove.append(event)

        # checking only for news because we are only updating the news events in memory
        # we could end up with no event in case no article passes the sentiment threshold
        if (
            self.thresholds.eventCreation == EventCreation.NEWS_FEED
            or self.thresholds.eventCreation == EventCreation.BOTH
        ) and not news_feed_events:
            audit_key = InsiderTradingV3RefinitivAuditName.NEWS_SENTIMENT_BELOW_THRESHOLD
            audit_data = dict(
                instrument=instrument,
                sentiment_threshold=self.thresholds.sentiment,
                event_day=event_day,
            )
            logger_.info(
                f"The event creation ({self.thresholds.eventCreation}) requires at "
                f"least one news event. Event day {event_day}",
            )
            events_direction_result[EventResultDict.MESSAGE] = (
                EventResultDict.EVENT_CREATION_THRESHOLD
            )
            return events_direction_result, audit_key, audit_data

        all_events = [*market_data_events, *news_feed_events]
        events_direction: list = list(
            filter(None, set([event.get_direction() for event in all_events]))
        )

        if not events_direction:
            audit_key = InsiderTradingV3RefinitivAuditName.NULL_EVENT_DIRECTION
            audit_data = dict(event_day=event_day, instrument=instrument)
            logger_.info(
                f"No event directions to analyse for instrument "
                f"{instrument}. Event day {event_day}",
            )

            events_direction_result[EventResultDict.MESSAGE] = EventResultDict.NO_DIRECTION
            return events_direction_result, audit_key, audit_data

        events_direction_result = analyse_event_direction(
            market_data_events=market_data_events,
            news_feed_events=news_feed_events,
            behaviour_series=behaviour_series,
            observation_amount=observation_amount,
            events_direction=events_direction,
            event_direction_default=events_direction_result,
        )

        if not events_direction_result.get(EventResultDict.CREATE_ALERT):
            audit_key = InsiderTradingV3RefinitivAuditName.EVENT_DIRECTION_ANALYSIS
            audit_data = dict(
                analysis_msg=events_direction_result.get(EventResultDict.MESSAGE),
                observation_amount=observation_amount,
                direction=events_direction_result.get(EventResultDict.EVENT_DIRECTION),
                behaviour_mean=behaviour_series.mean(),
                event_day=event_day,
            )
            logger_.info(
                f"{events_direction_result.get(EventResultDict.MESSAGE)} "
                f"Observation amount: {observation_amount}. "
                f"Event direction: "
                f"{events_direction_result.get(EventResultDict.EVENT_DIRECTION)}. "
                f"Behaviour mean: {behaviour_series.mean()}. Event day {event_day}"
            )
        events_direction_result[EventKeys.MARKET_DATA_EVENTS] = market_data_events
        events_direction_result[EventKeys.NEWS_FEED_EVENTS] = news_feed_events
        return events_direction_result, audit_key, audit_data

    def _write_result_to_ndjson(self, **kwargs):
        alerts_df: pd.DataFrame = kwargs.get("alerts_df", pd.DataFrame())
        if alerts_df.empty:
            return
        with open(self.result_local_file_path, "a") as output_alerts:
            output_alerts.write(alerts_df.to_json(date_format="iso", orient="records", lines=True))
