# mypy: disable-error-code="no-redef, attr-defined"
import datetime
import pandas as pd
import time
from front_running_v2_apply_strategy.static import (
    DFColumns,
    EvaluationType,
    Flow,
    ThresholdsNames,
)
from mar_utils.auditor.strategy_audits.front_running_v2_audit import FrontRunningV2AuditName
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from market_abuse_algorithms.utils.processment import (
    new_instrument_combination_with_rics,
)
from typing import List, Union


class Queries(BaseQuery):  # type: ignore[misc]
    # 10 days of lookback to calculate ADV
    MARKET_ADV_WINDOW = 20

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_adv = self.context.thresholds.dict().get(ThresholdsNames.ADV)
        self._th_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )
        self._th_flow = self.context.thresholds.dict().get(ThresholdsNames.FLOW)
        self._th_time_window_frontrunning_execution = self.context.thresholds.dict().get(
            ThresholdsNames.TIME_WINDOW_FRONTRUNNING_EXECUTION
        )
        self._th_cross_product_activity = self.context.thresholds.dict().get(
            ThresholdsNames.CROSS_PRODUCT_ACTIVITY
        )

        self._market_data_client = get_market_client(tenant=context.tenant)
        self.START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        self.auditable_list: list[dict] = []

    def get_algo_groups(self) -> list[tuple]:
        """Create instrument ric groups which is used in algo run."""
        query = self._get_orders_query()
        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(
            query=query,
            agg_script=QueryAggScripts.EXT_DERIVATIVE_UNDERLYING_INSTRUMENTS_PRIORITY,
            add_to_total_counts=True,
        )

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = self._market_data_client.get_ric_map(  # noqa: E501
            instrument_combinations=instruments_combinations
        )

        new_instrument_combinations_with_ric: List = new_instrument_combination_with_rics(
            instrument_combinations=instruments_combinations,
            instrument_ric_mapping=instrument_id_ric_mapping,
            instrument_order_count_map=self._total_records_analyzed,
        )

        return new_instrument_combinations_with_ric

    def get_cases_to_analyse_based_on_instrument_combinations(
        self, new_instrument_combinations_with_ric: list[tuple]
    ):
        """Fetch order data for the instrument ric combination."""
        for instruments_and_rics in [new_instrument_combinations_with_ric]:
            inst_comb = [inst for inst, ric, order_count in instruments_and_rics]
            inst_ric_map = {inst: ric for inst, ric, order_count in instruments_and_rics}
            start = time.perf_counter()
            query = self._get_orders_query(inst_comb=inst_comb)
            query = self.get_initial_query(query)

            results = self._sdp_repository.search_after_query(query=query)

            if results.empty:
                continue

            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )

            original_meta_keys = results.loc[:, OrderField.META_KEY].tolist()

            results: pd.DataFrame = self.filter_newos_by_child_executions_fields(
                orders_df=results,
                mode="filter",
                fields={
                    OrderField.EXC_DTL_ORD_STATUS: [
                        OrderStatus.FILL,
                        OrderStatus.PARF,
                    ]
                },
            )
            resulting_order_meta_keys = results.get(OrderField.META_KEY, pd.Series()).tolist()
            dropped = set(original_meta_keys) - set(resulting_order_meta_keys)

            if len(dropped) > 0:
                self.auditable_list.append(
                    dict(
                        audit_key=FrontRunningV2AuditName.FILL_OR_PARFS_MISSING,
                        audit_data=dict(),
                        number_of_input_orders=len(original_meta_keys),
                        number_of_resulting_orders=len(resulting_order_meta_keys),
                        list_of_order_ids=list(dropped),
                        list_of_instruments=inst_comb,
                    )
                )
            else:
                self.auditable_list.append(
                    dict(
                        audit_key=FrontRunningV2AuditName.NEWO_PRESERVED,
                        audit_data=dict(),
                        number_of_input_orders=len(original_meta_keys),
                        number_of_resulting_orders=len(original_meta_keys),
                    )
                )

            if results.empty:
                self._logger.info("No orders were fetched from ES that respect the algo filters")
                continue

            pre_result_keys = results.get(OrderField.META_KEY, pd.Series()).tolist()

            try:
                results[DFColumns.RIC] = results[OrderField.INST_EXT_UNIQUE_IDENT].map(inst_ric_map)
                self.auditable_list.append(
                    dict(
                        audit_key=FrontRunningV2AuditName.INSTRUMENT_COL_PRESENT,
                        audit_data=dict(col=OrderField.INST_EXT_UNIQUE_IDENT),
                        number_of_input_orders=len(pre_result_keys),
                        number_of_resulting_orders=len(pre_result_keys),
                    )
                )
            except KeyError:
                self.auditable_list.append(
                    dict(
                        audit_key=FrontRunningV2AuditName.INSTRUMENT_COL_ABSENT,
                        audit_data=dict(col=OrderField.INST_EXT_UNIQUE_IDENT),
                        number_of_input_orders=len(pre_result_keys),
                        number_of_resulting_orders=0,
                        list_of_order_ids=pre_result_keys,
                        list_of_instruments=inst_comb,
                    )
                )

                self._logger.exception(
                    f"RIC mapping failed because the dataframe does not have the "
                    f"{OrderField.INST_EXT_UNIQUE_IDENT} column. Skipping instruments {inst_comb}."
                )

                continue
            yield results

    def _get_orders_query(self, inst_comb: List[str] = None) -> OrderQuery:  # type: ignore[assignment]
        """Creates a query for retrieving Orders from ES.

        :param inst_comb: List of instruments to be fetched
        :return:
        """
        q = OrderQuery()

        required = [
            OrderField.DATE,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.PC_FD_INIT_QTY,
            OrderField.TS_ORD_SUBMITTED,
        ]

        includes = [
            *OrderField.get_instrument_fields(),
            OrderField.CLIENT_FILE_IDENTIFIER,
            OrderField.DATE,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.INST_FULL_NAME,
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.PC_FD_INIT_QTY,
            OrderField.TRADER_FILE_IDENTIFIER,
            OrderField.TRD_ALGO_FILE_IDENTIFIER,
            OrderField.TRD_ALGO_FIRM_DESKS_NAME,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.INST_EXT_UNDER_INST_INST_CODE,
            OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
            OrderField.INST_DERIV_OPTION_TYPE,
            OrderField.INST_CLASSIFICATION,
            # required for cross product
            OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency="USD"),
            OrderField.META_ID,
            OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
        ]
        if self._th_cross_product_activity:
            # required for cross product
            required.append(OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency="USD"))

        if self._th_flow == Flow.CLIENT_VS_CLIENT:
            required.extend([OrderField.CLIENT_FILE_IDENTIFIER])
            includes.extend([OrderField.TRX_DTL_TR_CAPACITY])
            required.extend([OrderField.TRX_DTL_TR_CAPACITY])

        elif self._th_flow == Flow.PROP_VS_CLIENT:
            includes.extend([OrderField.TRX_DTL_TR_CAPACITY])
            required.extend([OrderField.TRX_DTL_TR_CAPACITY])

        elif self._th_flow == Flow.PAD_VS_NON_PAD:
            includes.extend([OrderField.MAR_DTL_PAD])

        if self._th_evaluation_type == EvaluationType.DESK:
            includes.extend([OrderField.TRD_ALGO_FIRM_DESKS_NAME])
            required.extend([OrderField.TRD_ALGO_FIRM_DESKS_NAME])

        elif self._th_evaluation_type == EvaluationType.PORTFOLIO_MANAGER:
            required.extend([OrderField.TRD_ALGO_FILE_IDENTIFIER])

        elif self._th_evaluation_type == EvaluationType.TRADER:
            required.extend([OrderField.TRADER_FILE_IDENTIFIER])

        if self._th_time_window_frontrunning_execution:
            includes.extend([OrderField.TS_TRADING_DATE_TIME])
            required.extend([OrderField.TS_TRADING_DATE_TIME])

        for field in required:
            q.exists(field=field)

        q.includes(includes)

        if inst_comb:
            q.instrument_id(inst_comb, check_underlying=True)

        return q

    @staticmethod
    def _add_query_condition(query: OrderQuery, path: str, field: str) -> OrderQuery:
        """Static method for nested condition to Order query.

        :param query: Order Query instance
        :return:
        """
        query.add_condition(
            mode="filter",
            conditions=[
                {
                    "nested": {
                        "path": path,
                        "query": {"bool": {"filter": {"exists": {"field": field}}}},
                    }
                }
            ],
        )

        return query

    def fetch_order_executions(self, order_ids: List[str], dataframe: pd.DataFrame) -> pd.DataFrame:  # noqa: E501
        """Join executions and newos data into one dataframe.

        :param dataframe: pd.DataFrame. Newos data
        :param order_ids: list of newos ids
        :return: pd.DataFrame merged with newos and respective executions
        """

        executions_query = self._get_order_executions_query(orders_ids=order_ids)
        executions = self._sdp_repository.search_after_query(executions_query)

        if OrderField.ORD_IDENT_ID_CODE not in executions.columns:
            return pd.DataFrame()

        merged_data = pd.merge(
            dataframe, executions, on=OrderField.ORD_IDENT_ID_CODE, suffixes=("", "_y")
        )

        merged_data.drop(merged_data.filter(regex="_y$").columns.tolist(), axis=1, inplace=True)
        return merged_data

    def _get_order_executions_query(
        self,
        orders_ids: List[str],
    ) -> OrderExecutionsQuery:
        """Query necessary to obtain results used for calculated price average.

        :param orders_ids: lists of strings to be fetched
        :return: order state query
        """

        specific_fields = [
            OrderField.PC_FD_INIT_QTY,
            OrderField.PC_FD_PRICE,
            OrderField.PC_FD_TRD_QTY,
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.TS_TRADING_DATE_TIME,
        ]

        required_fields = [*specific_fields]
        include_fields = [*specific_fields]

        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.includes(include_fields)

        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=required_fields)

        query.order_id(orders_ids)

        return query

    def fetch_market_adv(self, inst_unique_identifier: str, instrument_ric: str) -> pd.DataFrame:
        """Fetches market data adv.

        :param inst_unique_identifier: str
        :param instrument_ric: str
        :return:
        """
        df = self._market_data_client.get_avg_daily_trading_volume(
            instrument_unique_identifier=inst_unique_identifier,
            instrument_ric=instrument_ric,
            time_series_col=DFColumns.DATE_ADV,
            adtv_column=DFColumns.MARKET_ADV,
            look_back_period=self.MARKET_ADV_WINDOW,
            min_periods=2,
            start_date=self.look_back_period_ts,
            end_date=self.market_data_end_date,
        )

        return df  # type: ignore[no-any-return]
