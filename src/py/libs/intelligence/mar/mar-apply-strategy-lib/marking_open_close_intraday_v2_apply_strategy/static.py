from enum import Enum
from market_abuse_algorithms.data_source.static.ref_data.trading_hours_report import (
    CoppClarkColumn,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField


class ThresholdsNames(str, Enum):
    BEHAVIOUR_TYPE = "behaviourType"
    RUN_TYPE = "runType"
    DIRECTIONALITY = "directionality"
    EVALUATION_TYPE = "evaluationType"
    MARKET_COMPARISON = "marketComparison"
    DAY_COMPARISON = "dayComparison"
    MINIMUM_NOTIONAL = "minimumNotional"
    MINIMUM_NOTIONAL_CURRENCY = "minimumNotionalCurrency"
    PRICE_IMPROVEMENT = "priceImprovement"
    LIMIT_PRICE_DIFFERENCE = "limitPriceDifference"
    TEN_DAY_COMPARISON = "tenDayComparison"
    TIME_WINDOW = "timeWindow"


class BehaviourType(str, Enum):
    MARKING_THE_CLOSE = "Marking the Close"
    MARKING_THE_FIXING = "Marking the Fixing"
    MARKING_THE_OPEN = "Marking the Open"


class RunType(str, Enum):
    ORDERS = "Orders"
    EXECUTIONS = "Executions"
    BOTH = "Both"


class Directionality(str, Enum):
    NET_TRANSACTIONS = "Net Transactions"
    ONLY_BUYS_OR_ONLY_SELLS = "Only Buys, or Only Sells"


class EvaluationType(str, Enum):
    CLIENT = "Client"
    DESK = "Desk"
    EXECUTING_ENTITY = "Executing Entity"
    PORTFOLIO_MANAGER = "Portfolio Manager"
    TRADER = "Trader"


class NotionalCurrency(str, Enum):
    USD = "USD"
    GBP = "GBP"
    JPY = "JPY"
    CHF = "CHF"
    EUR = "EUR"


class AlgoColumnsEnum:
    AVERAGE_LIMIT_STOP_PRICE = "averageLimitStopPrice"
    CFI = "CFI"
    PLACEMENT_PHASE = "vPlacementPhase"
    SUM_QUANTITY_BY_TS_DATE = "vQuantity"
    NET_SUM_QUANTITY_BY_TS_DATE = "vNetQuantity"
    SUM_ORDER_STATE_VALUE_BY_TS_DATE = "vNotional"
    NET_SUM_ORDER_STATE_VALUE_BY_TS_DATE = "vNetNotional"
    SCENARIO_DIRECTIONALITY = "vScenarioDirectionality"
    SCENARIO_VOLUME = "vScenarioVolume"
    MARKET_VOLUME_COMPARISON = "vMarketVolumeComparison"
    MARKET_COMPARISON = "marketComparison"
    RANGE_WINDOW_START = "range_window_start"
    RANGE_WINDOW_END = "range_window_end"
    TRADING_SESSION_MATCH = "coop_clark_match"
    RUN_TYPE = "runType"
    BEHAVIOUR_TYPE = "behaviourType"
    DATE = "date"
    MINIMUM_NOTIONAL_CURRENCY = "minimumNotionalCurrency"
    EVALUATION_TYPE = "evaluationType"
    EVALUATION_TYPE_ID = "evaluationID"
    DAY_COMPARISON = "dayComparison"
    PRICE_CHANGE_PERCENTAGE = "priceChangePercentage"
    LIMIT_PRICE_DIFFERENCE = "LimitPriceDifference"
    PREVIOUS_10_DAY_COMPARISON = "10dayComparison"
    WINDOW_START_TIME = "windowStartTime"
    WINDOW_END_TIME = "windowEndTime"
    MINIMUM_NOTIONAL_CURRENCY_RATE = str(OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE)
    INST_ID_CODE = str(OrderField.INST_ID_CODE)
    EXC_DTL_ORD_TYPE = str(OrderField.EXC_DTL_ORD_TYPE)
    TS_ORD_SUBMITTED = str(OrderField.TS_ORD_SUBMITTED)
    TS_TRADING_DATE_TIME = str(OrderField.TS_TRADING_DATE_TIME)
    EXC_DTL_BUY_SELL_IND = str(OrderField.EXC_DTL_BUY_SELL_IND)
    PC_FD_TRD_QTY = str(OrderField.PC_FD_TRD_QTY)
    PC_FD_INIT_QTY = str(OrderField.PC_FD_INIT_QTY)
    PC_FD_PRICE = str(OrderField.PC_FD_PRICE)
    EXC_DTL_LIMIT_PRICE = str(OrderField.EXC_DTL_LIMIT_PRICE)
    EXC_DTL_STOP_PRICE = str(OrderField.EXC_DTL_STOP_PRICE)
    TRX_DTL_PC_CCY = str(OrderField.TRX_DTL_PC_CCY)
    TRX_DTL_ULTIMATE_VENUE = str(OrderField.TRX_DTL_ULTIMATE_VENUE)
    ORDERS = "orders"
    ORDERS_STATE_KEYS = "orderStateKeys"
    RIC = "RIC"
    TS_TRADING_DATE = "tradingDate"
    UNDESIRED_ULTIMATE_VENUE = "undesired_ultimate_venue"
    WINDOW_END_PRICE = "windowEndPrice"
    WINDOW_START_PRICE = "windowStartPrice"

    @classmethod
    def get_scenario_columns(cls, currency_used: str):
        return [
            cls.RUN_TYPE,
            cls.BEHAVIOUR_TYPE,
            cls.DATE,
            cls.MARKET_COMPARISON,
            cls.MINIMUM_NOTIONAL_CURRENCY_RATE + "." + currency_used,
            cls.MINIMUM_NOTIONAL_CURRENCY,
            cls.EVALUATION_TYPE,
            cls.EVALUATION_TYPE_ID,
            cls.DAY_COMPARISON,
            cls.PLACEMENT_PHASE,
            cls.PRICE_CHANGE_PERCENTAGE,
            cls.LIMIT_PRICE_DIFFERENCE,
            cls.PREVIOUS_10_DAY_COMPARISON,
            cls.WINDOW_START_TIME,
            cls.WINDOW_END_TIME,
            cls.ORDERS,
            cls.ORDERS_STATE_KEYS,
            cls.WINDOW_END_PRICE,
            cls.WINDOW_START_PRICE,
        ]


EVALUATION_TYPE_GROUPING_MAP = {
    EvaluationType.DESK: OrderField.TRD_ALGO_FIRM_DESKS_ID,
    EvaluationType.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    EvaluationType.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
    EvaluationType.CLIENT: OrderField.CLIENT_FILE_IDENTIFIER,
    EvaluationType.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
}


class PlacementPhase(str, Enum):
    INTRA_DAY_AUCTION = "Intra-Day Auction"
    CLOSE = "Close"
    CLOSING_AUCTION = "Closing Auction"
    OPEN = "Open"
    OPENING_AUCTION = "Opening Auction"


class PriceChangeCategory(str, Enum):
    INCREASE = "Increase"
    DECREASE = "Decrease"
    NO_CHANGE = "No Change"


class TimezoneType(str, Enum):
    DST = "DST"
    UTC = "UTC"


TIMEZONE_START_MAP = {
    TimezoneType.DST: CoppClarkColumn.PHASE_STARTS_DST,
    TimezoneType.UTC: CoppClarkColumn.PHASE_STARTS_UTC,
}
TIMEZONE_ENDS_MAP = {
    TimezoneType.DST: CoppClarkColumn.PHASE_ENDS_DST,
    TimezoneType.UTC: CoppClarkColumn.PHASE_ENDS_UTC,
}
