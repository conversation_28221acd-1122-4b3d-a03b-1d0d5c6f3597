# ruff: noqa: E501
# mypy: disable-error-code="attr-defined, union-attr, assignment, arg-type, no-redef, return-value, unreachable, comparison-overlap"
import datetime
import logging
import numpy as np
import pandas as pd
from mar_utils.abstract.abstract_mar_apply_strategy import AbstractMarApplyStrategy
from mar_utils.auditor.strategy_audits.marking_open_close_intraday_v2_audit import (
    MarkingOpenCloseIntradayV2Audit,
    MarkingV2AuditName,
)
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    get_closest_tick,
    get_nearest_tick_data_from_submitted,
)
from market_abuse_algorithms.data_source.static.ref_data.trading_hours_report import (
    CoppClarkColumn,
    PhaseType,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    BuySell,
    NewColumns,
    OptionType,
    OrderField,
    OrderType,
    VenueCode,
)
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.utils.data import (
    get_unique_value_for_alert,
    is_option,
    set_time_thresholds,
)
from market_abuse_algorithms.utils.formulas import calculate_confidence_interval
from marking_open_close_intraday_v2_apply_strategy.alerts import (
    Scenario,
)
from marking_open_close_intraday_v2_apply_strategy.models import (
    Thresholds,
)
from marking_open_close_intraday_v2_apply_strategy.query import (
    Queries,
)
from marking_open_close_intraday_v2_apply_strategy.static import (
    EVALUATION_TYPE_GROUPING_MAP,
    TIMEZONE_ENDS_MAP,
    TIMEZONE_START_MAP,
    AlgoColumnsEnum,
    BehaviourType,
    Directionality,
    PlacementPhase,
    PriceChangeCategory,
    RunType,
    TimezoneType,
)
from marking_open_close_intraday_v2_apply_strategy.utils import (
    check_invalid_price_check_conditions_options,
    filter_by_null_column,
    filter_execs,
    get_unique_field,
    in_between,
    orders_or_execs_timestamp_column,
    price_change_percentage_category_definition,
    quantity_column,
)
from pandas._libs.tslibs.offsets import BDay
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from typing import Generator, List, Optional, Tuple


class ApplyStrategy(AbstractMarApplyStrategy):
    """Marking the Open / Close / Intraday V2.

    DESCRIPTION:
    This model identifies price manipulations made at a time of the day that creates
    a price benchmark, that could be used to price instruments or positions within
    the firm. It flags attempts of manipulation by a trader around these sensitive
    periods of time.
    """

    def __init__(self, **kwargs):
        super().__init__(
            mar_auditor_cls=MarkingOpenCloseIntradayV2Audit,
            queries_cls=Queries,
            thresholds_cls=Thresholds,
            scenario_cls=Scenario,
            **kwargs,
        )

        self.evaluation_type_column_name: str = EVALUATION_TYPE_GROUPING_MAP.get(
            self._thresholds.evaluationType
        )
        self.minimum_notional_currency_column: str = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(
            self._thresholds.minimumNotionalCurrency.value
        )

        self._timedelta: pd.Timedelta = set_time_thresholds(time_field=self._thresholds.timeWindow)

        self._run_orders: bool = self._thresholds.runType in [RunType.ORDERS, RunType.BOTH]
        self._run_executions: bool = self._thresholds.runType in [
            RunType.EXECUTIONS,
            RunType.BOTH,
        ]

        self.copp_clark_file: pd.DataFrame = self.queries.get_trading_hours_report_data()
        self.audit_data_list: list[dict] = []

    def _write_result_to_ndjson(self, **kwargs):
        result_df = kwargs["results_df"]
        with open(self.result_local_file_path, "a") as output_alerts:
            output_alerts.write(result_df.to_json(date_format="iso", orient="records", lines=True))

    def _apply_strategy(self):
        """Loop through the data and run the algorithm on each batch of
        data."""
        logging.debug(
            msg=f"Start getting data to analyse for {StrategyName.MARKING_OPEN_CLOSE_INTRADAY_V2}"
        )

        if self.copp_clark_file is None:
            logging.warning(msg="Copp Clark file data necessary for algorithm run")
            return

        self.group_data = self.get_group().to_pandas().to_dict(orient="records")

        for data in self.group_data:
            instrument_ric_combination = data.get("instrument_ric_combination", [])
            for (
                executions,
                orders,
                audit_data_list,
            ) in self.queries.get_cases_to_analyse_based_on_instrument_combinations(
                new_instrument_combinations_with_ric=instrument_ric_combination
            ):
                if executions.empty and orders.empty:
                    for audit_data in audit_data_list:
                        self.auditor.step_audit(**audit_data)
                    continue
                instrument_ids = [inst for inst, ric, order_count in instrument_ric_combination]
                total_record_count = sum(
                    [int(order_count) for inst, ric, order_count in instrument_ric_combination]
                )

                audit_positive_case = []
                if self._run_orders:
                    if orders.empty:
                        audit_data_list.append(
                            dict(
                                audit_key=MarkingV2AuditName.GROUP_DATA_ABSENT,
                                audit_data=dict(model="orders", run_type=self._thresholds.runType),
                                number_of_input_orders=total_record_count,
                                number_of_resulting_orders=0,
                                list_of_instruments=instrument_ids,
                            )
                        )
                        logging.warning("No orders were found")
                    else:
                        audit_positive_case.append(True)
                        self._process_steps(
                            orders=orders,
                            executions=executions,
                            is_orders=True,
                        )

                if self._run_executions:
                    if executions.empty:
                        audit_data_list.append(
                            dict(
                                audit_key=MarkingV2AuditName.GROUP_DATA_ABSENT,
                                audit_data=dict(
                                    model="executions", run_type=self._thresholds.runType
                                ),
                                number_of_input_orders=total_record_count,
                                number_of_resulting_orders=0,
                                list_of_instruments=instrument_ids,
                            )
                        )
                        logging.warning("No executions were found")
                    else:
                        audit_positive_case.append(True)
                        self._process_steps(
                            orders=None,
                            executions=executions,
                            is_orders=False,
                        )

                # Since all([]) is Truthy
                if audit_positive_case and all(audit_positive_case):
                    audit_data_list.append(
                        dict(
                            audit_key=MarkingV2AuditName.GROUP_DATA_PRESENT,
                            audit_data=dict(run_type=self._thresholds.runType),
                            number_of_input_orders=total_record_count,
                            number_of_resulting_orders=total_record_count,
                        )
                    )

                for audit_data in audit_data_list:
                    self.auditor.step_audit(**audit_data)

    def _process_steps(
        self, orders: Optional[pd.DataFrame], executions: pd.DataFrame, is_orders: bool
    ):
        """Process algorithm steps. Either with orders or executions.

        Args:
            orders (pd.DataFrame): The batch of orders to run the algo on
            executions (pd.DataFrame): The batch of executions to run the algo on
            is_orders (bool): Whether this run should focus on orders or on executions
        """
        data: pd.DataFrame = orders if is_orders else executions

        for group in self._step_2_grouping(data=data):
            dataset_step2 = self.match_trading_session(records=group, is_orders=is_orders)
            if dataset_step2 is None:
                logging.warning("No records remaining after matching trading session")
                continue

            if AlgoColumnsEnum.TRADING_SESSION_MATCH not in dataset_step2:
                logging.warning(
                    f"Cannot find session match column '{AlgoColumnsEnum.TRADING_SESSION_MATCH}' in previous step's output for group '{group}'"
                )
                continue

            if OrderField.ORD_IDENT_ID_CODE not in dataset_step2:
                logging.warning(
                    f"Cannot find order ID column '{OrderField.ORD_IDENT_ID_CODE}' group '{group}'"
                )
                self.auditor.step_audit(
                    audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_MISSING_COL_IN_GROUP,
                    audit_data=dict(col=AlgoColumnsEnum.TRADING_SESSION_MATCH),
                    number_of_input_orders=len(dataset_step2),
                    number_of_resulting_orders=0,
                    list_of_order_ids=dataset_step2.loc[:, OrderField.META_KEY].values.tolist(),
                )
                continue
            else:
                self.auditor.step_audit(
                    audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_MISSING_COL_IN_GROUP,
                    audit_data=dict(col=AlgoColumnsEnum.TRADING_SESSION_MATCH),
                    number_of_input_orders=len(dataset_step2),
                    number_of_resulting_orders=len(dataset_step2),
                )

            for dataset_step7 in self._steps_5_6_7_placement_phase(
                records=dataset_step2, is_orders=is_orders
            ):
                if dataset_step7.empty:
                    logging.warning("No records in intended placement phase")
                    continue
                for dataset_step8 in self._step_8(
                    records=dataset_step7,
                    is_orders=is_orders,
                    executions_df=filter_execs(records_df=dataset_step7, executions_df=executions)
                    if is_orders
                    else executions,
                ):
                    self.audit_data_list = []
                    result: Optional[pd.DataFrame] = self._step_9_10_11_12(
                        records=dataset_step8,
                        is_orders=is_orders,
                    )

                    for audit_data in self.audit_data_list:
                        self.auditor.step_audit(**audit_data)

                    if result is None:
                        logging.warning("Step 12 returned no records.")
                        continue

                    self.audit_data_list = []
                    result: Optional[pd.DataFrame] = (
                        self._step_13_14_16_17(order_data=result)
                        if is_orders
                        else self._step_15_16_17(executions_df=result)
                    )
                    for audit_data in self.audit_data_list:
                        self.auditor.step_audit(**audit_data)

                    if result is None:
                        logging.warning("Step 17 returned no records.")
                        continue

                    result: Optional[pd.DataFrame] = self._step_18(
                        records=result, is_orders=is_orders
                    )
                    if result is None:
                        logging.warning("Step 18 returned no records.")
                        continue

                    result: Optional[pd.DataFrame] = self._step_19(
                        records=result, is_orders=is_orders
                    )
                    if result is None:
                        logging.warning("Step 19 returned no records.")
                        continue

                    self._create_alert(alert_data=result, orders=orders, executions=executions)

    def _step_2_grouping(self, data: pd.DataFrame) -> Generator[pd.DataFrame, None, None]:
        """Run step 2 of the algorithm on a batch of data: Grouping.

        Args:
            data (pd.DataFrame): The batch of records to run the algo on

        Returns:
            Dataframes resulting from the step execution
        """
        # Check from step 2 (validation/logging)
        original_order_ids = data.loc[:, OrderField.META_KEY].values.tolist()
        processed_order_ids = []
        for col in [OrderField.TRX_DTL_ULTIMATE_VENUE, OrderField.EXC_DTL_BUY_SELL_IND]:
            data: pd.DataFrame = filter_by_null_column(df=data, column_name=col)
            processed_order_ids.extend(data.loc[:, OrderField.META_KEY].values.tolist())

            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.BUY_SELL_TRX_VENUE_DATA_ABSENT,
                audit_data=dict(col=col),
                number_of_input_orders=len(original_order_ids),
                number_of_resulting_orders=len(data),
                list_of_order_ids=list(set(original_order_ids) - set(processed_order_ids)),
            )

        # Step 2.1 of algo logic
        grouping_columns: List[str] = [self.evaluation_type_column_name]

        # Step 2.2 of algo logic
        if NewColumns.INSTRUMENT_CODE not in data.columns:
            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.FAILED_TO_RESOLVE_INST_CODE_COL,
                audit_data=dict(),
                number_of_input_orders=len(data),
                number_of_resulting_orders=0,
                list_of_order_ids=data.loc[:, OrderField.META_KEY].values.tolist(),
            )

            logging.debug(msg=f"Could not find column {NewColumns.INSTRUMENT_CODE} in data")
            return
        else:
            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.FAILED_TO_RESOLVE_INST_CODE_COL,
                audit_data=dict(),
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(data),
            )

        grouping_columns.append(NewColumns.INSTRUMENT_CODE)

        # Step 2.3 of algo logic
        data[AlgoColumnsEnum.UNDESIRED_ULTIMATE_VENUE] = data[
            OrderField.TRX_DTL_ULTIMATE_VENUE
        ].isin([VenueCode.XOFF, VenueCode.XXXX])
        original_order_ids = data.loc[:, OrderField.META_KEY].values.tolist()
        data: pd.DataFrame = filter_by_null_column(
            df=data, column_name=AlgoColumnsEnum.UNDESIRED_ULTIMATE_VENUE
        )
        processed_order_ids = data.loc[:, OrderField.META_KEY].values.tolist()
        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.UND_ULTIMATE_VENUE_DATA_ABSENT,
            audit_data=dict(col=col),
            number_of_input_orders=len(original_order_ids),
            number_of_resulting_orders=len(data),
            list_of_order_ids=list(set(original_order_ids) - set(processed_order_ids)),
        )

        grouping_columns.append(OrderField.TRX_DTL_ULTIMATE_VENUE)

        # Step 2.4 of algo logic
        if self._thresholds.directionality == Directionality.ONLY_BUYS_OR_ONLY_SELLS:
            grouping_columns.append(OrderField.EXC_DTL_BUY_SELL_IND)

        for name, group in data.groupby(by=grouping_columns):
            yield group

    def match_trading_session(
        self, records: pd.DataFrame, is_orders: bool
    ) -> Optional[pd.DataFrame]:
        """Run steps 3 & 4 of the algorithm to match the session with the Coop
        Clark file.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution
        """

        t_col = orders_or_execs_timestamp_column(is_orders=is_orders)

        # Generating CFI
        records[AlgoColumnsEnum.CFI] = records[OrderField.INST_CLASSIFICATION].map(
            ApplyStrategy._getCFI
        )
        original_order_ids = records.loc[:, OrderField.META_KEY].values.tolist()
        records: pd.DataFrame = filter_by_null_column(df=records, column_name=AlgoColumnsEnum.CFI)
        processed_order_ids = records.loc[:, OrderField.META_KEY].values.tolist()

        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.CFI_DATA_ABSENT,
            audit_data=dict(),
            number_of_input_orders=len(original_order_ids),
            number_of_resulting_orders=len(records),
            list_of_order_ids=list(set(original_order_ids) - set(processed_order_ids)),
        )
        if records.empty:
            logging.debug(msg="Dropping records due to no CFI being found.")
            return None

        # Matching Coop Clark entry
        matches = records.apply(
            lambda x: self.record_map_to_trading_session(row=x, timestamp_column=t_col),
            axis=1,
        )
        records = pd.concat([records, matches], axis=1)

        # Dropping records if no time window range can be constructed
        idn: pd.Series = (
            records[[AlgoColumnsEnum.RANGE_WINDOW_START, AlgoColumnsEnum.RANGE_WINDOW_END]]
            .isnull()
            .all(axis=1)
        )
        original_order_records = records.loc[:, OrderField.META_KEY].values.tolist()

        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.TRADING_SESSION_DATA_NOT_LINKED,
            audit_data=dict(),
            number_of_input_orders=len(original_order_records),
            number_of_resulting_orders=len(records[~idn]),
            list_of_order_ids=records[idn].loc[:, OrderField.META_KEY].values.tolist(),
        )
        if idn.any():
            logging.debug(
                msg=f"Dropping {idn.sum()} records due to no time window range being found."
            )
            records: pd.DataFrame = records[~idn]

            if records.empty:
                logging.info(
                    msg="After dropping records due to no time window range being found, dataframe is empty."
                )
                return None

        return records

    def link_session(
        self, timestamp: pd.Timestamp, row: pd.Series
    ) -> Tuple[Optional[pd.Timedelta], Optional[pd.Timedelta], str]:
        """Logic mentioned in step 3.

        Performs linkage between trading session with record using timestamps
        Tries first to match with DST and afterwards with UTC

        :param timestamp: timestamp of record to map session
        :param row: Series with data for one trading session
        :return: tuple with start and ending timestamps of trading session
        also returns phase type of trading session
        """

        session_matched = False

        dst_flag: bool = (
            row[CoppClarkColumn.DST_STARTS] <= timestamp <= row[CoppClarkColumn.DST_ENDS]
        )

        tz_type = TimezoneType.DST if dst_flag else TimezoneType.UTC

        # checking if it matches with DST columns
        phase_starts, phase_ends = self.session_matcher_dst_utc(
            row=row, timestamp=timestamp, tz_type=tz_type
        )

        if phase_starts is not None and phase_ends is not None:
            session_matched = True

        if session_matched:
            return (
                phase_starts,
                phase_ends,
                row[CoppClarkColumn.PHASE_TYPE],
            )

        return None, None, row[CoppClarkColumn.PHASE_TYPE]

    def record_map_to_trading_session(self, row: pd.Series, timestamp_column: str) -> pd.Series:
        """Logic for step 4 Fetch cop clark data and match records in trading
        sessions.

        :param row: data for a single record (order or execution)
        :param timestamp_column: column to select timestamp
        :return: Series with trading session, start timestamp and end timestamp
        """

        no_match: pd.Series = pd.Series(
            [None, None, None],
            index=[
                AlgoColumnsEnum.TRADING_SESSION_MATCH,
                AlgoColumnsEnum.RANGE_WINDOW_START,
                AlgoColumnsEnum.RANGE_WINDOW_END,
            ],
        )

        if pd.isnull(row[AlgoColumnsEnum.CFI]):
            logging.debug(
                msg=f"No instrument classification was found for the record: {row[OrderField.META_KEY]}"
            )
            return no_match

        # generates key and gets indices for compatible trading sessions
        if row[AlgoColumnsEnum.CFI] in ["FXXXXX", "OXXXXX"]:
            if OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT not in row.index:
                return no_match
            copp_clark_key: str = f"{row[OrderField.TRX_DTL_ULTIMATE_VENUE]}|{row[AlgoColumnsEnum.CFI]}|{row[OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT]}"
            trading_session_candidates: pd.Series = (
                self.copp_clark_file[CoppClarkColumn.KEY_DETAILED] == copp_clark_key
            )
        else:
            copp_clark_key: str = (
                f"{row[OrderField.TRX_DTL_ULTIMATE_VENUE]}|{row[AlgoColumnsEnum.CFI]}"
            )
            trading_session_candidates: pd.Series = (
                self.copp_clark_file[CoppClarkColumn.KEY_SUMMARY] == copp_clark_key
            )

        if ~trading_session_candidates.any():
            logging.debug(
                msg=f"No linkage can be made to Coop Clark file. No match found with '{copp_clark_key}'."
            )
            return no_match

        timestamp: pd.Timestamp = row[timestamp_column]

        timestamp_date = timestamp.date()

        candidate_matching_sessions: pd.DataFrame = self.copp_clark_file.iloc[
            trading_session_candidates[trading_session_candidates].index
        ]

        special_day_mask: pd.Series = (
            candidate_matching_sessions[CoppClarkColumn.CALENDAR_START].dt.date == timestamp_date
        ) & (candidate_matching_sessions[CoppClarkColumn.CALENDAR_END].isnull())

        if any(special_day_mask):
            trading_session_candidates = special_day_mask

        # for each trading session verifies if matches with record
        for trading_session_index in trading_session_candidates[trading_session_candidates].index:
            copp_clark_row: pd.Series = self.copp_clark_file.iloc[trading_session_index]
            linked_start, linked_end, phase_type = self.link_session(
                timestamp=timestamp, row=copp_clark_row
            )
            # specific case where it is running
            if (
                linked_start is not None
                and linked_end is not None
                and phase_type == PhaseType.DAY_TRADING_T0
                and timestamp_column == OrderField.TS_TRADING_DATE_TIME
            ):
                (
                    linked_start,
                    linked_end,
                ) = self.executions_only_session_phase_checker(
                    phase_start=linked_start,
                    phase_end=linked_end,
                    timestamp=timestamp,
                )

            if linked_start is not None and linked_end is not None:
                return pd.Series(  # type: ignore[no-any-return]
                    [trading_session_index, linked_start, linked_end],
                    index=no_match.index,
                )

        logging.debug(
            msg=f"No trading session was matched for the record: {row[OrderField.META_KEY]}"
        )
        return no_match

    @staticmethod
    def session_matcher_dst_utc(
        row: pd.Series, timestamp: pd.Timestamp, tz_type: TimezoneType
    ) -> Tuple[Optional[pd.Timedelta], Optional[pd.Timedelta]]:
        """Checks the timestamp is between the provided phase time range (First
        DST then UTC if it failed)

        :param row: phase data
        :param timestamp: timestamp of the record
        :param tz_type: type of timezone to use (DST or UTC)
        :return: tuple with start and end of the time range
        """

        phase_starts_field = TIMEZONE_START_MAP.get(tz_type)
        phase_ends_field = TIMEZONE_ENDS_MAP.get(tz_type)

        session_matched: bool = (
            (
                (
                    row[CoppClarkColumn.CALENDAR_START]
                    < timestamp
                    < row[CoppClarkColumn.CALENDAR_END]
                )
                | (
                    row[CoppClarkColumn.CALENDAR_START] < timestamp
                    and pd.isnull(row[CoppClarkColumn.CALENDAR_END])
                )
            )
            & (
                in_between(
                    timestamp=timestamp,
                    start=row[phase_starts_field],  # type: ignore[index]
                    end=row[phase_ends_field],  # type: ignore[index]
                    start_inclusive=False,
                    end_inclusive=False,
                )
            )
            & (
                (
                    row[CoppClarkColumn.WEEKDAY_START]
                    <= timestamp.weekday()
                    <= row[CoppClarkColumn.WEEKDAY_END]
                )
                | (
                    row[CoppClarkColumn.WEEKDAY_START] == timestamp.weekday()
                    and row[CoppClarkColumn.WEEKDAY_END] == -1
                )
            )
        )
        if session_matched:
            return row[phase_starts_field], row[phase_ends_field]  # type: ignore[index]

        return None, None

    def executions_only_session_phase_checker(
        self,
        phase_start: pd.Timestamp,
        phase_end: pd.Timestamp,
        timestamp: pd.Timestamp,
    ) -> Tuple[Optional[pd.Timestamp], Optional[pd.Timestamp]]:
        """Logic of step 4 for executions running when phase is Day trading
        (T+0)

        :param phase_start: phase start timestamp
        :param phase_end: phase end timestamp
        :param timestamp: timestamp of the record (trading date time or order submitted)
        :return: Tuple with timestamps for phase start and phase end
        """
        if (
            (self._thresholds.behaviourType == BehaviourType.MARKING_THE_CLOSE)
            and (
                in_between(
                    timestamp=timestamp,
                    start=phase_end - self._timedelta,
                    end=phase_end,
                    start_inclusive=False,
                    end_inclusive=False,
                )
            )
        ) or (
            (self._thresholds.behaviourType == BehaviourType.MARKING_THE_OPEN)
            and (
                in_between(
                    timestamp=timestamp,
                    start=phase_start,
                    end=phase_start + self._timedelta,
                    start_inclusive=False,
                    end_inclusive=False,
                )
            )
        ):
            return phase_start, phase_end

        elif self._thresholds.behaviourType == BehaviourType.MARKING_THE_FIXING:
            return None, None

        logging.debug(msg="Failed to match the intra day trading session")
        return None, None

    @staticmethod
    def _getCFI(instr: str) -> str:
        if instr.startswith("CB"):
            return "CBXXXX"
        elif instr.startswith("CE"):
            return "CEXXXX"
        elif instr.startswith("C"):
            return "CXXXXX"
        elif instr.startswith("DS"):
            return "DSXXXX"
        elif instr.startswith("D"):
            return "DXXXXX"
        elif instr.startswith("ED"):
            return "EDXXXX"
        elif instr.startswith("E") or instr.startswith("JES"):
            return "EXXXXX"
        elif instr.startswith("JT") or instr.startswith("JEI") or instr.startswith("F"):
            return "FXXXXX"
        elif instr.startswith("O"):
            return "OXXXXX"
        elif instr.startswith("R"):
            return "RXXXXX"
        else:
            return None

    def _steps_5_6_7_placement_phase(
        self, records: pd.DataFrame, is_orders: bool
    ) -> Generator[pd.DataFrame, None, None]:
        """Run steps 5, 6 & 7 of the algorithm on a batch of data: Setting
        'AlgoColumnsEnum.PLACEMENT_PHASE' and grouping.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution
        """

        records[AlgoColumnsEnum.PLACEMENT_PHASE] = None
        records[CoppClarkColumn.PHASE] = None
        records[CoppClarkColumn.PHASE_TYPE] = None

        def placement_phase_selection(record: pd.DataFrame):
            # Step 5 of algo logic
            record = self._get_coop_phase_data_from_group(record=record)

            timestamp: datetime.datetime = pd.to_datetime(
                record.loc[orders_or_execs_timestamp_column(is_orders=is_orders)]
            )

            phase = record[CoppClarkColumn.PHASE]
            phase_type = record[CoppClarkColumn.PHASE_TYPE]
            phase_starts_utc = record[AlgoColumnsEnum.RANGE_WINDOW_START]
            phase_ends_utc = record[AlgoColumnsEnum.RANGE_WINDOW_END]

            placement_phase = None
            if (
                in_between(timestamp=timestamp, start=phase_starts_utc, end=phase_ends_utc)
                and phase == PhaseType.INTRADAY_AUCTION.lower()
            ):
                placement_phase = PlacementPhase.INTRA_DAY_AUCTION
            elif (
                in_between(
                    timestamp=timestamp,
                    start=phase_ends_utc - self._timedelta,
                    end=phase_ends_utc,
                )
                and phase_type == PhaseType.DAY_TRADING_T0.lower()
            ):
                placement_phase = PlacementPhase.CLOSE
            elif in_between(
                timestamp=timestamp, start=phase_starts_utc, end=phase_ends_utc
            ) and phase_type in [
                PhaseType.CLOSING_AUCTION_CALL.lower(),
                PhaseType.PRE_CLOSING.lower(),
            ]:
                placement_phase = PlacementPhase.CLOSING_AUCTION
            elif (
                in_between(
                    timestamp=timestamp,
                    start=phase_starts_utc,
                    end=phase_starts_utc + self._timedelta,
                )
                and phase_type == PhaseType.DAY_TRADING_T0.lower()
            ):
                placement_phase = PlacementPhase.OPEN
            elif in_between(
                timestamp=timestamp, start=phase_starts_utc, end=phase_ends_utc
            ) and phase_type in [
                PhaseType.OPENING_AUCTION_CALL.lower(),
                PhaseType.PRE_OPENING.lower(),
            ]:
                placement_phase = PlacementPhase.OPENING_AUCTION

            record[AlgoColumnsEnum.PLACEMENT_PHASE] = placement_phase
            return record

        records = records.apply(placement_phase_selection, axis=1)

        # Step 6 of algo logic
        if self._thresholds.behaviourType == BehaviourType.MARKING_THE_CLOSE:
            filter: List[BehaviourType] = [
                PlacementPhase.CLOSING_AUCTION,  # type: ignore[list-item]
                PlacementPhase.CLOSE,  # type: ignore[list-item]
            ]
        elif self._thresholds.behaviourType == BehaviourType.MARKING_THE_OPEN:
            filter: List[BehaviourType] = [
                PlacementPhase.OPENING_AUCTION,
                PlacementPhase.OPEN,
            ]
        elif self._thresholds.behaviourType == BehaviourType.MARKING_THE_FIXING:
            filter: List[BehaviourType] = [PlacementPhase.INTRA_DAY_AUCTION]
        else:
            raise ValueError(f"Unsupported behavior type '{self._thresholds.behaviourType}'")

        original_order_ids = records.loc[:, OrderField.META_KEY].values.tolist()
        records: pd.DataFrame = records[records[AlgoColumnsEnum.PLACEMENT_PHASE].isin(filter)]
        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_PLACEMENT_PHASE_FILTERS,
            audit_data=dict(),
            number_of_input_orders=len(original_order_ids),
            number_of_resulting_orders=len(records),
            list_of_order_ids=list(
                set(original_order_ids) - set(records.loc[:, OrderField.META_KEY].values.tolist())
            ),
        )
        if records.empty:
            logging.info(msg=f"No records found with a placement phase in '{filter}'.")
            yield pd.DataFrame()

        for name, group in records.groupby(by=[AlgoColumnsEnum.PLACEMENT_PHASE]):
            yield group

    def _step_8(
        self,
        records: pd.DataFrame,
        is_orders: bool,
        executions_df: Optional[pd.DataFrame] = None,
    ) -> Generator[pd.DataFrame, None, None]:
        """Run step 8 of the algorithm on a batch of data: Calculating
        'vQuantity' and 'vNotional'.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution
        """

        # Checking which quantity column to use in the step
        q_column: OrderField = quantity_column(is_orders=is_orders)

        # Check from validation/logging (OrderField.PC_FD_INIT_QTY or OrderField.PC_FD_TRD_QTY)
        original_order_ids = records.loc[:, OrderField.META_KEY].values.tolist()
        records: pd.DataFrame = filter_by_null_column(df=records, column_name=q_column)
        processed_order_ids = records.loc[:, OrderField.META_KEY].values.tolist()

        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_QTY_COL_DATA,
            audit_data=dict(col=q_column),
            number_of_input_orders=len(original_order_ids),
            number_of_resulting_orders=len(records),
            list_of_order_ids=list(set(original_order_ids) - set(processed_order_ids)),
        )

        if records.empty:
            logging.warning(msg=f"No records remaining after dropping records without {q_column}")
            return

        # Check from validation/logging (OrderField.INST_DERIV_OPTION_TYPE)
        options_mask: pd.Series = is_option(order_states_df=executions_df)
        if any(options_mask):
            options_df: pd.DataFrame = executions_df.loc[options_mask]
            options_df: pd.DataFrame = filter_by_null_column(
                df=options_df,
                column_name=OrderField.INST_DERIV_OPTION_TYPE,
            )
            if options_df.empty:
                logging.info(
                    msg=f"No field '{OrderField.INST_DERIV_OPTION_TYPE}' in options records found."
                )

        # Applying range from step 4
        range_window_start: pd.Timedelta = pd.to_timedelta(  # type: ignore[call-overload]
            get_unique_field(df=records, column=AlgoColumnsEnum.RANGE_WINDOW_START)
        )
        range_window_end: pd.Timedelta = pd.to_timedelta(  # type: ignore[call-overload]
            get_unique_field(df=records, column=AlgoColumnsEnum.RANGE_WINDOW_END)
        )
        timestamp_mask: pd.Series = records[
            orders_or_execs_timestamp_column(is_orders=is_orders)
        ].apply(lambda t: in_between(timestamp=t, start=range_window_start, end=range_window_end))
        records: pd.DataFrame = records[timestamp_mask]

        self.auditor.step_audit(
            audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_TIME_RANGE_FILTER,
            audit_data=dict(window_start=range_window_start, window_end=range_window_end),
            number_of_input_orders=len(records),
            number_of_resulting_orders=len(records[timestamp_mask]),
            list_of_order_ids=records.loc[~timestamp_mask, OrderField.META_KEY].values.tolist(),
        )

        if records.empty:
            logging.info(
                msg=f"No options records found between timerange: [{range_window_start}, {range_window_end})."
            )
            return

        for group in self._vquantity_computer(
            records=records, is_orders=is_orders, executions_df=executions_df
        ):
            yield group

    def _vquantity_computer(
        self,
        records: pd.DataFrame,
        is_orders: bool,
        executions_df: Optional[pd.DataFrame] = None,
    ) -> Generator[pd.DataFrame, None, None]:
        """Run core logic of step 8 of the algorithm on a batch of data:
        Calculating 'vQuantity' and 'vNotional' This logic is implemented
        separately to be able to be called from step 19.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution
        """

        if is_orders and (executions_df is None or executions_df.empty):
            logging.warning("Executions are necessary in order to compute quantity and notional")
            return

        # Checking which quantity column to use in the step
        q_column: OrderField = quantity_column(is_orders=is_orders)

        timestamp_column: OrderField = orders_or_execs_timestamp_column(is_orders=is_orders)

        records[AlgoColumnsEnum.TS_TRADING_DATE] = records[timestamp_column].apply(
            lambda x: pd.to_datetime(x).date()
        )

        # Running logic from the step
        if self._thresholds.directionality == Directionality.ONLY_BUYS_OR_ONLY_SELLS:
            for vScenarioDirectionality, group in records.groupby(
                by=[OrderField.EXC_DTL_BUY_SELL_IND]
            ):
                group[AlgoColumnsEnum.SCENARIO_DIRECTIONALITY] = vScenarioDirectionality[0]

                for name, grp in group.groupby(by=[AlgoColumnsEnum.TS_TRADING_DATE]):
                    quantity_orders_value: float = grp[q_column].sum()
                    grp[AlgoColumnsEnum.SUM_QUANTITY_BY_TS_DATE] = quantity_orders_value

                    filtered_executions: pd.DataFrame = (
                        filter_execs(records_df=grp, executions_df=executions_df)
                        if is_orders
                        else grp
                    )

                    orders_notional_value: float = filtered_executions.loc[
                        :, self.minimum_notional_currency_column
                    ].sum()
                    grp[AlgoColumnsEnum.SUM_ORDER_STATE_VALUE_BY_TS_DATE] = orders_notional_value

                    if (
                        self._thresholds.minimumNotional is not None
                        and orders_notional_value < self._thresholds.minimumNotional
                    ):
                        self.auditor.step_audit(
                            audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MIN_NOTIONAL_THRESHOLD,
                            audit_data=dict(
                                notional_value=orders_notional_value,
                                threshold_value=self._thresholds.minimumNotional,
                            ),
                            number_of_input_orders=len(grp),
                            number_of_resulting_orders=0,
                            list_of_order_ids=grp.loc[:, OrderField.META_KEY].values.tolist(),
                        )
                        logging.warning(
                            msg=f"The calculated value of net notional value: {orders_notional_value} was below the threshold value: {self._thresholds.minimumNotional}"
                        )

                        continue
                    else:
                        self.auditor.step_audit(
                            audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MIN_NOTIONAL_THRESHOLD,
                            audit_data=dict(
                                notional_value=orders_notional_value,
                                threshold_value=self._thresholds.minimumNotional,
                            ),
                            number_of_input_orders=len(grp),
                            number_of_resulting_orders=len(grp),
                        )

                    yield grp

        elif self._thresholds.directionality != Directionality.ONLY_BUYS_OR_ONLY_SELLS:
            for name, group in records.groupby(by=[AlgoColumnsEnum.TS_TRADING_DATE]):
                buys: pd.DataFrame = group[group[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY]
                sells: pd.DataFrame = group[group[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL]

                filtered_buy_executions: pd.DataFrame = (
                    filter_execs(records_df=buys, executions_df=executions_df)
                    if is_orders
                    else buys
                )

                filtered_sell_executions: pd.DataFrame = (
                    filter_execs(records_df=sells, executions_df=executions_df)
                    if is_orders
                    else sells
                )

                # Step 8 calculations when group has options
                options: pd.Series = group[OrderField.INST_CLASSIFICATION].str.startswith("O")

                if any(options):
                    put_mask = group[OrderField.INST_DERIV_OPTION_TYPE] == OptionType.PUT
                    call_other_mask = group[OrderField.INST_DERIV_OPTION_TYPE].isin(
                        [OptionType.CALL, OptionType.OTHR]
                    )

                    puto_buys = filtered_buy_executions.loc[options & put_mask]
                    puto_sells = filtered_sell_executions.loc[options & put_mask]

                    call_other_buys = filtered_buy_executions.loc[options & call_other_mask]
                    call_other_sells = filtered_sell_executions.loc[options & call_other_mask]

                    net_notional = (
                        call_other_buys[self.minimum_notional_currency_column].sum()
                        + puto_sells[self.minimum_notional_currency_column].sum()
                        - puto_buys[self.minimum_notional_currency_column].sum()
                        - call_other_sells[self.minimum_notional_currency_column].sum()
                    )

                    net_quantity = (
                        call_other_buys[q_column].sum()
                        + puto_sells[q_column].sum()
                        - puto_buys[q_column].sum()
                        - call_other_sells[q_column].sum()
                    )

                else:
                    buys_notional_sum: float = filtered_buy_executions.loc[
                        :, self.minimum_notional_currency_column
                    ].sum()
                    sells_notional_sum: float = filtered_sell_executions.loc[
                        :, self.minimum_notional_currency_column
                    ].sum()
                    buys_quantity_sum: float = buys[q_column].sum()
                    sells_quantity_sum: float = sells[q_column].sum()

                    net_quantity: float = buys_quantity_sum - sells_quantity_sum
                    net_notional: float = buys_notional_sum - sells_notional_sum

                vScenarioDirectionality: BuySell = BuySell.SELL if net_notional < 0 else BuySell.BUY
                group[AlgoColumnsEnum.SCENARIO_DIRECTIONALITY] = vScenarioDirectionality

                group[AlgoColumnsEnum.NET_SUM_QUANTITY_BY_TS_DATE] = net_quantity
                group[AlgoColumnsEnum.NET_SUM_ORDER_STATE_VALUE_BY_TS_DATE] = net_notional

                if (
                    self._thresholds.minimumNotional is not None
                    and abs(net_notional) < self._thresholds.minimumNotional
                ):
                    self.auditor.step_audit(
                        audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MIN_NOTIONAL_THRESHOLD,
                        audit_data=dict(
                            notional_value=abs(net_notional),
                            threshold_value=self._thresholds.minimumNotional,
                        ),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                        list_of_order_ids=group.loc[:, OrderField.META_KEY].values.tolist(),
                    )
                    logging.warning(
                        msg=f"The calculated value of net notional value: {abs(net_notional)} was below the threshold value: {self._thresholds.minimumNotional}"
                    )
                    continue
                else:
                    self.auditor.step_audit(
                        audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MIN_NOTIONAL_THRESHOLD,
                        audit_data=dict(
                            notional_value=abs(net_notional),
                            threshold_value=self._thresholds.minimumNotional,
                        ),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=len(group),
                    )

                yield group
        else:
            raise ValueError(f"Unknown directionality '{self._thresholds.directionality}'")

    def _step_9_10_11_12(self, records: pd.DataFrame, is_orders: bool) -> Optional[pd.DataFrame]:
        """Run steps 9, 10, 11 & 12 of the algorithm on a batch of data:
        Calculating window times and price change, and dropping invalid
        groupings.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """

        # Step 7 groups by AlgoColumnsEnum.PLACEMENT_PHASE, so we should have a single value
        placement_phase: PlacementPhase = get_unique_field(
            df=records, column=AlgoColumnsEnum.PLACEMENT_PHASE
        )

        # Step 8 groups by AlgoColumnsEnum.SCENARIO_DIRECTIONALITY, so we should have a single value
        vScenarioDirectionality: BuySell = get_unique_field(
            df=records, column=AlgoColumnsEnum.SCENARIO_DIRECTIONALITY
        )

        ric: str = get_unique_field(df=records, column=AlgoColumnsEnum.RIC)

        # Step 9 of the algo
        phase_starts_utc: datetime.Timdelta = get_unique_field(  # type: ignore[name-defined]
            df=records, column=AlgoColumnsEnum.RANGE_WINDOW_START
        ).to_pytimedelta()

        phase_ends_utc: datetime.Timdelta = get_unique_field(  # type: ignore[name-defined]
            df=records, column=AlgoColumnsEnum.RANGE_WINDOW_END
        ).to_pytimedelta()

        records_date: datetime.date = pd.to_datetime(
            records[orders_or_execs_timestamp_column(is_orders)].values[0]
        ).date()

        date: datetime.datetime = datetime.datetime.combine(
            records_date, datetime.datetime.min.time()
        )

        if placement_phase in [
            PlacementPhase.INTRA_DAY_AUCTION,
            PlacementPhase.CLOSING_AUCTION,
            PlacementPhase.OPENING_AUCTION,
        ]:
            vWindowStartTime: datetime.datetime = date + phase_starts_utc
            vWindowEndTime: datetime.datetime = date + phase_ends_utc

            tick_data = self.queries._market_data_client.get_tick_data(
                instrument_ric=ric,
                dates=[vWindowStartTime, vWindowEndTime],
                event_type=RefinitivEventType.AUCTION,
            )

            self.audit_data_list.append(
                dict(
                    audit_key=MarkingV2AuditName.NO_TICK_DATA_FOR_GIVEN_RIC,
                    audit_data=dict(date=date, ric=ric),
                    number_of_input_orders=len(records),
                    number_of_resulting_orders=0 if tick_data.empty else len(records),
                    list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                    if tick_data.empty
                    else [],
                )
            )

            if tick_data.empty:
                logging.info(msg=f"No tick data found for date {date} for ric {ric}")
                return None

            startPricingData: pd.Series = get_nearest_tick_data_from_submitted(
                market_data=tick_data,
                order_time_submitted=vWindowStartTime,
                after_flag=True,
            )

            endPricingData: pd.Series = get_closest_tick(
                tick_data=tick_data, order_time_submitted=vWindowEndTime
            )

            pricingColumn: RefinitivExtractColumns = RefinitivExtractColumns.IND_AUCTION_PRICE

        elif placement_phase is PlacementPhase.CLOSE:
            vWindowStartTime: datetime.datetime = date + phase_ends_utc - self._timedelta
            vWindowEndTime: datetime.datetime = date + phase_ends_utc

            tick_data = self.queries._market_data_client.get_tick_data(
                instrument_ric=ric,
                dates=[vWindowStartTime, vWindowEndTime],
                event_type=RefinitivEventType.TRADE,
            )

            self.audit_data_list.append(
                dict(
                    audit_key=MarkingV2AuditName.NO_TICK_DATA_FOR_GIVEN_RIC,
                    audit_data=dict(date=date, ric=ric),
                    number_of_input_orders=len(records),
                    number_of_resulting_orders=0 if tick_data.empty else len(records),
                    list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                    if tick_data.empty
                    else [],
                )
            )

            if tick_data.empty:
                logging.info(msg=f"No tick data found for date {date} for ric {ric}")
                return None

            startPricingData: pd.Series = get_nearest_tick_data_from_submitted(
                market_data=tick_data, order_time_submitted=vWindowStartTime
            )

            endPricingData: pd.Series = get_nearest_tick_data_from_submitted(
                market_data=tick_data, order_time_submitted=vWindowEndTime
            )

            pricingColumn = RefinitivExtractColumns.PRICE
        elif placement_phase is PlacementPhase.OPEN:
            vWindowStartTime: datetime.datetime = date + phase_starts_utc
            vWindowEndTime: datetime.datetime = date + phase_starts_utc + self._timedelta

            tick_data = self.queries._market_data_client.get_tick_data(
                instrument_ric=ric,
                dates=[vWindowStartTime, vWindowEndTime],
                event_type=RefinitivEventType.TRADE,
            )

            self.audit_data_list.append(
                dict(
                    audit_key=MarkingV2AuditName.NO_TICK_DATA_FOR_GIVEN_RIC,
                    audit_data=dict(date=date, ric=ric),
                    number_of_input_orders=len(records),
                    number_of_resulting_orders=0 if tick_data.empty else len(records),
                    list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                    if tick_data.empty
                    else [],
                )
            )

            if tick_data.empty:
                logging.info(msg=f"No tick data found for date {date} for ric {ric}")
                return None

            startPricingData: pd.Series = get_nearest_tick_data_from_submitted(
                market_data=tick_data,
                order_time_submitted=vWindowStartTime,
                after_flag=True,
            )

            endPricingData: pd.Series = get_nearest_tick_data_from_submitted(
                market_data=tick_data, order_time_submitted=vWindowEndTime
            )

            pricingColumn = RefinitivExtractColumns.PRICE
        else:
            logging.info(msg="Unrecognized placement phase {placement_phase}")
            return None

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MISSING_PRICING_DATA,
                audit_data=dict(),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0
                if startPricingData.empty or endPricingData.empty
                else len(records),
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                if startPricingData.empty or endPricingData.empty
                else [],
            )
        )

        if startPricingData.empty or endPricingData.empty:
            logging.info(msg="Dropping group due to not finding startPricingData or endPricingData")
            return None

        vWindowStartPrice: float = startPricingData[pricingColumn]
        vWindowEndPrice: float = endPricingData[pricingColumn]

        if placement_phase in [
            PlacementPhase.INTRA_DAY_AUCTION,
            PlacementPhase.CLOSING_AUCTION,
            PlacementPhase.OPENING_AUCTION,
        ]:
            if pd.isna(vWindowStartPrice):
                vWindowStartPrice: float = startPricingData[RefinitivExtractColumns.PRICE]

            if pd.isna(vWindowEndPrice):
                vWindowEndPrice: float = endPricingData[RefinitivExtractColumns.PRICE]

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MISSING_WINDOW_PRICING_DATA,
                audit_data=dict(),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0
                if pd.isna(vWindowStartPrice) or pd.isna(vWindowEndPrice)
                else len(records),
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                if pd.isna(vWindowStartPrice) or pd.isna(vWindowEndPrice)
                else [],
            )
        )

        if pd.isna(vWindowStartPrice) or pd.isna(vWindowEndPrice):
            logging.info(
                msg="Dropping group due to either 'vWindowStartPrice' "
                "or 'vWindowEndPrice' being NaN. "
                f"vWindowStartPrice: {vWindowStartPrice} vWindowEndPrice: {vWindowEndPrice}"
            )
            return None

        # Step 10 of the algo
        vPriceChangePercentage: float = (vWindowEndPrice - vWindowStartPrice) / vWindowStartPrice

        vPriceChangeCategory: PriceChangeCategory = price_change_percentage_category_definition(
            price_start=vWindowStartPrice, price_end=vWindowEndPrice
        )

        option_type = None
        if self._thresholds.directionality == Directionality.ONLY_BUYS_OR_ONLY_SELLS:
            options = is_option(records)
            if any(options):
                option_type = records[OrderField.INST_DERIV_OPTION_TYPE].iloc[0]

        group_price_validity: bool = check_invalid_price_check_conditions_options(
            direction=vScenarioDirectionality,
            option_type=option_type,
            price_movement=vPriceChangeCategory,
        )

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_INVALID_PRICE_CONDITION,
                audit_data=dict(
                    buy_sell=vScenarioDirectionality,
                    price_movement=vPriceChangeCategory,
                    option_type=option_type,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=len(records) if group_price_validity else 0,
                list_of_order_ids=[]
                if group_price_validity
                else records.loc[:, OrderField.META_KEY].values.tolist(),
            )
        )

        if not group_price_validity:
            return

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_PRICE_IMP_THRESHOLD,
                audit_data=dict(
                    calculated_data=vPriceChangePercentage,
                    threshold_value=self._thresholds.priceImprovement,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0
                if self._thresholds.priceImprovement > abs(vPriceChangePercentage)
                else len(records),
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                if self._thresholds.priceImprovement > abs(vPriceChangePercentage)
                else [],
            )
        )

        if self._thresholds.priceImprovement > abs(vPriceChangePercentage):
            logging.info(
                f"Dropping group as price improvement: {vPriceChangePercentage} was lower than the threhsold {self._thresholds.priceImprovement}"
            )
            return

        records[AlgoColumnsEnum.WINDOW_START_TIME] = vWindowStartTime
        records[AlgoColumnsEnum.WINDOW_END_TIME] = vWindowEndTime
        records[AlgoColumnsEnum.WINDOW_START_PRICE] = vWindowStartPrice
        records[AlgoColumnsEnum.WINDOW_END_PRICE] = vWindowEndPrice
        records[AlgoColumnsEnum.PRICE_CHANGE_PERCENTAGE] = vPriceChangePercentage
        return records

    def _step_13_14_16_17(self, order_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Run steps 13, 14, 16 & 17 of the algorithm on a batch of orders:
        Price difference of limit order & market volume comparison auctions.

        Args:
            order_data (pd.DataFrame): The batch of orders to run the algo on

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """
        instrument_id: str = get_unique_field(
            df=order_data, column=OrderField.INST_EXT_UNIQUE_IDENT
        )

        order_data = order_data.sort_values(OrderField.TS_ORD_SUBMITTED).reset_index(drop=True)

        # step 13
        limit_stop_columns_list = [
            OrderField.EXC_DTL_LIMIT_PRICE,
            OrderField.EXC_DTL_STOP_PRICE,
        ]

        missing_column = [col for col in limit_stop_columns_list if col not in order_data.columns]

        if len(missing_column) > 0:
            order_data[missing_column] = pd.NA

        # selects limit and stop orders to calculate limit price difference
        limit_and_stop_orders = order_data.loc[
            order_data[OrderField.EXC_DTL_ORD_TYPE].isin([OrderType.LIMIT, OrderType.STOP])
        ]

        if (
            limit_and_stop_orders.empty
            or self._thresholds.limitPriceDifference is None
            or self._thresholds.directionality == Directionality.NET_TRANSACTIONS
        ):
            logging.info(msg="Skipping step 13 as there are not limit or stop orders")

        else:
            first_timestamp = limit_and_stop_orders[OrderField.TS_ORD_SUBMITTED].iloc[0]

            auction_tick_data: pd.DataFrame = self.queries._market_data_client.get_tick_data(
                event_type=RefinitivEventType.AUCTION,
                instrument_unique_identifier=instrument_id,
                dates=[first_timestamp],
            )

            self.audit_data_list.append(
                dict(
                    audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_MISSING_AUCTION_DATA,
                    audit_data=dict(instrument_id=instrument_id),
                    number_of_input_orders=len(order_data),
                    number_of_resulting_orders=0 if auction_tick_data.empty else len(order_data),
                    list_of_order_ids=order_data.loc[:, OrderField.META_KEY].values.tolist()
                    if auction_tick_data.empty
                    else [],
                )
            )

            if auction_tick_data.empty:
                logging.info(msg=f"Could not find auction data for instrument {instrument_id}.")
                return None

            limit_tick: pd.DataFrame = get_closest_tick(
                tick_data=auction_tick_data, order_time_submitted=first_timestamp
            )

            if limit_tick.empty:
                logging.info(msg=f"Could not find auction data for instrument {instrument_id}.")
            else:
                auction_price: float = limit_tick[RefinitivExtractColumns.IND_AUCTION_PRICE]
                if pd.isna(auction_price):
                    auction_price: float = limit_tick[RefinitivExtractColumns.PRICE]

                self.audit_data_list.append(
                    dict(
                        audit_key=MarkingV2AuditName.RECORDS_DROPPED_DUE_TO_UNRESOLVED_AUCTION_PRICE,
                        audit_data=dict(instrument_id=instrument_id, window=[first_timestamp]),
                        number_of_input_orders=len(order_data),
                        number_of_resulting_orders=0 if pd.isna(auction_price) else len(order_data),
                        list_of_order_ids=order_data.loc[:, OrderField.META_KEY].values.tolist()
                        if pd.isna(auction_price)
                        else [],
                    )
                )

                if pd.isna(auction_price):
                    logging.info(msg=f"Auction price for instrument {instrument_id} was NaN.")
                    return
                # Column created with limit and stop prices to calculate average
                limit_and_stop_orders[AlgoColumnsEnum.AVERAGE_LIMIT_STOP_PRICE] = (
                    limit_and_stop_orders[OrderField.EXC_DTL_LIMIT_PRICE].fillna(
                        limit_and_stop_orders[OrderField.EXC_DTL_STOP_PRICE]
                    )
                )

                average_limit_stop_price = limit_and_stop_orders[
                    AlgoColumnsEnum.AVERAGE_LIMIT_STOP_PRICE
                ].mean()

                vLimitPriceDifference = (
                    (average_limit_stop_price - auction_price) / auction_price
                ) * 100

                order_data[AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE] = vLimitPriceDifference

                order_data = order_data[
                    order_data[AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE]
                    >= self._thresholds.limitPriceDifference
                ]

                self.audit_data_list.append(
                    dict(
                        audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_LIMIT_PRICE_DIFF_THRESHOLD,
                        audit_data=dict(threshold_value=self._thresholds.limitPriceDifference),
                        number_of_input_orders=len(order_data),
                        number_of_resulting_orders=len(order_data),
                        list_of_order_ids=order_data.loc[:, OrderField.META_KEY].values.tolist(),
                    )
                )

                if order_data.empty:
                    logging.info(
                        msg="Limit price difference is below the value defined in threshold for all records in the group"
                    )
                    return None

        # Step 14 of the algo
        placement_phase: str = get_unique_field(
            df=order_data, column=AlgoColumnsEnum.PLACEMENT_PHASE
        )
        if placement_phase not in [
            PlacementPhase.OPENING_AUCTION,
            PlacementPhase.CLOSING_AUCTION,
            PlacementPhase.INTRA_DAY_AUCTION,
        ]:
            logging.info(msg=f"Placement phase '{placement_phase}' is not feasible for step 14.")
            return order_data

        window_start_timestamp: pd.Timestamp = order_data[AlgoColumnsEnum.WINDOW_START_TIME].iloc[0]

        window_end_timestamp: pd.Timestamp = order_data[AlgoColumnsEnum.WINDOW_END_TIME].iloc[0]

        auction_tick_data: pd.DataFrame = self.queries._market_data_client.get_tick_data(
            event_type=RefinitivEventType.AUCTION,
            instrument_unique_identifier=instrument_id,
            dates=[window_start_timestamp, window_end_timestamp],
            result_between_dates=True,
        )

        vMarketTradedVolume: float = auction_tick_data[RefinitivExtractColumns.VOLUME].sum()

        # Steps 16 & 17 of the algo
        return self._step_16_17(
            records=order_data,
            vMarketTradedVolume=vMarketTradedVolume,
        )

    def _step_15_16_17(self, executions_df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Run steps 15 & 16 of the algorithm on a batch of executions: Market
        volume comparison non-auctions.

        Args:
            executions_df (pd.DataFrame): The batch of executions to run the algo on

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """

        # Step 15 of the algo
        placement_phase: str = get_unique_field(
            df=executions_df, column=AlgoColumnsEnum.PLACEMENT_PHASE
        )
        if placement_phase not in [
            PlacementPhase.OPEN,
            PlacementPhase.CLOSING_AUCTION,
            PlacementPhase.OPENING_AUCTION,
            PlacementPhase.CLOSE,
        ]:
            logging.info(msg=f"Placement phase '{placement_phase}' is not feasible for step 15.")
            return None

        ric: str = get_unique_field(df=executions_df, column=AlgoColumnsEnum.RIC)
        vWindowStartTime: datetime.datetime = get_unique_field(
            df=executions_df, column=AlgoColumnsEnum.WINDOW_START_TIME
        )
        vWindowEndTime: datetime.datetime = get_unique_field(
            df=executions_df, column=AlgoColumnsEnum.WINDOW_END_TIME
        )

        event_type = (
            RefinitivEventType.AUCTION
            if placement_phase in [PlacementPhase.CLOSING_AUCTION, PlacementPhase.OPENING_AUCTION]
            else RefinitivEventType.TRADE
        )

        ticks: pd.DataFrame = self.queries._market_data_client.get_tick_data(
            instrument_ric=ric,
            dates=[vWindowStartTime, vWindowEndTime],
            event_type=event_type,
            result_between_dates=True,
        )

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MISSING_TRADE_TICK_VOLUME,
                audit_data=dict(ric=ric),
                number_of_input_orders=len(executions_df),
                number_of_resulting_orders=0 if ticks.empty else len(executions_df),
                list_of_order_ids=executions_df.loc[:, OrderField.META_KEY].values.tolist()
                if ticks.empty
                else [],
            )
        )

        if ticks.empty:
            logging.info(
                msg=f"Could not find trade tick {RefinitivExtractColumns.VOLUME} for ric {ric}."
            )
            return None

        vMarketTradedVolume: float = ticks[RefinitivExtractColumns.VOLUME].sum()

        # Steps 16 & 17 of the algo
        return self._step_16_17(
            records=executions_df,
            vMarketTradedVolume=vMarketTradedVolume,
        )

    def _step_16_17(
        self, records: pd.DataFrame, vMarketTradedVolume: float
    ) -> Optional[pd.DataFrame]:
        """Run steps 16 & 17 of the algorithm on a batch of executions: Market
        volume comparison.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            vMarketTradedVolume (float): The market traded volume from the previous step

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """

        if self._thresholds.directionality is Directionality.ONLY_BUYS_OR_ONLY_SELLS:
            vScenarioVolume: float = get_unique_field(
                df=records, column=AlgoColumnsEnum.SUM_QUANTITY_BY_TS_DATE
            )
        else:
            vScenarioVolume: float = get_unique_field(
                df=records,
                column=AlgoColumnsEnum.NET_SUM_QUANTITY_BY_TS_DATE,
            )

        vMarketVolumeComparison: float = abs(vScenarioVolume / vMarketTradedVolume)

        self.audit_data_list.append(
            dict(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_MARKET_COMPARISON_THRESHOLD,
                audit_data=dict(
                    calculated_value=vMarketVolumeComparison,
                    threshold_value=self._thresholds.marketComparison,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0
                if vMarketVolumeComparison < self._thresholds.marketComparison
                else len(records),
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                if vMarketVolumeComparison < self._thresholds.marketComparison
                else [],
            )
        )

        if vMarketVolumeComparison < self._thresholds.marketComparison:
            logging.info(
                msg="Dropping group due to 'vMarketVolumeComparison' being less than "
                f"the threshold ({self._thresholds.marketComparison})."
            )
            return None

        records[AlgoColumnsEnum.MARKET_VOLUME_COMPARISON] = vMarketVolumeComparison
        records[AlgoColumnsEnum.SCENARIO_VOLUME] = vScenarioVolume
        return records

    def _step_18(self, records: pd.DataFrame, is_orders: bool) -> Optional[pd.DataFrame]:
        """Run steps 18 of the algorithm on a batch of data: Market Volume
        Comparison & Day Comparison.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """
        if self._thresholds.dayComparison is None:
            return records

        date: datetime.date = pd.to_datetime(
            records[orders_or_execs_timestamp_column(is_orders=is_orders)].values[0]
        ).date()

        instrument_id: str = get_unique_field(df=records, column=NewColumns.INSTRUMENT_CODE)

        dayVolume: Optional[float] = self.queries.get_range_total_volume(
            from_datetime=datetime.datetime.combine(date, datetime.time.min),
            to_datetime=datetime.datetime.combine(date, datetime.time.max),
            instrument_id=instrument_id,
            is_orders=is_orders,
        )

        if dayVolume is None:
            dayComparison: float = np.nan
            logging.info(
                msg=f"Could not calculate day volume for instrument '{instrument_id}'. Continuing..."
            )
        else:
            vScenarioVolume: float = get_unique_field(
                df=records, column=AlgoColumnsEnum.SCENARIO_VOLUME
            )

            dayComparison: float = vScenarioVolume / dayVolume

            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_DAY_VOLUME_COMPARISON_THRESHOLD,
                audit_data=dict(
                    calculated_value=dayComparison,
                    threshold_value=self._thresholds.dayComparison,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0
                if dayComparison < self._thresholds.dayComparison
                else len(records),
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist()
                if dayComparison < self._thresholds.dayComparison
                else [],
            )

            if dayComparison < self._thresholds.dayComparison:
                logging.info(
                    msg="Dropping group due to 'dayComparison' being less than "
                    f"the threshold ({self._thresholds.dayComparison})."
                )
                return None

        records[AlgoColumnsEnum.DAY_COMPARISON] = dayComparison
        return records

    def _step_19(self, records: pd.DataFrame, is_orders: bool) -> Optional[pd.DataFrame]:
        """Run step 19 of the algorithm on a batch of data: Previous 10 Days
        Comparison.

        Args:
            records (pd.DataFrame): The batch of records to run the algo on
            is_orders (bool): A flag indicating whether the input data is executions or orders

        Returns:
            Dataframes resulting from the step execution, or None if the group was dropped
        """
        if self._thresholds.tenDayComparison is None:
            return records

        timestamp: OrderField = orders_or_execs_timestamp_column(is_orders=is_orders)
        date: datetime.date = pd.to_datetime(
            records[orders_or_execs_timestamp_column(is_orders=is_orders)].values[0]
        ).date()

        instrument_id: str = get_unique_field(df=records, column=OrderField.INST_EXT_UNIQUE_IDENT)

        datetime_from: datetime.datetime = date - BDay(11)
        datetime_to: datetime.datetime = pd.Timestamp(
            datetime.datetime.combine(date - BDay(1), datetime.time.max)
        )

        last_10_days_orders: pd.DataFrame = (
            self.queries.fetch_date_range_orders(
                instrument_unique_identifier=instrument_id,
                datetime_from=datetime_from,
                datetime_to=datetime_to,
            )
            if is_orders
            else pd.DataFrame()
        )

        last_10_days_executions: pd.DataFrame = self.queries.fetch_date_range_executions(
            instrument_unique_identifier=instrument_id,
            datetime_from=datetime_from,
            datetime_to=datetime_to,
        )

        if not is_orders and last_10_days_executions.empty:
            return records

        if is_orders and last_10_days_orders.empty:
            return records

        vNetQuantities: List[float] = []
        for i_start in pd.date_range(start=date - datetime.timedelta(days=10), end=date):
            i_end = i_start + records[AlgoColumnsEnum.RANGE_WINDOW_END].iloc[0]
            i_start = i_start + records[AlgoColumnsEnum.RANGE_WINDOW_START].iloc[0]

            i_date_orders = (
                last_10_days_orders.loc[
                    (i_start <= last_10_days_orders[timestamp])
                    & (last_10_days_orders[timestamp] < i_end)
                ]
                if is_orders
                else pd.DataFrame()
            )
            i_date_execs = last_10_days_executions.loc[
                (i_start <= last_10_days_executions[timestamp])
                & (last_10_days_executions[timestamp] < i_end)
            ]

            if i_date_orders.empty and i_date_execs.empty:
                logging.debug(
                    msg=f"No order nor execution data between '{i_start}' & '{i_end}' for instrument '{instrument_id}'."
                )
                continue

            vQuantityDay: float = 0.0
            for group in self._vquantity_computer(
                records=i_date_orders if is_orders else i_date_execs,
                is_orders=is_orders,
                executions_df=i_date_execs if is_orders else pd.DataFrame,
            ):
                if self._thresholds.directionality == Directionality.ONLY_BUYS_OR_ONLY_SELLS:
                    quantity = group[AlgoColumnsEnum.SUM_QUANTITY_BY_TS_DATE].values[0]

                    if group[OrderField.EXC_DTL_BUY_SELL_IND].iloc[0] == BuySell.SELL:
                        quantity = -quantity

                    vQuantityDay += quantity

                elif self._thresholds.directionality != Directionality.ONLY_BUYS_OR_ONLY_SELLS:
                    vQuantityDay += group[AlgoColumnsEnum.NET_SUM_QUANTITY_BY_TS_DATE].values[0]
                else:
                    raise ValueError(f"Unknown directionality '{self._thresholds.directionality}'")

            if abs(vQuantityDay) > 0.0:
                vNetQuantities.append(vQuantityDay)

        if len(vNetQuantities) == 0:
            return records

        if len(vNetQuantities) == 1:
            tenDaysLowerBound = vNetQuantities[0]
            tenDaysUpperBound = vNetQuantities[0]

        else:
            confidence = calculate_confidence_interval(
                value_series=pd.Series(vNetQuantities), alpha=self._thresholds.tenDayComparison
            )

            tenDaysLowerBound: float = confidence.get(DateRangeParameters.START)
            tenDaysUpperBound: float = confidence.get(DateRangeParameters.END)

        previous_10_day_interval = (tenDaysLowerBound, tenDaysUpperBound)
        records[AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON] = [previous_10_day_interval] * len(
            records
        )

        net_quantity_volume = records[AlgoColumnsEnum.SCENARIO_VOLUME].iloc[0]

        if records[OrderField.EXC_DTL_BUY_SELL_IND].iloc[0] == BuySell.SELL:
            net_quantity_volume = -net_quantity_volume

        if (net_quantity_volume > 0 and net_quantity_volume > tenDaysUpperBound) or (
            net_quantity_volume < 0 and net_quantity_volume < tenDaysLowerBound
        ):
            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_TEN_DAY_QUANTITY_COMPARISON_THRESHOLD,
                audit_data=dict(
                    net_quantity_volume=net_quantity_volume,
                    tenDaysLowerBound=tenDaysLowerBound,
                    tenDaysUpperBound=tenDaysUpperBound,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=len(records),
            )
            return records
        else:
            self.auditor.step_audit(
                audit_key=MarkingV2AuditName.GROUP_DROPPED_DUE_TO_TEN_DAY_QUANTITY_COMPARISON_THRESHOLD,
                audit_data=dict(
                    net_quantity_volume=net_quantity_volume,
                    tenDaysLowerBound=tenDaysLowerBound,
                    tenDaysUpperBound=tenDaysUpperBound,
                ),
                number_of_input_orders=len(records),
                number_of_resulting_orders=0,
                list_of_order_ids=records.loc[:, OrderField.META_KEY].values.tolist(),
            )
            logging.info(
                msg=f"Dropping group due to 'tenDayADV' ('{net_quantity_volume}') being outside of bounds "
                f"({tenDaysLowerBound}-{tenDaysUpperBound})."
            )
            return None

    def _create_alert(
        self,
        alert_data: pd.DataFrame,
        orders: Optional[pd.DataFrame],
        executions: pd.DataFrame,
    ):
        """Create alert to be sent to the user.

        Args:
            alert_data (pd.DataFrame): Data with valid alert
            orders (Optional[pd.DataFrame]): The orders that generated the alert
            executions (pd.DataFrame): The executions of the orders that generated the alert
        """
        logging.info(msg="Appending alert data")
        alert: dict = alert_data.to_dict(orient="list")

        currency_used: str = self._thresholds.minimumNotionalCurrency.value

        alert[AlgoColumnsEnum.RUN_TYPE] = self._thresholds.runType.value
        alert[AlgoColumnsEnum.BEHAVIOUR_TYPE] = self._thresholds.behaviourType.value

        alert[AlgoColumnsEnum.DATE] = alert_data[AlgoColumnsEnum.WINDOW_START_TIME].iloc[0].date()

        alert[AlgoColumnsEnum.MINIMUM_NOTIONAL_CURRENCY_RATE + "." + currency_used] = (
            get_unique_value_for_alert(
                data=alert_data,
                column_name=AlgoColumnsEnum.MINIMUM_NOTIONAL_CURRENCY_RATE + "." + currency_used,
            )
        )
        alert[AlgoColumnsEnum.MINIMUM_NOTIONAL_CURRENCY] = currency_used
        alert[AlgoColumnsEnum.EVALUATION_TYPE] = self._thresholds.evaluationType
        alert[AlgoColumnsEnum.EVALUATION_TYPE_ID] = get_unique_value_for_alert(
            data=alert_data, column_name=self.evaluation_type_column_name
        )[0]

        market_comparison = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.MARKET_VOLUME_COMPARISON
        )

        if market_comparison is pd.NA:
            logging.info(msg="No market volume comparison existed")
            return

        alert[AlgoColumnsEnum.MARKET_COMPARISON] = market_comparison[0]

        alert[AlgoColumnsEnum.PLACEMENT_PHASE] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.PLACEMENT_PHASE
        )[0].value

        if self._thresholds.dayComparison:
            alert[AlgoColumnsEnum.DAY_COMPARISON] = get_unique_value_for_alert(
                data=alert_data, column_name=AlgoColumnsEnum.DAY_COMPARISON
            )[0]

        alert[AlgoColumnsEnum.PRICE_CHANGE_PERCENTAGE] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.PRICE_CHANGE_PERCENTAGE
        )[0]

        if (
            self._thresholds.limitPriceDifference
            and AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE in alert_data.columns
        ):
            alert[AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE] = get_unique_value_for_alert(
                data=alert_data, column_name=AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE
            )[0]

        if (
            self._thresholds.tenDayComparison
            and AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON in alert_data.columns
        ):
            alert[AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON] = get_unique_value_for_alert(
                data=alert_data, column_name=AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON
            )[0]
        alert[AlgoColumnsEnum.WINDOW_START_TIME] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.WINDOW_START_TIME
        )[0]
        alert[AlgoColumnsEnum.WINDOW_END_TIME] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.WINDOW_END_TIME
        )[0]
        alert[AlgoColumnsEnum.WINDOW_START_PRICE] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.WINDOW_START_PRICE
        )[0]
        alert[AlgoColumnsEnum.WINDOW_END_PRICE] = get_unique_value_for_alert(
            data=alert_data, column_name=AlgoColumnsEnum.WINDOW_END_PRICE
        )[0]
        if isinstance(orders, pd.DataFrame):
            alert[AlgoColumnsEnum.ORDERS] = get_unique_value_for_alert(
                data=alert_data, column_name=OrderField.META_KEY
            )
            alert[AlgoColumnsEnum.ORDERS_STATE_KEYS] = pd.NA

        elif isinstance(executions, pd.DataFrame):
            alert[AlgoColumnsEnum.ORDERS_STATE_KEYS] = get_unique_value_for_alert(
                data=alert_data, column_name=OrderField.META_KEY
            )
            alert[AlgoColumnsEnum.ORDERS] = pd.NA

        result: dict = dict(
            (key, alert[key])
            for key in AlgoColumnsEnum.get_scenario_columns(currency_used=currency_used)
            if key in alert
        )

        alerts_df = pd.DataFrame([result])
        if not alerts_df.empty:
            self._write_result_to_ndjson(results_df=alerts_df)

    def _get_coop_phase_data_from_group(
        self,
        record: pd.DataFrame,
    ) -> pd.DataFrame:
        """Get the 'Phase Starts UTC', 'Phase Ends UTC' and 'Phase Type'
        indexed by the provided group. There must be a single unique value
        indexing the session in the provided dataframe.

        Args:
            df (pd.DataFrame): The dataframe to link the phase data from

        Returns:
            A tuple with the 'Phase Starts UTC', 'Phase Ends UTC' and 'Phase Type'
        """
        coop_clark_index: int = record[AlgoColumnsEnum.TRADING_SESSION_MATCH]
        session_data: pd.Series = self.copp_clark_file.loc[coop_clark_index, :]

        record[CoppClarkColumn.PHASE] = session_data[CoppClarkColumn.PHASE].lower().strip()
        record[CoppClarkColumn.PHASE_TYPE] = (
            session_data[CoppClarkColumn.PHASE_TYPE].lower().strip()
        )

        return record
