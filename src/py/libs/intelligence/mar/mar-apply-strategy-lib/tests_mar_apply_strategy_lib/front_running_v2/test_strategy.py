# ruff: noqa: E501
# type: ignore
import ast
import front_running_v2_apply_strategy.apply_strategy as strategy_module
import json
import logging
import market_abuse_algorithms
import os
import pandas as pd
import pytest
import re
from datetime import datetime
from front_running_v2_apply_strategy.alerts import <PERSON><PERSON><PERSON>
from front_running_v2_apply_strategy.apply_strategy import ApplyStrategy
from front_running_v2_apply_strategy.models import Thresholds
from front_running_v2_apply_strategy.static import (
    DFColumns,
    EvaluationType,
    Flow,
    OrderType,
    RefinitivColumns,
)
from market_abuse_algorithms.cross_product.cross_product import CrossProductActivityDisplay
from market_abuse_algorithms.cross_product.static import CrossProductColumns
from market_abuse_algorithms.data_source.repository.base import Repository
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import OrderVolume
from market_abuse_algorithms.strategy.base.models import TimeUnit
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from pathlib import Path
from surveillance_utils.test_mock_helpers import load_ndjson_into_list
from tests_mar_apply_strategy_lib.front_running_v2.filters import (
    FILTER_1,
    FILTER_2,
    FILTER_3,
    FILTER_5,
    FILTER_6,
    FILTER_7,
    FILTER_8,
    FILTER_9,
)
from tests_mar_apply_strategy_lib.front_running_v2.mock_data import (
    FakeCrossProduct,
    FakeCrossProduct_test_case_18,
    process_client_vs_client_empty,
    process_prop_vs_client_empty,
)
from tests_mar_apply_strategy_lib.front_running_v2.test_fr_v2_utils import (
    FakeMarketClient,
)
from types import MethodType
from unittest.mock import MagicMock, patch

TEST_DATA = Path(__file__).parent.joinpath("test_data")


def custom_date_parser():
    return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")


@pytest.fixture()
def fake_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def _parse_data(*args):
        result = pd.read_csv(
            *args,
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )
        return result

    return _parse_data


def fake_get_related_orders(data_front_running: pd.DataFrame, start_col: str, end_col: str):
    data_front_running[CrossProductColumns.RELATED_INSTRUMENT] = False

    return data_front_running


class TestFrontRunningV2:
    def test_case_6_client_vs_client(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Client vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_6
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = fake_data(TEST_DATA.joinpath("test_case_6_client_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert scenarios
        assert len(scenarios) == 2

        for scenario in scenarios:
            assert len(set(scenario.get("data").get("frontRunOrdersClients"))) == 1
            assert scenario.get("data").get("frontRunOrdersClients")[0] != scenario.get("data").get(
                "frontRunningOrdersClients"
            )

    def test_case_7_prop_vs_client(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Prop vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_7
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = fake_data(TEST_DATA.joinpath("test_case_7_prop_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert scenarios
        assert len(scenarios) == 2

        for scenario in scenarios:
            assert len(set(scenario.get("data").get("frontRunOrdersClients"))) == 1
            assert scenario.get("data").get("frontRunOrdersClients")[0] != scenario.get("data").get(
                "frontRunningOrdersClients"
            )

    def test_case_6_client_vs_client_empty(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Client vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_6
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        strategy._process_client_vs_client = MagicMock()
        strategy._process_client_vs_client.return_value = process_client_vs_client_empty()
        strategy._process_prop_vs_client = MagicMock()
        strategy._process_prop_vs_client.return_value = process_prop_vs_client_empty()

        data = fake_data(TEST_DATA.joinpath("test_case_6_client_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        assert not os.path.exists(strategy.scenario_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )

    def test_case_7_client_vs_client_empty(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Prop vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_7
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        strategy._process_client_vs_client = MagicMock()
        strategy._process_client_vs_client.return_value = process_client_vs_client_empty()
        strategy._process_prop_vs_client = MagicMock()
        strategy._process_prop_vs_client.return_value = process_prop_vs_client_empty()

        data = fake_data(TEST_DATA.joinpath("test_case_7_prop_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        assert not os.path.exists(strategy.scenario_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )

    @pytest.mark.parametrize(
        "evaluation_type,volume_difference,expected_scenarios,frontrun_order_volume",
        [("Trader", 0.1, 1, 500000), ("Desk", 0.11, 0, 5000000)],
    )
    def test_case_8_client_vs_client(
        self,
        helpers,
        mock_apply_strategy_kwargs,
        fake_data,
        evaluation_type,
        volume_difference,
        expected_scenarios,
        frontrun_order_volume,
        monkeypatch,
    ):
        def fake_get_execution_data_tc8(df: pd.DataFrame) -> pd.DataFrame:
            df[OrderVolume.WEIGHTED_EXECUTION_PRICE] = pd.Series([1.06, 1.08])
            return df

        thresholds = {
            "adv": None,
            "frontrunOrderVolume": frontrun_order_volume,
            "evaluationType": evaluation_type,
            "flow": "Client vs. Client",
            "priceImprovement": True,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": volume_difference,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_8
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)
        monkeypatch.setattr(strategy, "get_execution_data", fake_get_execution_data_tc8)

        data = fake_data(TEST_DATA.joinpath("test_case_8_client_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        if expected_scenarios:
            scenarios = load_ndjson_into_list(strategy.result_local_file_path)
            assert len(scenarios) == expected_scenarios
            for scenario in scenarios:
                assert scenario.get("data").get("frontRunOrdersClients")[0] != scenario.get(
                    "data"
                ).get("frontRunningOrdersClients")

                assert scenario.get("data").get("frontRunningOrdersClients") == "account:client1"

                assert scenario.get("data").get("frontRunOrdersClients") == ["account:client2"]

                assert scenario.get("data").get("volumeDifference") == 0.1
        else:
            assert not os.path.exists(strategy.scenario_local_file_path), (
                "Expecting 0 scenarios, file should NOT exist"
            )

    def test_thresholds(self):
        thresholds = dict(
            adv=0.5,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.PROP_VS_CLIENT,
            frontrunOrderVolume=1000,
            priceImprovement=False,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 36000},
            timeWindowFrontrunningExecution={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.3,
        )

        Thresholds(**thresholds)

    def test_client_vs_client_no_trader(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, one client order, preceding by another client order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=0.05,
            assetClass="",
            evaluationType=None,
            flow=Flow.CLIENT_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("1_1_client_vs_client_adv.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("1_1_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        test_market_data = pd.read_csv(
            TEST_DATA.joinpath("1_1_market_adv.csv"),
            parse_dates=[RefinitivColumns.DATE],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            if strategy.thresholds.adv:
                df_window = strategy.check_adv(
                    df=df_window,
                    market_data_adv_df=test_market_data,
                )

            if df_window.empty:
                continue

            assert DFColumns.ADV in df_window
            assert df_window.loc[0, DFColumns.ADV] >= strategy.thresholds.adv

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_client_vs_client_trader(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, one client order, preceding by another client order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=0.05,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.CLIENT_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("1_2_client_vs_client_adv_trader.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("1_2_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        test_market_data = pd.read_csv(
            TEST_DATA.joinpath("1_1_market_adv.csv"),
            parse_dates=[RefinitivColumns.DATE],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            assert not df_window.empty or df_window.s
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            if strategy.thresholds.adv:
                df_window = strategy.check_adv(
                    df=df_window,
                    market_data_adv_df=test_market_data,
                )

            if df_window.empty:
                continue

            assert DFColumns.ADV in df_window
            assert df_window.loc[0, DFColumns.ADV] >= strategy.thresholds.adv

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_process_client_vs_client(self, helpers, mock_apply_strategy_kwargs):
        thresholds = dict(
            adv=0.1,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.CLIENT_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("client_vs_client_test_process.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        front_running_orders = test_data.loc[test_data.loc[:, "orderType"] == "frontrunning"]

        result = strategy._process_client_vs_client(
            group=test_data, front_running_orders=front_running_orders
        )

        front_run_orders = result.loc[result.loc[:, "orderType"] == "frontrun"]

        assert len(front_run_orders) == 2
        assert "DEAL" not in result.loc[:, OrderField.TRX_DTL_TR_CAPACITY].tolist()

    def test_client_vs_client_trader_high_adv(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """no alerts, as adv is too high."""

        thresholds = dict(
            adv=0.1,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.CLIENT_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("1_2_client_vs_client_adv_trader.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("1_2_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        test_market_data = pd.read_csv(
            TEST_DATA.joinpath("1_1_market_adv.csv"),
            parse_dates=[RefinitivColumns.DATE],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            if strategy.thresholds.adv:
                df_window = strategy.check_adv(
                    df=df_window,
                    market_data_adv_df=test_market_data,
                )

            if df_window.empty:
                continue

            assert DFColumns.ADV in df_window
            assert df_window.loc[0, DFColumns.ADV] >= strategy.thresholds.adv

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        assert not os.path.exists(strategy.scenario_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )

    def test_prop_vs_client_trader(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, one prop order, preceding by another client order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=None,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.PROP_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 1},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_2
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("2_1_prop_vs_client_trader.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("2_1_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_prop_vs_client_desk(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, one prop order, preceding by another client order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=None,
            assetClass="",
            evaluationType=EvaluationType.DESK,
            flow=Flow.PROP_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 1},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_2
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("2_2_prop_vs_client_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("2_2_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_prop_vs_client_portfolio_manager(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """1 alert, one prop order, preceding by another client order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=None,
            assetClass="",
            evaluationType=EvaluationType.PORTFOLIO_MANAGER,
            flow=Flow.PROP_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 1},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_2
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("2_3_prop_vs_client_portfolio_manager.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("2_3_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_desk_vs_desk(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, one desk order, preceding by another desk  order."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=None,
            assetClass="",
            evaluationType=None,
            flow=Flow.DESK_VS_DESK,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 1},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("3_1_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_pad_vs_nonpad(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        """1 alert, 1 pad, precending 1 anything."""

        number_of_scenarios = 1

        thresholds = dict(
            adv=None,
            assetClass="",
            evaluationType=None,
            flow=Flow.PAD_VS_NON_PAD,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 1},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("4_1_pad_vs_nonpad.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("4_1_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        res = []

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window = strategy._check_volume_difference(df=df_window)

            if df_window.empty:
                continue

            assert DFColumns.VOLUME_DIFFERENCE in df_window
            assert (
                df_window.loc[0, DFColumns.VOLUME_DIFFERENCE]
                >= strategy.thresholds.volumeDifference
            )

            if strategy.thresholds.priceImprovement:
                df_window = strategy._check_improvement_flag(df=test_execution_data)

            if df_window.empty:
                continue

            assert DFColumns.WEIGHTED_EXECUTION_PRICE in df_window.columns
            assert DFColumns.PRICE_IMPROVEMENT in df_window.columns
            assert (df_window.loc[:, DFColumns.PRICE_IMPROVEMENT] > 0).any()

            res.append(df_window)

        for result in res:
            strategy._create_alerts(df=result)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)
        assert len(scenarios) == number_of_scenarios

    def test_process_pad_vs_nonpad(self, helpers, mock_apply_strategy_kwargs):
        thresholds = {
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.25,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }

        filters = {
            "bool": {"must": {"terms": {"orderIdentifiers.orderIdCode": ["3054714783", "EU-5269"]}}}
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = pd.read_csv(
            TEST_DATA.joinpath("test_process_pad_vs_nonpad.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        result = strategy._process_pad_vs_nonpad(data)
        assert result[DFColumns.ORDER_TYPE][0] == OrderType.FRONT_RUNNING.value
        assert result[DFColumns.ORDER_TYPE][1] == OrderType.FRONT_RUN.value

    def test_case_9_client_vs_client(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 1000,
            "evaluationType": "Trader",
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = pd.read_csv(
            TEST_DATA.joinpath("test_case_9_client_vs_client.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert scenarios
        assert len(scenarios) == 1

        for scenario in scenarios:
            assert len(set(scenario.get("data").get("frontRunOrdersClients"))) == 1
            assert scenario.get("data").get("frontRunOrdersClients")[0] != scenario.get("data").get(
                "frontRunningOrdersClients"
            )

    def test_case_10_client_vs_client(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 1000,
            "evaluationType": "Trader",
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = pd.read_csv(
            TEST_DATA.joinpath("test_case_10_client_vs_client.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert scenarios
        assert len(scenarios) == 2

        for scenario in scenarios:
            assert len(set(scenario.get("data").get("frontRunOrdersClients"))) == 1
            assert scenario.get("data").get("frontRunOrdersClients")[0] != scenario.get("data").get(
                "frontRunningOrdersClients"
            )

    def test_multiple_frontrunningorder(self, helpers, mock_apply_strategy_kwargs):
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 1000,
            "evaluationType": "Trader",
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        result = {
            "data": {
                "frontRunOrdersClients": ["Client 2"],
                "frontRunningOrdersClients": "Client 1",
                "buySell": "BUYI",
                "instrumentName": "VODAFONE GROUP PLC ORD USD0.20 20/21",
                "frontRunOrders": ["Order:frontrunningAndShadowing_9_3:1:NEWO:1673971137760"],
                "frontRunningOrder": [
                    "Order:frontrunningAndShadowing_9_1:1:NEWO:1673971137760",
                    "Order:frontrunningAndShadowing_9_2:1:NEWO:1673971137760",
                ],
                "frontRunOrdersCount": 1,
                "frontRunOrdersQuantity": 500000.0,
                "frontRunningOrdersQuantity": 100000.0,
                "frontRunOrdersTraders": ["Trader 1"],
                "frontRunningOrdersTraders": "Trader 1",
                "frontRunDesk": ["desk1"],
                "frontRunningOrderDesks": "desk1",
                "frontRunPortfolioManager": ["Trader 1"],
                "frontRunningPortfolioManager": "Trader 1",
                "frontRunOrdersTimestamp": pd.Timestamp("2020-11-03 12:00:20"),
                "frontRunningOrdersTimestamp": pd.Timestamp("2020-11-03 12:00:00"),
                "timeDifference": 20.0,
                "volumeDifference": 0.1,
            }
        }
        scenario = Scenario(result=result, context=strategy.context)
        normalized_scenario = scenario.normalize

        assert len(normalized_scenario.trades) == 3

    def test_check_volume_difference_front_running_condition(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        thresholds = dict(
            adv=0.2,
            assetClass="",
            evaluationType=EvaluationType.PORTFOLIO_MANAGER,
            flow=Flow.CLIENT_VS_CLIENT,
            priceImprovement=False,
            timeWindow={"unit": TimeUnit.HOURS, "value": 5},
            volumeDifference=0.1,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters={}
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("1_2_client_vs_client_adv_trader.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert not df_window.empty or df_window.shape[0] == 1

            df_window.loc[:, DFColumns.ORDER_TYPE] = OrderType.FRONT_RUN

            df_window = strategy._check_volume_difference(df=df_window)

            assert df_window.empty

    def test_time_window_threshold(self, helpers, mock_apply_strategy_kwargs):
        front_running_data = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frunning.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )

        front_run_orders = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frun.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )
        thresholds = {
            "adv": None,
            "evaluationType": "Trader",
            "flow": "Prop vs. Client",
            "priceImprovement": True,
            "timeWindow": {"unit": "hours", "value": 60},
            "timeWindowFrontrunningExecution": {"unit": "hours", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        res = strategy._evaluate_time_window_groups(
            front_running_data=front_running_data, front_run_orders=front_run_orders
        )

        assert res.shape[0] == 3
        assert res.loc[res[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING].shape[0] == 1
        assert res.loc[res[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN].shape[0] == 2

    @pytest.mark.parametrize(
        "evaluation_type,required",
        [
            (
                "Portfolio Manager",
                "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier",
            ),
            ("Trader", "traderFileIdentifier"),
        ],
    )
    def test_query_required_field(
        self, helpers, mock_apply_strategy_kwargs, evaluation_type, required
    ):
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 1000,
            "evaluationType": evaluation_type,
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.1,
        }
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters={}
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        query = strategy.queries._get_orders_query()

        required_fields_list = query.to_dict()["query"]["bool"]["filter"]

        assert required == required_fields_list[-1]["exists"]["field"]

    def test_case_5_pad_vs_nonpad(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        def fake_get_execution_data_tc5(df: pd.DataFrame) -> pd.DataFrame:
            df[OrderVolume.WEIGHTED_EXECUTION_PRICE] = pd.Series([1.08, 1.06])
            return df

        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 5000000,
            "evaluationType": None,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": True,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_5
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_execution_data", fake_get_execution_data_tc5)
        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = fake_data(TEST_DATA.joinpath("test_case_5_pad_vs_nonpad.csv"))
        data.loc[1, OrderField.INST_DERIV_UND_INSTS] = [
            [{"underlyingInstrumentCode": "GB00BH4HKS39"}]
        ]

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert len(scenarios) == 1

    def test_define_grouping_column(
        self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch
    ):
        data = pd.read_csv(
            TEST_DATA.joinpath("test_define_grouping_column.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
            ],
        )

        data.loc[3:, OrderField.INST_DERIV_UND_INSTS] = data.loc[
            3:, OrderField.INST_DERIV_UND_INSTS
        ].apply(lambda x: [{"underlyingInstrumentCode": "FR0000120628"}])
        data.loc[7:8, OrderField.INST_EXT_UNDER_INST] = data.loc[
            7:8, OrderField.INST_EXT_UNDER_INST
        ].apply(lambda x: [{"instrumentIdCode": "FR0000120628"}])
        data.loc[10:10, OrderField.INST_EXT_UNDER_INST] = data.loc[
            10:10, OrderField.INST_EXT_UNDER_INST
        ].apply(lambda x: [{"instrumentIdCode": "FR0000120628"}])

        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 5000000,
            "evaluationType": None,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": True,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.insiderTradingV3.2.csv"
                        ]
                    }
                }
            }
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        strategy._process_client_vs_client = MagicMock()
        strategy._process_client_vs_client.return_value = process_client_vs_client_empty()
        strategy._process_prop_vs_client = MagicMock()
        strategy._process_prop_vs_client.return_value = process_prop_vs_client_empty()

        expected_col = DFColumns.GROUPING_COLUMN

        grouping_field, df = strategy._define_grouping_column(data=data)

        assert grouping_field == expected_col
        assert df[DFColumns.GROUPING_COLUMN].unique() == "FR0000120628"

    def test_time_window_threshold_multiple_front_running_order(
        self, helpers, mock_apply_strategy_kwargs
    ):
        """test case where there are multiple front_running_orders."""
        front_running_data = pd.read_csv(
            TEST_DATA.joinpath("test_timewindow_frontrunning_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )

        front_run_orders = pd.read_csv(
            TEST_DATA.joinpath("test_timewindow_frontrun_orders.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )
        thresholds = {
            "adv": 0.2,
            "evaluationType": "Portfolio Manager",
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "timeWindow": {"unit": "hours", "value": 5},
            "timeWindowFrontrunningExecution": {"unit": "seconds", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        res = strategy._evaluate_time_window_groups(
            front_running_data=front_running_data, front_run_orders=front_run_orders
        )

        assert res.shape[0] == 1
        assert res.loc[res[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING].shape[0] == 0
        assert res.loc[res[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN].shape[0] == 1

    def test_test_case_6_audit(self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Client vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_6
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = fake_data(TEST_DATA.joinpath("test_case_6_client_vs_client.csv"))

        strategy._apply_strategy_mini_batch(data=data)

        with open(strategy.auditor._audits_key_files["group_data_present"], "r") as f:
            data = f.readlines()
        assert any(
            ["Data present for Flow.CLIENT_VS_CLIENT flow threshold" in audits for audits in data]
        )
        assert len(data) == 3

    @pytest.mark.parametrize(
        "col",
        [OrderField.CLIENT_FILE_IDENTIFIER, OrderField.TRX_DTL_TR_CAPACITY],
    )
    def test_client_vs_client_no_columns_audit(
        self, helpers, mock_apply_strategy_kwargs, fake_data, col
    ):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 1000,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.01,
            "flow": "Client vs. Client",
            "evaluationType": "Trader",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_6
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = fake_data(TEST_DATA.joinpath("test_case_6_client_vs_client.csv"))
        data = data.drop(columns=[col])

        strategy._apply_strategy_mini_batch(data=data)
        with open(
            strategy.auditor._audits_key_files["required_col_missing_flow_threshold"], "r"
        ) as f:
            data = f.readlines()
        assert any(
            [
                f"For flow threshold Flow.CLIENT_VS_CLIENT, column {col} missing" in audits
                for audits in data
            ]
        )
        assert len(data) == 1

    def test_desk_vs_desk_no_columns_audit(self, helpers, mock_apply_strategy_kwargs):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 5000000,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
            "flow": "Desk vs. Desk",
            "evaluationType": None,
            "priceImprovement": True,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )
        data = data.drop(columns=[OrderField.TRD_ALGO_FIRM_DESKS_NAME])

        strategy._apply_strategy_mini_batch(data=data)

        with open(
            strategy.auditor._audits_key_files["required_col_missing_flow_threshold"], "r"
        ) as f:
            data = f.readlines()
        assert any(
            [
                f"For flow threshold Flow.DESK_VS_DESK, column {OrderField.TRD_ALGO_FIRM_DESKS_NAME} missing"
                in audits
                for audits in data
            ]
        )
        assert len(data) == 1

    def test_pad_vs_nonpad_no_columns_audit(self, helpers, mock_apply_strategy_kwargs, fake_data):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 5000000,
            "evaluationType": None,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": True,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_5
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = fake_data(TEST_DATA.joinpath("test_case_5_pad_vs_nonpad.csv"))
        data = data.drop(columns=[OrderField.MAR_DTL_PAD])

        strategy._apply_strategy_mini_batch(data=data)

        with open(
            strategy.auditor._audits_key_files["required_col_missing_flow_threshold"], "r"
        ) as f:
            data = f.readlines()
        assert any(
            [
                f"For flow threshold Flow.PAD_VS_NON_PAD, column {OrderField.MAR_DTL_PAD} missing"
                in audits
                for audits in data
            ]
        )
        assert len(data) == 2

    def test_desk_vs_desk_audit(self, helpers, mock_apply_strategy_kwargs, monkeypatch):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 5000000,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
            "flow": "Desk vs. Desk",
            "evaluationType": None,
            "priceImprovement": True,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        test_execution_data = pd.read_csv(
            TEST_DATA.joinpath("3_1_execution_data.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.DATE,
                DFColumns.TIME_WINDOW,
            ],
            index_col=0,
        )

        strategy.get_execution_data = MagicMock()
        strategy.get_execution_data.return_value = test_execution_data

        strategy._apply_strategy_mini_batch(data=data)

        with open(strategy.auditor._audits_key_files["group_data_present"], "r") as f:
            data = f.readlines()
        assert any(
            [
                re.search(
                    r"Data present for Flow.DESK_VS_DESK flow threshold.*Process data for front running and front run orders",
                    audit,
                )
                for audit in data
            ]
        )
        assert len(data) == 2

    def test_process_desk_vs_desk_audit(self, helpers, mock_apply_strategy_kwargs):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 5000000,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
            "flow": "Desk vs. Desk",
            "evaluationType": None,
            "priceImprovement": True,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        strategy._process_desk_vs_desk = MagicMock()
        strategy._process_desk_vs_desk.return_value = pd.DataFrame()

        strategy._apply_strategy_mini_batch(data=data)

        with open(strategy.auditor._audits_key_files["no_group_data"], "r") as f:
            data = f.readlines()
        any(
            [
                re.search(
                    r"There are no front run orders for Flow.DESK_VS_DESK flow threshold.*Process data for front running and front run orders",
                    audit,
                )
                for audit in data
            ]
        )

    def test_process_pad_vs_nonpad_audit(self, helpers, mock_apply_strategy_kwargs, fake_data):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "adv": None,
            "frontrunOrderVolume": 5000000,
            "evaluationType": None,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": True,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_5
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = fake_data(TEST_DATA.joinpath("test_case_5_pad_vs_nonpad.csv"))

        strategy._process_pad_vs_nonpad = MagicMock()
        strategy._process_pad_vs_nonpad.return_value = pd.DataFrame()

        strategy._apply_strategy_mini_batch(data=data)

        with open(strategy.auditor._audits_key_files["no_group_data"], "r") as f:
            data = f.readlines()
        assert any(
            [
                re.search(
                    r"There are no front run orders for Flow.PAD_VS_NON_PAD flow threshold.*Process data for front running and front run orders",
                    audit,
                )
                for audit in data
            ]
        )
        assert len(data) == 2

    def test_desk_vs_desk_no_execution_data_audit(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "frontrunOrderVolume": 5000000,
            "timeWindow": {"unit": "minutes", "value": 1},
            "volumeDifference": 0.01,
            "flow": "Desk vs. Desk",
            "evaluationType": None,
            "priceImprovement": True,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_3
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)

        data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        strategy.get_execution_data = MagicMock()
        strategy.get_execution_data.return_value = pd.DataFrame()

        strategy._apply_strategy_mini_batch(data=data)
        with open(strategy.auditor._audits_key_files["execution_data_absent"], "r") as f:
            data = f.readlines()
        assert any(
            [
                re.search(
                    r"Relevant executions data absent in dataset.*Check if relevant executions are present in the data",
                    audit,
                )
                for audit in data
            ]
        )
        assert len(data) == 1

    def test_case_options(self, helpers, mock_apply_strategy_kwargs, fake_data, monkeypatch):
        thresholds = {
            "timeWindow": {"unit": "minutes", "value": 15},
            "volumeDifference": 0.09,
            "flow": "Prop vs. Client",
            "priceImprovement": False,
            "evaluationType": "Trader",
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.frontrunning2.14.1.csv"
                        ]
                    }
                }
            }
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        monkeypatch.setattr(strategy, "get_related_orders", fake_get_related_orders)
        strategy.CrossProductActivityDisplay = MagicMock()
        strategy.CrossProductActivityDisplay.return_value = FakeCrossProduct()

        data = fake_data(TEST_DATA.joinpath("test_case_options.csv"))
        # column OrderField.INST_DERIV_UND_INSTS returns from ES as a List[dict]
        data.loc[2:, OrderField.INST_DERIV_UND_INSTS] = data.loc[
            2:, OrderField.INST_DERIV_UND_INSTS
        ].apply(lambda x: [{"underlyingInstrumentCode": "GB00BH4HKS39"}])

        strategy._apply_strategy_mini_batch(data=data)
        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert len(scenarios) == 2
        assert scenarios[0]["data"]["frontRunOrders"] == [
            "Order:frontrunning_14_6:1:NEWO:1694706179486"
        ]
        assert scenarios[0]["data"]["frontRunningOrder"] == [
            "Order:frontrunning_14_4:1:NEWO:1694706179486",
            "Order:frontrunning_14_8:2:NEWO:1694706179486",
        ]

    @patch.object(Repository, "search_after_query")
    def test_no_data_to_analyse(
        self, mock_search_after_query, helpers, mock_apply_strategy_kwargs, caplog
    ):
        """Test for front running v2 get cases when filter filla and parf is
        empty."""
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "timeWindow": {"unit": "minutes", "value": 60},
            "volumeDifference": 0.1,
            "flow": "Prop vs. Client",
            "priceImprovement": False,
            "evaluationType": "Trader",
        }

        strategy_module.CrossProductActivityDisplay = MagicMock()
        strategy_module.CrossProductActivityDisplay = FakeCrossProduct_test_case_18

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_9
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = pd.read_csv(
            TEST_DATA.joinpath("initial_query.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
        )

        mock_search_after_query.return_value = data

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["GB00BH4HKS39GBPXLON"]]

        strategy.queries._market_data_client.get_ric_map = MagicMock()
        strategy.queries._market_data_client.get_ric_map.return_value = {
            "GB00BH4HKS39GBPXLON": "VOD.L"
        }

        mock_group = MagicMock()
        mock_group.to_pandas.return_value = pd.DataFrame(
            {"instrument_ric_combination": [[("GB00BH4HKS39GBPXLON", "VOD.L", None)]]}
        )
        strategy.get_group = MagicMock(return_value=mock_group)
        caplog.set_level(logging.INFO)

        strategy.run()

        assert not os.path.exists(strategy.scenario_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )

        assert "No orders were fetched from ES that respect the algo filters" in caplog.messages

    def test_case_18(self, helpers, mock_apply_strategy_kwargs):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "timeWindow": {"unit": "minutes", "value": 60},
            "volumeDifference": 0.1,
            "flow": "Prop vs. Client",
            "priceImprovement": False,
            "evaluationType": "Trader",
        }

        strategy_module.CrossProductActivityDisplay = MagicMock()
        strategy_module.CrossProductActivityDisplay = FakeCrossProduct_test_case_18

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_9
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        data = pd.read_json(
            TEST_DATA.joinpath("test_cp_18.json"),
        )

        strategy.get_execution_data = MagicMock()
        strategy.get_execution_data.return_value = pd.DataFrame()

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = pd.read_json(
            TEST_DATA.joinpath("test_cp_18_related_results.json")
        )

        strategy._apply_strategy_mini_batch(data=data)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert len(scenarios) == 1
        # TODO rectify when data for the actual test case is available
        # assert len(scenarios[0].normalize.trades) == 4

        # scenarios[0].json

        # assert scenario["data"]["relatedActivityDetected"]

        # Assert the number of relations is 54
        # assert len(scenario["data"]["relatedRecords"]) == 6

    def test_case_21_2(self, helpers, mock_apply_strategy_kwargs, fake_data):
        thresholds = {
            "adv": 0.07,
            "timeWindow": {"unit": "minutes", "value": 5},
            "volumeDifference": 0.11,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.frontRunning.ENG-6528.csv",
                        ]
                    }
                }
            }
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        df_window = fake_data(TEST_DATA.joinpath("tc21_2_df_window.csv"))

        market_data = pd.read_csv(TEST_DATA.joinpath("tc21_market_data.csv"))

        result = strategy.check_adv(
            df=df_window,
            market_data_adv_df=market_data,
        )

        assert result.empty

        with open(
            strategy.auditor._audits_key_files["cal_market_data_adv_less_than_threshold"], "r"
        ) as f:
            data = f.readlines()
        assert any(
            [
                "The calculated ADV 0.06859577340739878 is less than the ADV threshold 0.07"
                in audits
                for audits in data
            ]
        )
        assert len(data) == 1

    @patch.object(Repository, "search_after_query")
    def test_frv2_missing_unique_identifier(
        self, mock_search_after_query, helpers, mock_apply_strategy_kwargs, fake_data, caplog
    ):
        thresholds = {
            "adv": 0.07,
            "timeWindow": {"unit": "minutes", "value": 5},
            "volumeDifference": 0.11,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        mock_search_after_query.return_value = fake_data(
            TEST_DATA.joinpath("missing_unique_identifier.csv")
        )

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            ["instrument1", "instrument2"]
        ]

        mock_group = MagicMock()
        mock_group.to_pandas.return_value = pd.DataFrame(
            {"instrument_ric_combination": [[("instrument1", "VOD.L", None)]]}
        )

        strategy.get_group = MagicMock(return_value=mock_group)
        caplog.set_level(logging.INFO)

        strategy.run()

        assert not os.path.exists(strategy.scenario_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )

        assert (
            "RIC mapping failed because the dataframe does not have the instrumentDetails.instrument.ext.instrumentUniqueIdentifier column. Skipping instruments ['instrument1']."
            in caplog.messages
        )

    @patch.object(Repository, "search_after_query")
    def test_frv2_missing_1_unique_identifier(
        self, mock_search_after_query, helpers, mock_apply_strategy_kwargs, fake_data
    ):
        thresholds = {
            "adv": 0.07,
            "timeWindow": {"unit": "minutes", "value": 5},
            "volumeDifference": 0.11,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        mock_search_after_query.return_value = fake_data(
            TEST_DATA.joinpath("missing_1_unique_identifier.csv")
        )

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            ["instrument1", "instrument2"]
        ]

        mock_group = MagicMock()
        mock_group.to_pandas.return_value = pd.DataFrame(
            {
                "instrument_ric_combination": [
                    [("instrument1", "ARX.TO", None), ("instrument2", "ARX.TO", None)]
                ]
            }
        )

        strategy.get_group = MagicMock(return_value=mock_group)

        strategy._apply_strategy_mini_batch = MagicMock()

        strategy.run()

        _, kwargs = strategy._apply_strategy_mini_batch.call_args
        assert kwargs["data"].shape[0] == 2

    @patch.object(Repository, "search_after_query")
    def test_frv2_not_missing_unique_identifier(
        self, mock_search_after_query, helpers, mock_apply_strategy_kwargs, monkeypatch, fake_data
    ):
        thresholds = {
            "adv": 0.07,
            "timeWindow": {"unit": "minutes", "value": 5},
            "volumeDifference": 0.11,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        mock_search_after_query.return_value = fake_data(
            TEST_DATA.joinpath("not_missing_unique_identifier.csv")
        )

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            ["instrument1", "instrument2"]
        ]

        mock_group = MagicMock()
        mock_group.to_pandas.return_value = pd.DataFrame(
            {
                "instrument_ric_combination": [
                    [("instrument1", "ARX.TO", None), ("instrument2", "ARX.TO", None)]
                ]
            }
        )

        strategy.get_group = MagicMock(return_value=mock_group)

        strategy._apply_strategy_mini_batch = MagicMock()

        strategy.run()

        _, kwargs = strategy._apply_strategy_mini_batch.call_args
        assert kwargs["data"].shape[0] == 2

    def test_fetch_order_executions_no_order_id_code(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch, fake_data
    ):
        thresholds = {
            "adv": 0.07,
            "timeWindow": {"unit": "minutes", "value": 5},
            "volumeDifference": 0.11,
            "flow": "PAD vs. Non Pad",
            "priceImprovement": False,
        }
        filters = {}

        data = fake_data(TEST_DATA.joinpath("get_execution_data_orders.csv"))

        import front_running_v2_apply_strategy.query

        front_running_v2_apply_strategy.query.get_market_client = MagicMock()
        front_running_v2_apply_strategy.query.get_market_client.return_value = FakeMarketClient()

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = data

        strategy.queries._get_order_executions_query = MagicMock()
        strategy.queries._get_order_executions_query.return_value = {"query": "Fake"}

        data_newos = data.copy()
        data_newos[OrderField.ORD_IDENT_ID_CODE] = range(0, data_newos.shape[0])

        execution_data = strategy.get_execution_data(df=data_newos)

        assert execution_data.empty

    def test_related_orders_client_vs_client(self, helpers, mock_apply_strategy_kwargs):
        """test that verifies client vs client check is applied to related
        orders."""
        thresholds = dict(
            adv=0.1,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.CLIENT_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("client_vs_client_test_process.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        front_running_orders = test_data.loc[test_data.loc[:, "orderType"] == "frontrunning"]

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert len(result) == 2
        assert (
            result.loc[:, OrderField.CLIENT_FILE_IDENTIFIER]
            != front_running_orders.loc[0, OrderField.CLIENT_FILE_IDENTIFIER]
        ).all()
        assert "DEAL" not in result.loc[:, OrderField.TRX_DTL_TR_CAPACITY].tolist()

        test_data[OrderField.CLIENT_FILE_IDENTIFIER] = "Client 1"

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert result.empty

    def test_related_orders_desk_vs_desk(self, helpers, mock_apply_strategy_kwargs):
        """test that verifies desk vs desk check is applied to related
        orders."""
        thresholds = dict(
            adv=0.1,
            assetClass="",
            flow=Flow.DESK_VS_DESK,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("3_1_desk_vs_desk.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        front_running_orders = test_data.loc[test_data.loc[:, "orderType"] == "frontrunning"]

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert len(result) == 1
        assert (
            result.loc[:, OrderField.TRD_ALGO_FIRM_DESKS_NAME]
            != front_running_orders.loc[0, OrderField.TRD_ALGO_FIRM_DESKS_NAME]
        ).all()

        test_data[OrderField.TRD_ALGO_FIRM_DESKS_NAME] = "desk1"

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert result.empty

    def test_related_orders_without_ecbf_rate_desk_vs_desk(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """test that verifies desk vs desk check is applied to related orders
        without ecbf rate as 0.0 dropping the related orders."""
        thresholds = dict(
            assetClass="",
            flow=Flow.DESK_VS_DESK,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        def fake_search_after_query(*args, **kwargs):
            fake_search_after_query.counter += 1
            if fake_search_after_query.counter == 1:
                return pd.read_csv(
                    TEST_DATA.joinpath("related_orders.csv"),
                    parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
                    index_col=0,
                )

            return pd.read_csv(
                TEST_DATA.joinpath("related_orders_childs.csv"),
                parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
                index_col=0,
            )

        fake_search_after_query.counter = 0

        related_orders_list = ["foo"]

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.base.Repository,
            "search_after_query",
            fake_search_after_query,
        )

        related_orders_result = strategy.get_related_orders_data(related_orders=related_orders_list)

        assert related_orders_result.empty

    def test_get_related_orders_data_empty_results_desk_vs_desk(
        self, helpers, mock_apply_strategy_kwargs
    ):
        """test get_related_orders_data when no related data is returned."""
        thresholds = dict(
            adv=0.1,
            assetClass="",
            flow=Flow.DESK_VS_DESK,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        related_orders_list = ["foo"]

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = pd.DataFrame()

        related_orders_result = strategy.get_related_orders_data(related_orders=related_orders_list)

        assert related_orders_result.empty

    def test_related_orders_prop_vs_client(self, helpers, mock_apply_strategy_kwargs):
        """test that verifies prop vs client check is applied to related
        orders."""
        thresholds = dict(
            adv=0.1,
            assetClass="",
            evaluationType=EvaluationType.TRADER,
            flow=Flow.PROP_VS_CLIENT,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("2_1_prop_vs_client_trader.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        front_running_orders = test_data.loc[test_data.loc[:, "orderType"] == "frontrunning"]

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert len(result) == 1
        assert "DEAL" not in result.loc[:, OrderField.TRX_DTL_TR_CAPACITY].tolist()

        test_data[OrderField.TRX_DTL_TR_CAPACITY] = "DEAL"

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert result.empty

    def test_related_orders_pad_vs_nonpad(self, helpers, mock_apply_strategy_kwargs):
        """test that verifies pad vs nonpad check is applied to related
        orders."""
        thresholds = dict(
            adv=0.1,
            assetClass="",
            flow=Flow.PAD_VS_NON_PAD,
            frontrunOrderVolume=5000000,
            priceImprovement=True,
            timeWindow={"unit": TimeUnit.SECONDS, "value": 30},
            volumeDifference=0.01,
        )

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("4_1_pad_vs_nonpad.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        front_running_orders = test_data.loc[test_data.loc[:, "orderType"] == "frontrunning"]

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert len(result) == 1
        assert not result.loc[:, OrderField.MAR_DTL_PAD].values[0]

        test_data[OrderField.MAR_DTL_PAD] = True

        result = strategy.check_related_orders(
            front_running_orders=front_running_orders,
            related_orders=test_data,
        )

        assert result.empty

    @patch("front_running_v2_apply_strategy.alerts.CrossProductActivityDisplay")
    def test_create_scenario_w_cross_product(
        self, mock_crossproductactivitydisplay, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """Based on Test Case 18 This tests the method create_scenario when we
        have related_record in the alert."""
        thresholds = dict(
            flow=Flow.PROP_VS_CLIENT,
            priceImprovement=False,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 60},
            volumeDifference=0.1,
            crossProductActivity=True,
        )
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters={}
        )

        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        alert_df = pd.read_csv(
            TEST_DATA.joinpath("tc18_alert_df.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        related_activity = pd.read_csv(
            TEST_DATA.joinpath("tc18_alert_df_related_activity.csv"),
            parse_dates=[OrderField.DATE],
            index_col=0,
        )
        enrich_scenario_original = getattr(CrossProductActivityDisplay, "enrich_scenario")

        mock_instance = mock_crossproductactivitydisplay.return_value
        mock_instance.enrich_scenario = MethodType(enrich_scenario_original, mock_instance)

        def mock_set_scenario_m(*args, **kwargs):
            mock_instance.scenario = kwargs["scenario"]
            mock_instance.records = kwargs["list_of_orders"]

        # Patch set_scenario method of the instance
        mock_instance.set_scenario.side_effect = mock_set_scenario_m
        mock_instance.related_activity.return_value = related_activity
        strategy._create_alerts(alert_df)
        strategy._write_scenarios_from_apply_strategy_result()

        scenarios = load_ndjson_into_list(strategy.scenario_local_file_path)
        expected_related_records_ids = [
            "Client_Related_2:1:NEWO",
            "Client_Related_3:1:NEWO",
            "Client_Related_4:2:NEWO",
            "Client_Related_5:1:NEWO",
        ]
        expected_correlations_scores = [1.0, None, None, 0.3887986037]

        assert len(scenarios) == 1

        scenario = json.loads(scenarios[0]["raw"])

        # Assert both relatedActivity fields ate flagged as True
        assert scenario["additionalFields"]["topLevel"]["relatedActivityAlerted"]
        assert scenario["additionalFields"]["topLevel"]["relatedActivityDetected"]

        # Assert the number of relations is 4
        assert len(scenario["additionalFields"]["topLevel"]["relatedRecords"]) == 4

        related_records = scenario["additionalFields"]["topLevel"]["relatedRecords"]

        id_list = []
        correlation_scores = []
        for item in related_records:
            id_list.append(item.get("&id"))
            correlation_scores.append(item.get("correlationScore"))

        assert len(set(id_list)) == 4
        assert len(set(id_list) - set(expected_related_records_ids)) == 0

        assert expected_correlations_scores == correlation_scores

    @patch("front_running_v2_apply_strategy.alerts.CrossProductActivityDisplay")
    def test_scenario_wo_related_activity(
        self, mock_crossproductactivitydisplay, helpers, mock_apply_strategy_kwargs
    ):
        """Based on Test Case 18 This tests the method create_scenario when we
        have related_record in the alert but the related_activity is not
        calculated (it should still have the records in the alert)"""
        thresholds = dict(
            flow=Flow.PROP_VS_CLIENT,
            priceImprovement=False,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 60},
            volumeDifference=0.1,
            crossProductActivity=True,
        )
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters={}
        )

        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        alert_df = pd.read_csv(
            TEST_DATA.joinpath("tc18_alert_df.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        enrich_scenario_original = getattr(CrossProductActivityDisplay, "enrich_scenario")
        add_related_records_to_scenario_original = getattr(
            CrossProductActivityDisplay, "add_related_records_to_scenario"
        )

        mock_instance = mock_crossproductactivitydisplay.return_value
        mock_instance.enrich_scenario = MethodType(enrich_scenario_original, mock_instance)
        mock_instance.add_related_records_to_scenario = MethodType(
            add_related_records_to_scenario_original, mock_instance
        )

        def mock_set_scenario_m(*args, **kwargs):
            mock_instance.scenario = kwargs["scenario"]
            mock_instance.records = kwargs["list_of_orders"]

        # Patch set_scenario method of the instance
        mock_instance.set_scenario.side_effect = mock_set_scenario_m
        mock_instance.related_activity.return_value = pd.DataFrame()

        strategy._create_alerts(alert_df)
        strategy._write_scenarios_from_apply_strategy_result()

        scenarios = load_ndjson_into_list(strategy.scenario_local_file_path)
        expected_related_records_ids = [
            "Client_Related_2:1:NEWO",
            "Client_Related_3:1:NEWO",
            "Client_Related_4:2:NEWO",
            "Client_Related_5:1:NEWO",
        ]

        assert len(scenarios) == 1

        scenario = json.loads(scenarios[0]["raw"])
        # Assert the relatedActivityAlerted is flagged as True
        assert scenario["additionalFields"]["topLevel"]["relatedActivityAlerted"]

        # relatedActivityDetected should be False as there was no related activity
        assert scenario["additionalFields"]["topLevel"]["relatedActivityDetected"] is False

        # Assert the number of relations is 4
        assert len(scenario["additionalFields"]["topLevel"]["relatedRecords"]) == 4

        related_records = scenario["additionalFields"]["topLevel"]["relatedRecords"]

        id_list = []
        correlation_scores = []
        for item in related_records:
            id_list.append(item.get("&id"))
            correlation_scores.append(item.get("correlationScore"))

        assert len(set(id_list)) == 4
        assert len(set(id_list) - set(expected_related_records_ids)) == 0

        assert all(pd.isnull(correlation_scores))

    def test_time_window_threshold_no_front_run_orders(self, helpers, mock_apply_strategy_kwargs):
        front_running_data = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frunning.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )

        front_run_orders = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frunning.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )
        thresholds = {
            "adv": None,
            "evaluationType": "Trader",
            "flow": "Prop vs. Client",
            "priceImprovement": True,
            "timeWindow": {"unit": "hours", "value": 60},
            "timeWindowFrontrunningExecution": {"unit": "hours", "value": 10},
            "volumeDifference": 0.1,
        }
        filters = {}
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        res = strategy._evaluate_time_window_groups(
            front_running_data=front_running_data, front_run_orders=front_run_orders
        )

        assert res.empty

    def test_time_window_threshold_no_front_run_orders_after_time_window(
        self, helpers, mock_apply_strategy_kwargs
    ):
        """Verifies front runs orders outside the timewindow are removed:

        happens with related orders
        """
        front_running_data = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frunning.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )

        front_run_orders = pd.read_csv(
            TEST_DATA.joinpath("test_time_window_exec_frun_outside_window.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                OrderField.DATE,
                OrderField.TS_TRADING_DATE_TIME,
            ],
            index_col=0,
        )
        thresholds = {
            "adv": None,
            "evaluationType": "Trader",
            "flow": "Prop vs. Client",
            "priceImprovement": True,
            "timeWindow": {"unit": "hours", "value": 60},
            "timeWindowFrontrunningExecution": {"unit": "hours", "value": 10},
            "volumeDifference": 0.1,
            "crossProductActivity": True,
        }
        filters = {}
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)
        res = strategy._evaluate_time_window_groups(
            front_running_data=front_running_data, front_run_orders=front_run_orders
        )

        assert res.empty

    def test_related_records_duplicate_removal_desk_vs_desk(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """Verify the duplicated related records are removed."""

        thresholds = {
            "frontrunOrderVolume": 50000,
            "timeWindow": {"unit": "minutes", "value": 240},
            "volumeDifference": 1,
            "flow": "Desk vs. Desk",
            "priceImprovement": False,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        def related_orders(*args, **kwargs):
            df = pd.read_csv(
                TEST_DATA.joinpath("front_running_csv_duplicated_related.csv"),
                parse_dates=[
                    OrderField.TS_ORD_SUBMITTED,
                    OrderField.DATE,
                    DFColumns.TIME_WINDOW,
                ],
                index_col=0,
            )

            df[DFColumns.FRONT_RUN_ORDERS] = df[DFColumns.FRONT_RUN_ORDERS].apply(ast.literal_eval)

            return df

        monkeypatch.setattr(strategy, "get_related_orders", related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("front_running_csv_duplicated_related.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert df_window.shape[0] == 2

    def test_related_records_duplicate_removal_client_vs_client(
        self, helpers, mock_apply_strategy_kwargs, monkeypatch
    ):
        """Verify the duplicated related records are removed when there are
        more than one original records."""

        thresholds = {
            "frontrunOrderVolume": 50000,
            "timeWindow": {"unit": "minutes", "value": 240},
            "volumeDifference": 1,
            "flow": "Client vs. Client",
            "priceImprovement": False,
            "crossProductActivity": True,
        }

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=FILTER_1
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        def related_orders(*args, **kwargs):
            df = pd.read_csv(
                TEST_DATA.joinpath("duplicated_front_running_client_vs_client.csv"),
                parse_dates=[
                    OrderField.TS_ORD_SUBMITTED,
                    OrderField.DATE,
                    DFColumns.TIME_WINDOW,
                ],
                index_col=0,
            )

            df[DFColumns.FRONT_RUN_ORDERS] = df[DFColumns.FRONT_RUN_ORDERS].apply(ast.literal_eval)

            return df

        monkeypatch.setattr(strategy, "get_related_orders", related_orders)

        test_data = pd.read_csv(
            TEST_DATA.joinpath("duplicated_front_running_client_vs_client.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        df_sorted = test_data.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        df_sorted[DFColumns.TIME_WINDOW] = (
            df_sorted.loc[:, OrderField.TS_ORD_SUBMITTED] + strategy._th_time_window
        )

        df_time_window_tuple = strategy._time_window_threshold(front_running=df_sorted)
        for df_window in df_time_window_tuple[0]:
            if df_window.empty or df_window.shape[0] == 1:
                continue

            assert df_window.shape[0] == 3

    def test_create_scenario_cp_w_null_values(self, helpers, mock_apply_strategy_kwargs):
        """Based on Test Case 18 This tests the method create_scenario when we
        have related_record with nan as originalRecordMetaKey."""
        thresholds = dict(
            flow=Flow.PROP_VS_CLIENT,
            priceImprovement=False,
            timeWindow={"unit": TimeUnit.MINUTES, "value": 60},
            volumeDifference=0.1,
        )
        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters={}
        )

        strategy_module.CrossProductActivityDisplay = MagicMock()
        strategy_module.CrossProductActivityDisplay = FakeCrossProduct_test_case_18

        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        alert_df = pd.read_csv(
            TEST_DATA.joinpath("tc18_alert_df_null_original_metakey.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
            index_col=0,
        )

        strategy._create_alerts(alert_df)

        scenarios = load_ndjson_into_list(strategy.result_local_file_path)

        assert len(scenarios) == 1

        scenario = scenarios[0]

        # Assert both relatedActivity fields ate flagged as False since all related_records
        # did not have a originalRecordMetaKey
        assert scenario["data"]["relatedActivityAlerted"] is False
        assert scenario["data"]["relatedActivityDetected"] is False
        assert "related_records" not in scenario["data"]

    def test_case_mc_99(self, helpers, mock_apply_strategy_kwargs, fake_data):
        """Verify new modification calculates the market adv correctly when
        there are cross product related records."""
        thresholds = {
            "adv": 2.0,
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.11,
            "flow": "Client vs. Client",
            "priceImprovement": False,
        }
        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        df_window = fake_data(TEST_DATA.joinpath("mc_99_df_window.csv"))

        market_data = pd.read_csv(TEST_DATA.joinpath("mc_99_market_data_adv_df.csv"))

        result = strategy.check_adv(
            df=df_window,
            market_data_adv_df=market_data,
        )

        assert result.empty
