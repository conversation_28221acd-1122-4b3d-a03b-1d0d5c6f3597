{"took": 3, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": 16, "max_score": 0.0, "hits": [{"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_1:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_1:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:56:55Z", "ultimateVenue": "XEUR", "venue": "XEUR"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO61MTCMTO61120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:56:55Z", "orderStatusUpdated": "2023-02-27T07:56:55Z", "orderSubmitted": "2023-02-27T07:56:55Z", "tradingDateTime": "2023-02-27T07:56:55Z"}, "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4142.0, "orderStatus": "NEWO", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "0", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_1", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_1", "internalOrderIdCode": "MTCMTO_6_1", "orderIdCode": "MTCMTO_6_1"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_1:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": ********.8, "BGN": ********.6, "BRL": *********.6, "CAD": ********.8, "CHF": ********.8, "CNY": *********.********, "CZK": **********.0, "DKK": *********.2, "EUR": ********.0, "GBP": ********.*********, "HKD": *********.6, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.4, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.4000001, "MYR": *********.********, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.2, "THB": **********.0, "TRY": *********.8000001, "USD": ********.8, "ZAR": *********.8, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1200.0, "remainingQuantity": 200.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "1e8ecb17f7b79fc33da156525f45e63e1ec032d5c47cce707c8f36f1bb5a1c4d", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_2:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_2:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 950.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:21Z", "ultimateVenue": "XEUR", "venue": "XEUR"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO62MTCMTO62120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:21Z", "orderStatusUpdated": "2023-02-27T07:58:21Z", "orderSubmitted": "2023-02-27T07:58:21Z", "tradingDateTime": "2023-02-27T07:58:21Z"}, "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4145.0, "orderStatus": "NEWO", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "1", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_2", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_2", "internalOrderIdCode": "MTCMTO_6_2", "orderIdCode": "MTCMTO_6_2"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_2:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": ********.0, "BGN": ********.0, "BRL": *********.0, "CAD": ********.0, "CHF": ********.0, "CNY": *********.0, "CZK": *********.0, "DKK": *********.0, "EUR": ********.0, "GBP": ********.5, "HKD": *********.0, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.0, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.0, "MYR": *********.0, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.0, "THB": **********.0, "TRY": *********.0, "USD": ********.********, "ZAR": *********.0, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 50.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "5f12512c7a6f6e1bf050d644d79625509c3055c3620404c00bb0a580d34c8339", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_3:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_3:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:25Z", "ultimateVenue": "XEUR", "venue": "XEUR"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO63MTCMTO63120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:25Z", "orderStatusUpdated": "2023-02-27T07:58:25Z", "orderSubmitted": "2023-02-27T07:58:25Z", "tradingDateTime": "2023-02-27T07:58:25Z"}, "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4148.0, "orderStatus": "NEWO", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "2", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_3", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_3", "internalOrderIdCode": "MTCMTO_6_3", "orderIdCode": "MTCMTO_6_3"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_3:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": ********.********, "BGN": *********.2, "BRL": *********.2, "CAD": ********.6, "CHF": ********.6, "CNY": *********.2, "CZK": **********.0, "DKK": *********.4, "EUR": ********.0, "GBP": ********.52, "HKD": *********.********, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.8, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": **********.8000001, "MYR": *********.4, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.4, "THB": **********.0, "TRY": **********.6000001, "USD": ********.*********, "ZAR": **********.6, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1300.0, "remainingQuantity": 300.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "475190fcdbed46afe7666097da3800ffa1be64fdfdbd24e2b62aa3c7a35dde63", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_4:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_4:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:59:30Z", "ultimateVenue": "XEUR", "venue": "XEUR"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO64MTCMTO64120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:59:30Z", "orderStatusUpdated": "2023-02-27T07:59:30Z", "orderSubmitted": "2023-02-27T07:59:30Z", "tradingDateTime": "2023-02-27T07:59:30Z"}, "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4151.0, "orderStatus": "NEWO", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "3", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_4", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_4", "internalOrderIdCode": "MTCMTO_6_4", "orderIdCode": "MTCMTO_6_4"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_4:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": ********.0, "BGN": ********.0, "BRL": *********.0, "CAD": ********.0, "CHF": ********.0, "CNY": *********.0, "CZK": *********.0, "DKK": *********.0, "EUR": ********.0, "GBP": ********.0, "HKD": *********.0, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.0, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.0, "MYR": *********.0, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.0, "THB": **********.0, "TRY": *********.0, "USD": ********.********, "ZAR": *********.0, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 0.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "4a77a290af90263cb91ebfc86c1a102b852423664e1245d270a3b67a35b0f671", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "priceFormingData.remainingQuantity", "message": "`Order Remaining Quantity` must be greater than 0", "code": "SE_DV-128", "category": "Quantities", "modules_affected": ["Market Abuse", "Best Execution", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_5:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_5:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:56:55Z", "ultimateVenue": "IFEU", "venue": "IFEU"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO65MTCMTO6520230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:56:55Z", "orderStatusUpdated": "2023-02-27T07:56:55Z", "orderSubmitted": "2023-02-27T07:56:55Z", "tradingDateTime": "2023-02-27T07:56:55Z"}, "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "4", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_5", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_5", "internalOrderIdCode": "MTCMTO_6_5", "orderIdCode": "MTCMTO_6_5"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_5:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.2512792, "CAD": *********.********, "CHF": ********.********, "CNY": *********.7816942, "CZK": **********.2433202, "DKK": *********.1432632, "EUR": ********.********, "GBP": ********.********, "HKD": *********.0233088, "HUF": ***********.21888, "IDR": *************.6545, "ILS": *********.********, "INR": **********.599205, "JPY": ***********.596931, "KRW": ************.0995, "MXN": **********.3013077, "MYR": *********.6048892, "NOK": *********.3479251, "NZD": *********.********, "PHP": **********.916999, "PLN": *********.********, "RON": *********.********, "SEK": *********.8470722, "SGD": *********.********, "THB": **********.3428087, "TRY": 15********.379193, "USD": ********.0, "ZAR": **********.3052871, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 0.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "c22ac5a8a5c9d5e919bf440136f05b08d6d3f18eb855e54dd7b3e39f70bc1cbc", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "priceFormingData.remainingQuantity", "message": "`Order Remaining Quantity` must be greater than 0", "code": "SE_DV-128", "category": "Quantities", "modules_affected": ["Market Abuse", "Best Execution", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_6:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_6:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:21Z", "ultimateVenue": "IFEU", "venue": "IFEU"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO66MTCMTO6620230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:21Z", "orderStatusUpdated": "2023-02-27T07:58:21Z", "orderSubmitted": "2023-02-27T07:58:21Z", "tradingDateTime": "2023-02-27T07:58:21Z"}, "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "5", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_6", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_6", "internalOrderIdCode": "MTCMTO_6_6", "orderIdCode": "MTCMTO_6_6"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_6:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.0947508, "CAD": *********.********, "CHF": ********.********, "CNY": *********.2736404, "CZK": **********.0977829, "DKK": *********.6120902, "EUR": ********.********, "GBP": ********.********, "HKD": *********.467311, "HUF": ***********.916622, "IDR": *************.6555, "ILS": *********.645253, "INR": **********.86697, "JPY": ***********.772598, "KRW": ************.96211, "MXN": **********.8375974, "MYR": *********.********, "NOK": *********.4388858, "NZD": *********.234982, "PHP": **********.555429, "PLN": *********.2302445, "RON": *********.5868865, "SEK": *********.1534964, "SGD": *********.********, "THB": **********.0598826, "TRY": **********.5698316, "USD": ********.0, "ZAR": **********.1694145, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 0.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "54d2fe46b4f7da8fb64b968118b2564b653120cb4131aa5e8953a06b1d75225c", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "priceFormingData.remainingQuantity", "message": "`Order Remaining Quantity` must be greater than 0", "code": "SE_DV-128", "category": "Quantities", "modules_affected": ["Market Abuse", "Best Execution", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_7:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_7:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 975.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:25Z", "ultimateVenue": "IFEU", "venue": "IFEU"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO67MTCMTO6720230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:25Z", "orderStatusUpdated": "2023-02-27T07:58:25Z", "orderSubmitted": "2023-02-27T07:58:25Z", "tradingDateTime": "2023-02-27T07:58:25Z"}, "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "6", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_7", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_7", "internalOrderIdCode": "MTCMTO_6_7", "orderIdCode": "MTCMTO_6_7"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_7:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.0545765, "BRL": *********.********, "CAD": *********.********, "CHF": ********.********, "CNY": *********.8885732, "CZK": **********.9158616, "DKK": *********.698124, "EUR": ********.********, "GBP": ********.********, "HKD": *********.272314, "HUF": ***********.288807, "IDR": *************.1572, "ILS": *********.5378056, "INR": **********.951678, "JPY": ***********.242186, "KRW": ************.04039, "MXN": **********.0079594, "MYR": *********.********, "NOK": *********.5525869, "NZD": *********.5497442, "PHP": **********.103469, "PLN": *********.4297897, "RON": *********.339966, "SEK": *********.2865267, "SGD": *********.1267766, "THB": **********.956226, "TRY": **********.30813, "USD": ********.0, "ZAR": **********.249574, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 25.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "a96e0330751c22a27f644e426bdd6291b3ac9d9f975b7f4f1432d27c7f400d65", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "MTCMTO_6_8:1:NEWO", "_score": 0.0, "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_8:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 980.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:59:30Z", "ultimateVenue": "IFEU", "venue": "IFEU"}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO68MTCMTO6820230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:59:30Z", "orderStatusUpdated": "2023-02-27T07:59:30Z", "orderSubmitted": "2023-02-27T07:59:30Z", "tradingDateTime": "2023-02-27T07:59:30Z"}, "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "sourceIndex": "7", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_8", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_8", "internalOrderIdCode": "MTCMTO_6_8", "orderIdCode": "MTCMTO_6_8"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "Order:MTCMTO_6_8:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.********, "CAD": *********.5730529, "CHF": ********.********, "CNY": *********.2575328, "CZK": **********.8067088, "DKK": *********.5497442, "EUR": ********.********, "GBP": ********.********, "HKD": *********.3553157, "HUF": ***********.312115, "IDR": *************.658, "ILS": *********.0733372, "INR": **********.402503, "JPY": ***********.123936, "KRW": ************.68735, "MXN": **********.9101765, "MYR": *********.0989199, "NOK": *********.6208074, "NZD": *********.9386015, "PHP": **********.832292, "PLN": *********.1495169, "RON": *********.5918136, "SEK": *********.7663447, "SGD": *********.********, "THB": **********.4940314, "TRY": **********.951109, "USD": ********.0, "ZAR": **********.8976693, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "remainingQuantity": 20.0}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "1a1b1690bc06678c96ff5c7c281fb2f32b64dd353d4c70020e58beece35d71ae", "counterpartyFileIdentifier": "account:counterparty3", "&validationErrors": [{"field_path": "buyer.0.officialIdentifiers.mifirId", "message": "Missing MiFIR ID for `Buyer`", "code": "SE_DV-85", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "buyer.0.officialIdentifiers.branchCountry", "message": "`Buyer Country of Branch` must be populated where `Buyer` is a `Client`", "code": "SE_DV-89", "category": "Parties", "modules_affected": ["Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}], "&user": "system"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_5:1:MTCMTO65MTCMTO6520230227BUYI:FILL:2023-02-27T07:56:55Z:0.0", "_score": 0.0, "_routing": "MTCMTO_6_5:1:NEWO", "_parent": "MTCMTO_6_5:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_5:1:MTCMTO65MTCMTO6520230227BUYI:FILL:2023-02-27T07:56:55Z:0.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 81.87, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:56:55Z", "ultimateVenue": "IFEU", "venue": "IFEU", "pricingDetails": {"nearestQuote": 81.55, "marketPrice": 81.55, "percentVsSlippage": -0.***************, "vwap": 81.64, "openPrice": 81.93, "percentVsBidAskSpread": 0.***************, "percentVsVwap": 0.***************, "arrivalPrice": 81.55, "bidAskSpread": 0.***************, "percentVsHighPrice": -0.**************, "percentVsLowPrice": 0.*************, "percentVsNearestQuote": 0.***************, "lowPrice": 80.49, "highPrice": 82.22, "percentVsOhlcPrice": 0.00538885486834, "percentVsClose": 0.009696225836146, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": 0.005************, "askPrice": 81.55, "percentVsMidPointPrice": 0.***************, "ohlc": 81.43, "percentVsAskPrice": 0.***************, "percentVsMarket": 0.***************, "percentVsBidPrice": 0.***************, "previousDayClosePrice": 81.87, "slippagePrice": -0.***************, "bidPrice": 81.52, "midPointPrice": 81.535, "previousDayOpenPrice": 81.45, "percentVsPreviousDayClose": 0.0, "percentVsArrival": 0.***************, "percentVsOpen": -0.***************, "closePrice": 81.08}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO65MTCMTO6520230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:56:55Z", "orderStatusUpdated": "2023-02-27T07:56:55Z", "orderSubmitted": "2023-02-27T07:56:55Z", "tradingDateTime": "2023-02-27T07:56:55Z"}, "&parent": "MTCMTO_6_5:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "FILL", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "4", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD", "RIC": "LCON3"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_5", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_5", "internalOrderIdCode": "MTCMTO_6_5", "orderIdCode": "MTCMTO_6_5", "transactionRefNo": "MTCMTO65MTCMTO6520230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_5:1:MTCMTO65MTCMTO6520230227BUYI:FILL:2023-02-27T07:56:55Z:0.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.2512792, "CAD": *********.********, "CHF": ********.********, "CNY": *********.7816942, "CZK": **********.2433202, "DKK": *********.1432632, "EUR": ********.********, "GBP": ********.********, "HKD": *********.0233088, "HUF": ***********.21888, "IDR": *************.6545, "ILS": *********.********, "INR": **********.599205, "JPY": ***********.596931, "KRW": ************.0995, "MXN": **********.3013077, "MYR": *********.6048892, "NOK": *********.3479251, "NZD": *********.********, "PHP": **********.916999, "PLN": *********.********, "RON": *********.********, "SEK": *********.8470722, "SGD": *********.********, "THB": **********.3428087, "TRY": 15********.379193, "USD": ********.0, "ZAR": **********.3052871, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 81.87, "remainingQuantity": 0.0, "tradedQuantity": 1000.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 81.55, "nativeCurrency": "USD"}, "source": "PRIMARY", "timestamp": "2023-02-27T07:55:41.983Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 81.55, "nativeCurrency": "USD"}, "mid": {"native": 81.535, "nativeCurrency": "USD"}, "source": "PRIMARY", "bid": {"native": 81.52, "nativeCurrency": "USD"}, "timestamp": "2023-02-27T07:56:54.932Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "0afc09330d35519ba6db41d93887abf900a636ded5ad1ff7e27dc60c6758145f", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_6:1:MTCMTO66MTCMTO6620230227BUYI:FILL:2023-02-27T07:58:21Z:0.0", "_score": 0.0, "_routing": "MTCMTO_6_6:1:NEWO", "_parent": "MTCMTO_6_6:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_6:1:MTCMTO66MTCMTO6620230227BUYI:FILL:2023-02-27T07:58:21Z:0.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 81.89, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:21Z", "ultimateVenue": "IFEU", "venue": "IFEU", "pricingDetails": {"nearestQuote": 81.63, "marketPrice": 81.63, "percentVsSlippage": -0.***************, "vwap": 81.64, "openPrice": 81.93, "percentVsBidAskSpread": 0.***************, "percentVsVwap": 0.***************, "arrivalPrice": 81.63, "bidAskSpread": 0.01********99996, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.***************, "percentVsNearestQuote": 0.***************, "lowPrice": 80.49, "highPrice": 82.22, "percentVsOhlcPrice": 0.005633112907176, "percentVsClose": 0.009940479842916, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": 0.005387535202645, "askPrice": 81.63, "percentVsMidPointPrice": 0.***************, "ohlc": 81.43, "percentVsAskPrice": 0.***************, "percentVsMarket": 0.***************, "percentVsBidPrice": 0.***************, "previousDayClosePrice": 81.87, "slippagePrice": -0.***************, "bidPrice": 81.61, "midPointPrice": 81.62, "previousDayOpenPrice": 81.45, "percentVsPreviousDayClose": 0.***************, "percentVsArrival": 0.***************, "percentVsOpen": -0.***************, "closePrice": 81.08}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO66MTCMTO6620230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:21Z", "orderStatusUpdated": "2023-02-27T07:58:21Z", "orderSubmitted": "2023-02-27T07:58:21Z", "tradingDateTime": "2023-02-27T07:58:21Z"}, "&parent": "MTCMTO_6_6:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "FILL", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "5", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD", "RIC": "LCON3"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_6", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_6", "internalOrderIdCode": "MTCMTO_6_6", "orderIdCode": "MTCMTO_6_6", "transactionRefNo": "MTCMTO66MTCMTO6620230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_6:1:MTCMTO66MTCMTO6620230227BUYI:FILL:2023-02-27T07:58:21Z:0.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.0947508, "CAD": *********.********, "CHF": ********.********, "CNY": *********.2736404, "CZK": **********.0977829, "DKK": *********.6120902, "EUR": ********.********, "GBP": ********.********, "HKD": *********.467311, "HUF": ***********.916622, "IDR": *************.6555, "ILS": *********.645253, "INR": **********.86697, "JPY": ***********.772598, "KRW": ************.96211, "MXN": **********.8375974, "MYR": *********.********, "NOK": *********.4388858, "NZD": *********.234982, "PHP": **********.555429, "PLN": *********.2302445, "RON": *********.5868865, "SEK": *********.1534964, "SGD": *********.********, "THB": **********.0598826, "TRY": **********.5698316, "USD": ********.0, "ZAR": **********.1694145, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 81.89, "remainingQuantity": 0.0, "tradedQuantity": 1000.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 81.63, "nativeCurrency": "USD"}, "source": "PRIMARY", "timestamp": "2023-02-27T07:58:13.172Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 81.63, "nativeCurrency": "USD"}, "mid": {"native": 81.62, "nativeCurrency": "USD"}, "source": "PRIMARY", "bid": {"native": 81.61, "nativeCurrency": "USD"}, "timestamp": "2023-02-27T07:58:19.592Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "3052883b33405e2bdc102c891cebe5a7b81525d381d065c39e364f48379628c2", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_7:1:MTCMTO67MTCMTO6720230227BUYI:PARF:2023-02-27T07:58:25Z:25.0", "_score": 0.0, "_routing": "MTCMTO_6_7:1:NEWO", "_parent": "MTCMTO_6_7:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_7:1:MTCMTO67MTCMTO6720230227BUYI:PARF:2023-02-27T07:58:25Z:25.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 81.915, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 975.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:25Z", "ultimateVenue": "IFEU", "venue": "IFEU", "pricingDetails": {"nearestQuote": 81.62, "marketPrice": 81.63, "percentVsSlippage": -0.***************, "vwap": 81.64, "openPrice": 81.93, "percentVsBidAskSpread": 0.***************, "percentVsVwap": 0.***************, "arrivalPrice": 81.62, "bidAskSpread": 0.**************, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.***************, "percentVsNearestQuote": 0.***************, "lowPrice": 80.49, "highPrice": 82.22, "percentVsOhlcPrice": 0.005938351342251, "percentVsClose": 0.010245713058683, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": 0.005692773849968, "askPrice": 81.62, "percentVsMidPointPrice": 0.***************, "ohlc": 81.43, "percentVsAskPrice": 0.***************, "percentVsMarket": 0.***************, "percentVsBidPrice": 0.***************, "previousDayClosePrice": 81.87, "slippagePrice": -0.***************, "bidPrice": 81.6, "midPointPrice": 81.61, "previousDayOpenPrice": 81.45, "percentVsPreviousDayClose": 0.***************, "percentVsArrival": 0.***************, "percentVsOpen": -0.***************, "closePrice": 81.08}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO67MTCMTO6720230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:25Z", "orderStatusUpdated": "2023-02-27T07:58:25Z", "orderSubmitted": "2023-02-27T07:58:25Z", "tradingDateTime": "2023-02-27T07:58:25Z"}, "&parent": "MTCMTO_6_7:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "PARF", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "6", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD", "RIC": "LCON3"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_7", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_7", "internalOrderIdCode": "MTCMTO_6_7", "orderIdCode": "MTCMTO_6_7", "transactionRefNo": "MTCMTO67MTCMTO6720230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_7:1:MTCMTO67MTCMTO6720230227BUYI:PARF:2023-02-27T07:58:25Z:25.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.********, "CAD": *********.********, "CHF": ********.********, "CNY": *********.7913588, "CZK": **********.6179652, "DKK": *********.9806709, "EUR": ********.********, "GBP": ********.********, "HKD": *********.2905061, "HUF": ***********.58159, "IDR": *************.2532, "ILS": *********.8743605, "INR": **********.077887, "JPY": ***********.58613, "KRW": ************.26437, "MXN": **********.1577604, "MYR": *********.********, "NOK": *********.7387722, "NZD": *********.8610006, "PHP": **********.550882, "PLN": *********.519045, "RON": 372304606.38146687, "SEK": 836924833.1793635, "SGD": 107806240.5486072, "THB": 2803370897.8823204, "TRY": 1508379270.7504268, "USD": ********.0, "ZAR": **********.2683346, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 81.915, "remainingQuantity": 25.0, "tradedQuantity": 975.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 81.63, "nativeCurrency": "USD"}, "source": "PRIMARY", "timestamp": "2023-02-27T07:58:13.172Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 81.62, "nativeCurrency": "USD"}, "mid": {"native": 81.61, "nativeCurrency": "USD"}, "source": "PRIMARY", "bid": {"native": 81.6, "nativeCurrency": "USD"}, "timestamp": "2023-02-27T07:58:24.942Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "5b01ba6eff51dfc30c7b5cbc10e75b84ee656d2e87097ba13dbf7247478bc1c3", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_8:1:MTCMTO68MTCMTO6820230227BUYI:PARF:2023-02-27T07:59:30Z:20.0", "_score": 0.0, "_routing": "MTCMTO_6_8:1:NEWO", "_parent": "MTCMTO_6_8:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_8:1:MTCMTO68MTCMTO6820230227BUYI:PARF:2023-02-27T07:59:30Z:20.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 81.93, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 980.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:59:30Z", "ultimateVenue": "IFEU", "venue": "IFEU", "pricingDetails": {"nearestQuote": 81.65, "marketPrice": 81.65, "percentVsSlippage": -0.***************, "vwap": 81.64, "openPrice": 81.93, "percentVsBidAskSpread": 0.***************, "percentVsVwap": 0.**************, "arrivalPrice": 81.65, "bidAskSpread": 0.**************, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.**************, "percentVsNearestQuote": 0.***************, "lowPrice": 80.49, "highPrice": 82.22, "percentVsOhlcPrice": 0.006121449559256, "percentVsClose": 0.010428808048586, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": 0.**************, "askPrice": 81.65, "percentVsMidPointPrice": 0.**************, "ohlc": 81.43, "percentVsAskPrice": 0.***************, "percentVsMarket": 0.***************, "percentVsBidPrice": 0.***************, "previousDayClosePrice": 81.87, "slippagePrice": -0.***************, "bidPrice": 81.63, "midPointPrice": 81.64, "previousDayOpenPrice": 81.45, "percentVsPreviousDayClose": 0.***************, "percentVsArrival": 0.***************, "percentVsOpen": 0.0, "closePrice": 81.08}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO68MTCMTO6820230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:59:30Z", "orderStatusUpdated": "2023-02-27T07:59:30Z", "orderSubmitted": "2023-02-27T07:59:30Z", "tradingDateTime": "2023-02-27T07:59:30Z"}, "&parent": "MTCMTO_6_8:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "PARF", "orderType": "Market", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "7", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Extraction Resources", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Commodities futures", "commoditiesOrEmissionAllowanceDerivativeInd": true, "commodityAndEmissionAllowances": {"finalPriceType": "EXOF", "transactionType": "FUTR"}, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-05-31", "priceMultiplier": 1000.0}, "ext": {"aii": {"daily": "IFEUBFF2023-05-31 00:00:00", "mic": "IFEU"}, "alternativeInstrumentIdentifier": "IFEUBFF2023-05 00:00:00", "bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/GB00H1JWRJ54/USD", "RIC": "LCON3"}, "venueInEEA": true}, "instrumentClassification": "FCECSX", "instrumentClassificationEMIRAssetClass": "CO", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "B-Brent <PERSON>", "instrumentIdCode": "GB00H1JWRJ54", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300UF4R84F48NCH34", "notionalCurrency1": "USD", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingApprovalDate": "2016-11-01T00:00:00", "admissionToTradingOrFirstTradeDate": "2016-11-01T00:00:00", "admissionToTradingRequestDate": "2016-11-01T00:00:00", "financialInstrumentShortName": "ICE/FUT ******** IFEU:B", "issuerRequestForAdmissionToTrading": true, "terminationDate": "2023-05-31T23:59:59", "tradingVenue": "IFEU"}}}, "id": "MTCMTO_6_8", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "GB00H1JWRJ54", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_8", "internalOrderIdCode": "MTCMTO_6_8", "orderIdCode": "MTCMTO_6_8", "transactionRefNo": "MTCMTO68MTCMTO6820230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_8:1:MTCMTO68MTCMTO6820230227BUYI:PARF:2023-02-27T07:59:30Z:20.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.********, "BRL": *********.2660604, "CAD": *********.********, "CHF": ********.********, "CNY": *********.5923822, "CZK": **********.6105747, "DKK": *********.7987494, "EUR": ********.********, "GBP": ********.********, "HKD": *********.8482095, "HUF": ***********.52588, "IDR": *************.105, "ILS": *********.6918705, "INR": **********.634454, "JPY": ***********.161459, "KRW": ************.69362, "MXN": **********.6719732, "MYR": *********.8169416, "NOK": *********.3683914, "NZD": *********.********, "PHP": **********.735646, "PLN": *********.28652656, "RON": 374282385.5599774, "SEK": 841370796.1910179, "SGD": 108378935.41785109, "THB": 2818263135.304151, "TRY": 1516392174.872087, "USD": ********.********, "ZAR": **********.4997163, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.********, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"commodityDerivativeIndicator": false, "executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 81.93, "remainingQuantity": 20.0, "tradedQuantity": 980.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 81.65, "nativeCurrency": "USD"}, "source": "PRIMARY", "timestamp": "2023-02-27T07:59:24.417Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 81.65, "nativeCurrency": "USD"}, "mid": {"native": 81.64, "nativeCurrency": "USD"}, "source": "PRIMARY", "bid": {"native": 81.63, "nativeCurrency": "USD"}, "timestamp": "2023-02-27T07:59:29.412Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "9d2ed7dfb5b78a02346f7123b30171199d910666acbd3dffbc8d2635eb746258", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_1:1:MTCMTO61MTCMTO61120230227BUYI:PARF:2023-02-27T07:56:55Z:200.0", "_score": 0.0, "_routing": "MTCMTO_6_1:1:NEWO", "_parent": "MTCMTO_6_1:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_1:1:MTCMTO61MTCMTO61120230227BUYI:PARF:2023-02-27T07:56:55Z:200.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 4141.0, "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:56:55Z", "ultimateVenue": "XEUR", "venue": "XEUR", "pricingDetails": {"nearestQuote": 4154.0, "marketPrice": 4145.0, "percentVsSlippage": 0.***************, "vwap": 4149.57, "openPrice": 4150.0, "percentVsBidAskSpread": 0.***************, "percentVsVwap": -0.***************, "arrivalPrice": 4154.0, "bidAskSpread": 3.0, "percentVsHighPrice": -0.***************, "percentVsLowPrice": -0.***************, "percentVsNearestQuote": -0.***************, "lowPrice": 4145.0, "highPrice": 4212.0, "percentVsOhlcPrice": -0.008296759454097, "percentVsClose": -0.012955854126679, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": -0.***************, "askPrice": 4154.0, "percentVsMidPointPrice": -0.***************, "ohlc": 4175.5, "percentVsAskPrice": -0.***************, "percentVsMarket": -0.***************, "percentVsBidPrice": -0.***************, "previousDayClosePrice": 4141.0, "slippagePrice": 4.0, "bidPrice": 4151.0, "midPointPrice": 4152.5, "previousDayOpenPrice": 4227.0, "percentVsPreviousDayClose": 0.0, "percentVsArrival": -0.***************, "percentVsOpen": -0.***************, "closePrice": 4195.0}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO61MTCMTO61120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:56:55Z", "orderStatusUpdated": "2023-02-27T07:56:55Z", "orderSubmitted": "2023-02-27T07:56:55Z", "tradingDateTime": "2023-02-27T07:56:55Z"}, "&parent": "MTCMTO_6_1:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4142.0, "orderStatus": "PARF", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "0", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR", "RIC": "STXEM3"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_1", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_1", "internalOrderIdCode": "MTCMTO_6_1", "orderIdCode": "MTCMTO_6_1", "transactionRefNo": "MTCMTO61MTCMTO61120230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_1:1:MTCMTO61MTCMTO61120230227BUYI:PARF:2023-02-27T07:56:55Z:200.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "transactionVolume": {"ecbRefRate": {"AUD": ********.0, "BGN": ********.0, "BRL": *********.0, "CAD": ********.0, "CHF": ********.0, "CNY": *********.0, "CZK": *********.0, "DKK": *********.0, "EUR": ********.0, "GBP": ********.3, "HKD": *********.0, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.0, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.0, "MYR": *********.0, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.0, "THB": **********.0, "TRY": *********.0, "USD": ********.********, "ZAR": *********.0, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1200.0, "price": 4141.0, "remainingQuantity": 200.0, "tradedQuantity": 1000.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 4145.0, "nativeCurrency": "EUR"}, "source": "PRIMARY", "timestamp": "2023-02-27T03:56:07.603Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 4154.0, "nativeCurrency": "EUR"}, "mid": {"native": 4152.5, "nativeCurrency": "EUR"}, "source": "PRIMARY", "bid": {"native": 4151.0, "nativeCurrency": "EUR"}, "timestamp": "2023-02-27T07:56:25.888Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "70f70f2640351e33f0039a859c61dcdcfd889df9f7fc2f3b72025d33dc9ce6bd", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_2:1:MTCMTO62MTCMTO62120230227BUYI:PARF:2023-02-27T07:58:21Z:50.0", "_score": 0.0, "_routing": "MTCMTO_6_2:1:NEWO", "_parent": "MTCMTO_6_2:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_2:1:MTCMTO62MTCMTO62120230227BUYI:PARF:2023-02-27T07:58:21Z:50.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 4145.0, "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 950.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:21Z", "ultimateVenue": "XEUR", "venue": "XEUR", "pricingDetails": {"nearestQuote": 4154.0, "marketPrice": 4145.0, "percentVsSlippage": 0.0, "vwap": 4149.57, "openPrice": 4150.0, "percentVsBidAskSpread": 0.***************, "percentVsVwap": -0.***************, "arrivalPrice": 4154.0, "bidAskSpread": 3.0, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.0, "percentVsNearestQuote": -0.***************, "lowPrice": 4145.0, "highPrice": 4212.0, "percentVsOhlcPrice": -0.***************, "percentVsClose": -0.011990407673861, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": -0.***************, "askPrice": 4154.0, "percentVsMidPointPrice": -0.***************, "ohlc": 4175.5, "percentVsAskPrice": -0.***************, "percentVsMarket": 0.0, "percentVsBidPrice": -0.***************, "previousDayClosePrice": 4141.0, "slippagePrice": 0.0, "bidPrice": 4151.0, "midPointPrice": 4152.5, "previousDayOpenPrice": 4227.0, "percentVsPreviousDayClose": 0.***************, "percentVsArrival": -0.***************, "percentVsOpen": -0.***************, "closePrice": 4195.0}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO62MTCMTO62120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:21Z", "orderStatusUpdated": "2023-02-27T07:58:21Z", "orderSubmitted": "2023-02-27T07:58:21Z", "tradingDateTime": "2023-02-27T07:58:21Z"}, "&parent": "MTCMTO_6_2:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4145.0, "orderStatus": "PARF", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "1", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR", "RIC": "STXEM3"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_2", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_2", "internalOrderIdCode": "MTCMTO_6_2", "orderIdCode": "MTCMTO_6_2", "transactionRefNo": "MTCMTO62MTCMTO62120230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_2:1:MTCMTO62MTCMTO62120230227BUYI:PARF:2023-02-27T07:58:21Z:50.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "transactionVolume": {"ecbRefRate": {"AUD": ********.25, "BGN": ********.5, "BRL": *********.0, "CAD": ********.5, "CHF": ********.75, "CNY": *********.5, "CZK": *********.5, "DKK": *********.5, "EUR": ********.0, "GBP": ********.575, "HKD": *********.5, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.5, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.75, "MYR": *********.5, "NOK": *********.25, "NZD": ********.75, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.25, "SGD": ********.5, "THB": **********.5, "TRY": *********.0, "USD": ********.********, "ZAR": *********.5, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 4145.0, "remainingQuantity": 50.0, "tradedQuantity": 950.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 4145.0, "nativeCurrency": "EUR"}, "source": "PRIMARY", "timestamp": "2023-02-27T03:56:07.603Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 4154.0, "nativeCurrency": "EUR"}, "mid": {"native": 4152.5, "nativeCurrency": "EUR"}, "source": "PRIMARY", "bid": {"native": 4151.0, "nativeCurrency": "EUR"}, "timestamp": "2023-02-27T07:58:10.282Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "4f6ee91448fac39794f0899eb3efa44a99a3e793ac851de590dec3330b0d3d8f", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_3:1:MTCMTO63MTCMTO63120230227BUYI:PARF:2023-02-27T07:58:25Z:300.0", "_score": 0.0, "_routing": "MTCMTO_6_3:1:NEWO", "_parent": "MTCMTO_6_3:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_3:1:MTCMTO63MTCMTO63120230227BUYI:PARF:2023-02-27T07:58:25Z:300.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 4148.0, "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:58:25Z", "ultimateVenue": "XEUR", "venue": "XEUR", "pricingDetails": {"nearestQuote": 4154.0, "marketPrice": 4145.0, "percentVsSlippage": -0.***************, "vwap": 4149.57, "openPrice": 4150.0, "percentVsBidAskSpread": 0.***************, "percentVsVwap": -0.**************, "arrivalPrice": 4154.0, "bidAskSpread": 3.0, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.***************, "percentVsNearestQuote": -0.**************, "lowPrice": 4145.0, "highPrice": 4212.0, "percentVsOhlcPrice": -0.006607797200697, "percentVsClose": -0.011266930360781, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": -0.018865671641791, "askPrice": 4154.0, "percentVsMidPointPrice": -0.***************, "ohlc": 4175.5, "percentVsAskPrice": -0.**************, "percentVsMarket": 0.***************, "percentVsBidPrice": -0.***************, "previousDayClosePrice": 4141.0, "slippagePrice": -3.0, "bidPrice": 4151.0, "midPointPrice": 4152.5, "previousDayOpenPrice": 4227.0, "percentVsPreviousDayClose": 0.**************, "percentVsArrival": -0.**************, "percentVsOpen": -0.***************, "closePrice": 4195.0}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO63MTCMTO63120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:58:25Z", "orderStatusUpdated": "2023-02-27T07:58:25Z", "orderSubmitted": "2023-02-27T07:58:25Z", "tradingDateTime": "2023-02-27T07:58:25Z"}, "&parent": "MTCMTO_6_3:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4148.0, "orderStatus": "PARF", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "2", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR", "RIC": "STXEM3"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_3", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_3", "internalOrderIdCode": "MTCMTO_6_3", "orderIdCode": "MTCMTO_6_3", "transactionRefNo": "MTCMTO63MTCMTO63120230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_3:1:MTCMTO63MTCMTO63120230227BUYI:PARF:2023-02-27T07:58:25Z:300.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "transactionVolume": {"ecbRefRate": {"AUD": ********.0, "BGN": ********.0, "BRL": *********.0, "CAD": ********.0, "CHF": ********.0, "CNY": *********.0, "CZK": *********.0, "DKK": *********.0, "EUR": ********.0, "GBP": ********.4, "HKD": *********.0, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.0, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.0, "MYR": *********.0, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.0, "THB": **********.0, "TRY": *********.0, "USD": ********.********, "ZAR": *********.0, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1300.0, "price": 4148.0, "remainingQuantity": 300.0, "tradedQuantity": 1000.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 4145.0, "nativeCurrency": "EUR"}, "source": "PRIMARY", "timestamp": "2023-02-27T03:56:07.603Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 4154.0, "nativeCurrency": "EUR"}, "mid": {"native": 4152.5, "nativeCurrency": "EUR"}, "source": "PRIMARY", "bid": {"native": 4151.0, "nativeCurrency": "EUR"}, "timestamp": "2023-02-27T07:58:10.282Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "793837e4913fd3ebe2ff1002f486e95618dec47b53be4a1fe63be963f6a0293f", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "MTCMTO_6_4:1:MTCMTO64MTCMTO64120230227BUYI:FILL:2023-02-27T07:59:30Z:0.0", "_score": 0.0, "_routing": "MTCMTO_6_4:1:NEWO", "_parent": "MTCMTO_6_4:1:NEWO", "_source": {"date": "2023-02-27", "seller": [{"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}], "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmtov2.6.csv", "&id": "MTCMTO_6_4:1:MTCMTO64MTCMTO64120230227BUYI:FILL:2023-02-27T07:59:30Z:0.0", "transactionDetails": {"buySellIndicator": "BUYI", "price": 4150.0, "priceCurrency": "EUR", "priceNotation": "MONE", "quantity": 1000.0, "quantityCurrency": "EUR", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-02-27T07:59:30Z", "ultimateVenue": "XEUR", "venue": "XEUR", "pricingDetails": {"nearestQuote": 4154.0, "marketPrice": 4145.0, "percentVsSlippage": -0.***************, "vwap": 4149.57, "openPrice": 4150.0, "percentVsBidAskSpread": 0.***************, "percentVsVwap": 0.***************, "arrivalPrice": 4154.0, "bidAskSpread": 3.0, "percentVsHighPrice": -0.***************, "percentVsLowPrice": 0.***************, "percentVsNearestQuote": -0.***************, "lowPrice": 4145.0, "highPrice": 4212.0, "percentVsOhlcPrice": -0.00612575821272, "percentVsClose": -0.010784901138406, "percentVsBestBid": 0.0, "percentVsPreviousDayOpen": -0.***************, "askPrice": 4154.0, "percentVsMidPointPrice": -0.***************, "ohlc": 4175.5, "percentVsAskPrice": -0.***************, "percentVsMarket": 0.***************, "percentVsBidPrice": -0.***************, "previousDayClosePrice": 4141.0, "slippagePrice": -5.0, "bidPrice": 4151.0, "midPointPrice": 4152.5, "previousDayOpenPrice": 4227.0, "percentVsPreviousDayClose": 0.***************, "percentVsArrival": -0.***************, "percentVsOpen": 0.0, "closePrice": 4195.0}}, "buyerFileIdentifier": "account:client3", "reportDetails": {"executingEntity": {"fileIdentifier": "lei:549300o1wyozrbh0rw26"}, "transactionRefNo": "MTCMTO64MTCMTO64120230227BUYI"}, "timestamps": {"orderReceived": "2023-02-27T07:59:30Z", "orderStatusUpdated": "2023-02-27T07:59:30Z", "orderSubmitted": "2023-02-27T07:59:30Z", "tradingDateTime": "2023-02-27T07:59:30Z"}, "&parent": "MTCMTO_6_4:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "limitPrice": 4151.0, "orderStatus": "FILL", "orderType": "Limit", "outgoingOrderAddlInfo": "Marking the Open - Futures", "tradingCapacity": "DEAL", "validityPeriod": ["GTCV"]}, "flags": {"auditId": "f2a4d7b8-a0f6-420b-9489-4414edbc3b94", "TCAFlagStatus": ["Hit"], "TCAClosePriceStatus": ["Hit"]}, "sourceIndex": "3", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "FcaFirdsInstrument:*******************:*************", "cfiAttribute1": "Indices", "cfiAttribute2": "Cash", "cfiAttribute3": "Standardized", "cfiAttribute4": "Not Applicable/Undefined", "cfiCategory": "Futures", "cfiGroup": "Financial futures", "commoditiesOrEmissionAllowanceDerivativeInd": false, "derivative": {"deliveryType": "CASH", "expiryDate": "2023-06-16", "priceMultiplier": 10.0, "underlyingIndexName": "EURO STOXX 50", "underlyingInstruments": [{"underlyingInstrumentCode": "EU0009658145"}]}, "ext": {"aii": {"daily": "XEURFESXFF2023-06-16 00:00:00", "mic": "XEUR"}, "alternativeInstrumentIdentifier": "XEURFESXFF2023-06 00:00:00", "bestExAssetClassMain": "Equity Derivatives", "bestExAssetClassSub": "Futures and options admitted to trading on a trading venue", "emirEligible": true, "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": true, "onFIRDS": true, "pricingReferences": {"ICE": "isin/DE000C6EV128/EUR", "RIC": "STXEM3"}, "venueInEEA": true}, "instrumentClassification": "FFICSX", "instrumentClassificationEMIRAssetClass": "EQ", "instrumentClassificationEMIRContractType": "FU", "instrumentClassificationEMIRProductType": "FUT", "instrumentFullName": "FESX SI ******** CS", "instrumentIdCode": "DE000C6EV128", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "529900UT4DG0LG5R9O07", "notionalCurrency1": "EUR", "sourceKey": "FULINS_F_20230429_01of01.xml", "venue": {"admissionToTradingOrFirstTradeDate": "2021-06-21T00:50:00", "financialInstrumentShortName": "Eurex/F ******** SX5E", "issuerRequestForAdmissionToTrading": false, "terminationDate": "2023-06-16T23:59:59", "tradingVenue": "XEUR"}}}, "id": "MTCMTO_6_4", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:549300o1wyozrbh0rw26", "buySell": "1", "clientFileIdentifier": "account:client3", "clientIdentifiers": {"client": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}]}, "marketIdentifiers": [{"labelId": "DE000C6EV128", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06-16 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "XEUREU0009658145FF2023-06 00:00:00", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:client3", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:549300o1wyozrbh0rw26", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:counterparty3", "path": "seller", "type": "ARRAY"}, {"labelId": "account:counterparty3", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:account:6486262", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:trader3", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:client3", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:trader3", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "MTCMTO_6_4", "internalOrderIdCode": "MTCMTO_6_4", "orderIdCode": "MTCMTO_6_4", "transactionRefNo": "MTCMTO64MTCMTO64120230227BUYI"}, "sellerFileIdentifier": "account:counterparty3", "trader": [{"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}], "&key": "OrderState:MTCMTO_6_4:1:MTCMTO64MTCMTO64120230227BUYI:FILL:2023-02-27T07:59:30Z:0.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "transactionVolume": {"ecbRefRate": {"AUD": ********.0, "BGN": ********.0, "BRL": *********.0, "CAD": ********.0, "CHF": ********.0, "CNY": *********.0, "CZK": *********.0, "DKK": *********.0, "EUR": ********.0, "GBP": ********.0, "HKD": *********.0, "HUF": ***********.0, "IDR": ************.0, "ILS": *********.0, "INR": **********.0, "JPY": **********.0, "KRW": ***********.0, "MXN": *********.0, "MYR": *********.0, "NOK": *********.0, "NZD": ********.0, "PHP": **********.0, "PLN": *********.0, "RON": *********.********, "SEK": *********.0, "SGD": ********.0, "THB": **********.0, "TRY": *********.0, "USD": ********.********, "ZAR": *********.0, "refRateDate": "2023-02-27T13:15:00Z"}, "native": ********.0, "nativeCurrency": "EUR"}}, "dataSourceName": "SteelEyeTradeBlotter", "buyer": [{"&id": "0d49ffde-ba23-5bfb-900f-53a9e49d341a", "&key": "MarketPerson:0d49ffde-ba23-5bfb-900f-53a9e49d341a:*************", "name": "Client 3", "personalDetails": {"firstName": "Client", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "client3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "isDecisionMaker": false, "isPersonalTradingAccount": false, "type": "Client"}, "uniqueIds": ["account:client3"]}], "traderFileIdentifier": "account:trader3", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "d55e4a3d-863f-5d71-8155-69ddc6c6ea64", "&key": "AccountPerson:d55e4a3d-863f-5d71-8155-69ddc6c6ea64:*************", "name": "Trader 3", "personalDetails": {"firstName": "Trader", "lastName": "3"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "trader3", "label": "account"}]}, "structure": {"client": {"electiveProfessional": false, "isAggregatedClientAccount": false, "metFaceToFace": false}, "desks": [{"id": "desk1", "name": "desk1"}], "isDecisionMaker": false, "isPersonalTradingAccount": false}, "uniqueIds": ["account:trader3"]}, "executionWithinFirmFileIdentifier": "account:trader3", "investmentDecisionWithinFirm": {"&id": "8b4696a9-ac44-4348-b7ad-92d8b83ea526", "&key": "AccountPerson:8b4696a9-ac44-4348-b7ad-92d8b83ea526:*************", "name": "account 6486262", "personalDetails": {"firstName": "account", "lastName": "6486262"}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "account:6486262", "label": "account"}]}, "structure": {"isDecisionMaker": true}, "uniqueIds": ["account:account:6486262"]}, "investmentDecisionWithinFirmFileIdentifier": "account:account:6486262"}, "priceFormingData": {"initialQuantity": 1000.0, "price": 4150.0, "remainingQuantity": 0.0, "tradedQuantity": 1000.0, "externalPricing": {"trades": {"PRIMARY": {"pre": {"quantity": 1.0, "price": {"native": 4145.0, "nativeCurrency": "EUR"}, "source": "PRIMARY", "timestamp": "2023-02-27T03:56:07.603Z"}}}, "quotes": {"PRIMARY": {"pre": {"ask": {"native": 4154.0, "nativeCurrency": "EUR"}, "mid": {"native": 4152.5, "nativeCurrency": "EUR"}, "source": "PRIMARY", "bid": {"native": 4151.0, "nativeCurrency": "EUR"}, "timestamp": "2023-02-27T07:59:27.417Z"}}}}}, "counterparty": {"&id": "0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb", "&key": "MarketCounterparty:0a65a0c0-1b3b-5da4-b4bf-cb769fc0cbcb:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": false, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"deaAccess": false, "isIsda": false, "kycApproved": false}, "name": "Counterparty 3", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "Counterparty3", "label": "account"}]}, "uniqueIds": ["account:counterparty3"]}, "&hash": "9ad319b65a6f4fff1b207c9a09ac840ab7fe2314dba5bf16a694caaba20fea5b", "counterpartyFileIdentifier": "account:counterparty3", "&user": "reference-refinitiv-tca-metrics"}}]}}