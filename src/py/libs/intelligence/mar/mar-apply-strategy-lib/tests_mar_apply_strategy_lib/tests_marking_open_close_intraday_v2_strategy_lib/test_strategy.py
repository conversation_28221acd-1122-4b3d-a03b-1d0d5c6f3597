# ruff: noqa: E402
import json
import os
import pandas as pd
import pathlib
import pytest
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField
from market_abuse_algorithms.strategy.base.models import TimeUnit
from marking_open_close_intraday_v2_apply_strategy.apply_strategy import ApplyStrategy
from marking_open_close_intraday_v2_apply_strategy.models import (
    TimeWindow,
)
from marking_open_close_intraday_v2_apply_strategy.static import (
    AlgoColumnsEnum,
    BehaviourType,
    Directionality,
    EvaluationType,
    PlacementPhase,
    RunType,
)
from marking_open_close_intraday_v2_apply_strategy.utils import (
    filter_execs,
)
from surveillance_utils.test_mock_helpers import load_ndjson_into_list
from typing import List, Optional, Union

executions_thresholds = {
    "behaviourType": BehaviourType.MARKING_THE_CLOSE,
    "runType": RunType.EXECUTIONS,
    "evaluationType": EvaluationType.TRADER,
    "directionality": Directionality.ONLY_BUYS_OR_ONLY_SELLS,
    "priceImprovement": 0,
    "marketComparison": 0,
    "timeWindow": TimeWindow(TimeUnit.SECONDS, 150),
}


@pytest.fixture
def executions_strategy_context(helpers):
    context = helpers.get_context(thresholds=executions_thresholds, filters={})
    return context


@pytest.fixture
def tc_3_3_strategy_context(helpers):
    thresholds = {
        "runType": RunType.ORDERS,
        "behaviourType": BehaviourType.MARKING_THE_FIXING,
        "directionality": Directionality.ONLY_BUYS_OR_ONLY_SELLS,
        "evaluationType": EvaluationType.TRADER,
        "marketComparison": 0.05,
        "timeWindow": TimeWindow(TimeUnit.SECONDS, 600),
        "priceImprovement": 0.001,
        "limitPriceDifference": 0.005,
        "dayComparison": 0.1,
        "tenDayComparison": 0.95,
    }
    context = helpers.get_context(thresholds=thresholds, filters={})
    return context


TEST_DATA = pathlib.Path(__file__).parent.joinpath("test_data")


@pytest.fixture(autouse=True)
def reset_mocks(monkeypatch):
    # Reset or reapply mocks before each test
    monkeypatch.undo()


class FakeMarketClient(MarketDataClient):
    def get_ric_map(
        self,
        list_inst_unique_id: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
        use_prefix_query: Optional[bool] = False,
    ) -> Union[dict, None]:
        if instrument_combinations == [["FR0000120628EURXPAR"]]:
            return {"FR0000120628EURXPAR": "AXAF.PA"}

        elif instrument_combinations == [["AT0000606306EURWBAH"]]:
            return {"AT0000606306EURWBAH": "RBIV.VI"}

    def get_tick_data(self, *args, **kwargs):
        if (
            "instrument_ric" in kwargs
            and kwargs["instrument_ric"] == "AXAF.PA"
            and "result_between_dates" not in kwargs
        ):
            return pd.read_csv(TEST_DATA.joinpath("tc_1_1_step_9_tick_data.csv"))

        elif (
            "instrument_ric" in kwargs
            and kwargs["instrument_ric"] == "AXAF.PA"
            and kwargs["result_between_dates"]
        ):
            return pd.read_csv(TEST_DATA.joinpath("tc_1_1_step_15_tick_data.csv"))

        elif kwargs.get("instrument_ric") == "SHEL.L" and kwargs["result_between_dates"]:
            return pd.read_csv(TEST_DATA.joinpath("ticks_executions_auction.csv"))

        elif (
            "instrument_unique_identifier" in kwargs
            and kwargs["instrument_unique_identifier"] == "AT0000606306EURWBAH"
            and "result_between_dates" not in kwargs
        ):
            return pd.read_csv(
                TEST_DATA.joinpath("tc_3_3_limit_price_difference_auction_ticks.csv")
            )

        elif (
            "instrument_unique_identifier" in kwargs
            and kwargs["instrument_unique_identifier"] == "AT0000606306EURWBAH"
            and kwargs["result_between_dates"]
        ):
            return pd.read_csv(TEST_DATA.joinpath("tc_3_3_step14_15_auction_ticks.csv"))


COPP_CLARK_FILE_NAME = str(
    pathlib.Path(__file__).parent.resolve() / "test_data" / "coppClark_new_v2.txt"
)


class TestStrategy:
    def test_step_2_grouping_executions(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        step_2_generator = strategy._step_2_grouping(data=executions_data)

        data = next(step_2_generator)

        assert not data.empty
        assert data.shape[0] == 7
        assert data[OrderField.META_ID].nunique() == 7

    def test_matching_trading_sessions(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        step_2_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        step_2_matched = strategy.match_trading_session(records=step_2_data, is_orders=False)

        assert not step_2_matched.empty
        assert step_2_matched.shape[0] == 7
        assert step_2_matched.shape[1] == 30
        assert step_2_matched[AlgoColumnsEnum.TRADING_SESSION_MATCH].nunique() == 3
        assert step_2_matched[AlgoColumnsEnum.RANGE_WINDOW_START].nunique() == 3

    def test_matching_trading_sessions_missing_exchange_simbol(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        row = pd.read_csv(TEST_DATA.joinpath("trading_session.csv"), index_col=0)

        row = row.squeeze()

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        timestamp_column = OrderField.TS_ORD_SUBMITTED

        output = strategy.record_map_to_trading_session(row=row, timestamp_column=timestamp_column)

        assert output["coop_clark_match"] is None
        assert output["range_window_start"] is None
        assert output["range_window_end"] is None

    def test_steps_5_6_7_placement_phase(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_step_2_with_mapped_trd_sessions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        steps_5_6_generator = strategy._steps_5_6_7_placement_phase(
            records=executions_data, is_orders=False
        )

        output_data = next(steps_5_6_generator)

        assert not output_data.empty
        assert output_data.shape[0] == 2

        output_data = next(steps_5_6_generator)
        assert output_data.shape[0] == 4

    def test_step_8_executions(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_step_7.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        step_8_generator = strategy._step_8(
            records=executions_data,
            executions_df=filter_execs(records_df=executions_data, executions_df=executions_data),
            is_orders=False,
        )

        output_data = next(step_8_generator)

        assert not output_data.empty
        assert output_data.shape[0] == 2
        assert output_data[AlgoColumnsEnum.SUM_QUANTITY_BY_TS_DATE][0] == 851908.0
        assert output_data[AlgoColumnsEnum.SUM_ORDER_STATE_VALUE_BY_TS_DATE][0] == 28136023.9487136

    def test_step_9_10_11_12(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "get_market_client",
            FakeMarketClient,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_step8_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            executions_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        executions_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.CLOSE

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_9_10_11_12(records=executions_data, is_orders=False)

        assert output.shape[0] == 2
        assert output.shape[1] == 43
        assert output[AlgoColumnsEnum.WINDOW_START_PRICE][0] == 30.75000
        assert output[AlgoColumnsEnum.WINDOW_END_PRICE][0] == 30.75500
        assert round(output[AlgoColumnsEnum.PRICE_CHANGE_PERCENTAGE][0], 5) == 0.00016

    def test_step_9_10_11_12_incorrect_direction(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "get_market_client",
            FakeMarketClient,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_step8_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            executions_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        executions_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.CLOSE

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        # Force incorrect direction to check direction and price improvement
        executions_data[AlgoColumnsEnum.SCENARIO_DIRECTIONALITY] = BuySell.SELL

        output = strategy._step_9_10_11_12(records=executions_data, is_orders=False)

        assert output is None

    def test_step_15_16_17(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "get_market_client",
            FakeMarketClient,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_step_15_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            executions_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        executions_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.CLOSE

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_15_16_17(executions_df=executions_data)

        assert not output.empty
        assert output.shape[0] == 2
        assert output.shape[1] == 45
        assert round(output[AlgoColumnsEnum.MARKET_VOLUME_COMPARISON][0], 5) == 75.23695
        assert output[AlgoColumnsEnum.SCENARIO_VOLUME][0] == 851908.0

    def test_step_15_16_17_executions_auctions(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "get_market_client",
            FakeMarketClient,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("executions_auction.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            executions_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        executions_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.OPENING_AUCTION

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_15_16_17(executions_df=executions_data)

        assert not output.empty
        assert output.shape[0] == 1
        assert output.shape[1] == 45
        assert round(output[AlgoColumnsEnum.MARKET_VOLUME_COMPARISON][0], 5) == 0.00111
        assert output[AlgoColumnsEnum.SCENARIO_VOLUME][0] == 12632.0

    def test_create_alert(
        self, monkeypatch, executions_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_1_1_final_result_executions.csv"),
            parse_dates=[
                OrderField.TS_TRADING_DATE_TIME,
                OrderField.TS_ORD_SUBMITTED,
                AlgoColumnsEnum.WINDOW_START_TIME,
                AlgoColumnsEnum.WINDOW_END_TIME,
            ],
        )

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_END] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.RANGE_WINDOW_START] = executions_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        executions_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            executions_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        executions_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.CLOSE

        mock_apply_strategy_kwargs["context"] = executions_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy._create_alert(alert_data=executions_data, orders=None, executions=executions_data)
        strategy._write_scenarios_from_apply_strategy_result()
        scenarios = load_ndjson_into_list(strategy.scenario_local_file_path)

        assert len(scenarios) == 1
        scenario = json.loads(scenarios[0]["raw"])

        assert scenario["records"]["orders"] is None
        assert len(scenario["records"]["executions"]) == 2

        additionalFields = scenario["additionalFields"]["topLevel"]

        assert additionalFields["vPlacementPhase"] == "Close"
        assert additionalFields["evaluationID"] == "account:trader1"

    def test_step_8_orders(self, monkeypatch, mock_apply_strategy_kwargs, tc_3_3_strategy_context):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_step8_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_step8_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_END] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_START] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        step_8_generator = strategy._step_8(
            records=orders_data,
            executions_df=filter_execs(records_df=orders_data, executions_df=executions_data),
            is_orders=True,
        )

        output_data = next(step_8_generator)

        assert not output_data.empty
        assert output_data.shape[0] == 6
        assert output_data[AlgoColumnsEnum.SUM_QUANTITY_BY_TS_DATE][0] == 3106.0
        assert (
            round(output_data[AlgoColumnsEnum.SUM_ORDER_STATE_VALUE_BY_TS_DATE][0], 3) == 58700.814
        )

    def test_step_13_14_16_17_orders(
        self, monkeypatch, mock_apply_strategy_kwargs, tc_3_3_strategy_context
    ):
        monkeypatch.setattr(
            "market_abuse_algorithms.data_source.repository.market_data.client.get_market_client",
            FakeMarketClient,
        )

        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "get_market_client",
            FakeMarketClient,
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_step13_14_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_END] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_START] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            orders_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        orders_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_13_14_16_17(order_data=orders_data)

        assert not output.empty
        assert output.shape[0] == 6
        assert output.shape[1] == 45
        assert round(output[AlgoColumnsEnum.MARKET_VOLUME_COMPARISON][0], 5) == 0.0905
        assert output[AlgoColumnsEnum.SCENARIO_VOLUME][0] == 3106.0
        assert round(output[AlgoColumnsEnum.LIMIT_PRICE_DIFFERENCE][0], 5) == 0.5814

    def test_step_18_orders(self, monkeypatch, mock_apply_strategy_kwargs, tc_3_3_strategy_context):
        import market_abuse_algorithms.data_source.repository.base
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        def fake_search(*args, **kwargs):
            return {
                "took": 2,
                "timed_out": False,
                "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
                "hits": {
                    "total": {"value": 27, "relation": "eq"},
                    "max_score": None,
                    "hits": [],
                },
                "aggregations": {"totalVolume": {"value": 28068.0}},
            }

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.base.Repository,
            "search_query",
            fake_search,
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_step_18_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_END] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_START] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            orders_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        orders_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_18(records=orders_data, is_orders=True)

        assert not output.empty
        assert output.shape[0] == 6
        assert output.shape[1] == 46
        assert round(output[AlgoColumnsEnum.DAY_COMPARISON][0], 5) == 0.11066

    def test_step_19(self, monkeypatch, mock_apply_strategy_kwargs, tc_3_3_strategy_context):
        import market_abuse_algorithms.data_source.repository.base
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        def fake_search_after(*args, **kwargs):
            executions_data = pd.read_csv(
                TEST_DATA.joinpath("tc_3_3_step_19_historical_executions.csv"),
                parse_dates=[
                    OrderField.TS_TRADING_DATE_TIME,
                    OrderField.TS_ORD_SUBMITTED,
                ],
            )

            orders_data = pd.read_csv(
                TEST_DATA.joinpath("tc_3_3_step_19_historical_orders.csv"),
                parse_dates=[OrderField.TS_ORD_SUBMITTED],
            )

            return_values = [orders_data, executions_data]

            def return_value(*args, **kwargs):
                return return_values.pop(0)

            return return_value

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.base.Repository,
            "search_after_query",
            fake_search_after(),
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_step_19_order.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_END] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_END
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.RANGE_WINDOW_START] = orders_data[
            AlgoColumnsEnum.RANGE_WINDOW_START
        ].apply(lambda x: pd.to_timedelta(x))

        orders_data[AlgoColumnsEnum.TS_TRADING_DATE] = pd.to_datetime(
            orders_data[AlgoColumnsEnum.TS_TRADING_DATE]
        ).dt.date

        orders_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        output = strategy._step_19(records=orders_data, is_orders=True)

        assert not output.empty
        assert output.shape[0] == 6
        assert output.shape[1] == 47
        assert round(output[AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON][0][0], 5) == -196.9553
        assert round(output[AlgoColumnsEnum.PREVIOUS_10_DAY_COMPARISON][0][1], 5) == 131.9553

    def test_create_alert_orders(
        self, monkeypatch, mock_apply_strategy_kwargs, tc_3_3_strategy_context
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        result_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_result.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                AlgoColumnsEnum.WINDOW_START_TIME,
                AlgoColumnsEnum.WINDOW_END_TIME,
            ],
        )

        result_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        strategy._create_alert(
            alert_data=result_data, orders=orders_data, executions=executions_data
        )

        strategy._write_scenarios_from_apply_strategy_result()
        scenarios = load_ndjson_into_list(strategy.scenario_local_file_path)

        assert len(scenarios) == 1
        scenario = json.loads(scenarios[0]["raw"])
        assert scenario["records"]["executions"] is None
        assert len(scenario["records"]["orders"]) == 6

        additionalFields = scenario["additionalFields"]["topLevel"]

        assert additionalFields["vPlacementPhase"] == "Intra-Day Auction"
        assert additionalFields["evaluationID"] == "account:trader3"

    def test_create_alert_orders_missing_market_volume_comparison(
        self, monkeypatch, tc_3_3_strategy_context, mock_apply_strategy_kwargs
    ):
        import marking_open_close_intraday_v2_apply_strategy.query

        monkeypatch.setattr(
            marking_open_close_intraday_v2_apply_strategy.query,
            "read_master_data_file",
            lambda *args, **kwargs: COPP_CLARK_FILE_NAME,
        )

        executions_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_executions.csv"),
            parse_dates=[OrderField.TS_TRADING_DATE_TIME, OrderField.TS_ORD_SUBMITTED],
        )

        orders_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )

        orders_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        result_data = pd.read_csv(
            TEST_DATA.joinpath("tc_3_3_final_result.csv"),
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                AlgoColumnsEnum.WINDOW_START_TIME,
                AlgoColumnsEnum.WINDOW_END_TIME,
            ],
        )

        result_data[AlgoColumnsEnum.PLACEMENT_PHASE] = PlacementPhase.INTRA_DAY_AUCTION

        mock_apply_strategy_kwargs["context"] = tc_3_3_strategy_context
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        result_data = result_data.drop(columns=[AlgoColumnsEnum.MARKET_VOLUME_COMPARISON])

        strategy._create_alert(
            alert_data=result_data, orders=orders_data, executions=executions_data
        )

        assert not os.path.exists(strategy.result_local_file_path), (
            "Expecting 0 scenarios, file should NOT exist"
        )
