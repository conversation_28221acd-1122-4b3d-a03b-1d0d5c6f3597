# TODO: Rewrite this post H1 and saas alignment
# import pytest
# from market_abuse_algorithms.strategy.base.models import TimeUnit
# from marking_open_close_intraday_v2_apply_strategy.models import (
#     TimeWindow,
# )
# from marking_open_close_intraday_v2_apply_strategy.static import (
#     BehaviourType,
#     Directionality,
#     EvaluationType,
#     RunType,
# )
# from market_abuse_algorithms.strategy.marking_open_close_intraday_v2.strategy import (
#     Strategy,
# )
#
# #
# # os.environ.setdefault(
# #     "COPP_CLARK_FILE_PATH",
# #     str(pathlib.Path(__file__).parent.resolve() / "test_data" / "coppClark_new.txt"),
# # )
#
#
# @pytest.mark.usefixtures("skip_test_in_ci")
# class TestDebugMarkingOpenCloseIntradayV2:
#     def test_debug(self, helpers):
#         thresholds = {
#             "behaviourType": BehaviourType.MARKING_THE_CLOSE,
#             "runType": RunType.EXECUTIONS,
#             "evaluationType": EvaluationType.TRADER,
#             "directionality": Directionality.ONLY_BUYS_OR_ONLY_SELLS,
#             "priceImprovement": 0,
#             "marketComparison": 0,
#             "timeWindow": TimeWindow(TimeUnit.SECONDS, 150),
#         }
#
#         thresholds = {
#             "runType": RunType.ORDERS,
#             "behaviourType": BehaviourType.MARKING_THE_FIXING,
#             "directionality": Directionality.ONLY_BUYS_OR_ONLY_SELLS,
#             "evaluationType": EvaluationType.TRADER,
#             "marketComparison": 0.05,
#             "timeWindow": TimeWindow(TimeUnit.SECONDS, 600),
#             "priceImprovement": 0.001,
#             "limitPriceDifference": 0.005,
#             "dayComparison": 0.1,
#             "tenDayComparison": 0.95,
#         }
#
#         filters = {
#             "bool": {
#                 "must": {
#                     "terms": {
#                         "sourceKey": [
#                             "s3://mares8.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtcmto.3.2.csv"  # noqa: E501
#                         ]
#                     }
#                 }
#             }
#         }
#
#         context = helpers.get_context(thresholds=thresholds, filters=filters)
#
#         strategy = Strategy(context=context)
#
#         strategy.run()
#
#         scenarios = strategy.scenarios
#
#         scenarios
