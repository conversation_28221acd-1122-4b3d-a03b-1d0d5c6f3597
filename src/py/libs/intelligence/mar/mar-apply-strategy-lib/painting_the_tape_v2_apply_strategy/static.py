# ruff: noqa: E501
# mypy: disable-error-code="misc"
from dataclasses import dataclass, field
from se_elastic_schema.elastic_schema.core.base import BaseStrEnum

# Number of days to compute ADTV for
LBP_ADTV = 20


class AlgoColumnsEnum(BaseStrEnum):
    AVG_EXEC_PRICE_BY_ORDER = "vPriceAverageByOrder"
    COMPARISON_PERCENTAGE = "vComparisonPercentage"
    COMPARISON_VOLUME = "vComparisonVolume"
    COUNT_OF_BUYS_IN_TIME_WINDOW_GROUPING = "vCountOfBuysInTimeWindowGrouping"
    COUNT_OF_COUNTERPARTS_IN_TIME_WINDOW_GROUPING = "vCountOfCounterpartsInTimeWindowGrouping"
    COUNT_OF_ORDERS = "vCountOfOrders"
    COUNT_OF_SELLS__IN_TIME_WINDOW_GROUPING = "vCountOfSellsInTimeWindowGrouping"
    INST_FULL_NAME = "instrumentName"
    INST_ID_CODE = "instrumentIdCode"
    ISIN = "isin"
    MARKET_AVG_DAILY_VOLUME = "marketAverageDailyVolume"
    MAXIMUM_TRADE_TIMESTAMP_IN_TIME_WINDOW_GROUPING = "vMaximumTradeTimestampInTimeWindowGrouping"
    MAX_EXEC_TIMESTAMP = "vExecutionTimestampMax"
    MINIMUM_ORDER_TIMESTAMP_IN_TIME_WINDOW_GROUPING = "vMinimumOrderTimestampInTimeWindowGrouping"
    ORDERS = "orders"
    ORDERS_STATE_KEYS = "orderStateKeys"
    ORDER_TIME_MIN = "vOrderTimeMin"
    PRICE_AVERAGE_BY_ORDER_IN_TIME_WINDOW = "vPriceAverageByOrderInTimeWindow"
    PRICE_IMPROVEMENT = "vPriceImprovement"
    PRICE_IMPROVEMENT_PERCENTAGE = "vPriceImprovementPercentage"
    QUANTITY_SUM_TIME_WINDOW_GROUPING = "vQuantitySumTimeWindowGrouping"
    RIC = "ric"
    SUM_EXEC_QUANTITY_BY_ORDER = "vQuantitySumByOrder"
    NEWO_META_KEY = "vNewoMetaKey"
    NEWO_INITIAL_QUANTITY = "vNewoInitialQuantity"
    TRIGGER_ORDER = "vTriggerOrder"
    UP_TICK_PERCENTAGE = "vUpTickPercentage"

    @classmethod
    def get_scenario_columns(cls):
        return [
            cls.INST_FULL_NAME,
            cls.INST_ID_CODE,
            cls.ISIN,
            cls.COUNT_OF_BUYS_IN_TIME_WINDOW_GROUPING,
            cls.COUNT_OF_SELLS__IN_TIME_WINDOW_GROUPING,
            cls.MINIMUM_ORDER_TIMESTAMP_IN_TIME_WINDOW_GROUPING,
            cls.QUANTITY_SUM_TIME_WINDOW_GROUPING,
            cls.PRICE_IMPROVEMENT,
            cls.PRICE_IMPROVEMENT_PERCENTAGE,
            cls.RIC,
            cls.COMPARISON_VOLUME,
            cls.COMPARISON_PERCENTAGE,
            cls.ORDERS,
            cls.ORDERS_STATE_KEYS,
            cls.TRIGGER_ORDER,
        ]


@dataclass
class MarAuditDroppedOrders:
    time_window_filtered_orders: list = field(default_factory=lambda: list())
    no_orders_in_time_window_filtered_orders: list = field(default_factory=lambda: list())
    unique_orders_count_less_filtered_orders: list = field(default_factory=lambda: list())
    counterparty_count_less_filtered_orders: list = field(default_factory=lambda: list())
    price_improvement_filtered_orders: list = field(default_factory=lambda: list())
    min_price_variation_filtered_orders: list = field(default_factory=lambda: list())
    no_tick_data_filtered_orders: list = field(default_factory=lambda: list())
    failed_uptick_cal_filtered_orders: list = field(default_factory=lambda: list())
    uptick_less_filtered_orders: list = field(default_factory=lambda: list())
    no_comparison_volume_filtered_orders: list = field(default_factory=lambda: list())
    comparison_volume_less_filtered_orders: list = field(default_factory=lambda: list())


REASON_MAP = {
    "time_window_filtered_orders": "Orders dropped after applying time window threshold.",
    "no_orders_in_time_window_filtered_orders": "No other orders found within time window",
    "unique_orders_count_less_filtered_orders": "Unique orders in time window group is less than threshold",
    "counterparty_count_less_filtered_orders": "Counterparty count is greater than 1 for positive sameCounterParty threshold",
    "price_improvement_filtered_orders": "Order group does not have price improvement",
    "min_price_variation_filtered_orders": "Minimum percentage price variation of order group less than threshold",
    "no_tick_data_filtered_orders": "Order group's instrument has no tick data",
    "failed_uptick_cal_filtered_orders": "Calculation of the upTickPercentage failed",
    "uptick_less_filtered_orders": "Computed upTickPercentage less than threshold",
    "no_comparison_volume_filtered_orders": "Order group has no comparison volume",
    "comparison_volume_less_filtered_orders": "Comparison volume metric less than threshold",
}
