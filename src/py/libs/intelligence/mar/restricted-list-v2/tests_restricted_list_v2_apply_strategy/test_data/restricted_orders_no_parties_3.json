[{"&hash": "7ef540143527d4cecb853f4cca01dee0cb37888dea84532a8fbf7bc4f25784e7", "&id": "rlv2SceGrpingthird4:1:NEWO", "&key": "Order:rlv2SceGrpingthird4:1:NEWO:1719406895846", "&model": "Order", "&timestamp": 1719406895846, "&user": "system", "&uniqueProps": [], "&validationErrors": [{"field_path": "instrumentDetails.instrument.ext.bestExAssetClassMain", "message": "`BestEx Asset Class` must be populated", "code": "SE_DV-48", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.ext.instrumentUniqueIdentifier", "message": "'Instrument Unique Identifier' must be populated", "code": "SE_DV-52", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentClassification", "message": "`Instrument Classification` must be populated", "code": "SE_DV-58", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentIdCode", "message": "`Instrument Identification Code` must be a valid ISIN", "code": "SE_DV-60", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.native", "message": "`Notional Value` in Native Currency must be populated for Orders", "code": "SE_DV-136", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.EUR", "message": "`Notional Value` in 'EUR' must be populated for Orders", "code": "SE_DV-138", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.GBP", "message": "`Notional Value` in 'GBP' must be populated for Orders", "code": "SE_DV-369", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.USD", "message": "`Notional Value` in 'USD' must be populated for Orders", "code": "SE_DV-371", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.JPY", "message": "`Notional Value` in 'JPY' must be populated for Orders", "code": "SE_DV-373", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.CHF", "message": "`Notional Value` in 'CHF' must be populated for Orders", "code": "SE_DV-375", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&version": 1, "buyer": [{"&id": "eb19f1d9-79c9-4659-a53b-f5b09908920c", "&key": "AccountPerson:eb19f1d9-79c9-4659-a53b-f5b09908920c:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "matt.storey", "label": "Live Chat"}, {"id": "matt.storey", "label": "BBG"}]}, "name": "<PERSON>", "officialIdentifiers": {"concatId": "GB19901226MATT#STORE", "mifirId": "GB19901226MATT#STORE", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1990-12-26", "firstName": "<PERSON>", "lastName": "Storey", "nationality": ["GB"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "matt.storey", "label": "id"}, {"id": "matt.storey", "label": "account"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "matt.storey", "account:matt.storey", "id:matt.storey"]}], "buyerDecisionMaker": [{"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "isCreatedThroughPartyFallback": true, "name": "exec1"}], "buyerDecisionMakerFileIdentifier": "account:exec1", "buyerFileIdentifier": "account:matt.storey", "buySell": "1", "clientFileIdentifier": "account:matt.storey", "clientIdentifiers": {"client": [{"&id": "eb19f1d9-79c9-4659-a53b-f5b09908920c", "&key": "AccountPerson:eb19f1d9-79c9-4659-a53b-f5b09908920c:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "matt.storey", "label": "Live Chat"}, {"id": "matt.storey", "label": "BBG"}]}, "name": "<PERSON>", "officialIdentifiers": {"concatId": "GB19901226MATT#STORE", "mifirId": "GB19901226MATT#STORE", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1990-12-26", "firstName": "<PERSON>", "lastName": "Storey", "nationality": ["GB"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "matt.storey", "label": "id"}, {"id": "matt.storey", "label": "account"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "matt.storey", "account:matt.storey", "id:matt.storey"]}]}, "counterparty": {"&id": "matt.storey", "&key": "MarketCounterparty:matt.storey:**********", "isCreatedThroughPartyFallback": true, "name": "matt.storey"}, "counterpartyFileIdentifier": "account:matt.storey", "dataSourceName": "SteelEyeTradeBlotter", "date": "2023-12-29", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- matt.storey;Counterparty ID- matt.storey;Trader ID- matt.storey", "tradingCapacity": "AOTC"}, "hierarchy": "Standalone", "id": "rlv2SceGrpingthird4", "instrumentDetails": {"instrument": {"derivative": {"deliveryType": "CASH"}, "ext": {"priceNotation": "MONE", "quantityNotation": "UNIT"}, "instrumentFullName": "US02999351079", "instrumentIdCode": "US02999351079", "isCreatedThroughFallback": true, "notionalCurrency1": "USD"}}, "isDiscretionary": true, "isSynthetic": false, "jurisdiction": {"businessLine": "Execution Hub", "country": "UK"}, "marketIdentifiers": [{"labelId": "account:matt.storey", "path": "buyer", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:matt.storey", "path": "seller", "type": "ARRAY"}, {"labelId": "account:matt.storey", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:vas", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:matt.storey", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:matt.storey", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:matt.storey", "path": "trader", "type": "ARRAY"}], "marDetails": {"isPersonalAccountDealing": false}, "orderIdentifiers": {"aggregatedOrderId": "rlv2SceGrpingthird4", "internalOrderIdCode": "rlv2SceGrpingthird4", "orderIdCode": "rlv2SceGrpingthird4"}, "priceFormingData": {"initialQuantity": 200.0}, "reportDetails": {"executingEntity": {"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "fileIdentifier": "account:exec1", "isCreatedThroughPartyFallback": true, "name": "exec1"}, "transactionRefNo": "RLV2SCEGRPINGTHIRD4BUYI"}, "seller": [{"&id": "eb19f1d9-79c9-4659-a53b-f5b09908920c", "&key": "AccountPerson:eb19f1d9-79c9-4659-a53b-f5b09908920c:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "matt.storey", "label": "Live Chat"}, {"id": "matt.storey", "label": "BBG"}]}, "name": "<PERSON>", "officialIdentifiers": {"concatId": "GB19901226MATT#STORE", "mifirId": "GB19901226MATT#STORE", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1990-12-26", "firstName": "<PERSON>", "lastName": "Storey", "nationality": ["GB"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "matt.storey", "label": "id"}, {"id": "matt.storey", "label": "account"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "matt.storey", "account:matt.storey", "id:matt.storey"]}], "sellerFileIdentifier": "account:matt.storey", "sourceIndex": "6", "sourceKey": "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/scenariogroupingrlv2ordersfile.csv", "timestamps": {"orderReceived": "2023-12-29T09:46:36Z", "orderStatusUpdated": "2023-12-29T09:46:36Z", "orderSubmitted": "2023-12-29T09:46:36Z"}, "trader": [{"&id": "eb19f1d9-79c9-4659-a53b-f5b09908920c", "&key": "AccountPerson:eb19f1d9-79c9-4659-a53b-f5b09908920c:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "matt.storey", "label": "Live Chat"}, {"id": "matt.storey", "label": "BBG"}]}, "name": "<PERSON>", "officialIdentifiers": {"concatId": "GB19901226MATT#STORE", "mifirId": "GB19901226MATT#STORE", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1990-12-26", "firstName": "<PERSON>", "lastName": "Storey", "nationality": ["GB"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "matt.storey", "label": "id"}, {"id": "matt.storey", "label": "account"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "matt.storey", "account:matt.storey", "id:matt.storey"]}], "traderFileIdentifier": "account:matt.storey", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "eb19f1d9-79c9-4659-a53b-f5b09908920c", "&key": "AccountPerson:eb19f1d9-79c9-4659-a53b-f5b09908920c:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "matt.storey", "label": "Live Chat"}, {"id": "matt.storey", "label": "BBG"}]}, "name": "<PERSON>", "officialIdentifiers": {"concatId": "GB19901226MATT#STORE", "mifirId": "GB19901226MATT#STORE", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1990-12-26", "firstName": "<PERSON>", "lastName": "Storey", "nationality": ["GB"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "matt.storey", "label": "id"}, {"id": "matt.storey", "label": "account"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "matt.storey", "account:matt.storey", "id:matt.storey"]}, "executionWithinFirmFileIdentifier": "account:matt.storey", "investmentDecisionWithinFirm": {"&id": "vas", "&key": "MarketPerson:vas:**********", "isCreatedThroughPartyFallback": true, "name": "vas", "retailOrProfessional": "N/A"}, "investmentDecisionWithinFirmFileIdentifier": "account:vas"}, "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "AOTC", "venue": "XOFF"}}, {"&hash": "b293105f42b26201f486eeea2993cb610d5efd3718c7f469d9721c3c716185cf", "&id": "rlv2SceGrpingthird5:1:NEWO", "&key": "Order:rlv2SceGrpingthird5:1:NEWO:*************", "&model": "Order", "&timestamp": *************, "&user": "system", "&uniqueProps": [], "&validationErrors": [{"field_path": "instrumentDetails.instrument.ext.bestExAssetClassMain", "message": "`BestEx Asset Class` must be populated", "code": "SE_DV-48", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.ext.instrumentUniqueIdentifier", "message": "'Instrument Unique Identifier' must be populated", "code": "SE_DV-52", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentClassification", "message": "`Instrument Classification` must be populated", "code": "SE_DV-58", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentIdCode", "message": "`Instrument Identification Code` must be a valid ISIN", "code": "SE_DV-60", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.native", "message": "`Notional Value` in Native Currency must be populated for Orders", "code": "SE_DV-136", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.EUR", "message": "`Notional Value` in 'EUR' must be populated for Orders", "code": "SE_DV-138", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.GBP", "message": "`Notional Value` in 'GBP' must be populated for Orders", "code": "SE_DV-369", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.USD", "message": "`Notional Value` in 'USD' must be populated for Orders", "code": "SE_DV-371", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.JPY", "message": "`Notional Value` in 'JPY' must be populated for Orders", "code": "SE_DV-373", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.CHF", "message": "`Notional Value` in 'CHF' must be populated for Orders", "code": "SE_DV-375", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&version": 1, "buyer": [{"&id": "e6549c0b-dc4c-4316-8dc1-bc0be551bfac", "&key": "AccountPerson:e6549c0b-dc4c-4316-8dc1-bc0be551bfac:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "Live Chat"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "BBG"}]}, "name": "<PERSON><PERSON><PERSON>", "officialIdentifiers": {"concatId": "********************", "mifirId": "********************", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1957-08-24", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tirmale", "nationality": ["IN"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "account"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "id"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "id:chandra<PERSON><PERSON>.tirmale", "chandra<PERSON><PERSON>.tirmale", "account:chandra<PERSON><PERSON>.tirmale"]}], "buyerDecisionMaker": [{"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "isCreatedThroughPartyFallback": true, "name": "exec1"}], "buyerDecisionMakerFileIdentifier": "account:exec1", "buyerFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "buySell": "1", "clientFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "clientIdentifiers": {"client": [{"&id": "e6549c0b-dc4c-4316-8dc1-bc0be551bfac", "&key": "AccountPerson:e6549c0b-dc4c-4316-8dc1-bc0be551bfac:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "Live Chat"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "BBG"}]}, "name": "<PERSON><PERSON><PERSON>", "officialIdentifiers": {"concatId": "********************", "mifirId": "********************", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1957-08-24", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tirmale", "nationality": ["IN"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "account"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "id"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "id:chandra<PERSON><PERSON>.tirmale", "chandra<PERSON><PERSON>.tirmale", "account:chandra<PERSON><PERSON>.tirmale"]}]}, "counterparty": {"&id": "chandra<PERSON><PERSON>.tirmale", "&key": "MarketCounterparty:chandra<PERSON><PERSON>.tirmale:**********", "isCreatedThroughPartyFallback": true, "name": "chandra<PERSON><PERSON>.tirmale"}, "counterpartyFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "dataSourceName": "SteelEyeTradeBlotter", "date": "2023-12-29", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- chandra<PERSON><PERSON>.tirmale;Counterparty ID- chandrashekhar.tirmale;Trader ID- chandrashekhar.tirmale", "tradingCapacity": "AOTC"}, "hierarchy": "Standalone", "id": "rlv2SceGrpingthird5", "instrumentDetails": {"instrument": {"derivative": {"deliveryType": "CASH"}, "ext": {"priceNotation": "MONE", "quantityNotation": "UNIT"}, "instrumentFullName": "US02999351079", "instrumentIdCode": "US02999351079", "isCreatedThroughFallback": true, "notionalCurrency1": "USD"}}, "isDiscretionary": true, "isSynthetic": false, "jurisdiction": {"businessLine": "Execution Hub", "country": "UK"}, "marketIdentifiers": [{"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "buyer", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "seller", "type": "ARRAY"}, {"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:vas", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:chandra<PERSON><PERSON>.tirmale", "path": "trader", "type": "ARRAY"}], "marDetails": {"isPersonalAccountDealing": false}, "orderIdentifiers": {"aggregatedOrderId": "rlv2SceGrpingthird5", "internalOrderIdCode": "rlv2SceGrpingthird5", "orderIdCode": "rlv2SceGrpingthird5"}, "priceFormingData": {"initialQuantity": 200.0}, "reportDetails": {"executingEntity": {"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "fileIdentifier": "account:exec1", "isCreatedThroughPartyFallback": true, "name": "exec1"}, "transactionRefNo": "RLV2SCEGRPINGTHIRD5BUYI"}, "seller": [{"&id": "e6549c0b-dc4c-4316-8dc1-bc0be551bfac", "&key": "AccountPerson:e6549c0b-dc4c-4316-8dc1-bc0be551bfac:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "Live Chat"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "BBG"}]}, "name": "<PERSON><PERSON><PERSON>", "officialIdentifiers": {"concatId": "********************", "mifirId": "********************", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1957-08-24", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tirmale", "nationality": ["IN"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "account"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "id"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "id:chandra<PERSON><PERSON>.tirmale", "chandra<PERSON><PERSON>.tirmale", "account:chandra<PERSON><PERSON>.tirmale"]}], "sellerFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "sourceIndex": "7", "sourceKey": "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/scenariogroupingrlv2ordersfile.csv", "timestamps": {"orderReceived": "2023-12-29T09:46:36Z", "orderStatusUpdated": "2023-12-29T09:46:36Z", "orderSubmitted": "2023-12-29T09:46:36Z"}, "trader": [{"&id": "e6549c0b-dc4c-4316-8dc1-bc0be551bfac", "&key": "AccountPerson:e6549c0b-dc4c-4316-8dc1-bc0be551bfac:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "Live Chat"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "BBG"}]}, "name": "<PERSON><PERSON><PERSON>", "officialIdentifiers": {"concatId": "********************", "mifirId": "********************", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1957-08-24", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tirmale", "nationality": ["IN"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "account"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "id"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "id:chandra<PERSON><PERSON>.tirmale", "chandra<PERSON><PERSON>.tirmale", "account:chandra<PERSON><PERSON>.tirmale"]}], "traderFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "e6549c0b-dc4c-4316-8dc1-bc0be551bfac", "&key": "AccountPerson:e6549c0b-dc4c-4316-8dc1-bc0be551bfac:*************", "communications": {"emails": ["<EMAIL>"], "imAccounts": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "Live Chat"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "BBG"}]}, "name": "<PERSON><PERSON><PERSON>", "officialIdentifiers": {"concatId": "********************", "mifirId": "********************", "mifirIdSubType": "CONCAT", "mifirIdType": "N"}, "personalDetails": {"dob": "1957-08-24", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tirmale", "nationality": ["IN"]}, "retailOrProfessional": "N/A", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "chandra<PERSON><PERSON>.tirmale", "label": "account"}, {"id": "chandra<PERSON><PERSON>.tirmale", "label": "id"}]}, "sourceKey": "s3://mares8.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv", "structure": {"department": "Macro Desk", "role": "Macro Trader"}, "uniqueIds": ["<EMAIL>", "id:chandra<PERSON><PERSON>.tirmale", "chandra<PERSON><PERSON>.tirmale", "account:chandra<PERSON><PERSON>.tirmale"]}, "executionWithinFirmFileIdentifier": "account:chandra<PERSON><PERSON>.tirmale", "investmentDecisionWithinFirm": {"&id": "vas", "&key": "MarketPerson:vas:**********", "isCreatedThroughPartyFallback": true, "name": "vas", "retailOrProfessional": "N/A"}, "investmentDecisionWithinFirmFileIdentifier": "account:vas"}, "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "AOTC", "venue": "XOFF"}}, {"&hash": "28005f81945b7d3de368c2302ddb8025e270c5c6c1a19802d2213475ba116558", "&id": "rlv2SceGrpingthird6:1:NEWO", "&key": "Order:rlv2SceGrpingthird6:1:NEWO:*************", "&model": "Order", "&timestamp": *************, "&user": "system", "&uniqueProps": [], "&validationErrors": [{"field_path": "instrumentDetails.instrument.ext.bestExAssetClassMain", "message": "`BestEx Asset Class` must be populated", "code": "SE_DV-48", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.ext.instrumentUniqueIdentifier", "message": "'Instrument Unique Identifier' must be populated", "code": "SE_DV-52", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentClassification", "message": "`Instrument Classification` must be populated", "code": "SE_DV-58", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "instrumentDetails.instrument.instrumentIdCode", "message": "`Instrument Identification Code` must be a valid ISIN", "code": "SE_DV-60", "category": "Instruments", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.native", "message": "`Notional Value` in Native Currency must be populated for Orders", "code": "SE_DV-136", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.EUR", "message": "`Notional Value` in 'EUR' must be populated for Orders", "code": "SE_DV-138", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.GBP", "message": "`Notional Value` in 'GBP' must be populated for Orders", "code": "SE_DV-369", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.USD", "message": "`Notional Value` in 'USD' must be populated for Orders", "code": "SE_DV-371", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.JPY", "message": "`Notional Value` in 'JPY' must be populated for Orders", "code": "SE_DV-373", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}, {"field_path": "bestExecutionData.orderVolume.ecbRefRate.CHF", "message": "`Notional Value` in 'CHF' must be populated for Orders", "code": "SE_DV-375", "category": "Quantities", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&version": 1, "buyer": [{"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketCounterparty:ar<PERSON>.shi<PERSON>raj:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj"}], "buyerDecisionMaker": [{"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "isCreatedThroughPartyFallback": true, "name": "exec1"}], "buyerDecisionMakerFileIdentifier": "account:exec1", "buyerFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "buySell": "1", "clientFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "clientIdentifiers": {"client": [{"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketCounterparty:ar<PERSON>.shi<PERSON>raj:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj"}]}, "counterparty": {"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketCounterparty:ar<PERSON>.shi<PERSON>raj:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj"}, "counterpartyFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "dataSourceName": "SteelEyeTradeBlotter", "date": "2023-12-29", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- arjun.shivraj;Counterparty ID- arjun.shivraj;Trader ID- arjun.shivraj", "tradingCapacity": "AOTC"}, "hierarchy": "Standalone", "id": "rlv2SceGrpingthird6", "instrumentDetails": {"instrument": {"derivative": {"deliveryType": "CASH"}, "ext": {"priceNotation": "MONE", "quantityNotation": "UNIT"}, "instrumentFullName": "US02999351079", "instrumentIdCode": "US02999351079", "isCreatedThroughFallback": true, "notionalCurrency1": "USD"}}, "isDiscretionary": true, "isSynthetic": false, "jurisdiction": {"businessLine": "Execution Hub", "country": "UK"}, "marketIdentifiers": [{"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "buyer", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "account:exec1", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "seller", "type": "ARRAY"}, {"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:vas", "path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm", "type": "OBJECT"}, {"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "path": "trader", "type": "ARRAY"}], "marDetails": {"isPersonalAccountDealing": false}, "orderIdentifiers": {"aggregatedOrderId": "rlv2SceGrpingthird6", "internalOrderIdCode": "rlv2SceGrpingthird6", "orderIdCode": "rlv2SceGrpingthird6"}, "priceFormingData": {"initialQuantity": 200.0}, "reportDetails": {"executingEntity": {"&id": "exec1", "&key": "MarketCounterparty:exec1:**********", "fileIdentifier": "account:exec1", "isCreatedThroughPartyFallback": true, "name": "exec1"}, "transactionRefNo": "RLV2SCEGRPINGTHIRD6BUYI"}, "seller": [{"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketCounterparty:ar<PERSON>.shi<PERSON>raj:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj"}], "sellerFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "sourceIndex": "8", "sourceKey": "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/scenariogroupingrlv2ordersfile.csv", "timestamps": {"orderReceived": "2023-12-29T09:46:36Z", "orderStatusUpdated": "2023-12-29T09:46:36Z", "orderSubmitted": "2023-12-29T09:46:36Z"}, "trader": [{"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketPerson:ar<PERSON>.shi<PERSON><PERSON>:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj", "retailOrProfessional": "N/A"}], "traderFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "tradersAlgosWaiversIndicators": {"executionWithinFirm": {"&id": "ar<PERSON><PERSON>shi<PERSON>raj", "&key": "MarketPerson:ar<PERSON>.shi<PERSON><PERSON>:**********", "isCreatedThroughPartyFallback": true, "name": "ar<PERSON><PERSON>shi<PERSON>raj", "retailOrProfessional": "N/A"}, "executionWithinFirmFileIdentifier": "account:ar<PERSON><PERSON>s<PERSON><PERSON>raj", "investmentDecisionWithinFirm": {"&id": "vas", "&key": "MarketPerson:vas:**********", "isCreatedThroughPartyFallback": true, "name": "vas", "retailOrProfessional": "N/A"}, "investmentDecisionWithinFirmFileIdentifier": "account:vas"}, "transactionDetails": {"buySellIndicator": "BUYI", "priceCurrency": "USD", "priceNotation": "MONE", "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "AOTC", "venue": "XOFF"}}]