import pytest


@pytest.mark.parametrize("env", ["prod", "dev"])
def test_get_crypto_factory(env: str, monkeypatch):
    import os

    os.environ["AWS_REGION"] = "a_region"
    os.environ["ENV"] = env

    import boto3

    monkeypatch.setattr(boto3, "client", lambda *args, **kwargs: None)

    from comms_dataset_utils.parquet_handling.encryption import _get_crypto_factory

    _, kms_connection_config = _get_crypto_factory("test_tenant", region="eu-west-1")

    expected = "alias/test_tenant/steeleye/co"

    assert kms_connection_config.key_access_token == expected
