from pydantic import BaseSettings, Field
from typing import Optional


class TaskConfig(BaseSettings):
    """This class is a global settings config for the task."""

    AWS_REGION: str = Field("eu-west-1", description="region")
    KMS_AWS_REGION: Optional[str] = Field(
        None, description="region of ksm, user if different from s3"
    )
    ENV: str
    READ_BATCH_SIZE: int = Field(2000, description="Size of batches for reading parquets")
    WRITE_BATCH_SIZE: int = Field(100, description="Size of batches for writing parquets")
    NO_OF_WORKERS_FOR_DATA_WRITE: int = 3
    ONLY_POSITIVES: bool = False


TASK_CONFIG = TaskConfig()
