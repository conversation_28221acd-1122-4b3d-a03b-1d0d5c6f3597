import backoff
import concurrent.futures
import logging
import multiprocessing as mp
import pyarrow as pa
import pyarrow.compute as pc
import se_schema_meta
import uuid
from comms_dataset_utils.arrow_fs_client import get_arrow_fs_client
from comms_dataset_utils.config import TASK_CONFIG
from comms_dataset_utils.parquet_handling.encryption import get_encryption_properties
from comms_dataset_utils.parquet_handling.static import MAIN_SCHEMA, CommunicationAlertFields
from functools import partial
from itertools import product

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(name)s | %(levelname)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger("comms_dataset_utils")


@backoff.on_exception(backoff.expo, OSError, max_tries=6)
def retryable_write(tbl, path_prefix, partition_cols, basename_template, tenant):
    pa.parquet.write_to_dataset(
        tbl,
        path_prefix,
        partition_cols=partition_cols,
        filesystem=get_arrow_fs_client(),
        basename_template=basename_template,
        encryption_properties=get_encryption_properties(tenant),
        compression_level=22,
        compression="ZSTD",
        existing_data_behavior="overwrite_or_ignore",
        schema=MAIN_SCHEMA,
    )


def process_parquet_write(split, path_prefix, tbl, partition_cols, tenant):
    model = split[0].as_py()
    year = split[1].as_py()

    mcol = se_schema_meta.MODEL if len(partition_cols) == 2 else CommunicationAlertFields.HIT_MODEL

    model_mask = pc.equal(tbl[mcol], model)
    year_mask = pc.equal(tbl["year"], year)
    batched_df = tbl.filter(pc.and_(model_mask, year_mask))

    # First portion of a uuid4 is unique enough for our purposes
    file_id = str(uuid.uuid4())[:8]

    logger.info(f"Processing batch for '{model}'-'{year}' with {len(batched_df)} records")
    retryable_write(
        tbl=batched_df,
        path_prefix=path_prefix,
        partition_cols=partition_cols,
        basename_template=f"part_{file_id}" + "{i}.parquet",
        tenant=tenant,
    )


def write_to_cloud(parquet_data, path_prefix, model, tenant, use_mp=True):
    if model == "CommunicationAlert":
        models = pc.unique(parquet_data[CommunicationAlertFields.HIT_MODEL])
        partition_cols = [se_schema_meta.MODEL, CommunicationAlertFields.HIT_MODEL]
    else:
        models = pc.unique(parquet_data[se_schema_meta.MODEL])
        partition_cols = [se_schema_meta.MODEL]

    years = pc.unique(parquet_data["year"])
    partition_cols.append("year")

    process_parquet_write_quantized = partial(
        process_parquet_write,
        path_prefix=path_prefix,
        tbl=parquet_data,
        partition_cols=partition_cols,
        tenant=tenant,
    )

    batches = list(product(models, years))

    logger.info(f"Total {len(batches)} batches to be written for model {model}")

    if use_mp:
        with concurrent.futures.ProcessPoolExecutor(
            max_workers=TASK_CONFIG.NO_OF_WORKERS_FOR_DATA_WRITE, mp_context=mp.get_context("spawn")
        ) as executor:
            list(
                executor.map(
                    process_parquet_write_quantized,
                    models,
                )
            )
    else:
        for split in batches:
            process_parquet_write_quantized(split)
