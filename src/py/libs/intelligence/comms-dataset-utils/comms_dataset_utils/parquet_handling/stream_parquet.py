import logging
from comms_dataset_utils.parquet_handling.encryption import get_decryption_properties
from comms_dataset_utils.parquet_handling.read_parquet import (
    get_processed_ids,
    read_encrypted_parquet,
)
from datetime import datetime

logger = logging.getLogger("comms_dataset_utils")


def stream_data(
    tenant: str,
    model: str,
    date_range_start: datetime,
    date_range_end: datetime,
    path_prefix: str,
    search_after_path: str | None = None,
):
    decrypt_props = get_decryption_properties(tenant)

    seen_ids = []
    if search_after_path:
        seen_ids = get_processed_ids(
            path_prefix=search_after_path,
            model=model,
            decrypt_props=decrypt_props,
            tenant=tenant,
            date_range_start=date_range_start,
            date_range_end=date_range_end,
        )

    logger.info(f"Staring parquet read for {model}")

    yield read_encrypted_parquet(
        path_prefix=path_prefix,
        model=model,
        decrypt_props=decrypt_props,
        tenant=tenant,
        date_range_start=date_range_start,
        date_range_end=date_range_end,
        seen_ids=seen_ids,
    )
