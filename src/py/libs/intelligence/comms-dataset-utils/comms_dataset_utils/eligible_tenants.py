import fsspec
from comms_dataset_utils.config import TASK_CONFIG
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def get_eligible_tenants_es(models: set[str]) -> set[str]:
    record_handler = SlimRecordHandler()

    mapping = {
        "CommunicationAlert": "communication_alert",
        "Email": "email",
        "Call": "call",
        "Message": "message",
        "Text": "text",
    }

    assert all(model in mapping for model in models)

    # Fetch
    response = record_handler.es_repo._client.indices.get_alias(name="*").body

    all_tenants = set()
    for index, vals in response.items():
        found = False
        for model in models:
            if f"-{mapping[model]}-index" in index:
                found = True
                break
        if not found:
            continue

        aliases = vals["aliases"].keys()

        for alias in aliases:
            tenant = alias.split(f"-{mapping[model]}")[0]
            all_tenants.add(tenant)

    return all_tenants


def tenant_s3_bucket(tenant: str, env: str) -> str:
    if env == "prod":
        env = ""
    else:
        env += "."

    return f"{tenant}.{env}steeleye.co"


def get_eligible_tenants_s3(models: set[str], path_prefix: str) -> set[str]:
    possible_tenants = get_eligible_tenants_es(models)

    fs, _, (_,) = fsspec.get_fs_token_paths("s3://")
    fs = fsspec.filesystem("s3", anon=False, client_kwargs={"region_name": TASK_CONFIG.AWS_REGION})
    eligible = set()
    for tenant in possible_tenants:
        bucket = "s3://" + tenant_s3_bucket(tenant, TASK_CONFIG.ENV)
        if fs.exists(f"{bucket}/{path_prefix}"):
            eligible.add(tenant)
    return eligible
