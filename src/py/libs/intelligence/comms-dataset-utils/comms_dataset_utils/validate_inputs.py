from comms_dataset_utils.eligible_tenants import get_eligible_tenants_es, get_eligible_tenants_s3
from comms_dataset_utils.parquet_handling.static import SUPPORTED_MODELS
from datetime import datetime, timezone


def validate_inputs(
    tenants: set[str],
    models: set[str],
    date_range_start: str,
    date_range_end: str,
    path_prefix: str,
    just_es: bool = False,
) -> tuple[set[str], set[str], datetime, datetime, str]:
    assert path_prefix

    path_prefix = path_prefix.strip("/") + "/"

    datetime_start = datetime.strptime(date_range_start, "%Y-%m-%dT%H:%M:%S").replace(
        tzinfo=timezone.utc
    )
    datetime_end = datetime.strptime(date_range_end, "%Y-%m-%dT%H:%M:%S").replace(
        tzinfo=timezone.utc
    )

    if "*" in models:
        models = set(SUPPORTED_MODELS.keys())

    if "*" in tenants:
        if just_es:
            tenants = get_eligible_tenants_es(models)
        else:
            tenants = get_eligible_tenants_s3(models, path_prefix)

    return tenants, models, datetime_start, datetime_end, path_prefix
