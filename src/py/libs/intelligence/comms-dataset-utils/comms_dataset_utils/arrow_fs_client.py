import boto3
from comms_dataset_utils.config import TASK_CONFIG
from pyarrow import fs


def get_arrow_fs_client():
    # Create AWS Creds to initialize file system
    # We need to explicitly do it as pyarrow
    # credential inference is wonky
    session = boto3.session.Session(region_name=TASK_CONFIG.AWS_REGION)
    credentials = session.get_credentials()
    s3_fs = fs.S3FileSystem(
        access_key=credentials.access_key,  # type: ignore[union-attr]
        secret_key=credentials.secret_key,  # type: ignore[union-attr]
        session_token=credentials.token,  # type: ignore[union-attr]
        region=session.region_name,
    )
    return s3_fs
