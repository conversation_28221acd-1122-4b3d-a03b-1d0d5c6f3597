from aries_se_api_client.client import AriesApiClient as SeApiClient
from data_platform_config_api_client.lexica import LexicaAP<PERSON>
from data_platform_config_api_client.stack import <PERSON>ackAPI
from fastapi import HTTPException
from httpx import HTTPStatusError
from typing import List


class SeLexicaConfigClient:
    def __init__(self, data_platform_config_url: str, stack: str) -> None:
        self.data_platform_config_url = data_platform_config_url
        self.stack_name = stack

    def get_data_platform_config_lexica_client(self):
        """Get a client for the data platform config lexica API.

        :return: A client for the data platform config lexica API
        :rtype: LexicaAPI
        """
        return LexicaAPI(SeApiClient(host=self.data_platform_config_url))

    def get_data_platform_config_stack_client(self):
        """Get a client for the data platform config stack API.

        :return: A client for the data platform config stack API
        :rtype: StackAPI
        """
        return StackAPI(SeApiClient(host=self.data_platform_config_url))

    def get_se_lexica_version_for_tenant(self, tenant: str) -> str:
        """Get the version of the SE lexica for a given tenant.

        :param tenant: The tenant to get the version for
        :type tenant: str
        :raises HTTPException: A 404 is raised if the tenant is not found
        :raises e: Any other HTTP error is raised
        :return: The version of the SE lexica for the tenant
        :rtype: str
        """
        data_platform_config_lexica_client = self.get_data_platform_config_lexica_client()
        try:
            se_lexica_resp = data_platform_config_lexica_client.get_se_lexica_version(
                tenant_name=tenant, stack_name=self.stack_name
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Tenant not found")
            raise e

        return str(se_lexica_resp.content.get("se_lexica_version", ""))

    def get_subscribed_languages(self, tenant: str, raise_errors: bool = False) -> List[str]:
        """Get the list of languages a tenant is subscribed to.

        :param tenant: The tenant to get the languages for
        :type tenant: str
        :param raise_errors: Whether to raise errors or not, defaults to False
        :type raise_errors: bool, optional
        :raises HTTPException: A 404 is raised if the tenant is not found
        :raises e: If the response does not contain the expected data
        :return: The list of languages the tenant is subscribed to
        :rtype: List[str]
        """
        data_platform_config_lexica_client = self.get_data_platform_config_lexica_client()
        try:
            se_lexica_resp = data_platform_config_lexica_client.get_se_lexica_version(
                tenant_name=tenant, stack_name=self.stack_name
            )
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Tenant not found")
            raise e

        try:
            return [str(lang) for lang in se_lexica_resp.content["subscribed_translations"]]
        except KeyError as e:
            if raise_errors:
                raise e
            return ["en"]

    def get_tenant_list(self) -> List[str]:
        """Get the list of tenants in the stack.

        :raises HTTPException: A 404 is raised if the stack is not found
        :raises e: Any other HTTP error is raised
        :return: The list of tenants in the stack
        :rtype: List[str]
        """
        data_platform_config_stack_client = self.get_data_platform_config_stack_client()
        try:
            resp = data_platform_config_stack_client.get_by_name(stack_name=self.stack_name)
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(status_code=404, detail="Stack not found")
            raise e
        tenants = [
            str(tenant.get("name"))
            for tenant in resp.content.get("tenants", [])
            if tenant.get("name") not in [None, self.stack_name]
        ]
        return tenants
