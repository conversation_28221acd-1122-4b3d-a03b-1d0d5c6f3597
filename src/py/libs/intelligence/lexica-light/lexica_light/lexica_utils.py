# ruff: noqa: E501
from datetime import datetime
from se_db_utils.database import Database
from typing import List


def get_version_tag_for_tenant(
    pg_db: Database,
    tenant: str,
    se_lexica_version: str,
    matcher_version: str,
    subscribed_languages: List[str],
):
    """Get the version tag for a tenant.

    :param pg_db: A database connection
    :type pg_db: Database
    :param tenant: The tenant to get the version tag for
    :type tenant: str
    :param se_lexica_version: The SE Lexica version
    :type se_lexica_version: str
    :param subscribed_languages: The languages the tenant is subscribed to
    :type subscribed_languages: List[str]
    :return: The version tag for the tenant
    :rtype: str
    """
    latest_edit_query = (
        "SELECT GREATEST(max_term, max_sub_behaviour, max_behaviour) FROM "
        f'    (SELECT MAX(GREATEST("{tenant}"."Term"."createdDateTime", "{tenant}"."Term"."updatedDateTime")) AS max_term FROM "{tenant}"."Term" '
        f'    WHERE "{tenant}"."Term".tenant = \'{tenant}\') AS term_sub_q,'
        f'    (SELECT MAX(GREATEST("{tenant}"."SubBehaviour"."createdDateTime", "{tenant}"."SubBehaviour"."updatedDateTime")) AS max_sub_behaviour FROM "{tenant}"."SubBehaviour" '
        f'    WHERE "{tenant}"."SubBehaviour".tenant = \'{tenant}\') AS behave_sub_q,'
        f'    (SELECT MAX(GREATEST("{tenant}"."Behaviour"."createdDateTime", "{tenant}"."Behaviour"."updatedDateTime")) AS max_behaviour FROM "{tenant}"."Behaviour" '
        f'    WHERE "{tenant}"."Behaviour".tenant = \'{tenant}\') AS behave_q;'
    )

    count_terms_query = (
        f'SELECT COUNT(*) FROM "{tenant}"."Term" '
        f"WHERE tenant = '{tenant}' AND NOT retired "
        'AND "subBehaviourId" IN ( '
        f'    SELECT id FROM "{tenant}"."SubBehaviour" '
        f"    WHERE tenant = '{tenant}' AND NOT retired "
        '    AND "behaviourId" IN ( '
        f'        SELECT id FROM "{tenant}"."Behaviour" '
        f"        WHERE tenant = '{tenant}' AND NOT retired AND NOT \"fromSeLexica\" "
        "    ) "
        ") "
    )

    with pg_db.session(tenant) as session:
        latest_edit = session.execute(latest_edit_query).fetchone()[0]
        count_terms = session.execute(count_terms_query).fetchone()[0]

    if latest_edit is None:
        # default to start of steeleye
        latest_edit = datetime.fromisoformat("2017-01-01T00:00:00+00:00")

    subscribed_languages_str: str = "_".join(sorted(list(set(subscribed_languages)))) or "NONE"

    return f"{matcher_version}___{se_lexica_version}___{subscribed_languages_str}___{latest_edit.timestamp()}___{count_terms}"
