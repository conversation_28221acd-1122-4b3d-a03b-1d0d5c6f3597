# ruff: noqa: E501
import datetime
import market_abuse_algorithms.data_source.repository.market_data.client
import numpy as np
import pandas as pd
import pyarrow as pa
import pytest
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    get_volume_at_level,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField
from market_abuse_algorithms.utils.data import get_list_instrument_unique_identifier
from pathlib import Path
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from tests_mar_apply_strategy_lib.tests_insider_trading_v3_refinitiv.thresholds_use_cases import (
    THRESHOLDS_USE_CASE_6,
)
from tests_market_abuse_algorithms.data_source.repository.market_data.mock_market_data_client import (
    MarketClient,
    MarketClientEmptyResponse,
    read_eod_stats,
    read_parquet_batch,
)
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture
def mock_context_test_case(helpers):
    filters = {}
    context = helpers.get_context(thresholds=THRESHOLDS_USE_CASE_6, filters=filters)
    return context


@pytest.fixture
def mock_timestamps_convert_convert_currencies() -> pd.Series:
    """
    mock timestamps to be used in convert currencies method
    :return: series of dates
    """
    return pd.Series(
        [
            pd.Timestamp(2022, 5, 25),
            pd.Timestamp(2022, 5, 26),
            pd.Timestamp(2022, 5, 20),
        ]
    )


@pytest.fixture
def mock_timestamps_convert_convert_currencies_missing_quote_data() -> pd.Series:
    """
    mock timestamps to be used in convert currencies method with missing data for the provided timestamps
    :return: series of dates
    """
    return pd.Series(
        [
            pd.Timestamp(2020, 5, 25),
            pd.Timestamp(2020, 5, 26),
            pd.Timestamp(2020, 5, 20),
        ]
    )


def mock_raise_connection_error(*args, **kwargs):
    """method to raise ConnectionError."""
    raise ConnectionError()


def get_fake_market_client(*args, **kwargs):
    return


def custom_date_parser():
    return lambda x: datetime.datetime.strptime(x, "%Y-%m-%d")


class TestMarketDataClient:
    def test_exists_calculate_volatility(self):
        from se_market_data_utils.statistics import calculate_volatility

        calculate_volatility

    @pytest.mark.parametrize("list_ids", [["US00165C1045USDFRAB"], [], None])
    @pytest.mark.parametrize("inst_combs", [[["US00165C1045USDFRAB"]], [], None])
    def test_get_ric_map(self, list_ids, inst_combs):
        client = MarketClient()

        x = client.get_ric_map(list_inst_unique_id=list_ids, instrument_combinations=inst_combs)
        if list_ids or inst_combs:
            assert x == {"US00165C1045USDFRAB": "AMC.N"}
        else:
            assert x is None

    @pytest.mark.parametrize("list_ids", [["XLON:GB00B10RZP78"], [], None])
    @pytest.mark.parametrize("inst_combs", [[["XLON:GB00B10RZP78"]], [], None])
    def test_get_local_ric_map(self, list_ids, inst_combs):
        client = MarketClient()

        x = client.get_local_ric_map(list_id_venue=list_ids, instrument_combinations=inst_combs)
        if list_ids or inst_combs:
            assert x == {"XLON:GB00B10RZP78": "ULVR.L"}
        else:
            assert x is None

    @pytest.mark.parametrize("list_ids", [["a"], []])
    @pytest.mark.parametrize("inst_combs", [[["a"]]])
    def test_get_ric_map_empty(self, list_ids, inst_combs):
        client = MarketClientEmptyResponse()

        x = client.get_ric_map(list_inst_unique_id=list_ids, instrument_combinations=inst_combs)
        assert x is None

    @pytest.mark.parametrize("list_ids", [["XLON:a"], []])
    @pytest.mark.parametrize("inst_combs", [[["XLON:a"]]])
    def test_get_local_ric_map_empty(self, list_ids, inst_combs):
        client = MarketClientEmptyResponse()

        x = client.get_local_ric_map(list_id_venue=list_ids, instrument_combinations=inst_combs)
        assert x == {}

    def test_calculate_average_daily_trading_volume(self):
        data = pd.read_csv(
            TEST_DATA.joinpath("market_data_stats.csv"),
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        client = MarketClient()
        adtv = client.calculate_average_daily_trading_volume(
            market_data=data,
            data_column="Trade Volume",
            look_back_period="20d",
            time_series="Date",
        )

        assert len(adtv) == 632
        assert adtv["2022-09-15 00:00:00"] == 4639645.25
        assert np.isnan(adtv[0])

    def test_get_avg_daily_trading_volume_trading_volume_zero_trd_vol(self, monkeypatch):
        def fake_get_eod_stats_with_margin(
            market_data_api=None,
            start_date=None,
            end_date=None,
            ric="",
        ):
            eod_file = None
            if ric == "278865BJ8=RRPS":
                eod_file = "test_zero_traded_vol_get_market_data_stats.csv"

            if eod_file is not None:
                return (
                    pa.dataset.dataset(TEST_DATA.joinpath(eod_file), format="csv")
                    .to_table()
                    .to_pandas()
                )

            return pa.Table.from_pandas(df=pd.DataFrame())

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "fetch_eod_stats_with_time_margin",
            fake_get_eod_stats_with_margin,
        )

        client = MarketClient()

        adtv = client.get_avg_daily_trading_volume(
            instrument_ric="278865BJ8=RRPS",
            look_back_period="20d",
            adtv_column="marketAverageDailyVolume",
            time_series_col="Date",
            start_date=pd.Timestamp("2022-01-01 00:00:00+0000", tz="UTC"),
            end_date=pd.Timestamp("2024-07-24 12:50:16.062783"),
        )

        assert len(adtv) == 0
        assert adtv.empty  # because trade volume is all zero

        market_data_eod: pd.DataFrame = client.get_market_data_stats(
            instrument_ric="278865BJ8=RRPS",
            start_date=pd.Timestamp("2022-01-01 00:00:00+0000", tz="UTC"),
            end_date=pd.Timestamp("2024-07-24 12:50:16.062783"),
            use_time_margin=True,
        )

        assert market_data_eod[TradeStatsColumns.TRADE_VOLUME].isna().all()

    def test_get_avg_daily_trading_volume(self):
        market_data = pd.read_csv(
            TEST_DATA.joinpath("market_data_stats.csv"),
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )

        client = MarketClient()

        client.get_market_data_stats = MagicMock()
        client.get_market_data_stats.return_value = market_data

        data = client.get_avg_daily_trading_volume(
            adtv_column="marketAverageDailyVolume",
            look_back_period="20d",
            time_series_col="Date",
            instrument_ric="ERICb.ST",
            start_date=pd.Timestamp("2021-01-04"),
            end_date=pd.Timestamp("2023-07-05"),
        )

        assert data.shape == (632, 20)
        assert "marketAverageDailyVolume" in data.columns

    def test_get_avg_daily_trading_volume_empty_market_data_stats(self):
        client = MarketClient()

        client.get_market_data_stats = MagicMock()
        client.get_market_data_stats.return_value = pd.DataFrame()

        data = client.get_avg_daily_trading_volume(
            adtv_column="marketAverageDailyVolume",
            look_back_period="20d",
            time_series_col="Date",
            instrument_ric="ERICb.ST",
            start_date=pd.Timestamp("2021-01-04"),
            end_date=pd.Timestamp("2023-07-05"),
        )

        assert data.empty

    def test_calculate_average_daily_trading_volume_empty(self):
        data = pd.read_csv(
            TEST_DATA.joinpath("market_data_stats.csv"),
            parse_dates=["Date"],
            date_parser=custom_date_parser(),
        )
        data.loc[:, "Trade Volume"] = np.nan

        client = MarketClient()
        adtv = client.calculate_average_daily_trading_volume(
            market_data=data,
            data_column="Trade Volume",
            look_back_period="20d",
            time_series="Date",
        )

        assert adtv.empty

    def test_fetch_level_of_order_book(self, helpers):
        """Check level of order book."""
        file = Path(__file__).parent / "test_data" / "ULVR.L__QUOTE_LEVEL2__TICK__20210824.parquet"

        obd_data = pd.read_parquet(file)
        tenant_data = pd.read_csv(TEST_DATA.joinpath("tenant_data.csv"), index_col=0)

        data_wt_level_obook: pd.DataFrame = MarketClient().select_level_of_order_book(
            order_book_data=obd_data,
            tenant_data=tenant_data,
            level_of_order_book_col="levelOfOrderBook",
        )

        level_of_order_book = data_wt_level_obook.loc[:, "levelOfOrderBook"].tolist()

        assert len(level_of_order_book) == 3
        assert level_of_order_book == [2, 2, 3]

    def test_fetch_level_of_order_book_no_limit_price(self, helpers):
        """Check level of order book."""
        file = Path(__file__).parent / "test_data" / "ULVR.L__QUOTE_LEVEL2__TICK__20210824.parquet"

        obd_data = pd.read_parquet(file)
        tenant_data = pd.read_csv(TEST_DATA.joinpath("tenant_data_no_limit_price.csv"), index_col=0)

        data_wt_level_obook: pd.DataFrame = MarketClient().select_level_of_order_book(
            order_book_data=obd_data,
            tenant_data=tenant_data,
            level_of_order_book_col="levelOfOrderBook",
        )

        level_of_order_book = data_wt_level_obook.loc[:, "levelOfOrderBook"].tolist()

        assert len(level_of_order_book) == 3
        assert level_of_order_book == [None, None, None]

    def test_volume_at_level_of_order_book(self, helpers):
        """Check level of order book."""
        file = Path(__file__).parent / "test_data" / "ULVR.L__QUOTE_LEVEL2__TICK__20210824.parquet"

        order_book_depth_data = pd.read_parquet(file)
        data: pd.DataFrame = pd.read_csv(TEST_DATA.joinpath("tenant_data_w_level.csv"), index_col=0)

        data.loc[:, "volumeAtLevel"] = data.apply(
            lambda row: get_volume_at_level(
                market_data=order_book_depth_data,
                order_time_submitted=row.loc[OrderField.TS_ORD_SUBMITTED],
                buy_sell=row.loc[OrderField.EXC_DTL_BUY_SELL_IND],
                level_of_order_book=row.loc["levelOfOrderBook"],
            ),
            axis=1,
        )

        volume_at_level = data.loc[:, "volumeAtLevel"].tolist()

        assert len(volume_at_level) == 3
        assert volume_at_level == [270.0, 98.0, 375.0]

    def test_fetch_level_of_order_book_by_ric(self, monkeypatch, helpers):
        """Check level of order book, using a given RIC.

        Since we need to fetch order book depth data
        """

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "get_tick_parquet",
            read_parquet_batch,
        )

        client = MarketClient()

        order_book_depth_data = client.get_order_book_depth_data(
            ric="ULVR.L",
            start_date=pd.Timestamp("2021-08-24 00:00:00"),
            end_date=pd.Timestamp("2021-08-24 23:59:59"),
        )

        assert not order_book_depth_data.empty

        tenant_data = pd.read_csv(TEST_DATA.joinpath("tenant_data.csv"), index_col=0)

        data_wt_level_obook = client.select_level_of_order_book(
            order_book_data=order_book_depth_data,
            tenant_data=tenant_data,
            level_of_order_book_col="levelOfOrderBook",
        )

        level_of_order_book = data_wt_level_obook.loc[:, "levelOfOrderBook"].tolist()

        assert len(level_of_order_book) != 0

    def test_fetch_level_of_order_book_by_instrument_venue(self, monkeypatch, helpers):
        """Check level of order book, using a given RIC.

        Since we need to fetch order book depth data
        """
        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "get_tick_parquet",
            read_parquet_batch,
        )

        instrument_details = pd.read_csv(TEST_DATA.joinpath("instrument_tests.csv"), index_col=0)

        client = MarketClient()

        obtained_level = []
        obtained_side = []

        for ind, row in instrument_details.iterrows():
            data = pd.DataFrame(row).transpose()

            list_of_isin = get_list_instrument_unique_identifier(data=data, venue_column="venue")

            rics = client.get_ric_map(list_inst_unique_id=list_of_isin)

            data["Date-Time"] = data["Date"].astype("str") + " " + data["Time"].astype("str")

            for ric in rics:
                start_date = pd.to_datetime(data["Date"]).iloc[0]

                end_date = start_date + pd.DateOffset(1) - datetime.timedelta(seconds=1)

                order_book_depth_data = client.get_order_book_depth_data(
                    ric=ric,
                    start_date=start_date,
                    end_date=end_date,
                )

                assert not order_book_depth_data.empty

                tenant_data = data[
                    [
                        OrderField.EXC_DTL_BUY_SELL_IND,
                        OrderField.EXC_DTL_LIMIT_PRICE,
                        OrderField.EXC_DTL_ORD_TYPE,
                        "Date-Time",
                    ]
                ]

                tenant_data[OrderField.TS_ORD_SUBMITTED] = data["Date-Time"].apply(
                    lambda row: pd.to_datetime(row).astimezone(None)
                )

                data_wt_level_obook = client.select_level_of_order_book(
                    order_book_data=order_book_depth_data,
                    tenant_data=tenant_data,
                    level_of_order_book_col="levelOfOrderBook",
                )

                data_wt_level_obook.loc[
                    data_wt_level_obook.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY,
                    "Side",
                ] = "Bid"

                data_wt_level_obook.loc[
                    data_wt_level_obook.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL,
                    "Side",
                ] = "Ask"

                level_of_order_book = data_wt_level_obook.loc[:, "levelOfOrderBook"].tolist()

                side_of_order = data_wt_level_obook.loc[:, "Side"].tolist()

                obtained_level.append(level_of_order_book[0])
                obtained_side.append(side_of_order[0])

                assert len(level_of_order_book) != 0
                assert len(side_of_order) != 0

        instrument_details["Obtained Side"] = obtained_side
        instrument_details["Obtained Level"] = obtained_level

        assert instrument_details["Obtained Side"].equals(instrument_details["Expected Side"])
        assert instrument_details["Obtained Level"].equals(instrument_details["Expected Level"])

    def test_get_eod_stats(self, monkeypatch):
        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "fetch_eod_stats_with_time_margin",
            read_eod_stats,
        )

        eod_data = MarketClient().get_market_data_stats(
            instrument_ric="VOD.L",
            start_date=pd.Timestamp("2021-08-24"),
            end_date=pd.Timestamp("2021-08-24"),
        )
        assert not eod_data.empty

    @pytest.mark.parametrize(
        "result_between_dates,nearest_timestamp",
        [(False, False), (True, False), (False, True)],
    )
    def test_get_tick_data(self, monkeypatch, result_between_dates, nearest_timestamp):
        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.utils,
            "get_tick_parquet",
            read_parquet_batch,
        )

        client = MarketClient()

        dates = pd.Series(
            [pd.Timestamp("2022-08-19 15:29:35"), pd.Timestamp("2022-08-19 15:29:45")]
        )

        data = client.get_tick_data(
            event_type=RefinitivEventType.QUOTE,
            instrument_ric="BABA.N",
            dates=dates,
            result_between_dates=result_between_dates,
            nearest_timestamp=nearest_timestamp,
        )

        if result_between_dates:
            assert data.shape == (116, 11)
            first_row = data.iloc[0]
            assert first_row["Date-Time"] == 1660922975236556036
            assert first_row["Mid Price"] == 89.4
            assert first_row["Bid Size"] == 1
        elif nearest_timestamp:
            assert data.shape == (2, 11)
            first_row = data.iloc[0]
            assert first_row["Date-Time"] == pd.Timestamp("2022-08-19 15:29:34.491829009")
            assert first_row["Mid Price"] == 89.39
            assert first_row["Ask Size"] == 1
        else:
            assert data.shape == (62431, 11)

    @pytest.mark.parametrize(
        "start_date,end_date,empty",
        [
            ("2021-08-19 15:29:35", "2021-12-19 15:29:45", True),
            ("2021-08-19 15:29:35", "2022-01-19 15:29:45", True),
            ("2021-08-19 15:29:35", "2022-08-19 15:29:45", False),
        ],
    )
    def test_get_tick_data_quote_date_limit(self, monkeypatch, start_date, end_date, empty):
        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.utils,
            "get_tick_parquet",
            read_parquet_batch,
        )

        client = MarketClient()

        dates = pd.Series([pd.Timestamp(start_date), pd.Timestamp(end_date)])

        data = client.get_tick_data(
            event_type=RefinitivEventType.QUOTE, instrument_ric="BABA.N", dates=dates
        )

        assert data.empty == empty
