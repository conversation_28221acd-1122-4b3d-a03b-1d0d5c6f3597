import numpy as np
import pandas as pd


def test_previous_alerts():
    min_order = 1

    df = pd.DataFrame(
        [
            {
                "&key": "Order:1",
                "timestamps.orderSubmitted": "2020-02-15T14:00:04.000Z",
                "smallOrders": ["Order:X", "Order:XX"],
            },
            {
                "&key": "Order:2",
                "timestamps.orderSubmitted": "2020-02-15T14:00:07.000Z",
                "smallOrders": ["Order:1"],
            },
            {
                "&key": "Order:3",
                "timestamps.orderSubmitted": "2020-02-15T14:10:12.000Z",
                "smallOrders": ["Order:1", "Order:2"],
            },
            {
                "&key": "Order:4",
                "timestamps.orderSubmitted": "2020-02-15T14:10:13.000Z",
                "smallOrders": ["Order:2", "Oder:3"],
            },
            {
                "&key": "Order:6",
                "timestamps.orderSubmitted": "2020-02-15T14:10:14.000Z",
                "smallOrders": ["Order:5"],
            },
        ]
    )

    df["smallOrdersSet"] = df["smallOrders"].apply(set)

    df["commonAlerts"] = df[["&key", "timestamps.orderSubmitted", "smallOrdersSet"]].apply(
        lambda x: df[["&key", "timestamps.orderSubmitted", "smallOrdersSet"]]
        .loc[
            (df["&key"] != x["&key"])
            & (df["timestamps.orderSubmitted"] <= x["timestamps.orderSubmitted"])
        ]
        .apply(
            lambda y: y["&key"]
            if len(y["smallOrdersSet"].intersection(x["smallOrdersSet"])) >= min_order
            else np.nan,
            axis=1,
        ),
        axis=1,
    )

    df["commonAlerts"] = df["commonAlerts"].apply(
        lambda x: x.dropna().tolist() if not (x.empty or x.dropna().empty) else np.nan
    )

    # df["commonAlerts"]
