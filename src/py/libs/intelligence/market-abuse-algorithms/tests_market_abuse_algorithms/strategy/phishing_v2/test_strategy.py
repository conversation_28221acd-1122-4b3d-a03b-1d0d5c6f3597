import pandas as pd
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.phishing_v2.static import DFColumns
from market_abuse_algorithms.strategy.phishing_v2.strategy import Strategy
from pathlib import Path
from unittest.mock import MagicMock

# TODO: make sure this tests run without the skip_ci fixture
# TODO: Add valid test cases here

TEST_DATA = Path(__file__).parent.joinpath("test_data")


class TestPhishingV2:
    def test_merge_alerts(self, helpers):
        thresholds = dict(
            assetClass="",
            timeWindow={"value": 400, "unit": "minutes"},
            percentageQuantityOfLargeOrder=0.5,
            mergedAlerts=True,
            minNumberOfSmallOrders=1,
            numberOfCounterpartiesInvolved="multiple",
            ordersInOppositeDirection=False,
        )

        input_df = pd.read_json(
            '{"&key":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374"],"Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374"],"Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":["Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399"],"Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":["Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399"]},"instrumentDetails.instrument.instrumentIdCode":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"CH0023405456","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":"CH0023405456","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":"CH0023405456","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":"CH0023405456"},"smallOrderQuantity":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":1500.0,"Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":60.0,"Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":2250.0,"Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":40.0},"smallOrders":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_001CH0023405456:1586275426080","Order:1:NEWO:PHISHING2_002CH0023405456:1586275342374"],"Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_004CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_001CH0023405456:1586275426080","Order:1:NEWO:PHISHING2_005CH0023405456:1586275342374"],"Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":["Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399","Order:1:NEWO:PHISHING2_005CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:2:NEWO:PHISHING2_008CH0023405456:1586275188399","Order:1:NEWO:PHISHING2_001CH0023405456:1586275426080","Order:1:NEWO:PHISHING2_002CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_004CH0023405456:1586275342374"],"Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":["Order:1:NEWO:PHISHING2_001CH0023405456:1586275426080","Order:1:NEWO:PHISHING2_005CH0023405456:1586275342374"]},"instrumentDetails.instrument.ext.bestExAssetClassMain":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"Equity","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":"Equity","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":"Equity","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":"Equity"},"instrumentDetails.instrument.instrumentFullName":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"Dufry AG","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":"Dufry AG","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":"Dufry AG","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":"Dufry AG"},"numberOfSmallOrders":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":2,"Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":3,"Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":7,"Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":2},"mergedGroups":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399"],"Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399"],"Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399":["Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399"],"Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399":["Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399"]}}'  # noqa: E501
        )
        expected_df = pd.read_json(
            '{"&key":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374"]},"instrumentDetails.instrument.instrumentIdCode":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"CH0023405456"},"smallOrderQuantity":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":2250.0},"smallOrders":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_004CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_002CH0023405456:1586275342374","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399","Order:2:NEWO:PHISHING2_008CH0023405456:1586275188399","Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_005CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_001CH0023405456:1586275426080"]},"instrumentDetails.instrument.ext.bestExAssetClassMain":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"Equity"},"instrumentDetails.instrument.instrumentFullName":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":"Dufry AG"},"numberOfSmallOrders":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":7},"mergedGroups":{"Order:1:NEWO:PHISHING2_003CH0023405456:1586275342374":["Order:1:NEWO:PHISHING2_006CH0023405456:1586275342374","Order:1:NEWO:PHISHING2_009CH0023405456:1586275188399","Order:2:NEWO:PHISHING2_007CH0023405456:1586275188399"]}}'  # noqa: E501
        )

        filters = {}
        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        result = strategy.merge_alerts(df=input_df)

        result[DFColumns.SMALL_ORDERS] = result[DFColumns.SMALL_ORDERS].apply(lambda x: sorted(x))
        result[OrderField.META_KEY] = result[OrderField.META_KEY].apply(lambda x: sorted(x))

        expected_df[DFColumns.SMALL_ORDERS] = expected_df[DFColumns.SMALL_ORDERS].apply(
            lambda x: sorted(x)
        )
        expected_df[OrderField.META_KEY] = expected_df[OrderField.META_KEY].apply(
            lambda x: sorted(x)
        )

        assert result.equals(expected_df)

    def test_get_order_query(self, helpers):
        thresholds = {
            "assetClass": "",
            "timeWindow": {"value": 400, "unit": "minutes"},
            "percentageQuantityOfLargeOrder": 0.5,
            "minNumberOfSmallOrders": 1,
        }
        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        query = strategy.queries._get_orders_query()

        assert len(query.to_dict()["query"]["bool"]["must_not"]) == 1
        assert query.to_dict()["query"]["bool"]["must_not"][0] == {"exists": {"field": "&expiry"}}

    def test_cases_to_analyse(self, helpers):
        thresholds = {
            "timeWindow": {"value": 20, "unit": "minutes"},
            "percentageQuantityOfLargeOrder": 0.2,
            "minNumberOfSmallOrders": 6,
        }
        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            ["BMG667211046", "CH0023405456"]
        ]

        scroll_data = pd.read_csv(
            TEST_DATA.joinpath("uc1.1_scroll_data_cases_to_analyse.csv"), sep=","
        )

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = scroll_data

        for data in strategy.queries.cases_to_analyse():
            assert data.shape == (18, 12)
