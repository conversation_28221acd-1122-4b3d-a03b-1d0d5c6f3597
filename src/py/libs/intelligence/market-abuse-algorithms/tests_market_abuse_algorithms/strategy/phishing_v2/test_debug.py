import pytest
from market_abuse_algorithms.strategy.phishing_v2.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerPhishingV2:
    def test_case_debug(self, helpers):
        thresholds = {
            "assetClass": "",
            "timeWindow": {"value": 400, "unit": "minutes"},
            "percentageQuantityOfLargeOrder": 0.5,
            "minNumberOfSmallOrders": 1,
        }

        filters = {
            "bool": {"must": [{"range": {"&timestamp": {"gte": "2022-02-23T12:51:36.483000Z"}}}]}
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        strategy
