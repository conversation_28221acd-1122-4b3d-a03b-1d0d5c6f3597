import pytest
from market_abuse_algorithms.strategy.trash_and_cash.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerTrashAndCash:
    def test_case_debug(self, helpers):
        thresholds = dict(assetClass="Equity", executionVsLowestMktPx24hPostXChange=0.10)
        filters = helpers.get_iris_filters(
            strategy_name="TrashAndCash",
            start_date=1543968000000,
            end_date=1544227199999,
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        scenarios = strategy.scenarios
        scenarios
