import pytest
from market_abuse_algorithms.strategy.phishing.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerPhishing:
    def test_case_debug(self, helpers):
        thresholds = dict(assetClass="Equity", buySellRatio=5, sellBuyRatio=5)

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1571356800000, "start": 1571270400000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",  # noqa: E501
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
