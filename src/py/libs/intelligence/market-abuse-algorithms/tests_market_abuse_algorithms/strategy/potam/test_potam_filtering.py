import pandas as pd
import pytest
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.potam.static import StrategyMetaField, ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from pathlib import Path
from tests_market_abuse_algorithms.strategy.potam.fakers import (
    mock_get_orders_to_analyse_geral,
    mock_get_potam_cases_geral,
    mock_non_potam_hits_matching_order_id,
    mock_potam_repository,
)
from unittest.mock import MagicMock


@pytest.fixture()
def mock_potam_isins(monkeypatch, *args, **kwargs):
    mock_potam_repository(monkeypatch)


market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotamFiltering:
    def test_filter_by_include_formal_sales_process_date_case_deleted(
        self, helpers, mock_potam_isins
    ):
        potam_cases_file = Path(__file__).parent / "test_data" / "potam_cases_no_date.csv"
        potam_cases = pd.read_csv(potam_cases_file, index_col=0)

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        filtered_potam_cases = strategy.filter_by_include_formal_sales_process(
            potam_cases=potam_cases
        )

        # It has the dateCaseDeleted
        assert not filtered_potam_cases.empty

    def test_filter_by_include_formal_sales_process_no_end_date(self, helpers):
        potam_cases_file = Path(__file__).parent / "test_data" / "potam_cases_no_end_date.csv"
        potam_cases = pd.read_csv(potam_cases_file, index_col=0)

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
            ThresholdsNames.INCLUDE_FORMAL_SALES_PROCESS: False,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        filtered_potam_cases = strategy.filter_by_include_formal_sales_process(
            potam_cases=potam_cases
        )
        assert filtered_potam_cases.empty

    def test_filter_by_include_formal_sales_process(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()  # noqa: E501
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (  # noqa: E501
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()  # noqa: E501

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
            ThresholdsNames.INCLUDE_FORMAL_SALES_PROCESS: True,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        filtered_potam_cases = strategy.filter_by_include_formal_sales_process(
            potam_cases=potam_cases
        )
        assert not filtered_potam_cases.empty
        assert len(filtered_potam_cases) == 70
        singleton_audit_object.delete_local_audit_files()
