import json
import market_abuse_algorithms.strategy.potam.strategy
import pandas as pd
from datetime import datetime
from market_abuse_algorithms.strategy.potam.query import PotamQuery
from pathlib import Path


def custom_date_parser():
    return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")


def custom_off_parser():
    return lambda x: eval(x) if not isinstance(x, float) else x


def datetime_parser():
    return lambda x: pd.to_datetime(x)


def mock_get_orders_to_analyse_emtpy_potam_cases(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_orders.csv"

    data = pd.read_csv(
        file_path,
        index_col=0,
        parse_dates=["timestamps.tradingDateTime", "timestamps.orderSubmitted"],
        date_parser=custom_date_parser(),
    )
    return data


def mock_get_potam_cases_emtpy_potam_cases(**kwargs):
    return pd.DataFrame()


def mock_get_orders_to_analyse_key_error(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_orders.csv"

    data = pd.read_csv(
        file_path,
        index_col=0,
        parse_dates=["timestamps.tradingDateTime", "timestamps.orderSubmitted"],
        date_parser=custom_date_parser(),
    )
    return data


def mock_get_potam_cases_key_error(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "results.csv"
    data = pd.read_csv(file_path, index_col=0)
    return data


def mock_get_orders_to_analyse_empty_orders(**kwargs):
    return pd.DataFrame()


def mock_potam_repository(monkeypatch, *args, **kwargs):
    def mock_process_frame_result(self, potam_results, **kwargs):
        return potam_results

    def mock_list_isins(self, *args, **kwargs):
        file_path = Path(__file__).parent / "test_data" / "response.json"
        with open(file_path) as json_file:
            data = json.load(json_file)
            return data["hits"]

    monkeypatch.setattr(
        PotamQuery,
        "isins_from_potam_cases",
        mock_list_isins,
    )

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.potam.query.PotamQuery,
        "process_frame_result",
        mock_process_frame_result,
    )


def mock_get_potam_cases_explode_isins(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_cases_isin_list.csv"
    data = pd.read_csv(file_path, index_col=0)

    data["dateCaseDeleted"] = pd.to_datetime(data["dateCaseDeleted"])
    data["offerees.offerPeriodCommenced"] = pd.to_datetime(data["offerees.offerPeriodCommenced"])
    data["offerors.offerorIdentified"] = pd.to_datetime(data["offerors.offerorIdentified"])
    data["offerors"] = data["offerors"].apply(lambda x: eval(x))
    data["offerees"] = data["offerees"].apply(lambda x: eval(x))
    data["offerees.twotenInformation.isin"] = data["offerees.twotenInformation.isin"].apply(
        lambda x: eval(x)
    )
    data["offerors.twotenInformation.isin"] = data["offerors.twotenInformation.isin"].apply(
        lambda x: eval(x)
    )
    return data


def mock_get_potam_cases(**kwargs):
    return pd.DataFrame()


def mock_get_orders_to_analyse_geral(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_orders.csv"

    data: pd.DataFrame = pd.read_csv(
        file_path,
        index_col=0,
        parse_dates=["timestamps.tradingDateTime", "timestamps.orderSubmitted"],
        date_parser=custom_date_parser(),
    )
    return data


def mock_get_potam_cases_geral(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_cases.csv"
    data = pd.read_csv(file_path, index_col=0)

    data["dateCaseDeleted"] = pd.to_datetime(data["dateCaseDeleted"])
    data["offerees.offerPeriodCommenced"] = pd.to_datetime(data["offerees.offerPeriodCommenced"])
    data["offerors.offerorIdentified"] = pd.to_datetime(data["offerors.offerorIdentified"])

    return data


def mock_non_potam_hits_matching_order_id(*args, **kwargs):
    return pd.DataFrame()


def mock_get_orders_to_analyse_geral_spi_860(**kwargs):
    file_path = Path(__file__).parent / "test_data" / "potam_spi_860_orders.csv"

    data = pd.read_csv(
        file_path,
        index_col=0,
        parse_dates=[
            "timestamps.tradingDateTime",
            "timestamps.orderSubmitted",
            "timestamps.orderReceived",
            "timestamps.orderStatusUpdated",
        ],
        date_format="%Y-%m-%d %H:%M:%S",
    )
    return data
