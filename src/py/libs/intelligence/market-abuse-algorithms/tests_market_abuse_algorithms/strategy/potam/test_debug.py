import logging
import pandas as pd
import pytest
from market_abuse_algorithms.strategy.potam.static import ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from pandas._libs.tslibs.offsets import BDay

logger = logging.getLogger(__name__)


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerPotam:
    def test_cased_debug(self, helpers):
        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 0,
            "runType": "inside",
            "includeFormalSalesProcess": False,
        }

        filters = {
            "bool": {
                "filter": [{"range": {"&timestamp": {"gte": "2024-08-15T08:01:14.256000+00:00"}}}]
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        strategy.run()

        scenarios = strategy.scenarios
        assert len(scenarios) > 0

    def test_debug_potam_algorithm_get_potam_window_end(self, helpers):
        """Run in benjamin.uat."""
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }
        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        strategy.run()

        scenarios = strategy.scenarios
        logger.info("\n")
        for sce in scenarios:
            top_level = sce._scenario.additionalFields.get("topLevel")
            potam_window_after = top_level.get("potamWindowEnd")
            potam_window_before = top_level.get("potamWindowStart")
            ts_order_submited = top_level.get("orderSubmittedTSLocal")

            potam_window_before_extended = potam_window_before - pd._libs.tslibs.offsets.BDay(
                thresholds.get(ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE)
            )
            if pd.isna(potam_window_after):
                potam_window_after_extended = pd.NA
                inside_potam_window = ts_order_submited >= potam_window_before
                outside_potam_window = (
                    potam_window_before_extended <= ts_order_submited <= potam_window_before
                )
            else:
                potam_window_after_extended = top_level.get("potamWindowEnd") + BDay(
                    thresholds.get(ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER)
                )
                inside_potam_window = potam_window_before <= ts_order_submited <= potam_window_after  # noqa: E501
                outside_potam_window = (
                    potam_window_before_extended <= ts_order_submited <= potam_window_before
                ) or (potam_window_after <= ts_order_submited <= potam_window_after_extended)

            logger.info(
                f"Instrument: {top_level.get('instrumentId')} || "
                f"Order TimeSubmitted: {top_level.get('orderSubmittedTSLocal')} ||"
                f"POTAM Window Start: {potam_window_before} ||  "
                f"POTAM Window END: {potam_window_after}  ||"
                f"POTAM Window Start (extended window before) : {potam_window_before_extended} ||"
                f"POTAM Window Start (extended window after) : {potam_window_after_extended} ||"
                f"Inside POTAM Window: {inside_potam_window}||"
                f"Outside POTAM Window: {outside_potam_window}||"
            )
