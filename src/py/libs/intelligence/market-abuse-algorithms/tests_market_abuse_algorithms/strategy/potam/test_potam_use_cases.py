# ruff: noqa: E501
import logging
import pandas as pd
from market_abuse_algorithms.strategy.base.strategy import market_abuse_audit_object
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from pathlib import Path
from tests_market_abuse_algorithms.strategy.potam.fakers import datetime_parser

market_abuse_audit_object.records_analysed = 0

logger = logging.getLogger(__name__)


class TestPotamUseCases:
    def test_potam_7_1_csv(self, helpers):
        """Tests the output for the Test Case 7.1 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 5 Orders, all Orders have full execution.

            Order ID : potam_7_1, Order Date: 20200311
            Order ID : potam_7_2, Order Date: 20200611
            Order ID : potam_7_3, Order Date: 20200101
            Order ID : potam_7_4, Order Date: 20200615
            Order ID : potam_7_5, Order Date: 20200715

        Expected Output:
            3 hits.
            potam_7_1 : InPotamWindow
            potam_7_2 : InExtendedWindow
            potam_7_4 : InExtendedWindow
        """

        thresholds = {"extendedWindowDaysBefore": 10, "extendedWindowDaysAfter": 10}
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.7.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "7.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleyeBlotter.mar.potam.7.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert len(hits["META_HIT_TYPE"]) == 6
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][3] == "InExtendedWindow"

    def test_potam_7_2_csv(self, helpers):
        """Tests the output for the Test Case 7.2 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 5 Orders, all Orders have full execution.

            Order ID : potam_7_1, Order Date: 20200311
            Order ID : potam_7_2, Order Date: 20200611
            Order ID : potam_7_3, Order Date: 20200101
            Order ID : potam_7_4, Order Date: 20200615
            Order ID : potam_7_5, Order Date: 20200715

        Expected Output:
            5 hits.
            potam_7_1 : InPotamWindow
            potam_7_2 : InExtendedWindow
            potam_7_3 : InExtendedWindow
            potam_7_4 : InExtendedWindow
            potam_7_5 : InExtendedWindow
        """

        thresholds = {"extendedWindowDaysBefore": 30, "extendedWindowDaysAfter": 30}
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.7.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "7.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleyeBlotter.mar.potam.7.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert len(hits["META_HIT_TYPE"]) == 10
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][3] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][4] == "InExtendedWindow"

    def test_potam_7_4_csv(self, helpers):
        """Tests the output for the Test Case 7.4 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 5 Orders, all Orders have full execution.

            Order ID : potam_7_1, Order Date: 20200311
            Order ID : potam_7_2, Order Date: 20200611
            Order ID : potam_7_3, Order Date: 20200101
            Order ID : potam_7_4, Order Date: 20200615
            Order ID : potam_7_5, Order Date: 20200715

        Expected Output:
            1 hits.
            potam_7_1 : InPotamWindow
        """

        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 30,
            "runType": "inside",
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.7.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "7.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleyeBlotter.mar.potam.7.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert len(hits["META_HIT_TYPE"]) == 2
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"

    def test_potam_7_5_csv(self, helpers):
        """Tests the output for the Test Case 7.5 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 5 Orders, all Orders have full execution.

            Order ID : potam_7_1, Order Date: 20200311
            Order ID : potam_7_2, Order Date: 20200611
            Order ID : potam_7_3, Order Date: 20200101
            Order ID : potam_7_4, Order Date: 20200615
            Order ID : potam_7_5, Order Date: 20200715

        Expected Output:
            4 hits.
            potam_7_2 : InExtendedWindow
            potam_7_3 : InExtendedWindow
            potam_7_4 : InExtendedWindow
            potam_7_5 : InExtendedWindow
        """

        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 30,
            "runType": "outside",
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.7.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "7.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleyeBlotter.mar.potam.7.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert len(hits["META_HIT_TYPE"]) == 8
        assert hits["META_HIT_TYPE"][1] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][3] == "InExtendedWindow"
        assert hits["META_HIT_TYPE"][4] == "InExtendedWindow"

    def test_potam_2_1_csv(self, helpers):
        """Tests the output for the Test Case 2.1 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 4 Orders, 2 Partially filled

            Order ID : potam_2_1, Order Date: 20210311
            Order ID : potam_2_2, Order Date: 20210329
            Order ID : potam_2_3, Order Date: 20210331
            Order ID : potam_2_4, Order Date: 20220219

        Expected Output:
            3 hits.
            potam_2_1 : InPotamWindow
            potam_2_2 : InPotamWindow
            potam_2_3 : InExtendedWindow
        """

        thresholds = {"extendedWindowDaysBefore": 10, "extendedWindowDaysAfter": 10}
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.2.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "2.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleye.Blotter.mar.potam.2.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)
        logger.info(hits)
        assert len(hits["META_HIT_TYPE"]) == 4
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"

    def test_potam_2_2_csv(self, helpers):
        """Tests the output for the Test Case 2.2 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 4 Orders, 2 Partially filled

            Order ID : potam_2_1, Order Date: 20210311
            Order ID : potam_2_2, Order Date: 20210329
            Order ID : potam_2_3, Order Date: 20210331
            Order ID : potam_2_4, Order Date: 20220219

        Expected Output:
            3 hits.
            potam_2_1 : InPotamWindow
            potam_2_2 : InPotamWindow
            potam_2_3 : InExtendedWindow
        """

        thresholds = {"extendedWindowDaysBefore": 30, "extendedWindowDaysAfter": 30}
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.2.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "2.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleye.Blotter.mar.potam.2.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)
        logger.info(hits)
        assert len(hits["META_HIT_TYPE"]) == 4
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"

    def test_potam_2_3_csv(self, helpers):
        """Tests the output for the Test Case 2.3 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 4 Orders, 2 Partially filled

            Order ID : potam_2_1, Order Date: 20210311
            Order ID : potam_2_2, Order Date: 20210329
            Order ID : potam_2_3, Order Date: 20210331
            Order ID : potam_2_4, Order Date: 20220219

        Expected Output:
            3 hits.
            potam_2_1 : InPotamWindow
            potam_2_2 : InPotamWindow
            potam_2_3 : InExtendedWindow
        """

        thresholds = {"extendedWindowDaysBefore": 60, "extendedWindowDaysAfter": 60}
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.2.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "2.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleye.Blotter.mar.potam.2.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)
        logger.info(hits)
        assert len(hits["META_HIT_TYPE"]) == 4
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"

    def test_potam_2_4_csv(self, helpers):
        """Tests the output for the Test Case 2.4 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 4 Orders, 2 Partially filled

            Order ID : potam_2_1, Order Date: 20210311
            Order ID : potam_2_2, Order Date: 20210329
            Order ID : potam_2_3, Order Date: 20210331
            Order ID : potam_2_4, Order Date: 20220219

        Expected Output:
            2 hits.
            potam_2_1 : InPotamWindow
            potam_2_2 : InPotamWindow
        """

        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 30,
            "runType": "inside",
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.2.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "2.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleye.Blotter.mar.potam.2.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)
        logger.info(hits)
        assert len(hits["META_HIT_TYPE"]) == 3
        assert hits["META_HIT_TYPE"][0] == "InPotamWindow"
        assert hits["META_HIT_TYPE"][1] == "InPotamWindow"

    def test_potam_2_5_csv(self, helpers):
        """Tests the output for the Test Case 2.5 as mentioned in POTAM
        confluence page.

        File Description:
            Contains 4 Orders, 2 Partially filled

            Order ID : potam_2_1, Order Date: 20210311
            Order ID : potam_2_2, Order Date: 20210329
            Order ID : potam_2_3, Order Date: 20210331
            Order ID : potam_2_4, Order Date: 20220219

        Expected Output:
            1 hits.
            potam_2_3 : InExtendedWindow
        """

        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 30,
            "runType": "outside",
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.potam.2.csv"
                        ]
                    }
                }
            }
        }

        potam_cases_file = Path(__file__).parent / "test_data" / "2.cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "steeleye.Blotter.mar.potam.2.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)
        logger.info(hits)
        assert len(hits["META_HIT_TYPE"]) == 1
        assert hits["META_HIT_TYPE"][2] == "InExtendedWindow"
