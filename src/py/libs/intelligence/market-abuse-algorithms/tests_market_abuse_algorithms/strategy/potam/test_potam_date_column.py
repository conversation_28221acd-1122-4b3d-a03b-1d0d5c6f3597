import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.strategy import market_abuse_audit_object
from market_abuse_algorithms.strategy.potam.static import StrategyMetaField, ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from pathlib import Path

market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotamDateColumn:
    def test_date_column_only_order_submitted_present(self, helpers):
        orders_file = (
            Path(__file__).parent
            / "test_data"
            / "test_date_column_only_order_submitted_present.csv"
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = pd.read_csv(orders_file)
        orders = strategy.add_date_column_to_orders(orders)
        assert set(orders[OrderField.TS_ORD_SUBMITTED]) == set(
            orders[StrategyMetaField.DATE_COLUMN]
        )

    def test_date_column_order_recieved_present(self, helpers):
        orders_file = (
            Path(__file__).parent / "test_data" / "test_date_column_order_recieved_present.csv"
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = pd.read_csv(orders_file)
        orders = strategy.add_date_column_to_orders(orders)
        assert set(orders[OrderField.TS_ORD_RECEIVED]) == set(orders[StrategyMetaField.DATE_COLUMN])  # noqa: E501

    def test_date_column_order_updated_present(self, helpers):
        orders_file = (
            Path(__file__).parent / "test_data" / "test_date_column_order_updated_present.csv"
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = pd.read_csv(orders_file)
        orders = strategy.add_date_column_to_orders(orders)
        assert set(orders[OrderField.TS_ORD_UPDATED]) == set(orders[StrategyMetaField.DATE_COLUMN])

    def test_date_column_trading_dt_present(self, helpers):
        orders_file = (
            Path(__file__).parent / "test_data" / "test_date_column_trading_dt_present.csv"
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = pd.read_csv(
            orders_file,
            index_col=0,
        )
        orders = strategy.add_date_column_to_orders(orders)
        assert set(orders[OrderField.TS_TRADING_DATE_TIME].fillna(pd.NA)) == set(
            orders[StrategyMetaField.DATE_COLUMN]
        )

    def test_date_column_no_date_present(self, helpers):
        orders_file = Path(__file__).parent / "test_data" / "test_date_column_no_date_present.csv"
        orders = pd.read_csv(
            orders_file,
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)
        orders = strategy.add_date_column_to_orders(orders)
        assert orders.empty
