# ruff: noqa: E501
import pandas as pd
import unittest
from market_abuse_algorithms.data_source.query.sdp.order import OrderBaseQuery
from market_abuse_algorithms.data_source.static.sdp.order import Model
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.potam.scenario import Scenario
from market_abuse_algorithms.strategy.potam.static import RunTypeMode, ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from pathlib import Path
from tests_market_abuse_algorithms.strategy.potam.fakers import (
    datetime_parser,
    mock_get_orders_to_analyse_geral,
    mock_get_potam_cases_geral,
    mock_non_potam_hits_matching_order_id,
)
from unittest.mock import MagicMock

market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotam:
    def test_model_index_in_query(self, helpers):
        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 0,
            "runType": "outside",
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = pd.DataFrame()

        hits = pd.read_csv(Path(__file__).parent / "test_data" / "test_model_index_in_query.csv")
        orders_non_hits = strategy.queries.non_potam_hits_matching_order_id(hits)
        assert orders_non_hits.empty

    def test_order_base_query_model_index(self, helpers):
        query = OrderBaseQuery(model=[Model.ORDER, Model.ORDER_STATE])
        assert query.MODEL_INDEX == "None-order-alias"

        thresholds = {
            "extendedWindowDaysBefore": 30,
            "extendedWindowDaysAfter": 0,
            "runType": "outside",
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        query.MODEL_INDEX = ""
        with unittest.TestCase.assertRaises(self, TypeError):
            strategy.queries.sdp_repository._get_index(query)

    def test_exclude_fsp(self, helpers):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        potam_cases_file = Path(__file__).parent / "test_data" / "test_exclude_fsp_potam_cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "test_exclude_fsp_orders.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "META_ORD_TS_FIELD",
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        assert len(potam_cases) == 6

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
            ThresholdsNames.INCLUDE_FORMAL_SALES_PROCESS: False,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        filtered_potam_cases = strategy.filter_by_include_formal_sales_process(
            potam_cases=potam_cases
        )

        assert len(filtered_potam_cases) == 5

        end_date_col_mask = (
            filtered_potam_cases["offerors.offerorIdentified"].notnull()
            | filtered_potam_cases["dateCaseDeleted"].notnull()
        )
        assert all(end_date_col_mask)

        strategy.add_date_column_to_orders(orders)
        strategy._run_algo(potam_cases=potam_cases, orders=orders)
        scenarios = strategy.scenarios
        assert len(scenarios) == 9

    def test_include_fsp(self, helpers):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        potam_cases_file = Path(__file__).parent / "test_data" / "test_exclude_fsp_potam_cases.csv"
        orders_file = Path(__file__).parent / "test_data" / "test_exclude_fsp_orders.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "META_ORD_TS_FIELD",
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )

        assert len(potam_cases) == 6

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
            ThresholdsNames.INCLUDE_FORMAL_SALES_PROCESS: True,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        filtered_potam_cases = strategy.filter_by_include_formal_sales_process(
            potam_cases=potam_cases
        )
        assert len(filtered_potam_cases) == 6

        end_date_col_mask = (
            filtered_potam_cases["offerors.offerorIdentified"].notnull()
            | filtered_potam_cases["dateCaseDeleted"].notnull()
        )

        assert not end_date_col_mask.all()

        orders = strategy.add_date_column_to_orders(orders)
        strategy._run_algo(potam_cases=potam_cases, orders=orders)
        scenarios = strategy.scenarios
        assert len(scenarios) == 9

        scenarios.pop(5)

        end_dates = []
        for alert in scenarios:
            end_date = (
                alert.json.get("additionalFields", {}).get("topLevel", {}).get("potamWindowEnd")
            )
            end_dates.append(end_date)

        end_dates.append(None)
        assert None in end_dates

    def test_run_mode_outside(self, helpers):
        potam_cases_file = (
            Path(__file__).parent / "test_data" / "test_run_mode_outside_potam_cases.csv"
        )
        orders_file = Path(__file__).parent / "test_data" / "test_run_mode_outside_orders.csv"

        potam_cases = pd.read_csv(
            potam_cases_file,
            index_col=0,
            parse_dates=[
                "offerors.offerorIdentified",
                "offerees.offerPeriodCommenced",
                "dateCaseDeleted",
            ],
            date_parser=datetime_parser(),
        )

        orders = pd.read_csv(
            orders_file,
            index_col=0,
            parse_dates=[
                "META_ORD_TS_FIELD",
                "timestamps.tradingDateTime",
                "timestamps.orderSubmitted",
            ],
            date_parser=datetime_parser(),
        )
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 6,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 6,
            ThresholdsNames.RUN_TYPE: RunTypeMode.OUTSIDE,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(potam_cases=potam_cases, orders=orders)

        assert len(hits) == 4

    def test_orders_to_analyse(self, helpers):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        isins = ["GB00B6ZM0X53", "GB00BYWKC989"]
        orders = strategy.queries.orders_to_analyse(isins=isins)
        assert not orders.empty
        assert orders.shape[0] == 3
        singleton_audit_object.delete_local_audit_files()

    def test_get_additional_fields(self, helpers):
        orders_file = Path(__file__).parent / "test_data" / "orders_additional_fields.csv"
        orders = pd.read_csv(orders_file, index_col=0)

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        scenario = Scenario(result=orders, context=strategy.context)

        additional_fields = scenario._get_additional_fields(potam_alert=orders)

        assert "topLevel" in additional_fields.keys()
        assert "potamWindowEnd" in additional_fields.get("topLevel").keys()
        assert not pd.isnull(additional_fields.get("topLevel").get("potamWindowEnd"))
