import pandas as pd
import pytest
from market_abuse_algorithms.data_source.static.srp.potam_case import PotamCaseField
from market_abuse_algorithms.strategy.base.strategy import market_abuse_audit_object
from market_abuse_algorithms.strategy.potam.query import _extract_offerors_offerees_data
from market_abuse_algorithms.strategy.potam.static import ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from tests_market_abuse_algorithms.strategy.potam.fakers import (
    mock_get_potam_cases_explode_isins,
    mock_potam_repository,
)
from unittest.mock import MagicMock


@pytest.fixture()
def mock_potam_isins(monkeypatch, *args, **kwargs):
    mock_potam_repository(monkeypatch)


market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotamIsins:
    def test_isins_from_potam_cases(self, helpers, mock_potam_isins):
        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        assert potam_isins is not None
        assert len(potam_isins) == 287

    def test_get_isins_1(self, helpers):
        data = [
            {"twotenInformation": [{"isin": "VGG0103J1075"}]},
            {"twotenInformation": [{"isin": "GB0030026057"}]},
        ]

        with pytest.raises(AttributeError):
            _extract_offerors_offerees_data(
                offerors_offerees_info=data, field_to_fetch=PotamCaseField.C_ISIN
            )

        data = [{"twotenInformation": [{"isin": "VGG0103J1075"}]}]
        with pytest.raises(AttributeError):
            _extract_offerors_offerees_data(
                offerors_offerees_info=data, field_to_fetch=PotamCaseField.C_ISIN
            )

        data = {"twotenInformation": [{"isin": "GB00BYSRJ698"}, {"isin": "US54336Q3020"}]}
        isins = _extract_offerors_offerees_data(
            offerors_offerees_info=data, field_to_fetch=PotamCaseField.C_ISIN
        )
        expected_result = ["GB00BYSRJ698", "US54336Q3020"]
        assert set(expected_result) == set(isins)

        data = {"twotenInformation": [{"isin": "GB00BF8H6F45"}]}
        isins = _extract_offerors_offerees_data(
            offerors_offerees_info=data, field_to_fetch=PotamCaseField.C_ISIN
        )
        expected_result = ["GB00BF8H6F45"]
        assert set(expected_result) == set(isins)

        offerors = []
        isins = _extract_offerors_offerees_data(
            offerors_offerees_info=offerors, field_to_fetch=PotamCaseField.C_ISIN
        )
        assert pd.isna(isins)

    def test_potam_cases_explode_isins(self, helpers, monkeypatch):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_explode_isins()
        )

        def mock_process_frame_result(self, potam_results, **kwargs):
            return potam_results

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.potam.query.PotamQuery,
            "process_frame_result",
            mock_process_frame_result,
        )

        isins_from_orders = ["GB00BLMQ9L68"]

        expected_dataframe = pd.DataFrame(
            {
                PotamCaseField.OFFREE_TTI_ISIN: [
                    ["JE00B5TT1872", "JE00B5TT1873"],
                    ["JE00B5TT1872", "JE00B5TT1873"],
                    ["JE00B5TT1872", "JE00B5TT1873"],
                ],
                PotamCaseField.OFFEROR_TTI_ISIN: [
                    ["KYG3040R1589"],
                    ["KYG3040R1589"],
                    ["KYG3040R1589"],
                ],
            }
        )

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        assert PotamCaseField.OFFREE_TTI_ISIN in potam_cases.columns
        assert PotamCaseField.OFFEROR_TTI_ISIN in potam_cases.columns

        offree_isin = potam_cases[PotamCaseField.OFFREE_TTI_ISIN]
        offeror_isin = potam_cases[PotamCaseField.OFFEROR_TTI_ISIN]

        assert all([isinstance(val, list) for val in offree_isin])
        assert all([isinstance(val, list) for val in offeror_isin])
        assert expected_dataframe.equals(
            potam_cases[[PotamCaseField.OFFREE_TTI_ISIN, PotamCaseField.OFFEROR_TTI_ISIN]]
        )
        assert len(potam_cases) == 3
