# ruff: noqa: E501
# type: ignore[assignment]
import pytest
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.potam.static import StrategyMetaField, ThresholdsNames
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from tests_market_abuse_algorithms.strategy.potam.fakers import (
    mock_get_orders_to_analyse_empty_orders,
    mock_get_orders_to_analyse_emtpy_potam_cases,
    mock_get_orders_to_analyse_geral,
    mock_get_orders_to_analyse_geral_spi_860,
    mock_get_orders_to_analyse_key_error,
    mock_get_potam_cases_emtpy_potam_cases,
    mock_get_potam_cases_geral,
    mock_get_potam_cases_key_error,
    mock_non_potam_hits_matching_order_id,
    mock_potam_repository,
)
from unittest.mock import MagicMock


@pytest.fixture()
def mock_potam_isins(monkeypatch, *args, **kwargs):
    mock_potam_repository(monkeypatch)


market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotamStrategy:
    def test_strategy_empty_order(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_empty_orders()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert orders.empty
        singleton_audit_object.delete_local_audit_files()

    def test_strategy_cases_empty(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_emtpy_potam_cases()

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_emtpy_potam_cases()
        )

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()

        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        assert potam_cases.empty
        singleton_audit_object.delete_local_audit_files()

    def test_strategy_key_error(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_key_error()

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_key_error()
        )

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        with pytest.raises(KeyError):
            strategy.queries.potam_cases_to_analyse(isins_from_orders)
        singleton_audit_object.delete_local_audit_files()

    def test_strategy_complete(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = (  # type: ignore[assignment]
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)
        orders = strategy.add_date_column_to_orders(orders)
        strategy._run_algo(orders=orders, potam_cases=potam_cases)
        singleton_audit_object.delete_local_audit_files()

    def test_spi_860_order_refactor(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral_spi_860()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = (  # type: ignore[assignment]
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)
        orders = strategy.add_date_column_to_orders(orders)
        strategy._run_algo(orders=orders, potam_cases=potam_cases)
        assert len(strategy.scenarios) == 1
        singleton_audit_object.delete_local_audit_files()
