# ruff: noqa: E501
import pytest
from market_abuse_algorithms.strategy.marking_the_open.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")

FILE_2 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.2.2.csv"
FILE_3 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.3.csv"
FILE_4 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.4.csv"
FILE_5 = [
    "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtc.ENG-6409.3.csv",
    "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtc.ENG-6409.client.trades.2.csv",
]


@pytest.mark.usefixtures("skip_test_in_ci")
class TestRealCasesMarkingTheOpen:
    def test_case_2_1(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 3600,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_2_2(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 5400,
            "market20DayAdv": 0.01,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0.01,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 2

    def test_case_2_3(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 4000,
            "market20DayAdv": 0.02,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0.02,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_2_4(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0.02,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0.02,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 3

    def test_case_2_5(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0.02,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0.025,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 3

    def test_case_2_6(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0.02,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0.03,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_2]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 0

    def test_case_3_1(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_3]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_4_1(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_4]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_5_1(self, helpers):  # TODO: data not in mar.uat
        thresholds = {
            "client20DayAdv": 0.19,
            "lookBackPeriod": 600,
            "market20DayAdv": 0.19,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": FILE_5}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_5_2(self, helpers):  # TODO: data not in mar.uat
        thresholds = {
            "client20DayAdv": 0.5,
            "lookBackPeriod": 600,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": FILE_5}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 0

    def test_case_5_3(self, helpers):  # TODO: data not in mar.uat
        thresholds = {
            "client20DayAdv": 0.479,
            "lookBackPeriod": 600,
            "market20DayAdv": 0.19,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": FILE_5}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_5_4(self, helpers):  # TODO: data not in mar.uat
        thresholds = {
            "client20DayAdv": 0.49,
            "lookBackPeriod": 600,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": FILE_5}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 0
