# ruff: noqa: E501
import market_abuse_algorithms.data_source.repository.market_data.utils
import market_abuse_algorithms.strategy.abstract_layering_v2.query
import market_abuse_algorithms.strategy.abstract_layering_v2.strategy
import pandas as pd
import pytest
from datetime import datetime
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.abstract_layering_v2.static import (
    EVALUATION_TYPE_GROUPING_MAP,
)
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.strategy.layering_v2.strategy import Strategy
from pathlib import Path
from se_elastic_schema.components.mar.strategy.layering_v2.thresholds import (
    BehaviourTypeEnum,
    EvaluationTypeEnum,
)
from tests_market_abuse_algorithms.strategy.layering_v2.mock_data import (
    FakeCrossProduct,
    FakeCrossProductWithQuery,
    fake_market_data_client,
    mock_layering_data,
)
from typing import List
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")

FILTER_1 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.layeringAndBookBalance.1.csv"
                ]
            }
        }
    }
}

FILTER_2 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.layeringAndBookBalance.2.csv"
                ]
            }
        }
    }
}

FILTER_3 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.layeringAndBookBalance.3.csv"
                ]
            }
        }
    }
}

FILTER_4 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.layeringAndBookBalance.4.csv"
                ]
            }
        }
    }
}


@pytest.fixture(scope="function")
def mock_data_sources(monkeypatch, *args, **kwargs):
    mock_layering_data(monkeypatch)


@pytest.fixture
def mock_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def custom_date_parser():
        return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")

    def _foo(*args):
        result = pd.read_csv(
            *args,
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderStatusUpdated", "timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )
        return result

    return _foo


class TestLayeringLevel2:
    def test_percentage_balance_formula(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """Check the percentage balance value is correct."""
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_count_orders.csv"))

        strategy._run_algo(data_to_analyse=test_data)
        scenarios = strategy.scenarios

        assert len(scenarios) == 1

        top_level: dict = strategy.scenarios[0].json.get("additionalFields").get("topLevel")
        percentage_balance = top_level.get("percentageOrdersBalance")

        assert percentage_balance
        assert percentage_balance[0] == 1.7695473251

    def test_level_of_order_book(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """Check if level of order book exists when there are volume at
        level."""

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_count_orders.csv"))

        strategy._run_algo(data_to_analyse=test_data)
        scenarios = strategy.scenarios

        if len(scenarios) > 0:
            for i in range(len(scenarios)):
                top_level: dict = strategy.scenarios[i].json.get("additionalFields").get("topLevel")

                volume_level: List = top_level.get("volumeLevel")
                price_level: List = top_level.get("priceLevel")

                assert len(price_level) == len(volume_level)
                assert pd.NaT not in volume_level and pd.NaT not in price_level

    def test_evaluation_type_in_include(self, helpers, mock_data, mock_data_sources, monkeypatch):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 60,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        fields_to_check = strategy.queries.include_fields
        fields_to_check.extend(strategy.queries.fields_for_alert)

        evaluation_type_to_check = list(EVALUATION_TYPE_GROUPING_MAP.values())

        evaluation_type_to_check.append(OrderField.CLIENT_IDENT_CLIENT)
        evaluation_type_to_check.append(OrderField.TRADER_NAME)
        evaluation_type_to_check.append(OrderField.PARTICIPANTS)

        assert set(evaluation_type_to_check).issubset(fields_to_check)

    def test_trader_name(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """check trader name."""

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_count_orders.csv"))

        strategy._run_algo(data_to_analyse=test_data)

        assert (
            strategy.scenarios[0]
            .json.get("additionalFields")
            .get("topLevel")
            .get("partiesTraderList")[0]
            is not None
        )

    def test_count_orders(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """check number of buy & sell orders."""
        number_of_hits = 1
        number_of_buys = 10
        number_of_sells = 1

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_count_orders.csv"))

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits
        assert (
            strategy.scenarios[0]
            .json.get("additionalFields")
            .get("topLevel")
            .get("numberSellOrders")[0]
            == number_of_sells
        )
        assert (
            strategy.scenarios[0]
            .json.get("additionalFields")
            .get("topLevel")
            .get("numberBuyOrders")[0]
            == number_of_buys
        )

    def test_percentage_balance_exists_unique(
        self, helpers, mock_data, mock_data_sources, monkeypatch
    ):
        """Check if percentage balance is unique."""
        number_of_hits = 1

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_percentage_balance.csv"))

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits
        assert (
            len(
                strategy.scenarios[0]
                .json.get("additionalFields")
                .get("topLevel")
                .get("percentageOrdersBalance")
            )
            == 1
        )

    def test_filter_1(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """
        Description:
            - 3 orders in the file
                06:50:10.804101593Z is a limit at 4321.5, and should be at level 2
                06:51:21.112071201Z is a limit at 4547, and should be at level 2
                06:54:15.104063517Z is a limit at 4321.5, and should be at level 3
            - 2 Hit;
            - Multiple price levels, the % of levels is around 40%;
        """
        number_of_hits = 1

        expected_grouping_fields = [
            "__instrument_code__",
            "reportDetails.executingEntity.fileIdentifier",
        ]

        thresholds = {
            "behaviourType": BehaviourTypeEnum.LAYERING,
            "evaluationType": EvaluationTypeEnum.EXECUTING_ENTITY,
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 245,
        }
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_filter_1.csv"))
        test_data["RIC"] = "test-ULVR.L"

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in test_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == EVALUATION_TYPE_GROUPING_MAP.get(
                EvaluationTypeEnum.EXECUTING_ENTITY
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 3

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits

    def test_filter_2(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """
        Description:
            - 3 orders in the file
            - No Hits;
            - Although these are defined as limit orders, none of these limit orders have a limit price
            so should fail against [Step 6] of the algo
        """
        number_of_hits = 0

        expected_grouping_fields = [
            "__instrument_code__",
            "reportDetails.executingEntity.fileIdentifier",
        ]
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 5,
        }
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_2)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_filter_2.csv"))

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in test_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == EVALUATION_TYPE_GROUPING_MAP.get(
                EvaluationTypeEnum.EXECUTING_ENTITY
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 3

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits

    def test_filter_3(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """
        Description:
            - 3 orders in the file
            - No Hits;
            - All of these are Market Orders, so should be filtered out as per [Step 5];
        """
        number_of_hits = 0

        expected_grouping_fields = [
            "__instrument_code__",
            "reportDetails.executingEntity.fileIdentifier",
        ]

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 5,
        }
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_filter_3.csv"))

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in test_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == EVALUATION_TYPE_GROUPING_MAP.get(
                EvaluationTypeEnum.EXECUTING_ENTITY
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 3

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits

    def test_filter_4(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """
        Description:
            - 2 orders in the file
               06:50:10.804101593Z is a limit at 4321.5, and should be at level 2
               06:51:21.112071201Z is a limit at 4547, and should be at level 2
            - No Hits;
            - Should detect no hits as there are not multiple price levels as per [Step 11]
        """
        number_of_hits = 0

        expected_grouping_fields = [
            "__instrument_code__",
            "reportDetails.executingEntity.fileIdentifier",
        ]
        thresholds = {
            "behaviourType": BehaviourTypeEnum.LAYERING,
            "evaluationType": EvaluationTypeEnum.EXECUTING_ENTITY,
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 5,
        }
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_4)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_filter_4.csv"))

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in test_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == EVALUATION_TYPE_GROUPING_MAP.get(
                EvaluationTypeEnum.EXECUTING_ENTITY
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 2

        strategy._run_algo(data_to_analyse=test_data)

        assert len(strategy.scenarios) == number_of_hits

    def test_check_number_of_orders_false_audit(
        self, helpers, mock_data, mock_data_sources, monkeypatch
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))

        test_data[OrderField.META_KEY] = test_data[OrderField.META_KEY].iloc[0]

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 2

    def test_filter_orders_without_limit_price_audit(
        self, helpers, mock_data, mock_data_sources, monkeypatch
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))

        test_data_2 = test_data.copy()

        test_data_2[OrderField.META_KEY] = test_data_2[OrderField.META_KEY].iloc[0]

        strategy._filter_orders_without_limit_price = MagicMock()
        strategy._filter_orders_without_limit_price.return_value = test_data_2

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 4

    def test_no_ric_audit(self, helpers, mock_data, mock_data_sources, monkeypatch):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))

        test_data["RIC"] = pd.NA

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 5

    def test_no_order_book_depth_data_audit(
        self, helpers, mock_data, mock_data_sources, monkeypatch
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))

        test_data["RIC"] = "test"

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 7

    def test_validate_market_data_audit(self, helpers, mock_data, mock_data_sources, monkeypatch):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        market_abuse_algorithms.data_source.repository.market_data.utils.add_vol_and_percentage_level_to_market_data = MagicMock()
        market_abuse_algorithms.data_source.repository.market_data.utils.add_vol_and_percentage_level_to_market_data.return_value = pd.DataFrame()

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))
        test_data["RIC"] = "test-ULVR.L"

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 11

    def test_validate_counts_audit(self, helpers, mock_data, mock_data_sources, monkeypatch):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 2,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 245,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = mock_data(TEST_DATA.joinpath("test_audit.csv"))

        strategy._validate_counts = MagicMock()
        strategy._validate_counts.return_value = False

        test_data["RIC"] = "test-ULVR.L"

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 9

    def test_order_dropped_creating_time_window(
        self, helpers, mock_data, mock_data_sources, monkeypatch
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "client",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        test_data = pd.read_csv(
            TEST_DATA.joinpath("test_order_dropped_creating_time_window.csv"),
            index_col=0,
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
            ],
        )

        test_data["RIC"] = "test-ULVR.L"

        strategy._layering_level2_algorithm(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 3

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 15

    def test_get_cases_to_analyse(self, helpers, mock_data, mock_data_sources, monkeypatch):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["XLON:GB00B10RZP78"]]

        scroll_data = pd.read_csv(
            TEST_DATA.joinpath("uc10.1_scroll_data.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )
        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = scroll_data

        unfully_filled_orders = pd.read_csv(
            TEST_DATA.joinpath("uc10.1_unfully_filled_orders.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )
        strategy.queries._get_unfully_filled_orders = MagicMock()
        strategy.queries._get_unfully_filled_orders.return_value = unfully_filled_orders

        for data in strategy.queries.get_cases_to_analyse(strategy_name="Layering V2"):
            assert data.shape == (2, 41)

    def test_case_19(self, helpers, mock_data, monkeypatch):
        """Test case 19 according to: https://steeleye.atlassian.net/wiki/space
        s/PRODUCT/pages/1011351590/Layering+Book+Imbalance+-+V2#Test-case-19.

        The test case should produce one alert with 36 relationships
        between alert records and related instruments
        """

        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(
            strategy,
            "cross_product",
            FakeCrossProductWithQuery(market_data_client=strategy._refinitiv_client),
        )

        strategy._audit = MagicMock()

        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            [
                "XLON:GB00B10RZP78",
                "FRAB:US904764AX59",
                "FRAB:US904764BB21",
                "IFLL:GB00KHK64W72",
                "IFLL:GB00KHLXXF30",
                "XEUE:NLEN13073775",
                "XEUE:NLEN13073841",
                "XEUR:DE000F0JT1N3",
                "XXXX:EZH0QV8H92H2",
            ]
        ]

        scroll_data = pd.read_csv(
            TEST_DATA.joinpath(TEST_DATA.joinpath("test_case_19/initial_query_data.csv")),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )
        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = scroll_data

        unfully_filled_orders = pd.read_csv(
            TEST_DATA.joinpath("test_case_19/unfilled_orders.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )
        strategy.queries._get_unfully_filled_orders = MagicMock()
        strategy.queries._get_unfully_filled_orders.return_value = unfully_filled_orders

        newos_list = [
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191BUYI:CAME:2021-08-24T13:00:30.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19112:REME:2021-08-24T13:00:05.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19122:REME:2021-08-24T13:00:10.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19132:REME:2021-08-24T13:00:15.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19142:REME:2021-08-24T13:00:20.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19152:REME:2021-08-24T13:00:25.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_2:1:LAYERINGANDBOOKBALANCE192BUYI:CAME:2021-08-24T13:00:30.696336Z:0.0:1705409631607",
        ]

        strategy.queries.sdp_repository.get_newo_child = MagicMock()
        strategy.queries.sdp_repository.get_newo_child.return_value = newos_list

        strategy.run()

        scenarios = strategy.scenarios

        # Verify one scenario is generated
        assert len(scenarios) == 1

        scenario = scenarios[0].json

        # Assert the relatedActivityDetected is flagged as True
        assert scenario["additionalFields"]["topLevel"]["relatedActivityDetected"]

        # Assert the number of relations is 54
        assert len(scenario["additionalFields"]["topLevel"]["relatedRecords"]) == 54

        # Assert the number of unique related instruments is 6
        id_list = [
            item.get("&id") for item in scenario["additionalFields"]["topLevel"]["relatedRecords"]
        ]
        assert len(set(id_list)) == 6

    def test_check_if_order_unfilled(self, helpers, mock_data, mock_data_sources, monkeypatch):
        """Test that verifies how the records are checked for verified for
        unfilled orders."""
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        include_fields = [
            "executionDetails.buySellIndicator",
            "executionDetails.orderStatus",
            "executionDetails.orderType",
            "executionDetails.limitPrice",
            "executionDetails.validityPeriod",
            "instrumentDetails.instrument.ext.instrumentUniqueIdentifier",
            "&id",
            "&key",
            "&parent",
            "priceFormingData.initialQuantity",
            "priceFormingData.tradedQuantity",
            "reportDetails.executingEntity.name",
            "reportDetails.executingEntity.fileIdentifier",
            "trader.name",
            "transactionDetails.priceCurrency",
            "timestamps.orderStatusUpdated",
            "timestamps.orderSubmitted",
            "counterpartyFileIdentifier",
            "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier",
            "clientFileIdentifier",
            "traderFileIdentifier",
        ]

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        strategy._audit = MagicMock()

        order_id = "layeringAndBookBalance_19_1:1:NEWO"

        input_data = pd.read_csv(
            TEST_DATA.joinpath(TEST_DATA.joinpath("test_case_19/layering_v2_pre_process.csv")),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )

        unfully_filled_orders = pd.read_csv(
            TEST_DATA.joinpath("test_case_19/unfilled_result.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )
        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = unfully_filled_orders

        assert strategy.queries._check_if_order_unfilled(
            order_id=order_id, include_fields=include_fields, newos_data=input_data
        )

        # Test without any return
        order_id = "layeringAndBookBalance_19_1:2:NEWO"

        strategy.queries.sdp_repository.search_after_query.return_value = pd.DataFrame()

        assert strategy.queries._check_if_order_unfilled(
            order_id=order_id, include_fields=include_fields, newos_data=input_data
        )

        # Test where only PARFs and returned

        partially_filled_orders = unfully_filled_orders
        partially_filled_orders.loc[0, OrderField.EXC_DTL_ORD_STATUS] = "PARF"

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = partially_filled_orders

        assert strategy.queries._check_if_order_unfilled(
            order_id=order_id, include_fields=include_fields, newos_data=input_data
        )

    def test_get_newo_query(self, helpers, mock_data, monkeypatch):
        """Tests if get_newo is able to fetch the meta keys of the child
        executions."""
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(strategy, "cross_product", FakeCrossProduct())

        strategy._audit = MagicMock()
        parent_ids = [
            "layeringAndBookBalance_19_1:1:NEWO",
            "layeringAndBookBalance_19_2:1:NEWO",
        ]
        expected_output = [
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191BUYI:CAME:2021-08-24T13:00:30.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19112:REME:2021-08-24T13:00:05.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19122:REME:2021-08-24T13:00:10.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19132:REME:2021-08-24T13:00:15.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19142:REME:2021-08-24T13:00:20.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19152:REME:2021-08-24T13:00:25.053054Z:0.0:1707116735577",
            "OrderState:layeringAndBookBalance_19_2:1:LAYERINGANDBOOKBALANCE192BUYI:CAME:2021-08-24T13:00:30.696336Z:0.0:1707116735577",
        ]

        newo_childs = pd.read_csv(TEST_DATA.joinpath("get_newo_result.csv"))

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = newo_childs

        output = strategy.queries.get_newo_child(parent_ids=parent_ids, required_fields=["&key"])

        assert expected_output == output

    def test_empty_related_activity(self, helpers, monkeypatch):
        """Uses test case 19 This tests for the inclusion of related records in
        the alert even if related activity is not present the alert should have
        all the related records and all of them should have correlationScore as
        None."""
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        monkeypatch.setattr(
            market_abuse_algorithms.strategy.abstract_layering_v2.query,
            "get_market_client",
            fake_market_data_client,
        )

        strategy = Strategy(context=context)

        monkeypatch.setattr(
            strategy,
            "cross_product",
            FakeCrossProductWithQuery(market_data_client=strategy._refinitiv_client),
        )

        strategy.queries._market_data_client.get_ric_map = MagicMock()
        strategy.queries._market_data_client.get_ric_map.return_value = None

        strategy._audit = MagicMock()

        alert_data = pd.read_csv(
            TEST_DATA.joinpath("test_case_19/alert_df.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED],
        )

        newos_list = [
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191BUYI:CAME:2021-08-24T13:00:30.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19112:REME:2021-08-24T13:00:05.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19122:REME:2021-08-24T13:00:10.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19132:REME:2021-08-24T13:00:15.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19142:REME:2021-08-24T13:00:20.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_1:1:LAYERINGANDBOOKBALANCE191LAYERINGANDBOOKBALANCE19152:REME:2021-08-24T13:00:25.053054Z:0.0:1705409631607",
            "OrderState:layeringAndBookBalance_19_2:1:LAYERINGANDBOOKBALANCE192BUYI:CAME:2021-08-24T13:00:30.696336Z:0.0:1705409631607",
        ]
        strategy.queries.get_newo_child = MagicMock()
        strategy.queries.get_newo_child.return_value = newos_list

        strategy._create_alert(alert_data=alert_data)

        scenarios = strategy.scenarios

        # Verify one scenario is generated
        assert len(scenarios) == 1

        scenario = scenarios[0].json

        # Assert the relatedActivityDetected is flagged as True
        assert scenario["additionalFields"]["topLevel"]["relatedActivityDetected"]

        # Assert the number of relations is 54
        assert len(scenario["additionalFields"]["topLevel"]["relatedRecords"]) == 54

        related_records = scenario["additionalFields"]["topLevel"]["relatedRecords"]

        id_list = []
        correlation_scores = []
        for item in related_records:
            id_list.append(item.get("&id"))
            correlation_scores.append(item.get("correlationScore"))

        # Assert the number of unique related instruments is 6
        assert len(set(id_list)) == 6
        assert all(pd.isnull(correlation_scores))
