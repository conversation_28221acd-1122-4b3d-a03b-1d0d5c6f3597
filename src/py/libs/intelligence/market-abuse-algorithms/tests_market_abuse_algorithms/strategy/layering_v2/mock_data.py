import market_abuse_algorithms.strategy.abstract_layering_v2.query
import market_abuse_algorithms.strategy.abstract_layering_v2.strategy
import pandas as pd
from market_abuse_algorithms.cross_product.cross_product import (
    CrossProductActivityDisplay,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario
from pathlib import Path
from typing import Dict, List, Optional, Union

TEST_DATA = Path(__file__).parent.joinpath("test_data")


class MarketDataAPI:
    pass


class MockMarketDataClient(MarketDataClient):
    def _get_se_market_data_api(self):
        return MarketDataAPI()

    def get_local_ric_map(
        self,
        list_inst_unique_id=None,
        instrument_combinations=None,
        use_prefix_query=False,
    ):
        return {"XLON:GB00B10RZP78": "ULVR.L"}

    def get_ric_map(
        self,
        list_inst_unique_id: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
        use_prefix_query: Optional[bool] = False,
    ) -> Union[dict, None]:
        return {"XLON:GB00B10RZP78": "ULVR.L"}

    def get_order_book_depth_data(self, ric, *args, **kwargs):
        if ric == "test":
            data = pd.DataFrame()
        elif ric == "ULVR.L":
            data = pd.read_csv(TEST_DATA.joinpath("test_case_19/order_book_depth.csv"))
        else:
            file_path = TEST_DATA.joinpath("ULVR.L__QUOTE_LEVEL2__TICK__20210824.parquet")
            data = pd.read_parquet(file_path)
        return data


def fake_market_data_client(*args, **kwargs):
    return MockMarketDataClient()


def mock_layering_data(monkeypatch, *args, **kwargs):
    def get_child(self, *args, **kwargs):
        return []

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_layering_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


class FakeCrossProduct(CrossProductActivityDisplay):
    def __init__(self, es_client=None, market_data_client=None):
        self.records = None
        self.scenario = None
        self.original_orders = None

    def set_scenario(
        self,
        scenario: Union[AbstractScenario, pd.Series],
        list_of_orders: Optional[List[str]] = None,
    ):
        self.scenario = scenario._scenario

    def run_checks_enrichment(self, start: int, end: int, filters):
        return pd.DataFrame()

    def related_activity(
        self, related_records_dict: Dict[str, List[Dict[str, str]]]
    ) -> pd.DataFrame:
        return pd.DataFrame()


class FakeCrossProductQuery:
    def __init__(self, es_client, market_data_client):
        self.es_client = es_client
        self.market_data_client = market_data_client


class FakeCrossProductWithQuery(CrossProductActivityDisplay):
    def __init__(self, es_client=None, market_data_client=None):
        self.records = None
        self.scenario = None
        self.original_orders = pd.read_csv(
            TEST_DATA.joinpath(TEST_DATA.joinpath("test_case_19/scenario_orignal_order.csv")),
            parse_dates=[OrderField.TS_ORD_SUBMITTED],
        )
        self.query = FakeCrossProductQuery(es_client, market_data_client)

    def set_scenario(
        self,
        scenario: Union[AbstractScenario, pd.Series],
        list_of_orders: Optional[List[str]] = None,
    ):
        self.scenario = scenario._scenario

    def explicitly_related(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        return replace_none_with_na(
            pd.read_json(TEST_DATA.joinpath("test_case_19/explicit_related.json"))
        )

    def implicitly_related(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        return pd.DataFrame()

    def explicitly_correlated(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        return replace_none_with_na(
            pd.read_json(TEST_DATA.joinpath("test_case_19/explicit_correlated.json"))
        )


def replace_none_with_na(data):
    if isinstance(data, list):
        return [replace_none_with_na(item) for item in data]
    elif isinstance(data, dict):
        return {key: replace_none_with_na(value) for key, value in data.items()}
    elif data is None:
        return pd.NA
    else:
        return data
