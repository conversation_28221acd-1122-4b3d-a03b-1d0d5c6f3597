import pytest
from market_abuse_algorithms.strategy.layering_v2.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerLayeringV2:
    def test_case_debug(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.19.csv"  # noqa: E501
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1
