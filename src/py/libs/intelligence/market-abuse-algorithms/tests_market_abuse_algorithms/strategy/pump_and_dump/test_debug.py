import pytest
from market_abuse_algorithms.strategy.pump_and_dump.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerPumpAndDump:
    def test_case_debug(self, helpers):
        thresholds = dict(assetClass="Equity", executionVsLowestMktPx24hPostXChange=0.10)
        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        scenarios = strategy.scenarios
        scenarios
