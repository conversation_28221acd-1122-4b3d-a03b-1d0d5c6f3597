{"took": 1, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": 4, "max_score": null, "hits": [{"_index": "mar_order_20230223", "_type": "OrderState", "_id": "PriceOutliers_UC5.1:1:PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI:FILL:2023-08-08T12:41:21Z:0.0", "_score": null, "_routing": "PriceOutliers_UC5.1:1:NEWO", "_parent": "PriceOutliers_UC5.1:1:NEWO", "_source": {"date": "2023-08-08", "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv", "&id": "PriceOutliers_UC5.1:1:PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI:FILL:2023-08-08T12:41:21Z:0.0", "transactionDetails": {"buySellIndicator": "BUYI", "netAmount": 638400.0, "price": 400.0, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1596.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-08-08T12:41:21Z", "venue": "XNAS", "pricingDetails": {"nearestQuote": 440.25, "vwapAllDayLimitAdjustedDetails": {"cost": {"CHF": -50885.***********, "JPY": -8258507.*********, "EUR": -52867.***********, "GBP": -45552.***********, "native": -58070.**********, "USD": -58070.**********}, "priceDifference": -38.**************, "source": "NSM"}, "nearestQuoteDetails": {"cost": {"CHF": -53594.52812595009, "JPY": -8698183.104991859, "EUR": -55682.62662436373, "GBP": -47977.82157835052, "native": -61161.79708420113, "USD": -61161.79708420113}, "marketVolume": 1.0, "priceDifference": -40.25, "source": "NSM", "timeDifference": 55443867700.0}, "marketDayTradedVolumeDetails": {"source": "NSM"}, "vwap": 438.1185, "arrivalPrice": 440.25, "preval5MinutesDetails": {"cost": {"CHF": -54215.367029783934, "JPY": -8798942.84023122, "EUR": -56327.65405691837, "GBP": -48533.59656506257, "native": -61870.29521611914, "USD": -61870.29521611914}, "source": "NSM"}, "preval10DaysDetails": {"cost": {"CHF": -37442.936537959446, "JPY": -6076842.718539891, "EUR": -38901.752247230594, "GBP": -33518.916788781295, "native": -42729.68466835809, "USD": -42729.68466835809}, "priceDifference": -27.69999999999999, "source": "NSM"}, "percentVsArrivalPriceMarketSubmission": -0.095804819994049, "arrivalPriceDetails": {"cost": {"CHF": -53594.52812595009, "JPY": -8698183.104991859, "EUR": -55682.62662436373, "GBP": -47977.82157835052, "native": -61161.79708420113, "USD": -61161.79708420113}, "marketVolume": 1.0, "priceDifference": -40.25, "source": "NSM", "timeDifference": 55443867700.0}, "vwapStartToCloseDetails": {"cost": {"CHF": -51174.***********, "JPY": -8305395.*********, "EUR": -53168.***********, "GBP": -45811.***********, "native": -58399.***********, "USD": -58399.***********}, "marketVolume": 519910.0, "priceDifference": -38.*************, "source": "NSM"}, "midPointPriceDetails": {"cost": {"CHF": -49617.99812310651, "JPY": -8052807.778504382, "EUR": -51551.16688114961, "GBP": -44418.03191980494, "native": -56623.80170225474, "USD": -56623.80170225474}, "priceDifference": -37.125, "source": "NSM", "timeDifference": 55443867700.0}, "percentVsClose": -0.091375402600501, "percentVsMarketAverageDailyTradedVolume": 0.000757837115424, "preval10Minutes": 440.23, "vwapAllDayLimitAdjusted": 438.1185, "preval2Hours": 446.0, "nearTouch": 434.0, "askPrice": 440.25, "percentVsPreval20Days": -0.095714166696421, "percentVsVwapAllDayLimitAdjusted": -0.090962077558245, "percentVsArrivalPriceMid": -0.088696431237868, "priceVolatility": 8.734965559965184, "preval1Minute": 437.9, "percentVsMarketDayTradedVolume": 0.001561546047997, "bidPriceDetails": {"cost": {"CHF": -45611.66798534952, "JPY": -7402596.006224882, "EUR": -47388.7459588047, "GBP": -40831.56518048489, "native": -52051.79856115108, "USD": -52051.79856115108}, "marketVolume": 1.0, "priceDifference": -34.0, "source": "NSM", "timeDifference": 55443867700.0}, "preval1HourDetails": {"cost": {"CHF": -53569.17238151915, "JPY": -8694067.966459332, "EUR": -55656.28299378613, "GBP": -47955.12311593594, "native": -61132.86124037469, "USD": -61132.86124037469}, "priceDifference": -40.23000000000002, "source": "NSM"}, "preval1MinuteDetails": {"cost": {"CHF": -50606.94343287202, "JPY": -8213309.74924565, "EUR": -52578.64252765924, "GBP": -45303.33576110703, "native": -57752.38095238092, "USD": -57752.38095238092}, "priceDifference": -37.89999999999998, "source": "NSM"}, "bidPrice": 434.0, "arrivalPriceMarketSubmissionDetails": {"cost": {"CHF": -53594.52812595009, "JPY": -8698183.104991859, "EUR": -55682.62662436373, "GBP": -47977.82157835052, "native": -61161.79708420113, "USD": -61161.79708420113}, "marketVolume": 1.0, "priceDifference": -40.25, "source": "NSM", "timeDifference": 55443867700.0}, "askPriceDetails": {"cost": {"CHF": -53594.52812595009, "JPY": -8698183.104991859, "EUR": -55682.62662436373, "GBP": -47977.82157835052, "native": -61161.79708420113, "USD": -61161.79708420113}, "marketVolume": 1.0, "priceDifference": -40.25, "source": "NSM", "timeDifference": 55443867700.0}, "percentVsPreval1Hour": -0.095759494424146, "arrivalPriceMid": 437.125, "marketDayTradedVolume": 1022064.0, "vwapStartToClose": 438.*************, "percentVsNearTouch": -0.081534772182254, "percentVsArrival": -0.095804819994049, "percentVsOpen": -0.096054072064353, "preval1Hour": 440.23, "marketPrice": 437.6, "arrivalPriceFirstFill": 440.25, "arrivalPriceFirstFillDetails": {"cost": {"CHF": -53594.52812595009, "JPY": -8698183.104991859, "EUR": -55682.62662436373, "GBP": -47977.82157835052, "native": -61161.79708420113, "USD": -61161.79708420113}, "marketVolume": 1.0, "priceDifference": -40.25, "source": "NSM", "timeDifference": 55443867700.0}, "openPriceDetails": {"cost": {"CHF": -53733.96314699799, "JPY": -8720812.865654603, "EUR": -55827.494178699206, "GBP": -48102.643809192596, "native": -61320.91960588321, "USD": -61320.91960588321}, "priceDifference": -40.360000000000014, "source": "NSM"}, "percentVsPreval2Hours": -0.108747044917258, "marketPriceDetails": {"cost": {"CHF": -50224.34298808168, "JPY": -8151215.187707261, "EUR": -52181.13557203291, "GBP": -44960.83184293071, "native": -57315.75931232095, "USD": -57315.75931232095}, "priceDifference": -37.60000000000002, "source": "NSM"}, "openPrice": 440.36, "percentVsPreval1Minute": -0.09046425587779, "percentVsVwap": -0.090962077558245, "preval20Days": 440.21, "percentVsPreval5Minutes": -0.096914622832267, "preval10MinutesDetails": {"cost": {"CHF": -53569.17238151915, "JPY": -8694067.966459332, "EUR": -55656.28299378613, "GBP": -47955.12311593594, "native": -61132.86124037469, "USD": -61132.86124037469}, "priceDifference": -40.23000000000002, "source": "1691497870350764683"}, "percentVsPreval5Days": -0.092103694164222, "arrivalPriceMarketSubmission": 440.25, "percentVsNearestQuote": -0.095804819994049, "percentVsArrivalPriceFirstFill": -0.095804819994049, "preval2HoursDetails": {"cost": {"CHF": -60834.58596126926, "JPY": -9873216.283646619, "EUR": -63204.76463508494, "GBP": -54459.12135252824, "native": -69424.1134751773, "USD": -69424.1134751773}, "priceDifference": -46.0, "source": "NSM"}, "arrivalPriceMidDetails": {"cost": {"CHF": -49617.99812310651, "JPY": -8052807.778504382, "EUR": -51551.16688114961, "GBP": -44418.03191980494, "native": -56623.80170225474, "USD": -56623.80170225474}, "marketVolume": 1.0, "priceDifference": -37.125, "source": "NSM", "timeDifference": 55443867700.0}, "closePriceDetails": {"cost": {"CHF": -51116.65138556434, "JPY": -8296033.364092473, "EUR": -53108.20923175516, "GBP": -45759.626320357194, "native": -58334.05702015987, "USD": -58334.05702015987}, "priceDifference": -38.30000000000001, "source": "NSM"}, "preval10Days": 427.7, "percentVsMidPointPrice": -0.088696431237868, "marketAverageDailyTradedVolumeDetails": {"source": "NSM"}, "vwapDetails": {"cost": {"CHF": -50885.***********, "JPY": -8258507.*********, "EUR": -52867.***********, "GBP": -45552.***********, "native": -58070.**********, "USD": -58070.**********}, "priceDifference": -38.**************, "source": "NSM"}, "percentVsPreval10Minutes": -0.095759494424146, "vwapStartToCloseLimitAdjusted": 438.*************, "preval1DayDetails": {"cost": {"CHF": -54240.692021892435, "JPY": -8803052.987781629, "EUR": -56353.965737031096, "GBP": -48556.2674979981, "native": -61899.195965554965, "USD": -61899.195965554965}, "priceDifference": -40.75999999999999, "source": "NSM"}, "percentVsAskPrice": -0.095804819994049, "percentVsMarket": -0.089780324737345, "marketAverageDailyTradedVolume": 2105993.44835118, "percentVsBidPrice": -0.081534772182254, "preval1Day": 440.76, "nearTouchDetails": {"cost": {"CHF": -45611.66798534952, "JPY": -7402596.006224882, "EUR": -47388.7459588047, "GBP": -40831.56518048489, "native": -52051.79856115108, "USD": -52051.79856115108}, "marketVolume": 1.0, "priceDifference": -34.0, "source": "NSM", "timeDifference": 55443867700.0}, "preval5Minutes": 440.74, "preval20DaysDetails": {"cost": {"CHF": -53543.8154299735, "JPY": -8689952.632016791, "EUR": -55629.938109063376, "GBP": -47932.423572912274, "native": -61103.92401899522, "USD": -61103.92401899522}, "priceDifference": -40.20999999999998, "source": "NSM"}, "preval5Days": 438.62, "volumeVolatility": 237602.4526348571, "midPointPrice": 437.125, "percentVsVwapStartToCloseLimitAdjusted": -0.**************, "percentVsPreval1Day": -0.096959893429754, "percentVsPreval10Days": -0.066932463452942, "preval5DaysDetails": {"cost": {"CHF": -51524.06765854695, "JPY": -8362155.437861422, "EUR": -53531.4988660228, "GBP": -46124.34536793122, "native": -58798.***********, "USD": -58798.***********}, "priceDifference": -38.***************, "source": "NSM"}, "percentVsVwapStartToClose": -0.**************, "closePrice": 438.3, "vwapStartToCloseLimitAdjustedDetails": {"cost": {"CHF": -51174.***********, "JPY": -8305395.*********, "EUR": -53168.***********, "GBP": -45811.***********, "native": -58399.***********, "USD": -58399.***********}, "marketVolume": 519910.0, "priceDifference": -38.*************, "source": "NSM"}}}, "buyerFileIdentifier": "account:scar", "reportDetails": {"executingEntity": {"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "fileIdentifier": "lei:894500wota5040khgx73", "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}, "transactionRefNo": "PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI"}, "timestamps": {"orderReceived": "2023-08-08T12:41:21Z", "orderStatusUpdated": "2023-08-08T12:41:21Z", "orderSubmitted": "2023-08-08T12:41:21Z", "tradingDateTime": "2023-08-08T12:41:21Z"}, "&parent": "PriceOutliers_UC5.1:1:NEWO", "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "FILL", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- Scar;Counterparty ID- Wonderland;Trader ID- Mufasa", "tradingCapacity": "DEAL"}, "flags": {"TCAFlagStatus": ["Hit"]}, "sourceIndex": "0", "marDetails": {"isPersonalAccountDealing": false}, "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "VenueInstrument:*******************:**********", "cfiAttribute1": "Voting", "cfiAttribute2": "Free", "cfiAttribute3": "<PERSON>y Paid", "cfiAttribute4": "Registered", "cfiCategory": "Equity", "cfiGroup": "Common/Ordinary shares", "derivative": {"deliveryType": "CASH"}, "ext": {"bestExAssetClassMain": "Equity", "emirEligible": false, "exchangeSymbol": "NFLX", "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": false, "priceNotation": "MONE", "pricingReferences": {"ICE": "isin/US64110L1061/USD", "RIC": "NFLX.OQ"}, "quantityNotation": "UNIT", "venueName": "<PERSON><PERSON><PERSON>"}, "instrumentClassification": "ESVUFR", "instrumentFullName": "NETFLIX ORD", "instrumentIdCode": "US64110L1061", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300Y7VHGU0I7CE873", "notionalCurrency1": "USD", "sourceKey": "refinitivInstruments.equities.ARCX.********", "venue": {"admissionToTradingOrFirstTradeDate": "2003-04-04T00:00:00", "tradingVenue": "ARCX"}}}, "buyerDecisionMaker": [{"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}], "id": "PriceOutliers_UC5.1", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:894500wota5040khgx73", "buySell": "1", "clientFileIdentifier": "account:scar", "marketIdentifiers": [{"labelId": "US64110L1061", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:scar", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:wonderland", "path": "seller", "type": "ARRAY"}, {"labelId": "account:wonderland", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:mufasa", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:scar", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:mufasa", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "PriceOutliers_UC5.1", "internalOrderIdCode": "PriceOutliers_UC5.1", "orderIdCode": "PriceOutliers_UC5.1", "transactionRefNo": "PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI"}, "sellerFileIdentifier": "account:wonderland", "&key": "OrderState:PriceOutliers_UC5.1:1:PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI:FILL:2023-08-08T12:41:21Z:0.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": 971490.**********, "BGN": 1136728.**********, "BRL": 3115803.*********, "CAD": 853679.**********, "CHF": 559413.**********, "CNY": 4590156.*********, "CZK": ********.********, "DKK": 4330879.*********, "EUR": 581209.**********, "GBP": 500787.**********, "HKD": 4983518.********, "HUF": *********.********, "IDR": **********.910414, "ILS": 2344306.*********, "INR": ********.********, "JPY": ********.7822287, "KRW": *********.4049526, "MXN": ********.*********, "MYR": 2910462.**********, "NOK": 6479608.*********, "NZD": 1046408.**********, "PHP": ********.********, "PLN": 2565340.*********, "RON": 2876403.*********, "SEK": 6778350.*********, "SGD": 856120.**********, "THB": ********.********, "TRY": ********.********, "USD": 638400.0, "ZAR": ********.*********, "refRateDate": "2023-08-07T13:15:00Z"}, "native": 638400.0, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "traderFileIdentifier": "account:mufasa", "tradersAlgosWaiversIndicators": {"executionWithinFirmFileIdentifier": "account:mufasa"}, "priceFormingData": {"initialQuantity": 1596.0, "price": 400.0, "remainingQuantity": 0.0, "tradedQuantity": 1596.0}, "&hash": "fddbf02f47d1e76e32c4f967a950df503fbf6257451a62d75366cbd15236875e", "counterpartyFileIdentifier": "account:wonderland", "&validationErrors": [{"field_path": "transactionDetails.tradingDateTime", "message": "`Trading Date Time´ must be less than the current time", "code": "SE_DV-15", "category": "Trade details", "modules_affected": ["Best Execution", "Orders", "Market Abuse", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "counterparty", "message": "`Counterparty Name` must be populated when `Counterparty ID` is populated", "code": "SE_DV-87", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "buyer.0.&key", "message": "`Buyer` must be populated", "code": "SE_DV-188", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "seller.0.&key", "message": "`Seller` must be populated", "code": "SE_DV-189", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "timestamps.tradingDateTime", "message": "`Trade Date Time` must not be in the future for `Executions`", "code": "SE_DV-339", "category": "Dates", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&user": "reference-refinitiv-tca-metrics-v2"}, "sort": ["s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv"]}, {"_index": "mar_order_20230223", "_type": "OrderState", "_id": "PriceOutliers_UC5.2:2:PRICEOUTLIERSUC52PRICEOUTLIERSUC52T20230808SELL:FILL:2023-08-08T13:41:30Z:0.0", "_score": null, "_routing": "PriceOutliers_UC5.2:2:NEWO", "_parent": "PriceOutliers_UC5.2:2:NEWO", "_source": {"date": "2023-08-08", "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv", "&id": "PriceOutliers_UC5.2:2:PRICEOUTLIERSUC52PRICEOUTLIERSUC52T20230808SELL:FILL:2023-08-08T13:41:30Z:0.0", "transactionDetails": {"buySellIndicator": "SELL", "netAmount": *********.0, "price": 460.0, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 357951.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-08-08T13:41:30Z", "venue": "XNAS", "pricingDetails": {"nearestQuote": 438.08, "vwapAllDayLimitAdjustedDetails": {"cost": {"CHF": 7030643.*********, "JPY": **********.214089, "EUR": 7304564.*********, "GBP": 6293831.*********, "native": 8023333.*********, "USD": 8023333.*********}, "priceDifference": 21.***************, "source": "NSM"}, "nearestQuoteDetails": {"cost": {"CHF": 7043315.557530263, "JPY": 1143102673.4979765, "EUR": 7317730.449382091, "GBP": 6305176.087101092, "native": 8037795.12560129, "USD": 8037795.12560129}, "marketVolume": 1.0, "priceDifference": 21.920000000000016, "source": "NSM", "timeDifference": 1843688413.0}, "marketDayTradedVolumeDetails": {"source": "NSM"}, "vwap": 438.1185, "arrivalPrice": 438.08, "preval5MinutesDetails": {"cost": {"CHF": 6006811.402182207, "JPY": 974882087.4128649, "EUR": 6240843.0152542405, "GBP": 5377297.567233512, "native": 6854941.967955259, "USD": 6854941.967955259}, "source": "NSM"}, "preval10DaysDetails": {"cost": {"CHF": 10499966.749540396, "JPY": 1704103694.4890442, "EUR": 10909056.363158852, "GBP": 9399570.234188562, "native": 11982507.509293683, "USD": 11982507.509293683}, "priceDifference": 32.30000000000001, "source": "NSM"}, "percentVsArrivalPriceMarketSubmission": 0.048815250311776, "arrivalPriceDetails": {"cost": {"CHF": 7043315.557530263, "JPY": 1143102673.4979765, "EUR": 7317730.449382091, "GBP": 6305176.087101092, "native": 8037795.12560129, "USD": 8037795.12560129}, "marketVolume": 1.0, "priceDifference": 21.920000000000016, "source": "NSM", "timeDifference": 1843688413.0}, "vwapStartToCloseDetails": {"cost": {"CHF": 7066901.*********, "JPY": **********.9985435, "EUR": 7342235.*********, "GBP": 6326290.*********, "native": 8064711.*********, "USD": 8064711.*********}, "marketVolume": 450397.0, "priceDifference": 21.***************, "source": "NSM"}, "midPointPriceDetails": {"cost": {"CHF": 6914998.187362182, "JPY": 1122277264.2575028, "EUR": 7184413.7011555135, "GBP": 6190306.377326625, "native": 7891360.009349217, "USD": 7891360.009349217}, "priceDifference": 21.529999999999973, "source": "NSM", "timeDifference": 1843688413.0}, "percentVsClose": 0.048313481019704, "percentVsMarketAverageDailyTradedVolume": 0.169967765227497, "preval10Minutes": 441.63, "vwapAllDayLimitAdjusted": 438.1185, "preval2Hours": 437.11, "nearTouch": 438.86, "askPrice": 438.86, "percentVsPreval20Days": 0.043967518690083, "percentVsVwapAllDayLimitAdjusted": 0.048727422940291, "percentVsArrivalPriceMid": 0.047925918505904, "priceVolatility": 8.734965559965184, "preval1Minute": 438.7, "percentVsMarketDayTradedVolume": 0.350223665054243, "bidPriceDetails": {"cost": {"CHF": 7043315.557530263, "JPY": 1143102673.4979765, "EUR": 7317730.449382091, "GBP": 6305176.087101092, "native": 8037795.12560129, "USD": 8037795.12560129}, "marketVolume": 1.0, "priceDifference": 21.920000000000016, "source": "NSM", "timeDifference": 1843688413.0}, "preval1HourDetails": {"cost": {"CHF": 8392424.64634343, "JPY": 1362057822.3431761, "EUR": 8719402.2299672, "GBP": 7512898.543406638, "native": 9577391.409395972, "USD": 9577391.409395972}, "priceDifference": 26.0, "source": "NSM"}, "preval1MinuteDetails": {"cost": {"CHF": 6839376.047478878, "JPY": 1110004085.5861564, "EUR": 7105845.244133899, "GBP": 6122609.437703092, "native": 7805060.416156675, "USD": 7805060.416156675}, "priceDifference": 21.30000000000001, "source": "NSM"}, "bidPrice": 438.08, "arrivalPriceMarketSubmissionDetails": {"cost": {"CHF": 7043315.557530263, "JPY": 1143102673.4979765, "EUR": 7317730.449382091, "GBP": 6305176.087101092, "native": 8037795.12560129, "USD": 8037795.12560129}, "marketVolume": 1.0, "priceDifference": 21.920000000000016, "source": "NSM", "timeDifference": 1843688413.0}, "askPriceDetails": {"cost": {"CHF": 6786792.166624211, "JPY": 1101469926.595707, "EUR": 7051212.640648531, "GBP": 6075536.347561994, "native": 7745051.964488346, "USD": 7745051.964488346}, "marketVolume": 1.0, "priceDifference": 21.139999999999986, "source": "NSM", "timeDifference": 1843688413.0}, "percentVsPreval1Hour": 0.058165548098434, "arrivalPriceMid": 438.47, "marketDayTradedVolume": 1022064.0, "vwapStartToClose": 438.00835123235726, "percentVsNearTouch": 0.047037358431791, "percentVsArrival": 0.048815250311776, "percentVsOpen": 0.043626993646986, "preval1Hour": 434.0, "marketPrice": 438.6, "arrivalPriceFirstFill": 438.08, "arrivalPriceFirstFillDetails": {"cost": {"CHF": 7043315.557530263, "JPY": 1143102673.4979765, "EUR": 7317730.449382091, "GBP": 6305176.087101092, "native": 8037795.12560129, "USD": 8037795.12560129}, "marketVolume": 1.0, "priceDifference": 21.920000000000016, "source": "NSM", "timeDifference": 1843688413.0}, "openPriceDetails": {"cost": {"CHF": 6294727.182991815, "JPY": 1021609696.8884689, "EUR": 6539976.2940174695, "GBP": 5635039.774214272, "native": 7183509.961348789, "USD": 7183509.961348789}, "priceDifference": 19.639999999999986, "source": "NSM"}, "percentVsPreval2Hours": 0.051030531372964, "marketPriceDetails": {"cost": {"CHF": 6872250.482116789, "JPY": 1115339478.2456763, "EUR": 7140000.500900559, "GBP": 6152038.631590949, "native": 7842576.550189175, "USD": 7842576.550189175}, "priceDifference": 21.399999999999977, "source": "NSM"}, "openPrice": 440.36, "percentVsPreval1Minute": 0.047401802603761, "percentVsVwap": 0.048727422940291, "preval20Days": 440.21, "percentVsPreval5Minutes": 0.041631529892149, "preval10MinutesDetails": {"cost": {"CHF": 5879392.093869138, "JPY": 954202430.1125172, "EUR": 6108459.318305597, "GBP": 5263231.802431651, "native": 6709531.715226868, "USD": 6709531.715226868}, "priceDifference": 18.370000000000005, "source": "1691501489796443679"}, "percentVsPreval5Days": 0.047584073356925, "arrivalPriceMarketSubmission": 438.08, "percentVsNearestQuote": 0.048815250311776, "percentVsArrivalPriceFirstFill": 0.048815250311776, "preval2HoursDetails": {"cost": {"CHF": 7362947.710656875, "JPY": 1194977726.6303484, "EUR": 7649815.803279869, "GBP": 6591310.790580034, "native": 8402557.67832261, "USD": 8402557.67832261}, "priceDifference": 22.889999999999986, "source": "NSM"}, "arrivalPriceMidDetails": {"cost": {"CHF": 6914998.187362182, "JPY": 1122277264.2575028, "EUR": 7184413.7011555135, "GBP": 6190306.377326625, "native": 7891360.009349217, "USD": 7891360.009349217}, "marketVolume": 1.0, "priceDifference": 21.529999999999973, "source": "NSM", "timeDifference": 1843688413.0}, "closePriceDetails": {"cost": {"CHF": 6970917.7015616335, "JPY": 1131352783.5438366, "EUR": 7242511.897726372, "GBP": 6240365.526437975, "native": 7955175.0684626475, "USD": 7955175.0684626475}, "priceDifference": 21.69999999999999, "source": "NSM"}, "preval10Days": 427.7, "percentVsMidPointPrice": 0.047925918505904, "marketAverageDailyTradedVolumeDetails": {"source": "NSM"}, "vwapDetails": {"cost": {"CHF": 7030643.*********, "JPY": **********.214089, "EUR": 7304564.*********, "GBP": 6293831.*********, "native": 8023333.*********, "USD": 8023333.*********}, "priceDifference": 21.***************, "source": "NSM"}, "percentVsPreval10Minutes": 0.040748422301831, "vwapStartToCloseLimitAdjusted": 438.00835123235726, "preval1DayDetails": {"cost": {"CHF": 6163786.63465847, "JPY": 1000358556.0519475, "EUR": 6403934.165878929, "GBP": 5517821.795346262, "native": 7034081.287801416, "USD": 7034081.287801416}, "priceDifference": 19.24000000000001, "source": "NSM"}, "percentVsAskPrice": 0.047037358431791, "percentVsMarket": 0.047629646116181, "marketAverageDailyTradedVolume": 2105993.44835118, "percentVsBidPrice": 0.048815250311776, "preval1Day": 440.76, "nearTouchDetails": {"cost": {"CHF": 6786792.166624211, "JPY": 1101469926.595707, "EUR": 7051212.640648531, "GBP": 6075536.347561994, "native": 7745051.964488346, "USD": 7745051.964488346}, "marketVolume": 1.0, "priceDifference": 21.139999999999986, "source": "NSM", "timeDifference": 1843688413.0}, "preval5Minutes": 441.24, "preval20DaysDetails": {"cost": {"CHF": 6343859.888825777, "JPY": 1029583743.6191943, "EUR": 6591023.261117689, "GBP": 5679023.372476835, "native": 7239579.950011671, "USD": 7239579.950011671}, "priceDifference": 19.79000000000002, "source": "NSM"}, "preval5Days": 438.62, "volumeVolatility": 237602.4526348571, "midPointPrice": 438.47, "percentVsVwapStartToCloseLimitAdjusted": 0.***************, "percentVsPreval1Day": 0.04271948132688, "percentVsPreval10Days": 0.072772332995381, "preval5DaysDetails": {"cost": {"CHF": 6865675.009857307, "JPY": 1114272304.7166855, "EUR": 7133168.841410189, "GBP": 6146152.2********, "native": 7835072.*********, "USD": 7835072.*********}, "priceDifference": 21.***************, "source": "NSM"}, "percentVsVwapStartToClose": 0.***************, "closePrice": 438.3, "vwapStartToCloseLimitAdjustedDetails": {"cost": {"CHF": 7066901.*********, "JPY": **********.9985435, "EUR": 7342235.*********, "GBP": 6326290.*********, "native": 8064711.*********, "USD": 8064711.*********}, "marketVolume": 450397.0, "priceDifference": 21.***************, "source": "NSM"}}}, "buyerFileIdentifier": "account:wonderland", "reportDetails": {"executingEntity": {"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "fileIdentifier": "lei:894500wota5040khgx73", "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}, "transactionRefNo": "PRICEOUTLIERSUC52PRICEOUTLIERSUC52T20230808SELL"}, "timestamps": {"orderReceived": "2023-08-08T13:41:30Z", "orderStatusUpdated": "2023-08-08T13:41:30Z", "orderSubmitted": "2023-08-08T13:41:30Z", "tradingDateTime": "2023-08-08T13:41:30Z"}, "&parent": "PriceOutliers_UC5.2:2:NEWO", "executionDetails": {"buySellIndicator": "SELL", "orderStatus": "FILL", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- Ken;Counterparty ID- Wonderland;Trader ID- Barbie", "tradingCapacity": "DEAL"}, "flags": {"TCAFlagStatus": ["Hit"]}, "sourceIndex": "1", "marDetails": {"isPersonalAccountDealing": false}, "sellerDecisionMakerFileIdentifier": "lei:894500wota5040khgx73", "&model": "OrderState", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "VenueInstrument:*******************:**********", "cfiAttribute1": "Voting", "cfiAttribute2": "Free", "cfiAttribute3": "<PERSON>y Paid", "cfiAttribute4": "Registered", "cfiCategory": "Equity", "cfiGroup": "Common/Ordinary shares", "derivative": {"deliveryType": "CASH"}, "ext": {"bestExAssetClassMain": "Equity", "emirEligible": false, "exchangeSymbol": "NFLX", "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": false, "priceNotation": "MONE", "pricingReferences": {"ICE": "isin/US64110L1061/USD", "RIC": "NFLX.OQ"}, "quantityNotation": "UNIT", "venueName": "<PERSON><PERSON><PERSON>"}, "instrumentClassification": "ESVUFR", "instrumentFullName": "NETFLIX ORD", "instrumentIdCode": "US64110L1061", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300Y7VHGU0I7CE873", "notionalCurrency1": "USD", "sourceKey": "refinitivInstruments.equities.ARCX.********", "venue": {"admissionToTradingOrFirstTradeDate": "2003-04-04T00:00:00", "tradingVenue": "ARCX"}}}, "id": "PriceOutliers_UC5.2", "&timestamp": *************, "buySell": "2", "clientFileIdentifier": "account:ken", "marketIdentifiers": [{"labelId": "US64110L1061", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:wonderland", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:ken", "path": "seller", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "sellerDecisionMaker", "type": "ARRAY"}, {"labelId": "account:wonderland", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:barbie", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:ken", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:barbie", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "PriceOutliers_UC5.2", "internalOrderIdCode": "PriceOutliers_UC5.2", "orderIdCode": "PriceOutliers_UC5.2", "transactionRefNo": "PRICEOUTLIERSUC52PRICEOUTLIERSUC52T20230808SELL"}, "sellerDecisionMaker": [{"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}], "sellerFileIdentifier": "account:ken", "&key": "OrderState:PriceOutliers_UC5.2:2:PRICEOUTL<PERSON>RSUC52PRICEOUTLIERSUC52T20230808SELL:FILL:2023-08-08T13:41:30Z:0.0:*************", "bestExecutionData": {"largeInScale": false, "pendingDisclosure": false, "rts27ValueBand": 1, "timeAcceptExecutePlaced": 0, "timeAcceptExecuteReceived": 0, "timeRfqResponsePlaced": 0, "timeRfqResponseReceived": 0, "timeToFill": 0, "transactionVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.3066278, "BRL": *********.7053896, "CAD": *********.********, "CHF": *********.********, "CNY": **********.6911871, "CZK": **********.4450107, "DKK": **********.896941, "EUR": *********.********, "GBP": *********.********, "HKD": **********.7246904, "HUF": ***********.91478, "IDR": *************.8657, "ILS": *********.7643845, "INR": ***********.054625, "JPY": ***********.438454, "KRW": ************.4188, "MXN": **********.427531, "MYR": *********.6627822, "NOK": **********.272032, "NZD": *********.5134741, "PHP": **********.369993, "PLN": *********.2498178, "RON": *********.2053896, "SEK": **********.3317552, "SGD": *********.603059, "THB": **********.085943, "TRY": **********.652221, "USD": *********.0, "ZAR": **********.5917697, "refRateDate": "2023-08-07T13:15:00Z"}, "native": *********.0, "nativeCurrency": "USD"}}, "dataSourceName": "SteelEyeTradeBlotter", "traderFileIdentifier": "account:barbie", "tradersAlgosWaiversIndicators": {"executionWithinFirmFileIdentifier": "account:barbie", "shortSellingIndicator": "SELL"}, "priceFormingData": {"initialQuantity": 357951.0, "price": 460.0, "remainingQuantity": 0.0, "tradedQuantity": 357951.0}, "&hash": "c13fbdc74446b6bfd2ebda2317a9c01329f0c8af731e0c15685cfccdd76c1dce", "counterpartyFileIdentifier": "account:wonderland", "&validationErrors": [{"field_path": "transactionDetails.tradingDateTime", "message": "`Trading Date Time´ must be less than the current time", "code": "SE_DV-15", "category": "Trade details", "modules_affected": ["Best Execution", "Orders", "Market Abuse", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "counterparty", "message": "`Counterparty Name` must be populated when `Counterparty ID` is populated", "code": "SE_DV-87", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "buyer.0.&key", "message": "`Buyer` must be populated", "code": "SE_DV-188", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "seller.0.&key", "message": "`Seller` must be populated", "code": "SE_DV-189", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "timestamps.tradingDateTime", "message": "`Trade Date Time` must not be in the future for `Executions`", "code": "SE_DV-339", "category": "Dates", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "HIGH", "source": "steeleye"}], "&user": "reference-refinitiv-tca-metrics-v2"}, "sort": ["s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv"]}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "PriceOutliers_UC5.1:1:NEWO", "_score": null, "_source": {"date": "2023-08-08", "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv", "&id": "PriceOutliers_UC5.1:1:NEWO", "transactionDetails": {"buySellIndicator": "BUYI", "netAmount": 638400.0, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 1596.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-08-08T12:41:21Z", "venue": "XNAS"}, "buyerFileIdentifier": "account:scar", "reportDetails": {"executingEntity": {"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "fileIdentifier": "lei:894500wota5040khgx73", "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}, "transactionRefNo": "PRICEOUTLIERSUC51PRICEOUTLIERSUC51T20230808BUYI"}, "timestamps": {"orderReceived": "2023-08-08T12:41:21Z", "orderStatusUpdated": "2023-08-08T12:41:21Z", "orderSubmitted": "2023-08-08T12:41:21Z", "tradingDateTime": "2023-08-08T12:41:21Z"}, "executionDetails": {"buySellIndicator": "BUYI", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- Scar;Counterparty ID- Wonderland;Trader ID- Mufasa", "tradingCapacity": "DEAL"}, "sourceIndex": "0", "marDetails": {"isPersonalAccountDealing": false}, "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "VenueInstrument:*******************:**********", "cfiAttribute1": "Voting", "cfiAttribute2": "Free", "cfiAttribute3": "<PERSON>y Paid", "cfiAttribute4": "Registered", "cfiCategory": "Equity", "cfiGroup": "Common/Ordinary shares", "derivative": {"deliveryType": "CASH"}, "ext": {"bestExAssetClassMain": "Equity", "emirEligible": false, "exchangeSymbol": "NFLX", "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": false, "priceNotation": "MONE", "pricingReferences": {"ICE": "isin/US64110L1061/USD"}, "quantityNotation": "UNIT", "venueName": "<PERSON><PERSON><PERSON>"}, "instrumentClassification": "ESVUFR", "instrumentFullName": "NETFLIX ORD", "instrumentIdCode": "US64110L1061", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300Y7VHGU0I7CE873", "notionalCurrency1": "USD", "sourceKey": "refinitivInstruments.equities.ARCX.********", "venue": {"admissionToTradingOrFirstTradeDate": "2003-04-04T00:00:00", "tradingVenue": "ARCX"}}}, "buyerDecisionMaker": [{"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}], "id": "PriceOutliers_UC5.1", "&timestamp": *************, "buyerDecisionMakerFileIdentifier": "lei:894500wota5040khgx73", "buySell": "1", "clientFileIdentifier": "account:scar", "marketIdentifiers": [{"labelId": "US64110L1061", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:scar", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "buyerDecisionMaker", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:wonderland", "path": "seller", "type": "ARRAY"}, {"labelId": "account:wonderland", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:mufasa", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:scar", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:mufasa", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "PriceOutliers_UC5.1", "internalOrderIdCode": "PriceOutliers_UC5.1", "orderIdCode": "PriceOutliers_UC5.1"}, "sellerFileIdentifier": "account:wonderland", "&key": "Order:PriceOutliers_UC5.1:1:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": 971490.**********, "BGN": 1136728.**********, "BRL": 3115803.*********, "CAD": 853679.**********, "CHF": 559413.**********, "CNY": 4590156.*********, "CZK": ********.********, "DKK": 4330879.*********, "EUR": 581209.**********, "GBP": 500787.**********, "HKD": 4983518.********, "HUF": *********.********, "IDR": **********.910414, "ILS": 2344306.*********, "INR": ********.********, "JPY": ********.7822287, "KRW": *********.4049526, "MXN": ********.*********, "MYR": 2910462.**********, "NOK": 6479608.*********, "NZD": 1046408.**********, "PHP": ********.********, "PLN": 2565340.*********, "RON": 2876403.*********, "SEK": 6778350.*********, "SGD": 856120.**********, "THB": ********.********, "TRY": ********.********, "USD": 638400.0, "ZAR": ********.*********, "refRateDate": "2023-08-07T13:15:00Z"}, "native": 638400.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "traderFileIdentifier": "account:mufasa", "tradersAlgosWaiversIndicators": {"executionWithinFirmFileIdentifier": "account:mufasa"}, "priceFormingData": {"initialQuantity": 1596.0, "remainingQuantity": 0.0}, "&hash": "e4f3154564419f8d5dbe63907ff85479459e0bb7fc8a356a0e0dea665d3edf45", "counterpartyFileIdentifier": "account:wonderland", "&validationErrors": [{"field_path": "transactionDetails.tradingDateTime", "message": "`Trading Date Time´ must be less than the current time", "code": "SE_DV-15", "category": "Trade details", "modules_affected": ["Best Execution", "Orders", "Market Abuse", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "counterparty", "message": "`Counterparty Name` must be populated when `Counterparty ID` is populated", "code": "SE_DV-87", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "buyer.0.&key", "message": "`Buyer` must be populated", "code": "SE_DV-188", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "seller.0.&key", "message": "`Seller` must be populated", "code": "SE_DV-189", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}], "&user": "system"}, "sort": ["s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv"]}, {"_index": "mar_order_20230223", "_type": "Order", "_id": "PriceOutliers_UC5.2:2:NEWO", "_score": null, "_source": {"date": "2023-08-08", "sourceKey": "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv", "&id": "PriceOutliers_UC5.2:2:NEWO", "transactionDetails": {"buySellIndicator": "SELL", "netAmount": *********.0, "priceCurrency": "USD", "priceNotation": "MONE", "quantity": 357951.0, "quantityCurrency": "USD", "quantityNotation": "UNIT", "tradingCapacity": "DEAL", "tradingDateTime": "2023-08-08T13:41:30Z", "venue": "XNAS"}, "buyerFileIdentifier": "account:wonderland", "reportDetails": {"executingEntity": {"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "fileIdentifier": "lei:894500wota5040khgx73", "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}, "transactionRefNo": "PRICEOUTLIERSUC52PRICEOUTLIERSUC52T20230808SELL"}, "timestamps": {"orderReceived": "2023-08-08T13:41:30Z", "orderStatusUpdated": "2023-08-08T13:41:30Z", "orderSubmitted": "2023-08-08T13:41:30Z", "tradingDateTime": "2023-08-08T13:41:30Z"}, "executionDetails": {"buySellIndicator": "SELL", "orderStatus": "NEWO", "orderType": "Market", "outgoingOrderAddlInfo": "Client ID- Ken;Counterparty ID- Wonderland;Trader ID- Barbie", "tradingCapacity": "DEAL"}, "sourceIndex": "1", "marDetails": {"isPersonalAccountDealing": false}, "sellerDecisionMakerFileIdentifier": "lei:894500wota5040khgx73", "&model": "Order", "&version": 1, "instrumentDetails": {"instrument": {"&id": "*******************", "&key": "VenueInstrument:*******************:**********", "cfiAttribute1": "Voting", "cfiAttribute2": "Free", "cfiAttribute3": "<PERSON>y Paid", "cfiAttribute4": "Registered", "cfiCategory": "Equity", "cfiGroup": "Common/Ordinary shares", "derivative": {"deliveryType": "CASH"}, "ext": {"bestExAssetClassMain": "Equity", "emirEligible": false, "exchangeSymbol": "NFLX", "instrumentIdCodeType": "ID", "instrumentUniqueIdentifier": "*******************", "mifirEligible": false, "priceNotation": "MONE", "pricingReferences": {"ICE": "isin/US64110L1061/USD"}, "quantityNotation": "UNIT", "venueName": "<PERSON><PERSON><PERSON>"}, "instrumentClassification": "ESVUFR", "instrumentFullName": "NETFLIX ORD", "instrumentIdCode": "US64110L1061", "isCreatedThroughFallback": false, "issuerOrOperatorOfTradingVenueId": "549300Y7VHGU0I7CE873", "notionalCurrency1": "USD", "sourceKey": "refinitivInstruments.equities.ARCX.********", "venue": {"admissionToTradingOrFirstTradeDate": "2003-04-04T00:00:00", "tradingVenue": "ARCX"}}}, "id": "PriceOutliers_UC5.2", "&timestamp": *************, "buySell": "2", "clientFileIdentifier": "account:ken", "marketIdentifiers": [{"labelId": "US64110L1061", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "*******************", "path": "instrumentDetails.instrument", "type": "OBJECT"}, {"labelId": "account:wonderland", "path": "buyer", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "reportDetails.executingEntity", "type": "OBJECT"}, {"labelId": "account:ken", "path": "seller", "type": "ARRAY"}, {"labelId": "lei:894500wota5040khgx73", "path": "sellerDecisionMaker", "type": "ARRAY"}, {"labelId": "account:wonderland", "path": "counterparty", "type": "OBJECT"}, {"labelId": "account:barbie", "path": "tradersAlgosWaiversIndicators.executionWithinFirm", "type": "OBJECT"}, {"labelId": "account:ken", "path": "clientIdentifiers.client", "type": "ARRAY"}, {"labelId": "account:barbie", "path": "trader", "type": "ARRAY"}], "hierarchy": "Standalone", "orderIdentifiers": {"aggregatedOrderId": "PriceOutliers_UC5.2", "internalOrderIdCode": "PriceOutliers_UC5.2", "orderIdCode": "PriceOutliers_UC5.2"}, "sellerDecisionMaker": [{"&id": "$this", "&key": "AccountFirm:$this:*************", "client": {"isAggregatedClientAccount": false, "metFaceToFace": false}, "details": {"firmStatus": "ACTIVE", "inEEA": false, "isEmirDelegatedReporting": false, "mifidRegistered": true, "parentOfCollectiveInvestmentSchema": false, "retailOrProfessional": "N/A"}, "firmIdentifiers": {"branchCountry": "GB", "deaAccess": false, "isIsda": false, "kycApproved": false, "lei": "894500WOTA5040KHGX73"}, "name": "SteelEye", "sinkIdentifiers": {"tradeFileIdentifiers": [{"id": "894500WOTA5040KHGX73", "label": "lei"}]}, "uniqueIds": ["lei:894500wota5040khgx73"]}], "sellerFileIdentifier": "account:ken", "&key": "Order:PriceOutliers_UC5.2:2:NEWO:*************", "bestExecutionData": {"largeInScale": false, "orderVolume": {"ecbRefRate": {"AUD": *********.********, "BGN": *********.3066278, "BRL": *********.7053896, "CAD": *********.********, "CHF": *********.********, "CNY": **********.6911871, "CZK": **********.4450107, "DKK": **********.896941, "EUR": *********.********, "GBP": *********.********, "HKD": **********.7246904, "HUF": ***********.91478, "IDR": *************.8657, "ILS": *********.7643845, "INR": ***********.054625, "JPY": ***********.438454, "KRW": ************.4188, "MXN": **********.427531, "MYR": *********.6627822, "NOK": **********.272032, "NZD": *********.5134741, "PHP": **********.369993, "PLN": *********.2498178, "RON": *********.2053896, "SEK": **********.3317552, "SGD": *********.603059, "THB": **********.085943, "TRY": **********.652221, "USD": *********.0, "ZAR": **********.5917697, "refRateDate": "2023-08-07T13:15:00Z"}, "native": *********.0, "nativeCurrency": "USD"}, "pendingDisclosure": false, "rts27ValueBand": 1}, "dataSourceName": "SteelEyeTradeBlotter", "traderFileIdentifier": "account:barbie", "tradersAlgosWaiversIndicators": {"executionWithinFirmFileIdentifier": "account:barbie", "shortSellingIndicator": "SELL"}, "priceFormingData": {"initialQuantity": 357951.0, "remainingQuantity": 0.0}, "&hash": "02d39527b701eff61100ba910d9a6da1f438ec9500b4c557da47a88800117cc1", "counterpartyFileIdentifier": "account:wonderland", "&validationErrors": [{"field_path": "transactionDetails.tradingDateTime", "message": "`Trading Date Time´ must be less than the current time", "code": "SE_DV-15", "category": "Trade details", "modules_affected": ["Best Execution", "Orders", "Market Abuse", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "counterparty", "message": "`Counterparty Name` must be populated when `Counterparty ID` is populated", "code": "SE_DV-87", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders"], "severity": "MEDIUM", "source": "steeleye"}, {"field_path": "buyer.0.&key", "message": "`Buyer` must be populated", "code": "SE_DV-188", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}, {"field_path": "seller.0.&key", "message": "`Seller` must be populated", "code": "SE_DV-189", "category": "Parties", "modules_affected": ["Best Execution", "Market Abuse", "Orders", "Transaction Reporting"], "severity": "CRITICAL", "source": "steeleye"}], "&user": "system"}, "sort": ["s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.priceoutliers.5.csv"]}]}}