# ruff: noqa: E501
import pandas as pd
import pytest
import re
from datetime import datetime
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.models import TimeUnit
from market_abuse_algorithms.strategy.message_volume.static import (
    MCG_GROUPING_FIELDS_MAP,
)
from market_abuse_algorithms.strategy.message_volume.strategy import Strategy
from pathlib import Path
from se_elastic_schema.static.mifid2 import OrderStatus, ValidityPeriod
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture
def fake_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def custom_date_parser():
        return lambda x: datetime.strptime(x, "%Y-%d-%m %H:%M:%S")

    def _foo(*args):
        result = pd.read_csv(
            *args,
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderStatusUpdated"],
            date_parser=custom_date_parser(),
        )
        return result

    return _foo


class TestMessageVolume:
    def test_mcg_map(self, helpers):
        """
        Test if the mapping created is correct
        :return:
        """

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.FILL, OrderStatus.PARF],
            messageCountGrouping=[
                "CLIENT_ID",
                "CLIENT_ASSET_CLASS",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        fields_to_group = strategy.queries._get_fields_to_include(
            message_grouping_map=MCG_GROUPING_FIELDS_MAP
        )

        assert sorted(fields_to_group) == sorted(
            [
                OrderField.CLIENT_FILE_IDENTIFIER,
                OrderField.CLIENT_IDENT_CLIENT_NAME,
                OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            ]
        )

    def test_get_base_query(self, helpers):
        """
        Test if the mapping created is correct
        :return:
        """

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.FILL, OrderStatus.PARF],
            messageCountGrouping=[
                "CLIENT_ID",
                "CLIENT_ASSET_CLASS",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        fields_to_group = strategy.queries._get_fields_to_include(
            message_grouping_map=MCG_GROUPING_FIELDS_MAP
        )

        query = strategy.queries._get_base_query(mcg_include_fields=fields_to_group)

        assert "clientFileIdentifier" in query.to_dict()["_source"]["includes"]
        assert "clientIdentifiers.client.&id" not in query.to_dict()["_source"]["includes"]

    def test_executions_client_triple_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping=
            by [Client]
            by [Client] by [Asset Class]
            by [Client] by [Same Instrument]
        Order Events = Execution’s
        Order Date == “20201001”
        """
        number_of_records = 10
        number_of_scenarios = 3

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.FILL, OrderStatus.PARF],
            messageCountGrouping=[
                "CLIENT",
                "CLIENT_ASSET_CLASS",
                "CLIENT_SAME_INSTRUMENT",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1601596800000, "start": 1601510400000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        # TODO: Check if thresholds are correct

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_executions_client_triple_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberExecutions"]
                == number_of_records
            )

    def test_newo_client_triple_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping=
            by [Client]
            by [Client] by [Asset Class]
            by [Client] by [Same Instrument]
        Order Events = New
        Order Date == “20201002”
        """
        number_of_records = 10
        number_of_scenarios = 2

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=[
                "CLIENT",
                "CLIENT_ASSET_CLASS",
                "CLIENT_SAME_INSTRUMENT",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1601683200000, "start": 1601596800000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_client_triple_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberNewos"]
                == number_of_records
            )

    def test_newo_client_single_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping = by [Client]
        Order Events = New
        Order Date == “20201005”
        """
        number_of_records = 10
        number_of_scenarios = 1

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=["CLIENT"],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1601942400000, "start": 1601856000000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_client_single_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberNewos"]
                == number_of_records
            )

    def test_newo_instrument_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping = by [Instrument]
        Order Events = New
        Order Date == “20201006”
        """
        number_of_records = 10
        number_of_scenarios = 1

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=["INSTRUMENT"],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602028800000, "start": 1601942400000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_instrument_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberNewos"]
                == number_of_records
            )

    def test_newo_order_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping = by [Order]
        Order Events = New
        Order Date == “20201007”
        """
        number_of_scenarios = 0

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=["ORDER_ID"],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602115140000, "start": 1602028800000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_order_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

    def test_executions_trader_triple_grouping(self, helpers, fake_data):
        """
        :::: NO DATA ::::

        Test Message Volume with the following set of parameters

            Time Window=10S
            Minimum Message Count = 10
            Message Count Grouping =
                by [Trader]
                by [Trader] by [Asset Class]
                by [Trader] by [Same Instrument]
            Order Events = Execution’s
            Order Date == “20201008”

        """
        number_of_records = 0
        number_of_scenarios = 0

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.FILL, OrderStatus.PARF],
            messageCountGrouping=[
                "TRADER",
                "TRADER_SAME_INSTRUMENT",
                "TRADER_ASSET_CLASS",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602201600000, "start": 1602115200000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_order_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberExecutions"]
                == number_of_records
            )

    def test_newo_trader_triple_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping =
            by [Trader]
            by [Trader] by [Asset Class]
            by [Trader] by [Same Instrument]
        Order Events = New
        Order Date == “20201009”
        """
        number_of_records = 10
        number_of_scenarios = 2

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=[
                "TRADER",
                "TRADER_SAME_INSTRUMENT",
                "TRADER_ASSET_CLASS",
            ],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602288000000, "start": 1602201600000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_trader_triple_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberNewos"]
                == number_of_records
            )

    def test_newo_trader_single_grouping(self, helpers, fake_data):
        """Test Message Volume with the following set of parameters.

        Time Window=10S
        Minimum Message Count = 10
        Message Count Grouping =
            by [Trader]
        Order Events = New
        Order Date == “20201010”
        """
        number_of_records = 10
        number_of_scenarios = 1

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=["TRADER"],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=False,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602374400000, "start": 1602288000000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_newo_trader_single_grouping.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"]["numberNewos"]
                == number_of_records
            )

    def test_exclusion_validity_periods(self):
        """
        Checks if the fields needed in exclusion validity exist
        :return: None
        """

        assert ValidityPeriod.IOCV
        assert ValidityPeriod.FOKV

    def test_get_query_exclusion_validity_periods(self, helpers):
        """
        Checks if the query has the correct terms for exclusion of the validity periods
        :return: None
        """

        thresholds = dict(
            minimumMessageCount=10,
            orderEvents=[OrderStatus.NEWO],
            messageCountGrouping=["TRADER"],
            timeWindow={"value": 10, "unit": TimeUnit.SECONDS},
            excValidityPeriods=True,
            restrictAlerts=False,
        )

        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1602374400000, "start": 1602288000000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        query = strategy.queries._get_base_query([OrderField.CLIENT_IDENT_CLIENT_NAME])

        json = query.to_json()

        iocv_match = re.search(ValidityPeriod.IOCV, json)
        fokv_match = re.search(ValidityPeriod.FOKV, json)
        negative_match = re.search(ValidityPeriod.GTSV, json)

        assert iocv_match is not None
        assert fokv_match is not None
        assert negative_match is None

    def test_unknown_venue(self):
        data = pd.read_csv(TEST_DATA.joinpath("test_unknown_venue.csv"))
        data[OrderField.TRX_DTL_ULTIMATE_VENUE] = data[OrderField.TRX_DTL_ULTIMATE_VENUE].fillna(
            "Unknown Venue"
        )

        data_changed = pd.read_csv(TEST_DATA.joinpath("test_unknown_venue_changed.csv"))
        assert data.equals(data_changed)

    def test__apply_strategy_mini_batch(self, helpers):
        thresholds = {
            "minimumMessageCount": 3,
            "orderEvents": [
                "NEWO",
                "PARF",
                "FILL",
                "REMO",
                "REMA",
                "REME",
                "REMH",
                "CHME",
                "CHMO",
                "CAMO",
                "CAME",
            ],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["INSTRUMENT"],
            "restrictAlerts": True,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        strategy = Strategy(context=context)

        data = pd.read_csv(TEST_DATA.joinpath("test_unknown_venue.csv"))

        strategy._apply_strategy_mini_batch(data)

        assert len(strategy.scenarios) == 3

    def test_1_2_csv(self, helpers):
        """Test the Test Case 1.2, as mentioned in the Message volume
        confluence page."""

        thresholds = {
            "minimumMessageCount": 10,
            "orderEvents": ["PARF", "FILL"],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["TRADER"],
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.messageVolume.1.csv"
                        ]
                    }
                }
            }
        }

        data = pd.read_csv(TEST_DATA.joinpath("test_1_2.csv"))

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._apply_strategy_mini_batch(data)

        scenerio = strategy.scenarios

        assert len(scenerio) == 1
        assert len(scenerio[0].json["records"]["orderStates"]) == 10

    def test_2_1_csv(self, helpers):
        """Test the Test Case 2.1, as mentioned in the Message volume
        confluence page."""

        thresholds = {
            "minimumMessageCount": 10,
            "orderEvents": ["NEWO"],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["TRADER"],
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.messageVolume.2.csv"
                        ]
                    }
                }
            }
        }

        data = pd.read_csv(TEST_DATA.joinpath("test_2_1.csv"))

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._apply_strategy_mini_batch(data)

        scenerio = strategy.scenarios

        assert len(scenerio) == 1
        assert len(scenerio[0].json["records"]["orders"]) == 10

    def test_4_2_csv(self, helpers):
        """Test the Test Case 4.2, as mentioned in the Message volume
        confluence page."""

        thresholds = {
            "minimumMessageCount": 10,
            "orderEvents": ["NEWO"],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["TRADER"],
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.messageVolume.4.csv"
                        ]
                    }
                }
            }
        }

        data = pd.read_csv(TEST_DATA.joinpath("test_4_2.csv"))

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._apply_strategy_mini_batch(data)

        scenerio = strategy.scenarios

        assert len(scenerio) == 1
        assert len(scenerio[0].json["records"]["orders"]) == 10

    def test_5_2_csv(self, helpers):
        """Test the Test Case 5.2, as mentioned in the Message volume
        confluence page."""

        thresholds = {
            "minimumMessageCount": 40,
            "orderEvents": ["REMA", "REME", "REMH"],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["TRADER"],
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.messageVolume.5.csv"
                        ]
                    }
                }
            }
        }

        data = pd.read_csv(TEST_DATA.joinpath("test_5_2.csv"))

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._apply_strategy_mini_batch(data)

        scenerio = strategy.scenarios

        assert len(scenerio) == 1
        assert len(scenerio[0].json["records"]["orderStates"]) == 40

    def test_cases_to_analyse(self, helpers):
        thresholds = {
            "minimumMessageCount": 10,
            "orderEvents": [
                "NEWO",
                "PARF",
                "FILL",
                "REMO",
                "REMA",
                "REME",
                "REMH",
                "CHME",
                "CHMO",
                "CAMO",
                "CAME",
            ],
            "timeWindow": {"unit": "seconds", "value": 600},
            "messageCountGrouping": ["INSTRUMENT"],
            "restrictAlerts": True,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})

        strategy = Strategy(context=context)

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [
            ["US3682872078", "US02079K3059"]
        ]

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = pd.read_csv(
            TEST_DATA.joinpath("test_cases_to_analyse.csv")
        )

        for data in strategy.queries.cases_to_analyse(mcg_fields_map=MCG_GROUPING_FIELDS_MAP):
            assert not data.empty
            assert data.shape == (11, 20)
            assert OrderStatus.NEWO in data["executionDetails.orderStatus"].unique().tolist()
