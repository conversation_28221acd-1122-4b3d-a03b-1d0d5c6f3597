import pytest
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query import (
    Queries,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.static import (
    CategoryEvaluationType,
    DayAndOrderEvaluationType,
    GeneralEvaluationType,
    MarketDataEvaluationType,
    QuantityEvaluationType,
)
from unittest.mock import MagicMock


class FakeMarketClient(MarketDataClient):
    pass


class TestBaseStrategyContext:
    def test_evaluate_thresholds_not_failed(self, helpers):
        context = StrategyContext(
            created_by="random.user",
            tenant="iris",
            commit_audit=True,
            filters={
                "bool": {
                    "must": [{"range": {"&timestamp": {"gte": "2022-09-16T10:21:29.225298Z"}}}],
                    "must_not": [],
                }
            },
            requested_by="Test",
            realm="iris.uat.steeleye.co",
            thresholds=Thresholds(
                assetClass=None,
                categoryEvaluationType=CategoryEvaluationType.MARKET,
                dayAndOrderEvaluationType=DayAndOrderEvaluationType.DAY,
                executionNotionalValueCurrency=None,
                generalEvaluationType=GeneralEvaluationType.EXECUTING_ENTITY,
                lookBackPeriod=None,
                marketDataEvaluationType=MarketDataEvaluationType.DAY_TRADED_VOLUME,
                minimumQuantity=10.0,
                minNumberOfDaysOrderFlow=None,
                normaliseBehaviour=None,
                percentageAdv=0.1,
                quantityEvaluationType=QuantityEvaluationType.ORDER_QUANTITY,
            ),
            watch_id="foo123",
            watch_execution_id="bar456",
        )

        context.thresholds = Thresholds.validate(context.thresholds)

        assert context.created_by == "random.user"
        assert context.watch_id == "foo123"
        assert context.watch_execution_id == "bar456"

    def test_evaluate_thresholds_failed(self, helpers):
        context = helpers.get_context(
            thresholds={
                "categoryEvaluationType": "market",
                "dayAndOrderEvaluationType": "order",
                "lookBackPeriod": {"unit": "days", "value": 10},
                "marketDataEvaluationType": "averageDailyTradedVolume",
                "marketPriceImpact": 0.01,
                "minimumQuantity": 500.0,
                "percentageAdv": 2.5,
                "quantityEvaluationType": "orderQuantity",
            },
            filters={},
        )

        import market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()  # noqa: E501
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()  # noqa: E501

        with pytest.raises(Exception):
            AbstractStrategy(
                context=context,
                strategy_name=StrategyName.SUSPICIOUS_LARGE_ORDER_VOLUME_V2,
                thresholds_class=Thresholds,
                queries_class=Queries,
            )
