# ruff: noqa: E501
import addict
import market_abuse_algorithms.strategy.base.query as baseQuery
import pandas as pd
import pytest
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import (
    OrderBaseQuery,
    OrderExecutionsQuery,
    OrderQuery,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import QueryAggScripts, StrategyName
from market_abuse_algorithms.strategy.parking.models import Thresholds
from pathlib import Path
from tests_market_abuse_algorithms.strategy.base.mocks import SDP
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture
def mock_context():
    return StrategyContext(
        created_by="test",
        commit_audit=True,
        requested_by="test",
        realm="mar",
        look_back_period_ts="2020-01-01",
        tenant="mar",
    )


@pytest.fixture
def mock_audit():
    return Audit(
        context=StrategyContext(
            created_by="test",
            commit_audit=True,
            requested_by="test",
            realm="mar",
            look_back_period_ts="2020-01-01",
            tenant="mar",
        ),
        strategy_name=StrategyName.PARKING,
        es_client=SDP(realm="mar"),
    )


class TestBaseQuery:
    def test_update_query_with_required_fields_inspect(self):
        q = OrderExecutionsQuery()
        q.size(10)
        q.instrument_id("DE000C1K1UM8")

        inspect_query = baseQuery.BaseQuery.get_query_with_required_fields(
            q,
            fields=[
                OrderField.META_ID,
                OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
                [OrderField.PARTICIPANTS_VALUE_ID, OrderField.INST_FULL_NAME],
            ],
            inspect=True,
        )

        assert inspect_query.to_dict() == {
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": "&expiry"}}],
                    "must": [{"terms": {"&model": ["Order"]}}],
                    "should": [
                        {
                            "terms": {
                                "instrumentDetails.instrument.instrumentIdCode": ["DE000C1K1UM8"]
                            }
                        },
                        {
                            "terms": {
                                "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": [
                                    "DE000C1K1UM8"
                                ]
                            }
                        },
                        {
                            "terms": {
                                "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": [
                                    "DE000C1K1UM8"
                                ]
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            },
            "aggs": {
                "&id": {"filter": {"exists": {"field": "&id"}}},
                "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode": {
                    "filter": {
                        "exists": {
                            "field": "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode"
                        }
                    }
                },
                "participants.value.&id --|-- instrumentDetails.instrument.instrumentFullName": {
                    "filter": {
                        "bool": {
                            "should": [
                                {"exists": {"field": "participants.value.&id"}},
                                {
                                    "exists": {
                                        "field": "instrumentDetails.instrument.instrumentFullName"
                                    }
                                },
                            ],
                            "minimum_should_match": 1,
                        }
                    }
                },
            },
            "size": 0,
        }

    def test_update_query_with_required_fields_no_inspect(self):
        q = OrderExecutionsQuery()
        q.size(10)
        q.instrument_id("DE000C1K1UM8")

        inspect_query = baseQuery.BaseQuery.get_query_with_required_fields(
            q,
            fields=[
                OrderField.META_ID,
                OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
                [OrderField.PARTICIPANTS_VALUE_ID, OrderField.INST_FULL_NAME],
            ],
        )

        assert inspect_query.to_dict() == {
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": "&expiry"}}],
                    "must": [
                        {"terms": {"&model": ["Order"]}},
                        {"exists": {"field": "&id"}},
                        {
                            "exists": {
                                "field": "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode"
                            }
                        },
                        {
                            "bool": {
                                "should": [
                                    {"exists": {"field": "participants.value.&id"}},
                                    {
                                        "exists": {
                                            "field": "instrumentDetails.instrument.instrumentFullName"
                                        }
                                    },
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                    ],
                    "should": [
                        {
                            "terms": {
                                "instrumentDetails.instrument.instrumentIdCode": ["DE000C1K1UM8"]
                            }
                        },
                        {
                            "terms": {
                                "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": [
                                    "DE000C1K1UM8"
                                ]
                            }
                        },
                        {
                            "terms": {
                                "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": [
                                    "DE000C1K1UM8"
                                ]
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            },
            "size": 10,
        }

    def test_get_instruments_combinations_negative(self, mock_context, mock_audit):
        mock_context.look_back_period_ts = "2024-03-04 00:00:00"
        mock_context.thresholds = Thresholds(
            assetClass="test",
            evaluationType="Client",
            lookBackPeriod=2,
            quantityDifference=0.5,
        )
        q = OrderBaseQuery(model="Order")

        def fake_executions() -> addict.Dict:
            dummy_instrument_agg = {
                "hits": {"total": 1900, "max_score": 18.843908, "hits": 0},
                "aggregations": {
                    "instrument": {
                        "doc_count_error_upper_bound": 0,
                        "sum_other_doc_count": 0,
                        "buckets": [{"key": "ID1000095706", "doc_count": 1900}],
                    }
                },
            }

            fake_data = addict.Dict(dummy_instrument_agg)
            return fake_data

        bq = baseQuery.BaseQuery(context=mock_context, audit=mock_audit)

        bq.sdp_repository.search_query = MagicMock()
        bq.sdp_repository.search_query.return_value = fake_executions()

        instrument_combinations = bq.get_instruments_combinations(
            query=q,
            agg_script=QueryAggScripts.ISIN_PRIORITY_SCRIPT,
        )

        assert len(instrument_combinations) == 1

    def test_get_instruments_combinations_positive(self, mock_context, mock_audit):
        mock_context.look_back_period_ts = "2024-03-04 00:00:00"
        mock_context.thresholds = Thresholds(
            assetClass="test",
            evaluationType="Client",
            lookBackPeriod=2,
            quantityDifference=0.5,
        )
        q = OrderBaseQuery(model="Order")

        def fake_executions() -> addict.Dict:
            dummy_instrument_agg = {
                "hits": {"total": 1900, "max_score": 18.843908, "hits": 0},
                "aggregations": {
                    "instrument": {
                        "doc_count_error_upper_bound": 0,
                        "sum_other_doc_count": 0,
                        "buckets": [{"key": "ID1000095706", "doc_count": 900}],
                    }
                },
            }

            fake_data = addict.Dict(dummy_instrument_agg)
            return fake_data

        bq = baseQuery.BaseQuery(context=mock_context, audit=mock_audit)

        bq.sdp_repository.search_query = MagicMock()
        bq.sdp_repository.search_query.return_value = fake_executions()

        instrument_combinations = bq.get_instruments_combinations(
            query=q,
            agg_script=QueryAggScripts.ISIN_PRIORITY_SCRIPT,
        )

        assert len(instrument_combinations) == 1

    def test_filter_newos_by_child_executions_fields(self, mock_context, mock_audit):
        def return_empty_executions():
            return pd.DataFrame()

        mock_context.look_back_period_ts = "2024-01-01 00:00:00"
        mock_context.thresholds = Thresholds(
            assetClass="test",
            evaluationType="Client",
            lookBackPeriod=2,
            quantityDifference=0.5,
        )
        orders_data = pd.read_csv(
            TEST_DATA.joinpath("original_orders_df.csv"),
            parse_dates=[OrderField.TS_ORD_SUBMITTED, OrderField.DATE],
        )

        executions_data = pd.read_csv(TEST_DATA.joinpath("executions_query_output.csv"))

        fields = {"executionDetails.orderStatus": ["FILL", "PARF"]}

        query = baseQuery.BaseQuery(context=mock_context, audit=mock_audit)

        query.sdp_repository.search_after_query = MagicMock()
        query.sdp_repository.search_after_query.return_value = return_empty_executions()

        empty_result = query.filter_newos_by_child_executions_fields(
            orders_df=orders_data, fields=fields, mode="filter"
        )

        assert empty_result.empty

        # Test which gets executions but don't have meta parent column
        def return_executions():
            return executions_data.drop(columns=[OrderField.META_PARENT])

        query.sdp_repository.search_after_query = MagicMock()
        query.sdp_repository.search_after_query.return_value = return_executions()

        empty_result = query.filter_newos_by_child_executions_fields(
            orders_df=orders_data, fields=fields, mode="filter"
        )

        assert empty_result.empty

        # Test which gets executions with META_PARENT column, but it's empty
        def return_executions():
            copy_executions_data = executions_data.copy()
            copy_executions_data.loc[:, OrderField.META_PARENT] = pd.NA
            return copy_executions_data

        query.sdp_repository.search_after_query = MagicMock()
        query.sdp_repository.search_after_query.return_value = return_executions()

        empty_result = query.filter_newos_by_child_executions_fields(
            orders_df=orders_data, fields=fields, mode="filter"
        )

        assert empty_result.empty

        # Test which gets executions mapped to parents
        def return_executions():
            return executions_data

        query.sdp_repository.search_after_query = MagicMock()
        query.sdp_repository.search_after_query.return_value = return_executions()

        result = query.filter_newos_by_child_executions_fields(
            orders_df=orders_data, fields=fields, mode="filter"
        )

        assert result.shape[0] == 2

        # Test which gets with must_not mode
        def return_executions():
            return executions_data

        query.sdp_repository.search_after_query = MagicMock()
        query.sdp_repository.search_after_query.return_value = return_executions()

        empty_result = query.filter_newos_by_child_executions_fields(
            orders_df=orders_data, fields=fields, mode="must_not"
        )

        assert empty_result.empty

    def test_inspect_required_fields(self, mock_context, mock_audit):
        """Test which verifies that a normal return of search query is not
        breaking on inspect_required_field method."""
        mock_context.look_back_period_ts = "2024-01-01 00:00:00"
        mock_context.thresholds = Thresholds(
            assetClass="test",
            evaluationType="Client",
            lookBackPeriod=2,
            quantityDifference=0.5,
        )
        expected_result = {
            "took": 7,
            "timed_out": False,
            "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
            "hits": {
                "total": {"value": 6, "relation": "eq"},
                "max_score": None,
                "hits": [],
            },
            "aggregations": {"&key": {"doc_count": 6}},
        }

        query = baseQuery.BaseQuery(context=mock_context, audit=mock_audit)

        def return_executions():
            return expected_result

        query.sdp_repository.search_query = MagicMock()
        query.sdp_repository.search_query.return_value = return_executions()

        query_to_be_inspected = OrderQuery()
        output = query.inspect_required_fields(
            query=query_to_be_inspected, fields=[OrderField.META_KEY]
        )

        assert output is None
