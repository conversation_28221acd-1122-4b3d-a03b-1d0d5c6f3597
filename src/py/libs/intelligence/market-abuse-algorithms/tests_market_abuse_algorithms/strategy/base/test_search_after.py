import addict
import market_abuse_algorithms.data_source.repository.sdp.es_sdp as es_sdp
import market_abuse_algorithms.data_source.repository.srp.base as base
import pytest
from se_elasticsearch.repository.abstractions import AbstractElasticsearchRepository
from se_elasticsearch.repository.helpers import Meta
from unittest.mock import MagicMock


class FakeEsClient(AbstractElasticsearchRepository):  # type: ignore[misc]
    def __init__(self, version: int):
        self.version = version
        self._meta = Meta()
        self._client = None

    def get_elasticsearch_version(self):
        return self.version


@pytest.fixture(params=[250, 5000])
def fake_es_client_6_for_run_query(request) -> AbstractElasticsearchRepository:
    class FakeIndex:
        def __init__(self, index: str) -> None:
            self.index = index

        @classmethod
        def exists(cls, index):
            if index:
                return True
            return False

    class FakeCreateClient:
        def __init__(self, index: FakeIndex) -> None:
            self.indices = index

        def create(self, **kwargs):
            # this returns nothing for now, think of what to implement here later
            return

        def delete(self, **kwargs):
            return

    class FakeEsClient6(FakeEsClient):
        def __init__(self, version: int, total_number_of_alerts: int):
            super().__init__(version)
            self._client = self._create_client()
            self.total_number_of_alerts = total_number_of_alerts
            self.expected_hits = total_number_of_alerts

        def search(self, body, **kwargs):
            size = min(body["size"], self.total_number_of_alerts)
            self.total_number_of_alerts -= size

            hit = {
                "_index": "fake-tenant_dev_steeleye_co",
                "_type": "SurveillanceWatch",
                "_id": "AYC0AQy-uzuvAlmqUT6I",
                "_score": 1.0,
                "_source": {"hit": {"&id": "asd1"}},
            }
            hits = [hit] * size

            return addict.Dict(
                {
                    "took": 36,
                    "timed_out": "false",
                    "_shards": {
                        "total": 1,
                        "successful": 1,
                        "skipped": 0,
                        "failed": 0,
                    },
                    "hits": {
                        "total": {"value": self.expected_hits},
                        "max_score": 1.0,
                        "hits": hits,
                    },
                }
            )

        def count(self, body, index, request_timeout):
            return {"count": self.expected_hits}

        def _create_client(self):
            return FakeCreateClient(index=FakeIndex("fake_index"))

        @property
        def client(self):
            return self._client

    FakeEsClient6.__abstractmethods__ = set()
    return FakeEsClient6(version=6, total_number_of_alerts=request.param)


def test_exception_for_search_after_query(fake_es_client_6_for_run_query):
    base.SRPClient._create_client = MagicMock()
    base.SRPClient._create_client.return_value = fake_es_client_6_for_run_query

    repo = base.SRPClient(index="mar:order")

    query = {
        "query": {
            "bool": {
                "must": [{"term": {"_id": "test_id"}}],
            }
        },
        "size": 50,
    }

    with pytest.raises(Exception) as e:
        repo.search_after_query(
            query=query,
            query_total_hits=0,
        )

    assert "value of query_total_hits can not be 0" in str(e.value).lower()


@pytest.mark.parametrize(
    "query_total_hits_param",
    [
        50,
        5001,
        -1,
        None,
    ],
)
def test_search_after_query(fake_es_client_6_for_run_query, query_total_hits_param):
    base.SRPClient._create_client = MagicMock()
    base.SRPClient._create_client.return_value = fake_es_client_6_for_run_query

    repo = base.SRPClient(index="mar:order")

    query = {
        "query": {
            "bool": {
                "must": [{"term": {"_id": "test_id"}}],
            }
        },
        "size": 50,
    }

    if query_total_hits_param is None:
        elastic_result = repo.search_after_query(query=query)
        query_total_hits_param = -1
    else:
        elastic_result = repo.search_after_query(
            query=query,
            query_total_hits=query_total_hits_param,
        )

    if query_total_hits_param is None or query_total_hits_param < 0:
        expected_number = fake_es_client_6_for_run_query.expected_hits
    else:
        expected_number = min(fake_es_client_6_for_run_query.expected_hits, query_total_hits_param)

    assert len(elastic_result) == expected_number


@pytest.mark.parametrize(
    "query_total_hits_param",
    [
        50,
        5001,
        -1,
        None,
    ],
)
def test_search_after_query_es8(fake_es_client_6_for_run_query, query_total_hits_param):
    es_sdp.SDP._create_client = MagicMock()
    es_sdp.SDP._create_client.return_value = fake_es_client_6_for_run_query

    repo = es_sdp.SDP(tenant="mar")

    query = {
        "query": {
            "bool": {
                "must": [{"term": {"_id": "test_id"}}],
            }
        },
        "size": 50,
    }

    if query_total_hits_param is None:
        elastic_result = repo.search_after_query(
            query=query,
        )
        query_total_hits_param = -1
    else:
        elastic_result = repo.search_after_query(
            query=query,
            query_total_hits=query_total_hits_param,
        )

    if query_total_hits_param is None or query_total_hits_param < 0:
        expected_number = fake_es_client_6_for_run_query.expected_hits
    else:
        expected_number = min(fake_es_client_6_for_run_query.expected_hits, query_total_hits_param)

    assert len(elastic_result) == expected_number
