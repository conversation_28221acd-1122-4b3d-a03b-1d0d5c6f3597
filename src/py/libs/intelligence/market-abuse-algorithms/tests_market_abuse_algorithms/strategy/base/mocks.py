from market_abuse_algorithms.data_source.repository.base import Repository


class FakeRepository(Repository):
    def _create_client(self):
        pass


class SDP(FakeRepository):
    def __init__(self, realm: str):
        self.HOST = self._get_host()
        self.PORT = 443
        self.SCHEME = "https"
        self.USE_SSL = False
        self.ELASTIC_API_KEY = None

        super().__init__(tenant=realm.split(".")[0])

    def _get_host(self):
        return f"test::{self.PORT}"

    def _create_client(self):
        pass
