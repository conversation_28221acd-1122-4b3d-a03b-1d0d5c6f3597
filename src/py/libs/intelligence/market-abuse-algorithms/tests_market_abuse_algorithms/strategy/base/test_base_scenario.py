import pandas as pd
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.scenario import (
    <PERSON>ena<PERSON>,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.static import (
    CategoryEvaluationType,
    DayAndOrderEvaluationType,
    GeneralEvaluationType,
    MarketDataEvaluationType,
    QuantityEvaluationType,
)


class TestBaseScenario:
    def test_scenario_normalize(self, helpers):
        result = {
            "orderStatesKeys": [
                "OrderState:SE|20220915|110|5|E|20220913:2:SE202209151105E20220913SE202209151105E20220913120220:FILL:2022-09-13T10:11:01Z:0.0:1663409062850"  # noqa: E501
            ],
            "executionsDetected": 1,
            "ordersKeys": ["Order:SE|20220915|110|5|E|20220913:2:NEWO:1663409054213"],
            "ordersDetected": 1,
            "earliestOrderTimestamp": pd.Timestamp("2022-09-13 10:11:00"),
            "instrumentNameList": ["KION GROUP AG KION GROUP ORD SHS"],
            "instrumentDetected": 1,
            "totalOrderQuantity": 57677.0,
            "clientList": ["Briana Lynch"],
            "counterpartyList": ["JP MORGAN SECURITIES PLC"],
            "traderList": ["Benjamin Nahum"],
            "advPercentage": [0.14111340549216106],
            "adv": [408728.0],
        }

        context = StrategyContext(
            commit_audit=True,
            filters={
                "bool": {
                    "must": [{"range": {"&timestamp": {"gte": "2022-09-16T10:21:29.225298Z"}}}],
                    "must_not": [],
                }
            },
            requested_by="Test",
            realm="iris.uat.steeleye.co",
            tenant="iris",
            thresholds=Thresholds(
                assetClass=None,
                categoryEvaluationType=CategoryEvaluationType.MARKET,
                dayAndOrderEvaluationType=DayAndOrderEvaluationType.DAY,
                executionNotionalValueCurrency=None,
                generalEvaluationType=GeneralEvaluationType.EXECUTING_ENTITY,
                lookBackPeriod=None,
                marketDataEvaluationType=MarketDataEvaluationType.DAY_TRADED_VOLUME,
                minimumQuantity=10.0,
                minNumberOfDaysOrderFlow=None,
                normaliseBehaviour=None,
                percentageAdv=0.1,
                quantityEvaluationType=QuantityEvaluationType.ORDER_QUANTITY,
            ),
        )

        context.thresholds = Thresholds.validate(context.thresholds)

        base_scenario = Scenario(context=context, result=result)

        normalized_scenario = base_scenario.normalize

        assert normalized_scenario.trades
        assert normalized_scenario.scenario_id
