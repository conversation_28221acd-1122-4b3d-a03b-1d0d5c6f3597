import pytest
from market_abuse_algorithms.strategy.front_running.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerFrontRunning:
    def test_case_debug(self, helpers):
        thresholds = dict(assetClass="", timeWindow=300)

        filters = {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "timestamps.orderSubmitted": {
                                "gte": 1721088000000,
                                "lt": 1721174399999,
                            }
                        }
                    },
                    {"range": {"&timestamp": {"gte": "2022-07-19T14:38:29.792687Z"}}},
                ]
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        strategy.scenarios
