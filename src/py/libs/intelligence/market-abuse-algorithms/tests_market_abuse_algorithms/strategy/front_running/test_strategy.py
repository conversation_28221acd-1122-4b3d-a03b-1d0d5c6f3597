# ruff: noqa: E501

import pandas as pd
import pytest
from datetime import datetime
from market_abuse_algorithms.strategy.front_running.strategy import Strategy
from pathlib import Path
from unittest.mock import MagicMock

ORDER_KEY_MAP_DICT = {
    "frontRunning_2_1:1:NEWO": "Order:frontRunning_2_1:1:NEWO:1695892799655",
    "frontRunning_2_2:1:NEWO": "Order:frontRunning_2_2:1:NEWO:1695892799655",
    "frontRunning_2_3:1:NEWO": "Order:frontRunning_2_3:1:NEWO:1695892799655",
    "frontRunning_2_4:1:NEWO": "Order:frontRunning_2_4:1:NEWO:1695892799655",
    "frontRunning_2_5:1:NEWO": "Order:frontRunning_2_5:1:NEWO:1695892799655",
    "frontRunning_2_8:1:NEWO": "Order:frontRunning_2_8:1:NEWO:1695892799655",
    "frontRunning_2_9:1:NEWO": "Order:frontRunning_2_9:1:NEWO:1695892799655",
}

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture()
def fake_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def custom_date_parser():
        return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")

    def _foo(*args):
        result = pd.read_csv(
            *args,
            sep=",",
            parse_dates=["timestamps.orderSubmitted", "timestamps.tradingDateTime"],
            date_parser=custom_date_parser(),
        )
        return result

    return _foo


class TestFrontRunning:
    def test_test_case(self, helpers, fake_data):
        """Test to verify Front running client favouritism as seen in:

        https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1891532947/Front+Running+-+Client+Favouritism
        """
        filters = {}

        context = helpers.get_context(thresholds={"timeWindow": 600}, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_for_scenarios = MagicMock()
        strategy.queries.get_additional_fields_for_scenarios.return_value = pd.read_csv(
            TEST_DATA.joinpath("additional_query.csv"), index_col="&id"
        )

        strategy.queries.get_orders_keys_map = MagicMock()
        strategy.queries.get_orders_keys_map.return_value = ORDER_KEY_MAP_DICT

        strategy._run_algo(data=fake_data(TEST_DATA.joinpath("executions_query.csv")))

        alerts = strategy.scenarios

        assert len(alerts) == 3

    def test_test_case_mc_194(self, helpers, fake_data):
        """Test to verify Front running client favouritism as seen in:

        https://steeleye.atlassian.net/browse/MC-194
        """
        filters = {}

        context = helpers.get_context(thresholds={"timeWindow": 300}, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_for_scenarios = MagicMock()
        strategy.queries.get_additional_fields_for_scenarios.return_value = pd.read_csv(
            TEST_DATA.joinpath("mc-194-additional-fields.csv"),
        )

        strategy.queries.get_orders_keys_map = MagicMock()
        strategy.queries.get_orders_keys_map.return_value = {
            "ORDER 1221:1:NEWO": "Order:ORDER 1221:1:NEWO:1743694466487",
            "ORDER 2321:1:NEWO": "Order:ORDER 2321:1:NEWO:1743694466612",
        }

        strategy._run_algo(data=fake_data(TEST_DATA.joinpath("mc-194.csv")))

        alerts = strategy.scenarios

        assert len(alerts) == 1

        alert_data = alerts[0].json

        alert_data

        assert alert_data["records"]["order"] == "Order:ORDER 2321:1:NEWO:1743694466612"
        assert len(alert_data["records"]["newOrders"]) == 2
        assert len(alert_data["records"]["executionsEntry"]) == 2
        assert len(alert_data["records"]["executionsExit"]) == 1
