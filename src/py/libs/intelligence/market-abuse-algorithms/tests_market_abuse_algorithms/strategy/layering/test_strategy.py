import pandas as pd
import pytest
from datetime import datetime
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.layering.strategy import Strategy
from pathlib import Path
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


def custom_date_parser():
    return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S.%f")


class TestLayeringV1:
    def test_missing_thresholds(self, helpers):
        thresholds = dict(
            assetClass="",
            percentageOppositeOrderVolume=0.32,
            detectLayeringBehaviour=False,
            detectSuccessfulAttempt=False,
            percentageProximityFromTouch=0.1,
            percentagePriceImprovement=0.3,
            excValidityPeriods=False,
        )

        context = helpers.get_context(thresholds=thresholds, filters={})

        with pytest.raises(Exception):
            Strategy(context=context)

    def test_wrong_thresholds(self, helpers):
        thresholds = dict(
            assetClass="",
            percentageOppositeOrderVolume=0.32,
            detectLayeringBehaviour=False,
            detectSuccessfulAttempt=False,
            percentageProximityFromTouch=0.1,
            percentagePriceImprovement=0.3,
            orderTimeWindo={"unit": "milliseconds", "value": 0},
            successfulReverseTradeWindow=1234,
            excValidityPeriods=False,
        )

        with pytest.raises(Exception):
            context = helpers.get_context(thresholds=thresholds, filters={})
            Strategy(context=context)

    def test_layering_algo(self, helpers):
        thresholds = {
            "orderTimeWindow": {"unit": "milliseconds", "value": 0},
            "percentageOppositeOrderVolume": 0.01,
            "detectLayeringBehaviour": False,
            "detectSuccessfulAttempt": False,
            "excValidityPeriods": True,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})
        strategy = Strategy(context=context)

        fictitious_orders_data = (
            pd.read_csv(
                TEST_DATA.joinpath("fictitious_orders_data.csv"),
                index_col=0,
                sep=",",
                parse_dates=["timestamps.orderSubmitted"],
                date_parser=custom_date_parser(),
            )
            .reset_index()
            .drop(columns=["index"])
        )

        real_orders_data = (
            pd.read_csv(
                TEST_DATA.joinpath("real_orders_data.csv"),
                index_col=0,
                sep=",",
                parse_dates=["timestamps.orderSubmitted"],
                date_parser=custom_date_parser(),
            )
            .reset_index()
            .drop(columns=["index"])
        )

        strategy.queries.get_real_orders_executions_data = MagicMock()
        strategy.queries.get_real_orders_executions_data.return_value = pd.DataFrame()

        strategy.queries.get_prices_for_fictitious_orders = MagicMock()
        strategy.queries.get_prices_for_fictitious_orders.return_value = pd.DataFrame()

        result = strategy._algo(
            fictitious_orders_data=fictitious_orders_data,
            real_orders_data=real_orders_data,
        )
        assert result.empty

    def test_drop_duplicated_records(self, helpers):
        df = pd.read_csv(TEST_DATA.joinpath("test_drop_duplicated_records.csv"))

        df_dropped = pd.read_csv(TEST_DATA.joinpath("test_drop_duplicated_records_dropped.csv"))

        cols_with_lists_mask = df.columns.isin(
            [
                OrderField.TRADER_FILE_IDENTIFIER,
                OrderField.CLIENT_FILE_IDENTIFIER,
                OrderField.EXC_DTL_VALIDITY_PERIOD,
            ]
        )
        cols_to_inspect_dupes = df.loc[:, (~cols_with_lists_mask)].columns.tolist()

        participant_field = OrderField.CLIENT_FILE_IDENTIFIER

        df_new = df.drop(columns=participant_field)

        df_new = df_new.loc[df_new.astype(str).drop_duplicates(subset=cols_to_inspect_dupes).index]

        assert df_new.equals(df_dropped)

    def test_drop_duplicated_records_func(self, helpers):
        thresholds = {
            "orderTimeWindow": {"unit": "milliseconds", "value": 300},
            "percentageOppositeOrderVolume": 0.01,
            "cancellationTimeWindow": {"unit": "milliseconds", "value": 1800},
            "participantRestriction": "Client",
            "excValidityPeriods": True,
            "detectLayeringBehaviour": False,
            "detectSuccessfulAttempt": False,
            "percentageProximityFromTouch": 0,
            "percentagePriceImprovement": 0,
        }
        context = helpers.get_context(thresholds=thresholds, filters={})
        strategy = Strategy(context=context)

        df = pd.read_csv(TEST_DATA.joinpath("test_drop_duplicated_records.csv"))

        df_dropped = pd.read_csv(TEST_DATA.joinpath("test_drop_duplicated_records_dropped.csv"))

        fictitious_orders_data, real_orders_data = strategy._process_data_for_algo(
            df, df, OrderField.CLIENT_FILE_IDENTIFIER
        )

        assert fictitious_orders_data.equals(df_dropped)
        assert real_orders_data.equals(df_dropped)

    @pytest.mark.parametrize(
        "participant_restriction",
        [("Client"), ("Trader")],
    )
    def test_participant_restriction_client(self, helpers, participant_restriction):
        """Uses the data from the test case 4.3 with the
        "participantRestriction" threshold Data was manipulated to include both
        the 'traderFileIdentifier' and 'clientFileIdentifier' columns."""
        thresholds = {
            "orderTimeWindow": {"unit": "seconds", "value": 3},
            "percentageOppositeOrderVolume": 0.01,
            "detectLayeringBehaviour": False,
            "detectSuccessfulAttempt": False,
            "participantRestriction": participant_restriction,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})
        strategy = Strategy(context=context)

        mini_batch_data = pd.read_csv(
            TEST_DATA.joinpath("participant_restriction_apply_mini_batch.csv"),
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )
        get_real_orders = pd.read_csv(
            TEST_DATA.joinpath("participant_restriction_get_real_orders.csv"),
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )

        strategy.queries.get_real_orders = MagicMock()
        strategy.queries.get_real_orders.return_value = get_real_orders

        strategy._run_algo = MagicMock()
        strategy._run_algo.return_value = pd.DataFrame()

        result = strategy._apply_strategy_mini_batch_group(mini_batch_data)

        assert result is None

    def test_get_cases_to_analyse(self, helpers):
        thresholds = {
            "orderTimeWindow": {"unit": "milliseconds", "value": 0},
            "percentageOppositeOrderVolume": 0.01,
            "detectLayeringBehaviour": False,
            "detectSuccessfulAttempt": False,
        }

        context = helpers.get_context(thresholds=thresholds, filters={})
        strategy = Strategy(context=context)

        scroll_data = (
            pd.read_csv(
                TEST_DATA.joinpath("uc1.1_scroll_data.csv"),
                index_col=0,
                sep=",",
                parse_dates=["timestamps.orderSubmitted"],
            )
            .reset_index()
            .drop(columns=["index"])
        )

        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["GB00H1QMMV56"]]

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = scroll_data

        for data in strategy.queries.get_cases_to_analyse():
            assert data.shape == (6, 16)
