import pytest
from market_abuse_algorithms.strategy.layering.strategy import Strategy


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerLayeringV1:
    def test_case_debug(self, helpers):
        thresholds = {
            "orderTimeWindow": {"unit": "milliseconds", "value": 0},
            "percentageOppositeOrderVolume": 10,
            "successfulReverseTradeWindow": {"unit": "milliseconds", "value": 0},
            "cancellationTimeWindow": {"unit": "milliseconds", "value": 0},
            "participantRestriction": "Client",
            "excValidityPeriods": True,
            "detectLayeringBehaviour": True,
            "detectSuccessfulAttempt": True,
            "percentageProximityFromTouch": 1,
            "percentagePriceImprovement": 0.1,
        }

        filters = {
            "bool": {
                "filter": [
                    {"range": {"&timestamp": {"gte": "2024-07-23T08:09:36.929000Z"}}},
                    # {"term": {"instrumentDetails.instrument.instrumentIdCode": "DE000C5SKSC4"}},
                ]
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        scenarios = strategy.scenarios

        assert len(scenarios) > 0
