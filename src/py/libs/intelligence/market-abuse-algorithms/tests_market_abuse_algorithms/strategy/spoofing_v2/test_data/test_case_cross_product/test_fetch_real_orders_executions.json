{"clientFileIdentifier": {"0": "account:client1", "1": "account:client1", "2": "account:client1", "3": "account:client1", "4": "account:client1", "5": "account:client1", "6": "account:client1", "7": "account:client1", "8": "account:client1", "9": "account:client1", "10": "account:client1", "11": "account:client1", "12": "account:client1", "13": "account:client1", "14": "account:client1", "15": "account:client1", "16": "account:client1", "17": "account:client1", "18": "account:client1", "19": "account:client1", "20": "account:client1", "21": "account:client1", "22": "account:client1", "23": "account:client1"}, "&parent": {"0": "ORD_ULVR.L_025662296:1:NEWO", "1": "layeringAndBookBalance_15A_3:2:NEWO", "2": "spoofingV2_10.1_1:2:NEWO", "3": "spoofingV2_10.1_1:2:NEWO", "4": "spoofingV2_10.1_1:2:NEWO", "5": "spoofingV2_10_1:2:NEWO", "6": "spoofingV2_10_1:2:NEWO", "7": "spoofingV2_10_1:2:NEWO", "8": "spoofingV2_10_2:1:NEWO", "9": "spoofingV2_14_1:2:NEWO", "10": "spoofingV2_14_2:1:NEWO", "11": "spoofingV2_14_2:1:NEWO", "12": "spoofingV2_5_1:1:NEWO", "13": "spoofingV2_6_1:2:NEWO", "14": "spoofingV2_6_2:1:NEWO", "15": "spoofingV2_7_2:1:NEWO", "16": "spoofingV2_8_1:2:NEWO", "17": "spoofingV2_8_2:1:NEWO", "18": "spoofingV2_9_1:2:NEWO", "19": "spoofingV2_9_1:2:NEWO", "20": "spoofingV2_9_1:2:NEWO", "21": "spoofingV2_9_1:2:NEWO", "22": "spoofingV2_9_1:2:NEWO", "23": "spoofingV2_9_2:1:NEWO"}, "&key": {"0": "OrderState:ORD_ULVR.L_025662296:1:ORDULVRL025662296BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:*************", "1": "OrderState:layeringAndBookBalance_15A_3:2:<PERSON>Y<PERSON>ING<PERSON>DBOOKBALANCE15A3LAYERINGANDBOOKBALANCE15A3:FILL:2021-08-24T13:00:05.696336Z:0.0:1689758744019", "2": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011120210824SELL:PARF:2021-08-24T13:00:05.696336Z:400.0:1701073983695", "3": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011220210824SELL:PARF:2021-08-24T13:00:06.696336Z:300.0:1701073983695", "4": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011320210824SELL:PARF:2021-08-24T13:00:07.696336Z:250.0:1701073983695", "5": "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21011SELL:PARF:400.0:1689759951048", "6": "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21012SELL:PARF:300.0:1689759951048", "7": "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21013SELL:PARF:250.0:1689759951048", "8": "OrderState:spoofingV2_10_2:1:SPOOFINGV2102BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689759951048", "9": "OrderState:spoofingV2_14_1:2:SPOOFINGV2141SPOOFINGV2141SELL:FILL:0.0:1689762007243", "10": "OrderState:spoofingV2_14_2:1:SPOOFINGV2142BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689762007243", "11": "OrderState:spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0:1689762007243", "12": "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689764578048", "13": "OrderState:spoofingV2_6_1:2:SPOOFINGV261SELL:CAME:2021-08-24T13:00:05.696336Z:500.0:1689761987515", "14": "OrderState:spoofingV2_6_2:1:SPOOFINGV262BUYI:FILL:0.0:1689761987515", "15": "OrderState:spoofingV2_7_2:1:SPOOFINGV272BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689767205368", "16": "OrderState:spoofingV2_8_1:2:SPOOFINGV281SPOOFINGV281SELL:FILL:0.0:1689767218864", "17": "OrderState:spoofingV2_8_2:1:SPOOFINGV282BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689767218864", "18": "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2911SELL:PARF:400.0:*************", "19": "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2912SELL:PARF:300.0:*************", "20": "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2913SELL:PARF:200.0:*************", "21": "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2914SELL:PARF:100.0:*************", "22": "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2915SELL:FILL:0.0:*************", "23": "OrderState:spoofingV2_9_2:1:SPOOFINGV292BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:*************"}, "traderFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "2": "account:trader1", "3": "account:trader1", "4": "account:trader1", "5": "account:trader1", "6": "account:trader1", "7": "account:trader1", "8": "account:trader1", "9": "account:trader1", "10": "account:trader1", "11": "account:trader1", "12": "account:trader1", "13": "account:trader1", "14": "account:trader1", "15": "account:trader1", "16": "account:trader1", "17": "account:trader1", "18": "account:trader1", "19": "account:trader1", "20": "account:trader1", "21": "account:trader1", "22": "account:trader1", "23": "account:trader1"}, "counterpartyFileIdentifier": {"0": "account:counterparty1", "1": "account:counterparty1", "2": "account:counterparty1", "3": "account:counterparty1", "4": "account:counterparty1", "5": "account:counterparty1", "6": "account:counterparty1", "7": "account:counterparty1", "8": "account:counterparty1", "9": "account:counterparty1", "10": "account:counterparty1", "11": "account:counterparty1", "12": "account:counterparty1", "13": "account:counterparty1", "14": "account:counterparty1", "15": "account:counterparty1", "16": "account:counterparty1", "17": "account:counterparty1", "18": "account:counterparty1", "19": "account:counterparty1", "20": "account:counterparty1", "21": "account:counterparty1", "22": "account:counterparty1", "23": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73", "1": "lei:894500wota5040khgx73", "2": "lei:894500wota5040khgx73", "3": "lei:894500wota5040khgx73", "4": "lei:894500wota5040khgx73", "5": "lei:894500wota5040khgx73", "6": "lei:894500wota5040khgx73", "7": "lei:894500wota5040khgx73", "8": "lei:894500wota5040khgx73", "9": "lei:894500wota5040khgx73", "10": "lei:894500wota5040khgx73", "11": "lei:894500wota5040khgx73", "12": "lei:894500wota5040khgx73", "13": "lei:894500wota5040khgx73", "14": "lei:894500wota5040khgx73", "15": "lei:894500wota5040khgx73", "16": "lei:894500wota5040khgx73", "17": "lei:894500wota5040khgx73", "18": "lei:894500wota5040khgx73", "19": "lei:894500wota5040khgx73", "20": "lei:894500wota5040khgx73", "21": "lei:894500wota5040khgx73", "22": "lei:894500wota5040khgx73", "23": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye", "1": "SteelEye", "2": "SteelEye", "3": "SteelEye", "4": "SteelEye", "5": "SteelEye", "6": "SteelEye", "7": "SteelEye", "8": "SteelEye", "9": "SteelEye", "10": "SteelEye", "11": "SteelEye", "12": "SteelEye", "13": "SteelEye", "14": "SteelEye", "15": "SteelEye", "16": "SteelEye", "17": "SteelEye", "18": "SteelEye", "19": "SteelEye", "20": "SteelEye", "21": "SteelEye", "22": "SteelEye", "23": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": 1629810005696, "1": 1629810005696, "2": 1629810005696, "3": 1629810006696, "4": 1629810007696, "5": null, "6": null, "7": null, "8": 1629810005696, "9": null, "10": 1629810005696, "11": 1629810001696, "12": 1629810005696, "13": 1629810005696, "14": null, "15": 1629810005696, "16": null, "17": 1629810005696, "18": null, "19": null, "20": null, "21": null, "22": null, "23": 1629810005696}, "orderIdentifiers.orderIdCode": {"0": "ORD_ULVR.L_025662296", "1": "layeringAndBookBalance_15A_3", "2": "spoofingV2_10.1_1", "3": "spoofingV2_10.1_1", "4": "spoofingV2_10.1_1", "5": "spoofingV2_10_1", "6": "spoofingV2_10_1", "7": "spoofingV2_10_1", "8": "spoofingV2_10_2", "9": "spoofingV2_14_1", "10": "spoofingV2_14_2", "11": "spoofingV2_14_2", "12": "spoofingV2_5_1", "13": "spoofingV2_6_1", "14": "spoofingV2_6_2", "15": "spoofingV2_7_2", "16": "spoofingV2_8_1", "17": "spoofingV2_8_2", "18": "spoofingV2_9_1", "19": "spoofingV2_9_1", "20": "spoofingV2_9_1", "21": "spoofingV2_9_1", "22": "spoofingV2_9_1", "23": "spoofingV2_9_2"}, "executionDetails.orderType": {"0": "Market", "1": "Market", "2": "Market", "3": "Market", "4": "Market", "5": "Market", "6": "Market", "7": "Market", "8": "Market", "9": "Market", "10": "Market", "11": "Market", "12": "Market", "13": "Market", "14": "Market", "15": "Market", "16": "Market", "17": "Market", "18": "Market", "19": "Market", "20": "Market", "21": "Market", "22": "Market", "23": "Market"}, "executionDetails.orderStatus": {"0": "CAME", "1": "FILL", "2": "PARF", "3": "PARF", "4": "PARF", "5": "PARF", "6": "PARF", "7": "PARF", "8": "CAME", "9": "FILL", "10": "CAME", "11": "PARF", "12": "CAME", "13": "CAME", "14": "FILL", "15": "CAME", "16": "FILL", "17": "CAME", "18": "PARF", "19": "PARF", "20": "PARF", "21": "PARF", "22": "FILL", "23": "CAME"}, "executionDetails.buySellIndicator": {"0": "BUYI", "1": "SELL", "2": "SELL", "3": "SELL", "4": "SELL", "5": "SELL", "6": "SELL", "7": "SELL", "8": "BUYI", "9": "SELL", "10": "BUYI", "11": "BUYI", "12": "BUYI", "13": "SELL", "14": "BUYI", "15": "BUYI", "16": "SELL", "17": "BUYI", "18": "SELL", "19": "SELL", "20": "SELL", "21": "SELL", "22": "SELL", "23": "BUYI"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 12", "3": "Trader 12", "4": "Trader 12", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 2", "12": "Trader 1", "13": "Trader 1", "14": "Trader 2", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 1", "20": "Trader 1", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "2": "account:trader1", "3": "account:trader1", "4": "account:trader1", "5": "account:trader1", "6": "account:trader1", "7": "account:trader1", "8": "account:trader1", "9": "account:trader1", "10": "account:trader1", "11": "account:trader2", "12": "account:trader1", "13": "account:trader1", "14": "account:trader2", "15": "account:trader1", "16": "account:trader1", "17": "account:trader1", "18": "account:trader1", "19": "account:trader1", "20": "account:trader1", "21": "account:trader1", "22": "account:trader1", "23": "account:trader1"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO", "1": "XXXXGBPO", "2": "XXXXGBPO", "3": "XXXXGBPO", "4": "XXXXGBPO", "5": "XXXXGBPO", "6": "XXXXGBPO", "7": "XXXXGBPO", "8": "XXXXGBPO", "9": "XXXXGBPO", "10": "XXXXGBPO", "11": "XXXXGBPO", "12": "XXXXGBPO", "13": "XXXXGBPO", "14": "XXXXGBPO", "15": "XXXXGBPO", "16": "XXXXGBPO", "17": "XXXXGBPO", "18": "XXXXGBPO", "19": "XXXXGBPO", "20": "XXXXGBPO", "21": "XXXXGBPO", "22": "XXXXGBPO", "23": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "*******************", "1": "*******************", "2": "*******************", "3": "*******************", "4": "*******************", "5": "*******************", "6": "*******************", "7": "*******************", "8": "*******************", "9": "*******************", "10": "*******************", "11": "*******************", "12": "*******************", "13": "*******************", "14": "*******************", "15": "*******************", "16": "*******************", "17": "*******************", "18": "*******************", "19": "*******************", "20": "*******************", "21": "*******************", "22": "*******************", "23": "*******************"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "2": "GB00B10RZP78", "3": "GB00B10RZP78", "4": "GB00B10RZP78", "5": "GB00B10RZP78", "6": "GB00B10RZP78", "7": "GB00B10RZP78", "8": "GB00B10RZP78", "9": "GB00B10RZP78", "10": "GB00B10RZP78", "11": "GB00B10RZP78", "12": "GB00B10RZP78", "13": "GB00B10RZP78", "14": "GB00B10RZP78", "15": "GB00B10RZP78", "16": "GB00B10RZP78", "17": "GB00B10RZP78", "18": "GB00B10RZP78", "19": "GB00B10RZP78", "20": "GB00B10RZP78", "21": "GB00B10RZP78", "22": "GB00B10RZP78", "23": "GB00B10RZP78"}, "instrumentDetails.instrument.instrumentFullName": {"0": "UNILEVER PLC ORD 3 1/9P", "1": "UNILEVER PLC ORD 3 1/9P", "2": "UNILEVER PLC ORD 3 1/9P", "3": "UNILEVER PLC ORD 3 1/9P", "4": "UNILEVER PLC ORD 3 1/9P", "5": "UNILEVER PLC ORD 3 1/9P", "6": "UNILEVER PLC ORD 3 1/9P", "7": "UNILEVER PLC ORD 3 1/9P", "8": "UNILEVER PLC ORD 3 1/9P", "9": "UNILEVER PLC ORD 3 1/9P", "10": "UNILEVER PLC ORD 3 1/9P", "11": "UNILEVER PLC ORD 3 1/9P", "12": "UNILEVER PLC ORD 3 1/9P", "13": "UNILEVER PLC ORD 3 1/9P", "14": "UNILEVER PLC ORD 3 1/9P", "15": "UNILEVER PLC ORD 3 1/9P", "16": "UNILEVER PLC ORD 3 1/9P", "17": "UNILEVER PLC ORD 3 1/9P", "18": "UNILEVER PLC ORD 3 1/9P", "19": "UNILEVER PLC ORD 3 1/9P", "20": "UNILEVER PLC ORD 3 1/9P", "21": "UNILEVER PLC ORD 3 1/9P", "22": "UNILEVER PLC ORD 3 1/9P", "23": "UNILEVER PLC ORD 3 1/9P"}, "priceFormingData.initialQuantity": {"0": 500.0, "1": 50.0, "2": 500.0, "3": 500.0, "4": 500.0, "5": 500.0, "6": 500.0, "7": 500.0, "8": 500.0, "9": 500.0, "10": 500.0, "11": 500.0, "12": 500.0, "13": 500.0, "14": 500.0, "15": 500.0, "16": 500.0, "17": 500.0, "18": 500.0, "19": 500.0, "20": 500.0, "21": 500.0, "22": 500.0, "23": 500.0}, "priceFormingData.tradedQuantity": {"0": 0.0, "1": 50.0, "2": 100.0, "3": 100.0, "4": 50.0, "5": 100.0, "6": 100.0, "7": 50.0, "8": 0.0, "9": 500.0, "10": 0.0, "11": 1.0, "12": 0.0, "13": 0.0, "14": 500.0, "15": 0.0, "16": 500.0, "17": 0.0, "18": 100.0, "19": 100.0, "20": 100.0, "21": 100.0, "22": 100.0, "23": 0.0}, "counterparty.name": {"0": "Counterparty 1", "1": "Counterparty 1", "2": "Counterparty 1", "3": "Counterparty 1", "4": "Counterparty 1", "5": "Counterparty 1", "6": "Counterparty 1", "7": "Counterparty 1", "8": "Counterparty 1", "9": "Counterparty 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Counterparty 1", "13": "Counterparty 1", "14": "Counterparty 1", "15": "Counterparty 1", "16": "Counterparty 1", "17": "Counterparty 1", "18": "Counterparty 1", "19": "Counterparty 1", "20": "Counterparty 1", "21": "Counterparty 1", "22": "Counterparty 1", "23": "Counterparty 1"}, "timestamps.tradingDateTime": {"0": null, "1": 1629810005696, "2": 1629810005696, "3": 1629810006696, "4": 1629810007696, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": 1629810001696, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null}, "priceFormingData.price": {"0": null, "1": 40.6325, "2": 4064.0, "3": 4064.0, "4": 4064.0, "5": 4064.0, "6": 4064.0, "7": 4064.0, "8": null, "9": 4064.0, "10": null, "11": 4065.0, "12": null, "13": null, "14": 40.64, "15": null, "16": 4064.0, "17": null, "18": 4064.0, "19": 4064.0, "20": 4064.0, "21": 4064.0, "22": 4064.0, "23": null}, "bestExecutionData.transactionVolume.native": {"0": null, "1": null, "2": 406400.0, "3": 406400.0, "4": 203200.0, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null}, "buyer.name": {"0": "Client 1", "1": "Counterparty 1", "2": "Counterparty 1", "3": "Counterparty 1", "4": "Counterparty 1", "5": "Counterparty 1", "6": "Counterparty 1", "7": "Counterparty 1", "8": "Client 1", "9": "Counterparty 1", "10": "Client 1", "11": "Client 1", "12": "Client 1", "13": "Counterparty 1", "14": "Client 1", "15": "Client 1", "16": "Counterparty 1", "17": "Client 1", "18": "Counterparty 1", "19": "Counterparty 1", "20": "Counterparty 1", "21": "Counterparty 1", "22": "Counterparty 1", "23": "Client 1"}, "seller.name": {"0": "Counterparty 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Counterparty 1", "9": "Client 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Counterparty 1", "13": "Client 1", "14": "Counterparty 1", "15": "Counterparty 1", "16": "Client 1", "17": "Counterparty 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Counterparty 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "1": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "2": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "3": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "4": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "5": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "6": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "7": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "8": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "9": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "10": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "11": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "12": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "13": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "14": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "15": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "16": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "17": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "18": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "19": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "20": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "21": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "22": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "23": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Client 1", "9": "Client 1", "10": "Client 1", "11": "Client 1", "12": "Client 1", "13": "Client 1", "14": "Client 1", "15": "Client 1", "16": "Client 1", "17": "Client 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Client 1"}, "__client__": {"0": "Client 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Client 1", "9": "Client 1", "10": "Client 1", "11": "Client 1", "12": "Client 1", "13": "Client 1", "14": "Client 1", "15": "Client 1", "16": "Client 1", "17": "Client 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Client 1"}, "trader.&id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null}, "trader.name": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 12", "3": "Trader 12", "4": "Trader 12", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 1", "13": "Trader 1", "14": "Trader 1", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 1", "20": "Trader 1", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1"}, "__trader_id__": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null}, "__trader_name__": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 12", "3": "Trader 12", "4": "Trader 12", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 1", "13": "Trader 1", "14": "Trader 1", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 1", "20": "Trader 1", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1"}, "__instrument_code__": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "2": "GB00B10RZP78", "3": "GB00B10RZP78", "4": "GB00B10RZP78", "5": "GB00B10RZP78", "6": "GB00B10RZP78", "7": "GB00B10RZP78", "8": "GB00B10RZP78", "9": "GB00B10RZP78", "10": "GB00B10RZP78", "11": "GB00B10RZP78", "12": "GB00B10RZP78", "13": "GB00B10RZP78", "14": "GB00B10RZP78", "15": "GB00B10RZP78", "16": "GB00B10RZP78", "17": "GB00B10RZP78", "18": "GB00B10RZP78", "19": "GB00B10RZP78", "20": "GB00B10RZP78", "21": "GB00B10RZP78", "22": "GB00B10RZP78", "23": "GB00B10RZP78"}, "__venue__": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1", "1": "desk1", "2": "desk1", "3": "desk1", "4": "desk1", "5": "desk1", "6": "desk1", "7": "desk1", "8": "desk1", "9": "desk1", "10": "desk1", "11": "desk2", "12": "desk1", "13": "desk1", "14": "desk2", "15": "desk1", "16": "desk1", "17": "desk1", "18": "desk1", "19": "desk1", "20": "desk1", "21": "desk1", "22": "desk1", "23": "desk1"}}