{"&id": {"0": "Layering_CP_7:2:NEWO", "1": "Layering_CP_8:2:NEWO", "2": "Layering_CP_9:1:NEWO", "3": "Layering|CP|7:2:NEWO", "4": "Layering|CP|8:2:NEWO", "5": "Layering|CP|9:1:NEWO", "6": "Layering|CrossProduct|7:2:NEWO", "7": "Layering|CrossProduct|8:2:NEWO", "8": "Layering|CrossProduct|9:1:NEWO", "9": "Layering|XProd|5:1:NEWO", "10": "Layering|XProd|6:1:NEWO", "11": "Layering|XProd|7:1:NEWO", "12": "SpoofingV2-CP-3:2:NEWO", "13": "SpoofingV2-CP-4:2:NEWO", "14": "SpoofingV2-CP-5:1:NEWO", "15": "SpoofingV2-CP|3:2:NEWO", "16": "SpoofingV2-CP|4:2:NEWO", "17": "SpoofingV2-CP|5:1:NEWO", "18": "SpoofingV2|CP|3:2:NEWO", "19": "SpoofingV2|CP|4:2:NEWO", "20": "SpoofingV2|CP|5:1:NEWO", "21": "Spoofing_CP_3:2:NEWO", "22": "Spoofing_CP_4:2:NEWO", "23": "Spoofing_CP_6:1:NEWO", "24": "Spoofing_CrossProd_3:2:NEWO"}, "clientFileIdentifier": {"0": "account:client1", "1": null, "2": null, "3": "account:client1", "4": null, "5": null, "6": "account:client1", "7": null, "8": null, "9": "account:client1", "10": null, "11": null, "12": "account:client1", "13": null, "14": null, "15": "account:client1", "16": null, "17": null, "18": "account:client1", "19": null, "20": null, "21": "account:client1", "22": null, "23": null, "24": "account:client1"}, "&key": {"0": "Order:Layering_CP_7:2:NEWO:*************", "1": "Order:Layering_CP_8:2:NEWO:*************", "2": "Order:Layering_CP_9:1:NEWO:*************", "3": "Order:Layering|CP|7:2:NEWO:*************", "4": "Order:Layering|CP|8:2:NEWO:*************", "5": "Order:Layering|CP|9:1:NEWO:*************", "6": "Order:Layering|CrossProduct|7:2:NEWO:*************", "7": "Order:Layering|CrossProduct|8:2:NEWO:*************", "8": "Order:Layering|CrossProduct|9:1:NEWO:*************", "9": "Order:Layering|XProd|5:1:NEWO:*************", "10": "Order:Layering|XProd|6:1:NEWO:*************", "11": "Order:Layering|XProd|7:1:NEWO:*************", "12": "Order:SpoofingV2-CP-3:2:NEWO:*************", "13": "Order:SpoofingV2-CP-4:2:NEWO:*************", "14": "Order:SpoofingV2-CP-5:1:NEWO:*************", "15": "Order:SpoofingV2-CP|3:2:NEWO:*************", "16": "Order:SpoofingV2-CP|4:2:NEWO:*************", "17": "Order:SpoofingV2-CP|5:1:NEWO:*************", "18": "Order:SpoofingV2|CP|3:2:NEWO:*************", "19": "Order:SpoofingV2|CP|4:2:NEWO:*************", "20": "Order:SpoofingV2|CP|5:1:NEWO:*************", "21": "Order:Spoofing_CP_3:2:NEWO:*************", "22": "Order:Spoofing_CP_4:2:NEWO:*************", "23": "Order:Spoofing_CP_6:1:NEWO:*************", "24": "Order:Spoofing_CrossProd_3:2:NEWO:*************"}, "traderFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "2": "account:trader1", "3": "account:trader1", "4": "account:trader1", "5": "account:trader1", "6": "account:trader1", "7": "account:trader1", "8": "account:trader1", "9": "account:trader1", "10": "account:trader1", "11": "account:trader1", "12": "account:trader3", "13": "account:trader3", "14": "account:trader3", "15": "account:trader3", "16": "account:trader3", "17": "account:trader3", "18": "account:trader2", "19": "account:trader2", "20": "account:trader2", "21": "account:trader1", "22": "account:trader1", "23": "account:trader1", "24": "account:trader2"}, "counterpartyFileIdentifier": {"0": "account:counterparty1", "1": "account:counterparty1", "2": "account:counterparty1", "3": "account:counterparty1", "4": "account:counterparty1", "5": "account:counterparty1", "6": "account:counterparty1", "7": "account:counterparty1", "8": "account:counterparty1", "9": "account:counterparty1", "10": "account:counterparty1", "11": "account:counterparty1", "12": "account:counterparty1", "13": "account:counterparty1", "14": "account:counterparty1", "15": "account:counterparty1", "16": "account:counterparty1", "17": "account:counterparty1", "18": "account:counterparty1", "19": "account:counterparty1", "20": "account:counterparty1", "21": "account:counterparty1", "22": "account:counterparty1", "23": "account:counterparty1", "24": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON", "1": "XXXX", "2": "OPRA", "3": "XLON", "4": "XXXX", "5": "OPRA", "6": "XLON", "7": "XXXX", "8": "OPRA", "9": "XLON", "10": "XXXX", "11": "OPRA", "12": "XLON", "13": "XXXX", "14": "OPRA", "15": "XLON", "16": "XXXX", "17": "OPRA", "18": "XLON", "19": "XXXX", "20": "OPRA", "21": "XLON", "22": "XXXX", "23": "OPRA", "24": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON", "1": null, "2": "OPRA", "3": "XLON", "4": null, "5": "OPRA", "6": "XLON", "7": null, "8": "OPRA", "9": "XLON", "10": null, "11": "OPRA", "12": "XLON", "13": null, "14": "OPRA", "15": "XLON", "16": null, "17": "OPRA", "18": "XLON", "19": null, "20": "OPRA", "21": "XLON", "22": null, "23": "OPRA", "24": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73", "1": "lei:894500wota5040khgx73", "2": "lei:894500wota5040khgx73", "3": "lei:894500wota5040khgx73", "4": "lei:894500wota5040khgx73", "5": "lei:894500wota5040khgx73", "6": "lei:894500wota5040khgx73", "7": "lei:894500wota5040khgx73", "8": "lei:894500wota5040khgx73", "9": "lei:894500wota5040khgx73", "10": "lei:894500wota5040khgx73", "11": "lei:894500wota5040khgx73", "12": "lei:894500wota5040khgx73", "13": "lei:894500wota5040khgx73", "14": "lei:894500wota5040khgx73", "15": "lei:894500wota5040khgx73", "16": "lei:894500wota5040khgx73", "17": "lei:894500wota5040khgx73", "18": "lei:894500wota5040khgx73", "19": "lei:894500wota5040khgx73", "20": "lei:894500wota5040khgx73", "21": "lei:894500wota5040khgx73", "22": "lei:894500wota5040khgx73", "23": "lei:894500wota5040khgx73", "24": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye", "1": "SteelEye", "2": "SteelEye", "3": "SteelEye", "4": "SteelEye", "5": "SteelEye", "6": "SteelEye", "7": "SteelEye", "8": "SteelEye", "9": "SteelEye", "10": "SteelEye", "11": "SteelEye", "12": "SteelEye", "13": "SteelEye", "14": "SteelEye", "15": "SteelEye", "16": "SteelEye", "17": "SteelEye", "18": "SteelEye", "19": "SteelEye", "20": "SteelEye", "21": "SteelEye", "22": "SteelEye", "23": "SteelEye", "24": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": 1700820069000, "1": 1700820379000, "2": 1700820670000, "3": 1700820069000, "4": 1700820379000, "5": 1700820670000, "6": 1700820072000, "7": 1700820379000, "8": 1700820670000, "9": 1700820072000, "10": 1700820379000, "11": 1700820670000, "12": 1700820069000, "13": 1700820379000, "14": 1700820670000, "15": 1700820069000, "16": 1700820379000, "17": 1700820670000, "18": 1700820069000, "19": 1700820379000, "20": 1700820670000, "21": 1700820069000, "22": 1700820379000, "23": 1700820670000, "24": 1700820069000}, "timestamps.tradingDateTime": {"0": 1700820069000, "1": 1700820379000, "2": 1700820670000, "3": 1700820069000, "4": 1700820379000, "5": 1700820670000, "6": 1700820072000, "7": 1700820379000, "8": 1700820670000, "9": 1700820072000, "10": 1700820379000, "11": 1700820670000, "12": 1700820069000, "13": 1700820379000, "14": 1700820670000, "15": 1700820069000, "16": null, "17": null, "18": 1700820069000, "19": null, "20": null, "21": 1700820069000, "22": 1700820379000, "23": 1700820670000, "24": 1700820069000}, "timestamps.orderSubmitted": {"0": 1700820071000, "1": 1700820379000, "2": 1700820670000, "3": 1700820071000, "4": 1700820379000, "5": 1700820670000, "6": 1700820072000, "7": 1700820379000, "8": 1700820670000, "9": 1700820072000, "10": 1700820379000, "11": 1700820670000, "12": 1700820071000, "13": 1700820379000, "14": 1700820670000, "15": 1700820071000, "16": 1700820379000, "17": 1700820670000, "18": 1700820071000, "19": 1700820379000, "20": 1700820670000, "21": 1700820071000, "22": 1700820379000, "23": 1700820670000, "24": 1700820071000}, "orderIdentifiers.orderIdCode": {"0": "Layering_CP_7", "1": "Layering_CP_8", "2": "Layering_CP_9", "3": "Layering|CP|7", "4": "Layering|CP|8", "5": "Layering|CP|9", "6": "Layering|CrossProduct|7", "7": "Layering|CrossProduct|8", "8": "Layering|CrossProduct|9", "9": "Layering|XProd|5", "10": "Layering|XProd|6", "11": "Layering|XProd|7", "12": "SpoofingV2-CP-3", "13": "SpoofingV2-CP-4", "14": "SpoofingV2-CP-5", "15": "SpoofingV2-CP|3", "16": "SpoofingV2-CP|4", "17": "SpoofingV2-CP|5", "18": "SpoofingV2|CP|3", "19": "SpoofingV2|CP|4", "20": "SpoofingV2|CP|5", "21": "Spoofing_CP_3", "22": "Spoofing_CP_4", "23": "Spoofing_CP_6", "24": "Spoofing_CrossProd_3"}, "executionDetails.validityPeriod": {"0": ["GTCV"], "1": ["GTCV"], "2": ["GTCV"], "3": ["GTCV"], "4": ["GTCV"], "5": ["GTCV"], "6": ["GTCV"], "7": ["GTCV"], "8": ["GTCV"], "9": ["GTCV"], "10": ["GTCV"], "11": ["GTCV"], "12": ["GTCV"], "13": ["GTCV"], "14": ["GTCV"], "15": ["GTCV"], "16": ["GTCV"], "17": ["GTCV"], "18": ["GTCV"], "19": ["GTCV"], "20": ["GTCV"], "21": ["GTCV"], "22": ["GTCV"], "23": ["GTCV"], "24": ["GTCV"]}, "executionDetails.orderType": {"0": "Market", "1": "Market", "2": "Market", "3": "Market", "4": "Market", "5": "Market", "6": "Market", "7": "Market", "8": "Market", "9": "Market", "10": "Market", "11": "Market", "12": "Market", "13": "Market", "14": "Market", "15": "Market", "16": "Market", "17": "Market", "18": "Market", "19": "Market", "20": "Market", "21": "Market", "22": "Market", "23": "Market", "24": "Market"}, "executionDetails.orderStatus": {"0": "NEWO", "1": "NEWO", "2": "NEWO", "3": "NEWO", "4": "NEWO", "5": "NEWO", "6": "NEWO", "7": "NEWO", "8": "NEWO", "9": "NEWO", "10": "NEWO", "11": "NEWO", "12": "NEWO", "13": "NEWO", "14": "NEWO", "15": "NEWO", "16": "NEWO", "17": "NEWO", "18": "NEWO", "19": "NEWO", "20": "NEWO", "21": "NEWO", "22": "NEWO", "23": "NEWO", "24": "NEWO"}, "executionDetails.buySellIndicator": {"0": "SELL", "1": "SELL", "2": "BUYI", "3": "SELL", "4": "SELL", "5": "BUYI", "6": "SELL", "7": "SELL", "8": "BUYI", "9": "BUYI", "10": "BUYI", "11": "BUYI", "12": "SELL", "13": "SELL", "14": "BUYI", "15": "SELL", "16": "SELL", "17": "BUYI", "18": "SELL", "19": "SELL", "20": "BUYI", "21": "SELL", "22": "SELL", "23": "BUYI", "24": "SELL"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 1", "1": null, "2": null, "3": "Trader 1", "4": null, "5": null, "6": "Trader 1", "7": null, "8": null, "9": "Trader 1", "10": null, "11": null, "12": "Trader 3", "13": null, "14": null, "15": "Trader 3", "16": null, "17": null, "18": "Trader 2", "19": null, "20": null, "21": "Trader 1", "22": null, "23": null, "24": "Trader 2"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1", "1": null, "2": null, "3": "account:trader1", "4": null, "5": null, "6": "account:trader1", "7": null, "8": null, "9": "account:trader1", "10": null, "11": null, "12": "account:trader3", "13": null, "14": null, "15": "account:trader3", "16": null, "17": null, "18": "account:trader2", "19": null, "20": null, "21": "account:trader1", "22": null, "23": null, "24": "account:trader2"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO", "1": "XXXXGB00BL6NGV24CFD", "2": "IFLODROOC2024-09 00:00:001.********", "3": "XXXXGBPO", "4": "XXXXGB00BL6NGV24CFD", "5": "IFLODROOC2024-09 00:00:001.********", "6": "XXXXGBPO", "7": "XXXXGB00BL6NGV24CFD", "8": "IFLODROOC2024-09 00:00:001.********", "9": "XXXXGBPO", "10": "XXXXGB00BL6NGV24CFD", "11": "IFLODROOC2024-09 00:00:001.********", "12": "XXXXGBPO", "13": "XXXXGB00BL6NGV24CFD", "14": "IFLODROOC2024-09 00:00:001.********", "15": "XXXXGBPO", "16": "XXXXGB00BL6NGV24CFD", "17": "IFLODROOC2024-09 00:00:001.********", "18": "XXXXGBPO", "19": "XXXXGB00BL6NGV24CFD", "20": "IFLODROOC2024-09 00:00:001.********", "21": "XXXXGBPO", "22": "XXXXGB00BL6NGV24CFD", "23": "IFLODROOC2024-09 00:00:001.********", "24": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "*******************", "1": "XXXXGB00BL6NGV24GBPCFD", "2": "*******************", "3": "*******************", "4": "XXXXGB00BL6NGV24GBPCFD", "5": "*******************", "6": "*******************", "7": "XXXXGB00BL6NGV24GBPCFD", "8": "*******************", "9": "*******************", "10": "XXXXGB00BL6NGV24GBPCFD", "11": "*******************", "12": "*******************", "13": "XXXXGB00BL6NGV24GBPCFD", "14": "*******************", "15": "*******************", "16": "XXXXGB00BL6NGV24GBPCFD", "17": "*******************", "18": "*******************", "19": "XXXXGB00BL6NGV24GBPCFD", "20": "*******************", "21": "*******************", "22": "XXXXGB00BL6NGV24GBPCFD", "23": "*******************", "24": "*******************"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON", "1": "XXXX", "2": "IFLO", "3": "XLON", "4": "XXXX", "5": "IFLO", "6": "XLON", "7": "XXXX", "8": "IFLO", "9": "XLON", "10": "XXXX", "11": "IFLO", "12": "XLON", "13": "XXXX", "14": "IFLO", "15": "XLON", "16": "XXXX", "17": "IFLO", "18": "XLON", "19": "XXXX", "20": "IFLO", "21": "XLON", "22": "XXXX", "23": "IFLO", "24": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00BL6NGV24", "1": null, "2": "GB00KKWNTR77", "3": "GB00BL6NGV24", "4": null, "5": "GB00KKWNTR77", "6": "GB00BL6NGV24", "7": null, "8": "GB00KKWNTR77", "9": "GB00BL6NGV24", "10": null, "11": "GB00KKWNTR77", "12": "GB00BL6NGV24", "13": null, "14": "GB00KKWNTR77", "15": "GB00BL6NGV24", "16": null, "17": "GB00KKWNTR77", "18": "GB00BL6NGV24", "19": null, "20": "GB00KKWNTR77", "21": "GB00BL6NGV24", "22": null, "23": "GB00KKWNTR77", "24": "GB00BL6NGV24"}, "instrumentDetails.instrument.instrumentFullName": {"0": "DR. MARTENS PLC ORD GBP0.01", "1": "DR MARTENS PLC CFD", "2": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "3": "DR. MARTENS PLC ORD GBP0.01", "4": "DR MARTENS PLC CFD", "5": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "6": "DR. MARTENS PLC ORD GBP0.01", "7": "DR MARTENS PLC CFD", "8": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "9": "DR. MARTENS PLC ORD GBP0.01", "10": "DR MARTENS PLC CFD", "11": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "12": "DR. MARTENS PLC ORD GBP0.01", "13": "DR. MARTENS PLC LS -,01 CFD", "14": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "15": "DR. MARTENS PLC ORD GBP0.01", "16": "DR. MARTENS PLC LS -,01 CFD", "17": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "18": "DR. MARTENS PLC ORD GBP0.01", "19": "DR. MARTENS PLC LS -,01 CFD", "20": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "21": "DR. MARTENS PLC ORD GBP0.01", "22": "DR MARTENS PLC CFD", "23": "DRO-<PERSON> Plc - (Std Option/Amer/Dlv)", "24": "DR. MARTENS PLC ORD GBP0.01"}, "priceFormingData.initialQuantity": {"0": 130.0, "1": 100.0, "2": 1.0, "3": 130.0, "4": 100.0, "5": 1.0, "6": 130.0, "7": 100.0, "8": 1.0, "9": 130.0, "10": 100.0, "11": 1.0, "12": 130.0, "13": 100.0, "14": 1.0, "15": 130.0, "16": 100.0, "17": 1.0, "18": 130.0, "19": 100.0, "20": 1.0, "21": 130.0, "22": 100.0, "23": 1.0, "24": 130.0}, "counterparty.name": {"0": "Counterparty 1", "1": "Counterparty 1", "2": "Counterparty 1", "3": "Counterparty 1", "4": "Counterparty 1", "5": "Counterparty 1", "6": "Counterparty 1", "7": "Counterparty 1", "8": "Counterparty 1", "9": "Counterparty 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Counterparty 1", "13": "Counterparty 1", "14": "Counterparty 1", "15": "Counterparty 1", "16": "Counterparty 1", "17": "Counterparty 1", "18": "Counterparty 1", "19": "Counterparty 1", "20": "Counterparty 1", "21": "Counterparty 1", "22": "Counterparty 1", "23": "Counterparty 1", "24": "Counterparty 1"}, "buyer.name": {"0": "Counterparty 1", "1": "Counterparty 1", "2": null, "3": "Counterparty 1", "4": "Counterparty 1", "5": null, "6": "Counterparty 1", "7": "Counterparty 1", "8": null, "9": "Client 1", "10": null, "11": null, "12": "Counterparty 1", "13": "Counterparty 1", "14": null, "15": "Counterparty 1", "16": "Counterparty 1", "17": null, "18": "Counterparty 1", "19": "Counterparty 1", "20": null, "21": "Counterparty 1", "22": "Counterparty 1", "23": null, "24": "Counterparty 1"}, "seller.name": {"0": "Client 1", "1": null, "2": "Counterparty 1", "3": "Client 1", "4": null, "5": "Counterparty 1", "6": "Client 1", "7": null, "8": "Counterparty 1", "9": "Counterparty 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Client 1", "13": null, "14": "Counterparty 1", "15": "Client 1", "16": null, "17": "Counterparty 1", "18": "Client 1", "19": null, "20": "Counterparty 1", "21": "Client 1", "22": null, "23": "Counterparty 1", "24": "Client 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "1": null, "2": null, "3": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "4": null, "5": null, "6": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "7": null, "8": null, "9": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "10": null, "11": null, "12": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "13": null, "14": null, "15": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "16": null, "17": null, "18": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "19": null, "20": null, "21": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "22": null, "23": null, "24": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1", "1": null, "2": null, "3": "Client 1", "4": null, "5": null, "6": "Client 1", "7": null, "8": null, "9": "Client 1", "10": null, "11": null, "12": "Client 1", "13": null, "14": null, "15": "Client 1", "16": null, "17": null, "18": "Client 1", "19": null, "20": null, "21": "Client 1", "22": null, "23": null, "24": "Client 1"}, "__client__": {"0": "Client 1", "1": null, "2": null, "3": "Client 1", "4": null, "5": null, "6": "Client 1", "7": null, "8": null, "9": "Client 1", "10": null, "11": null, "12": "Client 1", "13": null, "14": null, "15": "Client 1", "16": null, "17": null, "18": "Client 1", "19": null, "20": null, "21": "Client 1", "22": null, "23": null, "24": "Client 1"}, "trader.&id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null}, "trader.name": {"0": "Trader 1", "1": "Trader 1", "2": "Trader 1", "3": "Trader 1", "4": "Trader 1", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 3", "13": "Trader 3", "14": "Trader 3", "15": "Trader 3", "16": "Trader 3", "17": "Trader 3", "18": "Trader 2", "19": "Trader 2", "20": "Trader 2", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1", "24": "Trader 2"}, "__trader_id__": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null}, "__trader_name__": {"0": "Trader 1", "1": "Trader 1", "2": "Trader 1", "3": "Trader 1", "4": "Trader 1", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 3", "13": "Trader 3", "14": "Trader 3", "15": "Trader 3", "16": "Trader 3", "17": "Trader 3", "18": "Trader 2", "19": "Trader 2", "20": "Trader 2", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1", "24": "Trader 2"}, "__instrument_code__": {"0": "GB00BL6NGV24", "1": "XXXXGB00BL6NGV24CFD", "2": "GB00KKWNTR77", "3": "GB00BL6NGV24", "4": "XXXXGB00BL6NGV24CFD", "5": "GB00KKWNTR77", "6": "GB00BL6NGV24", "7": "XXXXGB00BL6NGV24CFD", "8": "GB00KKWNTR77", "9": "GB00BL6NGV24", "10": "XXXXGB00BL6NGV24CFD", "11": "GB00KKWNTR77", "12": "GB00BL6NGV24", "13": "XXXXGB00BL6NGV24CFD", "14": "GB00KKWNTR77", "15": "GB00BL6NGV24", "16": "XXXXGB00BL6NGV24CFD", "17": "GB00KKWNTR77", "18": "GB00BL6NGV24", "19": "XXXXGB00BL6NGV24CFD", "20": "GB00KKWNTR77", "21": "GB00BL6NGV24", "22": "XXXXGB00BL6NGV24CFD", "23": "GB00KKWNTR77", "24": "GB00BL6NGV24"}, "__venue__": {"0": "XLON", "1": "XXXX", "2": "OPRA", "3": "XLON", "4": "XXXX", "5": "OPRA", "6": "XLON", "7": "XXXX", "8": "OPRA", "9": "XLON", "10": "XXXX", "11": "OPRA", "12": "XLON", "13": "XXXX", "14": "OPRA", "15": "XLON", "16": "XXXX", "17": "OPRA", "18": "XLON", "19": "XXXX", "20": "OPRA", "21": "XLON", "22": "XXXX", "23": "OPRA", "24": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1", "1": null, "2": null, "3": "desk1", "4": null, "5": null, "6": "desk1", "7": null, "8": null, "9": "desk1", "10": null, "11": null, "12": "desk1", "13": null, "14": null, "15": "desk1", "16": null, "17": null, "18": "desk2", "19": null, "20": null, "21": "desk1", "22": null, "23": null, "24": "desk2"}, "instrumentDetails.instrument.ext.exchangeSymbolRoot": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null}, "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode": {"0": null, "1": ["GB00BL6NGV24"], "2": ["GB00BL6NGV24"], "3": null, "4": ["GB00BL6NGV24"], "5": ["GB00BL6NGV24"], "6": null, "7": ["GB00BL6NGV24"], "8": ["GB00BL6NGV24"], "9": null, "10": ["GB00BL6NGV24"], "11": ["GB00BL6NGV24"], "12": null, "13": ["GB00BL6NGV24"], "14": ["GB00BL6NGV24"], "15": null, "16": ["GB00BL6NGV24"], "17": ["GB00BL6NGV24"], "18": null, "19": ["GB00BL6NGV24"], "20": ["GB00BL6NGV24"], "21": null, "22": ["GB00BL6NGV24"], "23": ["GB00BL6NGV24"], "24": null}, "instrumentDetails.instrument.ext.underlyingInstruments.instrumentIdCode": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null}, "instrumentDetails.instrument.issuerOrOperatorOfTradingVenueId": {"0": "213800QPT8YM6NQZPH28", "1": null, "2": "549300UF4R84F48NCH34", "3": "213800QPT8YM6NQZPH28", "4": null, "5": "549300UF4R84F48NCH34", "6": "213800QPT8YM6NQZPH28", "7": null, "8": "549300UF4R84F48NCH34", "9": "213800QPT8YM6NQZPH28", "10": null, "11": "549300UF4R84F48NCH34", "12": "213800QPT8YM6NQZPH28", "13": null, "14": "549300UF4R84F48NCH34", "15": "213800QPT8YM6NQZPH28", "16": null, "17": "549300UF4R84F48NCH34", "18": "213800QPT8YM6NQZPH28", "19": null, "20": "549300UF4R84F48NCH34", "21": "213800QPT8YM6NQZPH28", "22": null, "23": "549300UF4R84F48NCH34", "24": "213800QPT8YM6NQZPH28"}, "relatedSubType": {"0": "issuer", "1": "isinToUnderlying", "2": "isinToUnderlying", "3": "issuer", "4": "isinToUnderlying", "5": "isinToUnderlying", "6": "issuer", "7": "isinToUnderlying", "8": "isinToUnderlying", "9": "issuer", "10": "isinToUnderlying", "11": "isinToUnderlying", "12": "issuer", "13": "isinToUnderlying", "14": "isinToUnderlying", "15": "issuer", "16": "isinToUnderlying", "17": "isinToUnderlying", "18": "issuer", "19": "isinToUnderlying", "20": "isinToUnderlying", "21": "issuer", "22": "isinToUnderlying", "23": "isinToUnderlying", "24": "issuer"}, "relatedType": {"0": "explicitlyRelated", "1": "explicitlyRelated", "2": "explicitlyRelated", "3": "explicitlyRelated", "4": "explicitlyRelated", "5": "explicitlyRelated", "6": "explicitlyRelated", "7": "explicitlyRelated", "8": "explicitlyRelated", "9": "explicitlyRelated", "10": "explicitlyRelated", "11": "explicitlyRelated", "12": "explicitlyRelated", "13": "explicitlyRelated", "14": "explicitlyRelated", "15": "explicitlyRelated", "16": "explicitlyRelated", "17": "explicitlyRelated", "18": "explicitlyRelated", "19": "explicitlyRelated", "20": "explicitlyRelated", "21": "explicitlyRelated", "22": "explicitlyRelated", "23": "explicitlyRelated", "24": "explicitlyRelated"}, "originalRecordMetaKey": {"0": "Order:SpoofingV2|CP|2:1:NEWO:*************", "1": "Order:SpoofingV2|CP|2:1:NEWO:*************", "2": "Order:SpoofingV2|CP|2:1:NEWO:*************", "3": "Order:SpoofingV2|CP|2:1:NEWO:*************", "4": "Order:SpoofingV2|CP|2:1:NEWO:*************", "5": "Order:SpoofingV2|CP|2:1:NEWO:*************", "6": "Order:SpoofingV2|CP|2:1:NEWO:*************", "7": "Order:SpoofingV2|CP|2:1:NEWO:*************", "8": "Order:SpoofingV2|CP|2:1:NEWO:*************", "9": "Order:SpoofingV2|CP|2:1:NEWO:*************", "10": "Order:SpoofingV2|CP|2:1:NEWO:*************", "11": "Order:SpoofingV2|CP|2:1:NEWO:*************", "12": "Order:SpoofingV2|CP|2:1:NEWO:*************", "13": "Order:SpoofingV2|CP|2:1:NEWO:*************", "14": "Order:SpoofingV2|CP|2:1:NEWO:*************", "15": "Order:SpoofingV2|CP|2:1:NEWO:*************", "16": "Order:SpoofingV2|CP|2:1:NEWO:*************", "17": "Order:SpoofingV2|CP|2:1:NEWO:*************", "18": "Order:SpoofingV2|CP|2:1:NEWO:*************", "19": "Order:SpoofingV2|CP|2:1:NEWO:*************", "20": "Order:SpoofingV2|CP|2:1:NEWO:*************", "21": "Order:SpoofingV2|CP|2:1:NEWO:*************", "22": "Order:SpoofingV2|CP|2:1:NEWO:*************", "23": "Order:SpoofingV2|CP|2:1:NEWO:*************", "24": "Order:SpoofingV2|CP|2:1:NEWO:*************"}, "relatedInstrument": {"0": true, "1": true, "2": true, "3": true, "4": true, "5": true, "6": true, "7": true, "8": true, "9": true, "10": true, "11": true, "12": true, "13": true, "14": true, "15": true, "16": true, "17": true, "18": true, "19": true, "20": true, "21": true, "22": true, "23": true, "24": true}}