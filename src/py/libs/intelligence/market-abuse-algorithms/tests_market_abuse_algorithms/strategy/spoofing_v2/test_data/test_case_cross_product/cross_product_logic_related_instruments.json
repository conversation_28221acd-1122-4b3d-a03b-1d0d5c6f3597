{"relatedSubType": {"0": "issuer", "1": "issuer", "2": "issuer", "3": "issuer", "4": "issuer", "5": "issuer", "6": "issuer", "7": "issuer", "8": "issuer", "9": "issuer", "10": "issuer", "11": "issuer", "12": "issuer", "13": "issuer", "14": "issuer", "15": "issuer", "16": "issuer", "17": "issuer", "18": "issuer", "19": "issuer", "20": "issuer", "21": "issuer", "22": "issuer", "23": "issuer", "24": "issuer", "25": "issuer", "26": "issuer", "27": "issuer", "28": "issuer", "29": "issuer", "30": "issuer", "31": "issuer", "32": "issuer", "33": "issuer", "34": "issuer", "35": "issuer", "36": "issuer", "37": "issuer", "38": "issuer", "39": "issuer", "40": "issuer", "41": "issuer", "42": "issuer", "43": "issuer", "44": "issuer", "45": "issuer", "46": "issuer", "47": "isinToUnderlying", "48": "isinToUnderlying", "49": "isinToUnderlying", "50": "isinToUnderlying", "51": "sameUnderlying", "52": "sameUnderlying"}, "&key": {"0": "Order:ORD_ULVR.L_025662296:1:NEWO:1701073983775", "1": "Order:layeringAndBookBalance_10_1:1:NEWO:1689760459715", "2": "Order:layeringAndBookBalance_10_2:1:NEWO:1689760459715", "3": "Order:layeringAndBookBalance_11_1:1:NEWO:1689759910219", "4": "Order:layeringAndBookBalance_11_2:1:NEWO:1689759910219", "5": "Order:layeringAndBookBalance_11_3:1:NEWO:1689759910219", "6": "Order:layeringAndBookBalance_11_4:1:NEWO:1689759910219", "7": "Order:layeringAndBookBalance_11_5:1:NEWO:1689759910219", "8": "Order:layeringAndBookBalance_14_1:1:NEWO:1689765354709", "9": "Order:layeringAndBookBalance_14_2:1:NEWO:1689765354709", "10": "Order:layeringAndBookBalance_14_3:2:NEWO:1689765354709", "11": "Order:layeringAndBookBalance_14_4:2:NEWO:1689765354709", "12": "Order:layeringAndBookBalance_15A_1:1:NEWO:1689758744019", "13": "Order:layeringAndBookBalance_15A_2:1:NEWO:1689758744019", "14": "OrderState:layeringAndBookBalance_15A_3:2:<PERSON>Y<PERSON>ING<PERSON>DBOOKBALANCE15A3LAYERINGANDBOOKBALANCE15A3:FILL:2021-08-24T13:00:05.696336Z:0.0:1689758744019", "15": "Order:layeringAndBookBalance_15A_3:2:NEWO:1689758744019", "16": "Order:layeringAndBookBalance_15_1:1:NEWO:1689759909778", "17": "Order:layeringAndBookBalance_15_2:1:NEWO:1689759909778", "18": "Order:layeringAndBookBalance_15_3:2:NEWO:1689759909778", "19": "OrderState:layeringAndBookBalance_17_1:1:LAYERINGANDBOOKBALANCE171LAYERINGANDBOOKBALANCE17112:REME:2021-08-24T13:00:05.053054Z:0.0:1689758534817", "20": "OrderState:layeringAndBookBalance_17_1:1:LAYERINGANDBOOKBALANCE171LAYERINGANDBOOKBALANCE17122:REME:2021-08-24T13:00:10.053054Z:0.0:1689758534817", "21": "Order:layeringAndBookBalance_17_1:1:NEWO:1689758534817", "22": "Order:layeringAndBookBalance_17_2:1:NEWO:1689758534817", "23": "Order:layeringAndBookBalance_6_1:1:NEWO:1689759404376", "24": "Order:layeringAndBookBalance_6_2:1:NEWO:1689759404376", "25": "Order:layeringAndBookBalance_9_1:1:NEWO:1689765354415", "26": "Order:layeringAndBookBalance_9_2:1:NEWO:1689765354415", "27": "Order:spoofingV2_10.1_1:2:NEWO:1701073983695", "28": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011120210824SELL:PARF:2021-08-24T13:00:05.696336Z:400.0:1701073983695", "29": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011220210824SELL:PARF:2021-08-24T13:00:06.696336Z:300.0:1701073983695", "30": "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011320210824SELL:PARF:2021-08-24T13:00:07.696336Z:250.0:1701073983695", "31": "Order:spoofingV2_10_1:2:NEWO:1689759951048", "32": "Order:spoofingV2_10_2:1:NEWO:1689759951048", "33": "Order:spoofingV2_14_1:2:NEWO:1689762007243", "34": "Order:spoofingV2_14_2:1:NEWO:1689762007243", "35": "OrderState:spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0:1689762007243", "36": "Order:spoofingV2_18_2:2:NEWO:1705925118102", "37": "Order:spoofingV2_5_1:1:NEWO:1689764578048", "38": "Order:spoofingV2_5_2:2:NEWO:1689764578048", "39": "Order:spoofingV2_6_1:2:NEWO:1689761987515", "40": "Order:spoofingV2_6_2:1:NEWO:1689761987515", "41": "Order:spoofingV2_7_1:2:NEWO:1689767205368", "42": "Order:spoofingV2_7_2:1:NEWO:1689767205368", "43": "Order:spoofingV2_8_1:2:NEWO:1689767218864", "44": "Order:spoofingV2_8_2:1:NEWO:1689767218864", "45": "Order:spoofingV2_9_1:2:NEWO:1689760954144", "46": "Order:spoofingV2_9_2:1:NEWO:1689760954144", "47": "Order:spoofingV2_18_3:2:NEWO:1705925118102", "48": "Order:spoofingV2_18_4:2:NEWO:1705925118102", "49": "Order:spoofingV2_18_5:2:NEWO:1705925118102", "50": "Order:spoofingV2_18_6:2:NEWO:1705925118102", "51": "Order:spoofingV2_18_10:2:NEWO:1705925118102", "52": "Order:spoofingV2_18_9:2:NEWO:1705925118102"}, "&id": {"0": "ORD_ULVR.L_025662296:1:NEWO", "1": "layeringAndBookBalance_10_1:1:NEWO", "2": "layeringAndBookBalance_10_2:1:NEWO", "3": "layeringAndBookBalance_11_1:1:NEWO", "4": "layeringAndBookBalance_11_2:1:NEWO", "5": "layeringAndBookBalance_11_3:1:NEWO", "6": "layeringAndBookBalance_11_4:1:NEWO", "7": "layeringAndBookBalance_11_5:1:NEWO", "8": "layeringAndBookBalance_14_1:1:NEWO", "9": "layeringAndBookBalance_14_2:1:NEWO", "10": "layeringAndBookBalance_14_3:2:NEWO", "11": "layeringAndBookBalance_14_4:2:NEWO", "12": "layeringAndBookBalance_15A_1:1:NEWO", "13": "layeringAndBookBalance_15A_2:1:NEWO", "14": "layeringAndBookBalance_15A_3:2:LAYERINGANDBOOKBALANCE15A3LAYERINGANDBOOKBALANCE15A3:FILL:2021-08-24T13:00:05.696336Z:0.0", "15": "layeringAndBookBalance_15A_3:2:NEWO", "16": "layeringAndBookBalance_15_1:1:NEWO", "17": "layeringAndBookBalance_15_2:1:NEWO", "18": "layeringAndBookBalance_15_3:2:NEWO", "19": "layeringAndBookBalance_17_1:1:LAYERINGANDBOOKBALANCE171LAYERINGANDBOOKBALANCE17112:REME:2021-08-24T13:00:05.053054Z:0.0", "20": "layeringAndBookBalance_17_1:1:LAYERINGANDBOOKBALANCE171LAYERINGANDBOOKBALANCE17122:REME:2021-08-24T13:00:10.053054Z:0.0", "21": "layeringAndBookBalance_17_1:1:NEWO", "22": "layeringAndBookBalance_17_2:1:NEWO", "23": "layeringAndBookBalance_6_1:1:NEWO", "24": "layeringAndBookBalance_6_2:1:NEWO", "25": "layeringAndBookBalance_9_1:1:NEWO", "26": "layeringAndBookBalance_9_2:1:NEWO", "27": "spoofingV2_10.1_1:2:NEWO", "28": "spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011120210824SELL:PARF:2021-08-24T13:00:05.696336Z:400.0", "29": "spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011220210824SELL:PARF:2021-08-24T13:00:06.696336Z:300.0", "30": "spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011320210824SELL:PARF:2021-08-24T13:00:07.696336Z:250.0", "31": "spoofingV2_10_1:2:NEWO", "32": "spoofingV2_10_2:1:NEWO", "33": "spoofingV2_14_1:2:NEWO", "34": "spoofingV2_14_2:1:NEWO", "35": "spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0", "36": "spoofingV2_18_2:2:NEWO", "37": "spoofingV2_5_1:1:NEWO", "38": "spoofingV2_5_2:2:NEWO", "39": "spoofingV2_6_1:2:NEWO", "40": "spoofingV2_6_2:1:NEWO", "41": "spoofingV2_7_1:2:NEWO", "42": "spoofingV2_7_2:1:NEWO", "43": "spoofingV2_8_1:2:NEWO", "44": "spoofingV2_8_2:1:NEWO", "45": "spoofingV2_9_1:2:NEWO", "46": "spoofingV2_9_2:1:NEWO", "47": "spoofingV2_18_3:2:NEWO", "48": "spoofingV2_18_4:2:NEWO", "49": "spoofingV2_18_5:2:NEWO", "50": "spoofingV2_18_6:2:NEWO", "51": "spoofingV2_18_10:2:NEWO", "52": "spoofingV2_18_9:2:NEWO"}, "instrumentDetails.instrument.ext.exchangeSymbolRoot": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": null, "48": null, "49": null, "50": null, "51": null, "52": null}, "transactionDetails.venue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON", "24": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "XLON", "30": "XLON", "31": "XLON", "32": "XLON", "33": "XLON", "34": "XLON", "35": "XLON", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON", "46": "XLON", "47": "XXXX", "48": "XEUR", "49": "XEUE", "50": "XEUE", "51": null, "52": null}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "2": "GB00B10RZP78", "3": "GB00B10RZP78", "4": "GB00B10RZP78", "5": "GB00B10RZP78", "6": "GB00B10RZP78", "7": "GB00B10RZP78", "8": "GB00B10RZP78", "9": "GB00B10RZP78", "10": "GB00B10RZP78", "11": "GB00B10RZP78", "12": "GB00B10RZP78", "13": "GB00B10RZP78", "14": "GB00B10RZP78", "15": "GB00B10RZP78", "16": "GB00B10RZP78", "17": "GB00B10RZP78", "18": "GB00B10RZP78", "19": "GB00B10RZP78", "20": "GB00B10RZP78", "21": "GB00B10RZP78", "22": "GB00B10RZP78", "23": "GB00B10RZP78", "24": "GB00B10RZP78", "25": "GB00B10RZP78", "26": "GB00B10RZP78", "27": "GB00B10RZP78", "28": "GB00B10RZP78", "29": "GB00B10RZP78", "30": "GB00B10RZP78", "31": "GB00B10RZP78", "32": "GB00B10RZP78", "33": "GB00B10RZP78", "34": "GB00B10RZP78", "35": "GB00B10RZP78", "36": "GB00B10RZP78", "37": "GB00B10RZP78", "38": "GB00B10RZP78", "39": "GB00B10RZP78", "40": "GB00B10RZP78", "41": "GB00B10RZP78", "42": "GB00B10RZP78", "43": "GB00B10RZP78", "44": "GB00B10RZP78", "45": "GB00B10RZP78", "46": "GB00B10RZP78", "47": "EZH0QV8H92H2", "48": "DE000F0JT1N3", "49": "NLEN13073841", "50": "NLEN13073775", "51": null, "52": null}, "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": ["GB00B10RZP78"], "48": ["GB00B10RZP78"], "49": ["GB00B10RZP78"], "50": ["GB00B10RZP78"], "51": null, "52": null}, "instrumentDetails.instrument.ext.underlyingInstruments.instrumentIdCode": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": null, "48": null, "49": null, "50": null, "51": null, "52": null}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "GB00B10RZP78GBPXLON", "1": "GB00B10RZP78GBPXLON", "2": "GB00B10RZP78GBPXLON", "3": "GB00B10RZP78GBPXLON", "4": "GB00B10RZP78GBPXLON", "5": "GB00B10RZP78GBPXLON", "6": "GB00B10RZP78GBPXLON", "7": "GB00B10RZP78GBPXLON", "8": "GB00B10RZP78GBPXLON", "9": "GB00B10RZP78GBPXLON", "10": "GB00B10RZP78GBPXLON", "11": "GB00B10RZP78GBPXLON", "12": "GB00B10RZP78GBPXLON", "13": "GB00B10RZP78GBPXLON", "14": "GB00B10RZP78GBPXLON", "15": "GB00B10RZP78GBPXLON", "16": "GB00B10RZP78GBPXLON", "17": "GB00B10RZP78GBPXLON", "18": "GB00B10RZP78GBPXLON", "19": "GB00B10RZP78GBPXLON", "20": "GB00B10RZP78GBPXLON", "21": "GB00B10RZP78GBPXLON", "22": "GB00B10RZP78GBPXLON", "23": "GB00B10RZP78GBPXLON", "24": "GB00B10RZP78GBPXLON", "25": "GB00B10RZP78GBPXLON", "26": "GB00B10RZP78GBPXLON", "27": "GB00B10RZP78GBPXLON", "28": "GB00B10RZP78GBPXLON", "29": "GB00B10RZP78GBPXLON", "30": "GB00B10RZP78GBPXLON", "31": "GB00B10RZP78GBPXLON", "32": "GB00B10RZP78GBPXLON", "33": "GB00B10RZP78GBPXLON", "34": "GB00B10RZP78GBPXLON", "35": "GB00B10RZP78GBPXLON", "36": "GB00B10RZP78GBPXLON", "37": "GB00B10RZP78GBPXLON", "38": "GB00B10RZP78GBPXLON", "39": "GB00B10RZP78GBPXLON", "40": "GB00B10RZP78GBPXLON", "41": "GB00B10RZP78GBPXLON", "42": "GB00B10RZP78GBPXLON", "43": "GB00B10RZP78GBPXLON", "44": "GB00B10RZP78GBPXLON", "45": "GB00B10RZP78GBPXLON", "46": "GB00B10RZP78GBPXLON", "47": "EZH0QV8H92H2GBPXXXX", "48": "DE000F0JT1N3EURXEUR", "49": "NLEN13073841EURXEUE", "50": "NLEN13073775EURXEUE", "51": null, "52": null}, "instrumentDetails.instrument.issuerOrOperatorOfTradingVenueId": {"0": "549300MKFYEKVRWML317", "1": "549300MKFYEKVRWML317", "2": "549300MKFYEKVRWML317", "3": "549300MKFYEKVRWML317", "4": "549300MKFYEKVRWML317", "5": "549300MKFYEKVRWML317", "6": "549300MKFYEKVRWML317", "7": "549300MKFYEKVRWML317", "8": "549300MKFYEKVRWML317", "9": "549300MKFYEKVRWML317", "10": "549300MKFYEKVRWML317", "11": "549300MKFYEKVRWML317", "12": "549300MKFYEKVRWML317", "13": "549300MKFYEKVRWML317", "14": "549300MKFYEKVRWML317", "15": "549300MKFYEKVRWML317", "16": "549300MKFYEKVRWML317", "17": "549300MKFYEKVRWML317", "18": "549300MKFYEKVRWML317", "19": "549300MKFYEKVRWML317", "20": "549300MKFYEKVRWML317", "21": "549300MKFYEKVRWML317", "22": "549300MKFYEKVRWML317", "23": "549300MKFYEKVRWML317", "24": "549300MKFYEKVRWML317", "25": "549300MKFYEKVRWML317", "26": "549300MKFYEKVRWML317", "27": "549300MKFYEKVRWML317", "28": "549300MKFYEKVRWML317", "29": "549300MKFYEKVRWML317", "30": "549300MKFYEKVRWML317", "31": "549300MKFYEKVRWML317", "32": "549300MKFYEKVRWML317", "33": "549300MKFYEKVRWML317", "34": "549300MKFYEKVRWML317", "35": "549300MKFYEKVRWML317", "36": "549300MKFYEKVRWML317", "37": "549300MKFYEKVRWML317", "38": "549300MKFYEKVRWML317", "39": "549300MKFYEKVRWML317", "40": "549300MKFYEKVRWML317", "41": "549300MKFYEKVRWML317", "42": "549300MKFYEKVRWML317", "43": "549300MKFYEKVRWML317", "44": "549300MKFYEKVRWML317", "45": "549300MKFYEKVRWML317", "46": "549300MKFYEKVRWML317", "47": null, "48": "529900UT4DG0LG5R9O07", "49": "724500V6UOK62XEZ2L78", "50": "724500V6UOK62XEZ2L78", "51": null, "52": null}, "originalRecordMetaKey": {"0": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "1": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "2": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "3": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "4": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "5": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "6": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "7": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "8": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "9": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "10": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "11": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "12": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "13": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "14": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "15": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "16": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "17": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "18": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "19": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "20": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "21": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "22": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "23": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "24": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "25": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "26": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "27": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "28": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "29": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "30": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "31": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "32": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "33": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "34": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "35": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "36": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "37": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "38": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "39": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "40": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "41": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "42": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "43": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "44": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "45": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "46": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "47": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "48": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "49": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "50": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "51": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "52": "Order:spoofingV2_18_1:1:NEWO:1705925118102"}, "relatedType": {"0": "explicitlyRelated", "1": "explicitlyRelated", "2": "explicitlyRelated", "3": "explicitlyRelated", "4": "explicitlyRelated", "5": "explicitlyRelated", "6": "explicitlyRelated", "7": "explicitlyRelated", "8": "explicitlyRelated", "9": "explicitlyRelated", "10": "explicitlyRelated", "11": "explicitlyRelated", "12": "explicitlyRelated", "13": "explicitlyRelated", "14": "explicitlyRelated", "15": "explicitlyRelated", "16": "explicitlyRelated", "17": "explicitlyRelated", "18": "explicitlyRelated", "19": "explicitlyRelated", "20": "explicitlyRelated", "21": "explicitlyRelated", "22": "explicitlyRelated", "23": "explicitlyRelated", "24": "explicitlyRelated", "25": "explicitlyRelated", "26": "explicitlyRelated", "27": "explicitlyRelated", "28": "explicitlyRelated", "29": "explicitlyRelated", "30": "explicitlyRelated", "31": "explicitlyRelated", "32": "explicitlyRelated", "33": "explicitlyRelated", "34": "explicitlyRelated", "35": "explicitlyRelated", "36": "explicitlyRelated", "37": "explicitlyRelated", "38": "explicitlyRelated", "39": "explicitlyRelated", "40": "explicitlyRelated", "41": "explicitlyRelated", "42": "explicitlyRelated", "43": "explicitlyRelated", "44": "explicitlyRelated", "45": "explicitlyRelated", "46": "explicitlyRelated", "47": "explicitlyRelated", "48": "explicitlyRelated", "49": "explicitlyRelated", "50": "explicitlyRelated", "51": "explicitlyCorrelated", "52": "explicitlyCorrelated"}, "originalISIN": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": null, "48": null, "49": null, "50": null, "51": "GB00B10RZP78", "52": "GB00B10RZP78"}, "Index ISIN": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": null, "48": null, "49": null, "50": null, "51": "GB0001383545", "52": "GB0001383545"}, "underlyingColumn": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null, "46": null, "47": null, "48": null, "49": null, "50": null, "51": "GB0001383545", "52": "GB0001383545"}}