{"&id": {"0": "spoofingV2_18_1:1:NEWO"}, "clientFileIdentifier": {"0": "account:client1"}, "&key": {"0": "Order:spoofingV2_18_1:1:NEWO:*************"}, "traderFileIdentifier": {"0": "account:trader1"}, "counterpartyFileIdentifier": {"0": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": *************}, "timestamps.orderSubmitted": {"0": *************}, "orderIdentifiers.orderIdCode": {"0": "spoofingV2_18_1"}, "executionDetails.orderType": {"0": "Market"}, "executionDetails.orderStatus": {"0": "NEWO"}, "executionDetails.buySellIndicator": {"0": "BUYI"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 1"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "*******************"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78"}, "instrumentDetails.instrument.instrumentFullName": {"0": "UNILEVER PLC ORD 3 1/9P"}, "priceFormingData.initialQuantity": {"0": 500}, "counterparty.name": {"0": "Counterparty 1"}, "buyer.name": {"0": "Client 1"}, "seller.name": {"0": "Counterparty 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1"}, "__client__": {"0": "Client 1"}, "trader.&id": {"0": null}, "trader.name": {"0": "Trader 1"}, "__trader_id__": {"0": null}, "__trader_name__": {"0": "Trader 1"}, "__instrument_code__": {"0": "GB00B10RZP78"}, "__venue__": {"0": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1"}, "venue": {"0": "XLON"}, "instrumentDetails.instrument.IdVenueKey": {"0": "XLON:GB00B10RZP78"}, "RIC": {"0": "ULVR.L"}, "spoofOrderStates": {"0": {"clientFileIdentifier": {"0": "account:client1"}, "&parent": {"0": "spoofingV2_18_1:1:NEWO"}, "&key": {"0": "OrderState:spoofingV2_18_1:1:SPOOFINGV2181BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:*************"}, "traderFileIdentifier": {"0": "account:trader1"}, "counterpartyFileIdentifier": {"0": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": *************}, "orderIdentifiers.orderIdCode": {"0": "spoofingV2_18_1"}, "executionDetails.orderType": {"0": "Market"}, "executionDetails.orderStatus": {"0": "CAME"}, "executionDetails.buySellIndicator": {"0": "BUYI"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 1"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "*******************"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78"}, "instrumentDetails.instrument.instrumentFullName": {"0": "UNILEVER PLC ORD 3 1/9P"}, "priceFormingData.initialQuantity": {"0": 500}, "priceFormingData.tradedQuantity": {"0": 0}, "counterparty.name": {"0": "Counterparty 1"}, "buyer.name": {"0": "Client 1"}, "seller.name": {"0": "Counterparty 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1"}, "__client__": {"0": "Client 1"}, "trader.&id": {"0": null}, "trader.name": {"0": "Trader 1"}, "__trader_id__": {"0": null}, "__trader_name__": {"0": "Trader 1"}, "__instrument_code__": {"0": "GB00B10RZP78"}, "__venue__": {"0": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1"}}}, "orderEntryTime": {"0": *************}, "orderCancelTime": {"0": *************}, "spoofOrderExecutedQuantity": {"0": 0}, "orderTimeToCancel": {"0": 5643}, "timedelta": {"0": 0}, "timeGroups": {"0": 0}, "levelOfOrderBook": {"0": 1}, "volumeLevel": {"0": 1204}, "percentageLevel": {"0": 0.415282392}, "realOrders": {"0": {"&id": {"0": "spoofingV2_18_2:2:NEWO", "1": "ORD_ULVR.L_025662296:1:NEWO", "15": "layeringAndBookBalance_15A_3:2:NEWO", "18": "layeringAndBookBalance_15_3:2:NEWO", "25": "spoofingV2_10.1_1:2:NEWO", "26": "spoofingV2_10_1:2:NEWO", "27": "spoofingV2_10_2:1:NEWO", "28": "spoofingV2_14_1:2:NEWO", "29": "spoofingV2_14_2:1:NEWO", "30": "spoofingV2_18_10:2:NEWO", "32": "spoofingV2_18_4:2:NEWO", "33": "spoofingV2_18_5:2:NEWO", "34": "spoofingV2_18_6:2:NEWO", "35": "spoofingV2_18_9:2:NEWO", "36": "spoofingV2_5_1:1:NEWO", "37": "spoofingV2_5_2:2:NEWO", "38": "spoofingV2_6_1:2:NEWO", "39": "spoofingV2_6_2:1:NEWO", "40": "spoofingV2_7_1:2:NEWO", "41": "spoofingV2_7_2:1:NEWO", "42": "spoofingV2_8_1:2:NEWO", "43": "spoofingV2_8_2:1:NEWO", "44": "spoofingV2_9_1:2:NEWO", "45": "spoofingV2_9_2:1:NEWO"}, "clientFileIdentifier": {"0": "account:client1", "1": "account:client1", "15": "account:client1", "18": "account:client1", "25": "account:client1", "26": "account:client1", "27": "account:client1", "28": "account:client1", "29": "account:client1", "30": "account:client1", "32": "account:client1", "33": "account:client1", "34": "account:client1", "35": "account:client1", "36": "account:client1", "37": "account:client1", "38": "account:client1", "39": "account:client1", "40": "account:client1", "41": "account:client1", "42": "account:client1", "43": "account:client1", "44": "account:client1", "45": "account:client1"}, "&key": {"0": "Order:spoofingV2_18_2:2:NEWO:*************", "1": "Order:ORD_ULVR.L_025662296:1:NEWO:*************", "15": "Order:layeringAndBookBalance_15A_3:2:NEWO:*************", "18": "Order:layeringAndBookBalance_15_3:2:NEWO:*************", "25": "Order:spoofingV2_10.1_1:2:NEWO:*************", "26": "Order:spoofingV2_10_1:2:NEWO:*************", "27": "Order:spoofingV2_10_2:1:NEWO:*************", "28": "Order:spoofingV2_14_1:2:NEWO:*************", "29": "Order:spoofingV2_14_2:1:NEWO:*************", "30": "Order:spoofingV2_18_10:2:NEWO:*************", "32": "Order:spoofingV2_18_4:2:NEWO:*************", "33": "Order:spoofingV2_18_5:2:NEWO:*************", "34": "Order:spoofingV2_18_6:2:NEWO:*************", "35": "Order:spoofingV2_18_9:2:NEWO:*************", "36": "Order:spoofingV2_5_1:1:NEWO:*************", "37": "Order:spoofingV2_5_2:2:NEWO:*************", "38": "Order:spoofingV2_6_1:2:NEWO:*************", "39": "Order:spoofingV2_6_2:1:NEWO:*************", "40": "Order:spoofingV2_7_1:2:NEWO:*************", "41": "Order:spoofingV2_7_2:1:NEWO:*************", "42": "Order:spoofingV2_8_1:2:NEWO:*************", "43": "Order:spoofingV2_8_2:1:NEWO:*************", "44": "Order:spoofingV2_9_1:2:NEWO:*************", "45": "Order:spoofingV2_9_2:1:NEWO:*************"}, "traderFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "15": "account:trader1", "18": "account:trader1", "25": "account:trader1", "26": "account:trader1", "27": "account:trader1", "28": "account:trader1", "29": "account:trader1", "30": "account:trader1", "32": "account:trader1", "33": "account:trader1", "34": "account:trader1", "35": "account:trader1", "36": "account:trader1", "37": "account:trader1", "38": "account:trader1", "39": "account:trader1", "40": "account:trader1", "41": "account:trader1", "42": "account:trader1", "43": "account:trader1", "44": "account:trader1", "45": "account:trader1"}, "counterpartyFileIdentifier": {"0": "account:counterparty1", "1": "account:counterparty1", "15": "account:counterparty1", "18": "account:counterparty1", "25": "account:counterparty1", "26": "account:counterparty1", "27": "account:counterparty1", "28": "account:counterparty1", "29": "account:counterparty1", "30": "account:counterparty1", "32": "account:counterparty1", "33": "account:counterparty1", "34": "account:counterparty1", "35": "account:counterparty1", "36": "account:counterparty1", "37": "account:counterparty1", "38": "account:counterparty1", "39": "account:counterparty1", "40": "account:counterparty1", "41": "account:counterparty1", "42": "account:counterparty1", "43": "account:counterparty1", "44": "account:counterparty1", "45": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON", "1": "XLON", "15": "XLON", "18": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "XLON", "30": "IFLL", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON", "1": "XLON", "15": "XLON", "18": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "XLON", "30": "IFLL", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73", "1": "lei:894500wota5040khgx73", "15": "lei:894500wota5040khgx73", "18": "lei:894500wota5040khgx73", "25": "lei:894500wota5040khgx73", "26": "lei:894500wota5040khgx73", "27": "lei:894500wota5040khgx73", "28": "lei:894500wota5040khgx73", "29": "lei:894500wota5040khgx73", "30": "lei:894500wota5040khgx73", "32": "lei:894500wota5040khgx73", "33": "lei:894500wota5040khgx73", "34": "lei:894500wota5040khgx73", "35": "lei:894500wota5040khgx73", "36": "lei:894500wota5040khgx73", "37": "lei:894500wota5040khgx73", "38": "lei:894500wota5040khgx73", "39": "lei:894500wota5040khgx73", "40": "lei:894500wota5040khgx73", "41": "lei:894500wota5040khgx73", "42": "lei:894500wota5040khgx73", "43": "lei:894500wota5040khgx73", "44": "lei:894500wota5040khgx73", "45": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye", "1": "SteelEye", "15": "SteelEye", "18": "SteelEye", "25": "SteelEye", "26": "SteelEye", "27": "SteelEye", "28": "SteelEye", "29": "SteelEye", "30": "SteelEye", "32": "SteelEye", "33": "SteelEye", "34": "SteelEye", "35": "SteelEye", "36": "SteelEye", "37": "SteelEye", "38": "SteelEye", "39": "SteelEye", "40": "SteelEye", "41": "SteelEye", "42": "SteelEye", "43": "SteelEye", "44": "SteelEye", "45": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": 1629810000696, "1": *************, "15": 1629810000696, "18": 1629810000696, "25": *************, "26": *************, "27": 1629810000696, "28": *************, "29": 1629810000696, "30": 1629810000696, "32": 1629810000696, "33": 1629810000696, "34": 1629810000696, "35": 1629810000696, "36": *************, "37": 1629810000696, "38": *************, "39": 1629810000696, "40": *************, "41": 1629810000696, "42": *************, "43": 1629810000696, "44": *************, "45": 1629810000696}, "timestamps.orderSubmitted": {"0": 1629810000696, "1": *************, "15": 1629810000696, "18": 1629810000696, "25": *************, "26": *************, "27": 1629810000696, "28": *************, "29": 1629810000696, "30": 1629810000696, "32": 1629810000696, "33": 1629810000696, "34": 1629810000696, "35": 1629810000696, "36": *************, "37": 1629810000696, "38": *************, "39": 1629810000696, "40": *************, "41": 1629810000696, "42": *************, "43": 1629810000696, "44": *************, "45": 1629810000696}, "orderIdentifiers.orderIdCode": {"0": "spoofingV2_18_2", "1": "ORD_ULVR.L_025662296", "15": "layeringAndBookBalance_15A_3", "18": "layeringAndBookBalance_15_3", "25": "spoofingV2_10.1_1", "26": "spoofingV2_10_1", "27": "spoofingV2_10_2", "28": "spoofingV2_14_1", "29": "spoofingV2_14_2", "30": "spoofingV2_18_10", "32": "spoofingV2_18_4", "33": "spoofingV2_18_5", "34": "spoofingV2_18_6", "35": "spoofingV2_18_9", "36": "spoofingV2_5_1", "37": "spoofingV2_5_2", "38": "spoofingV2_6_1", "39": "spoofingV2_6_2", "40": "spoofingV2_7_1", "41": "spoofingV2_7_2", "42": "spoofingV2_8_1", "43": "spoofingV2_8_2", "44": "spoofingV2_9_1", "45": "spoofingV2_9_2"}, "executionDetails.orderType": {"0": "Market", "1": "Market", "15": "Market", "18": "Market", "25": "Market", "26": "Market", "27": "Market", "28": "Market", "29": "Market", "30": "Market", "32": "Market", "33": "Market", "34": "Market", "35": "Market", "36": "Market", "37": "Market", "38": "Market", "39": "Market", "40": "Market", "41": "Market", "42": "Market", "43": "Market", "44": "Market", "45": "Market"}, "executionDetails.orderStatus": {"0": "NEWO", "1": "NEWO", "15": "NEWO", "18": "NEWO", "25": "NEWO", "26": "NEWO", "27": "NEWO", "28": "NEWO", "29": "NEWO", "30": "NEWO", "32": "NEWO", "33": "NEWO", "34": "NEWO", "35": "NEWO", "36": "NEWO", "37": "NEWO", "38": "NEWO", "39": "NEWO", "40": "NEWO", "41": "NEWO", "42": "NEWO", "43": "NEWO", "44": "NEWO", "45": "NEWO"}, "executionDetails.buySellIndicator": {"0": "SELL", "1": "BUYI", "15": "SELL", "18": "SELL", "25": "SELL", "26": "SELL", "27": "BUYI", "28": "SELL", "29": "BUYI", "30": "SELL", "32": "SELL", "33": "SELL", "34": "SELL", "35": "SELL", "36": "BUYI", "37": "SELL", "38": "SELL", "39": "BUYI", "40": "SELL", "41": "BUYI", "42": "SELL", "43": "BUYI", "44": "SELL", "45": "BUYI"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 1", "1": "Trader 12", "15": "Trader 1", "18": "Trader 1", "25": "Trader 12", "26": "Trader 1", "27": "Trader 2", "28": "Trader 1", "29": "Trader 2", "30": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 2", "38": "Trader 1", "39": "Trader 2", "40": "Trader 1", "41": "Trader 2", "42": "Trader 1", "43": "Trader 2", "44": "Trader 1", "45": "Trader 2"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "15": "account:trader1", "18": "account:trader1", "25": "account:trader1", "26": "account:trader1", "27": "account:trader2", "28": "account:trader1", "29": "account:trader2", "30": "account:trader1", "32": "account:trader1", "33": "account:trader1", "34": "account:trader1", "35": "account:trader1", "36": "account:trader1", "37": "account:trader2", "38": "account:trader1", "39": "account:trader2", "40": "account:trader1", "41": "account:trader2", "42": "account:trader1", "43": "account:trader2", "44": "account:trader1", "45": "account:trader2"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO", "1": "XXXXGBPO", "15": "XXXXGBPO", "18": "XXXXGBPO", "25": "XXXXGBPO", "26": "XXXXGBPO", "27": "XXXXGBPO", "28": "XXXXGBPO", "29": "XXXXGBPO", "30": "IFLLZFF2024-03 00:00:00", "32": "XEURUNIPFF2024-01 00:00:00", "33": "XEUEUNOP2024-01 00:00:0048.********", "34": "XEUEUNOC2024-01 00:00:0048.********", "35": "IFLLZFF2024-06 00:00:00", "36": "XXXXGBPO", "37": "XXXXGBPO", "38": "XXXXGBPO", "39": "XXXXGBPO", "40": "XXXXGBPO", "41": "XXXXGBPO", "42": "XXXXGBPO", "43": "XXXXGBPO", "44": "XXXXGBPO", "45": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "*******************", "1": "*******************", "15": "*******************", "18": "*******************", "25": "*******************", "26": "*******************", "27": "*******************", "28": "*******************", "29": "*******************", "30": "*******************", "32": "*******************", "33": "NLEN13073841EURXEUE", "34": "NLEN13073775EURXEUE", "35": "GB00KHLXXF30GBPIFLL", "36": "*******************", "37": "*******************", "38": "*******************", "39": "*******************", "40": "*******************", "41": "*******************", "42": "*******************", "43": "*******************", "44": "*******************", "45": "*******************"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON", "1": "XLON", "15": "XLON", "18": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "XLON", "30": "IFLL", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "15": "GB00B10RZP78", "18": "GB00B10RZP78", "25": "GB00B10RZP78", "26": "GB00B10RZP78", "27": "GB00B10RZP78", "28": "GB00B10RZP78", "29": "GB00B10RZP78", "30": "GB00KHK64W72", "32": "DE000F0JT1N3", "33": "NLEN13073841", "34": "NLEN13073775", "35": "GB00KHLXXF30", "36": "GB00B10RZP78", "37": "GB00B10RZP78", "38": "GB00B10RZP78", "39": "GB00B10RZP78", "40": "GB00B10RZP78", "41": "GB00B10RZP78", "42": "GB00B10RZP78", "43": "GB00B10RZP78", "44": "GB00B10RZP78", "45": "GB00B10RZP78"}, "instrumentDetails.instrument.instrumentFullName": {"0": "UNILEVER PLC ORD 3 1/9P", "1": "UNILEVER PLC ORD 3 1/9P", "15": "UNILEVER PLC ORD 3 1/9P", "18": "UNILEVER PLC ORD 3 1/9P", "25": "UNILEVER PLC ORD 3 1/9P", "26": "UNILEVER PLC ORD 3 1/9P", "27": "UNILEVER PLC ORD 3 1/9P", "28": "UNILEVER PLC ORD 3 1/9P", "29": "UNILEVER PLC ORD 3 1/9P", "30": "Z-FTSE 100 (New Style) Index Future", "32": "UNIP SI 20240102 PS", "33": "AO1UN.2401.04800.P", "34": "AO1UN.2401.04800.C", "35": "Z-FTSE 100 (New Style) Index Future", "36": "UNILEVER PLC ORD 3 1/9P", "37": "UNILEVER PLC ORD 3 1/9P", "38": "UNILEVER PLC ORD 3 1/9P", "39": "UNILEVER PLC ORD 3 1/9P", "40": "UNILEVER PLC ORD 3 1/9P", "41": "UNILEVER PLC ORD 3 1/9P", "42": "UNILEVER PLC ORD 3 1/9P", "43": "UNILEVER PLC ORD 3 1/9P", "44": "UNILEVER PLC ORD 3 1/9P", "45": "UNILEVER PLC ORD 3 1/9P"}, "priceFormingData.initialQuantity": {"0": 500, "1": 500, "15": 50, "18": 50, "25": 500, "26": 500, "27": 500, "28": 500, "29": 500, "30": 500, "32": 500, "33": 500, "34": 500, "35": 500, "36": 500, "37": 500, "38": 500, "39": 500, "40": 500, "41": 500, "42": 500, "43": 500, "44": 500, "45": 500}, "counterparty.name": {"0": "Counterparty 1", "1": "Counterparty 1", "15": "Counterparty 1", "18": "Counterparty 1", "25": "Counterparty 1", "26": "Counterparty 1", "27": "Counterparty 1", "28": "Counterparty 1", "29": "Counterparty 1", "30": "Counterparty 1", "32": "Counterparty 1", "33": "Counterparty 1", "34": "Counterparty 1", "35": "Counterparty 1", "36": "Counterparty 1", "37": "Counterparty 1", "38": "Counterparty 1", "39": "Counterparty 1", "40": "Counterparty 1", "41": "Counterparty 1", "42": "Counterparty 1", "43": "Counterparty 1", "44": "Counterparty 1", "45": "Counterparty 1"}, "buyer.name": {"0": "Counterparty 1", "1": "Client 1", "15": "Counterparty 1", "18": "Counterparty 1", "25": "Counterparty 1", "26": "Counterparty 1", "27": "Client 1", "28": "Counterparty 1", "29": "Client 1", "30": "Counterparty 1", "32": "Counterparty 1", "33": "Counterparty 1", "34": "Counterparty 1", "35": "Counterparty 1", "36": "Client 1", "37": "Counterparty 1", "38": "Counterparty 1", "39": "Client 1", "40": "Counterparty 1", "41": "Client 1", "42": "Counterparty 1", "43": "Client 1", "44": "Counterparty 1", "45": "Client 1"}, "seller.name": {"0": "Client 1", "1": "Counterparty 1", "15": "Client 1", "18": "Client 1", "25": "Client 1", "26": "Client 1", "27": "Counterparty 1", "28": "Client 1", "29": "Counterparty 1", "30": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Counterparty 1", "37": "Client 1", "38": "Client 1", "39": "Counterparty 1", "40": "Client 1", "41": "Counterparty 1", "42": "Client 1", "43": "Counterparty 1", "44": "Client 1", "45": "Counterparty 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "1": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "15": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "18": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "25": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "26": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "27": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "28": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "29": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "30": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "32": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "33": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "34": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "35": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "36": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "37": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "38": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "39": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "40": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "41": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "42": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "43": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "44": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "45": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1", "1": "Client 1", "15": "Client 1", "18": "Client 1", "25": "Client 1", "26": "Client 1", "27": "Client 1", "28": "Client 1", "29": "Client 1", "30": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Client 1", "37": "Client 1", "38": "Client 1", "39": "Client 1", "40": "Client 1", "41": "Client 1", "42": "Client 1", "43": "Client 1", "44": "Client 1", "45": "Client 1"}, "__client__": {"0": "Client 1", "1": "Client 1", "15": "Client 1", "18": "Client 1", "25": "Client 1", "26": "Client 1", "27": "Client 1", "28": "Client 1", "29": "Client 1", "30": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Client 1", "37": "Client 1", "38": "Client 1", "39": "Client 1", "40": "Client 1", "41": "Client 1", "42": "Client 1", "43": "Client 1", "44": "Client 1", "45": "Client 1"}, "trader.&id": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "trader.name": {"0": "Trader 1", "1": "Trader 12", "15": "Trader 1", "18": "Trader 1", "25": "Trader 12", "26": "Trader 1", "27": "Trader 1", "28": "Trader 1", "29": "Trader 1", "30": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 1", "38": "Trader 1", "39": "Trader 1", "40": "Trader 1", "41": "Trader 1", "42": "Trader 1", "43": "Trader 1", "44": "Trader 1", "45": "Trader 1"}, "__trader_id__": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "__trader_name__": {"0": "Trader 1", "1": "Trader 12", "15": "Trader 1", "18": "Trader 1", "25": "Trader 12", "26": "Trader 1", "27": "Trader 1", "28": "Trader 1", "29": "Trader 1", "30": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 1", "38": "Trader 1", "39": "Trader 1", "40": "Trader 1", "41": "Trader 1", "42": "Trader 1", "43": "Trader 1", "44": "Trader 1", "45": "Trader 1"}, "__instrument_code__": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "15": "GB00B10RZP78", "18": "GB00B10RZP78", "25": "GB00B10RZP78", "26": "GB00B10RZP78", "27": "GB00B10RZP78", "28": "GB00B10RZP78", "29": "GB00B10RZP78", "30": "GB00KHK64W72", "32": "DE000F0JT1N3", "33": "NLEN13073841", "34": "NLEN13073775", "35": "GB00KHLXXF30", "36": "GB00B10RZP78", "37": "GB00B10RZP78", "38": "GB00B10RZP78", "39": "GB00B10RZP78", "40": "GB00B10RZP78", "41": "GB00B10RZP78", "42": "GB00B10RZP78", "43": "GB00B10RZP78", "44": "GB00B10RZP78", "45": "GB00B10RZP78"}, "__venue__": {"0": "XLON", "1": "XLON", "15": "XLON", "18": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "XLON", "30": "IFLL", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1", "1": "desk1", "15": "desk1", "18": "desk1", "25": "desk1", "26": "desk1", "27": "desk2", "28": "desk1", "29": "desk2", "30": "desk1", "32": "desk1", "33": "desk1", "34": "desk1", "35": "desk1", "36": "desk1", "37": "desk2", "38": "desk1", "39": "desk2", "40": "desk1", "41": "desk2", "42": "desk1", "43": "desk2", "44": "desk1", "45": "desk2"}, "relatedInstrument": {"0": false, "1": true, "15": true, "18": true, "25": true, "26": true, "27": true, "28": true, "29": true, "30": true, "32": true, "33": true, "34": true, "35": true, "36": true, "37": true, "38": true, "39": true, "40": true, "41": true, "42": true, "43": true, "44": true, "45": true}, "executionDetails.limitPrice": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "Index ISIN": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": "GB0001383545", "32": null, "33": null, "34": null, "35": "GB0001383545", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "relatedType": {"0": null, "1": "explicitlyRelated", "15": "explicitlyRelated", "18": "explicitlyRelated", "25": "explicitlyRelated", "26": "explicitlyRelated", "27": "explicitlyRelated", "28": "explicitlyRelated", "29": "explicitlyRelated", "30": "explicitlyCorrelated", "32": "explicitlyRelated", "33": "explicitlyRelated", "34": "explicitlyRelated", "35": "explicitlyCorrelated", "36": "explicitlyRelated", "37": "explicitlyRelated", "38": "explicitlyRelated", "39": "explicitlyRelated", "40": "explicitlyRelated", "41": "explicitlyRelated", "42": "explicitlyRelated", "43": "explicitlyRelated", "44": "explicitlyRelated", "45": "explicitlyRelated"}, "instrumentDetails.instrument.ext.underlyingInstruments.instrumentIdCode": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "originalRecordMetaKey": {"0": null, "1": "Order:spoofingV2_18_1:1:NEWO:*************", "15": "Order:spoofingV2_18_1:1:NEWO:*************", "18": "Order:spoofingV2_18_1:1:NEWO:*************", "25": "Order:spoofingV2_18_1:1:NEWO:*************", "26": "Order:spoofingV2_18_1:1:NEWO:*************", "27": "Order:spoofingV2_18_1:1:NEWO:*************", "28": "Order:spoofingV2_18_1:1:NEWO:*************", "29": "Order:spoofingV2_18_1:1:NEWO:*************", "30": "Order:spoofingV2_18_1:1:NEWO:*************", "32": "Order:spoofingV2_18_1:1:NEWO:*************", "33": "Order:spoofingV2_18_1:1:NEWO:*************", "34": "Order:spoofingV2_18_1:1:NEWO:*************", "35": "Order:spoofingV2_18_1:1:NEWO:*************", "36": "Order:spoofingV2_18_1:1:NEWO:*************", "37": "Order:spoofingV2_18_1:1:NEWO:*************", "38": "Order:spoofingV2_18_1:1:NEWO:*************", "39": "Order:spoofingV2_18_1:1:NEWO:*************", "40": "Order:spoofingV2_18_1:1:NEWO:*************", "41": "Order:spoofingV2_18_1:1:NEWO:*************", "42": "Order:spoofingV2_18_1:1:NEWO:*************", "43": "Order:spoofingV2_18_1:1:NEWO:*************", "44": "Order:spoofingV2_18_1:1:NEWO:*************", "45": "Order:spoofingV2_18_1:1:NEWO:*************"}, "instrumentDetails.instrument.issuerOrOperatorOfTradingVenueId": {"0": null, "1": "549300MKFYEKVRWML317", "15": "549300MKFYEKVRWML317", "18": "549300MKFYEKVRWML317", "25": "549300MKFYEKVRWML317", "26": "549300MKFYEKVRWML317", "27": "549300MKFYEKVRWML317", "28": "549300MKFYEKVRWML317", "29": "549300MKFYEKVRWML317", "30": null, "32": "529900UT4DG0LG5R9O07", "33": "724500V6UOK62XEZ2L78", "34": "724500V6UOK62XEZ2L78", "35": null, "36": "549300MKFYEKVRWML317", "37": "549300MKFYEKVRWML317", "38": "549300MKFYEKVRWML317", "39": "549300MKFYEKVRWML317", "40": "549300MKFYEKVRWML317", "41": "549300MKFYEKVRWML317", "42": "549300MKFYEKVRWML317", "43": "549300MKFYEKVRWML317", "44": "549300MKFYEKVRWML317", "45": "549300MKFYEKVRWML317"}, "underlyingColumn": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": "GB0001383545", "32": null, "33": null, "34": null, "35": "GB0001383545", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "instrumentDetails.instrument.ext.exchangeSymbolRoot": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "originalISIN": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": "GB00B10RZP78", "32": null, "33": null, "34": null, "35": "GB00B10RZP78", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "relatedSubType": {"0": null, "1": "issuer", "15": "issuer", "18": "issuer", "25": "issuer", "26": "issuer", "27": "issuer", "28": "issuer", "29": "issuer", "30": "sameUnderlying", "32": "isinToUnderlying", "33": "isinToUnderlying", "34": "isinToUnderlying", "35": "sameUnderlying", "36": "issuer", "37": "issuer", "38": "issuer", "39": "issuer", "40": "issuer", "41": "issuer", "42": "issuer", "43": "issuer", "44": "issuer", "45": "issuer"}, "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode": {"0": null, "1": null, "15": null, "18": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "32": ["GB00B10RZP78"], "33": ["GB00B10RZP78"], "34": ["GB00B10RZP78"], "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "realOrdersExecutedQuantity": {"0": 0, "1": 0, "15": 50, "18": 0, "25": 250, "26": 250, "27": 0, "28": 500, "29": 1, "30": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 500, "40": 0, "41": 0, "42": 500, "43": 0, "44": 500, "45": 0}, "realOrdersFilledPercentage": {"0": 0.0, "1": 0.0, "15": 1.0, "18": 0.0, "25": 0.5, "26": 0.5, "27": 0.0, "28": 1.0, "29": 0.002, "30": 0.0, "32": 0.0, "33": 0.0, "34": 0.0, "35": 0.0, "36": 0.0, "37": 0.0, "38": 0.0, "39": 1.0, "40": 0.0, "41": 0.0, "42": 1.0, "43": 0.0, "44": 1.0, "45": 0.0}}}, "spoofOrdersMidPrice": {"0": 41.1075}, "realOrdersMidPrice": {"0": 41.1075}, "percentagePriceImprovement": {"0": 0.0}, "numberSpoofOrders": {"0": 1}, "numberOfRealOrders": {"0": 24}, "spoofOrderSide": {"0": "BUYI"}, "spoofOrderQuantity": {"0": 500}, "realOrdersQuantity": {"0": 11100}, "realOrdersExecutedQuantity": {"0": 0}, "ordersRatio": {"0": 182.7586206897}}