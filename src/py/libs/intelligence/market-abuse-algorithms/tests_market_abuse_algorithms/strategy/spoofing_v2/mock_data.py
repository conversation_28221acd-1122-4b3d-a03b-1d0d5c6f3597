# ruff: noqa: E501
import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
import market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy
import market_abuse_algorithms.strategy.spoofing_v2.strategy  # TODO
import pandas as pd
from market_abuse_algorithms.cross_product.cross_product import (
    CrossProductActivityDisplay,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from pathlib import Path


class MarketDataAPI:
    pass


class MockMarketDataClient(MarketDataClient):
    def _get_se_market_data_api(self):
        return MarketDataAPI()

    def get_ric_map(
        self,
        list_inst_unique_id=None,
        instrument_combinations=None,
        use_prefix_query=False,
    ):
        return {"XLON:GB00B10RZP78": "ULVR.L"}

    def get_order_book_depth_data(self, *args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "ULVR.L__QUOTE_LEVEL2__TICK__20210824.parquet"
        )
        data = pd.read_parquet(file_path)
        return data

    def get_local_ric_map(self, list_id_venue=None, instrument_combinations=None):
        if instrument_combinations == [["XLON:GB00BK63S759"]]:
            return {"XLON:GB00BK63S759": "BRCK.L"}


def mock_spoofing_data(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "time_cancel_filtered_data_client_one_hit.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "test_evaluation_by_client_one_hit_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_nohits1(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_data_evaluation_by_client_no_hits_spoof_time_1.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "test_evaluation_by_client_no_hits_spoof_time_1.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_nohits2(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_data_evaluation_by_client_no_hits_spoof_time_2.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "test_evaluation_by_client_no_hits_spoof_time_2.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_trader(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_data_evaluation_by_trader.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = Path(__file__).parent / "test_data" / "test_evaluation_by_tarder.csv"
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_50percentagefilled_nohits(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_evaluation_by_client_50_percentage_filled.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "test_evaluation_by_client_50_percentage_filled.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_50percentagefilled_onehit(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_evaluation_by_client_50_percentage_filled_filter2.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "test_evaluation_by_client_50_percentage_filled_filter2.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:1:NEWO:SPOOFINGV2_1B_1GB00B10RZP78:1:CAME:SPOOFINGV2_1B_1GB00B10RZP78:SPOOFINGV2_1B_1||:80.0:1629787812000:1635874525817"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_part_cancellations(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_data_include_part_cancellations.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "test_include_part_cancellations_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1643658031006",
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0:1643658031006",
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_no_child_real_orders(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = (
            Path(__file__).parent
            / "test_data"
            / "time_cancel_filtered_data_no_child_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "test_case_no_child_real_orders_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1643301064369"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.SpoofingV2AbstractStrategy,
        "apply_time_to_cancel_filter",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_no_traded_quantity(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = Path(__file__).parent / "test_data" / "newo_execution_child_no_traded_qtt.csv"
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "test_case_no_child_real_orders_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1643301064369"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_spoof_execs",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


def mock_spoofing_data_no_order_submitted(monkeypatch, *args, **kwargs):
    def mock_spoofing_data(*args, **kwargs):
        file_path = Path(__file__).parent / "test_data" / "newo_execution_child.csv"
        data = pd.read_csv(file_path, index_col=0)
        return data

    def real_orders(*args, **kwargs):
        file_path = (
            Path(__file__).parent / "test_data" / "test_case_no_child_real_orders_real_orders.csv"
        )
        data = pd.read_csv(file_path, index_col=0)
        return data

    def get_child(*args, **kwargs):
        return [
            "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1643301064369"
        ]

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_spoof_execs",
        mock_spoofing_data,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "fetch_real_orders",
        real_orders,
    )
    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy.Queries,
        "get_newo_child",
        get_child,
    )


class FakeCrossProductQuery:
    def __init__(self, es_client, market_data_client):
        self.es_client = es_client
        self.market_data_client = market_data_client

    def get_filter_orders(self, list_of_orders):
        return pd.DataFrame()


class FakeCrossProductWithQuery(CrossProductActivityDisplay):
    def __init__(self, es_client, market_data_client):
        self.records = None
        self.scenario = None
        self.original_orders = None
        self.query = FakeCrossProductQuery(es_client, market_data_client)

    def set_scenario(self, scenario, list_of_orders=None):
        if isinstance(scenario, pd.Series):
            self.scenario = scenario
        else:
            self.scenario = scenario._scenario

    def set_orders(self, orders):
        self.records = orders
        self.original_orders = self.query.get_filter_orders(self.records)

    def related_activity(self, related_records_dict):
        pass
