# ruff: noqa: E501
# type: ignore
import market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy
import pandas as pd
import pytest
from datetime import datetime
from market_abuse_algorithms.cross_product.static import CrossProductColumns, RelatedType
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    ORDER_TYPE_GROUP_MAP,
    AlertColumnsEnum,
    AlgoColumnsEnum,
)
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.spoofing_v2.strategy import Strategy
from pathlib import Path
from se_elastic_schema.components.mar.strategy.spoofing_v2.thresholds import (
    EvaluationTypeEnum,
)
from tests_market_abuse_algorithms.strategy.spoofing_v2.mock_data import (
    FakeCrossProductWithQuery,
    MockMarketDataClient,
    mock_spoofing_data,
    mock_spoofing_data_50percentagefilled_nohits,
    mock_spoofing_data_50percentagefilled_onehit,
    mock_spoofing_data_no_child_real_orders,
    mock_spoofing_data_no_order_submitted,
    mock_spoofing_data_no_traded_quantity,
    mock_spoofing_data_nohits1,
    mock_spoofing_data_nohits2,
    mock_spoofing_data_part_cancellations,
    mock_spoofing_data_trader,
)
from unittest import mock
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")
TEST_DATA_CROSS_PRODUCT = Path(__file__).parent.joinpath("test_data/test_case_cross_product")

FILTER_1 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.spoofingV2.1.B.csv"
                ]
            }
        }
    }
}

FILTER_2 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://mar.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/steeleyeBlotter.mar.spoofingV2.2.B.csv"
                ]
            }
        }
    }
}

FILTER_3 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://storey.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.14.csv"
                ]
            }
        }
    }
}

FILTER_4 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://storey.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.5.csv"
                ]
            }
        }
    }
}


@pytest.fixture
def mock_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def custom_date_parser():
        return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")

    def _foo(*args):
        result = pd.read_csv(
            *args,
            index_col=0,
            sep=",",
            parse_dates=["timestamps.orderStatusUpdated", "timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )
        return result

    return _foo


@pytest.fixture()
def mock_spoof_data_test1(monkeypatch, *args, **kwargs):
    mock_spoofing_data(monkeypatch)


@pytest.fixture()
def mock_spoof_data_test2(monkeypatch, *args, **kwargs):
    mock_spoofing_data_nohits1(monkeypatch)


@pytest.fixture()
def mock_spoof_data_test3(monkeypatch, *args, **kwargs):
    mock_spoofing_data_nohits2(monkeypatch)


@pytest.fixture()
def mock_spoof_data_test4(monkeypatch, *args, **kwargs):
    mock_spoofing_data_trader(monkeypatch)


@pytest.fixture()
def mock_spoof_data_test5(monkeypatch, *args, **kwargs):
    mock_spoofing_data_50percentagefilled_nohits(monkeypatch)


@pytest.fixture()
def mock_spoof_data_test6(monkeypatch, *args, **kwargs):
    mock_spoofing_data_50percentagefilled_onehit(monkeypatch)


@pytest.fixture()
def mock_spoof_data_no_child_real_orders(monkeypatch, *args, **kwargs):
    mock_spoofing_data_no_child_real_orders(monkeypatch)


@pytest.fixture()
def mock_spoof_data_include_part_cancellations(monkeypatch, *args, **kwargs):
    mock_spoofing_data_part_cancellations(monkeypatch)


@pytest.fixture()
def mock_spoof_data_no_traded_quantity(monkeypatch, *args, **kwargs):
    mock_spoofing_data_no_traded_quantity(monkeypatch)


@pytest.fixture()
def mock_spoof_data_no_order_submitted(monkeypatch, *args, **kwargs):
    mock_spoofing_data_no_order_submitted(monkeypatch)


@mock.patch.object(
    market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy,
    "CrossProductActivityDisplay",
    FakeCrossProductWithQuery,
)
class TestSpoofingLevel2:
    def test_cases_to_analyse(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)
        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["XLON:GB00BK63S759"]]

        strategy.queries.scroll_data = MagicMock()
        strategy.queries.scroll_data.return_value = pd.read_csv(
            TEST_DATA.joinpath("cases_to_analyse.csv"), index_col=0
        )

        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = pd.read_csv(
            TEST_DATA.joinpath("newo_execution_child.csv"), index_col=0
        )
        for data in strategy.queries.cases_to_analyse(strategy_name="Spoofing V2"):
            assert not data.empty

    def test_cases_to_analyse_empty(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)
        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["XLON:GB00BK63S759"]]
        strategy.queries.scroll_data = MagicMock()
        strategy.queries.scroll_data.return_value = pd.read_csv(
            TEST_DATA.joinpath("cases_to_analyse.csv"), index_col=0
        )

        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = pd.read_csv(
            TEST_DATA.joinpath("newo_execution_child.csv"), index_col=0
        )

        strategy.queries._filter_orders = MagicMock()
        strategy.queries._filter_orders.return_value = pd.DataFrame()

        for data in strategy.queries.cases_to_analyse(strategy_name="Spoofing V2"):
            assert data.empty

    def test_include_part_cancellations_no_parent_ids(self, helpers, mock_data):
        """Check if the spoof order doesn't have cancellation orders, meaning
        that needs to connect to ES."""
        test_data = pd.read_csv(TEST_DATA.joinpath("test_include_parf_cond.csv"), index_col=0)

        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)

        test_data = test_data.drop(columns=["&id"])

        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = test_data

        processed_result = strategy.queries.process_include_parf_condition(
            data=test_data, include_parf=strategy._th_include_partial_cancellation
        )

        assert processed_result.empty

    def test_include_part_cancellations_threshold_false(self, helpers, mock_data):
        """Check if the spoof order doesn't have cancellation orders, meaning
        that needs to connect to ES."""
        test_data = pd.read_csv(TEST_DATA.joinpath("test_include_parf_cond.csv"), index_col=0)

        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)

        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = test_data

        processed_result = strategy.queries.process_include_parf_condition(
            data=test_data, include_parf=strategy._th_include_partial_cancellation
        )

        assert not processed_result.empty

    def test_include_part_cancellations_child(self, helpers, mock_data):
        """Check if the spoof order has cancellation orders, meaning that needs
        to connect to ES."""
        test_data = pd.read_csv(TEST_DATA.joinpath("test_include_parf_cond.csv"), index_col=0)

        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)

        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = test_data

        processed_result = strategy.queries.process_include_parf_condition(
            data=test_data, include_parf=strategy._th_include_partial_cancellation
        )

        assert not processed_result.empty

    def test_case_no_child_real_orders(
        self,
        helpers,
        mock_data,
        mock_spoof_data_no_child_real_orders,
    ):
        """the real orders don't have executions but the threshold is set to
        zero, so we should have 1 hit."""
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }
        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_4)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_case_no_child_real_orders.csv"))

        strategy._run_algo(data=test_data)

        assert len(strategy.scenarios) == 1

    def test_include_part_cancellations(
        self,
        helpers,
        mock_data,
        mock_spoof_data_include_part_cancellations,
    ):
        """Check if the spoof order & real orders."""
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }
        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_include_part_cancellations.csv"))

        strategy._run_algo(data=test_data)

        scenarios = strategy.scenarios

        assert len(scenarios) == 1

    def test_real_and_fake_orders(
        self,
        helpers,
        mock_data,
        mock_spoof_data_include_part_cancellations,
    ):
        """Check if fake orders and real orders are there."""
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }
        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_3)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_include_part_cancellations.csv"))

        strategy._run_algo(data=test_data)

        scenarios = strategy.scenarios

        assert len(scenarios) == 1

        top_level = scenarios[0]._scenario.get("additionalFields").get("topLevel")
        records = scenarios[0]._scenario.get("records")

        assert records.get("realOrders")[0] == "Order:spoofingV2_14_1:2:NEWO:1643658030930"
        assert records.get("fakeOrders")[0] == "Order:spoofingV2_14_2:1:NEWO:1643658030930"
        assert top_level.get("spoofOrderSide")[0] == "BUYI"
        assert not set(records.get("fakeOrders")).intersection(set(records.get("realOrders")))

    def test_calc_price_improvement(self, helpers, mock_data):
        """Since spoof order mid proce is lower than real order mid price, we
        should apply this formula:

        vPriceImprovementPercentage  = (V1 - V2) / V1 * -1
        """
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 10,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)
        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("spoof_orders_sorted_submitted.csv"))

        test_data.loc[:, AlertColumnsEnum.PERCENTAGE_PRICE_IMPROVEMENT] = test_data.apply(
            lambda x: strategy._calculate_percentage_price_improvement(
                spoof_mid_price=x[AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE],
                real_mid_price=x[AlertColumnsEnum.REAL_ORDERS_MID_PRICE],
            ),
            axis=1,
        )

        expected_price_movement: float = abs(
            (
                test_data[AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE]
                - test_data[AlertColumnsEnum.REAL_ORDERS_MID_PRICE]
            )
            / test_data[AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE]
        )

        assert test_data[AlertColumnsEnum.PERCENTAGE_PRICE_IMPROVEMENT].equals(
            expected_price_movement
        )

    def test_evaluation_type_in_include(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        thresholds = {
            "evaluationType": "executingEntity",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 10,
            "spoofingTimeWindow": 10,
            "priceImprovement": False,
        }
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)
        strategy = Strategy(context=context)

        fields_to_check = strategy.queries.include_fields
        fields_to_check.extend(strategy.queries.fields_for_alert)

        evaluation_type_to_check = list(ORDER_TYPE_GROUP_MAP.values())

        evaluation_type_to_check.append(OrderField.CLIENT_IDENT_CLIENT)
        evaluation_type_to_check.append(OrderField.TRADER_NAME)
        evaluation_type_to_check.append(OrderField.PARTICIPANTS)

        assert set(evaluation_type_to_check).issubset(fields_to_check)

    def test_trader_name(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        """Check if trader name is filled."""
        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }
        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))
        strategy._run_algo(data=test_data)

        assert (
            strategy.scenarios[0]
            .json.get("additionalFields")
            .get("topLevel")
            .get("partiesTraderList")[0]
            is not None
        )

    def test_evaluation_by_client_one_hit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        """
        Description:
         - Filter 1;
         - 1 Hit;

        """
        number_of_hits = 1
        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath("time_cancel_filtered_data_client_one_hit.csv")
        )
        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.CLIENT
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_no_hits_spoof_time_1(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test2,
    ):
        """
        Description:
            - Spoof time to cancel is actually 2 seconds, so no hits should be detected;
            - Filter 1;
            - No hits;
        """
        number_of_hits = 0

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderTimeToCancel": 1,
            "spoofOrderPercentageLevel": 0.2,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_no_hits_spoof_time_1.csv")
        )

        time_cancel_filtered_data = pd.read_csv(
            TEST_DATA.joinpath(
                "time_cancel_filtered_data_evaluation_by_client_no_hits_spoof_time_1.csv"
            ),
            index_col=0,
        )

        assert time_cancel_filtered_data.empty

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_no_hits_spoof_time_2(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test3,
    ):
        """
        Description:
            - Real order is 50% filled, so this shouldn’t detect as the the threshold is set to 100%;
            - Filter 1;
            - No hits;

        """
        number_of_hits = 0
        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]
        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 1,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.2,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_no_hits_spoof_time_2.csv")
        )

        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath(
                "time_cancel_filtered_data_evaluation_by_client_no_hits_spoof_time_2.csv"
            )
        )

        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.CLIENT
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_trader(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test4,
    ):
        """
        Description:
            - Trader is different on the real vs. the spoof order, so no hits should be detected;
            - Filter 1;
            - No hits;

        """
        number_of_hits = 0
        expected_grouping_fields = [
            "__instrument_code__",
            "traderFileIdentifier",
            "executionDetails.buySellIndicator",
        ]
        thresholds = {
            "evaluationType": "trader",
            "includePartCancellations": False,
            "realOrderPercentageFilled": 0.5,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.2,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)
        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_trader.csv"))

        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath("time_cancel_filtered_data_evaluation_by_trader.csv")
        )

        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.TRADER
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_50_percentage_filled(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test5,
    ):
        """
        Description:
            - Both the spoof and real order are Buying, hence no hits should be detected
            as spoofing requires opposite buy/sell indicators;
            - Filter 1;
            - No Hits;

        """

        number_of_hits = 0

        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]

        thresholds = {
            "evaluationType": EvaluationTypeEnum.CLIENT,
            "includePartCancellations": False,
            "realOrderPercentageFilled": 0.5,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.2,
            "spoofingTimeWindow": 11,
        }

        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_50_percentage_filled.csv")
        )

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_50_percentage_filled_filter2(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test6,
    ):
        """
        Description:
            - Difference between spoof and real order is 10 seconds, so this should not detect a hit as the
            spoofing time window actual is 10 seconds;
            - Filter 2;
            - 1 hit;
        """
        number_of_hits = 1
        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]
        thresholds = {
            "evaluationType": EvaluationTypeEnum.CLIENT,
            "includePartCancellations": False,
            "realOrderPercentageFilled": 0.5,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.02,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_2)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_50_percentage_filled_filter2.csv")
        )
        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath(
                "time_cancel_filtered_evaluation_by_client_50_percentage_filled_filter2.csv"
            )
        )

        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.CLIENT
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_no_traded_quantity_spoof_child(
        self,
        helpers,
        mock_data,
        mock_spoof_data_no_traded_quantity,
    ):
        """
        Description:
            - Difference between spoof and real order is 10 seconds, so this should not detect a hit as the
            spoofing time window actual is 10 seconds;
            - Filter 2;
            - 1 hit;
        """
        number_of_hits = 0
        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]
        thresholds = {
            "evaluationType": EvaluationTypeEnum.CLIENT,
            "includePartCancellations": False,
            "realOrderPercentageFilled": 0.5,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.02,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_2)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_50_percentage_filled_filter2.csv")
        )
        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath(
                "time_cancel_filtered_evaluation_by_client_50_percentage_filled_filter2.csv"
            )
        )

        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.CLIENT
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_evaluation_by_client_no_timestamp_order_submitted_spoof_child(
        self,
        helpers,
        mock_data,
        mock_spoof_data_no_order_submitted,
    ):
        """
        Description:
            - Difference between spoof and real order is 10 seconds, so this should not detect a hit as the
            spoofing time window actual is 10 seconds;
            - Filter 2;
            - 1 hit;
        """
        number_of_hits = 0
        expected_grouping_fields = [
            "__instrument_code__",
            "clientFileIdentifier",
            "executionDetails.buySellIndicator",
        ]
        thresholds = {
            "evaluationType": EvaluationTypeEnum.CLIENT,
            "includePartCancellations": False,
            "realOrderPercentageFilled": 0.5,
            "spoofOrderTimeToCancel": 2,
            "spoofOrderPercentageLevel": 0.02,
            "spoofingTimeWindow": 10,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_2)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_50_percentage_filled_filter2.csv")
        )

        test_data = test_data.drop(columns=[OrderField.TS_ORD_SUBMITTED])
        time_cancel_filtered_data = mock_data(
            TEST_DATA.joinpath(
                "time_cancel_filtered_evaluation_by_client_50_percentage_filled_filter2.csv"
            )
        )

        assert len(time_cancel_filtered_data) == 1

        obtained_grouping_fields = [
            NewColumns.INSTRUMENT_CODE,
            strategy._th_evaluation_type,
            OrderField.EXC_DTL_BUY_SELL_IND,
        ]

        assert obtained_grouping_fields == expected_grouping_fields

        for field, group in time_cancel_filtered_data.groupby(expected_grouping_fields):
            assert strategy._th_evaluation_type == ORDER_TYPE_GROUP_MAP.get(
                EvaluationTypeEnum.CLIENT
            )

            groups = group.groupby(by=OrderField.META_KEY)

            assert groups.ngroups == 1

        strategy._run_algo(data=test_data)
        assert len(strategy.scenarios) == number_of_hits

    def test_no_ric_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        test_data[AlgoColumnsEnum.RIC] = pd.NA

        strategy.run_spoofing_algo(test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 2

    def test_no_order_book_depth_data_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        strategy._refinitiv_client.get_order_book_depth_data = MagicMock()
        strategy._refinitiv_client.get_order_book_depth_data.return_value = pd.DataFrame()

        strategy.run_spoofing_algo(test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 5

    def test_no_level_of_order_book_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        empty_df = pd.DataFrame()
        empty_df.loc[0, AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK] = pd.NA

        strategy._refinitiv_client.select_level_of_order_book = MagicMock()
        strategy._refinitiv_client.select_level_of_order_book.return_value = empty_df

        strategy.run_spoofing_algo(test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 6

    def test_no_real_orders_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        strategy.queries.fetch_real_orders = MagicMock()
        strategy.queries.fetch_real_orders.return_value = pd.NA

        strategy.run_spoofing_algo(test_data)

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 8

    def test_spoofing_time_window_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_test1,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.cross_product.query
        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query

        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results = MagicMock()
        market_abuse_algorithms.cross_product.query.CrossProductQuery.fetch_results.return_value = (
            pd.DataFrame()
        )
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        strategy._apply_spoofing_time_window = MagicMock()
        strategy._apply_spoofing_time_window.return_value = pd.NA

        strategy.run_spoofing_algo(test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 3

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 9

    def test_apply_time_to_cancel_filter_audit(self, helpers, mock_data):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        strategy.queries.get_spoof_execs = MagicMock()
        strategy.queries.get_spoof_execs.return_value = pd.DataFrame()

        strategy._run_algo(test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

    def test_fetch_real_orders_audit(self, helpers, mock_data):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = pd.DataFrame()

        test_data[AlgoColumnsEnum.REAL_ORDERS]: pd.DataFrame = test_data.apply(
            lambda x: strategy.queries.fetch_real_orders(
                instrument=x[OrderField.INST_ID_CODE],
                buy_sell_ind=x[OrderField.EXC_DTL_BUY_SELL_IND],
                spoof_evaluation=x[strategy._th_evaluation_type],
                order_id=x.get(OrderField.META_KEY, ""),
                related_instrument=pd.DataFrame(),
                method_id="test",
            ),
            axis=1,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

    def test_fetch_real_orders_audit_1(self, helpers, mock_data):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        test_output = pd.DataFrame()
        test_output.loc[0, "test"] = "test"

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = test_output

        test_data[AlgoColumnsEnum.REAL_ORDERS]: pd.DataFrame = test_data.apply(
            lambda x: strategy.queries.fetch_real_orders(
                instrument=x[OrderField.INST_ID_CODE],
                buy_sell_ind=x[OrderField.EXC_DTL_BUY_SELL_IND],
                spoof_evaluation=x[strategy._th_evaluation_type],
                order_id=x.get(OrderField.META_KEY, ""),
                related_instrument=pd.DataFrame(),
                method_id="test",
            ),
            axis=1,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

    def test_fetch_real_orders_audit_2(self, helpers, mock_data):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(TEST_DATA.joinpath("test_evaluation_by_client_one_hit.csv"))

        test_data["clientFileIdentifier"] = test_data[NewColumns.CLIENT]

        test_output = pd.DataFrame()
        test_output.loc[0, strategy._th_evaluation_type] = pd.NA

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = test_output

        test_data[AlgoColumnsEnum.REAL_ORDERS]: pd.DataFrame = test_data.apply(
            lambda x: strategy.queries.fetch_real_orders(
                instrument=x[OrderField.INST_ID_CODE],
                buy_sell_ind=x[OrderField.EXC_DTL_BUY_SELL_IND],
                spoof_evaluation=x[strategy._th_evaluation_type],
                order_id=x.get(OrderField.META_KEY, ""),
                related_instrument=pd.DataFrame(),
                method_id="test",
            ),
            axis=1,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

    def test_record_analysed(self, helpers, mock_data):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()
        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        market_abuse_audit_object.records_analysed = 0

        test_data = pd.DataFrame()
        test_data.loc[0, "test"] = "test"

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = test_data

        strategy._run_algo = MagicMock()
        strategy._run_algo.return_value = pd.DataFrame()

        strategy._apply_strategy()

        assert market_abuse_audit_object.records_analysed > 0

    def test_filter_relevant_timestamps_audit(
        self,
        helpers,
        mock_data,
        mock_spoof_data_no_order_submitted,
    ):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "evaluationType": "client",
            "includePartCancellations": True,
            "realOrderPercentageFilled": 0.96,
            "spoofOrderPercentageLevel": 0.05,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        test_data = mock_data(
            TEST_DATA.joinpath("test_evaluation_by_client_50_percentage_filled_filter2.csv")
        )

        strategy._run_algo(data=test_data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            audit_data = f.readlines()
        assert len(audit_data) == 1

    def test_apply_time_to_cancel_filter(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "trader",
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.01,
            "spoofOrderTimeToCancel": 600,
            "spoofingTimeWindow": 900,
            "includePartCancellations": True,
            "priceImprovement": True,
        }
        filters = {}

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("test_apply_time_to_cancel_filter.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_UPDATED, OrderField.TS_ORD_SUBMITTED],
        )
        strategy.queries.get_spoof_execs = MagicMock()
        strategy.queries.get_spoof_execs.return_value = pd.read_csv(
            TEST_DATA.joinpath("spoofing_child.csv"),
            index_col=0,
            parse_dates=[OrderField.TS_ORD_UPDATED, OrderField.TS_TRADING_DATE_TIME],
        )

        result = strategy.apply_time_to_cancel_filter(data)

        spoofing_data_timestamp = pd.read_csv(
            TEST_DATA.joinpath("spoofing_data_timestamp.csv"),
            index_col=0,
            parse_dates=[
                OrderField.TS_ORD_UPDATED,
                OrderField.TS_ORD_SUBMITTED,
                AlgoColumnsEnum.ORDER_ENTRY_TIME,
                AlgoColumnsEnum.ORDER_CANCEL_TIME,
                AlgoColumnsEnum.ORDER_TIME_TO_CANCEL,
            ],
        )
        spoofing_data_timestamp[AlgoColumnsEnum.ORDER_TIME_TO_CANCEL] = pd.to_timedelta(
            spoofing_data_timestamp[AlgoColumnsEnum.ORDER_TIME_TO_CANCEL]
        )
        spoofing_data_timestamp = spoofing_data_timestamp.drop("spoofOrderStates", axis=1)
        result = result.drop("spoofOrderStates", axis=1)
        assert result.equals(spoofing_data_timestamp)

    def test_case_17_cross_product(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "trader",
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.5,
            "spoofOrderTimeToCancel": 10,
            "spoofingTimeWindow": 900,
            "includePartCancellations": False,
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.SpoofingV2.17.3.csv"
                        ]
                    }
                }
            }
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        context.commit_audit = False

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [
            pd.read_json(
                TEST_DATA_CROSS_PRODUCT.joinpath("tc17_cases_to_analyse.json"),
            )
        ]
        strategy.queries.get_spoof_execs = MagicMock()
        strategy.queries.get_spoof_execs.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc17_spoofing_child.json"),
        )
        strategy.step_6_get_market_data = MagicMock()
        strategy.step_6_get_market_data.return_value = pd.read_csv(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc17_obd_data.csv"),
        )
        strategy.cross_product_logic = MagicMock()
        strategy.cross_product_logic.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc17_related_instruments.json"),
        )
        strategy.queries.fetch_real_orders = MagicMock()
        strategy.queries.fetch_real_orders.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc17_real_orders.json"),
        )
        strategy.cross_product.related_activity = MagicMock()
        strategy.cross_product.related_activity.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc17_related_activity.json"),
        )

        strategy.queries.get_newo_child = MagicMock()
        strategy.queries.get_newo_child.return_value = [
            "OrderState:SpoofingV2-CP-2:1:SPOOFINGV2CP2BUYI:CAME:2023-11-24T10:01:10Z:5700.0:1706101312696",
            "OrderState:SpoofingV2-CP-3:2:SPOOFINGV2CP3SPOOFINGV2CP3120231124SELL:FILL:2023-11-24T10:01:09Z:0.0:1706101312696",
            "OrderState:SpoofingV2-CP-5:1:SPOOFINGV2CP5SPOOFINGV2CP5120231124BUYI:FILL:2023-11-24T10:11:10Z:0.0:1706101312696",
            "OrderState:SpoofingV2-CP|3:2:SPOOFINGV2CP3SPOOFINGV2CP3120231124SELL:FILL:2023-11-24T10:01:09Z:0.0:1706092968785",
        ]
        strategy.run()

        scenarios = strategy.scenarios
        assert len(scenarios) == 1

    def test_case_18_cross_product(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.1,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "includePartCancellations": True,
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.18.csv"
                        ]
                    }
                }
            }
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        context.commit_audit = False

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [
            pd.read_json(
                TEST_DATA_CROSS_PRODUCT.joinpath("tc18_cases_to_analyse.json"),
            )
        ]
        strategy.queries.get_spoof_execs = MagicMock()
        strategy.queries.get_spoof_execs.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_spoofing_child.json"),
        )
        strategy._refinitiv_client.get_order_book_depth_data = MagicMock()
        strategy._refinitiv_client.get_order_book_depth_data.return_value = pd.read_csv(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_obd_data.csv"),
        )
        strategy.cross_product_logic = MagicMock()
        strategy.cross_product_logic.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_related_instruments.json"),
        )
        strategy.queries.fetch_real_orders = MagicMock()
        strategy.queries.fetch_real_orders.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_real_orders.json"),
        )
        strategy.cross_product.related_activity = MagicMock()
        strategy.cross_product.related_activity.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_related_activity.json"),
        )

        strategy.queries.get_newo_child = MagicMock()
        strategy.queries.get_newo_child.return_value = [
            "OrderState:ORD_ULVR.L_025662296:1:ORDULVRL025662296BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1701073983775",
            "OrderState:layeringAndBookBalance_15A_3:2:LAYERINGANDBOOKBALANCE15A3LAYERINGANDBOOKBALANCE15A3:FILL:2021-08-24T13:00:05.696336Z:0.0:1689758744019",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011120210824SELL:PARF:2021-08-24T13:00:05.696336Z:400.0:1701073983695",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011220210824SELL:PARF:2021-08-24T13:00:06.696336Z:300.0:1701073983695",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011320210824SELL:PARF:2021-08-24T13:00:07.696336Z:250.0:1701073983695",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21011SELL:PARF:400.0:1689759951048",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21012SELL:PARF:300.0:1689759951048",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21013SELL:PARF:250.0:1689759951048",
            "OrderState:spoofingV2_10_2:1:SPOOFINGV2102BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689759951048",
            "OrderState:spoofingV2_14_1:2:SPOOFINGV2141SPOOFINGV2141SELL:FILL:0.0:1689762007243",
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689762007243",
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0:1689762007243",
            "OrderState:spoofingV2_18_1:1:SPOOFINGV2181BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1705925118102",
            "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689764578048",
            "OrderState:spoofingV2_6_1:2:SPOOFINGV261SELL:CAME:2021-08-24T13:00:05.696336Z:500.0:1689761987515",
            "OrderState:spoofingV2_6_2:1:SPOOFINGV262BUYI:FILL:0.0:1689761987515",
            "OrderState:spoofingV2_7_2:1:SPOOFINGV272BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689767205368",
            "OrderState:spoofingV2_8_1:2:SPOOFINGV281SPOOFINGV281SELL:FILL:0.0:1689767218864",
            "OrderState:spoofingV2_8_2:1:SPOOFINGV282BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689767218864",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2911SELL:PARF:400.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2912SELL:PARF:300.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2913SELL:PARF:200.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2914SELL:PARF:100.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2915SELL:FILL:0.0:1689760954144",
            "OrderState:spoofingV2_9_2:1:SPOOFINGV292BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689760954144",
        ]

        strategy.run()

        scenarios = strategy.scenarios
        assert len(scenarios) == 1

    def test_cross_product_logic(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.1,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "includePartCancellations": True,
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.18.csv.csv"
                        ]
                    }
                }
            }
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.cross_product.query.get_filter_orders = MagicMock()
        strategy.cross_product.query.get_filter_orders.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("cross_product_logic_original_orders.json"),
        )
        strategy.cross_product.run_checks_alert_detection = MagicMock()
        strategy.cross_product.run_checks_alert_detection.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("cross_product_logic_related_instruments.json"),
        )
        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("cross_product_logic_related_orders.json"),
        )

        data = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("data_cross_product_logic.json"),
        )

        related = strategy.cross_product_logic(data=data)
        type_counts = related[CrossProductColumns.RELATED_TYPE].value_counts().to_dict()

        assert related.shape == (46, 49)
        assert type_counts[RelatedType.EXPLICITLY_RELATED] == 44
        assert type_counts[RelatedType.EXPLICITLY_CORRELATED] == 2
        assert len(related[CrossProductColumns.ORIGINAL_RECORD_METAKEY].unique()) == 1
        assert (
            related[CrossProductColumns.ORIGINAL_RECORD_METAKEY].unique()[0]
            == data[OrderField.META_KEY].values[0]
        )

    def test_cross_product_fetch_real_orders(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.1,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "includePartCancellations": True,
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.18.csv.csv"
                        ]
                    }
                }
            }
        }

        import market_abuse_algorithms.strategy.abstract_spoofing_v2.query
        import market_abuse_algorithms.strategy.spoofing_v2.strategy

        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.abstract_spoofing_v2.query.get_market_client.return_value = MockMarketDataClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("data_cross_product_logic.json"),
        )
        related_instruments = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_related_instruments.json"),
        )

        strategy.queries.sdp_repository.search_after_query = MagicMock()
        strategy.queries.sdp_repository.search_after_query.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("test_fetch_real_orders_data.json"),
        )
        strategy.queries.get_real_execs = MagicMock()
        strategy.queries.get_real_execs.return_value = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("test_fetch_real_orders_executions.json"),
        )

        real_order = strategy.queries.fetch_real_orders(
            instrument=data.loc[0, OrderField.INST_ID_CODE],
            buy_sell_ind=data.loc[0, OrderField.EXC_DTL_BUY_SELL_IND],
            spoof_evaluation=data.loc[0, strategy._th_evaluation_type],
            order_id=data.loc[0, :].get(OrderField.META_KEY, ""),
            related_instrument=related_instruments,
            method_id="1",
        )
        related_counts = real_order[CrossProductColumns.RELATED_INSTRUMENT].value_counts().to_dict()

        assert real_order.shape == (24, 51)
        assert related_counts[False] == 1

    def test_scenario_wo_related_activity(self, helpers, mock_data):
        thresholds = {
            "evaluationType": "executingEntity",
            "realOrderPercentageFilled": 0,
            "spoofOrderPercentageLevel": 0.1,
            "spoofOrderTimeToCancel": 6,
            "spoofingTimeWindow": 10,
            "includePartCancellations": True,
            "priceImprovement": False,
        }
        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.spoofingV2.18.csv"
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        context.commit_audit = False

        strategy.cross_product.related_activity = MagicMock()
        strategy.cross_product.related_activity.return_value = pd.DataFrame()

        strategy.queries.get_newo_child = MagicMock()
        strategy.queries.get_newo_child.return_value = [
            "OrderState:ORD_ULVR.L_025662296:1:ORDULVRL025662296BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1701073983775",
            "OrderState:layeringAndBookBalance_15A_3:2:LAYERINGANDBOOKBALANCE15A3LAYERINGANDBOOKBALANCE15A3:FILL:2021-08-24T13:00:05.696336Z:0.0:1689758744019",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011120210824SELL:PARF:2021-08-24T13:00:05.696336Z:400.0:1701073983695",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011220210824SELL:PARF:2021-08-24T13:00:06.696336Z:300.0:1701073983695",
            "OrderState:spoofingV2_10.1_1:2:SPOOFINGV21011SPOOFINGV21011320210824SELL:PARF:2021-08-24T13:00:07.696336Z:250.0:1701073983695",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21011SELL:PARF:400.0:1689759951048",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21012SELL:PARF:300.0:1689759951048",
            "OrderState:spoofingV2_10_1:2:SPOOFINGV2101SPOOFINGV21013SELL:PARF:250.0:1689759951048",
            "OrderState:spoofingV2_10_2:1:SPOOFINGV2102BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689759951048",
            "OrderState:spoofingV2_14_1:2:SPOOFINGV2141SPOOFINGV2141SELL:FILL:0.0:1689762007243",
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689762007243",
            "OrderState:spoofingV2_14_2:1:SPOOFINGV2142SPOOFINGV214220210824BUYI:PARF:2021-08-24T13:00:01.696336Z:499.0:1689762007243",
            "OrderState:spoofingV2_18_1:1:SPOOFINGV2181BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1705925118102",
            "OrderState:spoofingV2_5_1:1:SPOOFINGV251BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689764578048",
            "OrderState:spoofingV2_6_1:2:SPOOFINGV261SELL:CAME:2021-08-24T13:00:05.696336Z:500.0:1689761987515",
            "OrderState:spoofingV2_6_2:1:SPOOFINGV262BUYI:FILL:0.0:1689761987515",
            "OrderState:spoofingV2_7_2:1:SPOOFINGV272BUYI:CAME:2021-08-24T13:00:05.696336Z:0.0:1689767205368",
            "OrderState:spoofingV2_8_1:2:SPOOFINGV281SPOOFINGV281SELL:FILL:0.0:1689767218864",
            "OrderState:spoofingV2_8_2:1:SPOOFINGV282BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689767218864",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2911SELL:PARF:400.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2912SELL:PARF:300.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2913SELL:PARF:200.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2914SELL:PARF:100.0:1689760954144",
            "OrderState:spoofingV2_9_1:2:SPOOFINGV291SPOOFINGV2915SELL:FILL:0.0:1689760954144",
            "OrderState:spoofingV2_9_2:1:SPOOFINGV292BUYI:CAME:2021-08-24T13:00:05.696336Z:500.0:1689760954144",
        ]

        alert_df = pd.read_json(
            TEST_DATA_CROSS_PRODUCT.joinpath("tc18_alerts_df.json"),
        )
        # this is ugly, but that's how the algo puts the realOrders
        alert_df["realOrders"] = alert_df["realOrders"].apply(
            lambda x: pd.DataFrame(alert_df["realOrders"][0])
        )

        strategy._create_alert(alert_df)

        scenarios = strategy.scenarios

        assert len(scenarios) == 1

        scenario = scenarios[0].json

        # Assert the relatedActivityAlerted is flagged as True
        assert scenario["additionalFields"]["topLevel"]["relatedActivityAlerted"]

        # relatedActivityDetected should be False as there was no related activity
        assert scenario["additionalFields"]["topLevel"]["relatedActivityDetected"] is False

        # Assert the number of relations is 23
        assert len(scenario["additionalFields"]["topLevel"]["relatedRecords"]) == 23

        related_records = scenario["additionalFields"]["topLevel"]["relatedRecords"]

        id_list = []
        correlation_scores = []
        for item in related_records:
            id_list.append(item.get("&id"))
            correlation_scores.append(item.get("correlationScore"))

        assert len(set(id_list)) == 23
        assert all(pd.isnull(correlation_scores))
