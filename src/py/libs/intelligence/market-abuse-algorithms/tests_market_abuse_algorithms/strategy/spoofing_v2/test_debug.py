import pytest
from market_abuse_algorithms.strategy.spoofing_v2.strategy import Strategy


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerSpoofingV2:
    def test_case_debug(self, helpers):
        thresholds = {
            "evaluationType": "trader",
            "realOrderPercentageFilled": 1,
            "spoofOrderPercentageLevel": 0.5,
            "spoofOrderTimeToCancel": 10,
            "spoofingTimeWindow": 900,
            "includePartCancellations": False,
            "priceImprovement": False,
        }

        filters = {
            "bool": {
                "should": [
                    {
                        "bool": {
                            "filter": [
                                {"term": {"executionDetails.orderStatus": "NEWO"}},
                                {
                                    "range": {
                                        "timestamps.orderSubmitted": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    {
                        "bool": {
                            "filter": [
                                {
                                    "range": {
                                        "timestamps.tradingDateTime": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                }
                            ],
                            "must_not": [{"term": {"executionDetails.orderStatus": "NEWO"}}],
                        }
                    },
                ],
                "minimum_should_match": 1,
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 1
        # for i in range(len(scenarios)):
        #     dic = scenarios[i].json
        #     final_sc = {
        #         "records": dic.get("records"),
        #         "additionalFields": dic.get("additionalFields"),
        #     }
        #
        #     with open(f"scenario_{i}.json", "w") as fp:
        #         json.dump(final_sc, fp)
