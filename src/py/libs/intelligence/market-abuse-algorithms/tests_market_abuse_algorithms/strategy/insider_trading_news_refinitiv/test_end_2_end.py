# ruff: noqa: E501
import pandas as pd
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.strategy import (
    Strategy,
)
from pathlib import Path
from se_elastic_schema.static.mar_audit import DropReason, StepType
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data/E2E")


class MarketDataStore:
    pass


class ApiClient:
    pass


class OBDMarketDataAPI:
    def rolling_eod_stats(self, ric):
        pass


class FakeMarketClient(MarketDataClient):
    def _get_api_client(self, *args, **kwargs):
        return ApiClient()

    def _initialize_market_data_store(self):
        return MarketDataStore()

    def _get_obd_client(self):
        return OBDMarketDataAPI()

    def get_ric_map(
        self,
        list_inst_unique_id=None,
        instrument_combinations=None,
        use_prefix_query=None,
    ):
        if instrument_combinations == [["CNE000000J36HKDXSHE"]]:
            return {"CNE000000J36HKDXSHE": "200581.SZ"}

        if instrument_combinations == [["US30303M1027USDARCX"]]:
            return {"US30303M1027USDARCX": "META.OQ"}


class FakeRefinitivNewsClient:
    def __init__(self, relevance):
        self._relevance = relevance
        self._refinitiv_sources = self.get_refinitiv_sources()

    def get_refinitiv_sources(self):
        return ["Refinitiv"]

    def get_refinitiv_news(
        self,
        ric=None,
        start_date=None,
        end_date=None,
        all_sources=None,
        method_id=None,
        all_subjects=None,
        orders_for_ric=None,
        sources=None,
        subjects=None,
    ):
        if ric == "200581.SZ":
            return pd.DataFrame()
        if ric == "META.OQ":
            return pd.read_csv(
                TEST_DATA.joinpath("test_case_9_1_no_sentiment_fetch_refinitiv_news.csv"),
                index_col=0,
                sep=",",
                parse_dates=[
                    "createdDateTime",
                    "updatedDateTime",
                ],
                date_parser=custom_date_parser(),
            )

        if ric == "META.OQ" and start_date == pd.Timestamp("2023-10-19 11:34:30"):
            return pd.read_csv(
                TEST_DATA.joinpath(
                    "test_no_fallback_order_volume_field_to_quantity_refinitiv_news.csv"
                ),
                index_col=0,
                sep=",",
                parse_dates=[
                    "createdDateTime",
                    "updatedDateTime",
                ],
                date_parser=custom_date_parser(),
            )


def custom_date_parser():
    return lambda x: pd.to_datetime(x)


class TestInsiderTradingWithNewsRefinitiv:
    def test_fallback_transaction_volume_column_empty_because_news_source(
        self, helpers, monkeypatch
    ):
        import market_abuse_algorithms.data_source.repository.sdp.es_sdp
        import market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query

        thresholds = {
            "newsSources": ["Reuters News"],
            "relevance": "high",
            "sentiment": 0.49,
            "beforeTradeTimestamp": {"unit": "days", "value": 0},
            "afterTradeTimestamp": {"unit": "days", "value": 6},
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.InsiderTrading.Sentiment.2.csv"
                            ]
                        }
                    }
                ]
            }
        }

        def fake_inspect_required_fields():
            pass

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client.return_value = FakeMarketClient()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client.return_value = FakeRefinitivNewsClient(
            relevance="high"
        )

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields.return_value = fake_inspect_required_fields()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations.return_value = [
            ["CNE000000J36HKDXSHE"]
        ]

        def custom_side_effect(*args, **kwargs):
            # Encapsulate the return values in this method
            values = [
                pd.DataFrame(),
                pd.read_csv(
                    TEST_DATA.joinpath("test_case_fallback_transaction_volume.csv"),
                    index_col=0,
                    sep=",",
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                ),
            ]

            def inner(*args, **kwargs):
                return values.pop(0)  # Pop the first value each time the method is called

            return inner

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.sdp.es_sdp.SDP,
            "search_after_query",
            custom_side_effect(),
        )

        strategy_context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=strategy_context)
        strategy.run_local = True
        strategy.run()

        scenarios = strategy.scenarios
        assert len(scenarios) == 0

        agg_step_audit_data = pd.read_json(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            lines=True,
        )

        assert len(agg_step_audit_data) == 1
        assert agg_step_audit_data["number_of_dropped_orders"].sum() == 3
        assert (
            agg_step_audit_data[agg_step_audit_data["step_name"] == "Get Refinitiv News Data."][
                "number_of_input_orders"
            ].sum()
            == agg_step_audit_data["number_of_dropped_orders"].sum()
        )
        assert list(filter(None, agg_step_audit_data["drop_reason"].unique())) == [
            DropReason.RECORDS_DROPPED_REFINITIV_NEWS
        ]
        assert list(filter(None, agg_step_audit_data["step_type"].unique())) == [
            StepType.REFINITIV_NEWS
        ]
        singleton_audit_object.delete_local_audit_files()

    def test_no_fallback_order_volume_field_to_quantity(self, helpers, monkeypatch):
        import market_abuse_algorithms.data_source.repository.sdp.es_sdp
        import market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query

        thresholds = {
            "newsSources": ["Reuters News"],
            "relevance": "high",
            "sentiment": 0.49,
            "beforeTradeTimestamp": {"unit": "days", "value": 0},
            "afterTradeTimestamp": {"unit": "days", "value": 6},
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.InsiderTrading.Sentiment.2.csv"
                            ]
                        }
                    }
                ]
            }
        }

        def fake_inspect_required_fields():
            pass

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client.return_value = FakeMarketClient()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client.return_value = FakeRefinitivNewsClient(
            relevance="high"
        )

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields.return_value = fake_inspect_required_fields()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations.return_value = [
            ["US30303M1027USDARCX"]
        ]

        def custom_side_effect(*args, **kwargs):
            # Encapsulate the return values in this method
            values = [
                pd.DataFrame(),
                pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_no_fallback_order_volume_field_to_quantity_search_after_query.csv"
                    ),
                    index_col=0,
                    sep=",",
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                ),
                pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_no_fallback_order_volume_field_to_quantity_get_net_scenario.csv"
                    ),
                    index_col=0,
                    sep=",",
                ),
            ]

            def inner(*args, **kwargs):
                return values.pop(0)  # Pop the first value each time the method is called

            return inner

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.sdp.es_sdp.SDP,
            "search_after_query",
            custom_side_effect(),
        )

        strategy_context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=strategy_context)
        strategy.run_local = True
        strategy.run()

        obtained_scenarios = strategy.scenarios
        assert len(obtained_scenarios) == 4

        expected_scenarios = [
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_4:2:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV7gkY1n",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.120107,
                        "sentimentNegative": 0.032598,
                        "sentimentPositive": 0.847295,
                        "storyCreated": "2023-10-24T20:01:06.724Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/RTV7gkY1n/2/",
                        "headline": "Refinitiv Newscasts - US Day Ahead: US stocks advance: Meta, Boeing head Wednesday's earnings diary",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 2500.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-24T20:01:06.724Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV95P7bG",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.183429,
                        "sentimentNegative": 0.295483,
                        "sentimentPositive": 0.521088,
                        "storyCreated": "2023-10-23T21:07:18.824Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/23/RTV95P7bG/5/",
                        "headline": "Refinitiv Newscasts - Bonds and Big Tech: why it's a big week for both",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 3500.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-23T21:07:18.824Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:ITV3_REFINITIV_1:1:NEWO:1700217683461"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BP27V",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.15326,
                        "sentimentNegative": 0.0921122,
                        "sentimentPositive": 0.754628,
                        "storyCreated": "2023-10-19T16:18:28.000Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/19/nL1N3BP27V/3/",
                        "headline": "CORRECTED-EssilorLuxottica Q3 sales up",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 500.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-19T16:18:28.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_4:2:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV5AU",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.165206,
                        "sentimentNegative": 0.141279,
                        "sentimentPositive": 0.693515,
                        "storyCreated": "2023-10-25T20:21:15.000Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV5AU/9/",
                        "headline": "UPDATE 5-Facebook-parent Meta beats revenue estimates on digital ad strength",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 2000.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T20:21:15.000Z",
                    }
                },
            },
        ]
        same_scenario = 0

        for obtained_scenario in obtained_scenarios:
            obtained_scenario_records = obtained_scenario.json.get("records")
            obtained_scenario_additional_fields = obtained_scenario.json.get(
                "additionalFields"
            ).get("topLevel")

            for expected_scenario in expected_scenarios:
                expected_scenario_additional_fields = expected_scenario.get("additionalFields").get(
                    "topLevel"
                )
                expected_scenario_records = expected_scenario.get("records")

                if validate_scenarios(
                    obtained_fields=obtained_scenario_additional_fields,
                    expected_fields=expected_scenario_additional_fields,
                ) and validate_scenarios(
                    obtained_fields=expected_scenario_records,
                    expected_fields=obtained_scenario_records,
                ):
                    same_scenario += 1

        assert same_scenario == len(obtained_scenarios)

    def test_fallback_order_volume_field_to_quantity(self, helpers, monkeypatch):
        import market_abuse_algorithms.data_source.repository.sdp.es_sdp
        import market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query

        thresholds = {
            "newsSources": ["Reuters News"],
            "relevance": "high",
            "sentiment": 0.49,
            "beforeTradeTimestamp": {"unit": "days", "value": 0},
            "afterTradeTimestamp": {"unit": "days", "value": 6},
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.InsiderTrading.Sentiment.2.csv"
                            ]
                        }
                    }
                ]
            }
        }

        def fake_inspect_required_fields():
            pass

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client.return_value = FakeMarketClient()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client.return_value = FakeRefinitivNewsClient(
            relevance="high"
        )

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields.return_value = fake_inspect_required_fields()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations.return_value = [
            ["US30303M1027USDARCX"]
        ]

        def custom_side_effect(*args, **kwargs):
            # Encapsulate the return values in this method
            values = [
                pd.DataFrame(),
                pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_fallback_order_volume_field_to_quantity_search_after_query_no_best_ex.csv"
                    ),
                    index_col=0,
                    sep=",",
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                ),
                pd.read_csv(
                    TEST_DATA.joinpath(
                        "test_fallback_order_volume_field_to_quantity_get_net_scenario.csv"
                    ),
                    index_col=0,
                    sep=",",
                ),
            ]

            def inner(*args, **kwargs):
                return values.pop(0)  # Pop the first value each time the method is called

            return inner

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.sdp.es_sdp.SDP,
            "search_after_query",
            custom_side_effect(),
        )

        strategy_context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=strategy_context)
        strategy.run_local = True
        strategy.run()

        obtained_scenarios = strategy.scenarios
        assert len(obtained_scenarios) == 4

        expected_scenarios = [
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_4:2:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV7gkY1n",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.120107,
                        "sentimentNegative": 0.032598,
                        "sentimentPositive": 0.847295,
                        "storyCreated": "2023-10-24T20:01:06.724Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/RTV7gkY1n/2/",
                        "headline": "Refinitiv Newscasts - US Day Ahead: US stocks advance: Meta, Boeing head Wednesday's earnings diary",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 2500.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-24T20:01:06.724Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV95P7bG",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.183429,
                        "sentimentNegative": 0.295483,
                        "sentimentPositive": 0.521088,
                        "storyCreated": "2023-10-23T21:07:18.824Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/23/RTV95P7bG/5/",
                        "headline": "Refinitiv Newscasts - Bonds and Big Tech: why it's a big week for both",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 3500.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-23T21:07:18.824Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:ITV3_REFINITIV_1:1:NEWO:1700217683461"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BP27V",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.15326,
                        "sentimentNegative": 0.0921122,
                        "sentimentPositive": 0.754628,
                        "storyCreated": "2023-10-19T16:18:28.000Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/19/nL1N3BP27V/3/",
                        "headline": "CORRECTED-EssilorLuxottica Q3 sales up",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITV3_REFINITIV_1:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 500.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 500.0,
                        "netScenarioPosition": 500.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-19T16:18:28.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 6},
                    "beforeTradeTimestamp": {"unit": "days", "value": 0},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": 0.49,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_2:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_3:1:NEWO:1700217683461",
                        "Order:ITV3_REFINITIV_4:2:NEWO:1700217683461",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV5AU",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.165206,
                        "sentimentNegative": 0.141279,
                        "sentimentPositive": 0.693515,
                        "storyCreated": "2023-10-25T20:21:15.000Z",
                        "involvedParties": ["Client 1", "Counterparty 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV5AU/9/",
                        "headline": "UPDATE 5-Facebook-parent Meta beats revenue estimates on digital ad strength",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:ITv3_REFINITIV_2:1:NEWO:1700217683461",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 2000.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T20:21:15.000Z",
                    }
                },
            },
        ]
        same_scenario = 0

        for obtained_scenario in obtained_scenarios:
            obtained_scenario_records = obtained_scenario.json.get("records")
            obtained_scenario_additional_fields = obtained_scenario.json.get(
                "additionalFields"
            ).get("topLevel")

            for expected_scenario in expected_scenarios:
                expected_scenario_additional_fields = expected_scenario.get("additionalFields").get(
                    "topLevel"
                )
                expected_scenario_records = expected_scenario.get("records")

                if validate_scenarios(
                    obtained_fields=obtained_scenario_additional_fields,
                    expected_fields=expected_scenario_additional_fields,
                ) and validate_scenarios(
                    obtained_fields=expected_scenario_records,
                    expected_fields=obtained_scenario_records,
                ):
                    same_scenario += 1

        assert same_scenario == len(obtained_scenarios)

    def test_case_9_1_no_sentiment(self, helpers, monkeypatch):
        """Retrieval of Refinitiv news.

        There may be more alerts than expected because the file used
        initially is only a sample of news data. Should get 27 alerts to
        refinitiv news.
        """
        import market_abuse_algorithms.data_source.repository.sdp.es_sdp
        import market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query

        thresholds = {
            "newsSources": [
                "Reuters News",
            ],
            "relevance": "high",
            "beforeTradeTimestamp": {"unit": "days", "value": 1},
            "afterTradeTimestamp": {"unit": "days", "value": 3},
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.insiderTrading.withNews.Refinitiv.csv"
                            ]
                        }
                    }
                ]
            }
        }

        def fake_inspect_required_fields():
            pass

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client.return_value = FakeMarketClient()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client.return_value = FakeRefinitivNewsClient(
            relevance="high"
        )

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields.return_value = fake_inspect_required_fields()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations.return_value = [
            ["US30303M1027USDARCX"]
        ]

        def custom_side_effect(*args, **kwargs):
            # Encapsulate the return values in this method
            values = [
                pd.DataFrame(),
                pd.read_csv(
                    TEST_DATA.joinpath("test_case_9_1_no_sentiment_orders.csv"),
                    index_col=0,
                    sep=",",
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                ),
                pd.read_csv(
                    TEST_DATA.joinpath("test_case_9_1_no_sentiment_get_net_scenario.csv"),
                    index_col=0,
                    sep=",",
                ),
            ]

            def inner(*args, **kwargs):
                return values.pop(0)  # Pop the first value each time the method is called

            return inner

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.sdp.es_sdp.SDP,
            "search_after_query",
            custom_side_effect(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        strategy.run_local = True

        strategy.run()

        obtained_scenarios = strategy.scenarios

        assert len(obtained_scenarios) == 27

        expected_scenarios = [
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "ACSW5wWda",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-20T20:30:42.584Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Accesswire', 'Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/20/ACSW5wWda/1/",
                        "headline": "Lado Okhotnikov and Meta Force Are Releasing a New Program. Tactile Is Ready to Unlock Cashback for Metaverse Users",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-20T20:30:42.584Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "Plx5FwNDD",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.585624,
                        "sentimentNegative": 0.241478,
                        "sentimentPositive": 0.172898,
                        "storyCreated": "2023-10-25T20:17:49.611Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/Plx5FwNDD/1/",
                        "headline": "BRIEF-Meta posts Q3 EPS USD 4.39",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T20:17:49.611Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV1KHdWS",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.125383,
                        "sentimentNegative": 0.818881,
                        "sentimentPositive": 0.0557361,
                        "storyCreated": "2023-10-25T08:49:32.219Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/RTV1KHdWS/6/",
                        "headline": "Refinitiv Newscasts - US states sue Meta, Instagram over kids' mental health",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T08:49:32.219Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV2stspC",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.125593,
                        "sentimentNegative": 0.818524,
                        "sentimentPositive": 0.055883,
                        "storyCreated": "2023-10-20T21:21:10.908Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/20/RTV2stspC/6/",
                        "headline": "Refinitiv Newscasts - Business Lookahead: Another curve ball for markets",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-20T21:21:10.908Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV3Mj4gN",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.116713,
                        "sentimentNegative": 0.677212,
                        "sentimentPositive": 0.206075,
                        "storyCreated": "2023-10-25T16:02:06.148Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/RTV3Mj4gN/2/",
                        "headline": "Refinitiv Newscasts - Trading at Noon:  U.S. Treasuries take another hit",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T16:02:06.148Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV5Tdjgx",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.113695,
                        "sentimentNegative": 0.710686,
                        "sentimentPositive": 0.17562,
                        "storyCreated": "2023-10-25T08:51:08.957Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/RTV5Tdjgx/2/",
                        "headline": "Refinitiv Newscasts - US states sue Meta, Instagram over kids' mental health",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T08:51:08.957Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV5z6vr",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.392435,
                        "sentimentNegative": 0.147374,
                        "sentimentPositive": 0.460192,
                        "storyCreated": "2023-10-24T16:02:06.585Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/RTV5z6vr/3/",
                        "headline": "Refinitiv Newscasts - Trading at Noon: PMI points up for U.S. manufacturing, earnings in focus",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 3,
                        "timestamp": "2023-10-24T16:02:06.585Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV7V1K1w",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.120331,
                        "sentimentNegative": 0.764598,
                        "sentimentPositive": 0.115071,
                        "storyCreated": "2023-10-20T20:02:25.927Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/20/RTV7V1K1w/7/",
                        "headline": "Refinitiv Newscasts - US Week Ahead: Wall St slides on yields, ME worries; big tech earnings in view",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-20T20:02:25.927Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV7gkY1n",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.120107,
                        "sentimentNegative": 0.032598,
                        "sentimentPositive": 0.847295,
                        "storyCreated": "2023-10-24T20:01:06.724Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/RTV7gkY1n/2/",
                        "headline": "Refinitiv Newscasts - US Day Ahead: US stocks advance: Meta, Boeing head Wednesday's earnings diary",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-24T20:01:06.724Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "RTV95P7bG",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.183429,
                        "sentimentNegative": 0.295483,
                        "sentimentPositive": 0.521088,
                        "storyCreated": "2023-10-23T21:07:18.824Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/23/RTV95P7bG/5/",
                        "headline": "Refinitiv Newscasts - Bonds and Big Tech: why it's a big week for both",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 2,
                        "timestamp": "2023-10-23T21:07:18.824Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BQ1I7",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.722003,
                        "sentimentNegative": 0.118422,
                        "sentimentPositive": 0.159576,
                        "storyCreated": "2023-10-20T14:44:27.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/20/nFWN3BQ1I7/2/",
                        "headline": "US Republican senators ask tech firms about content moderation in Israel-Hamas war",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-20T14:44:27.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BT0UI",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-23T11:04:04.166Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/23/nFWN3BT0UI/1/",
                        "headline": "META PLATFORMS INC <META.O>: STIFEL RAISES TARGET PRICE TO $402 FROM $385",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 2000.0,
                        "numberOfOrders": 2,
                        "timestamp": "2023-10-23T11:04:04.166Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BU0YZ",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-24T10:26:25.405Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/nFWN3BU0YZ/1/",
                        "headline": "META PLATFORMS INC  <META.O>: SEAPORT RESEARCH PARTNERS INITIATES WITH BUY RATING, TARGET PRICE $365",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 2,
                        "timestamp": "2023-10-24T10:26:25.405Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BU28R",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-24T17:07:04.446Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/24/nFWN3BU28R/2/",
                        "headline": "META PLATFORMS - TO HELP MANAGE TEENS’ USE OF META QUEST, WILL SOON BE ABLE TO SET DAILY TIME LIMITS AND SCHEDULED BREAKS",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-24T17:07:04.446Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BV3G4",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-25T21:08:00.438Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nFWN3BV3G4/19/",
                        "headline": "CFO SAYS META DOES NOT HAVE MATERIAL DIRECT REVENUE EXPOSURE TO ISRAEL, MIDDLE EAST, BUT OBSERVED SOFTER AD SPEND IN BEGINNING OF Q4",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T21:08:00.438Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nFWN3BW41D",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-26T14:27:56.452Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/26/nFWN3BW41D/1/",
                        "headline": "META PLATFORMS INC  <META.O>: CITOGROUP RAISES TARGET PRICE TO $425 FROM $385",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "SELL",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": -1000.0,
                        "netScenarioPosition": -2000.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-26T14:27:56.452Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BN3DJ",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.168139,
                        "sentimentNegative": 0.37355,
                        "sentimentPositive": 0.458311,
                        "storyCreated": "2023-10-19T14:19:16.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/19/nL1N3BN3DJ/8/",
                        "headline": "ANALYSIS-Wall Street's 'Magnificent Seven' face moment of truth as earnings season arrives",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-19T14:19:16.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BP27V",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.15326,
                        "sentimentNegative": 0.0921122,
                        "sentimentPositive": 0.754628,
                        "storyCreated": "2023-10-19T16:18:28.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/19/nL1N3BP27V/3/",
                        "headline": "CORRECTED-EssilorLuxottica Q3 sales up",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-19T16:18:28.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {"orders": ["Order:IT_REFINITIV_1:1:NEWO:1700131607052"]},
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BQ21W",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.125306,
                        "sentimentNegative": 0.678764,
                        "sentimentPositive": 0.19593,
                        "storyCreated": "2023-10-20T22:55:32.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/20/nL1N3BQ21W/6/",
                        "headline": "Wall St Week Ahead-Investors seek shelter as U.S. stocks grow more turbulent",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_1:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 1000.0,
                        "numberOfOrders": 1,
                        "timestamp": "2023-10-20T22:55:32.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BT25O",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.129169,
                        "sentimentNegative": 0.795792,
                        "sentimentPositive": 0.0750389,
                        "storyCreated": "2023-10-23T19:43:04.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/23/nL1N3BT25O/8/",
                        "headline": "UPDATE 2-Massachusetts to update multistate probe on Instagram's impact on kids",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 2,
                        "timestamp": "2023-10-23T19:43:04.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BV3F5",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.128031,
                        "sentimentNegative": 0.814371,
                        "sentimentPositive": 0.0575978,
                        "storyCreated": "2023-10-25T20:35:00.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL1N3BV3F5/6/",
                        "headline": "US STOCKS-S&P 500, Nasdaq end sharply lower as Alphabet disappoints, Treasury yields bounce",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T20:35:00.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL1N3BW252",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.138729,
                        "sentimentNegative": 0.576361,
                        "sentimentPositive": 0.28491,
                        "storyCreated": "2023-10-26T14:28:18.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/26/nL1N3BW252/1/",
                        "headline": "LIVE MARKETS-Wall Street indexes dip on earnings, data",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "SELL",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": -1000.0,
                        "netScenarioPosition": -2000.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-26T14:28:18.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV138",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.198337,
                        "sentimentNegative": 0.68397,
                        "sentimentPositive": 0.117692,
                        "storyCreated": "2023-10-25T04:23:25.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV138/1/",
                        "headline": "PRESS DIGEST- New York Times business news - Oct 25",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": 0.0,
                        "numberOfOrders": 4,
                        "timestamp": "2023-10-25T04:23:25.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV2QK",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.115971,
                        "sentimentNegative": 0.680262,
                        "sentimentPositive": 0.203767,
                        "storyCreated": "2023-10-25T17:56:57.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV2QK/3/",
                        "headline": "BUZZ-U.S. STOCKS ON THE MOVE-Meta, GE, Vicor",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T17:56:57.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV5AG",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.143198,
                        "sentimentNegative": 0.788016,
                        "sentimentPositive": 0.0687856,
                        "storyCreated": "2023-10-25T20:17:41.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV5AG/2/",
                        "headline": "Facebook parent Meta beats quarterly revenue estimates",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T20:17:41.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nL4N3BV5AU",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": 0.165206,
                        "sentimentNegative": 0.141279,
                        "sentimentPositive": 0.693515,
                        "storyCreated": "2023-10-25T20:21:15.000Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nL4N3BV5AU/9/",
                        "headline": "UPDATE 5-Facebook-parent Meta beats revenue estimates on digital ad strength",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T20:21:15.000Z",
                    }
                },
            },
            {
                "thresholds": {
                    "assetClass": None,
                    "afterTradeTimestamp": {"unit": "days", "value": 3},
                    "beforeTradeTimestamp": {"unit": "days", "value": 1},
                    "minimumOrderQuantity": None,
                    "newsSources": ["Reuters News"],
                    "sentiment": None,
                    "relevance": "high",
                },
                "records": {
                    "orders": [
                        "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_4:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_3:1:NEWO:1700131607052",
                        "Order:IT_REFINITIV_5:2:NEWO:1700131607052",
                        "Order:IT_REFINITIV_6:2:NEWO:1700131607052",
                    ]
                },
                "additionalFields": {
                    "topLevel": {
                        "storyId": "nTUA30HGXB",
                        "storySubjectRelevance": "high",
                        "sentimentNeutral": None,
                        "sentimentNegative": None,
                        "sentimentPositive": None,
                        "storyCreated": "2023-10-25T20:16:29.289Z",
                        "involvedParties": ["Counterparty 1", "Client 1"],
                        "newsSourceName": "['Reuters News']",
                        "linkURL": "s3://news.steeleye.co/aries/lake/ingress/news/2023/10/25/nTUA30HGXB/23/",
                        "headline": "META PLATFORMS INC <META.O> Q4 REV VIEW $38.85 BLN -- REFINITIV IBES DATA",
                        "entityName": "META PLATFORMS CL A ORD",
                        "&key": "Order:IT_REFINITIV_2:1:NEWO:1700131607052",
                        "priceFormingData.initialQuantity": 1000.0,
                        "executionDetails.buySellIndicator": "BUYI",
                        "trader.&id": None,
                        "trader.name": None,
                        "__trader_id__": None,
                        "__trader_name__": None,
                        "__instrument_code__": None,
                        "__venue__": None,
                        "netQuantity": 1000.0,
                        "netScenarioPosition": -1000.0,
                        "numberOfOrders": 5,
                        "timestamp": "2023-10-25T20:16:29.289Z",
                    }
                },
            },
        ]

        same_scenario = 0

        for obtained_scenario in obtained_scenarios:
            obtained_scenario_records = obtained_scenario.json.get("records")
            obtained_scenario_additional_fields = obtained_scenario.json.get(
                "additionalFields"
            ).get("topLevel")

            for expected_scenario in expected_scenarios:
                expected_scenario_additional_fields = expected_scenario.get("additionalFields").get(
                    "topLevel"
                )
                expected_scenario_records = expected_scenario.get("records")

                if validate_scenarios(
                    obtained_fields=obtained_scenario_additional_fields,
                    expected_fields=expected_scenario_additional_fields,
                ) and validate_scenarios(
                    obtained_fields=expected_scenario_records,
                    expected_fields=obtained_scenario_records,
                ):
                    same_scenario += 1

        assert same_scenario == len(obtained_scenarios)

    def test_case_9_2_sentiment(self, helpers, monkeypatch):
        """Retrieval of Refinitiv news.

        There may be more alerts than expected because the file used
        initially is only a sample of news data. Should get 27 alerts to
        refinitiv news.
        """
        import market_abuse_algorithms.data_source.repository.sdp.es_sdp
        import market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query

        thresholds = {
            "newsSources": [
                "Reuters News",
            ],
            "relevance": "high",
            "sentiment": 0.7,
            "beforeTradeTimestamp": {"unit": "days", "value": 0},
            "afterTradeTimestamp": {"unit": "days", "value": 2},
        }

        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.insiderTrading.withNews.Refinitiv.csv"
                            ]
                        }
                    }
                ]
            }
        }

        def fake_inspect_required_fields():
            pass

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_market_client.return_value = FakeMarketClient()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.get_refinitiv_client.return_value = FakeRefinitivNewsClient(
            relevance="high"
        )

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.inspect_required_fields.return_value = fake_inspect_required_fields()

        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations = MagicMock()
        market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query.Queries.get_instruments_combinations.return_value = [
            ["US30303M1027USDARCX"]
        ]

        def custom_side_effect(*args, **kwargs):
            # Encapsulate the return values in this method
            values = [
                pd.DataFrame(),
                pd.read_csv(
                    TEST_DATA.joinpath("test_case_9_1_no_sentiment_orders.csv"),
                    index_col=0,
                    sep=",",
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                ),
                pd.read_csv(
                    TEST_DATA.joinpath("test_case_9_2_sentiment_get_net_scenario.csv"),
                    index_col=0,
                    sep=",",
                ),
            ]

            def inner(*args, **kwargs):
                return values.pop(0)  # Pop the first value each time the method is called

            return inner

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.sdp.es_sdp.SDP,
            "search_after_query",
            custom_side_effect(),
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        strategy.run_local = True

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 2


def validate_scenarios(obtained_fields, expected_fields):
    for field in obtained_fields:
        if isinstance(obtained_fields.get(field), list):
            return isinstance(expected_fields.get(field), list) and set(
                obtained_fields.get(field)
            ) == set(expected_fields.get(field))
        else:
            return obtained_fields.get(field) == expected_fields.get(field)
