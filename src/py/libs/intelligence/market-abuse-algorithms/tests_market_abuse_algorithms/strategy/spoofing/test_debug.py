import pytest
from market_abuse_algorithms.strategy.spoofing.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerSpoofing:
    def test_case_debug(self, helpers):
        thresholds = {"timeWindow": 60, "cancellationsExecutionsVolume": 5}

        filters = {
            "bool": {
                "must_not": [
                    {"terms": {"executionDetails.validityPeriod": ["FOKV", "IOCV"]}},
                    {"terms": {"orderIdentifiers.tradingVenueTransactionIdCode": ["TCMLIQ"]}},
                    {
                        "terms": {
                            "&id": {
                                "index": "fcc:surv_exclude_by_id",
                                "type": "doc",
                                "id": "9280d114-a85d-4f64-903f-1ae6d8035742",
                                "path": "excludes",
                            }
                        }
                    },
                ],
                "must": [{"range": {"&timestamp": {"gte": "2022-09-19T13:52:28.940000Z"}}}],
            }
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        strategy.scenarios
