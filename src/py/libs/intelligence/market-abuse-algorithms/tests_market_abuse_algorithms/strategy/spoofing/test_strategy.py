import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.spoofing.static import ThresholdsNames
from market_abuse_algorithms.strategy.spoofing.strategy import Strategy
from mock.mock import MagicMock
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


class TestSpoofing:
    def test_empty_order_updated_fields(self):
        """Test for bug on https://steeleye.atlassian.net/browse/MA-132
        Contains function with logic implemented to fix the bug in the algo
        function."""

        df = pd.read_csv(TEST_DATA.joinpath("test_data.csv"))

        def filter_cancellations_mask(df: pd.DataFrame) -> pd.DataFrame:
            mask = (
                (df[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.CAME)
                & (df.loc[:, OrderField.TS_ORD_UPDATED].notna())
                & (df.loc[:, OrderField.PC_FD_TRD_QTY].notna())
            )

            df = df.loc[mask, :]

            return df

        result = filter_cancellations_mask(df=df)

        # Test the case where involved parties exist should be 19 with involvedParties column
        assert result.shape[0] == 1

    def test_case_1_1(self, helpers):
        """Test https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/188966
        5414/Spoofing#Test-Case-1.1---Thresholds Confirms the behavior when
        priceFormingData.tradedQuantity for CAME orders is null."""

        filters = {}

        thresholds = {
            ThresholdsNames.TIME_WINDOW: 86400,
            ThresholdsNames.CANCELLATIONS_EXECUTIONS_VOLUME: 1,
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_for_scenarios = MagicMock()
        strategy.queries.get_additional_fields_for_scenarios.return_value = pd.DataFrame()

        file_path = TEST_DATA.joinpath("steeleyeBlotter.mar.spoofing.2.2.csv")

        data = pd.read_csv(
            file_path,
            parse_dates=[OrderField.TS_ORD_UPDATED, OrderField.TS_TRADING_DATE_TIME],
            index_col=0,
        )

        strategy._apply_strategy_mini_batch(data)

        assert len(strategy.scenarios) == 1

    def test_case_2_2(self, helpers):
        """Test from the EU-4824.

        Confirms the behavior when priceFormingData.tradedQuantity for
        CAME orders is 0
        """
        filters = {}

        thresholds = {
            ThresholdsNames.TIME_WINDOW: 86400,
            ThresholdsNames.CANCELLATIONS_EXECUTIONS_VOLUME: 1.7,
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_for_scenarios = MagicMock()
        strategy.queries.get_additional_fields_for_scenarios.return_value = pd.DataFrame()

        file_path = TEST_DATA.joinpath("steeleyeBlotter.mar.spoofing.2.2.csv")

        data = pd.read_csv(
            file_path,
            parse_dates=[OrderField.TS_ORD_UPDATED, OrderField.TS_TRADING_DATE_TIME],
            index_col=0,
        )

        strategy._apply_strategy_mini_batch(data)

        assert len(strategy.scenarios) == 1
