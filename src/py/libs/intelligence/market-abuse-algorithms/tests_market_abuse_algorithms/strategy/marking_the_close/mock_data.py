import market_abuse_algorithms.strategy.abstract_marking_the_price.query
import market_abuse_algorithms.strategy.marking_the_close.strategy


def mock_marking_the_close_query(monkeypatch, *args, **kwargs):
    def _evaluate_thresholds(*args, **kwargs):
        pass

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_marking_the_price.query.MarkingThePriceQueries,
        "_evaluate_thresholds",
        _evaluate_thresholds,
    )
