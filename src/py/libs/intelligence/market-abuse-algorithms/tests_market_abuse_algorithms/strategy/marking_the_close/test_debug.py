import pytest
from market_abuse_algorithms.strategy.marking_the_close.strategy import Strategy


class TestDebuggerMarkingTheClose:
    @pytest.mark.usefixtures("skip_test_in_ci")
    def test_case_debug(self, helpers):
        # 1 2c18ed4e-b828-4b7e-bbb9-2da9f9e63028

        thresholds = {
            "client20DayAdv": 0.01,
            "lookBackPeriod": 900,
            "market20DayAdv": 0.01,
            "markingType": "Marking the close (including close-auction)",
            "minimumNotionalCurrency": "AUD",
            "priceSpike": 0,
            "minimumNotional": 0,
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "&timestamp": {
                                "gte": "2024-10-10T00:01:14.256000+00:00",
                                "lte": "2024-10-10T20:00:14.256000+00:00",
                            }
                        }
                    }
                ]
            }
        }
        # filters = {
        #     "bool": {
        #         "must": [
        #             {
        #                 "terms": {
        #                     "sourceKey": [
        #                         "s3://hesham.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/FIL_SEblotter_20230221.csv"  # noqa: E501
        #                     ]
        #                 }
        #             },
        #             {
        #                 "terms": {
        #                     "instrumentDetails.instrument.instrumentIdCode": [
        #                         "CA23809L1085"
        #                     ]
        #                 }
        #             },
        #         ]
        #     }
        # }

        # 2 49b04225-0e53-498e-89a2-4085ddf6576c
        # thresholds = {
        #     "client20DayAdv": 0,
        #     "lookBackPeriod": 1800,
        #     "market20DayAdv": 0,
        #     "markingType": "Marking the close-auction",
        #     "minimumNotionalCurrency": "USD",
        #     "priceSpike": 0,
        #     "minimumNotional": 0,
        # }
        # filters = {
        #     "bool": {
        #         "must": [
        #             {
        #                 "terms": {
        #                     "instrumentDetails.instrument.instrumentIdCode": [
        #                         "SGXC50067435"
        #                     ]
        #                 }
        #             },
        #             {
        #                 "terms": {
        #                     "sourceKey": [
        #                         "s3://hesham.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/FIL_SEblotter_20230221.csv"  # noqa: E501
        #                     ]
        #                 }
        #             },
        #         ]
        #     }
        # }

        # 3 5440727e-1385-4072-a773-e8f4db4342d4
        # thresholds = {
        #     "client20DayAdv": 0,
        #     "lookBackPeriod": 1800,
        #     "market20DayAdv": 0,
        #     "markingType": "Marking the close",
        #     "minimumNotionalCurrency": "USD",
        #     "priceSpike": 0,
        #     "minimumNotional": 0,
        # }
        # filters = {
        #     "bool": {
        #         "must": [
        #             {
        #                 "terms": {
        #                     "instrumentDetails.instrument.instrumentIdCode": [
        #                         "SGXC50067435"
        #                     ]
        #                 }
        #             },
        #             {
        #                 "terms": {
        #                     "sourceKey": [
        #                         "s3://hesham.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/FIL_SEblotter_20230221.csv"  # noqa: E501
        #                     ]
        #                 }
        #             },
        #         ]
        #     }
        # }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        scenarios = strategy.scenarios

        scenarios
