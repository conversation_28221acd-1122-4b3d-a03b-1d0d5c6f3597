# ruff: noqa: E501, E721
import pandas as pd
import pytest
from datetime import datetime
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, NewColumns
from market_abuse_algorithms.strategy.parking.static import DFColumns
from market_abuse_algorithms.strategy.parking.strategy import Strategy
from pathlib import Path
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")

FILTER_1 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://refinitiv.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/mar/parking/steeleyeBlotter.mar.parking.1.csv"
                ]
            }
        }
    }
}

FILTER_2 = {
    "bool": {
        "must": {
            "terms": {
                "sourceKey": [
                    "s3://refinitiv.uat.steeleye.co/feeds/trade/steeleye-trade-blotter/skip_tc/mar/parking/steeleyeBlotter.mar.parking.2.csv"
                ]
            }
        }
    }
}

COLUMNS = [
    "&key",
    "&parent",
    "__client__",
    "__instrument_code__",
    NewColumns.TRADER_NAME,
    NewColumns.TRADER_ID,
    "__venue__",
    "bestExecutionData.transactionVolume.native",
    "bestExecutionData.transactionVolume.nativeCurrency",
    "buyer.name",
    "clientIdentifiers.client.name",
    "counterparty.name",
    "executionDetails.buySellIndicator",
    "executionDetails.orderType",
    "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier",
    "instrumentDetails.instrument.ext.bestExAssetClassMain",
    "instrumentDetails.instrument.ext.instrumentUniqueIdentifier",
    "instrumentDetails.instrument.instrumentFullName",
    "instrumentDetails.instrument.instrumentIdCode",
    "instrumentDetails.instrument.venue.tradingVenue",
    "orderIdentifiers.orderIdCode",
    "priceFormingData.price",
    "priceFormingData.tradedQuantity",
    "reportDetails.executingEntity.name",
    "seller.name",
    "timestamps.orderSubmitted",
    "timestamps.tradingDateTime",
    "trader",
    "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks",
    "transactionDetails.venue",
]


@pytest.fixture
def fake_data():
    """
    Process & Read fake data files
    :return: pandas DataFrame with fake data
    """

    def custom_date_parser():
        return lambda x: datetime.strptime(x, "%Y-%m-%d %H:%M:%S")

    def _foo(*args):
        result = pd.read_csv(
            *args,
            index_col=0,
            sep=",",
            parse_dates=["timestamps.tradingDateTime", "timestamps.orderSubmitted"],
            date_parser=custom_date_parser(),
        )
        return result

    return _foo


class TestParking:
    @pytest.mark.usefixtures("skip_test_in_ci")
    def test_get_data_to_analyse(self, helpers):
        """Test if the data comes in the correct format and with the correct
        content.

        should be tested in refinitiv.uat
        """
        thresholds = {
            "evaluationType": "Client",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        filters = FILTER_1
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        data = strategy.queries.cases_to_analyse()

        for result in data:
            assert type(result) == pd.DataFrame
            assert len(result.columns) == len(COLUMNS)
            assert sorted(result.columns) == COLUMNS

    def test_evaluation_type_client_with_filter_1(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Client",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Client",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_evaluation_type_client_with_filter_1.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_trader_with_filter_1(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Trader",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Trader",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        # data = strategy.queries.cases_to_analyse()
        #
        # for result in data:
        #     result.to_csv("test_evaluation_type_trader_with_filter_1.csv")

        client_data = fake_data(TEST_DATA.joinpath("test_evaluation_type_trader_with_filter_1.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_desk_with_filter_1(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Desk",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Desk",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_evaluation_type_desk_with_filter_1.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_counterparty_with_filter_1(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Counterparty",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Counterparty",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_counterparty_with_filter_1.csv")
        )

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_entity_with_filter_1(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Executing Entity",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Executing Entity",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(TEST_DATA.joinpath("test_evaluation_type_entity_with_filter_1.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_entity_with_filter_1_no_hits(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Executing Entity",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 100001,
        quantityDifference = 0

        and

        evaluationType = "Executing Entity",
        lookBackPeriod = 7,
        maxTradeCount = 6,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 1
        """

        number_of_scenarios = 0

        thresholds_1 = {
            "evaluationType": "Executing Entity",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 1000001,
            "quantityDifference": 0,
        }

        thresholds_2 = {
            "evaluationType": "Executing Entity",
            "lookBackPeriod": 7,
            "maxTradeCount": 2,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        # threshold 1
        context = helpers.get_context(thresholds=thresholds_1, filters=FILTER_1)

        strategy_1 = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_entity_with_filter_1_no_hits_1.csv")
        )

        strategy_1._apply_strategy_mini_batch(client_data)

        assert len(strategy_1.scenarios) == number_of_scenarios

        # threshold 2
        context = helpers.get_context(thresholds=thresholds_2, filters=FILTER_1)

        strategy_2 = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_entity_with_filter_1_no_hits_2.csv")
        )

        strategy_2._apply_strategy_mini_batch(client_data)

        assert len(strategy_2.scenarios) == number_of_scenarios

    def test_two_evaluation_types_with_filter_2_no_hits(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Client",
        lookBackPeriod = 7,
        maxTradeCount = 4,
        minTradeQuantity = 5000,
        quantityDifference = 0

        and

        evaluationType = "Trader",
        lookBackPeriod = 7,
        maxTradeCount = 5,
        minTradeQuantity = 50000,
        quantityDifference = 0

        Filter 2
        """

        number_of_scenarios = 0

        thresholds_1 = {
            "evaluationType": "Client",
            "lookBackPeriod": 7,
            "maxTradeCount": 3,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        thresholds_2 = {
            "evaluationType": "Trader",
            "lookBackPeriod": 7,
            "maxTradeCount": 3,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        # threshold 1
        context = helpers.get_context(thresholds=thresholds_1, filters=FILTER_1)

        strategy_1 = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_client_with_filter_2_no_hits.csv")
        )

        strategy_1._apply_strategy_mini_batch(client_data)

        assert len(strategy_1.scenarios) == number_of_scenarios

        # threshold 2
        context = helpers.get_context(thresholds=thresholds_2, filters=FILTER_1)

        strategy_2 = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_trader_with_filter_2_no_hits.csv")
        )

        strategy_2._apply_strategy_mini_batch(client_data)

        assert len(strategy_2.scenarios) == number_of_scenarios

    def test_evaluation_type_entity_with_filter_2(self, helpers, fake_data):
        """Test Parking with the following set of parameters.

        evaluationType = "Executing Entity",
        lookBackPeriod = 7,
        maxTradeCount = 4,
        minTradeQuantity = 5000,
        quantityDifference = 0

        Filter 2
        """
        number_of_scenarios = 1
        number_of_buys = 2
        number_of_sells = 2

        thresholds = {
            "evaluationType": "Executing Entity",
            "lookBackPeriod": 7,
            "maxTradeCount": 5,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)
        strategy.queries.cases_to_analyse()

        client_data = fake_data(TEST_DATA.joinpath("test_evaluation_type_entity_with_filter_2.csv"))

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

        for scenario in strategy.scenarios:
            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.BUY)
                == number_of_buys
            )

            assert (
                scenario._scenario["additionalFields"]["topLevel"][DFColumns.BUY_SELL_INDICATOR]
                .tolist()
                .count(BuySell.SELL)
                == number_of_sells
            )

    def test_evaluation_type_trader_with_price_difference_0_hits(self, helpers, fake_data):
        """Test Parking: Test Case 2.3.

        evaluationType = "Trader",
        lookBackPeriod = 5,
        maxTradeCount = 4,
        minTradeQuantity = 123200,
        quantityDifference = 0,
        priceDifference = 0
        """
        number_of_scenarios = 0

        thresholds = {
            "evaluationType": "Trader",
            "lookBackPeriod": 5,
            "maxTradeCount": 4,
            "minTradeQuantity": 123200,
            "quantityDifference": 0,
            "priceDifference": 0,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_client_with_filter_2_1_hit.csv")
        )

        strategy._apply_strategy_mini_batch(client_data)

        assert len(strategy.scenarios) == number_of_scenarios

    def test_evaluation_type_trader_with_price_difference_1_hit(self, helpers, fake_data):
        """Test Parking: When priceDifference > 0.

        evaluationType = "Trader",
        lookBackPeriod = 5,
        maxTradeCount = 4,
        minTradeQuantity = 123200,
        quantityDifference = 0,
        priceDifference = 1
        """
        number_of_scenarios = 1

        thresholds = {
            "evaluationType": "Trader",
            "lookBackPeriod": 5,
            "maxTradeCount": 4,
            "minTradeQuantity": 123200,
            "quantityDifference": 0,
            "priceDifference": 1,
        }

        context = helpers.get_context(thresholds=thresholds, filters=FILTER_1)

        strategy = Strategy(context=context)

        client_data = fake_data(
            TEST_DATA.joinpath("test_evaluation_type_client_with_filter_2_1_hit.csv")
        )

        strategy._apply_strategy_mini_batch(client_data)
        scenarios = strategy.scenarios
        assert len(scenarios) == number_of_scenarios

        top_level_dict = scenarios[0]._scenario.get("additionalFields").get("topLevel")

        assert all(
            col in top_level_dict.keys()
            for col in [
                DFColumns.PRICE_DIFFERENCE_PERCENTAGE,
                DFColumns.PRICE_DIFFERENCE,
                DFColumns.QUANTITY_DIFFERENCE,
                DFColumns.QUANTITY_DIFFERENCE_PERCENTAGE,
            ]
        )

        price_diff_percentage = top_level_dict.get(DFColumns.PRICE_DIFFERENCE_PERCENTAGE)
        quantity_diff_percentage = top_level_dict.get(DFColumns.QUANTITY_DIFFERENCE_PERCENTAGE)
        price_diff = top_level_dict.get(DFColumns.PRICE_DIFFERENCE)
        quantity_diff = top_level_dict.get(DFColumns.QUANTITY_DIFFERENCE)

        assert price_diff_percentage.round(8) == 0.48959608
        assert quantity_diff_percentage.round(8) == 0
        assert price_diff.round(8) == 0.00489596
        assert quantity_diff.round(8) == 0

    def test_implied_pl(self):
        # Case where the sells are larger than the buys and should be positive
        df_1 = pd.read_csv(TEST_DATA.joinpath("calculate_implied_pl_positive.csv"))

        result_1 = Strategy.calculate_implied_pl(df_1)

        assert result_1 == 470042.5167864859

        # Case where the buys are larger than the sells and should be negative
        df_2 = pd.read_csv(TEST_DATA.joinpath("calculate_implied_pl_negative.csv"))

        result_2 = Strategy.calculate_implied_pl(df_2)

        assert result_2 == -10482787.483213512

    def test_cases_to_analyse(self, helpers, fake_data):
        thresholds = {
            "evaluationType": "Client",
            "lookBackPeriod": 5,
            "maxTradeCount": 4,
            "minTradeQuantity": 50000,
            "quantityDifference": 0,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.parking.1.csv"
                        ]
                    }
                }
            }
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        scroll_data = fake_data(TEST_DATA.joinpath("uc1.1_scroll_data.csv"))

        strategy.queries.get_initial_query = MagicMock()
        strategy.queries.get_initial_query.return_value = OrderQuery()

        strategy.queries.get_instruments_combinations = MagicMock()
        strategy.queries.get_instruments_combinations.return_value = [["US88160R1014"]]

        strategy.queries._sdp_repository.search_after_query = MagicMock()
        strategy.queries._sdp_repository.search_after_query.return_value = scroll_data

        strategy.queries.get_order_meta_key = MagicMock()
        strategy.queries.get_order_meta_key.return_value = {
            "parking_1_1:1:NEWO": "Order:parking_1_1:1:NEWO:1690286410806",
            "parking_1_2:2:NEWO": "Order:parking_1_2:2:NEWO:1690286410806",
            "parking_1_3:1:NEWO": "Order:parking_1_3:1:NEWO:1690286410806",
            "parking_1_4:2:NEWO": "Order:parking_1_4:2:NEWO:1690286410806",
        }

        for data in strategy.queries.cases_to_analyse():
            assert data.shape == (4, 35)
