import pytest
from market_abuse_algorithms.strategy.parking.strategy import Strategy


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerParking:
    def test_case_debug(self, helpers):
        thresholds = {
            "evaluationType": "Trader",
            "lookBackPeriod": 5,
            "maxTradeCount": 4,
            "minTradeQuantity": 50000,
            "priceDifference": 0,
            "quantityDifference": 0,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/mar/steeleyeBlotter.mar.parking.2.csv"  # noqa: E501
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        strategy
