# ruff: noqa: E501
import pandas as pd
import pytest
from market_abuse_algorithms.data_source.query.utils import (
    remove_date_range_from_iris_filters,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.utils.filters import (
    filter_market_visible_orders,
    get_value_based_on_column_timestamp,
)


class TestGetValueBasedOnColumnTimestamp:
    @pytest.fixture
    def mock_dataframe_missing_column(self) -> pd.DataFrame:
        """dataframe with missing required column."""
        df = pd.DataFrame(
            {
                "foo": [
                    pd.Timestamp(2022, 6, 6),
                    pd.Timestamp(2022, 6, 7),
                    pd.Timestamp(2022, 6, 8),
                ]
            }
        )
        return df

    @pytest.fixture
    def mock_dataframe_normal(self) -> pd.DataFrame:
        """normal dataframe."""
        df = pd.DataFrame(
            {
                "foo": [
                    pd.Timestamp(2022, 6, 6),
                    pd.Timestamp(2022, 6, 7),
                    pd.<PERSON>tamp(2022, 6, 8),
                ],
                "bar": [15, 100, -200],
            }
        )
        return df

    def test_missing_column(self, mock_dataframe_missing_column):
        """test to verify it returns nothing if a required column is missing.

        :param mock_dataframe_missing_column: dataframe with missing column
        """
        result = get_value_based_on_column_timestamp(
            data=mock_dataframe_missing_column,
            ts=pd.Timestamp(2022, 6, 6),
            ts_column="foo",
            value_column="bar",
        )

        assert pd.isna(result)

    def test_normal_case(self, mock_dataframe_normal):
        """test if the with needed timestamp and columns returns the required
        value.

        :param mock_dataframe_normal: normal dataframe with required columns
        """
        result = get_value_based_on_column_timestamp(
            data=mock_dataframe_normal,
            ts=pd.Timestamp(2022, 6, 6),
            ts_column="foo",
            value_column="bar",
        )
        assert result == 15

    def test_not_existing_timestamp(self, mock_dataframe_normal):
        """test to check it returns None if there is no timestamp matching the
        input timestamp.

        :param mock_dataframe_normal: normal dataframe with required columns
        """
        result = get_value_based_on_column_timestamp(
            data=mock_dataframe_normal,
            ts=pd.Timestamp(2022, 6, 5),
            ts_column="foo",
            value_column="bar",
        )
        assert pd.isna(result)


@pytest.fixture()
def source_dataframe_fokv() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.EXC_DTL_VALIDITY_PERIOD: [
                "FOKV",
                "FOKV",
                "FOKV",
                "FOKV",
                "FOKV",
            ],
            OrderField.PC_FD_INIT_QTY: ["off", pd.NA, "12.4", "34", 5],
            OrderField.PC_FD_PRICE: ["2", "56", "23", "45", pd.NA],
        }
    )
    return df


@pytest.fixture()
def source_dataframe_davy() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.EXC_DTL_VALIDITY_PERIOD: [
                "DAVY",
                "DAVY",
                "DAVY",
                "DAVY",
                "DAVY",
            ],
            OrderField.PC_FD_INIT_QTY: ["off", pd.NA, "12.4", "34", 5],
            OrderField.PC_FD_PRICE: ["2", "56", "23", "45", pd.NA],
        }
    )
    return df


class TestDataFilters:
    def test_filter_market_visible_orders_empty(self, source_dataframe_fokv):
        obtained_result = filter_market_visible_orders(data=source_dataframe_fokv)
        assert obtained_result.empty

    def test_filter_market_visible_orders_not_empty(self, source_dataframe_davy):
        obtained_result = filter_market_visible_orders(data=source_dataframe_davy)
        assert obtained_result.shape == (5, 3)


class TestRemoveIrisFilters:
    def test_remove_iris_filters_list_with_script_and_range(self):
        """Test to verify it removes script and range filters from a list while
        keeping the other conditions."""
        input_filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                            ]
                        }
                    },
                    {
                        "script": {
                            "script": {
                                "lang": "painless",
                                "params": {
                                    "end": 1699315199999,
                                    "start": 1699228800000,
                                },
                                "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                            }
                        }
                    },
                    {"range": {"&timestamp": {"gte": "2019-05-22T11:10:05.433000Z"}}},
                ]
            }
        }

        expected_filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                            ]
                        }
                    }
                ]
            }
        }

        output_filters = remove_date_range_from_iris_filters(filters=input_filters)

        assert output_filters == expected_filters

    def test_remove_iris_filters_single_range_filter(self):
        """Test that removes single range filter."""
        input_filters = {
            "bool": {"filter": {"range": {"&timestamp": {"gte": "2019-05-22T11:10:05.433000Z"}}}}
        }

        expected_filters = {}

        output_filters = remove_date_range_from_iris_filters(filters=input_filters)

        assert output_filters == expected_filters

    def test_remove_iris_filters_single_non_date_range_filter(self):
        """Verifies single non date range filters is kept."""
        input_filters = {
            "bool": {
                "filter": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                        ]
                    }
                }
            }
        }

        expected_filters = {
            "bool": {
                "filter": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                        ]
                    }
                }
            }
        }

        output_filters = remove_date_range_from_iris_filters(filters=input_filters)

        assert output_filters == expected_filters

    def test_remove_iris_filters_single_non_date_range_filter_should_without_filters(
        self,
    ):
        """Verifies it removes should date range filters when filters not
        available."""

        input_filters = {
            "bool": {
                "should": [
                    {
                        "bool": {
                            "filter": [
                                {"term": {"executionDetails.orderStatus": "NEWO"}},
                                {
                                    "range": {
                                        "timestamps.orderSubmitted": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    {
                        "bool": {
                            "filter": [
                                {
                                    "range": {
                                        "timestamps.tradingDateTime": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                }
                            ],
                            "must_not": [{"term": {"executionDetails.orderStatus": "NEWO"}}],
                        }
                    },
                ],
                "minimum_should_match": 1,
            }
        }

        expected_filters = {}

        output_filters = remove_date_range_from_iris_filters(filters=input_filters)

        assert expected_filters == output_filters

    def test_remove_iris_filters_single_non_date_range_filter_should_with_filters(self):
        """Verifies it removes should date range filters."""

        input_filters = {
            "bool": {
                "filter": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                        ]
                    }
                },
                "should": [
                    {
                        "bool": {
                            "filter": [
                                {"term": {"executionDetails.orderStatus": "NEWO"}},
                                {
                                    "range": {
                                        "timestamps.orderSubmitted": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    {
                        "bool": {
                            "filter": [
                                {
                                    "range": {
                                        "timestamps.tradingDateTime": {
                                            "gte": "2024-02-15T00:00:00",
                                            "lte": "2024-12-31T23:59:59",
                                        }
                                    }
                                }
                            ],
                            "must_not": [{"term": {"executionDetails.orderStatus": "NEWO"}}],
                        }
                    },
                ],
                "minimum_should_match": 1,
            },
        }

        expected_filters = {
            "bool": {
                "filter": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.EU-9410.3.csv"
                        ]
                    }
                }
            }
        }

        output_filters = remove_date_range_from_iris_filters(filters=input_filters)

        assert expected_filters == output_filters
