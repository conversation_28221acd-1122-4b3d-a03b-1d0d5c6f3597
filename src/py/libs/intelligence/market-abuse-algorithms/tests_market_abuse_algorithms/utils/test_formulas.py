import pandas as pd
import pytest
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField
from market_abuse_algorithms.utils.formulas import calculate_net_amount


class TestNetTradeAmount:
    @pytest.fixture()
    def mock_dataframe_negative(self):
        """
        example dataframe that should have a negative return
        :return: mock dataframe
        """
        df = pd.DataFrame(
            {
                OrderField.EXC_DTL_BUY_SELL_IND: [
                    BuySell.BUY,
                    BuySell.SELL,
                    BuySell.BUY,
                ],
                "value_column": [5000, 10000, 200],
            }
        )
        return df

    @pytest.fixture()
    def mock_dataframe_positive(self):
        """
        example dataframe that should have a positive return
        :return: mock dataframe
        """
        df = pd.DataFrame(
            {
                OrderField.EXC_DTL_BUY_SELL_IND: [
                    BuySell.SELL,
                    BuySell.BUY,
                    BuySell.SELL,
                ],
                "value_column": [5000, 10000, 200],
            }
        )
        return df

    def test_net_amount_negative(self, mock_dataframe_negative):
        """Tests for cases when there are more sells value than buys value.

        :param mock_dataframe_negative: dataframe to test
        :return: None
        """
        net_amount = calculate_net_amount(data=mock_dataframe_negative, column_name="value_column")
        assert net_amount == -4800

    def test_net_amount_positive(self, mock_dataframe_positive):
        """Tests for cases when there are more buys value than sells value.

        :param mock_dataframe_positive: dataframe to test
        :return: None
        """
        net_amount = calculate_net_amount(data=mock_dataframe_positive, column_name="value_column")
        assert net_amount == 4800
