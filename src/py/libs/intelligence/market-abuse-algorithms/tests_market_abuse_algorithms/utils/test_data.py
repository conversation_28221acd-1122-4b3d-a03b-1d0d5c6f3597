# ruff: noqa: E501
import datetime
import numpy as np
import pandas as pd
import pytest
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import Buy<PERSON>ell, NewColumns, OrderField
from market_abuse_algorithms.data_source.static.utility import OrderStateValueColumns, PNLColumns
from market_abuse_algorithms.strategy.abstract_layering_v2.static import AlgoColumnsEnum
from market_abuse_algorithms.utils.data import (
    create_time_window_groups,
    epoch_filter_extract,
    filter_df_by_timestamps,
    get_client_name,
    get_instrument_id_code,
    get_list_instrument_unique_identifier,
    get_trader_info,
    get_venue_fallback,
    get_venue_name,
    process_numeric_columns,
    remove_rows_with_value_in_column,
    sort_by_date,
)
from market_abuse_algorithms.utils.formulas import (
    calculate_confidence_interval,
    calculate_order_states_values,
    calculate_pnl_order_states,
)
from pathlib import Path
from scipy.stats import t
from se_elastic_schema.static.mifid2 import PriceNotation
from se_market_data_utils.schema.parquet import EoDStatsColumns
from typing import Dict, List, Union

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE: [6, 32, 46, "87", 67],
            OrderField.PC_FD_INIT_QTY: ["off", pd.NA, "12.4", "34", 5],
            OrderField.PC_FD_PRICE: ["2", "56", "23", "45", pd.NA],
        }
    )
    return df


@pytest.fixture()
def source_dataframe_more_nan() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE: [6, 32, 46, "87", 67, 34, 35, 36],
            OrderField.PC_FD_INIT_QTY: ["off", pd.NA, "12.4", "34", 5, 40, 41, 42],
            OrderField.PC_FD_PRICE: ["2", "56", "23", "45", pd.NA, 37, 38, 39],
        }
    )
    return df


@pytest.fixture()
def source_dataframe_order_values() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.PC_FD_PRICE: [10, 20, 30, 5, 2],
            OrderField.PC_FD_TRD_QTY: [1, 2, 3, 2, 4],
            OrderField.INST_DERIV_PRICE_MULTIPLIER: [-0.4, 1.5, 1, 1, 3],
            OrderField.INST_DERIV_STRIKE_PRICE: [3, 1.3, 2, 2, 6],
            OrderField.TRX_DTL_PC_NOTATION: ["MONE", "BAPO", "PERC", "MONE", "TRYS"],
            OrderField.TS_TRADING_DATE_TIME: [
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
            ],
            OrderField.TRX_DTL_QUANTITY_NOTATION: [
                "UNIT",
                "UNIT",
                "UNIT",
                "NOML",
                "MONE",
            ],
            OrderField.INST_CLASSIFICATION: [
                "VSLSGT",
                "PSSYTR",
                "OSLOPP",
                "PSLKIU",
                "OSLOPP",
            ],
        }
    )
    return df


@pytest.fixture
def source_dataframe_order_values_missing_traded_quantity() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.PC_FD_PRICE: [10, 20, 30, 5, 2],
            OrderField.INST_DERIV_PRICE_MULTIPLIER: [-0.4, 1.5, 1, 1, 3],
            OrderField.INST_DERIV_STRIKE_PRICE: [3, 1.3, 2, 2, 6],
            OrderField.TRX_DTL_PC_NOTATION: ["MONE", "BAPO", "TRYS", "MONE", "TRYS"],
            OrderField.TRX_DTL_QUANTITY_NOTATION: [
                "UNIT",
                "UNIT",
                "UNIT",
                "NOML",
                "MONE",
            ],
            OrderField.INST_CLASSIFICATION: [
                "VSLSGT",
                "PSSYTR",
                "OSLOPP",
                "PSLKIU",
                "OSLOPP",
            ],
        }
    )
    return df


@pytest.fixture
def source_dataframe_order_values_without_price_multiplier() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            OrderField.PC_FD_PRICE: [10, 200000, 30, 5, 2],
            OrderField.PC_FD_TRD_QTY: [1, 2, 3, 2, 4],
            OrderField.INST_DERIV_STRIKE_PRICE: [3, 1.3, 2, 2, 6],
            OrderField.TRX_DTL_PC_NOTATION: ["MONE", "BAPO", "TRYS", "MONE", "TRYS"],
            OrderField.TS_TRADING_DATE_TIME: [
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
                pd.Timestamp(2022, 5, 4, 0, 0, 0),
            ],
            OrderField.TRX_DTL_QUANTITY_NOTATION: [
                "UNIT",
                "UNIT",
                "UNIT",
                "NOML",
                "MONE",
            ],
            OrderField.INST_CLASSIFICATION: [
                "VSLSGT",
                "PSSYTR",
                "OSLOPP",
                "PSLKIU",
                "OSLOPP",
            ],
        }
    )
    return df


class TestData:
    def test_process_numeric_columns_with_nan_dropping_nan(self, source_dataframe):
        source_dataframe[OrderField.PC_FD_INIT_QTY] = [12.7, pd.NA, "12.4", "34", 5]
        outcome_df = process_numeric_columns(data=source_dataframe, dropnan=True)

        outcome_df = outcome_df.reset_index().drop(columns="index")

        expected_df = pd.DataFrame(
            {
                OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE: [6.0, 46.0, 87.0],
                OrderField.PC_FD_INIT_QTY: [12.7, 12.4, 34.0],
                OrderField.PC_FD_PRICE: [2.0, 23.0, 45.0],
            }
        )
        assert outcome_df.equals(expected_df)

    def test_process_numeric_columns_with_nan_all_types_of_not_dropping_nans(
        self, source_dataframe_more_nan
    ):
        source_dataframe_more_nan[OrderField.PC_FD_INIT_QTY] = [
            12.7,
            pd.NA,
            np.nan,
            "nan",
            "NA",
            "12.4",
            "34",
            5,
        ]
        outcome_df = process_numeric_columns(data=source_dataframe_more_nan)

        outcome_df = outcome_df.reset_index().drop(columns="index")

        expected_df = pd.DataFrame(
            {
                OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE: [
                    6,
                    32,
                    46,
                    87,
                    67,
                    34,
                    35,
                    36,
                ],
                OrderField.PC_FD_INIT_QTY: [
                    12.7,
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                    12.4,
                    34,
                    5,
                ],
                OrderField.PC_FD_PRICE: [2, 56, 23, 45, np.nan, 37, 38, 39],
            }
        )
        assert all(outcome_df == expected_df)

    def test_remove_rows_with_invalid_id(self, source_dataframe):
        """test to verify the rows with a specific value from a specific column
        are removed.

        :param source_dataframe: dataframe to be filtered
        """
        filtered_df: pd.DataFrame = remove_rows_with_value_in_column(
            data=source_dataframe, column=OrderField.PC_FD_INIT_QTY, value="off"
        )
        assert filtered_df.shape[0] == 4

    def test_remove_rows_with_invalid_id_no_column(self, source_dataframe):
        """test to verify the original dataframe is returned when the column is
        not available in the source dataframe.

        :param source_dataframe: dataframe to be filtered
        """
        filtered_df: pd.DataFrame = remove_rows_with_value_in_column(
            data=source_dataframe, column="foo", value="off"
        )
        assert filtered_df.equals(source_dataframe)

    def test_order_states_values(self, source_dataframe_order_values):
        outcome_df = calculate_order_states_values(order_states_df=source_dataframe_order_values)

        # test

        expected_df = pd.DataFrame(
            {
                OrderField.PC_FD_PRICE: [10, 20, 30, 5, 2],
                OrderField.PC_FD_TRD_QTY: [1, 2, 3, 2, 4],
                OrderField.INST_DERIV_PRICE_MULTIPLIER: [-0.4, 1.5, 1, 1, 3],
                OrderField.INST_DERIV_STRIKE_PRICE: [3, 1.3, 2, 2, 6],
                OrderField.TRX_DTL_PC_NOTATION: [
                    "MONE",
                    "BAPO",
                    "PERC",
                    "MONE",
                    "TRYS",
                ],
                OrderField.TS_TRADING_DATE_TIME: [
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                ],
                OrderField.TRX_DTL_QUANTITY_NOTATION: [
                    "UNIT",
                    "UNIT",
                    "UNIT",
                    "NOML",
                    "MONE",
                ],
                OrderField.INST_CLASSIFICATION: [
                    "VSLSGT",
                    "PSSYTR",
                    "OSLOPP",
                    "PSLKIU",
                    "OSLOPP",
                ],
                OrderStateValueColumns.ORDER_STATE_VALUE: [-4.0, 0.004, 0.006, 2, 36],
                PNLColumns.ORDER_STATE_TRADE_DATE: [
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                ],
            }
        )

        assert all(outcome_df == expected_df)

    def test_order_states_values_missing_col(self, source_dataframe_order_values):
        source_dataframe_order_values.drop(columns=OrderField.PC_FD_TRD_QTY, inplace=True)

        outcome_df = calculate_order_states_values(
            order_states_df=source_dataframe_order_values, method_id="uuid"
        )
        expected_df = pd.DataFrame(
            {
                OrderField.PC_FD_PRICE: [10, 20, 30, 5, 2],
                OrderField.INST_DERIV_PRICE_MULTIPLIER: [-0.4, 1.5, 1, 1, 3],
                OrderField.INST_DERIV_STRIKE_PRICE: [3, 1.3, 2, 2, 6],
                OrderField.TRX_DTL_PC_NOTATION: [
                    "MONE",
                    "BAPO",
                    "TRYS",
                    "MONE",
                    "TRYS",
                ],
                OrderField.TS_TRADING_DATE_TIME: [
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                ],
                OrderField.TRX_DTL_QUANTITY_NOTATION: [
                    "UNIT",
                    "UNIT",
                    "UNIT",
                    "NOML",
                    "MONE",
                ],
                OrderField.INST_CLASSIFICATION: [
                    "VSLSGT",
                    "PSSYTR",
                    "OSLOPP",
                    "PSLKIU",
                    "OSLOPP",
                ],
                OrderStateValueColumns.ORDER_STATE_VALUE: [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
            }
        )
        assert all(outcome_df == expected_df)

    def test_order_states_missing_price_multiplier(
        self, source_dataframe_order_values_without_price_multiplier
    ):
        """Test for when there is no price multiplier column.

        :param source_dataframe_order_values_without_price_multiplier: order states without price multiplier
        """
        outcome_df = calculate_order_states_values(
            order_states_df=source_dataframe_order_values_without_price_multiplier
        )

        expected_df = pd.DataFrame(
            {
                OrderField.PC_FD_PRICE: [10, 20, 30, 5, 2],
                OrderField.PC_FD_TRD_QTY: [1, 2, 3, 2, 4],
                OrderField.INST_DERIV_PRICE_MULTIPLIER: [1, 1, 1, 1, 1],
                OrderField.INST_DERIV_STRIKE_PRICE: [3, 2, 2, 2, 6],
                OrderField.TRX_DTL_PC_NOTATION: [
                    "MONE",
                    "BAPO",
                    "TRYS",
                    "MONE",
                    "TRYS",
                ],
                OrderField.TS_TRADING_DATE_TIME: [
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                    pd.Timestamp(2022, 5, 4, 0, 0, 0),
                ],
                OrderField.TRX_DTL_QUANTITY_NOTATION: [
                    "UNIT",
                    "UNIT",
                    "UNIT",
                    "NOML",
                    "MONE",
                ],
                OrderField.INST_CLASSIFICATION: [
                    "VSLSGT",
                    "PSSYTR",
                    "OSLOPP",
                    "PSLKIU",
                    "OSLOPP",
                ],
                OrderStateValueColumns.ORDER_STATE_VALUE: [10, 40, 6.0, 2, 24],
                PNLColumns.ORDER_STATE_TRADE_DATE: [
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                    datetime.date(2022, 5, 4),
                ],
            }
        )

        assert all(outcome_df.sort_index(axis=1) == expected_df.sort_index(axis=1))

    def test_missing_traded_quantity(self, source_dataframe_order_values_missing_traded_quantity):
        """test the method returns the original dataframe when traded quantity
        does not exist.

        :param source_dataframe_order_values_missing_traded_quantity: dataframe without traded quantity
        """
        outcome_df = calculate_order_states_values(
            order_states_df=source_dataframe_order_values_missing_traded_quantity,
            method_id="uuid",
        )

        assert outcome_df.equals(source_dataframe_order_values_missing_traded_quantity)

    def test_process_numeric_columns_with_nan(self, source_dataframe):
        source_dataframe[OrderField.PC_FD_INIT_QTY] = [12.7, pd.NA, "12.4", "34", 5]
        outcome_df = process_numeric_columns(data=source_dataframe)

        outcome_df = outcome_df.reset_index().drop(columns="index")

        expected_df = pd.DataFrame(
            {
                OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE: [6, 32, 46, 87, 67],
                OrderField.PC_FD_INIT_QTY: [12.7, np.nan, 12.4, 34, 5],
                OrderField.PC_FD_PRICE: [2, 56, 23, 45, np.nan],
            }
        )
        assert all(outcome_df == expected_df)


class TestVenueName:
    @pytest.fixture
    def mock_dataframe_no_venue(self) -> pd.DataFrame:
        """
        dataframe with no venue from transaction details but other field called venue
        :return:dataframe with main column missing
        """
        df = pd.DataFrame({AlgoColumnsEnum.VENUE: ["XLON", "XLON", "XOFF"]})
        return df

    def test_case_without_transaction_details_venue(self, mock_dataframe_no_venue):
        """Test to verify if it works with alternative column.

        :param mock_dataframe_no_venue: mock dataframe without main venue column
        """
        venue_names: Union[pd.Series, None] = get_venue_name(data=mock_dataframe_no_venue)
        assert venue_names == ["XLON", "XOFF"]


class TestGetIDCode:
    @pytest.fixture
    def mock_dataframe_no_id_code(self) -> pd.DataFrame:
        """
        dataframe with no instrument id code but with new columns instrument id
        :return:dataframe without main instrument code column
        """
        df = pd.DataFrame({NewColumns.INSTRUMENT_CODE: ["IJDISDJ", "IJDISDJ", "FMWOFMQW"]})
        return df

    def test_no_instrument_id_code(self, mock_dataframe_no_id_code):
        """Verify it gets the NewColumns instrument ID column when the
        instrument id code is not available.

        :param mock_dataframe_no_id_code: dataframe with column or columns for id codes
        """
        unique_id_codes = get_instrument_id_code(data=mock_dataframe_no_id_code)

        assert isinstance(unique_id_codes, list)
        assert len(unique_id_codes) == 2


class TestGetClientName:
    @pytest.fixture
    def mock_dataframe_no_client_name(self) -> pd.DataFrame:
        """
        dataframe with no client name code but with new column client name
        :return: dataframe without main client column
        """
        df = pd.DataFrame({NewColumns.CLIENT: ["foo", "bar", "foo"]})
        return df

    def test_no_client_name_with_new_columns(self, mock_dataframe_no_client_name):
        """Verify it gets the NewColumns client name column when the client
        name is not available.

        :param mock_dataframe_no_client_name: dataframe with column or columns for client name
        """
        unique_client_name = get_client_name(data=mock_dataframe_no_client_name)

        assert isinstance(unique_client_name, list)
        assert len(unique_client_name) == 2


class TestVenueFallback:
    @pytest.fixture
    def no_venue_columns_list(self) -> List[str]:
        """list of invalid venue columns."""
        no_venue_list = ["foo", "bar"]
        return no_venue_list

    def test_no_venue_column(self, no_venue_columns_list):
        """tests case where there is no valid venue columns.

        :param no_venue_columns_list: list of invalid venue columns
        :return: None
        """
        with pytest.raises(ValueError):
            get_venue_fallback(no_venue_columns_list)


class TestSortByDate:
    @pytest.fixture
    def mock_dataframe(self) -> pd.DataFrame:
        """
        dataframe with random column
        :return:  test dataframe with missing column
        """
        df = pd.DataFrame({NewColumns.CLIENT: ["foo", "bar", "foo"]})
        return df

    def test_incorrect_column_name(self, mock_dataframe):
        """test to verify if when the column input does not exist in the df
        raises an error.

        :param mock_dataframe: test dataframe with missing column
        """
        with pytest.raises(ValueError):
            sort_by_date(data=mock_dataframe, date_column=OrderField.TS_ORD_SUBMITTED)


class TestGetListInstrumentUniqueIdentifier:
    @pytest.fixture
    def mock_dataframe(self) -> pd.DataFrame:
        """
        dataframe with random column
        :return: dataframe with missing column
        """
        df = pd.DataFrame({NewColumns.CLIENT: ["foo", "bar", "foo"]})
        return df

    def test_incorrect_column_name(self, mock_dataframe):
        """tests for raising error when no column exists for instrument unique
        identifier.

        :param mock_dataframe: mock dataframe
        """
        with pytest.raises(ValueError):
            get_list_instrument_unique_identifier(
                data=mock_dataframe, venue_column=OrderField.TRX_DTL_VENUE
            )


class TestCalculatePNL:
    @pytest.fixture
    def mock_event_day_close_price(self) -> float:
        return 10.0

    @pytest.fixture
    def mock_event_day_timestamp(self) -> pd.Timestamp:
        """mock example of timestamp for an event day."""
        return pd.Timestamp(2022, 6, 6)

    @pytest.fixture
    def mock_dataframe_without_required_column(self) -> pd.DataFrame:
        """
        dataframe without one required column
        :return: order states dataframe with missing column
        """
        df = pd.DataFrame(
            {
                OrderField.PC_FD_TRD_QTY: [500, 100, 200],
                OrderField.PC_FD_PRICE: [5.3, 10.2, 2.1],
            }
        )
        return df

    @pytest.fixture
    def mock_order_states_dataframe(self) -> pd.DataFrame:
        """
        dataframe without one required column
        :return: mock order states dataframe
        """
        df = pd.DataFrame(
            {
                OrderField.PC_FD_TRD_QTY: [500, 100, 200],
                OrderField.PC_FD_PRICE: [5.3, 9.5, 2.1],
                OrderField.TRX_DTL_PC_NOTATION: [
                    PriceNotation.MONE,
                    PriceNotation.MONE,
                    PriceNotation.PERC,
                ],
                PNLColumns.ORDER_STATE_TRADE_DATE: [
                    datetime.date(2022, 6, 3),
                    datetime.date(2022, 6, 4),
                    datetime.date(2022, 6, 5),
                ],
                OrderField.EXC_DTL_BUY_SELL_IND: [
                    BuySell.BUY,
                    BuySell.BUY,
                    BuySell.BUY,
                ],
            }
        )
        return df

    @pytest.fixture
    def mock_market_data_dataframe(self) -> pd.DataFrame:
        """mock market data dataframe."""
        df = pd.DataFrame(
            {
                EoDStatsColumns.DATE: [
                    pd.Timestamp(2022, 6, 3),
                    pd.Timestamp(2022, 6, 4),
                    pd.Timestamp(2022, 6, 5),
                    pd.Timestamp(2022, 6, 6),
                ],
                EoDStatsColumns.CLOSE_PRICE: [5.2, 10.3, 2.0, 15],
            }
        )
        return df

    def test_without_required_column(
        self,
        mock_dataframe_without_required_column,
        mock_event_day_close_price,
    ):
        """Test for when there is a missing mandatory column.

        :param mock_dataframe_without_required_column: mock order state dataframe with missing column
        :param mock_market_data_dataframe: mock market data dataframe
        """

        result_df = calculate_pnl_order_states(
            order_states_df=mock_dataframe_without_required_column,
            event_day_close_price=mock_event_day_close_price,
            price_column=OrderField.PC_FD_PRICE,
            method_id="test",
        )
        assert result_df.equals(mock_dataframe_without_required_column)

    def test_normal_run(
        self,
        mock_order_states_dataframe,
        mock_event_day_close_price,
    ):
        """test to verify columns are being calculated as expected.

        :param mock_order_states_dataframe: mock order states dataframe
        """
        order_states = calculate_pnl_order_states(
            order_states_df=mock_order_states_dataframe,
            event_day_close_price=mock_event_day_close_price,
            price_column=OrderField.PC_FD_PRICE,
            method_id="fake_method_id",
        )
        assert PNLColumns.PNL_VALUE_BY_RECORD in order_states.columns

        assert order_states.loc[:, PNLColumns.PNL_VALUE_BY_RECORD][0] == 2350.0
        assert order_states.loc[:, PNLColumns.PNL_VALUE_BY_RECORD][1] == 50.0
        assert order_states.loc[:, PNLColumns.PNL_VALUE_BY_RECORD][2] == 15.8


class TestFilterByTimestamps:
    @pytest.fixture
    def mock_dataframe(self) -> pd.DataFrame:
        """dataframe with timestamp column."""
        df = pd.DataFrame(
            {
                "random_colum": [0, 1, 2],
                "timestamp_column": [
                    pd.Timestamp(2022, 6, 4),
                    pd.Timestamp(2022, 6, 5),
                    pd.Timestamp(2022, 6, 6),
                ],
            }
        )
        return df

    def test_filter_timestamps(self, mock_dataframe):
        """test to filter df by timestamp.

        :param mock_dataframe: dataframe with timestamp column
        """
        df = filter_df_by_timestamps(
            data=mock_dataframe,
            timestamp_column="timestamp_column",
            date_range={
                DateRangeParameters.START: pd.Timestamp(2022, 6, 4),
                DateRangeParameters.END: pd.Timestamp(2022, 6, 5),
            },
        )

        assert df.shape[0] == 2

        assert df.loc[:, "timestamp_column"].tolist() == [
            pd.Timestamp(2022, 6, 4),
            pd.Timestamp(2022, 6, 5),
        ]


class TestCalculateConfidenceInterval:
    @pytest.fixture
    def mock_values_series(self) -> pd.Series:
        """mock series with sample of numbers."""
        series = pd.Series([4, 3, 4, 5, 6, 7, 6, 5, 4, 5, 6, 7, 8, 5, 3, 4])
        return series

    def test_confidence_interval(self, mock_values_series):
        # with a higher alpha of 99%, the value 6 is inside confidence interval, so not significantly different from the sample
        # calculated with scipy method
        confidence_interval = calculate_confidence_interval(
            value_series=mock_values_series, alpha=0.99
        )

        m = mock_values_series.mean()
        s = mock_values_series.std()
        dof = len(mock_values_series) - 1
        confidence = 0.99

        t_crit = np.abs(t.ppf((1 - confidence) / 2, dof))

        # expected confidence interval calculated formula manually
        expected_confidence_interval = {
            "start": m - s * t_crit / np.sqrt(len(mock_values_series)),
            "end": m + s * t_crit / np.sqrt(len(mock_values_series)),
        }

        assert confidence_interval == expected_confidence_interval
        assert confidence_interval.get(DateRangeParameters.START) < 6
        assert confidence_interval.get(DateRangeParameters.END) > 6

        # with a lower alpha of 90%, the value 6 is above the upper limite of the confidence limite, so it is significantly different from the sample
        confidence_interval_1 = calculate_confidence_interval(
            value_series=mock_values_series, alpha=0.9
        )
        confidence = 0.9

        t_crit = np.abs(t.ppf((1 - confidence) / 2, dof))

        # expected confidence interval calculated formula manually
        expected_confidence_interval_1 = {
            "start": m - s * t_crit / np.sqrt(len(mock_values_series)),
            "end": m + s * t_crit / np.sqrt(len(mock_values_series)),
        }

        assert confidence_interval_1 == expected_confidence_interval_1

        assert confidence_interval_1.get(DateRangeParameters.START) < 6
        assert confidence_interval_1.get(DateRangeParameters.END) > 5


class TestFilterExtract:
    @pytest.fixture
    def mock_filters(self) -> Dict[str, str]:
        """
        example of filters
        :return:
        """
        filters = {
            "bool": {
                "must": {
                    "script": {
                        "script": {
                            "lang": "painless",
                            "params": {"end": 1653523825000, "start": 1653264625000},
                            "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                        }
                    }
                }
            }
        }
        return filters

    @pytest.fixture
    def mock_filters_must_list(self) -> Dict[str, str]:
        """
        example of filters
        :return:
        """
        filters = {
            "bool": {
                "must": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://benjamin.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6.csv"
                            ]
                        }
                    },
                    {
                        "script": {
                            "script": {
                                "lang": "painless",
                                "params": {
                                    "end": 1655424000000,
                                    "start": 1655251200000,
                                },
                                "inline": "def model = doc['&model']; def rawDt = model == 'OrderState' ? doc['timestamps.tradingDateTime'] : doc['timestamps.orderSubmitted']; rawDt >= params.start && rawDt <= params.end",
                            }
                        }
                    },
                ]
            }
        }
        return filters

    @pytest.fixture
    def mock_filters_must_date_range(self) -> Dict[str, str]:
        """
        example of filters
        :return:
        """
        filters = {
            "bool": {
                "must": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://benjamin.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/Insider_Trading_V3_Split_UC6.csv"
                            ]
                        }
                    },
                    {"range": {"&timestamp": {"gte": 1655251200000, "lte": 1655424000000}}},
                    {
                        "range": {
                            "timestamps.orderReceived": {
                                "gte": 1655251200000,
                                "lte": 1655424000000,
                            }
                        }
                    },
                    {
                        "range": {
                            "timestamps.orderSubmitted": {
                                "gte": 1655251200000,
                                "lte": 1655424000000,
                            }
                        }
                    },
                ]
            }
        }

        return filters

    def test_epoch_extractor(self, mock_filters):
        date_range = epoch_filter_extract(filters=mock_filters)

        assert date_range.get(DateRangeParameters.START) == pd.Timestamp(2022, 5, 23, 0, 10, 25)
        assert date_range.get(DateRangeParameters.END) == pd.Timestamp(2022, 5, 26, 0, 10, 25)

    def test_epoch_extractor_must_list(self, mock_filters_must_list):
        date_range = epoch_filter_extract(filters=mock_filters_must_list)

        assert date_range.get(DateRangeParameters.START) == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        assert date_range.get(DateRangeParameters.END) == pd.Timestamp(2022, 6, 17, 0, 0, 0)

    def test_epoch_extractor_must_list_range(self, mock_filters_must_date_range):
        date_range = epoch_filter_extract(filters=mock_filters_must_date_range)

        assert date_range.get(DateRangeParameters.START) == pd.Timestamp(2022, 6, 15, 0, 0, 0)
        assert date_range.get(DateRangeParameters.END) == pd.Timestamp(2022, 6, 17, 0, 0, 0)

    def test_trader_info_none(self):
        trader_list = [{"types": "TRADER", "value": None}]

        outcome_trader = get_trader_info(
            trader_list=trader_list, tag="&id", top_level_column="__trader__"
        )

        assert pd.isna(outcome_trader)

    def test_trader_info_id(self):
        trader_list = [
            {
                "types": ["TRADER"],
                "value": {
                    "&id": "773ee288-8bb1-5d12-b83b-9ff95434aad5",
                    "communications": {"emails": ["<EMAIL>"]},
                    "name": "Stefano Bleve",
                    "personalDetails": {"firstName": "Stefano", "lastName": "Bleve"},
                    "retailOrProfessional": "N/A",
                    "location": {
                        "officeAddress": {
                            "country": "CH",
                            "address": "Rue du Rhône 62",
                            "city": "Geneva",
                            "postalCode": "1204",
                        }
                    },
                    "&key": "AccountPerson:773ee288-8bb1-5d12-b83b-9ff95434aad5:*************",
                    "sinkIdentifiers": {
                        "tradeFileIdentifiers": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                        "orderFileIdentifiers": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                    },
                    "officialIdentifiers": {
                        "traderIds": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                        "branchCountry": "CH",
                    },
                    "uniqueIds": ["<EMAIL>", "finnova id:bleve stefano"],
                },
            }
        ]

        outcome_trader_id = get_trader_info(
            trader_list=trader_list, tag="&id", top_level_column="__trader__"
        )

        expected_id = "773ee288-8bb1-5d12-b83b-9ff95434aad5"
        assert outcome_trader_id == expected_id

    def test_trader_info_name(self):
        trader_list = [
            {
                "types": ["TRADER"],
                "value": {
                    "&id": "773ee288-8bb1-5d12-b83b-9ff95434aad5",
                    "communications": {"emails": ["<EMAIL>"]},
                    "name": "Stefano Bleve",
                    "personalDetails": {"firstName": "Stefano", "lastName": "Bleve"},
                    "retailOrProfessional": "N/A",
                    "location": {
                        "officeAddress": {
                            "country": "CH",
                            "address": "Rue du Rhône 62",
                            "city": "Geneva",
                            "postalCode": "1204",
                        }
                    },
                    "&key": "AccountPerson:773ee288-8bb1-5d12-b83b-9ff95434aad5:*************",
                    "sinkIdentifiers": {
                        "tradeFileIdentifiers": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                        "orderFileIdentifiers": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                    },
                    "officialIdentifiers": {
                        "traderIds": [{"id": "Bleve Stefano", "label": "Finnova ID"}],
                        "branchCountry": "CH",
                    },
                    "uniqueIds": ["<EMAIL>", "finnova id:bleve stefano"],
                },
            }
        ]

        outcome_trader_id = get_trader_info(
            trader_list=trader_list, tag="name", top_level_column="__trader__"
        )

        expected_id = "Stefano Bleve"
        assert outcome_trader_id == expected_id

    def test_create_time_window_group(self):
        data = pd.read_csv(
            TEST_DATA.joinpath("create_time_window.csv"),
        )

        groups = create_time_window_groups(
            data=data,
            time_column=OrderField.TS_ORD_SUBMITTED,
            time_threshold=300,
            time_delta_column="time_delta",
        )

        assert len(groups) == 1

        groups = create_time_window_groups(
            data=data,
            time_column=OrderField.TS_ORD_SUBMITTED,
            time_threshold=30,
            time_delta_column="time_delta",
        )

        assert len(groups) == 2
