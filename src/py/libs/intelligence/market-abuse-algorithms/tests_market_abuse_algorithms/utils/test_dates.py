import pandas as pd
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.utils.dates import (
    determine_observation_period,
    get_order_state_date_range,
)


def test_determine_observation_period_with_intraday():
    """Test intra day surveillance determined the observation period as being
    the same exact day as the event day."""
    event_day = pd.Timestamp("2024-07-17 00:00:00")
    intraday_surveillance = False
    observation_period = 1

    expected_output = {
        DateRangeParameters.START: pd.Timestamp("2024-07-16 00:00:00"),
        DateRangeParameters.END: pd.Timestamp("2024-07-16 23:59:59"),
    }

    output = determine_observation_period(
        event_day=event_day,
        intraday_surveillance=intraday_surveillance,
        observation_period=observation_period,
    )

    assert expected_output == output

    # Test with same values but with intraday threshold activated
    intraday_surveillance = True

    expected_output_intra_day = {
        DateRangeParameters.START: pd.Timestamp("2024-07-17 00:00:00"),
        DateRangeParameters.END: pd.Timestamp("2024-07-17 23:59:59"),
    }

    output_intra_day = determine_observation_period(
        event_day=event_day,
        intraday_surveillance=intraday_surveillance,
        observation_period=observation_period,
    )

    assert expected_output_intra_day == output_intra_day


def test_get_order_state_date_range_with_intraday():
    event_day = pd.Timestamp("2024-07-17 00:00:00")
    intraday_surveillance = False
    observation_period = 1
    behaviour_period = 1

    expected_output = {
        DateRangeParameters.START: pd.Timestamp("2024-07-15 00:00:00"),
        DateRangeParameters.END: pd.Timestamp("2024-07-16 23:59:59"),
    }

    output = get_order_state_date_range(
        event_day=event_day,
        intraday_surveillance=intraday_surveillance,
        observation_period=observation_period,
        behaviour_period=behaviour_period,
    )

    assert expected_output == output

    # Test with same values but with intraday threshold activated
    intraday_surveillance = True

    expected_output_intra_day = {
        DateRangeParameters.START: pd.Timestamp("2024-07-16 00:00:00"),
        DateRangeParameters.END: pd.Timestamp("2024-07-17 23:59:59"),
    }

    intraday_output = get_order_state_date_range(
        event_day=event_day,
        intraday_surveillance=intraday_surveillance,
        observation_period=observation_period,
        behaviour_period=behaviour_period,
    )

    assert expected_output_intra_day == intraday_output
