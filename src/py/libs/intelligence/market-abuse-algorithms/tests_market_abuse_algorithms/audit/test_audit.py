import pytest
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.repository.sdp.es_sdp import SDP
from market_abuse_algorithms.exceptions import FilterDataError
from se_elastic_schema.static.mar import SkipReason
from unittest.mock import MagicMock, PropertyMock

REPORT_TYPE = "TEST"
REQUESTED_BY = "Programmatic"
THRESHOLDS = {"foo": "bar"}
FILTERS = {"foo": "bar"}
REALM = "mar.uat.steeleye.co"
TENANT = "mar"


class TestAudit:
    def make_mock_context(self, filters):
        mock_context = MagicMock()
        type(mock_context).requested_by = PropertyMock(return_value="john doe")
        type(mock_context).thresholds = "foo"
        type(mock_context).filters = PropertyMock(return_value=filters)
        return mock_context

    def test_given_eu_421_payload_remove_must_not_records_works_with_dicts(self):
        """Regression test for error reported in API-46."""
        mock_context = self.make_mock_context(
            {
                "bool": {
                    "must": {"terms": {"sourceKey": ["s3://blahblah"]}},
                    "must_not": {"terms": {"instrumentDetails": ["Equity"], "&id": ["FOO:BAR"]}},
                }
            }
        )
        es_client = SDP(tenant=TENANT)

        assert es_client.tenant == "mar"
        audit = Audit(context=mock_context, strategy_name="Quote Stuffing", es_client=es_client)
        audit._remove_must_not_records_from_filters()
        expected = "{'bool': {'must': {'terms': {'sourceKey': ['s3://blahblah']}}, 'must_not': []}}"  # noqa: E501
        assert audit._record.filters == expected

    def test_given_eu_421_payload_remove_must_not_records_works_with_lists(self):
        """Regression test for error reported in API-46."""
        mock_context = self.make_mock_context(
            {
                "bool": {
                    "must": {"terms": {"sourceKey": ["s3://blahblah"]}},
                    "must_not": [
                        {
                            "terms": {
                                "instrumentDetails": ["Equity"],
                                "&id": ["FOO:BAR"],
                            }
                        },
                        {"terms": {"foo.bar": ["123"]}},
                    ],
                }
            }
        )
        es_client = SDP(tenant=TENANT)

        assert es_client.tenant == "mar"

        audit = Audit(context=mock_context, strategy_name="Quote Stuffing", es_client=es_client)
        audit._remove_must_not_records_from_filters()

        expected = "{'bool': {'must': {'terms': {'sourceKey': ['s3://blahblah']}}, 'must_not': [{'terms': {'foo.bar': ['123']}}]}}"  # noqa: E501
        assert audit._record.filters == expected

    def test_given_api_46_payload_remove_must_not_records_from_filters_handles_error(
        self,
    ):
        """Regression test for error reported in API-46."""
        mock_context = MagicMock()
        type(mock_context).requested_by = PropertyMock(return_value="john doe")
        type(mock_context).thresholds = "foo"
        type(mock_context).filters = PropertyMock(
            return_value={
                "bool": {
                    "must_not": {
                        "terms": [
                            {"instrumentDetails.instrument.ext.bestExAssetClassMain": ["Equity"]}
                        ]
                    }
                }
            }
        )
        es_client = SDP(tenant=TENANT)

        assert es_client.tenant == "mar"

        audit = Audit(context=mock_context, strategy_name="Quote Stuffing", es_client=es_client)
        with pytest.raises(FilterDataError):
            audit._remove_must_not_records_from_filters()

    def test_update_skipped_records(self):
        mock_context = MagicMock()
        type(mock_context).requested_by = PropertyMock(return_value="john doe")
        type(mock_context).thresholds = "foo"
        type(mock_context).filters = PropertyMock(
            return_value={
                "bool": {
                    "must_not": {
                        "terms": {
                            "instrumentDetails.instrument.ext.bestExAssetClassMain": ["Equity"]
                        }
                    }
                }
            }
        )
        es_client = SDP(tenant=TENANT)

        assert es_client.tenant == "mar"

        audit = Audit(context=mock_context, strategy_name="Quote Stuffing", es_client=es_client)

        keys = [
            "Order:1:NEWO:SLOEV_1_3GB00BL68HJ26:1619708644610",
            "Order:1:NEWO:SLOEV_1_2US0378331005:1619708642669",
        ]

        audit.update_skipped_records(
            keys=keys, classification=SkipReason.INSTRUMENT, message="test"
        )
