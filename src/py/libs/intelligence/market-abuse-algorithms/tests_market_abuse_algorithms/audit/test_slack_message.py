import addict
import os
import pytest
from market_abuse_algorithms.audit.models import SlackMessage
from market_abuse_algorithms.audit.slack import SlackReport

REALM = "mar.uat.steeleye.co"


class TestSlackReport:
    token = os.environ.get(
        "MAR_WATCHER_SLACK_TOKEN",
        "********************************************************",
    )

    @staticmethod
    def send_slack_message(token: str, channel: str):
        message = addict.Dict(
            tenant=REALM.split(".steeleye.co")[0],
            report_type="test",
            id="test111",
            key="test",
            thresholds="{test: test}",
            filters="{}",
        )

        slack_report = SlackReport(
            message=SlackMessage(**message),
            username="Market Abuse Algorithms",
            icon=":red_circle:",
        )

        text_to_send = slack_report.message.convert_to_string()

        send_message = dict(
            token=token,
            channel=channel,
            text=text_to_send,
            icon_emoji=":red_circle:",
            username="Market Abuse Algorithms",
        )

        result = slack_report.send_raw_message(message=send_message)

        return result

    @pytest.mark.usefixtures("skip_test_in_ci")
    def test_valid_message(self):
        result = self.send_slack_message(token=self.token, channel="#mar-dev-error-watcher")

        assert result.get("ok") is True

    def test_invalid_channel(self):
        result = self.send_slack_message(token=self.token, channel="mar-test-watcher")

        assert result.get("ok") is False
        assert result.get("error") == "channel_not_found"
