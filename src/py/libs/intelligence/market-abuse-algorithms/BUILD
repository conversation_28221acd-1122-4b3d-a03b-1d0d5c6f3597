python_sources(
    name="task",
    sources=["market_abuse_algorithms/**/*.py"],
    dependencies=[
        "//:3rdparty#s3fs",  # s3fs is used to upload ndjson files
        "//:3rdparty#adlfs",  # adlfs is used to upload ndjson files
        "//:3rdparty#scipy",
    ],
)

resources(
    name="test_resources",
    sources=[
        "tests_market_abuse_algorithms/**/*.json",
        "tests_market_abuse_algorithms/**/*.csv",
        "tests_market_abuse_algorithms/**/*.parquet",
        "tests_market_abuse_algorithms/**/*.ndjson",
        "tests_market_abuse_algorithms/**/*.txt",
    ],
)

python_test_utils(
    name="test_utils",
    sources=[
        "tests_market_abuse_algorithms/**/conftest.py",
        "tests_market_abuse_algorithms/**/mock*.py",
        "tests_market_abuse_algorithms/**/threshold*py",
        "tests_market_abuse_algorithms/**/filters*py",
        "tests_market_abuse_algorithms/**/fake*py",
        "tests_market_abuse_algorithms/**/static*py",
    ],
)

python_tests(
    name="tests",
    sources=["tests_market_abuse_algorithms/**/test_*.py"],
    dependencies=[":test_resources", ":test_utils"],
)
