# ruff: noqa: E501
# type: ignore
import logging
import pandas as pd
from insider_trading_v3_refinitiv_apply_strategy.utils.query_utils import (
    select_instrument_column,
)
from mar_utils.auditor.strategy_audits.insider_trading_v3_refinitiv_audit import (
    InsiderTradingV3RefinitivAuditName,
)
from market_abuse_algorithms.data_source.query.sdp.base import SDPBaseQuery
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.data_source.static.utility import (
    EVALUATION_TYPE_FALL_BACK,
    EVALUATION_TYPE_FIELD_MAPPING,
    ORDER_STATE_CALCULATION_TYPES_MAP,
    DFColumns,
    InstrumentRegexPatterns,
)
from market_abuse_algorithms.mar_audit.mar_audit import StepAudit
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.utils.filters import filter_regex_pattern_on_column
from pandas._libs.tslibs.offsets import BDay
from se_market_data_utils.schema.parquet import EoDStatsColumns
from typing import Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)


def cds_order_check(orders_df):
    cds_orders_based_on_inst_classification = filter_regex_pattern_on_column(
        data=orders_df,
        regex_pattern=InstrumentRegexPatterns.CDS,
        column=OrderField.INST_CLASSIFICATION,
    )
    if cds_orders_based_on_inst_classification.empty:
        return False
    return True


def process_executions_results(
    raw_query_df: pd.DataFrame,
    initial_query: bool = False,
    method_id: Optional[str] = None,
) -> pd.DataFrame:
    """Processes query to obtain instrument of interest and filters by accepted
    instrument types with regex.

    :param method_id: uuid to identify the method on the auditN
    :param raw_query_df: raw query results to be processed
    :param initial_query: True if it is to process the initial query, False for order states query
    :return: processed dataframe
    """

    if raw_query_df.empty:
        if method_id is not None:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[],
                    list_of_instruments=[],
                    reason="No execution data was fetched from ES to be processed.",
                )
            )
        logger.info("No data was fetched to be processed", exc_info=True)
        return pd.DataFrame()

    if OrderField.INST_CLASSIFICATION not in raw_query_df.columns:
        logger.info("Missing instrument classification fields", exc_info=True)
        return pd.DataFrame()

    # Creating a Default INSTRUMENT column
    raw_query_df.loc[:, DFColumns.INSTRUMENT] = pd.NA

    cds_order_bool = cds_order_check(raw_query_df)
    cds_df = pd.DataFrame()
    if cds_order_bool:
        if OrderField.INST_EXT_UNIQUE_IDENT not in raw_query_df.columns:
            logger.info("Missing instrument unique identifier needed for CDS orders", exc_info=True)
        else:
            cds_mask = (
                raw_query_df.loc[:, OrderField.INST_CLASSIFICATION]
                .str.match(InstrumentRegexPatterns.CDS)
                .astype(bool)
            )
            cds_df = raw_query_df.loc[cds_mask, :]

            # CDS Always use INST_EXT_UNIQUE_IDENT as INSTRUMENT
            cds_df.loc[cds_mask, DFColumns.INSTRUMENT] = raw_query_df.loc[
                cds_mask, OrderField.INST_EXT_UNIQUE_IDENT
            ]

            # segregating CDS orders from the rest of the DF
            raw_query_df = raw_query_df.loc[~cds_mask, :]

    if (
        OrderField.INST_EXT_UNDER_INST_INST_CODE not in raw_query_df.columns
        and OrderField.INST_DERIV_UND_INSTS not in raw_query_df.columns
    ):
        filtered_instrument_df = process_non_derivative_results(
            data=raw_query_df, initial_query=initial_query
        )
    else:
        filtered_instrument_df = process_derivative_results(
            data=raw_query_df, initial_query=initial_query
        )

    if cds_order_bool:
        # Merging CDS instruments with non_derivative/derivative
        columns = filtered_instrument_df.columns
        filtered_instrument_df = pd.concat([filtered_instrument_df, cds_df], ignore_index=True)
        filtered_instrument_df = filtered_instrument_df[columns]

    # Dropping the Default INSTRUMENT added
    filtered_instrument_df = filtered_instrument_df.dropna(subset=[DFColumns.INSTRUMENT])
    if filtered_instrument_df.empty:
        logger.info(
            "No instrument data after process and filter applications",
            exc_info=True,
        )

    return filtered_instrument_df


def process_non_derivative_results(data: pd.DataFrame, initial_query: bool = False) -> pd.DataFrame:
    """processes non derivative records and filters by acceptable regex
    patterns.

    :param data: records dataframe to be processed
    :param initial_query: flag for initial query processing
    :return: processed dataframe
    """
    if OrderField.INST_ID_CODE not in data.columns:
        data[OrderField.INST_ID_CODE] = pd.NA

    columns_to_check = [OrderField.INST_ID_CODE, OrderField.INST_EXT_UNIQUE_IDENT]

    # Filter the columns that exist in the DataFrame
    instrument_field_ranking = [col for col in columns_to_check if col in data.columns]

    data.loc[:, DFColumns.INSTRUMENT] = (
        data.loc[:, instrument_field_ranking].bfill(axis=1).iloc[:, 0]
    )

    if initial_query:
        instrument_unique_df = (
            data.loc[
                :,
                [
                    DFColumns.INSTRUMENT,
                    OrderField.INST_ID_CODE,
                    OrderField.INST_CLASSIFICATION,
                    OrderField.INST_EXT_UNIQUE_IDENT,
                ],
            ]
        ).drop_duplicates()
    else:
        instrument_unique_df = data.drop_duplicates()

    instrument_unique_df = filter_regex_pattern_on_column(
        data=instrument_unique_df,
        regex_pattern=InstrumentRegexPatterns.NON_DERIVATIVES,
        column=OrderField.INST_CLASSIFICATION,
    )

    instrument_unique_df = instrument_unique_df.dropna(subset=[DFColumns.INSTRUMENT])
    return instrument_unique_df


def process_derivative_results(data: pd.DataFrame, initial_query: bool) -> pd.DataFrame:
    """processes derivative records and filters by acceptable regex patterns.

    :param data: records dataframe to be processed
    :param initial_query: flag for initial query processing
    :return: processed dataframe
    """
    if OrderField.INST_EXT_UNIQUE_IDENT not in data.columns:
        logger.info("Missing instrument unique identifier", exc_info=True)
        return pd.DataFrame()

    columns_to_check = [
        NewColumns.UNDERLYING_INSTRUMENT_CODE,
        OrderField.INST_ID_CODE,
        OrderField.INST_EXT_UNIQUE_IDENT,
    ]

    # Filter the columns that exist in the DataFrame
    instrument_field_ranking = [col for col in columns_to_check if col in data.columns]

    data.loc[:, DFColumns.INSTRUMENT] = (
        data.loc[:, instrument_field_ranking].bfill(axis=1).iloc[:, 0]
    )

    data = data.dropna(subset=[DFColumns.INSTRUMENT])

    necessary_columns = [
        DFColumns.INSTRUMENT,
        OrderField.INST_CLASSIFICATION,
        OrderField.INST_EXT_UNIQUE_IDENT,
        NewColumns.UNDERLYING_INSTRUMENT_CODE,
    ]

    if OrderField.INST_ID_CODE in data.columns:
        necessary_columns.append(OrderField.INST_ID_CODE)

    if initial_query:
        instrument_unique_df = (data.loc[:, necessary_columns]).drop_duplicates()

    else:
        if OrderField.INST_DERIV_UND_INSTS in data.columns:
            data = data.drop(OrderField.INST_DERIV_UND_INSTS, axis=1)
        if OrderField.INST_EXT_UNDER_INST in data.columns:
            data = data.drop(OrderField.INST_EXT_UNDER_INST, axis=1)
        instrument_unique_df = data.drop_duplicates()

    filtered_instrument_df = filter_regex_pattern_on_column(
        data=instrument_unique_df,
        regex_pattern=InstrumentRegexPatterns.POSSIBLE_INSTRUMENTS,
        column=OrderField.INST_CLASSIFICATION,
    )

    if filtered_instrument_df.empty:
        return pd.DataFrame()

    filtered_instrument_df[DFColumns.INSTRUMENT] = filtered_instrument_df.apply(
        select_instrument_column, axis=1
    )

    filtered_instrument_df = filtered_instrument_df.dropna(subset=[DFColumns.INSTRUMENT])
    return filtered_instrument_df


def get_close_price_to_analyse(data_to_fetch_from: pd.DataFrame):
    """Get the close price for a give dataset.

    :param data_to_fetch_from:
    :return:
    """
    return data_to_fetch_from.loc[:, EoDStatsColumns.CLOSE_PRICE].values[0]


def get_market_data_for_day(
    market_data: pd.DataFrame, day_of_event: pd.Timestamp, backward_looking: bool = False
):
    """Get market data for a specific day When market data for the day_of_event
    does not exist it is possible to extend the window for the previous 4
    bdays. Depending on the backward_looking, if TRUE will look into the next
    days while if FALSE will only look for day_of_event.

    :param market_data: pd.Dataframe, dataframe with the market data
    :param day_of_event: pd.Timestamp, timestamp of the event day
    :param backward_looking: bool, by default False. argumento to decide if the market data
    is searched in the past or not

    :return:
    """
    event_day_mask: pd.DataFrame = market_data.loc[:, EoDStatsColumns.DATE] == day_of_event.replace(
        tzinfo=None
    )

    event_day_data = market_data.loc[event_day_mask, :]

    if event_day_data.empty and not market_data.empty:
        if backward_looking:
            bday_range = pd.date_range(
                start=day_of_event.replace(tzinfo=None) - BDay(4),
                end=day_of_event.replace(tzinfo=None) - BDay(1),
                freq="B",
            ).tolist()

            mask = market_data.loc[:, EoDStatsColumns.DATE].isin(bday_range)

            if mask.any():
                logger.info(
                    f"Fallback to use market data from date {market_data.loc[mask, EoDStatsColumns.DATE].iloc[-1]}"
                )

                event_day_data = market_data.loc[mask, :].iloc[[-1], :]

    return event_day_data


def filter_instruments(list_of_instruments: List, data_to_be_analysed: pd.DataFrame) -> List:
    """filter instruments, based on the list of instruments that have events.

    :param list_of_instruments:
    :param data_to_be_analysed:
    :return:
    """

    event_instruments_mask = data_to_be_analysed[DFColumns.INSTRUMENT].isin(list_of_instruments)
    columns_to_check = [
        OrderField.INST_ID_CODE,
        NewColumns.UNDERLYING_INSTRUMENT_CODE,
        OrderField.INST_EXT_UNIQUE_IDENT,
    ]
    # Filter the columns that exist in the DataFrame
    instrument_columns = [col for col in columns_to_check if col in data_to_be_analysed.columns]
    filtered_instruments = data_to_be_analysed.loc[event_instruments_mask, instrument_columns]

    return list(
        set(
            instrument
            for instrument in filtered_instruments.values.flatten()
            if pd.notna(instrument)
        )
    )


def validate_event_day_close_price(
    event_day_close_price, instrument_id: str, event_day: pd.Timestamp
) -> Optional[float]:
    """verifies if event day close price is a valid value.

    :param event_day_close_price: event day close price
    :param instrument_id: instrument id of the close price
    :param event_day: event day Timestamp

    :return: float with close price or Na if invalid
    """
    if pd.isna(event_day_close_price) or pd.isnull(event_day_close_price):
        logger.info(
            f"No close price was determined for the instrument {instrument_id} for the event day {event_day}",
            exc_info=True,
        )
        return pd.NA

    return event_day_close_price


def exclude_orders(
    dataset_to_exclude: pd.DataFrame,
    execution_parents: pd.DataFrame,
    observation_period: Dict = None,
    method_id: Optional[str] = None,
    event_day: Optional[pd.Timestamp] = None,
) -> pd.DataFrame | Tuple[pd.DataFrame, str, Dict]:
    """
    Exclude orders based on parent id - ignore the Fills linked to Orders - NEWOs - placed outside
    of the Observation period.
    :param event_day: event day to log in StepAudit
    :param method_id: uuid to identify the method on the audit
    :param execution_parents:
    :param dataset_to_exclude:
    :param observation_period:
    :return: dataframe without the filtered order states
    """

    execution_parents_ids: List[str] = (
        dataset_to_exclude.loc[:, OrderField.META_PARENT].unique().tolist()
    )

    date_column = (
        OrderField.TS_ORD_SUBMITTED
        if OrderField.TS_ORD_SUBMITTED in execution_parents.columns
        else OrderField.TS_ORD_UPDATED
    )

    if date_column not in execution_parents.columns:
        audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_DATE_MISSING
        audit_data = dict(date_column=date_column, event_day=event_day)
        logger.info(
            f"The parent dataset for the meta parent ids {execution_parents_ids} "
            f"doesn't have the date column {date_column}. Event day {event_day}",
            exc_info=True,
        )
        if method_id:
            return pd.DataFrame()
        return pd.DataFrame(), audit_key, audit_data

    timestamp_column = pd.merge(
        dataset_to_exclude,
        execution_parents,
        left_on=OrderField.META_PARENT,
        right_on=OrderField.META_ID,
    )[date_column]

    dataset_to_exclude = dataset_to_exclude.reset_index().drop(columns=["index"])

    dataset_to_exclude[date_column] = timestamp_column
    order_states_filtered_mask = (
        dataset_to_exclude[date_column] >= observation_period.get(DateRangeParameters.START)
    ) & (dataset_to_exclude[date_column] <= observation_period.get(DateRangeParameters.END))
    order_states_df: pd.DataFrame = dataset_to_exclude.loc[order_states_filtered_mask]
    if order_states_df.empty:
        audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_OUTSIDE_OBSERVATION_PERIOD
        audit_data = dict(observation_period=observation_period, event_day=event_day)
        logger.warning(
            f"After excluding the order states based on NEWOs that happened outside the observation period"
            f"{observation_period}, there isn't any order state left to be evaluated. Event day {event_day}",
            exc_info=True,
        )
        if method_id:
            return pd.DataFrame()
        return pd.DataFrame(), audit_key, audit_data

    if OrderField.INST_EXT_UNIQUE_IDENT not in order_states_df.columns:
        audit_key = InsiderTradingV3RefinitivAuditName.INSTRUMENT_FIELD_MISSING
        audit_data = dict(field=OrderField.INST_EXT_UNIQUE_IDENT, event_day=event_day)
        logger.warning(
            f"The column {OrderField.INST_EXT_UNIQUE_IDENT} is not present in the order state dataset. Event day {event_day}",
            exc_info=True,
        )
        if method_id:
            return pd.DataFrame()
        return pd.DataFrame(), audit_key, audit_data
    if method_id:
        return order_states_df
    if order_states_filtered_mask.all():
        return order_states_df, InsiderTradingV3RefinitivAuditName.ORDER_EXCLUSION_NO_DROP, {}
    audit_key = InsiderTradingV3RefinitivAuditName.PARENT_ORDER_MISSING
    audit_data = dict(observation_period=observation_period, event_day=event_day)
    return order_states_df, audit_key, audit_data


def columns_in_df(keys: str, df: pd.DataFrame) -> bool:
    """
     Verify columns in dataframe
    :param: keys string with the mandatory columns
    :param: df dataframe to test the columns
    :return: bool value for the presence of the columns
    """
    columns = ORDER_STATE_CALCULATION_TYPES_MAP.get(keys)
    presence_bool = pd.Series(columns).isin(df.columns).all()

    return presence_bool


def set_instrument_value_evaluation_grp_val(
    group_values: Tuple[str],
) -> Tuple[str | None, str | None, str | None]:
    """sets instrument value and evaluation grp value for grouping.

    :param group_values: tuple or string with fields to be grouped by
    :return: tuple with fields to be grouped with
    """
    evaluation_grp_val = None
    side = None
    # in this version of pandas (2.1.4), group_values will always be a tuple. No need to check for string
    if isinstance(group_values, tuple):
        if len(group_values) > 2:
            instrument_value, evaluation_grp_val, side = group_values
        elif len(group_values) > 1:
            instrument_value, evaluation_grp_val = group_values
        else:
            # the algo always groups by instrument
            instrument_value = group_values[0]

    else:
        logger.info("Invalid group value type", exc_info=True)
        return None, None, None

    return instrument_value, evaluation_grp_val, side


def new_instrument_combination_with_rics(
    instrument_combinations: List,
    instrument_ric_mapping: Optional[Union[dict, pd.DataFrame]],
    instrument_order_count_map: dict[str, str] | None = None,
) -> List:
    """Add to each instrument the correspondent, if it exists, RIC.

    :param instrument_ric_mapping:
    :param instrument_combinations:
    :param instrument_order_count_map:
    :return:
    """
    instrument_comb_with_ric = []

    for inst_comb in instrument_combinations:
        new_inst_comb = []
        for inst in inst_comb:
            if isinstance(instrument_ric_mapping, pd.DataFrame) or instrument_ric_mapping is None:
                inst_tuple = (
                    (inst, None, instrument_order_count_map.get(inst, 0))
                    if instrument_order_count_map
                    else (inst, None)
                )
            else:
                instrument_keys: List[str] = instrument_ric_mapping.keys()

                inst_key = [key for key in instrument_keys if inst in key]

                if len(inst_key) > 0:
                    inst_tuple = (
                        (
                            inst,
                            instrument_ric_mapping.get(inst_key[0]),
                            instrument_order_count_map.get(inst, 0),
                        )
                        if instrument_order_count_map
                        else (inst, instrument_ric_mapping.get(inst_key[0]))
                    )

                else:
                    inst_tuple = (
                        (inst, None, instrument_order_count_map.get(inst, 0))
                        if instrument_order_count_map
                        else (inst, None)
                    )

            new_inst_comb.append(inst_tuple)

        instrument_comb_with_ric.append(new_inst_comb)

    return instrument_comb_with_ric


def new_instrument_ric_map(instruments_ids: List[str], instrument_id_ric_mapping: Dict) -> dict:
    """Method to remap the ric to the instrument used.

    :param instruments_ids: List[str], list of instruments to remap
    :param instrument_id_ric_mapping: Dict, mapping obtained from *get_ric_map*
    :return: Dict, new map
    """

    def _get_value(inst, mapping):
        for key in mapping:
            if inst in key:
                return mapping.get(key)
        return None

    instrument_ric_map = {
        inst: _get_value(inst, instrument_id_ric_mapping) for inst in instruments_ids
    }

    return instrument_ric_map


def get_grouping_columns(
    evaluation_type: str,
    data: pd.DataFrame,
    directionality: Optional[bool] = None,
) -> Dict | None:
    """
    get grouping columns to group the executions
    :param: evaluation_type: evaluation type that we get based on EVALUATION_TYPE_FIELD_MAPPING
    :return:
    """
    evaluation_column = EVALUATION_TYPE_FIELD_MAPPING.get(evaluation_type)
    grouping_columns = {
        "instrumentGrouping": DFColumns.INSTRUMENT,
    }

    if evaluation_column:
        grouping_columns.update({"evaluationGrouping": evaluation_column})

        if evaluation_column not in data.columns:
            fall_back_field: Optional[str] = EVALUATION_TYPE_FALL_BACK.get(evaluation_type.value)
            if fall_back_field is not None:
                grouping_columns["evaluationGrouping"] = fall_back_field
                logger.info(f"Fallback to {fall_back_field}")
            else:
                # No groupings will be applied if evaluation type is missing.
                return None

    if directionality:
        grouping_columns["directionalityGrouping"] = OrderField.EXC_DTL_BUY_SELL_IND

    return grouping_columns


def get_query_string_to_audit(query: SDPBaseQuery) -> str:
    """get string query to add to audit.

    :param query: SDPBaseQuery
    :return: string query
    """
    try:
        query_string = query.to_json()
        return query_string
    except Exception as e:
        logger.debug(f"Query has fields that are not serializable. Error: {e}")
    try:
        query_string = query.to_dict().__str__()
    except Exception as e:
        query_string = ""
        logger.debug(
            f"Not able to convert the query to str, the audit won't have the query "
            f"described. Error: {e}"
        )

    return query_string
