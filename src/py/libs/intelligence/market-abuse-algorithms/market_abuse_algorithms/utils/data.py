# type: ignore
import addict
import logging
import numpy as np
import pandas as pd
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.abstract_layering_v2.static import AlgoColumnsEnum
from market_abuse_algorithms.strategy.base.static import FloatColumns
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.models import (
    DisorderlyWindow,
)
from market_abuse_algorithms.strategy.message_volume.models import TimeWindow
from typing import Dict, List, Union

logger = logging.getLogger(__name__)


# TODO: Move the methods to the correct utility file
def check_value_exists(x):
    return not (x is None or (isinstance(x, float) and pd.isna(x)))


def get_venue_name(data: pd.DataFrame) -> Union[pd.Series, None]:
    """Get the venue name for a group of orders.

    :param data: pd.Dataframe, data to get the orders id
    :return: pd.Series with the the list of unique ids
    """

    if OrderField.TRX_DTL_VENUE in data.columns:
        venue_name: pd.Series = data.loc[:, OrderField.TRX_DTL_VENUE].unique().tolist()

    else:
        logger.info(
            f"{OrderField.TRX_DTL_VENUE} columns not in data. "
            f"Going to fallback to {OrderField.TRX_DTL_ULTIMATE_VENUE}."
        )

        if OrderField.TRX_DTL_ULTIMATE_VENUE in data.columns:
            venue_name: pd.Series = data.loc[:, OrderField.TRX_DTL_ULTIMATE_VENUE].unique().tolist()  # noqa: E501

        else:
            logger.info(
                f"{OrderField.TRX_DTL_ULTIMATE_VENUE} columns not in data. "
                f"Going to fallback to {AlgoColumnsEnum.VENUE}."
            )
            if AlgoColumnsEnum.VENUE in data.columns:
                venue_name: pd.Series = data.loc[:, AlgoColumnsEnum.VENUE].unique().tolist()

            else:
                venue_name = pd.NA

    return venue_name


def get_instrument_id_code(data: pd.DataFrame) -> Union[pd.Series, None]:
    """Get the instrument id code  for a group of orders.

    :param data: pd.Dataframe, data to get the orders id
    :return: pd.Series with the the list of unique ids
    """

    if OrderField.INST_ID_CODE in data.columns:
        instrument_id_code: pd.Series = data.loc[:, OrderField.INST_ID_CODE].unique().tolist()

    else:
        logger.info(
            f"{OrderField.INST_ID_CODE} columns not in data. "
            f"Going to fallback to {NewColumns.INSTRUMENT_CODE}."
        )

        if NewColumns.INSTRUMENT_CODE in data.columns:
            instrument_id_code: pd.Series = (
                data.loc[:, NewColumns.INSTRUMENT_CODE].unique().tolist()
            )

        else:
            instrument_id_code = pd.NA

    return instrument_id_code


def get_earliest_timestamp(
    data: pd.DataFrame, time_field: str, time_delta_column: str
) -> pd.DataFrame:
    """Get the earliest timestamp in a dataframe.

    :param data: pd.DataFrame.
    :param time_field: str. name of time column to use
    :param time_delta_column: str. name of timedelta column
    :return: pd.DataFrame ordered by earliest timestamp
    """

    data.loc[:, time_delta_column] = (
        data[time_field].diff().fillna(pd.Timedelta(seconds=0)).astype("timedelta64[s]")
    )

    data.loc[:, time_delta_column] = data[time_delta_column].apply(
        lambda x: pd.to_timedelta(x, unit="seconds")
    )

    data: pd.DataFrame = data.reset_index(drop=True)
    return data


def group_by_time_window(
    data: pd.DataFrame,
    order_field: OrderField,
    time_delta_column: str,
    time_threshold: pd.Timedelta,
    group_column: str,
    only_one_order: bool = False,
) -> List[pd.DataFrame]:
    """Group data by time window. Start with the earliest [Order Timestamp
    Updated] and Find all items thereafter that are <= [Order Timestamp
    Updated] + TW.

    :param data: pd.DataFrame ordered by earliest timestamp
    :param order_field: str. Order Field to create group
    :param time_delta_column: str
    :param time_threshold: pd.timedelta
    :param group_column: str
    :param only_one_order: bool, boolean to check if the group can have only one order
    :return: dataframe grouped
    """
    count: int = 0
    time_groups: List = []
    group_result: List = []
    for index, row in data.iterrows():
        if row[time_delta_column] >= pd.to_timedelta(time_threshold, unit="s"):
            count += 1

        time_groups.append(count)

    data[group_column] = pd.Series(time_groups)

    for ix, group in data.groupby(group_column):
        add: bool = len(group[order_field]) > 1

        if only_one_order:
            add: bool = len(group[order_field]) >= 1

        if add:
            group_result.append(group)

    return group_result


def create_time_window_groups(
    data: pd.DataFrame,
    time_column: str,
    time_threshold: int,
    time_delta_column: str,
    only_one_order: bool = False,
) -> List[pd.DataFrame]:
    """Grouping data based on the time window threshold.

    :param data: pd.Dataframe. Algorithm data
    :param time_threshold: pd.Timedelta. It corresponds to the timeWindow threshold
    :param time_delta_column: str
    :param only_one_order: bool, boolean to check if the group can have only one order
    :return: List of DataFrame. Returns a list of dataframes grouped by time window threshold
    """
    data[time_column] = pd.to_datetime(data[time_column], format="mixed")
    data: pd.DataFrame = data.sort_values(time_column)

    earliest_timestamp: pd.DataFrame = get_earliest_timestamp(
        data=data,
        time_field=time_column,
        time_delta_column=time_delta_column,
    )

    result: List[pd.DataFrame] = group_by_time_window(
        data=earliest_timestamp,
        order_field=OrderField.EXC_DTL_ORD_STATUS,
        time_delta_column=time_delta_column,
        time_threshold=time_threshold,
        group_column="timeGroups",
        only_one_order=only_one_order,
    )

    return result


def process_timestamp_result_response(result: pd.DataFrame) -> pd.DataFrame:
    """Process timestamp order fields. When Order Submitted does not exist fill
    with Order Updated timestamp.

    :param result: pd.DataFrame
    :return: pd.DataFrame
    """
    if (OrderField.TS_ORD_SUBMITTED not in result.columns) and (
        OrderField.TS_ORD_UPDATED in result.columns
    ):
        result.loc[:, OrderField.TS_ORD_SUBMITTED] = result.loc[:, OrderField.TS_ORD_UPDATED]

    elif OrderField.TS_ORD_SUBMITTED and OrderField.TS_ORD_UPDATED in result.columns:
        result[OrderField.TS_ORD_SUBMITTED] = result[OrderField.TS_ORD_SUBMITTED].fillna(
            result[OrderField.TS_ORD_UPDATED]
        )

    return result


def process_query_results(dataset: pd.DataFrame) -> pd.DataFrame:
    """Get trader ID & name using the Trader.

    :param dataset: pd.DataFrame. Data with executions

    :return: pd.DataFrame with trader ID and name
    """

    if OrderField.TRADER in dataset.columns:
        dataset.loc[:, OrderField.TRADER_NAME] = dataset[OrderField.TRADER].apply(
            lambda x: get_trader_info(trader_list=x, tag="name", top_level_column=OrderField.TRADER)  # noqa: E501
        )
        dataset.loc[:, OrderField.TRADER_ID] = dataset[OrderField.TRADER].apply(
            lambda x: get_trader_info(trader_list=x, tag="&id", top_level_column=OrderField.TRADER)
        )

    return dataset.reset_index(drop=True)


def get_trader_info(trader_list: List[Dict], tag: str, top_level_column: str) -> str:
    """Get trader info, based on the top level column.

    :param trader_list:
    :param tag: &id | name
    :param top_level_column: str, column to get the trader info
    :return:
    """

    if not check_value_exists(trader_list) or not isinstance(trader_list, list):
        return pd.NA

    trader_info = trader_list[0]

    if top_level_column == NewColumns.TRADER:
        data = trader_list[0]
        trader_info = data.get("value") if "value" in data.keys() else data

    trader_info = trader_info[0] if isinstance(trader_info, list) else trader_info

    if trader_info is None:
        return pd.NA

    return trader_info[tag] if tag in trader_info else pd.NA


def set_time_thresholds(time_field: Union[DisorderlyWindow, TimeWindow]) -> pd.Timedelta:
    """This method converts time window thresholds to pd.Timedelta."""

    time_threshold_timedelta = pd.Timedelta(
        value=time_field.value,
        unit=time_field.unit.value,
    )

    return time_threshold_timedelta


def process_numeric_columns(data: pd.DataFrame, dropnan: bool = False) -> pd.DataFrame:
    """convert numeric columns to float.

    :param data: pd.DataFrame. Data to be converted
    :param dropnan: bool. Check if it to drop nan in the float columns
    :return: pd.DataFrame
    """

    float_columns: List[str] = [col.value for col in FloatColumns.get_values()]

    if len(float_columns) == 0:
        return data

    if dropnan:
        data = data.dropna(subset=[col for col in float_columns if col in data.columns])

    for col in float_columns:
        if col in data.columns:
            data[col] = data[col].apply(lambda x: convert_to_float(x))

    return data


def convert_to_float(value_to_convert: any) -> float:
    """Convert a value to float, account to cases where it can be a pd.NA or a
    string with "nan" or "NA".

    :param value_to_convert:
    :return:
    """
    if pd.isna(value_to_convert):
        return np.nan
    try:
        value = float(value_to_convert)
    except Exception as e:
        logger.info(
            f"Failed to convert value to float. Value {value_to_convert}. "
            f"Error: {e}."
            f"Converting to numpy.nan"
        )
        return np.nan

    return value


def get_client_name(data: pd.DataFrame) -> Union[pd.Series, None]:
    """Get the client name for a group of orders.

    :param data: pd.Dataframe, data to get the orders id
    :return: pd.Series with the the list of unique ids
    """

    if OrderField.CLIENT_IDENT_CLIENT_NAME in data.columns:
        client_name: pd.Series = data.loc[:, OrderField.CLIENT_IDENT_CLIENT_NAME].unique().tolist()

    else:
        logger.info(
            f"{OrderField.CLIENT_IDENT_CLIENT_NAME} columns not in data. "
            f"Going to fallback to {NewColumns.CLIENT}."
        )
        if NewColumns.CLIENT in data.columns:
            client_name: pd.Series = data.loc[:, NewColumns.CLIENT].unique().tolist()
        else:
            client_name = pd.NA

    return client_name


def get_order_status_for_ids(data: pd.DataFrame, order_ids: List[str]) -> Union[pd.Series, None]:
    """Get the order status for a list of unique order ids present in the
    specific dataframe.

    :param data: pd.Dataframe, data to get the orders id
    :param order_ids: pd.Series with the the list of unique ids
    :return: pd.Series with the the list of order status
    """
    filter_data: pd.DataFrame = data[data.loc[:, OrderField.META_KEY].isin(order_ids)]

    if OrderField.EXC_DTL_ORD_STATUS in data.columns:
        order_status: pd.Series = filter_data.loc[:, OrderField.EXC_DTL_ORD_STATUS].tolist()

    else:
        order_status = pd.NA

    return order_status


def calculate_percentage_level(volume_at_level_col: str, single_order_data: pd.Series) -> float:
    """Calculate the percentage of the bid / ask volume at the defined price
    level.

    :param volume_at_level_col: str, name of column for the volume of an order at a specific level
    :param single_order_data: pd.Series, single order data to get the volume from
    :return: float, percentage at a level
    """
    if (
        single_order_data.empty
        or OrderField.PC_FD_INIT_QTY not in single_order_data.keys()
        or volume_at_level_col not in single_order_data.keys()
        or single_order_data[volume_at_level_col] is None
    ):
        return pd.NA

    percentage: float = (
        single_order_data.loc[OrderField.PC_FD_INIT_QTY] / single_order_data[volume_at_level_col]
    )

    return percentage


def get_venue_fallback(data_columns: List[str]) -> str:
    """Get venue column to use in the filter market facing orders market
    method. First try is  to use the transactionDetails.venue, then fallback to
    instrumentDetails.instrument.venue and lastly
    instrumentDetails.instrument.venue.ultimateVenue.

    :param data_columns: List[str], list with dataframe columns
    :return: str, venue column to use
    """
    if OrderField.TRX_DTL_VENUE not in data_columns:
        logger.warning(
            f"{OrderField.TRX_DTL_VENUE} columns not in NEWOs data. "
            f"Going to fallback to {OrderField.INST_VENUE_TRD_VENUE}."
        )

        if OrderField.INST_VENUE_TRD_VENUE not in data_columns:
            logger.warning(
                f"{OrderField.INST_VENUE_TRD_VENUE} columns not in NEWOs data. "
                f"Going to fallback to {OrderField.TRX_DTL_ULTIMATE_VENUE}."
            )
            venue_col: str = OrderField.TRX_DTL_ULTIMATE_VENUE
        else:
            venue_col: str = OrderField.INST_VENUE_TRD_VENUE
    else:
        venue_col: str = OrderField.TRX_DTL_VENUE

    if venue_col not in data_columns:
        logger.error(
            f"{venue_col} not in NEWO data. This is mandatory to get the market facing orders."
        )
        raise ValueError(f"{venue_col} column not in NEWOs data.")

    return venue_col


def sort_by_date(data: pd.DataFrame, date_column: str) -> pd.DataFrame:
    """Sort data by the date column (OrderField.TS_ORD_SUBMITTED or
    OrderField.TS_ORD_UPDATED)

    :param data: pd.Dataframe, data to be sorted
    :param date_column: str, name of time column to sort the data
    :return: pd.DataFrame, sorted data
    """
    logger.debug(f"Sort date by {date_column}")
    if date_column not in data.columns:
        logger.error(f"Column {date_column} not present in the data columns.")
        raise ValueError(f"Column {date_column} not in data to analyse.")

    sort_data: pd.DataFrame = data.sort_values(by=[date_column], ascending=True)
    return sort_data


def check_number_of_orders(data: pd.DataFrame, only_one_order: bool = False) -> bool:
    """Check if the group has data from more than one order id.

    :param data: pd.Dataframe, data to check
    :param only_one_order: bool, boolean to check if the group can have only one order
    :return: bool: true if there are more than one order id
    """
    groups = data.groupby(by=OrderField.META_KEY)

    if only_one_order:
        return groups.ngroups >= 1

    return groups.ngroups > 1


def get_list_instrument_unique_identifier(data: pd.DataFrame, venue_column: str) -> List[str]:
    """Get the instrument unique identifier to retrieve the correspondent RIC.

    :param data: pd.Dataframe, tenant data
    :param venue_column: str, name of venue column
    :return: str, instrument unique identifier
    """
    if OrderField.INST_EXT_UNIQUE_IDENT in data.columns:
        instrument_unique_identifier_list: List[str] = (
            data.loc[:, OrderField.INST_EXT_UNIQUE_IDENT].unique().tolist()
        )
    else:
        logger.debug(
            f"{OrderField.INST_EXT_UNIQUE_IDENT} column is not present in NEWOs data."
            f" Fallback to venue & currency."
        )

        if OrderField.TRX_DTL_PC_CCY not in data.columns or venue_column not in data.columns:
            logger.error(
                f"Either {OrderField.TRX_DTL_PC_CCY} or {venue_column} columns "
                f"not present in NEWOs data."
                f" Mandatory to define the instrument unique identifier."
            )
            raise ValueError("No column available to fetch instrument unique identifiers")
        combinations: List[tuple] = list(
            data.groupby(
                [NewColumns.INSTRUMENT_CODE, OrderField.TRX_DTL_PC_CCY, venue_column]
            ).groups.keys()
        )

        instrument_unique_identifier_list: List[str] = [
            "".join(combination) for combination in combinations
        ]

    return instrument_unique_identifier_list


def calculate_percentage(
    first_variable: float,
    second_variable: float,
    number_of_orders: dict,
    absolute_value: bool = False,
) -> float:
    """
    Calculate the percentage difference between two variables
    :param: first_variable float
    :param: second_variable, float
    :param: number_of_orders, dict, with number of order by each type
    :param: absolute_value, bool, to *100 or not
    :return: float, percentage
    """
    if 0 in number_of_orders.values():
        return pd.NA

    absolute_val: float = abs(first_variable - second_variable) / (
        (first_variable + second_variable) / 2
    )

    if absolute_value:
        return absolute_val

    return absolute_val * 100


def get_unique_value_for_alert(data: pd.DataFrame, column_name: str) -> List:
    """Get the unique value in a column value list and return it in a list.

    :param: data pd.DataFrame with alert data
    :param: column_name, column name to search the data for
    :return: List w/ an unique value
    """
    return data.loc[:, column_name].unique().tolist() if column_name in data.columns else pd.NA


def filter_df_by_timestamps(
    data: pd.DataFrame,
    timestamp_column: str,
    date_range: Dict,
) -> pd.DataFrame:
    """filters and returns dataframe using timestamps.

    :param data: data to be filtered
    :param timestamp_column: column tp filter
    :param date_range: date range with start/end of time filter
    :return: filtered by timerange dataframe
    """
    time_from: pd.Timestamp = date_range.get(DateRangeParameters.START).tz_localize(None)
    time_to: pd.Timestamp = date_range.get(DateRangeParameters.END).tz_localize(None)

    data[timestamp_column] = pd.to_datetime(data[timestamp_column], format="mixed")
    try:
        timerange_mask = (data.loc[:, timestamp_column] >= time_from) & (
            data.loc[:, timestamp_column] <= time_to
        )
        return data.loc[timerange_mask]
    except Exception as e:
        logger.error(f"{e}")
        return pd.DataFrame()


def get_dates_by_script(must_arguments: Union[List, Dict]) -> addict.Dict:
    """

    :param must_arguments:
    :return:
    """
    if isinstance(must_arguments, list):
        date_params = [
            must.get("script", {}).get("script", {}).get("params", {})
            for must in must_arguments
            if must.get("script", {}).get("script", {}).get("params", {})
        ]
        if date_params:
            date_params = date_params[0]
    else:
        date_params = must_arguments.get("script", {}).get("script", {}).get("params", {})

    return date_params


def get_dates_by_range(must_arguments: Union[List, Dict]) -> addict.Dict:
    """

    :param must_arguments:
    :return:
    """
    if isinstance(must_arguments, list):
        date_params = [must.get("range", {}) for must in must_arguments if must.get("range", {})]
        if date_params:
            date_params = date_params[0]
    else:
        date_params = must_arguments.get("range", {})

    return date_params


def epoch_filter_extract(filters: Dict[str, str]) -> addict.Dict:
    """Extracts the start and the end timestamps from the filters.

    :param filters: iris filters
    :return: starting and ending timestamps
    """

    must_list = filters.get("bool", {}).get("must", {})

    date_filters_dict = addict.Dict()

    if not must_list:
        return date_filters_dict

    date_params = get_dates_by_script(must_arguments=must_list) or get_dates_by_range(
        must_arguments=must_list
    )

    if date_params:
        if (
            DateRangeParameters.END in date_params.keys()
            and DateRangeParameters.START in date_params.keys()
        ):
            date_filters_dict[DateRangeParameters.START] = pd.to_datetime(
                date_params.get(DateRangeParameters.START), unit="ms"
            )
            date_filters_dict[DateRangeParameters.END] = pd.to_datetime(
                date_params.get(DateRangeParameters.END), unit="ms"
            )
        else:
            for k, val in date_params.items():
                if "timestamp" in k and "gte" in val.keys() and "lte" in val.keys():
                    lower_bound = val.get("gte")
                    upper_bound = val.get("lte")
                    date_filters_dict[DateRangeParameters.START] = (
                        pd.to_datetime(lower_bound, format="mixed")
                        if isinstance(lower_bound, str)
                        else pd.to_datetime(lower_bound, unit="ms")
                    )
                    date_filters_dict[DateRangeParameters.END] = (
                        pd.to_datetime(upper_bound, format="mixed")
                        if isinstance(upper_bound, str)
                        else pd.to_datetime(upper_bound, unit="ms")
                    )

    return date_filters_dict


def remove_rows_with_invalid_id(data: pd.DataFrame, column: str, value: str) -> pd.DataFrame:
    """Method filter rows with a specified value in a specified column.

    :param data: dataframe to be filtered
    :param column: column to filter the value
    :param value: value to be used as criterion to remove row
    :return: filtered dataframe
    """
    if column not in data.columns:
        logger.warning("The required column is not in the dataframe", exc_info=True)
        return data

    data_mask = data.loc[:, column] == value

    return data.loc[~data_mask]


def filter_regex_pattern_on_column(
    data: pd.DataFrame, regex_pattern: str, column: str
) -> pd.DataFrame:
    """method to select by regex pattern on specified column.

    :param data: data to be filtered
    :param regex_pattern: regex pattern to be used in the filter
    :param column: column where the values will be filtered by regex
    :return: filtered dataframe
    """
    if column not in data.columns:
        logger.warning("The required column is not in the dataframe", exc_info=True)
        return pd.DataFrame()

    regex_mask = data.loc[:, column].str.match(regex_pattern).astype(bool)

    filtered_df = data.loc[regex_mask]
    return filtered_df


def select_column_if_available(data: pd.DataFrame, column_name: str, fallback_column_name: str):
    """return column name if in dataframe, else returns fallback column.

    :param data: dataframe to be checked
    :param column_name: column to check
    :param fallback_column_name: column to be returned if not required column is available
    :return: required or fallback column name
    """
    if column_name in data.columns:
        return column_name

    return fallback_column_name


def remove_rows_with_value_in_column(data: pd.DataFrame, column: str, value: str) -> pd.DataFrame:
    """Method filter rows with a specified value in a specified column.

    :param data: dataframe to be filtered
    :param column: column to filter the value
    :param value: value to be used as criterion to remove row
    :return: filtered dataframe
    """
    if column not in data.columns:
        logger.warning("The required column is not in the dataframe", exc_info=True)
        return data

    data_mask = data.loc[:, column] == value

    return data.loc[~data_mask]


def is_option(order_states_df: pd.DataFrame) -> pd.Series:
    """Method to asses which rows of the provided dataframe are options.

    :param data: dataframe to be checked
    :return: boolean series indicating whether each corresponding row is an option
    """
    if OrderField.INST_CLASSIFICATION not in order_states_df.columns:
        logger.warning(
            "The required column '{OrderField.INST_CLASSIFICATION}' is not in the dataframe",
            exc_info=True,
        )
        return pd.Series()

    return (
        order_states_df[OrderField.INST_CLASSIFICATION]
        .astype(str)
        .apply(lambda x: x.startswith("O"))
    )
