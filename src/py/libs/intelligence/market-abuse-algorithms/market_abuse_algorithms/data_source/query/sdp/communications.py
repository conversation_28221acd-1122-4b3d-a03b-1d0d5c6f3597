from elasticsearch_dsl.query import Bool, Nested, QueryString, Terms
from market_abuse_algorithms.data_source.query.base import ValueStringType
from market_abuse_algorithms.data_source.query.sdp.base import SDPBaseQuery
from market_abuse_algorithms.data_source.query.utils import parse_value
from market_abuse_algorithms.data_source.static.sdp.communications import (
    CommunicationsField,
)
from se_elastic_schema.models.tenant.communication.call import Call
from se_elastic_schema.models.tenant.communication.email import Email
from se_elastic_schema.models.tenant.communication.message import Message
from se_elastic_schema.models.tenant.communication.text import Text


class _CommunicationsQuery(SDPBaseQuery):
    MODEL = ["Call", "Email", "Message", "Text"]
    MODEL_INDEX = [model.get_elastic_index_alias() for model in (Call, Email, Message, Text)]

    def __init__(self):
        super().__init__()

        self.model(value=self.MODEL, mode="filter")

    @parse_value
    def evidences_of_instrument(self, value: ValueStringType):
        conditions = []

        for v in value:
            for patt in [f"*{v}*", f"*underlying {v}*"]:
                cond = QueryString(
                    query=patt,
                    # fields=CommunicationsField.BODY_TEXT,
                    default_field="&all",
                    default_operator="AND",
                    analyze_wildcard="true",
                )
                conditions.append(cond)

        self.add_condition(mode="should", conditions=conditions)

    @parse_value
    def trader_id(self, value: ValueStringType, mode="must"):
        self.add_nested_condition(
            path=CommunicationsField.PARTICIPANTS,
            mode=mode,
            conditions=[
                Nested(
                    path=CommunicationsField.PART_OFF_IDENT_TRADER_IDS,
                    query=Bool(
                        must=[Terms(**{CommunicationsField.PART_OFF_IDENT_TRADER_IDS_ID: value})]
                    ),
                    ignore_unmapped="true",
                )
            ],
        )


class CommunicationsQuery(_CommunicationsQuery):
    pass


class CallQuery(_CommunicationsQuery):
    MODEL = "Call"


class EmailQuery(_CommunicationsQuery):
    MODEL = "Email"


class MessageQuery(_CommunicationsQuery):
    MODEL = "Message"


class TextQuery(_CommunicationsQuery):
    MODEL = "Text"
