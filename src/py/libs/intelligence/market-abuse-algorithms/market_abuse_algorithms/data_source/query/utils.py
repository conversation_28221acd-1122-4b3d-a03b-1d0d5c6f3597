# type: ignore
import addict
import logging
import pandas as pd
from functools import partial, wraps
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, NewColumns, OrderField
from market_abuse_algorithms.utils.data import get_trader_info  # type: ignore[attr-defined]
from typing import Tuple


def parse_value(func=None, is_timestamp=False):
    """Decorator to use in data_source Query methods. It is expected `value` as
    the first and only positional argument.

    :param func: function
    :param is_timestamp: bool
    :return:
    """

    def _parse_value(value) -> list:
        if value is None or (isinstance(value, (list, tuple, str)) and len(value) == 0):
            return []

        value = [value] if not isinstance(value, (list, tuple)) else value

        # Filter None, np.Nan, "" values from list
        value = list(filter(lambda x: not pd.isna(x) and x != "", value))

        return value

    @wraps(func)
    def wrapper(self, value, **kwargs):
        value = _parse_value(value)

        if len(value) == 0:
            return

        if is_timestamp:
            if kwargs.get("to_date"):
                value = list(map(lambda x: x.date().isoformat(), value))
            else:
                value = list(map(lambda x: x.isoformat(), value))

        return func(self, value, **kwargs)

    if func is None:
        return partial(parse_value, is_timestamp=is_timestamp)

    return wrapper


def remove_date_range_from_iris_filters(filters: dict) -> dict:
    """Method which removes date related filters provided in iris filters.

    :param filters: dictionary of iris filters provided by the user
    :return: dictionary with data
    """
    filters = addict.Dict(filters)
    bool_filter = filters.bool.filter
    should = filters.bool.should
    if not bool_filter and not should:
        logging.info("No filters were found in input iris filters")
        return filters

    if len(should) >= 2:
        filters = remove_timerange_should_conditions(
            should=should, filters=filters, filter_flag=bool_filter
        )
        if not filters:
            return {}

    expected_script_params = {
        DateRangeParameters.END,
        DateRangeParameters.START,
    }

    if isinstance(bool_filter, dict):
        params = bool_filter.get("script", {}).get("script", {}).get("params", {})
        range_params = bool_filter.get("range", {})

        if (
            not params or not all(val in expected_script_params for val in params)
        ) and not range_params:
            logging.info("No date filters were found in the input iris filters")
            return filters

        if "script" in bool_filter:
            filters.bool.filter.pop("script")

        if filters.get("bool", {}).get("filter", {}) == {}:
            logging.info("Scrip date range filters were removed")
            return {}

        if "range" in bool_filter:
            filters.bool.filter.pop("range")

        if filters.get("bool", {}).get("filter", {}) == {}:
            logging.info("Date range filters were removed")
            return {}

        return filters

    index_to_pop = []
    for i, cond in enumerate(bool_filter):
        if "script" not in cond and "range" not in cond:
            continue

        if "script" in cond:
            params = cond.get("script", {}).get("script", {}).get("params", {})
            if not params or not len(set(params.keys()) & expected_script_params) == len(
                expected_script_params
            ):
                continue

            index_to_pop.append(i)

        elif "range" in cond:
            index_to_pop.append(i)

    if len(index_to_pop) == 0:
        logging.info("No date filters were found in the input iris filters")
        return filters

    for index in reversed(index_to_pop):
        del bool_filter[index]

    logging.info("Script and/or range data scripts were removed")

    filters.bool.filter = bool_filter

    return filters


def get_date_range_from_filters(filters: dict):
    date_range = {}
    filters = addict.Dict(filters)
    must = filters.bool.must
    if not must:
        return date_range

    expected_script_params = {"start", "end"}

    if isinstance(must, dict):
        params = must.get("script", {}).get("script", {}).get("params", {})

        if not params or not len(set(params.keys()) & expected_script_params) == len(
            expected_script_params
        ):
            return date_range

        date_range["start_date"] = pd.to_datetime(params["start"], unit="ms")
        date_range["end_date"] = pd.to_datetime(params["end"], unit="ms")

        return date_range

    for i, cond in enumerate(must):
        if "script" not in cond:
            continue

        params = cond.get("script").get("script").get("params")
        if not params or not len(set(params.keys()) & expected_script_params) == len(
            expected_script_params
        ):
            continue

        date_range["start_date"] = pd.to_datetime(params["start"], unit="ms")
        date_range["end_date"] = pd.to_datetime(params["end"], unit="ms")

        return date_range

    return date_range


def convert_timestamp_fields_to_datetime(
    dataframe_to_be_processed: pd.DataFrame,
) -> pd.DataFrame:
    """
    Convert the timestamps fields to datetime, using the to_datetime method
    :return:
    """
    for col in OrderField.get_ts_fields():
        if col in dataframe_to_be_processed.columns:
            dataframe_to_be_processed[col] = pd.to_datetime(
                dataframe_to_be_processed[col].str.rstrip("Z"), format="mixed"
            )

    return dataframe_to_be_processed


def get_clients_data(data_to_be_processed: pd.DataFrame) -> Tuple[pd.DataFrame, list]:
    """Get the clients data (&id & and name)

    :param data_to_be_processed:
    :return:
    """
    columns_to_be_dropped_in_the_end = []

    if OrderField.BUYER in data_to_be_processed.columns:
        data_to_be_processed.loc[
            data_to_be_processed[OrderField.BUYER].notnull(), OrderField.BUYER_NAME
        ] = data_to_be_processed[OrderField.BUYER].dropna().apply(lambda x: x[0].get("name"))
        columns_to_be_dropped_in_the_end.append(OrderField.BUYER)

    if OrderField.SELLER in data_to_be_processed.columns:
        data_to_be_processed.loc[
            data_to_be_processed[OrderField.SELLER].notnull(),
            OrderField.SELLER_NAME,
        ] = data_to_be_processed[OrderField.SELLER].dropna().apply(lambda x: x[0].get("name"))
        columns_to_be_dropped_in_the_end.append(OrderField.SELLER)

    if OrderField.CLIENT_IDENT_CLIENT in data_to_be_processed.columns:
        data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT_ID] = (
            data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT]
            .dropna()
            .apply(lambda x: x[0].get("&id"))
        )
        data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT_NAME] = (
            data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT]
            .dropna()
            .apply(lambda x: x[0].get("name"))
        )

        for col in [
            OrderField.CLIENT_IDENT_CLIENT_ID,
            OrderField.CLIENT_IDENT_CLIENT_NAME,
        ]:
            if data_to_be_processed[col].dropna().empty:
                columns_to_be_dropped_in_the_end.append(col)

        columns_to_be_dropped_in_the_end.append(OrderField.CLIENT_IDENT_CLIENT)

    if OrderField.CLIENT_IDENT_CLIENT not in data_to_be_processed.columns:
        # Create client column based on buyer and seller when clientIdentifiers.client
        # is not present
        data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT_NAME] = pd.NA

        if OrderField.BUYER_NAME in data_to_be_processed.columns:
            data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT_NAME] = data_to_be_processed[
                OrderField.CLIENT_IDENT_CLIENT_NAME
            ].fillna(
                data_to_be_processed.loc[
                    data_to_be_processed[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY,
                    OrderField.BUYER_NAME,
                ]
            )
        if OrderField.SELLER_NAME in data_to_be_processed.columns:
            data_to_be_processed[OrderField.CLIENT_IDENT_CLIENT_NAME] = data_to_be_processed[
                OrderField.CLIENT_IDENT_CLIENT_NAME
            ].fillna(
                data_to_be_processed.loc[
                    data_to_be_processed[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL,
                    OrderField.SELLER_NAME,
                ]
            )

    data_to_be_processed[NewColumns.CLIENT] = data_to_be_processed[
        OrderField.CLIENT_IDENT_CLIENT_NAME
    ]

    return data_to_be_processed, columns_to_be_dropped_in_the_end


def get_trader_data(data_to_be_processed: pd.DataFrame) -> Tuple[pd.DataFrame, list]:
    """Get the trader data (&id & and name)

    :param data_to_be_processed:
    :return:
    """
    columns_to_be_dropped_in_the_end = [
        NewColumns.TRADER,
        OrderField.TRADER,
        OrderField.PARTICIPANTS,
    ]

    if OrderField.TRADER not in data_to_be_processed.columns:
        data_to_be_processed[OrderField.TRADER] = pd.NA

    data_to_be_processed[NewColumns.TRADER] = data_to_be_processed[OrderField.TRADER].combine_first(  # noqa: E501
        data_to_be_processed[OrderField.PARTICIPANTS]
    )

    data_to_be_processed[OrderField.TRADER_ID] = data_to_be_processed[NewColumns.TRADER].apply(
        lambda x: get_trader_info(x, tag="&id", top_level_column=NewColumns.TRADER)
    )

    data_to_be_processed[OrderField.TRADER_NAME] = data_to_be_processed[NewColumns.TRADER].apply(
        lambda x: get_trader_info(x, tag="name", top_level_column=NewColumns.TRADER)
    )

    data_to_be_processed[NewColumns.TRADER_ID] = data_to_be_processed[OrderField.TRADER_ID]
    data_to_be_processed[NewColumns.TRADER_NAME] = data_to_be_processed[OrderField.TRADER_NAME]

    return data_to_be_processed, columns_to_be_dropped_in_the_end


def remove_timerange_should_conditions(
    should: addict.Dict, filters: addict.Dict, filter_flag: addict.Dict
) -> addict.Dict:
    """Verifies the should conditions are timerange and removes them.

    :param should: should conditions
    :param filters: full iris filters addict
    :param filter_flag: filters for bol populated if it contains other types of filters
    :return: Addict with filters or empty dict in case of no filters
    """
    timerange_index = []
    for index, should_condition in enumerate(should):  # rename the i x vars
        for condition in should_condition.get("bool", {}).get("filter", []):
            range_condition = condition.get("range", None)
            if range_condition and (
                OrderField.TS_TRADING_DATE_TIME in range_condition
                or OrderField.TS_ORD_SUBMITTED in range_condition
            ):
                timerange_index.append(index)
                break

    if timerange_index:
        if len(timerange_index) == len(should):
            if filter_flag:
                filters.bool.pop("should")
                filters.bool.pop("minimum_should_match")
            else:
                return addict.Dict()
        else:
            for index in timerange_index[::-1]:
                del filters.bool.should[index]

    return filters
