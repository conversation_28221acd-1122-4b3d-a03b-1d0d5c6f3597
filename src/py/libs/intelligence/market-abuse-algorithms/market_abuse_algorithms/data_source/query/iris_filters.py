from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from typing import Optional


class IrisFilters:
    def __init__(self, filters: dict):
        self._filters = filters
        self._ts_order_submitted_start_date = None
        self._ts_order_submitted_end_date = None
        self._ts_order_received_start_date = None
        self._ts_order_received_end_date = None

    @property
    def filters(self):
        return self._filters

    def remove_all_date_range_conditions(self):
        if self._filters is None:
            return

        must = self._filters.get("bool", {}).get("must", {})

        if not must:
            return

        if isinstance(must, dict):
            if self._is_date_range_condition():
                self._filters["bool"].pop("must")

            return

        date_range_index = []

        for i, cond in enumerate(must):
            if self._is_date_range_condition(must_ix=i):
                date_range_index.append(i)

        if not date_range_index:
            return

        must = [cond for i, cond in enumerate(must) if i not in date_range_index]

        self._filters["bool"]["must"] = must

    def _is_date_range_condition(self, must_ix: Optional[int] = None):
        must = (
            self._filters.get("bool").get("must")[must_ix]
            if must_ix is not None
            else self._filters.get("bool").get("must")
        )

        ts_field = f"{OrderField.TS}."

        # Range condition
        if list(must.get("range", {"_": 0}).keys())[0].startswith(ts_field):
            return True
        # Script based on Hour Range
        elif ts_field in must.get("script", {}).get("script", {}).get("inline", ""):
            return True
        # Script for calendar
        elif any(
            [
                v.startswith(ts_field)
                for v in list(must.get("script", {}).get("script", {}).get("params", {}).values())
                if isinstance(v, str)
            ]
        ):
            return True
