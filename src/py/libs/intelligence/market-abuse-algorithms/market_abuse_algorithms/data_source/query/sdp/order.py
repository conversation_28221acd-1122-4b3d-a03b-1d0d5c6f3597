# type: ignore
import pandas as pd
from elasticsearch_dsl.aggs import A, Agg
from elasticsearch_dsl.query import Term, Terms
from market_abuse_algorithms.data_source.query.base import BaseAggs, ValueDateType, ValueStringType
from market_abuse_algorithms.data_source.query.sdp.base import SDPBaseQuery
from market_abuse_algorithms.data_source.query.utils import (
    convert_timestamp_fields_to_datetime,
    get_clients_data,
    get_trader_data,
    parse_value,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    Model,
    NewColumns,
    OrderField,
    OrderStatus,
    ParticipantsType,
)
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from se_elastic_schema.models.tenant.mifid2.order import Order
from typing import List, Optional, Union


class OrderBaseQuery(SDPBaseQuery):
    MODEL_INDEX = Order.get_elastic_index_alias()

    def __init__(
        self,
        model: Union[str, List[str]],
        order_states: Optional[Union[List[str], str]] = None,
    ):
        super().__init__()
        self.model(value=model)

        if order_states is not None:
            self.order_status(value=order_states)

    def add_iris_filters(self, filters: dict):
        if filters:
            self.update_query(filters)

    @parse_value
    def best_ex_asset_class_main(self, value: ValueStringType, mode="filter"):
        self.add_condition(
            mode=mode,
            conditions=[Terms(**{OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN: value})],
        )

    @parse_value
    def buy_sell(self, value: ValueStringType, mode="filter"):
        # TODO: Possibly replace by should and buy_sell fields
        self.add_condition(
            mode=mode, conditions=[Terms(**{OrderField.EXC_DTL_BUY_SELL_IND: value})]
        )

    @parse_value(is_timestamp=True)
    def date(self, value: ValueDateType, mode="filter", to_date: bool = False):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.DATE: value})])

    @parse_value
    def instrument_id(
        self,
        value: ValueStringType,
        mode: str = "should",
        check_underlying: bool = False,
    ):
        conditions = [Terms(**{field: value}) for field in OrderField.get_instrument_fields()]

        if check_underlying:
            conditions.append(Terms(**{OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE: value}))
        self.add_condition(mode=mode, conditions=conditions)

    @parse_value
    def instrument_unique_identifier(self, value: ValueStringType, mode="filter"):
        self.add_terms_query_with_chunks(
            value=value, field=OrderField.INST_EXT_UNIQUE_IDENT, mode=mode
        )

    @parse_value
    def order_id(self, value: ValueStringType, mode="filter"):
        self.add_terms_query_with_chunks(value=value, field=OrderField.ORD_IDENT_ID_CODE, mode=mode)  # noqa: E501

    @parse_value
    def order_status(self, value: ValueStringType, mode="filter"):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.EXC_DTL_ORD_STATUS: value})])

    @parse_value
    def parent(self, value: ValueStringType, mode="filter"):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.META_PARENT: value})])

    @parse_value
    def order_type(self, value: ValueStringType, mode="filter"):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.EXC_DTL_ORD_TYPE: value})])

    @parse_value
    def record_type(self, value: ValueStringType, mode="filter"):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.TRX_DTL_RECORD_TYPE: value})])  # noqa: E501

    @parse_value
    def venue(self, value: ValueStringType, mode="filter"):
        self.add_condition(mode=mode, conditions=[Terms(**{OrderField.TRX_DTL_VENUE: value})])

    @parse_value
    def ultimate_venue(self, value: ValueStringType, mode="filter"):
        self.add_condition(
            mode=mode, conditions=[Terms(**{OrderField.TRX_DTL_ULTIMATE_VENUE: value})]
        )

    @parse_value
    def short_selling_indicator(self, value: ValueStringType, mode="filter"):
        self.add_condition(
            mode=mode,
            conditions=[Terms(**{OrderField.EXC_DTL_SHORT_SELLING_IND: value})],
        )

    @parse_value
    def trader_id(self, value: ValueStringType, mode="filter"):
        self.add_nested_condition(
            path=OrderField.PARTICIPANTS,
            mode=mode,
            conditions=[
                Term(**{OrderField.PARTICIPANTS_TYPE: ParticipantsType.TRADER}),
                Terms(**{OrderField.PARTICIPANTS_VALUE_ID: value}),
            ],
        )

    @parse_value
    def trader_name(self, value: ValueStringType, mode="filter"):
        self.add_nested_condition(
            path=OrderField.PARTICIPANTS,
            mode=mode,
            conditions=[
                Term(**{OrderField.PARTICIPANTS_TYPE: ParticipantsType.TRADER}),
                Terms(**{OrderField.PARTICIPANTS_VALUE_NAME: value}),
            ],
        )

    def filter_by_minimum_notional(self, value: float, currency: str, mode="filter"):
        self.add_condition(
            mode=mode,
            conditions=[
                {
                    "script": {
                        "script": {
                            "inline": f"doc['{OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE}.{currency}'].value >= {value}",  # noqa: E501
                            "lang": "painless",
                        }
                    }
                }
            ],
        )

    @staticmethod  # noqa: C901
    def process_frame_result(result: pd.DataFrame) -> pd.DataFrame:
        drop_columns = []
        client_cols = [
            OrderField.BUYER,
            OrderField.SELLER,
            OrderField.CLIENT_IDENT_CLIENT,
        ]

        # Convert TS columns to datetime
        datetime_processed = convert_timestamp_fields_to_datetime(dataframe_to_be_processed=result)

        if set(client_cols).intersection(set(result.columns)):
            result, client_cols_to_drop = get_clients_data(data_to_be_processed=datetime_processed)
            drop_columns.extend(client_cols_to_drop)

        if OrderField.PARTICIPANTS not in result.columns:
            result[OrderField.PARTICIPANTS] = pd.NA

        result, trader_cols_to_drop = get_trader_data(data_to_be_processed=result)
        drop_columns.extend(trader_cols_to_drop)

        # Instrument code
        result[NewColumns.INSTRUMENT_CODE] = pd.NA
        for col in OrderField.get_instrument_fields():
            if col in result.columns:
                result[NewColumns.INSTRUMENT_CODE] = result[NewColumns.INSTRUMENT_CODE].fillna(
                    result[col]
                )

        # Format underlying instrument
        if OrderField.INST_DERIV_UND_INSTS in result.columns:
            result[NewColumns.UNDERLYING_INSTRUMENT_CODE] = result[
                OrderField.INST_DERIV_UND_INSTS
            ].apply(
                lambda x: x[0].get("underlyingInstrumentCode") if not isinstance(x, float) else x
            )
            result[NewColumns.INSTRUMENT_CODE] = result[NewColumns.INSTRUMENT_CODE].fillna(
                result[NewColumns.UNDERLYING_INSTRUMENT_CODE]
            )

        if OrderField.INST_EXT_UNDER_INST in result.columns:
            mask = result.loc[:, OrderField.INST_EXT_UNDER_INST].notna()

            result.loc[mask, NewColumns.UNDERLYING_INSTRUMENT_CODE] = result.loc[
                mask, OrderField.INST_EXT_UNDER_INST
            ].apply(lambda x: x[0].get("instrumentIdCode") if not isinstance(x, float) else x)

            result[NewColumns.INSTRUMENT_CODE] = result[NewColumns.INSTRUMENT_CODE].fillna(
                result[NewColumns.UNDERLYING_INSTRUMENT_CODE]
            )

        # Venue
        result[NewColumns.VENUE] = pd.NA
        for col in OrderField.get_venue_fields():
            if col in result.columns:
                result[NewColumns.VENUE] = result[NewColumns.VENUE].fillna(result[col])

        # Desks
        if OrderField.TRD_ALGO_FIRM_DESKS in result.columns:
            result[OrderField.TRD_ALGO_FIRM_DESKS_ID] = (
                result[OrderField.TRD_ALGO_FIRM_DESKS].dropna().apply(lambda x: x[0].get("id"))
            )
            result[OrderField.TRD_ALGO_FIRM_DESKS_NAME] = (
                result[OrderField.TRD_ALGO_FIRM_DESKS].dropna().apply(lambda x: x[0].get("name"))
            )

            drop_columns.append(OrderField.TRD_ALGO_FIRM_DESKS)

        # Drop unused columns
        result = result.drop(columns=drop_columns)

        return result

    def aggs_instruments(
        self,
        agg_script: QueryAggScripts,
    ):
        """Generates the aggregate to choose the preferred ID code for the
        instrument from the given instrument aggregate script.

        :param agg_script: QueryAggScripts, the script to choose the preferred\
                            ID code for the instrument.
        """

        self.add_aggs_bucket(
            bucket_name="instrument",
            condition=A(
                name_or_agg="terms",
                script=agg_script,
                size=self.MAX_AGGS_SIZE,
            ),
        )


class OrderQuery(OrderBaseQuery):
    MODEL_INDEX = Order.get_elastic_index_alias()

    def __init__(self):
        super().__init__(model=Model.ORDER, order_states=OrderStatus.NEWO)


class OrderExecutionsQuery(OrderBaseQuery):
    MODEL_INDEX = Order.get_elastic_index_alias()

    def __init__(self):
        super().__init__(model=Model.ORDER)


class OrderAggs(BaseAggs):
    # Aggs
    BY_CLIENT = "by_client"
    BY_CLIENT_ID = "by_client_id"
    BY_COUNTERPARTY = "by_counterparty"
    BY_FIRM = "by_firm"
    BY_ORDER_ID = "by_order_id"
    BY_INSTRUMENT = "by_instrument"
    BY_TRADER = "by_trader"
    BY_TRADER_ID = "by_trader_id"
    BY_INSTRUMENT_UNIQUE_IDENTIFIER = "by_instrument_unique_identifier"

    # Metrics
    BUYS = "buys"
    SELLS = "sells"
    BUYS_VOLUME = "buysTotalVolume"
    SELLS_VOLUME = "sellsTotalVolume"
    MAX_PRICE = "maxPrice"
    MIN_PRICE = "minPrice"
    MAX_TS = "maxTs"
    MIN_TS = "minTs"

    @classmethod
    def aggs_client(cls, query: Agg) -> Agg:
        client_aggs = A(
            "terms",
            field=OrderField.CLIENT_FILE_IDENTIFIER,
            size=OrderExecutionsQuery.MAX_AGGS_SIZE,
        )

        query.bucket(cls.BY_CLIENT, "nested", path=OrderField.CLIENT_IDENT_CLIENT).bucket(
            cls.BY_CLIENT_ID, client_aggs
        ).bucket(cls.REVERSE_NESTED, "reverse_nested")

        q_aggs = query[cls.BY_CLIENT][cls.BY_CLIENT_ID][cls.REVERSE_NESTED]

        return q_aggs

    @classmethod
    def aggs_counterparty(cls, query: Agg) -> Agg:
        query.bucket(
            cls.BY_COUNTERPARTY,
            "terms",
            field=OrderField.COUNTERPARTY_ID,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        q_aggs = query[cls.BY_COUNTERPARTY]

        return q_aggs

    @classmethod
    def aggs_instrument_unique_identifier(cls, query: Agg) -> Agg:
        query.bucket(
            cls.BY_INSTRUMENT_UNIQUE_IDENTIFIER,
            "terms",
            field=OrderField.INST_EXT_UNIQUE_IDENT,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        q_aggs = query[cls.BY_INSTRUMENT_UNIQUE_IDENTIFIER]

        return q_aggs

    @classmethod
    def aggs_instrument(cls, query: Agg) -> Agg:
        inst_agg = A(
            "terms",
            script=QueryAggScripts.ISIN_PRIORITY_SCRIPT,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        query.bucket(cls.BY_INSTRUMENT, inst_agg)

        q_aggs = query[cls.BY_INSTRUMENT]

        return q_aggs

    @classmethod
    def aggs_order_id(cls, query: Agg) -> Agg:
        query.bucket(
            cls.BY_ORDER_ID,
            "terms",
            field=OrderField.ORD_IDENT_ID_CODE,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        q_aggs = query[cls.BY_ORDER_ID]

        return q_aggs

    @classmethod
    def aggs_trader(cls, query: Agg) -> Agg:
        trader_aggs = A(
            "terms",
            script=QueryAggScripts.TRADER_SCRIPT,
            size=OrderBaseQuery.MAX_AGGS_SIZE,
        )

        query.bucket(cls.BY_TRADER, "nested", path=OrderField.PARTICIPANTS).bucket(
            cls.BY_TRADER_ID, trader_aggs
        ).bucket(cls.REVERSE_NESTED, "reverse_nested")

        q_aggs = query[cls.BY_TRADER][cls.BY_TRADER_ID][cls.REVERSE_NESTED]

        return q_aggs
