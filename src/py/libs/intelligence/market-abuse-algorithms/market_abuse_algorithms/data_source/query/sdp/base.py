# type: ignore

from elasticsearch_dsl.query import Exists
from market_abuse_algorithms.data_source.query.base import BaseQuery, ValueStringType
from market_abuse_algorithms.data_source.query.utils import parse_value
from market_abuse_algorithms.data_source.static.sdp.base import SDPField


class SDPBaseQuery(BaseQuery):
    def __init__(self):
        super().__init__()
        self.add_condition(mode="must_not", conditions=[Exists(field=SDPField.META_EXPIRY)])

    @parse_value
    def condition_by_id(self, value: ValueStringType, mode="filter"):
        self.add_terms_query_with_chunks(value=value, field=SDPField.META_ID, mode=mode)

    @parse_value
    def key(self, value: ValueStringType, mode="filter"):
        self.add_terms_query_with_chunks(value=value, field=SDPField.META_KEY, mode=mode)
