# type: ignore
import copy
import json
import pandas as pd
from elasticsearch_dsl import Search
from elasticsearch_dsl.aggs import A, Agg
from elasticsearch_dsl.query import Bool, Exists, Nested, Range, Terms
from market_abuse_algorithms.data_source.query.utils import parse_value
from market_abuse_algorithms.data_source.static.base import Base<PERSON>ield
from typing import List, Optional, Union

ValueStringType = Union[List[str], str]
ValueIntType = Union[List[int], int]
ValueDateType = Union[List[pd.Timestamp], pd.Timestamp]


class BaseQuery:
    MAX_AGGS_SIZE = 2147483647
    SIZE = 1000
    SEPARATOR = " ---- "
    MAX_VALUES_SIZE = 900
    MODEL_INDEX = ""

    # DEV NOTES: never use `self._q.update_from_dict` since it overrides the current query
    def __init__(self):
        self._q = Search()

    @property
    def q(self) -> Search:
        return self._q

    @q.setter
    def q(self, query: Search):
        self._q = query

    @property
    def aggs(self) -> Agg:
        return self._q.aggs

    def clone(self):
        new = copy.deepcopy(self)

        return new

    def to_dict(self) -> dict:
        return self._q.to_dict()

    def to_json(self):
        return json.dumps(self._q.to_dict(), indent=4)

    def update_query(self, query=None) -> None:
        if query is not None:
            self._q = self._q.query(query)

    def size(self, value: int):
        self._q = self._q.extra(size=value)

    @parse_value
    def includes(self, value: ValueStringType):
        self._q = self._q.source(includes=value)

    @parse_value
    def excludes(self, value: ValueStringType):
        self._q = self._q.source(excludes=value)

    @staticmethod
    def _convert_date_value_to_list(value: ValueDateType, convert_to_date: bool = False) -> list:
        if isinstance(value, pd.Timestamp):
            if convert_to_date:
                value = value.date()
            return [value.isoformat()]
        elif isinstance(value, list):
            if convert_to_date:
                return list(map(lambda x: x.value().isoformat(), value))
            return list(map(lambda x: x.isoformat(), value))
        else:
            raise NotImplementedError

    def add_condition(self, mode: str = "must", conditions: list = []):
        assert len(conditions) > 0

        if mode == "must":
            self.update_query(Bool(must=conditions))
        elif mode == "must_not":
            self.update_query(Bool(must_not=conditions))
        elif mode == "should":
            self.update_query(Bool(should=conditions, minimum_should_match=1))
        elif mode == "filter":
            self.update_query(Bool(filter=conditions))
        else:
            raise TypeError(f"mode {mode} not defined.")

    def add_nested_condition(
        self,
        path: str,
        mode: str = "must",
        mapped: str = "true",
        conditions: list = [],
    ):
        nested_conds = [
            Nested(
                path=path,
                query=Bool(must=conditions),
                ignore_unmapped=mapped,
            )
        ]

        if mode == "filter":
            self.update_query(Bool(filter=nested_conds))
        elif mode == "must":
            self.update_query(Bool(must=nested_conds))
        elif mode == "should":
            self.update_query(Bool(should=nested_conds))
        else:
            raise TypeError(f"mode {mode} not defined.")

    @parse_value(is_timestamp=True)
    def add_start_date(
        self,
        value: ValueDateType,
        field: str,
        equal: bool = True,
        to_date: bool = False,
    ):
        """

        :param value:
        :param field:
        :param equal:
        :param to_date: used in @parse_value
        :return:
        """
        self._add_date(value, field=field, equal=equal, cond="gt")

    @parse_value(is_timestamp=True)
    def add_end_date(
        self,
        value: ValueDateType,
        field: str,
        equal: bool = True,
        to_date: bool = False,
    ):
        """
        :param value:
        :param field:
        :param equal:
        :param to_date: used in @parse_value
        :return:
        """
        self._add_date(value, field=field, equal=equal, cond="lt")

    def _add_date(self, value: ValueDateType, field: str, cond: str, equal: bool = True):
        assert len(value) == 1
        value = value.pop()
        cond += "e" if equal else cond

        self.update_query(Bool(must=[Range(**{field: {cond: value}})]))

    @parse_value
    def model(self, value: ValueStringType, mode="must"):
        self.add_condition(mode=mode, conditions=[Terms(**{BaseField.META_MODEL: value})])

    def add_aggs_bucket(self, bucket_name: str, condition: A):
        self._q.aggs.bucket(bucket_name, condition)

    @staticmethod
    def process_frame_result(result: pd.DataFrame) -> pd.DataFrame:
        return result

    @classmethod
    def values_to_chunks(cls, values: list) -> list:
        """Convert list of values into chunks size MAX_VALUES_SIZE.

        :param values:
        :return:
        """
        values = [
            values[ix : ix + cls.MAX_VALUES_SIZE]
            for ix in range(0, len(values), cls.MAX_VALUES_SIZE)
        ]

        return values

    def add_terms_query_with_chunks(self, value: list, field: str, mode: str = "must"):
        chunks = self.values_to_chunks(value)

        if len(chunks) == 1:
            self.add_condition(mode=mode, conditions=[Terms(**{field: chunks[0]})])
            return

        shoulds = [Terms(**{field: chunk}) for chunk in chunks]

        self.add_condition(mode=mode, conditions=[Bool(should=shoulds, minimum_should_match=1)])

    def exists(self, field: str, mode: str = "filter"):
        self.add_condition(mode=mode, conditions=[Exists(field=field)])


class BaseAggs:
    # CONSTANTS
    MAX_AGGS_SIZE = 2147483647
    EWMA_ALPHA = 0.7

    ADV = "adv"
    DATE_HISTOGRAM = "date_histogram"
    REVERSE_NESTED = "reverse_nested"
    TOTAL_VOLUME = "totalVolume"

    @classmethod
    def aggs_adv(cls, query: Agg, window: int, buckets_path: str) -> Agg:
        query.pipeline(
            cls.ADV,
            "moving_fn",
            buckets_path=buckets_path,
            window=window,
            script=f"MovingFunctions.ewma(values, {cls.EWMA_ALPHA})",
        )

        return query

    @classmethod
    def aggs_date_histogram(cls, query: Agg, field: str, interval: str) -> Agg:
        query.bucket(
            cls.DATE_HISTOGRAM,
            "date_histogram",
            field=field,
            calendar_interval=interval,
        )

        q_aggs = query[cls.DATE_HISTOGRAM]

        return q_aggs

    @classmethod
    def metric_adv_exists(cls, query: Agg, adv_bucket_path: Optional[str] = None):
        if not adv_bucket_path:
            adv_bucket_path = cls.ADV

        query.metric(
            "check_adv_exists",
            "bucket_selector",
            buckets_path={"adv": adv_bucket_path},
            script="params.adv != null && params.adv > 0",
        )
