# type: ignore
from schema_sdk.base import BaseStrEnum


class RefinitivNewsColumns:
    CONTENT_URI = "contentUri"
    COPYRIGHT = "copyright"
    COPYRIGHT_HOLDER = "copyrightHolder"
    CREATED = "createdDateTime"
    CREATED_BY = "createdBy"
    EDITORIAL_URGENCY = "editorialUrgency"
    ID = "id"
    LANGUAGE = "language"
    MESSAGE_ID = "messageId"
    PERM_ID = "permId"
    PROVIDER = "provider"
    PUBLICATION_STATUS = "publicationStatus"
    RIC = "ric"
    ROLE = "role"
    SENTIMENT_NEGATIVE = "sentimentNegative"
    SENTIMENT_NEUTRAL = "sentimentNeutral"
    SENTIMENT_POSITIVE = "sentimentPositive"
    SOURCES = "sources"
    SOURCE_KEY = "sourceKey"
    STORY_CREATED = "storyCreated"
    STORY_ID = "storyId"
    STORY_SUBJECT_CONFIDENCE = "storySubjectConfidence"
    STORY_SUBJECT_RELEVANCE = "storySubjectRelevance"
    STORY_VERSION_CREATED = "storyVersionCreated"
    SUBJECTS = "subjects"
    SUBJECT_CODE = "subjectCode"
    SUBJECT_ID = "subjectId"
    TITLE = "title"
    UPDATED = "updatedDateTime"
    UPDATED_BY = "updatedBy"
    VENDOR_FILE_NAME = "vendorFileName"
    VERSION = "version"


class RefinitivNewsRequestResult:
    STORY_META_DATA = "StoryMetaData"
    STORY_SUBJECT = "StorySubject"
    SOURCES = "sources"
    SE_GROUPINGS = "seGroupings"


class RefinitivNewsRelevance(BaseStrEnum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class RefinitivNewsRequestBody:
    DATE_RANGE = "date_range"
    END = "end"  # str
    ISIN = "isin"
    IUID = "iuid"
    LATEST_VERSION = "latest_version"  # bool
    LIMIT = "limit"  # int
    MIN_RELEVANCE = "min_relevance"
    PERM_ID = "perm_id"
    RIC = "ric"
    SE_GROUPINGS = "seGroupings"
    SOURCES = "sources"
    START = "start"  # str
