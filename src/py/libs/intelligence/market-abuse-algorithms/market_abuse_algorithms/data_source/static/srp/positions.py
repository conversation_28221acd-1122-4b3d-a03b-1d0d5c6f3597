from market_abuse_algorithms.data_source.static.srp.base import SR<PERSON>Field


class PositionsField(SRPField):
    AMOUNT = "amount"
    AMOUNT_NATIVE = f"{AMOUNT}.native"
    AMOUNT_NATIVE_CURRENCY = f"{AMOUNT}.nativeCurrency"

    DATE = "date"
    DIRECTION = "direction"
    LEVEL = "level"
    SOURCE_INDEX = "sourceIndex"
    SOURCE_KEY = "sourceKey"

    # PARTIES
    PARTIES = "parties"
    PARTIES_TYPE = f"{PARTIES}.type"

    PARTIES_VALUE = f"{PARTIES}.value"

    PARTIES_VALUE_ID = f"{PARTIES_VALUE}.&id"
    # PARTIES_VALUE_ID = f"{PARTIES_VALUE}.fileIdentifier"
    PARTIES_VALUE_NAME = f"{PARTIES_VALUE}.name"
    PARTIES_VALUE_KEY = f"{PARTIES_VALUE}.&key"
    PARTIES_VALUE_PROFESSIONAL = f"{PARTIES_VALUE}.retailOrProfessional"

    PARTIES_CLIENT = f"{PARTIES}.client"
    PARTIES_CLIENT_ID = f"{PARTIES_CLIENT}.&id"
    PARTIES_CLIENT_NAME = f"{PARTIES_CLIENT}.name"

    PARTIES_TRADER = f"{PARTIES}.client"
    PARTIES_TRADER_ID = f"{PARTIES_TRADER}.&id"
    PARTIES_TRADER_NAME = f"{PARTIES_TRADER}.name"

    PARTIES_FILE_IDENTIFIER = f"{PARTIES}.fileIdentifier"

    PARTIES_FUND = f"{PARTIES}.client"
    PARTIES_FUND_ID = f"{PARTIES_FUND}.&id"
    PARTIES_FUND_NAME = f"{PARTIES_FUND}.name"

    PARTIES_ACCOUNT = f"{PARTIES}.client"
    PARTIES_ACCOUNT_ID = f"{PARTIES_ACCOUNT}.&id"
    PARTIES_ACCOUNT_NAME = f"{PARTIES_ACCOUNT}.name"

    PARTIES_PORTFOLIO_MANAGER = f"{PARTIES}.client"
    PARTIES_PORTFOLIO_MANAGER_ID = f"{PARTIES_PORTFOLIO_MANAGER}.&id"
    PARTIES_PORTFOLIO_MANAGER_NAME = f"{PARTIES_PORTFOLIO_MANAGER}.name"

    QUANTITY = "quantity"
    QUANTITY_NOTATION = "quantityNotation"

    @classmethod
    def get_amount_ccy(cls, currency: str):
        """
        WARNING: it uses transaction volume field
        :param currency:
        :return:
        """
        capitalize_first_currency_letter = str.title(currency)
        return f"{cls.AMOUNT}.amountIn{capitalize_first_currency_letter}"


class PositionsDirection:
    LONG = "LONG"
    SHORT = "SHORT"


class PositionLevel:
    ACCOUNT = "account"
    CLIENT = "Client"
    FUND = "Fund"
    PORTFOLIO = "Portfolio"
    RISK_ENTITY = "Risk Entity"
    TRADER = "Trader"
