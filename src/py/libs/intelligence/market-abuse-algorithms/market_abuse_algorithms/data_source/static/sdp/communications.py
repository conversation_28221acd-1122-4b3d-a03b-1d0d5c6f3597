from market_abuse_algorithms.data_source.static.base import <PERSON><PERSON><PERSON>
from typing import Tuple


class CommunicationsField(BaseField):
    # Root
    BODY = "body"
    PARTICIPANTS = "participants"
    TS = "timestamps"

    # Body
    BODY_TEXT = f"{BODY}.text"

    # Participants
    PART_VALUE_ID = f"{PARTICIPANTS}.value.&id"
    PART_OFF_IDENT = f"{PARTICIPANTS}.officialIdentifiers"

    # Official Identifiers
    PART_OFF_IDENT_TRADER_IDS = f"{PART_OFF_IDENT}.traderIds"
    PART_OFF_IDENT_TRADER_IDS_ID = f"{PART_OFF_IDENT_TRADER_IDS}.id"

    # Timestamps
    TS_START = f"{TS}.timestampStart"
    TS_END = f"{TS}.timestampEnd"

    @classmethod
    def get_nested_paths(cls) -> Tuple[str, str]:
        return cls.PARTICIPANTS, cls.PART_OFF_IDENT_TRADER_IDS
