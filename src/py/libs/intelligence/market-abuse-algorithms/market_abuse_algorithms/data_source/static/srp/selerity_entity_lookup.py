from market_abuse_algorithms.data_source.static.srp.base import SRPField


class SelerityEntityLookupField(SRPField):
    ENTITY_ID = "entityId"
    INSTRUMENT_MODEL = "instrumentModel"
    ISIN = "isin"
    ISSUER_ID = "issuerId"
    TENANT = "tenant"


class SelerityQueryTypes:
    """The specs of the queries can be checked in
    https://steeleye.atlassian.net/browse/EP-1350."""

    MA_DOWN = "OR(CONCEPT(SUBJ.FIN.MERGER_ACQUISITION, 0.5, 1.0),CONCEPT(SUBJ.FIN.EARNINGS, 0.5, 1.0)),OR(KEYWORD(negative,HEADLINE),KEYWORD(down,HEADLINE),KEYWORD(plunges,HEADLINE),KEYWORD(falls,HEADLINE),KEYWORD(drops,HEADLINE),KEYWORD(dips,HEADLINE),KEY<PERSON>ORD(tumbles,HEADLINE),<PERSON><PERSON><PERSON><PERSON>OR<PERSON>(underperforms,HEADLINE),KEYWOR<PERSON>(miss,HEADLINE), KEYWORD(fail,HEADLINE), KEYWORD(fails,HEADLINE), KEYWORD(failing,HEADLINE), KEYWORD(misses,HEADLINE),KEYWORD(missed,HEADLINE)))),CREDIBILITY(), IMPORTANCE(), RECENCY()))"  # noqa: E501
    MA_UP = "OR(CONCEPT(SUBJ.FIN.MERGER_ACQUISITION, 0.5, 1.0),CONCEPT(SUBJ.FIN.EARNINGS, 0.5, 1.0)),OR(KEYWORD(up,HEADLINE),KEYWORD(surge,HEADLINE),KEYWORD(leap,HEADLINE),KEYWORD(rise,HEADLINE),KEYWORD(positive,HEADLINE),KEYWORD(increase,HEADLINE),KEYWORD(upgrades,HEADLINE),KEYWORD(outperform,HEADLINE),KEYWORD(perform,HEADLINE),KEYWORD(smash,HEADLINE),KEYWORD(spike,HEADLINE),KEYWORD(beats,HEADLINE),KEYWORD(rally,HEADLINE),KEYWORD(rallies,HEADLINE),KEYWORD(jump,HEADLINE),KEYWORD(jumps,HEADLINE),KEYWORD(beat,HEADLINE), KEYWORD(beating,HEADLINE), KEYWORD(exceed,HEADLINE),KEYWORD(exceeds,HEADLINE)))),CREDIBILITY(), IMPORTANCE(), RECENCY()))"  # noqa: E501
    RATINGS_DOWN = "KEYWORD(downgrade,HEADLINE),CREDIBILITY(), IMPORTANCE(), RECENCY()))"
    RATINGS_UP = "KEYWORD(upgrade,HEADLINE),CREDIBILITY(), IMPORTANCE(), RECENCY()))"


class SelerityQlikColumnNames:
    ENTITY_ID = "Selerity Entity ID"
    INSTRUMENT = "Instrument"
    ISIN = "ISIN"
    ISSUER = "Issuer"
    RIC = "RIC"
    TICKET = "Ticker"
    VENUE_SELERITY = "Venue - Selerity"
    VENUE_REFINITIV = "Venue - Refinitiv"

    @classmethod
    def get_values(cls):
        return [v for k, v in vars(cls).items() if k.isupper()]


class SelerityEndpoints:
    QUERY = "query"
    SOURCES = "sources"


class SelerityColumns:
    BATCH_ID = "batchID"
    CONTENT_ID = "contentID"
    CONTRIBUTIONS = "contributions"
    HEADLINE = "headline"
    LAST_UPDATED = "lastUpdated"
    LINK_URL = "linkURL"
    MATERIAL_IMPACT = "materialImpact"
    QUERY_STRING = "queryString"
    RELEVANCE = "relevance"
    SCORE = "score"
    SOURCE = "source"
    SOURCE_ICON_URL = "sourceIconURL"
    SUMMARY = "summary"
    TIMESTAMP = "timestamp"


class SeleritySRPColumns:
    ISIN = "isin"
    ENTITY_ID = "entityId"


class SelerityClientStatus:
    BUILD_QUERY_FAILURE = "build_query_failure"
    INCOMPLETE_RESPONSE = "incomplete_response"
    INVALID_SELERITY_ENTITY_MAP = "invalid_selerity_entity_map"
    MISSING_SELERITY_ENTITY_MAP = "missing_selerity_entity_map"
    NON_200_SELERITY_RESPONSE = "non_200_selerity_response"
    RESPONSE_PARSING_FAILURE = "response_parsing_failure"
