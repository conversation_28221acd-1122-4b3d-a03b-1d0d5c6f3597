class CoppClarkColumn:
    IS_KEY_POPULATED = "_isKeyPopulated"
    IS_KEY_POPULATED_SUMMARY = "_isKeyPopulatedSummary"
    KEY_SUMMARY = "_keySummary"
    KEY_DETAILED = "_keyDetailed"
    ASSET_CLASS = "Asset Class"
    CFI = "CFI"
    CALENDAR_START = "Calendar Start"
    CALENDAR_END = "Calendar End"
    CALENDAR_DURATION_TYPE = "Calendar Duration Type"
    CODE = "Code"
    COUNTRY = "Country"
    DST_END = "Day End"
    DST_START = "Day Start"
    DST = "DST"
    DST_STARTS = "DST Starts"
    DST_ENDS = "DST Ends"
    EXCHANGE = "Exchange"
    GROUP = "Group"
    ISO_COUNTRY_CODE = "ISO Code"
    LAST_CONFIRMED = "Last Confirmed"
    MIC_CODE = "MIC Code"
    MIC_CODE_RAW = "MIC Code Raw"
    NOTES = "Notes"
    OLSON_TIMEZONE = "Olson time zone"
    PHASE = "Phase"
    PHASE_RAW = "Phase Raw"
    PHASE_TYPE = "Phase Type"
    PHASE_STARTS = "Phase Starts"
    PHASE_STARTS_DST = "Phase Starts DST"
    PHASE_STARTS_UTC = "Phase Starts UTC"
    PHASE_ENDS = "Phase Ends"
    PHASE_ENDS_DST = "Phase Ends DST"
    PHASE_ENDS_UTC = "Phase Ends UTC"
    PRODUCT = "Product"
    RANDOM_START_MIN = "Random Start Min"
    RANDOM_START_MAX = "Random Start Max"
    RANDOM_END_MIN = "Random End Min"
    RANDOM_END_MAX = "Random End Max"
    RANGE_TYPE = "Range Type"
    SEQUENCE = "Sequence"
    SOURCE_FILE = "Source File"
    STANDARD_TIME = "Standard Time"
    TYPE = "Type"
    WEEKDAY_START = "weekday_start"
    WEEKDAY_END = "weekday_end"


class PhaseType:
    INTRADAY_AUCTION = "Intra-day auction"
    CLOSING_AUCTION_CALL = "Closing auction/call"
    PRE_CLOSING = "Pre-closing"
    DAY_TRADING_T0 = "Day trading (T+0)"
    OPENING_AUCTION_CALL = "Opening auction/call"
    PRE_OPENING = "Pre-opening"


class ExtInstrumentClassification:
    DEBT_INSTRUMENTS = "Debt Instruments"
    DERIVATIVE_FUTURES = "Derivative - Futures"
    DERIVATIVE_OPTIONS = "Derivative - Options"
    STOCKS_AND_ETFS = "Stocks and ETFs"
    STRUCTURED_FINANCE_INSTRUMENTS = "Structured Finance Instruments"
