from market_abuse_algorithms.data_source.static.srp.base import SR<PERSON><PERSON><PERSON>
from typing import List


class PotamCaseField(SRPField):
    # Model
    _META = "_meta"
    META_MODEL = f"{_META}.model"
    AMP_MODEL = "&model"

    # component parts of field names, not full field names
    C_OFFER_PERIOD_COMMENCED = "offerPeriodCommenced"
    C_OFFEROR_PERIOD_IDENTIFIED = "offerorIdentified"
    C_TWO_TEN_INFORMATION = "twotenInformation"
    C_ISIN = "isin"

    CASE_ID = "caseId"
    DATE_CASE_DELETED = "dateCaseDeleted"
    OFFEREES = "offerees"
    OFFERORS = "offerors"

    OFFEREE_PERIOD_COMMENCED = f"{OFFEREES}.{C_OFFER_PERIOD_COMMENCED}"

    OFFREE_TWO_TEN_INFO = f"{OFFEREES}.{C_TWO_TEN_INFORMATION}"
    OFFEROR_TWO_TEN_INFO = f"{OFFERORS}.{C_TWO_TEN_INFORMATION}"
    OFFREE_TTI_ISIN = f"{OFFREE_TWO_TEN_INFO}.{C_ISIN}"
    OFFEROR_TTI_ISIN = f"{OFFEROR_TWO_TEN_INFO}.{C_ISIN}"
    OFFEROR_PERIOD_IDENTIFIED = f"{OFFERORS}.{C_OFFEROR_PERIOD_IDENTIFIED}"

    OFFEREE_NAME = "offereename"
    OFFEROR_NAME = "offerorname"

    @classmethod
    def get_datetime_fields(cls) -> List[str]:
        return [
            cls.DATE_CASE_DELETED,
            cls.OFFEREE_PERIOD_COMMENCED,
            cls.OFFEROR_PERIOD_IDENTIFIED,
        ]

    @classmethod
    def get_isin_fields(cls) -> List[str]:
        return [cls.OFFREE_TTI_ISIN, cls.OFFEROR_TTI_ISIN]
