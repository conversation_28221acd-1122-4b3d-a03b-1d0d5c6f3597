from market_abuse_algorithms.data_source.static.sdp.base import SD<PERSON>Field


class OrderField(SDPField):
    # Root
    BUYER = "buyer"
    BEST_EXC_DATA = "bestExecutionData"
    CLIENT_IDENT = "clientIdentifiers"
    COUNTERPARTY = "counterparty"
    DATE = "date"
    EXC_DTL = "executionDetails"
    INST_DTL = "instrumentDetails"
    MAR_DTL = "marDetails"
    ORD_IDENT = "orderIdentifiers"
    PARTICIPANTS = "participants"
    PC_FD = "priceFormingData"
    RPT_DTL = "reportDetails"
    SELLER = "seller"
    TS = "timestamps"
    TRX_DTL = "transactionDetails"
    TRD_ALGO = "tradersAlgosWaiversIndicators"

    # Best Execution Data
    BEST_EXC_DATA_ORD_VOL = f"{BEST_EXC_DATA}.orderVolume"
    BEST_EXC_DATA_ORD_VOL_ECB_REF_RATE = f"{BEST_EXC_DATA_ORD_VOL}.ecbRefRate"
    BEST_EXC_DATA_ORD_VOL_NATIVE = f"{BEST_EXC_DATA}.orderVolume.native"
    BEST_EXC_DATA_ORD_VOL_NATIVE_CURRENCY = f"{BEST_EXC_DATA}.orderVolume.nativeCurrency"
    BEST_EXC_DATA_TRX_VOL = f"{BEST_EXC_DATA}.transactionVolume"
    BEST_EXC_DATA_TRX_VOL_NATIVE = f"{BEST_EXC_DATA}.transactionVolume.native"
    BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY = f"{BEST_EXC_DATA}.transactionVolume.nativeCurrency"
    BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE = f"{BEST_EXC_DATA_TRX_VOL}.ecbRefRate"

    # Buyer
    BUYER_NAME = f"{BUYER}.name"

    # Client Identifiers
    CLIENT_IDENT_CLIENT = f"{CLIENT_IDENT}.client"
    CLIENT_IDENT_CLIENT_ID = f"{CLIENT_IDENT_CLIENT}.&id"
    CLIENT_IDENT_CLIENT_NAME = f"{CLIENT_IDENT_CLIENT}.name"
    CLIENT_FILE_IDENTIFIER = "clientFileIdentifier"

    # Counterparty
    COUNTERPARTY_ID = f"{COUNTERPARTY}FileIdentifier"
    COUNTERPARTY_NAME = f"{COUNTERPARTY}.name"

    # Execution Details
    EXC_DTL_BUY_SELL_IND = f"{EXC_DTL}.buySellIndicator"  # BUY_SELL
    EXC_DTL_LIMIT_PRICE = f"{EXC_DTL}.limitPrice"
    EXC_DTL_STOP_PRICE = f"{EXC_DTL}.stopPrice"
    EXC_DTL_ORD_STATUS = f"{EXC_DTL}.orderStatus"
    EXC_DTL_ORD_TYPE = f"{EXC_DTL}.orderType"
    EXC_DTL_PASS_AGGR_IND = f"{EXC_DTL}.passiveAggressiveIndicator"
    EXC_DTL_SHORT_SELLING_IND = f"{EXC_DTL}.shortSellingIndicator"
    EXC_DTL_VALIDITY_PERIOD = f"{EXC_DTL}.validityPeriod"

    # Instrument Details
    INST = f"{INST_DTL}.instrument"

    INST_CFI_CATEGORY = f"{INST}.cfiCategory"
    INST_CLASSIFICATION = f"{INST}.instrumentClassification"
    INST_EXT = f"{INST}.ext"
    INST_DERIV = f"{INST}.derivative"
    INST_FULL_NAME = f"{INST}.instrumentFullName"
    INST_ID_CODE = f"{INST}.instrumentIdCode"
    INST_ID_VENUE_KEY = f"{INST}.IdVenueKey"
    INST_VENUE = f"{INST}.venue"
    INST_ISSUER = f"{INST}.issuerOrOperatorOfTradingVenueId"

    INST_DERIV_OPTION_TYPE = f"{INST_DERIV}.optionType"
    INST_DERIV_PRICE_MULTIPLIER = f"{INST_DERIV}.priceMultiplier"
    INST_DERIV_STRIKE_PRICE = f"{INST_DERIV}.strikePrice"
    INST_DERIV_UND_INDEX_NAME = f"{INST_DERIV}.underlyingIndexName"
    INST_DERIV_UND_INSTS = f"{INST_DERIV}.underlyingInstruments"
    INST_DERIV_UND_INSTS_UND_INST_CODE = f"{INST_DERIV_UND_INSTS}.underlyingInstrumentCode"

    INST_EXT_ALT_IDENT = f"{INST_EXT}.alternativeInstrumentIdentifier"
    INST_EXT_UNIQUE_IDENT = f"{INST_EXT}.instrumentUniqueIdentifier"
    INST_EXT_EXCHANGE_SYMBOL_ROOT = f"{INST_EXT}.exchangeSymbolRoot"
    INST_EXT_BEST_EX_ASSET_CLASS_MAIN = f"{INST_EXT}.bestExAssetClassMain"
    INST_EXT_BEST_EX_ASSET_CLASS_SUB = f"{INST_EXT}.bestExAssetClassSub"
    INST_EXT_PC_REFS = f"{INST_EXT}.pricingReferences"
    INST_EXT_PC_REFS_ICE = f"{INST_EXT}.pricingReferences.ICE"
    INST_EXT_UNDER_INST = f"{INST_EXT}.underlyingInstruments"
    INST_EXT_UNDER_INST_INST_CODE = f"{INST_EXT}.underlyingInstruments.instrumentIdCode"

    INST_VENUE_TRD_VENUE = f"{INST_VENUE}.tradingVenue"

    # Mar details
    MAR_DTL_PAD = f"{MAR_DTL}.isPersonalAccountDealing"

    # Order Identifiers
    ORD_IDENT_ID_CODE = f"{ORD_IDENT}.orderIdCode"
    ORD_IDENT_AGGREGATED_ID_CODE = f"{ORD_IDENT}.aggregatedOrderId"

    # Participants
    PARTICIPANTS_TYPE = f"{PARTICIPANTS}.types"
    PARTICIPANTS_VALUE = f"{PARTICIPANTS}.value"
    PARTICIPANTS_VALUE_ID = f"{PARTICIPANTS_VALUE}.&id"
    PARTICIPANTS_VALUE_NAME = f"{PARTICIPANTS_VALUE}.name"

    # Price Forming Data
    PC_FD_PRICE = f"{PC_FD}.price"  # PRICE
    PC_FD_TRD_QTY = f"{PC_FD}.tradedQuantity"
    PC_FD_INIT_QTY = f"{PC_FD}.initialQuantity"

    # Report
    RPT_DTL_EXC_ENTITY = f"{RPT_DTL}.executingEntity"
    RPT_DTL_EXC_ENTITY_NAME = f"{RPT_DTL_EXC_ENTITY}.name"
    RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER = f"{RPT_DTL_EXC_ENTITY}.fileIdentifier"

    # Seller
    SELLER_NAME = f"{SELLER}.name"

    # Trader
    TRADER = "trader"
    TRADER_ID = f"{TRADER}.&id"
    TRADER_NAME = f"{TRADER}.name"
    TRADER_FILE_IDENTIFIER = "traderFileIdentifier"

    # Timestamps
    TS_ORD_RECEIVED = f"{TS}.orderReceived"
    TS_ORD_SUBMITTED = f"{TS}.orderSubmitted"
    TS_ORD_UPDATED = f"{TS}.orderStatusUpdated"
    TS_TRADING_DATE_TIME = f"{TS}.tradingDateTime"

    # Transaction Details
    TRX_DTL_PC_CCY = f"{TRX_DTL}.priceCurrency"
    TRX_DTL_PC_NOTATION = f"{TRX_DTL}.priceNotation"
    TRX_DTL_RECORD_TYPE = f"{TRX_DTL}.recordType"
    TRX_DTL_VENUE = f"{TRX_DTL}.venue"  # VENUE
    TRX_DTL_ULTIMATE_VENUE = f"{TRX_DTL}.ultimateVenue"
    TRX_DTL_QUANTITY = f"{TRX_DTL}.quantity"
    TRX_DTL_PC_DTL_ARRIVAL_PRICE = f"{TRX_DTL}.pricingDetails.arrivalPrice"
    TRX_DTL_PC_DTL_MID_POINT_PRICE = f"{TRX_DTL}.pricingDetails.midPointPrice"
    TRX_DTL_PC_DTL_PERCENT_VS_ASK_PRICE = f"{TRX_DTL}.pricingDetails.percentVsAskPrice"
    TRX_DTL_PC_DTL_PERCENT_VS_BID_PRICE = f"{TRX_DTL}.pricingDetails.percentVsBidPrice"

    TRX_DTL_PC_DTL_PERCENT_VS_NEAREST_QUOTE = f"{TRX_DTL}.pricingDetails.percentVsNearestQuote"
    TRX_DTL_PC_DTL_PERCENT_VS_CLOSE = f"{TRX_DTL}.pricingDetails.percentVsClose"
    TRX_DTL_PC_DTL_PERCENT_VS_OPEN = f"{TRX_DTL}.pricingDetails.percentVsOpen"
    TRX_DTL_QUANTITY_NOTATION = f"{TRX_DTL}.quantityNotation"
    TRX_DTL_TR_CAPACITY = f"{TRX_DTL}.tradingCapacity"

    # TradersAlgosWaiversIndicators
    TRD_ALGO_FILE_IDENTIFIER = f"{TRD_ALGO}.investmentDecisionWithinFirmFileIdentifier"
    TRD_ALGO_FIRM = f"{TRD_ALGO}.investmentDecisionWithinFirm"
    TRD_ALGO_FIRM_COUNTRY = f"{TRD_ALGO_FIRM}.location.officeAddress.country"
    TRD_ALGO_FIRM_DESKS = f"{TRD_ALGO_FIRM}.structure.desks"
    TRD_ALGO_FIRM_NAME = f"{TRD_ALGO_FIRM}.name"
    TRD_ALGO_FIRM_DESKS_ID = f"{TRD_ALGO_FIRM_DESKS}.id"
    TRD_ALGO_FIRM_DESKS_NAME = f"{TRD_ALGO_FIRM_DESKS}.name"

    @classmethod
    def get_instrument_fields(cls):
        return [cls.INST_ID_CODE, cls.INST_EXT_ALT_IDENT, cls.INST_EXT_UNIQUE_IDENT]

    @classmethod
    def get_fields_for_market_data(cls):
        return [cls.INST_EXT_PC_REFS_ICE]

    @classmethod
    def get_venue_fields(cls):
        return [cls.TRX_DTL_VENUE, cls.TRX_DTL_ULTIMATE_VENUE, cls.INST_VENUE_TRD_VENUE]

    @classmethod
    def get_ts_fields(cls):
        return [
            cls.TS_ORD_RECEIVED,
            cls.TS_ORD_SUBMITTED,
            cls.TS_ORD_UPDATED,
            cls.TS_TRADING_DATE_TIME,
        ]

    @classmethod
    def get_client_fields(cls):
        return [cls.BUYER_NAME, cls.SELLER_NAME]

    @classmethod
    def get_involved_parties_fields(cls):
        return [cls.BUYER, cls.SELLER, cls.PARTICIPANTS, cls.COUNTERPARTY_NAME]

    @classmethod
    def get_best_exc_trx_ecb_ref_rate_ccy(cls, currency: str) -> str:
        """
        WARNING: it uses transaction volume field
        :param currency:
        :return:
        """
        return f"{cls.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE}.{currency}"

    @classmethod
    def get_best_exc_ord_ecb_ref_rate_ccy(cls, currency: str) -> str:
        """
        WARNING: it uses order volume field
        :param currency: str
        :return:
        """
        return f"{cls.BEST_EXC_DATA_ORD_VOL_ECB_REF_RATE}.{currency}"


class NewColumns:
    _FIXES = "__"
    TRADER = f"{_FIXES}trader{_FIXES}"
    TRADER_ID = f"{_FIXES}trader_id{_FIXES}"
    TRADER_NAME = f"{_FIXES}trader_name{_FIXES}"
    CLIENT = f"{_FIXES}client{_FIXES}"
    UNDERLYING_INSTRUMENT_CODE = f"{_FIXES}underlying_instrument_code{_FIXES}"
    INSTRUMENT_CODE = f"{_FIXES}instrument_code{_FIXES}"
    VENUE = f"{_FIXES}venue{_FIXES}"

    @classmethod
    def get_columns(cls):
        return [
            cls.TRADER_ID,
            cls.TRADER_NAME,
            cls.CLIENT,
            cls.UNDERLYING_INSTRUMENT_CODE,
            cls.INSTRUMENT_CODE,
            cls.VENUE,
        ]

    @classmethod
    def get_values(cls):
        return [v for k, v in vars(cls).items() if k.isupper()]


class BuySell:
    BUY = "BUYI"
    SELL = "SELL"

    @classmethod
    def get_opposite_side(cls, side):
        # TODO: I am not confident this is working for pandas functions.
        return cls.BUY if side == cls.SELL else cls.SELL


class Model:
    ORDER = "Order"
    ORDER_STATE = "OrderState"


class OrderStatus:
    CAME = "CAME"
    CAMO = "CAMO"
    CHME = "CHME"
    CHMO = "CHMO"
    EXPI = "EXPI"
    FILL = "FILL"
    NEWO = "NEWO"
    REMA = "REMA"
    REME = "REME"
    REMH = "REMH"
    REMO = "REMO"
    PARF = "PARF"
    TRIG = "TRUG"
    PNDC = "PNDC"


class OrderType:
    LIMIT = "Limit"
    STOP = "Stop"
    MARKET = "Market"


class OptionType:
    CALL = "CALL"
    PUT = "PUTO"
    OTHR = "OTHR"


class RecordType:
    ALLOCATION = "Allocation"
    CLIENT_SIDE = "Client Side"
    MARKET_SIDE = "Market Side"


class ShortSellingIndicator:
    SESH = "SESH"
    SSEX = "SSEX"
    SELL = "SELL"
    NTAV = "NTAV"


class ParticipantsType:
    TRADER = "TRADER"
    DECISION_MAKER = "DECISION_MAKER"


class BestExAssetClassMain:
    DEBT_INSTRUMENTS = "Debt Instruments"
    EQUITY = "Equity"
    EXCHANGE_TRADED_PRODUCTS = "Exchange Traded Products"
    STRUCTURED_FINANCE_INSTRUMENTS = "Structured Finance Instruments"


class VenueCode:
    XXXX = "XXXX"
    XOFF = "XOFF"
