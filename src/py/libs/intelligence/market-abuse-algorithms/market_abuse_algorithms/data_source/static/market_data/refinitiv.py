class RefinitivColumnsEnum:
    ASK_PRICE: str = "AskPrice"
    ASK_SIZE: str = "AskSize"
    BID_PRICE: str = "BidPrice"
    BID_SIZE: str = "BidSize"
    CURRENCY_CODE: str = "Currency Code"
    EXCHANGE_CODE: str = "Exchange Code"
    IDENTIFIER_TYPE: str = "IdentifierType"
    MARKET_MIC: str = "Market MIC"
    RIC: str = "RIC"


class RefinitivQuoteLevel1Enum:
    ASK_PRICE: str = "Ask Price"
    ASK_SIZE: str = "Ask Size"
    BID_PRICE: str = "Bid Price"
    BID_SIZE: str = "Bid Size"
