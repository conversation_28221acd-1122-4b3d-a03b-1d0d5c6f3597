# type: ignore
import logging
import os
from elasticsearch8 import Elasticsearch
from market_abuse_algorithms.data_source.repository.base import Repository

logging.getLogger("elasticsearch").setLevel(logging.DEBUG)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s.%(msecs)03d %(levelname)s [%(funcName)s:%(lineno)d] %(message)s",
    datefmt="%Y-%m-%dT%H:%M:%S",
)


class SDP(Repository):
    VERSION = "8"

    def __init__(self, tenant: str):
        self.ELASTIC_URL = os.environ.get("ELASTIC_URL")
        self.USE_SSL = False
        self.VERIFY_CERTS = os.environ.get("ELASTIC_VERIFY_CERTS", False)
        self.ELASTIC_API_KEY = os.environ.get("ELASTIC_API_KEY")

        super().__init__(tenant=tenant)

    def _create_client(self):
        client = Elasticsearch(
            hosts=[self.ELASTIC_URL],
            timeout=self.TIMEOUT,
            verify_certs=self.VERIFY_CERTS,
            ssl_show_warn=self.VERIFY_CERTS,
            retry_on_status=self.RETRY_ON_STATUS,
            retry_on_timeout=self.RETRY_ON_TIMEOUT,
            max_retries=self.MAX_RETRIES,
            api_key=self.ELASTIC_API_KEY,
        )

        return client

    @property
    def transport(self):
        return self._client.transport
