from pydantic import BaseSettings, Field


class MasterDataSettingsCls(BaseSettings):
    MASTER_DATA_STORAGE_BUCKET: str = Field("s3://master-data.eu-west-1.steeleye.co")
    TICK_BUCKET: str = Field("s3://refinitiv.steeleye.co")
    MARKET_DATA_API_URL: str = Field("https://api.dev-market-data.steeleye.co")
    MASTER_DATA_API_HOST: str = Field("https://api.dev-master-data.steeleye.co")
    COGNITO_CLIENT_ID: str
    COGNITO_CLIENT_SECRET: str
    COGNITO_AUTH_URL: str


master_data_config: MasterDataSettingsCls = MasterDataSettingsCls()
