import httpx
import logging
from functools import lru_cache
from market_abuse_algorithms.data_source.repository.master_data.config import (
    master_data_config,
)
from master_data_api_client.api.potam import Potam
from se_api_client.auth.oauth2_auth_client import OAuth2A<PERSON><PERSON>lient
from se_api_client.client import <PERSON><PERSON><PERSON><PERSON>
from typing import List

logger = logging.getLogger(__name__)


class MarketDataMappingError(Exception):
    pass


class MarketDataMappingNotFound(Exception):
    pass


class MasterDataClient:
    def __init__(self):
        self._auth_client = OAuth2AuthClient(
            client_id=master_data_config.COGNITO_CLIENT_ID,
            client_secret=master_data_config.COGNITO_CLIENT_SECRET,
            oauth2_url=master_data_config.COGNITO_AUTH_URL,
        )
        self.master_api_host = master_data_config.MASTER_DATA_API_HOST
        self._master_api_client = self._get_api_client(host=self.master_api_host)
        self.potam = Potam(self._master_api_client)

    def _get_api_client(self, host: str):
        try:
            return ApiClient(host=host, auth_client=self._auth_client)
        except Exception as e:
            raise ConnectionError(f"Cannot initialize API client: {type(e)} - {e}")

    def get_isins_from_cases(self):
        """Call master-data-api to get isins from cases."""
        try:
            return self.potam.get_isins_from_cases().content
        except httpx.HTTPStatusError as exc:
            logger.warning(
                "Error response %s while requesting %s. - %s",
                exc.response.status_code,
                exc.request.url,
                exc.response.content,
            )
            if exc.response.status_code == 404:
                raise MarketDataMappingNotFound from exc
            raise
        except httpx.HTTPError as exc:
            logger.warning("An error occurred while requesting %s. - %s", exc.request.url, exc)
            raise MarketDataMappingError from exc

    def get_cases_from_isins(
        self,
        isins: List[str],
    ):
        """Call master-data-api to get cases from isins."""
        try:
            return self.potam.get_cases_from_isins(isins=isins).content
        except httpx.HTTPStatusError as exc:
            logger.warning(
                "Error response %s while requesting %s. - %s",
                exc.response.status_code,
                exc.request.url,
                exc.response.content,
            )
            if exc.response.status_code == 404:
                raise MarketDataMappingNotFound from exc
            raise
        except httpx.HTTPError as exc:
            logger.warning("An error occurred while requesting %s. - %s", exc.request.url, exc)
            raise MarketDataMappingError from exc


@lru_cache()
def get_master_data_client() -> MasterDataClient:
    """Return a cached master data client.

    Using @lru_cache allows us to re-use the same client for all partitions
    processed by the same worker resulting in a time saving on flow execution.

    Returns:
        MasterDataClient: master data client
    """
    return MasterDataClient()
