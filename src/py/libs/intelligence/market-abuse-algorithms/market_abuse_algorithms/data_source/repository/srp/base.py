import os
from elasticsearch6 import Elasticsearch
from market_abuse_algorithms.data_source.repository.base import Repository
from typing import Optional


class SRPClient(Repository):
    PORT = 9200
    SCHEME = "http"
    USE_SSL = False
    VERIFY_CERTS = True
    VERSION = "6"

    def __init__(self, index: Optional[str] = None):
        self.HOST = os.environ.get("SRP_ELASTIC_HOST", self.HOST)
        self.PORT = int(os.environ.get("SRP_ELASTIC_PORT", self.PORT))
        super().__init__(tenant=index)

    def _create_client(self):
        client = Elasticsearch(
            hosts=[self.HOST],
            port=self.PORT,
            scheme=self.SCHEME,
            timeout=self.TIMEOUT,
            use_ssl=self.USE_SSL,
            verify_certs=self.VERIFY_CERTS,
            retry_on_status=self.RETRY_ON_STATUS,
            retry_on_timeout=self.RETRY_ON_TIMEOUT,
            max_retries=self.MAX_RETRIES,
        )

        return client
