# ruff: noqa: E501
# type: ignore
import copy
import httpx
import logging
import os
import pandas as pd
import pytz
from functools import lru_cache
from mar_utils.auditor.refinitv_audit_static import RefinitivAuditName
from market_abuse_algorithms.data_source.static.news.refinitiv_news import (
    RefinitivNewsColumns,
    RefinitivNewsRelevance,
    RefinitivNewsRequestBody,
    RefinitivNewsRequestResult,
)
from market_abuse_algorithms.mar_audit.mar_audit import StepAudit
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from master_data_api_client.api.news import News
from requests.exceptions import JSONDecodeError
from se_api_client.auth.oauth2_auth_client import OAuth2AuthClient
from se_api_client.client import ApiClient
from typing import List, Optional, Tuple

logging.basicConfig(
    level=logging.DEBUG,
    format="{asctime} | {levelname} | [{name}:{funcName}:{lineno}] -- {message}",
    style="{",
    datefmt="%Y-%m-%dT%H:%M:%S%s.%03d",
)
logger = logging.getLogger("RefinitivNewsClient")

# We're storing Australian News Sources separately because of the huge timezone
# differences, we want to consider the UTC offset while fetching news for a given
# event day
AUSTRALIAN_NEWS_SOURCES = [
    "Australian Stock Exchange",
    "Australian Associated Press",
    "Cboe Australian News Service",
    "National Stock Exchange of Australia",
]


class RefinitivNewsClient:
    def __init__(
        self,
        relevance: RefinitivNewsRelevance,
    ):
        """
        :param relevance: one of [low, medium, high]
        """
        self._auth_client = OAuth2AuthClient(
            client_id=os.environ.get("COGNITO_CLIENT_ID"),
            client_secret=os.environ.get("COGNITO_CLIENT_SECRET"),
            oauth2_url=os.environ.get("COGNITO_AUTH_URL"),
        )
        self.news = News(self._get_api_client(host=os.environ.get("MASTER_DATA_API_HOST")))

        self.relevance = relevance

    def _get_api_client(self, host: str):
        try:
            return ApiClient(host=host, auth_client=self._auth_client)
        except Exception as e:
            raise ConnectionError(f"Cannot initialize API client: {type(e)} - {e}")

    @staticmethod
    def _log_errors(method: callable, *args, **kwargs):
        content = []
        try:
            content = method(*args, **kwargs).content
        except httpx.HTTPStatusError as exc:
            logger.exception(
                "Error response %s while requesting %s. - %s",
                exc.response.status_code,
                exc.request.url,
                exc.response.content,
            )
        except JSONDecodeError:
            logger.exception("An error occurred while parsing response")
        except Exception as e:
            logger.exception(
                f"An error of type {type(e)} occurred while fetching and parsing news. {str(e)}"
            )

        return content

    def get_refinitiv_sources(self) -> List[str]:
        """
        Get all news sources from DB
        :return: List with all news sources present in the DB
        """
        content = self._log_errors(self.news.get_sources)
        return content

    def get_refinitiv_subjects(self) -> List[str]:
        """
        Get all news subjects from DB
        :return: List with all news subjects present in the DB
        """
        content = self._log_errors(self.news.get_groupings)
        return content

    def get_refinitiv_news(
        self,
        ric: str,
        start_date: str,
        end_date: str,
        all_sources: bool,
        all_subjects: bool,
        orders_for_ric: List,
        sources: Optional[List[str]] = None,
        subjects: Optional[List[str]] = None,
        method_id: str | None = None,
        update_australian_tz_offset: bool = False,
    ) -> pd.DataFrame | Tuple[pd.DataFrame, str, dict]:
        """Get news for a given ric and date range.

        :param orders_for_ric:
        :param subjects: list of groupings to filter (AKA **seGroupings**)
        :param ric: RIC to fetch news for
        :param start_date: start date string to filter the news; format "2023-10-18T07:00:00.265000+00:00" can be used
        :param end_date: end date string to filter the news
        :param method_id: str uuid for mar_audit
        :param sources: Optional, label of the news sources
        :param all_sources: bool, if we have all sources or not
        :param all_subjects: bool, if we have all subjects or not
        :param update_australian_tz_offset: bool, use AU timezone for australian news
        :return: pd.DataFrame with the retrieved news
        """

        if self.relevance not in list(RefinitivNewsRelevance):
            logger.warning(
                f"Selected relevance ({self.relevance}) is not one of {RefinitivNewsRelevance.list()}."
            )
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"Selected relevance ({self.relevance}) is not one of {RefinitivNewsRelevance.list()}.",
                        list_of_order_ids=orders_for_ric,
                    )
                )

                return pd.DataFrame()
            else:
                audit_data = {
                    "relevance": self.relevance,
                    "allowed_relevance": RefinitivNewsRelevance.list(),
                }
                return pd.DataFrame(), RefinitivAuditName.INVALID_RELEVANCE, audit_data

        if not all_sources and (not isinstance(sources, list) or not sources):
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="No sources provided despite all_sources being False."
                        "If all_sources is False, a list of sources must be provided.",
                        list_of_order_ids=orders_for_ric,
                    )
                )
                logger.warning(
                    "No sources provided despite all_sources being False. "
                    "If all_sources is False, a list of sources must be provided."
                )
                return pd.DataFrame()
            else:
                audit_data = {"value": "sources"}
                return pd.DataFrame(), RefinitivAuditName.MISSING_VALUES, audit_data

        if not all_subjects and (not isinstance(subjects, list) or not subjects):
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="No subjects provided despite all_subjects being False. "
                        "If all_subjects is False, a list of subjects must be provided.",
                        list_of_order_ids=orders_for_ric,
                    )
                )
                logger.warning(
                    "No subjects provided despite all_subjects being False. "
                    "If all_subjects is False, a list of subjects must be provided."
                )
                return pd.DataFrame()
            else:
                audit_data = {"value": "subjects"}
                return pd.DataFrame(), RefinitivAuditName.MISSING_VALUES, audit_data

        request_body = {
            RefinitivNewsRequestBody.DATE_RANGE: {
                RefinitivNewsRequestBody.START: str(start_date),
                RefinitivNewsRequestBody.END: str(end_date),
            },
            RefinitivNewsRequestBody.MIN_RELEVANCE: self.relevance.value,
            RefinitivNewsRequestBody.RIC: str(ric),
        }

        if not all_sources and sources:
            request_body[RefinitivNewsRequestBody.SOURCES] = sources

        if not all_subjects and subjects:
            request_body[RefinitivNewsRequestBody.SE_GROUPINGS] = subjects
        content = self._log_errors(self.news.bulk_get_stories, request_body)

        result: pd.DataFrame = self.parse_refinitiv_news(
            content=content,
            orders_states_keys_for_audit=orders_for_ric,
            method_id=method_id,
        )
        if update_australian_tz_offset:
            naive_start_date = start_date.tz_localize(None)
            naive_end_date = end_date.tz_localize(None)

            # Treat as Australian time
            aus_tz = pytz.timezone("Australia/Sydney")
            aus_start_date = naive_start_date.tz_localize(aus_tz)
            aus_end_date = naive_end_date.tz_localize(aus_tz)

            # Convert back to UTC
            adjusted_utc_start_date = aus_start_date.tz_convert(pytz.UTC)
            adjusted_utc_end_date = aus_end_date.tz_convert(pytz.UTC)
            request_body[RefinitivNewsRequestBody.DATE_RANGE][RefinitivNewsRequestBody.START] = str(
                adjusted_utc_start_date
            )
            request_body[RefinitivNewsRequestBody.DATE_RANGE][RefinitivNewsRequestBody.END] = str(
                adjusted_utc_end_date
            )
            request_body[RefinitivNewsRequestBody.SOURCES] = AUSTRALIAN_NEWS_SOURCES
            content_aussie = self._log_errors(self.news.bulk_get_stories, request_body)
            result_aussie: pd.DataFrame = self.parse_refinitiv_news(
                content=content_aussie,
                orders_states_keys_for_audit=orders_for_ric,
                method_id=method_id,
            )
            if not result.empty:
                non_aussie_result = result[
                    ~result["sources"]
                    .astype("string")
                    .str.contains("|".join(AUSTRALIAN_NEWS_SOURCES), regex=True)
                ]
                result = pd.concat([non_aussie_result, result_aussie]).reset_index(drop=True)
            else:
                result = result_aussie

        if result.empty:
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="No news after parsing the response from the API.",
                        list_of_order_ids=orders_for_ric,
                    )
                )
                logger.warning("No news after parsing the response from the API.")
                return pd.DataFrame()
            else:
                audit_data = dict(
                    start_date=start_date,
                    end_date=end_date,
                    ric=ric,
                )
                return pd.DataFrame(), RefinitivAuditName.NO_NEWS_TO_PARSE, audit_data
        if method_id:
            return result
        return result, RefinitivAuditName.NEWS_FOUND, dict(ric=ric)

    @staticmethod
    def parse_refinitiv_news(
        content: List[dict], orders_states_keys_for_audit: List, method_id: str
    ) -> pd.DataFrame:
        """Method to parse the content from the *get_refinitiv_news* method.

        :param orders_states_keys_for_audit:
        :param method_id: uuid for mar_audit
        :param content: response to parse
        :return: pd.DataFrame with the response parsed
        """
        if not isinstance(content, list) or not content:
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="No news to parse.",
                        list_of_order_ids=orders_states_keys_for_audit,
                    )
                )
            logger.warning("Content is empty. Nothing to parse.")
            return pd.DataFrame()

        all_news = []
        try:
            for news in content:
                # save a copy of the dictionary
                article = copy.copy(news[RefinitivNewsRequestResult.STORY_SUBJECT])

                # remove None from list of seGroupings
                groupings = news[RefinitivNewsRequestResult.SE_GROUPINGS]
                groupings = (
                    [group for group in groupings if group is not None]
                    if isinstance(groupings, list)
                    else []
                )

                # define sources as empty list if None
                sources = news[RefinitivNewsRequestResult.SOURCES]
                sources = sources if sources is not None else []

                # save the id from this dict as subjectId
                article[RefinitivNewsColumns.SUBJECT_ID] = article[RefinitivNewsColumns.ID]

                # update the copied dict with the remaining data. All keys on "article" will be replaced by the
                # matching keys from StoryMetaData.
                article.update(
                    news[RefinitivNewsRequestResult.STORY_META_DATA],
                    sources=sources,
                    subjects=groupings,
                )
                all_news.append(article)

        except KeyError:
            if method_id:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"The provided news does not contain one of {RefinitivNewsRequestResult.STORY_META_DATA}, "
                        f"{RefinitivNewsRequestResult.STORY_SUBJECT}, {RefinitivNewsRequestResult.SOURCES}, or "
                        f"{RefinitivNewsRequestResult.SE_GROUPINGS} fields.",
                        list_of_order_ids=orders_states_keys_for_audit,
                    )
                )
            logger.error(
                f"The provided news does not contain one of {RefinitivNewsRequestResult.STORY_META_DATA}, "
                f"{RefinitivNewsRequestResult.STORY_SUBJECT}, {RefinitivNewsRequestResult.SOURCES}, or "
                f"{RefinitivNewsRequestResult.SE_GROUPINGS} fields."
            )
            return pd.DataFrame()

        return pd.DataFrame(all_news)


@lru_cache()
def get_refinitiv_client(relevance: RefinitivNewsRelevance) -> RefinitivNewsClient:
    """Return a cached refinitiv news client Using @lru_cache allows us to re-
    use the same client for all partitions processed by the same worker,
    resulting in a time saving on flow execution.

    Returns:
        RefinitivNewsClient: refinitiv news client that gets refinitiv news from DB
    """
    return RefinitivNewsClient(relevance=relevance)
