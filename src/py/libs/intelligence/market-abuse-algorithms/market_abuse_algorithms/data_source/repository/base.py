# type: ignore
import addict
import copy
import functools
import logging
import numpy as np
import os
import pandas as pd
import time
from elasticsearch6.exceptions import ConnectionTimeout
from market_abuse_algorithms.data_source.query.base import BaseQuery
from typing import Iterator, List, Optional, Union
from urllib3.exceptions import ReadTimeoutError

logging.getLogger("elasticsearch").setLevel(logging.DEBUG)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s.%(msecs)03d %(levelname)s [%(funcName)s:%(lineno)d] %(message)s",
    datefmt="%Y-%m-%dT%H:%M:%S",
)

# NOTE: this decorated is to use in case there are more
#  read timeouts


def make_request(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        wait = 2
        valid_response = False
        response = None
        max_func_retries = 4
        retry = 0
        while not valid_response and retry <= max_func_retries:
            try:
                response = func(*args, **kwargs)
                valid_response = True

            except (ReadTimeoutError, ConnectionTimeout) as e:
                retry += 1
                time.sleep(wait)
                wait *= 2

                if retry > max_func_retries:
                    raise e

        return response

    return wrapper


class Repository:
    HOST = "localhost"
    PORT = 443
    SCHEME = "https"
    USE_SSL = True
    VERIFY_CERTS = True
    TIMEOUT = int(os.environ.get("ELASTIC_TIMEOUT", "120"))
    RETRY_ON_TIMEOUT = True
    MAX_RETRIES = 5
    RETRY_ON_STATUS = (408, 500, 502, 503, 504, 598)
    VERSION = None
    _default_batch_size = 1000

    _default_scroll_time = "5m"

    def __init__(self, tenant: Optional[str] = None):
        self._tenant = tenant
        self._client = self._create_client()

    @property
    def client(self):
        return self._client

    @property
    def tenant(self):
        return self._tenant

    def _create_client(self):
        raise NotImplementedError

    def _search(self, index: str, query: dict) -> dict:
        return self._client.search(
            index=index,
            scroll=self._default_scroll_time,
            size=self._default_batch_size,
            body=query,
            request_timeout=120,
        )

    def search_query(self, query: Union[BaseQuery, dict]):
        index = self._get_index(query)

        query = self._format_query(query)

        response = self._client.search(
            index=index,
            body=query,
            request_timeout=self.TIMEOUT,
            track_total_hits="true",
        )

        response = addict.Dict(dict(response))

        return response

    # Adapted from https://github.com/steeleye/se-mono/blob/a1dcb09615657763ca559ec49fcef263f84eb25e/src/py/libs/steeleye/shared/es-utils/se_es_utils/search_after.py#L8  # noqa: E501
    def search_after_query_yield(
        self,
        query: Union[BaseQuery, dict],
        batch_size: Optional[int] = None,
        model_index: Optional[str] = None,
        query_total_hits: int = -1,
        request_timeout: int = int(os.environ.get("REQUEST_TIMEOUT_SECONDS", 120)),
        sort_id_field: str = "&id",
    ) -> Iterator[List[dict]]:
        """Search-after for ElasticSearch queries. Param query_total_hits is
        used to limit the total records retrieved from the query, setting it
        to.

        -1, its default value, will fetch all records.

        :param batch_size: number of hits per batch
        :param query: query object or dict
        :param query_total_hits: int to limit the total records retrieved
        :param request_timeout: request time out
        :param sort_id_field: field to sort ES results
        :return: iterator
        """
        if query_total_hits == 0:
            raise Exception("Value of query_total_hits can not be 0")

        index = self._get_index(query)

        if model_index:
            index = model_index.replace("None", index)

        query_dict = self._format_query(query)

        query_dict = copy.deepcopy(query_dict)

        query_dict["size"] = batch_size if batch_size is not None else query_dict.get("size", 250)

        query_dict["sort"] = (
            {sort_id_field: "asc"}
            if not query_dict.get("sort")
            else query_dict["sort"] + [{sort_id_field: "asc"}]
        )

        response = self._client.search(
            index=index,
            body=query_dict,
            request_timeout=request_timeout,
        )

        try:
            yield [hit.get("_source") for hit in response["hits"]["hits"]]
        except KeyError as e:
            logging.error(f"Query response with incorrect format with error {e}")
            return []

        # Count the total number of records that match the query
        count_query = copy.deepcopy(query_dict)

        count_query.pop("size", None)
        count_query.pop("sort", None)
        count_query.pop("_source", None)

        hit_count = self._client.count(
            body=count_query,
            index=index,
            request_timeout=request_timeout,
        )["count"]

        if query_total_hits < 0:
            total_hits = hit_count
        else:
            total_hits = min(hit_count, query_total_hits)

        search_after: int = len(response["hits"]["hits"])

        while search_after < total_hits:
            # limit the last fetch of comms
            if search_after + query_dict["size"] > total_hits:
                query_dict["size"] = total_hits - search_after
            # Get the sort value of the last hit and
            # Update the search query with it
            query_dict["search_after"] = response["hits"]["hits"][-1]["sort"]
            response = self._client.search(
                index=index, body=query_dict, request_timeout=request_timeout
            )
            # if, for some reason, no hits came back...break out of the loop
            try:
                if len(response["hits"]["hits"]) < 1:
                    break

                search_after += len(response["hits"]["hits"])

                yield [hit.get("_source") for hit in response["hits"]["hits"]]

            except KeyError as e:
                logging.error(f"Query response with incorrect format {e}")
                return []

    def search_after_query_yield_dataframe(
        self,
        query: Union[BaseQuery, dict],
        query_total_hits: int = -1,
        request_timeout: int = int(os.environ.get("REQUEST_TIMEOUT_SECONDS", 120)),
        batch_size: Optional[int] = None,
    ) -> Iterator[pd.DataFrame]:
        """Wrapper around search_after_query_yield generator but returing a
        dataframe on each iteration.

        :param batch_size: number of hits per batch
        :param request_timeout: request time out
        :param query: query object or dict
        :param query_total_hits: int to limit the total records retrieved
        :return: iterator
        """

        for result in self.search_after_query_yield(
            query=query,
            batch_size=batch_size,
            query_total_hits=query_total_hits,
            request_timeout=request_timeout,
        ):
            result_df = pd.json_normalize(result)
            result = self.process_scroll_response(result=result_df, query=query)
            yield result

    def search_after_query(
        self,
        query: Union[BaseQuery, dict],
        query_total_hits: int = -1,
        sort_id_field: str = "&id",
        batch_size: Optional[int] = None,
        model_index: Optional[str] = None,
    ) -> pd.DataFrame:
        results = []
        for result in self.search_after_query_yield(
            query=query,
            query_total_hits=query_total_hits,
            sort_id_field=sort_id_field,
            batch_size=batch_size,
            model_index=model_index,
        ):
            results.extend(result)

        results_df = pd.json_normalize(results)

        results_df = self.process_scroll_response(result=results_df, query=query)

        return results_df

    def _get_index(self, query: Union[BaseQuery, dict]) -> str:
        """Base on the query class, it formats the MODEL_INDEX with the
        self._tenant.

        In case it is an SRP model (so no {company} present), it doesn't
        fails and just returns the alias as it is.
        """
        if not isinstance(query, BaseQuery):
            return self._tenant

        if isinstance(query.MODEL_INDEX, str) and len(query.MODEL_INDEX) != 0:
            if self.VERSION != "8":
                index = query.MODEL_INDEX.format(company=self._tenant)
            else:
                index = query.MODEL_INDEX.replace("None", self._tenant)
        elif isinstance(query.MODEL_INDEX, list):
            index = ",".join(
                [
                    model_index.format(company=self._tenant)
                    if self.VERSION != "8"
                    else model_index.replace("None", self._tenant)
                    for model_index in query.MODEL_INDEX
                ]
            )
        else:
            raise TypeError(
                f"{type(query.MODEL_INDEX)} not recognizable for model to add to ES index."
            )

        return index

    @staticmethod
    def _retrieve_as(response, type_=Union[list, pd.DataFrame]) -> Union[list, pd.DataFrame]:
        results = [hit.get("_source") for hit in response.get("hits", {}).get("hits", {})]

        if isinstance(pd.DataFrame(), type_):
            results = pd.json_normalize(results)

        return results

    @staticmethod
    def process_scroll_response(result, query: Union[BaseQuery, dict]) -> pd.DataFrame:
        if not isinstance(result, pd.DataFrame):
            return result

        if result.empty:
            return result

        result = result.replace(r"^\s*$", np.nan, regex=True)

        if hasattr(query, "process_frame_result"):
            result = query.process_frame_result(result)

        return result

    @staticmethod
    def _format_query(query: Union[BaseQuery, dict]) -> dict:
        if isinstance(query, BaseQuery):
            query = query.to_dict()
        elif isinstance(query, dict):
            pass
        else:
            raise NotImplementedError

        return query
