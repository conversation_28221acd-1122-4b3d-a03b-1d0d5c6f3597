# ruff: noqa: E501
# type: ignore
import addict
import datetime
import itertools
import logging
import os
import pandas as pd
import pyarrow.compute as pc
from functools import lru_cache
from market_abuse_algorithms.data_source.repository.market_data.static import (
    DATE_TIME,
    EODStatsDataColumns,
    OrderBookDepthDataColumns,
    StatsColumns,
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    MarketDataStoreError,
    dt_to_timestamp,
    fetch_eod_stats_with_time_margin,
    fetch_tick_data_with_increasing_time_windows,
    get_bid_or_ask_cols,
    get_integer_from_time_window,
    get_nearest_tick_data_from_submitted,
    level_order_book,
)
from market_abuse_algorithms.data_source.static.market_data.refinitiv import (
    RefinitivColumnsEnum,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField, OrderType
from se_market_data_utils.client import MarketDataAPI
from se_market_data_utils.market_data_settings import market_data_config
from se_market_data_utils.parquet_handlers import (
    get_eod_stats,
    get_tick_parquet,
    read_master_data_file,
)
from se_market_data_utils.schema.api import APIResponseStatus
from se_market_data_utils.schema.parquet import EoDStatsColumns
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from se_market_data_utils.statistics import (
    calculate_exponential_moving_average,
    calculate_volatility,
)
from se_market_data_utils.utils import get_data_based_on_window
from typing import Dict, List, Optional, Union

OBD_DATA_STORAGE_BUCKET = market_data_config.MASTER_DATA_STORAGE_BUCKET

logger = logging.getLogger(__name__)

CHUNK_SIZE = 10000


# NOTE: Any changes in this file needs to be propagated to
# NOTE: se-mono/blob/main/src/py/libs/intelligence/mar-utils/mar_utils/data_repository/market_data/market_data_repository.py


class MarketDataClient(MarketDataAPI):
    @property
    def market_data_api(self) -> MarketDataAPI:
        """This is for compatibility with legacy code."""
        return self

    def get_local_ric_map(
        self,
        list_id_venue: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
    ) -> Optional[dict]:
        """Builds a mapping between the input instrumentUniqueIdentifiers and
        their corresponding Local RIC value. Bulk fetches the Local RIC from
        master-data-api and build a dictionary with the mapping. Should any
        error occur while fetching or processing the RICs, it is captured and
        returned inside a pandas DataFrame.

        :param list_id_venue: A list of OrderField.INST_ID_VENUE_KEY
        :type list_id_venue: List[str]
        :param instrument_combinations: A list of lists containing the id_venue combinations
        :type instrument_combinations: List
        :return: Either a mapping between id_venues and RICs or a DataFrame containing the failure reason
        :rtype: Union[dict, pd.DataFrame]
        """
        if not list_id_venue:
            if not instrument_combinations:
                logger.error("No data provided")
                return None
            else:
                list_id_venue = list(itertools.chain(*instrument_combinations))

        try:
            response: List[dict] = self.bulk_get_ric_level_2(
                isin_and_venue_keys=list(set(list_id_venue))
            )

            result_dict = {}
            for doc in response:
                ric = doc.get("ric", None)
                if ric:
                    result_dict[doc["key"]] = ric

            if len(result_dict) == 0:
                logger.error("No RIC data available")
                raise MarketDataStoreError(status=APIResponseStatus.MISSING_RIC)

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch Local RICs in bulk for {list_id_venue}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return {}

        except ConnectionError as e:
            logger.warning(
                msg=f"Connection error when trying to fetch fetch RICs in bulk for {list_id_venue}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return {}

        except Exception as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_id_venue}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return {}

        return result_dict

    def get_ric_map(
        self,
        list_inst_unique_id: Optional[List[str]] = None,
        instrument_combinations: Optional[List] = None,
        use_prefix_query: Optional[bool] = False,
    ) -> Union[dict, None]:
        """Builds a mapping between the input instrumentUniqueIdentifiers and
        their corresponding RIC value. Bulk fetches the RIC from master-data-
        api and build a dictionary with the mapping. Should any error occur
        while fetching or processing the RICs, it is captured and returned
        inside a pandas DataFrame.

        :param use_prefix_query:
        :param instrument_combinations:
        :param list_inst_unique_id: A list of OrderField.INST_EXT_UNIQUE_IDENT
        :type list_inst_unique_id: List[str]
        :return: Either a mapping between instrumentUniqueIdentifiers and RICs or a DataFrame containing the failure reason
        :rtype: Union[dict, pd.DataFrame]
        """
        if not list_inst_unique_id:
            if not instrument_combinations:
                logger.error("No data provided")
                return None
            else:
                list_inst_unique_id = list(itertools.chain(*instrument_combinations))

        try:
            response: List[dict] = self.market_data_api.bulk_get_ric_level_1(
                instruments=list(set(list_inst_unique_id)),
                isin_prefix_flag=use_prefix_query,
            )["hits"]

            result_dict = {}
            for doc in response:
                ric = self.select_ric(addict.Dict(doc))
                if isinstance(ric, str):
                    result_dict[doc["instrumentUniqueIdentifier"]] = ric

            if len(result_dict) == 0:
                logger.error("No RIC data available")
                raise MarketDataStoreError(status=APIResponseStatus.MISSING_RIC)

        except MarketDataStoreError as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_inst_unique_id}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return None

        except ConnectionError as e:
            logger.warning(
                msg=f"Connection error when trying to fetch fetch RICs in bulk for {list_inst_unique_id}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return None

        except Exception as e:
            logger.warning(
                msg=f"Failed to fetch RICs in bulk for {list_inst_unique_id}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return None

        return result_dict

    def get_market_data_stats(
        self,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        currency: Optional[str] = None,
        use_time_margin: bool = True,
    ) -> pd.DataFrame:
        """Fetches trade stats and fetches quotes stats when trade stats are
        not available.

        :param use_time_margin: if True fetches eod stats with a iterating time window margin
        :param start_date: pd.Timestamp, start date to filter market data
        :param end_date: pd.Timestamp, end date to filter market data
        :param instrument_ric: ric mapped to a given instrument
        :param instrument_unique_identifier: instrument id
        :param currency: optional selection of data by a specific currency
        :return: dataframe with trade or quote data
        """
        date_from = start_date.date()
        date_to = end_date.date()
        try:
            if instrument_ric is None:
                response = self.market_data_api.get_ric_level_1(
                    iuid_or_isin=instrument_unique_identifier
                )["results"][0]
                ric_selected = self.select_ric(ric_lookup=addict.Dict(response))
            else:
                ric_selected = instrument_ric

        except Exception as e:
            logger.warning(
                msg=f"Failed to get RIC for instrument {instrument_unique_identifier}. "
                f"Error: {type(e)} - {str(e)}."
            )
            return pd.DataFrame()

        try:
            ric_currency = None
            if use_time_margin:
                data: pd.DataFrame = fetch_eod_stats_with_time_margin(
                    market_data_api=self.market_data_api,
                    start_date=date_from,
                    end_date=date_to,
                    ric=ric_selected,
                )
            else:
                data: pd.DataFrame = get_eod_stats(
                    market_client=self.market_data_api,
                    date_from=str(date_from),
                    date_to=str(date_to),
                    ric=ric_selected,
                    columns=EODStatsDataColumns.get_columns(),
                ).to_pandas()

            if not data.empty:
                ric_currency = (
                    data[EoDStatsColumns.CURRENCY].unique().tolist()
                    if EoDStatsColumns.CURRENCY in data.columns
                    else None
                )
                data = self._process_market_data_stats(
                    df=data,
                    currency=currency,
                )

            if data.empty:
                logger.warning(
                    msg=f"No Elektron data for instrument {instrument_unique_identifier} after _process_market_data_stats. "
                    f"Order currency: {currency}. "
                    f"RIC: {ric_selected}. "
                    f"RIC currency: {ric_currency}. ",
                    exc_info=True,
                )
                return pd.DataFrame()

            return data

        except Exception as e:
            logger.warning(
                msg=f"Failed to fetch market data with event_type: {RefinitivEventType.ELEKTRON}. "
                f"RIC: {ric_selected}."
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            return pd.DataFrame()

    def get_tick_data(
        self,
        dates: Union[List[pd.Timestamp], pd.Series],
        event_type: Union[
            RefinitivEventType.QUOTE,
            RefinitivEventType.TRADE,
            RefinitivEventType.AUCTION,
        ],
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        result_between_dates: bool = False,
        nearest_timestamp: bool = False,
        nearest_after_flag: bool = False,
    ) -> pd.DataFrame:
        """Generic method that fetches tick data for QUOTE and TRADE. This
        method gets the time window to search the data from the list of dates
        provided. It gets the min and max date to be used when fetching the
        data.

        :param nearest_after_flag: flag to fetch the nearest tick data afterwards
        :param dates: list of dates we want to search. The min and max dates will be used to limit the search
        :param event_type: type of data to fetch, QUOTE or TRADE
        :param instrument_unique_identifier: instrument unique identifier to fetch RIC and then market data
        :param instrument_ric: RIC to fetch market data
        :param result_between_dates: defaults to False. If True it returns the market data between the min and max dates defined from the dates list
        :param nearest_timestamp: defaults to False. If True it returns only the nearest timestamp to the date provided
        :return: dataframe with the market data
        """
        try:
            if instrument_ric is None:
                response = self.market_data_api.get_ric_level_1(
                    iuid_or_isin=instrument_unique_identifier
                )["results"][0]
                ric_selected = self.select_ric(ric_lookup=addict.Dict(response))
            else:
                ric_selected = instrument_ric

        except Exception as e:
            logger.warning(
                msg=f"Failed to get RIC for instrument {instrument_unique_identifier}. "
                f"Error: {type(e)} - {str(e)}."
            )
            return pd.DataFrame()

        # get start and end dates to filter market data
        start_date = min(dates)
        end_date = max(dates)

        if event_type == RefinitivEventType.QUOTE:
            if start_date.year < 2022 and end_date.year < 2022:
                return pd.DataFrame()

            elif start_date.year < 2022:
                start_date = pd.Timestamp(year=2022, month=1, day=1, hour=0)

        start_timestamp = int(start_date.timestamp() * 1e9)
        end_timestamp = int(end_date.timestamp() * 1e9)

        try:
            tick_data: pd.DataFrame = fetch_tick_data_with_increasing_time_windows(
                market_data_api=self.market_data_api,
                start_date=start_date,
                end_date=end_date,
                ric=ric_selected,
                event_type=event_type,
            )

            if tick_data.empty:
                logger.info("No ticks data")
                return tick_data

            if result_between_dates:
                tick_data = tick_data.loc[
                    (tick_data[DATE_TIME] >= start_timestamp)
                    & (tick_data[DATE_TIME] <= end_timestamp)
                ]

                if tick_data.empty:
                    logger.info(
                        f"Tick data is empty after filtering between the provided dates ({start_date}, {end_date})"
                    )
                    return tick_data

            if nearest_timestamp:
                nearest_data = pd.DataFrame()
                for timestamp in dates:
                    nearest = get_nearest_tick_data_from_submitted(
                        market_data=tick_data,
                        order_time_submitted=timestamp,
                        after_flag=nearest_after_flag,
                    )

                    # in cases like marking we used to have the timestamps of the orders inserted into the tick data
                    # if we pass a pd.Series on the dates argument we do that insert
                    if not nearest.empty:
                        if isinstance(dates, pd.Series) and dates.name is not None:
                            nearest[dates.name] = timestamp

                        nearest_data = pd.concat(
                            [nearest_data, nearest.to_frame().T]
                        )  # transpose the Series to match the DF

                tick_data = nearest_data

                if tick_data.empty:
                    logger.info(
                        f"Tick data is empty after getting the nearest timestamp for the dates ({dates})"
                    )
                    return tick_data

            return tick_data

        except Exception as e:
            logger.error(
                msg=f"Failed trying to fetch ticks for instrument id: {instrument_unique_identifier} | "
                f"RIC: {ric_selected}. "
                f"Error: {type(e)} - {str(e)}",
                exc_info=True,
            )
            raise e

    def get_market_data_volatility(
        self,
        volatility_col_name: str,
        min_periods: int,
        volatility_window: int,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        currency: Optional[str] = None,
    ) -> pd.DataFrame:
        """Uses instrument unique identifier as basis to fetch market data
        stats and calculate volatility.

        :param start_date: pd.Timestamp, start date to filter market data
        :param end_date: pd.Timestamp, end date to filter market data
        :param volatility_col_name: string with name of the column of the volatility
        :param volatility_window: number of days to be used on window to calculate volatility
        :param min_periods: minimum number of days to calculate volatility
        :param instrument_unique_identifier: string with instrument unique identifier
        :param instrument_ric: string with instrument ric
        :param currency: optional field to fetch market data with a specific currency
        :return: dataframe with market data stats and volatility
        """

        data = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
            currency=currency,
            start_date=start_date,
            end_date=end_date,
        )

        if data.empty:
            return data

        data[volatility_col_name] = calculate_volatility(
            market_data=data,
            data_column=RefinitivExtractColumns.CLOSE_PRICE,
            volatility_window=volatility_window,
            min_periods=min_periods,
        )

        return data

    def get_market_data_adv(
        self,
        adv_col_name: str,
        window: int,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
        min_periods: int = 2,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
    ):
        data = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
            start_date=start_date,
            end_date=end_date,
        )

        if data.empty:
            return data

        data[adv_col_name] = calculate_exponential_moving_average(
            market_data=data,
            data_column=TradeStatsColumns.TRADE_VOLUME,
            alpha=0.7,
            window=window,
            min_periods=min_periods,
        )

        return data

    def get_avg_daily_trading_volume(
        self,
        look_back_period: Union[int, str],
        adtv_column: str,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
        time_series_col: Optional[str] = None,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        min_periods: int = 1,
    ) -> pd.DataFrame:
        """
        Calculate average daily traded volume for a certain instrument
        NOTE: this is only used for SLOV v2 & price ramping v2
        :param start_date: pd.Timestamp, start date to filter market data
        :param end_date: pd.Timestamp, end date to filter market data
        :param min_periods: minimum observations to calculate the ADTV
        :param instrument_unique_identifier: str. instrument id
        :param look_back_period: Int or Str. Look back period
        :param adtv_column: str. Name of ADTV column to populate
        :param time_series_col: str. Datetime column name to set as index.
        :param instrument_ric: string with instrument ric
        :param instrument_unique_identifier: str. instrument id
        :return: pd.DataFrame. DataFrame with ADTV calculated
        """
        data: pd.DataFrame = self.get_market_data_stats(
            instrument_unique_identifier=instrument_unique_identifier,
            instrument_ric=instrument_ric,
            start_date=start_date,
            end_date=end_date,
        )

        if (
            data.empty
            or TradeStatsColumns.TRADE_VOLUME not in data.columns
            or (
                TradeStatsColumns.TRADE_VOLUME in data.columns
                and data[TradeStatsColumns.TRADE_VOLUME].isna().all()
            )
        ):
            # [ENG-10440] if the data is empty will return an empty dataframe and if all rows for the trade volume
            # [ENG-10440] are NULL we should also return an empty dataframe and don't proceed with the calculation
            trade_vol_nan = False
            trade_vol_present = TradeStatsColumns.TRADE_VOLUME in data.columns
            if TradeStatsColumns.TRADE_VOLUME in data.columns:
                trade_vol_nan = data[TradeStatsColumns.TRADE_VOLUME].isna().all()

            logger.warning(
                f"Either the EOD data is empty or the trading volume is NaN in all days."
                f" Is trade volume present in market data: {trade_vol_present}"
                f" Is trade volume NaN in all days? {trade_vol_nan}."
                f" Is market data empty? {data.empty}"
            )
            return pd.DataFrame()

        data[StatsColumns.DATE] = pd.to_datetime(data[StatsColumns.DATE], format="mixed")

        data = data.sort_values(by=StatsColumns.DATE, ascending=True)

        # select only business days
        data: pd.DataFrame = data.loc[data[StatsColumns.DATE].dt.dayofweek < 5]

        # replace NaN in the TRADE_VOLUME col so all days within the lb period are considered in the calc.
        data[TradeStatsColumns.TRADE_VOLUME] = data[TradeStatsColumns.TRADE_VOLUME].fillna(0.0)

        calculated_adtv = self.calculate_average_daily_trading_volume(
            market_data=data.set_index(StatsColumns.DATE, drop=False),
            data_column=TradeStatsColumns.TRADE_VOLUME,
            look_back_period=look_back_period,
            time_series=time_series_col,
            min_periods=min_periods,
        )

        if calculated_adtv.empty:
            logger.warning("Could not calculate the ADTV.")
            return pd.DataFrame()

        calculated_adtv.name = adtv_column

        # As per [ENG-10440] and https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/876871681/Algo+Abstractions#a.%5BADTV%5D
        #  Records with no adtv are dropped
        data = data.merge(right=calculated_adtv, on=StatsColumns.DATE)

        return data

    def get_order_book_depth_data(
        self, ric: str, start_date: pd.Timestamp, end_date: pd.Timestamp
    ) -> pd.DataFrame:
        """Get the order book depth data for a given ric.

        :param ric: str
        :param start_date: pd.Timestamp, start date to filter the OBD data
        :param end_date: pd.Timestamp, end date to filter the OBD data
        :return: dataframe with order book data
        """
        # remove 1 hour from the start date so that we have a margin to get the nearest timestamp
        date_from = start_date - pd.Timedelta(hours=1)
        date_from_timestamp = int(date_from.timestamp() * 1e9)

        # add 1 hour from the end date so that we have a margin to get the nearest timestamp
        date_to = end_date + pd.Timedelta(hours=1)
        date_to_timestamp = int(date_to.timestamp() * 1e9)

        # get all unique dates
        dates = list({start_date.date(), end_date.date()})

        # pyarrow expression to filter the data on the dates
        pa_expression = (
            pc.field(OrderBookDepthDataColumns.DATE_TIME.value) >= date_from_timestamp
        ) & (pc.field(OrderBookDepthDataColumns.DATE_TIME.value) <= date_to_timestamp)

        try:
            order_book_data = pd.DataFrame()
            for batch in get_tick_parquet(
                market_client=self.market_data_api,
                event_type=RefinitivEventType.OBD,
                dates=dates,
                ric=ric,
                columns=OrderBookDepthDataColumns.get_values_obd(),
                pa_expression=pa_expression,
            ):
                order_book_data = pd.concat([order_book_data, batch.to_pandas()])

            if not order_book_data.empty:
                order_book_data = order_book_data.sort_values(
                    by=[OrderBookDepthDataColumns.DATE_TIME.value]
                ).reset_index(drop=True)

            if order_book_data.empty:
                logger.warning(
                    f"Failed getting the order book depth data for this RIC {ric}. "
                    f"Between {datetime.datetime.fromtimestamp(date_from_timestamp * 1e-9)} "
                    f"and {datetime.datetime.fromtimestamp(date_to_timestamp * 1e-9)}"
                )

            return order_book_data

        except Exception as e:
            logger.error(
                f"Failed getting the order book depth data for this RIC {ric}. "
                f"Between {datetime.datetime.fromtimestamp(date_from_timestamp * 1e-9)} "
                f"and {datetime.datetime.fromtimestamp(date_to_timestamp * 1e-9)}"
                f"Error: {type(e)} - {str(e)}"
            )
            raise e

    def select_level_of_order_book(
        self,
        order_book_data: pd.DataFrame,
        tenant_data: pd.DataFrame,
        level_of_order_book_col: str,
    ) -> pd.DataFrame:
        """Select the LEVEL_OF_ORDER_BOOK from the order book depth data.

        :param order_book_data: pd.DataFrame, tenant data
        :param tenant_data: pd.Dataframe, tenant data
        :param level_of_order_book_col: str, level of order book column to be created
        :return: pd.DataFrame, data with level of order book
        """
        tenant_data[level_of_order_book_col]: pd.Series = pd.NA

        if not tenant_data.loc[
            tenant_data.loc[:, OrderField.EXC_DTL_ORD_TYPE] == OrderType.MARKET
        ].empty:
            tenant_data.loc[
                (tenant_data.loc[:, OrderField.EXC_DTL_ORD_TYPE] == OrderType.MARKET),
                level_of_order_book_col,
            ] = 1

        if not tenant_data.loc[
            (tenant_data.loc[:, OrderField.EXC_DTL_ORD_TYPE] != OrderType.MARKET)
        ].empty:
            tenant_data.loc[
                (tenant_data.loc[:, OrderField.EXC_DTL_ORD_TYPE] != OrderType.MARKET),
                level_of_order_book_col,
            ] = tenant_data.apply(
                lambda row: self.get_level_order_book(
                    order_book_data=order_book_data,
                    order_time_submitted=row.loc[OrderField.TS_ORD_SUBMITTED],
                    buy_sell=row.loc[OrderField.EXC_DTL_BUY_SELL_IND],
                    limit_price=row.loc[OrderField.EXC_DTL_LIMIT_PRICE],
                ),
                axis=1,
            )

        return tenant_data

    def get_trade_surveillance_data(self, filename: str) -> pd.DataFrame:
        """Method to fetch data from master-data.

        :param filename: s3_key with the filename to fetch data
        :return: dataframe with the data fetched from master-data
        """
        try:
            file_path: str = read_master_data_file(
                market_client=self.market_data_api,
                bucket=OBD_DATA_STORAGE_BUCKET,
                s3_key=filename,
            )
            data = pd.read_csv(file_path)
        except Exception as e:
            logger.error(f"Error fetching data for {filename}. Failed due to {type(e)} - {str(e)}")
            data = pd.DataFrame()

        return data

    def get_filtered_trade_surveillance_data(
        self,
        filename,
        filter_map: Dict[str, List],
        columns_to_include: List,
    ) -> pd.DataFrame:
        """Apply a set of filters on a file.

        :param filter_map: A dictionary specifying filters to be applied on the file.
        Format: {ColumnName: List}
        :param filename: Locally downloaded filepath
        :param columns_to_include: Columns to be included after creating the DataFrame
        :return:
        """
        result = []
        file_path: str = read_master_data_file(
            market_client=self.market_data_api,
            bucket=OBD_DATA_STORAGE_BUCKET,
            s3_key=filename,
        )

        if not file_path:
            raise Exception(f"Failed to fetch {OBD_DATA_STORAGE_BUCKET}/{filename}")

        for chunk in pd.read_csv(file_path, chunksize=CHUNK_SIZE):
            final_mask = None
            for filter_column, filter_values in filter_map.items():
                mask = chunk[filter_column].isin(filter_values)
                if final_mask is None:
                    final_mask = mask
                else:
                    final_mask &= mask

            chunk = chunk.loc[final_mask, columns_to_include]
            result.append(chunk)

        return pd.concat(result)

    @staticmethod
    def get_level_order_book(
        order_book_data: pd.DataFrame,
        buy_sell: str,
        order_time_submitted: datetime.datetime,
        limit_price: float,
    ) -> Union[int, None]:
        """
        Get the level of order book for a given order
        Specs: https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2082996227/7.1+Order+Book+Depth+-+Supporting+assets+-+Level+of+Order+Book+for+an+Order+and+Volume+at+Level#Level-of-Order-Book-for-an-Order
        :param order_book_data: pd.Dataframe, order book depth data
        :param buy_sell: str, BuySell order field
        :param order_time_submitted: datetime, timestamp when the order was submitted
        :param limit_price: float, limit price for a given order
        :return: int
        """

        if pd.isnull(limit_price) or pd.isna(limit_price):
            logger.warning("Limit price is null.")
            return None

        level_of_order: Union[int, None] = None
        tick_data: pd.Series = get_nearest_tick_data_from_submitted(
            market_data=order_book_data, order_time_submitted=order_time_submitted
        )
        if tick_data.empty:
            logger.warning("No tick data to get the level of order book.")
            return None

        if buy_sell == BuySell.BUY:
            bid_prices: pd.Series = get_bid_or_ask_cols(
                market_data=tick_data,
                refinitiv_col_bid_ask=RefinitivColumnsEnum.BID_PRICE,
            )
            filter_bids: pd.Series = bid_prices.loc[bid_prices <= limit_price]
            if filter_bids.empty:
                logger.warning("No bid prices lower than the limit price.")
                return None
            level_of_order: int = level_order_book(prices=filter_bids, buy_sell=BuySell.BUY)

        if buy_sell == BuySell.SELL:
            ask_prices: pd.Series = get_bid_or_ask_cols(
                market_data=tick_data,
                refinitiv_col_bid_ask=RefinitivColumnsEnum.ASK_PRICE,
            )
            filter_asks: pd.Series = ask_prices.loc[ask_prices >= limit_price]
            if filter_asks.empty:
                logger.warning("No ask prices higher than the limit price.")
                return None
            level_of_order: int = level_order_book(prices=filter_asks, buy_sell=BuySell.SELL)

        return level_of_order

    @staticmethod
    def calculate_average_daily_trading_volume(
        market_data: pd.DataFrame,
        data_column: RefinitivExtractColumns,
        look_back_period: Union[int, str],
        min_periods: Optional[int] = 1,
        time_series: Optional[str] = None,
        decimal_precision: int = 8,
        adtv_column: str = TradeStatsColumns.AVERAGE_DAILY_TRADED_VOLUME,
    ) -> pd.Series:
        """Method to calculate the average daily trading volume. Retrieve the
        Daily Trading Volume for each business day in
        (EventDate-1)-look_back_period, divided by number of business days
        used.

        https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/876871681/Algo+Abstractions#a.%5BADTV%5D
        :param market_data: Pandas DataFrame. Market data to calculate the ADV
        :param data_column: RefinitivExtractColumns. Needs to be a column defined on the scope
        :param look_back_period: Int or Str. Look back period
        :param min_periods: Int. Minimum Observations to calculate the ADTV
        :param decimal_precision: int. Decimal precision of volatility. Default: 8
        :param time_series: str. Datetime column name to set as index. Default is None
        :param adtv_column: str

        :return: Pandas Series. A new column with the ADTV calculated
        """
        # we will only have the business days, so we will do the rolling window by index and not time
        # (removing the time notation from the window e.g. "20d")
        window = get_integer_from_time_window(look_back_period)

        if isinstance(look_back_period, str) and isinstance(time_series, str):
            # if we have a datetime colum and the lbp as a str then we use the column as index
            window_value = look_back_period
            time_series_column = time_series
        else:
            window_value = window
            time_series_column = None

        data: pd.Series = get_data_based_on_window(
            window=window_value,
            data=market_data,
            data_column=data_column,
            time_series_column=time_series_column,
        )

        # data shift so that the calculations are T-1
        data_offset = data.shift(1)

        adtv: pd.Series = (
            data_offset.rolling(window=window, min_periods=min_periods)
            .apply(lambda x: x.dropna().sum() / x.dropna().shape[0])
            .round(decimals=decimal_precision)
        )

        # Set series name
        adtv.name = adtv_column

        # [ENG-10440] ADTV is a mean, if the value is zero, means the traded volume is zero, when it was previously NaN
        # [ENG-10440]  and we want to drop these records, because it means no trade volume
        # we can't drop the NaN because if you don't have enough data to calc ADTV you wil get the NaN and those values
        # must be kept, unless all row is populated w NaN
        adtv = adtv[~adtv.isin([0.0])]

        if adtv.isnull().all():
            return pd.Series()

        return adtv

    @staticmethod
    def _process_market_data_stats(
        df: pd.DataFrame, currency: Optional[str] = None
    ) -> pd.DataFrame:
        """Process market data stats.

        :param df: dataframe with raw market data stats
        :param currency: optional selection of currency
        :return: dataframe with processed market data stats
        """
        if EoDStatsColumns.DATE not in df.columns:
            return pd.DataFrame()
        df[EoDStatsColumns.DATE] = pd.to_datetime(
            df[EoDStatsColumns.DATE].fillna(pd.NaT), format="mixed"
        )

        if EoDStatsColumns.CURRENCY not in df.columns:
            return pd.DataFrame()

        # to return normal dataframe when currency is not provided
        if not currency:
            return df

        if df.loc[:, EoDStatsColumns.CURRENCY].isna().any():
            # fills the NaNs with currency with the known RIC currency
            if df.loc[:, EoDStatsColumns.CURRENCY].nunique() == 1:
                df.loc[:, EoDStatsColumns.CURRENCY] = (
                    df.loc[:, EoDStatsColumns.CURRENCY].dropna().unique()[0]
                )

            # if there is more than one currency it replaces every currency for the latest one (Checked with Storey)
            elif df.loc[:, EoDStatsColumns.CURRENCY].nunique() >= 1:
                df.loc[:, EoDStatsColumns.CURRENCY] = df.iloc[
                    -1, df.columns.get_loc(EoDStatsColumns.CURRENCY)
                ]
        df = df.loc[
            (df[EoDStatsColumns.CURRENCY] == currency) | (df[EoDStatsColumns.CURRENCY].isna())
        ]

        return df

    def ticks_between_timestamps_for_ric(
        self,
        ric: str,
        from_timestamp: datetime.datetime,
        to_timestamp: datetime.datetime,
        event_type: RefinitivEventType = RefinitivEventType.TRADE,
    ) -> pd.DataFrame:
        """Returns the ticks for the event given between `from_timestamp` to
        `to_timestamp`.

        :param instrument_id:
        :param from_timestamp:
        :param to_timestamp:
        :param event_type:
        :return:
        """

        try:
            date_range = self._market_client._get_dates_between_timestamps(
                from_timestamp=from_timestamp, to_timestamp=to_timestamp
            )

            results = []
            for date in date_range:
                try:
                    frame = self._market_data_store.tick_events_for_date(
                        ric=ric,
                        event_type=event_type,
                        date=date,
                    )
                except MarketDataStoreError:
                    continue

                if frame.empty:
                    continue

                results.append(frame)

            result = pd.concat(results, ignore_index=True)

            # Normalise
            from_timestamp_int = dt_to_timestamp(from_timestamp)
            to_timestamp_int = dt_to_timestamp(to_timestamp)

            mask = (result[RefinitivExtractColumns.DATE_TIME] >= from_timestamp_int) & (
                result[RefinitivExtractColumns.DATE_TIME] <= to_timestamp_int
            )

            result = result.loc[mask]

            return result

        except MarketDataStoreError as e:
            logger.exception(e)
            return pd.DataFrame()
        except Exception as e:
            logger.exception(e)
            return pd.DataFrame()

    @staticmethod
    def select_ric(ric_lookup: addict.Dict) -> str:
        """Phase 1: Preferred -> Primary -> Ric -> Composite Ric."""
        if ric_lookup.preferredRic is not None:
            return ric_lookup.preferredRic

        if ric_lookup.primaryTradingRic is not None:
            return ric_lookup.primaryTradingRic

        if ric_lookup.ric is not None:
            return ric_lookup.ric

        if ric_lookup.compositeRic is not None:
            return ric_lookup.compositeRic

        raise MarketDataStoreError(status=APIResponseStatus.MISSING_RIC)


@lru_cache()
def get_market_client(tenant: str | None = None) -> MarketDataClient:
    """Return a cached market data client.

    Using @lru_cache allows us to re-use the same client for all partitions processed by the same worker,
    resulting in a time saving on flow execution.

    Returns:
        MarketDataClient: market data client that gets order book depth data
    """
    return MarketDataClient(tenant=tenant, stack=os.environ.get("STACK"))
