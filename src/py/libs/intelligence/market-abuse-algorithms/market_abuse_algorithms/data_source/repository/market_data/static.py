from enum import Enum
from se_market_data_utils.schema.refinitiv import RefinitivExtractColumns
from typing import List

DATE_TIME = "Date-Time"


class BaseColumns(Enum):
    @classmethod
    def get_columns(cls) -> List[str]:
        return [col.value for col in cls]


class OrderBookDepthDataColumns(BaseColumns):
    RIC = "#RIC"
    DATE_TIME = "Date-Time"
    GMT_OFFSET = "GMT Offset"
    # L1
    L1_BID_PRICE = "L1-BidPrice"
    L1_BID_SIZE = "L1-BidSize"
    L1_BUY_NO = "L1-BuyNo"
    L1_ASK_PRICE = "L1-AskPrice"
    L1_ASK_SIZE = "L1-AskSize"
    L1_SELL_NO = "L1-SellNo"
    # L2
    L2_BID_PRICE = "L2-BidPrice"
    L2_BID_SIZE = "L2-BidSize"
    L2_BUY_NO = "L2-BuyNo"
    L2_ASK_PRICE = "L2-AskPrice"
    L2_ASK_SIZE = "L2-AskSize"
    L2_SELL_NO = "L2-SellNo"
    # L3
    L3_BID_PRICE = "L3-BidPrice"
    L3_BID_SIZE = "L3-BidSize"
    L3_BUY_NO = "L3-BuyNo"
    L3_ASK_PRICE = "L3-AskPrice"
    L3_ASK_SIZE = "L3-AskSize"
    L3_SELL_NO = "L3-SellNo"
    # L4
    L4_BID_PRICE = "L4-BidPrice"
    L4_BID_SIZE = "L4-BidSize"
    L4_BUY_NO = "L4-BuyNo"
    L4_ASK_PRICE = "L4-AskPrice"
    L4_ASK_SIZE = "L4-AskSize"
    L4_SELL_NO = "L4-SellNo"
    # L5
    L5_BID_PRICE = "L5-BidPrice"
    L5_BID_SIZE = "L5-BidSize"
    L5_BUY_NO = "L5-BuyNo"
    L5_ASK_PRICE = "L5-AskPrice"
    L5_ASK_SIZE = "L5-AskSize"
    L5_SELL_NO = "L5-SellNo"
    # L6
    L6_BID_PRICE = "L6-BidPrice"
    L6_BID_SIZE = "L6-BidSize"
    L6_BUY_NO = "L6-BuyNo"
    L6_ASK_PRICE = "L6-AskPrice"
    L6_ASK_SIZE = "L6-AskSize"
    L6_SELL_NO = "L6-SellNo"
    # L7
    L7_BID_PRICE = "L7-BidPrice"
    L7_BID_SIZE = "L7-BidSize"
    L7_BUY_NO = "L7-BuyNo"
    L7_ASK_PRICE = "L7-AskPrice"
    L7_ASK_SIZE = "L7-AskSize"
    L7_SELL_NO = "L7-SellNo"
    # L8
    L8_BID_PRICE = "L8-BidPrice"
    L8_BID_SIZE = "L8-BidSize"
    L8_BUY_NO = "L8-BuyNo"
    L8_ASK_PRICE = "L8-AskPrice"
    L8_ASK_SIZE = "L8-AskSize"
    L8_SELL_NO = "L8-SellNo"
    # L9
    L9_BID_PRICE = "L9-BidPrice"
    L9_BID_SIZE = "L9-BidSize"
    L9_BUY_NO = "L9-BuyNo"
    L9_ASK_PRICE = "L9-AskPrice"
    L9_ASK_SIZE = "L9-AskSize"
    L9_SELL_NO = "L9-SellNo"
    # L10
    L10_BID_PRICE = "L10-BidPrice"
    L10_BID_SIZE = "L10-BidSize"
    L10_BUY_NO = "L10-BuyNo"
    L10_ASK_PRICE = "L10-AskPrice"
    L10_ASK_SIZE = "L10-AskSize"
    L10_SELL_NO = "L10-SellNo"

    @classmethod
    def get_values_obd(cls):
        return [
            cls.RIC.value,
            cls.DATE_TIME.value,
            cls.GMT_OFFSET.value,
            cls.L1_BID_PRICE.value,
            cls.L1_BID_SIZE.value,
            cls.L1_ASK_PRICE.value,
            cls.L1_ASK_SIZE.value,
            cls.L2_BID_PRICE.value,
            cls.L2_BID_SIZE.value,
            cls.L2_ASK_PRICE.value,
            cls.L2_ASK_SIZE.value,
            cls.L3_BID_PRICE.value,
            cls.L3_BID_SIZE.value,
            cls.L3_ASK_PRICE.value,
            cls.L3_ASK_SIZE.value,
            cls.L4_BID_PRICE.value,
            cls.L4_BID_SIZE.value,
            cls.L4_ASK_PRICE.value,
            cls.L4_ASK_SIZE.value,
            cls.L5_BID_PRICE.value,
            cls.L5_BID_SIZE.value,
            cls.L5_ASK_PRICE.value,
            cls.L5_ASK_SIZE.value,
            cls.L6_BID_PRICE.value,
            cls.L6_BID_SIZE.value,
            cls.L6_ASK_PRICE.value,
            cls.L6_ASK_SIZE.value,
            cls.L7_BID_PRICE.value,
            cls.L7_BID_SIZE.value,
            cls.L7_ASK_PRICE.value,
            cls.L7_ASK_SIZE.value,
            cls.L8_BID_PRICE.value,
            cls.L8_BID_SIZE.value,
            cls.L8_ASK_PRICE.value,
            cls.L8_ASK_SIZE.value,
            cls.L9_BID_PRICE.value,
            cls.L9_BID_SIZE.value,
            cls.L9_ASK_PRICE.value,
            cls.L9_ASK_SIZE.value,
            cls.L10_BID_PRICE.value,
            cls.L10_BID_SIZE.value,
            cls.L10_ASK_PRICE.value,
            cls.L10_ASK_SIZE.value,
        ]


class EODStatsDataColumns(BaseColumns):
    CLOSE_ASK_PRICE = "Close Ask Price"
    CLOSE_BID_PRICE = "Close Bid Price"
    CLOSE_PRICE = "Close Price"
    CURRENCY = "Currency"
    DAILY_TRADED_NOTIONAL = "Daily Traded Notional"
    DAILY_TRADED_NOTIONAL_AUD = "Daily Traded Notional (AUD)"
    DAILY_TRADED_NOTIONAL_CHF = "Daily Traded Notional (CHF)"
    DAILY_TRADED_NOTIONAL_EUR = "Daily Traded Notional (EUR)"
    DAILY_TRADED_NOTIONAL_GBP = "Daily Traded Notional (GBP)"
    DAILY_TRADED_NOTIONAL_JPY = "Daily Traded Notional (JPY)"
    DAILY_TRADED_NOTIONAL_SGD = "Daily Traded Notional (SGD)"
    DAILY_TRADED_NOTIONAL_USD = "Daily Traded Notional (USD)"
    DATE = "Date"
    EXCHANGE_CODE = "Exchange Code"
    HIGH_ASK_PRICE = "High Ask Price"
    HIGH_BID_PRICE = "High Bid Price"
    HIGH_PRICE = "High Price"
    LOW_ASK_PRICE = "Low Ask Price"
    LOW_BID_PRICE = "Low Bid Price"
    LOW_PRICE = "Low Price"
    OPEN_ASK_PRICE = "Open Ask Price"
    OPEN_BID_PRICE = "Open Bid Price"
    OPEN_INTEREST = "Open Interest"
    OPEN_PRICE = "Open Price"
    RIC = "#RIC"
    TRADE_VOLUME = "Trade Volume"
    TRADED_VOLUME_20_DAY_EMA = "Traded Volume 20 Day EMA"
    VWAP = "VWAP"


class AuctionColumns(BaseColumns):
    AUCTION_PRICE_CURRENCY = "Auction Price Currency"
    DATE_TIME = "Date-Time"
    EXCH_TIME = "Exch Time"
    GMT_OFFSET = "GMT Offset"
    INDICATIVE_AUCTION_PRICE = "Indicative Auction Price"
    INDICATIVE_AUCTION_VOLUME = "Indicative Auction Volume"
    PRICE = "Price"
    QUALIFIERS = "Qualifiers"
    RIC = "#RIC"
    SEQUENCE_NUMBER = "Sequence Number"
    VOLUME = "Volume"


class TradeColumns(BaseColumns):
    DATE_TIME = "Date-Time"
    GMT_OFFSET = "GMT Offset"
    HIGH = "High"
    LOW = "Low"
    MARKET_VWAP = "Market VWAP"
    OPEN = "Open"
    PRICE = "Price"
    RIC = "#RIC"
    TRADE_PRICE_CURRENCY = "Trade Price Currency"
    VOLUME = "Volume"


class QuoteColumns(BaseColumns):
    ACCUMULATED_ASK_ORDER = "Accumulated Ask Order"
    ACCUMULATED_BID_ORDER = "Accumulated Bid Order"
    ASK_PRICE = "Ask Price"
    ASK_SIZE = "Ask Size"
    BID_PRICE = "Bid Price"
    BID_SIZE = "Bid Size"
    DATE_TIME = "Date-Time"
    GMT_OFFSET = "GMT Offset"
    MID_PRICE = "Mid Price"
    QUOTE_PRICE_CURRENCY = "Quote Price Currency"
    RIC = "#RIC"


class StatsColumns(str, BaseColumns):
    CLOSE_PRICE = "Close Price"
    DATE = "Date"
    HIGH_PRICE = "High Price"
    LOW_PRICE = "Low Price"
    OPEN_PRICE = "Open Price"
    RIC = RefinitivExtractColumns.RIC


class TradeStatsColumns(str, BaseColumns):
    AVERAGE_DAILY_TRADED_VOLUME = "Average Daily Traded Volume"
    CLOSE_PRICE = "Close Price"
    DATE = "Date"
    HIGH_PRICE = "High Price"
    LOW_PRICE = "Low Price"
    MARKET_VWAP = RefinitivExtractColumns.MARKET_VWAP
    OPEN_PRICE = "Open Price"
    RIC = RefinitivExtractColumns.RIC
    TRADE_CLOSE_DATE_TIME = "Trade Close Date-Time"
    TRADE_COUNT = "Trade Count"
    TRADE_OPEN_DATE_TIME = "Trade Open Date-Time"
    TRADE_PRICE_CURRENCY = "Trade Price Currency"
    TRADE_VOLUME = "Trade Volume"
