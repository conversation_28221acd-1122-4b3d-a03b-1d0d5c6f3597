# mypy: disable-error-code="attr-defined"
import addict
import logging
from datetime import datetime
from market_abuse_algorithms.audit.models import SlackMessage
from market_abuse_algorithms.audit.slack import SlackReport
from market_abuse_algorithms.data_source.repository.sdp.es_sdp import SDP
from market_abuse_algorithms.data_source.static.sdp.base import <PERSON><PERSON><PERSON>ield
from market_abuse_algorithms.exceptions import FilterDataError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from se_elastic_schema.components.mar.error import Error
from se_elastic_schema.components.mar.skipped_records import SkippedRecords
from se_elastic_schema.static.mar import SkipReason
from se_elastic_schema.static.surveillance import MarketAbuseReportType, WatchExecutionStatus
from traceback import format_exc
from typing import List, Optional, Union


class Audit:
    def __init__(
        self,
        context: StrategyContext,
        strategy_name: str,
        es_client: SDP,
        report_type: Optional[MarketAbuseReportType] = None,
    ):
        self._logger = logging.getLogger(__name__)
        self._context = context
        self._strategy_name = strategy_name

        self._record = None
        self._report_type = report_type
        self._tenant = es_client.tenant
        self._es_client = es_client
        self._initialize()

    def _initialize(self):
        self._record = addict.Dict(
            start=datetime.utcnow(),
            requestedBy=self._context.requested_by,
            filters=self._context.filters,
            thresholds=str(self._context.thresholds),
            reportType=self._report_type
            if self._report_type
            else StrategyName.get_report_type(self._strategy_name),
            status=WatchExecutionStatus.IN_PROGRESS,
        )

    def finish(self):
        self._record.end = datetime.utcnow()

        date_diff = self._record.end - self._record.start

        self._record.executionDurationMs = (datetime.min + date_diff).time().microsecond

        self._remove_must_not_records_from_filters()

        self._report_errors_in_slack()

    def _remove_must_not_records_from_filters(self):
        # NOTE: FR: This removes the must_not.<meta id> from filters, because in
        # mar task it can get too large.

        def get_sdp_meta_id(value):
            try:
                return value.get("terms", {}).get(SDPField.META_ID, [])
            except AttributeError as e:
                # Something in here isn't a dict.
                raise FilterDataError from e

        filters = self._record.filters

        if not isinstance(filters, dict):
            self._record.filters = str(filters)
            return

        must_not = (
            filters.get("bool", {}).get("must_not", []) if isinstance(filters, dict) else None
        )

        if isinstance(must_not, dict):
            must_not = [must_not]

        if must_not and isinstance(get_sdp_meta_id(must_not[0]), list):
            must_not = list(filter(lambda x: not get_sdp_meta_id(x), must_not))

            filters["bool"]["must_not"] = must_not

        self._record.filters = str(filters)

    def update_status(self, status):
        self._record.status = status

        return self

    def set_error(self, classification, error):
        if error.args and len(error.args) > 0:
            error_message = error.args[0]
        else:
            if error.exception and error.exception.args and len(error.exception.args) > 0:
                error_message = error.exception.args[0]
            else:
                error_message = f"Error run strategy {self._strategy_name}"

        error = Error(
            **dict(
                classification=classification,
                message=str(error_message),
                traceback=format_exc(),
                type=type(error).__name__,
            )
        )

        self._record.error = error

        return self

    def update_metrics(self, number_of_scenarios=None, records_analysed=None, records_skipped=None):  # noqa: E501
        metrics = self._record.metrics

        if number_of_scenarios:
            metrics.numberOfScenarios += number_of_scenarios

        if records_analysed:
            metrics.recordsAnalysed += records_analysed

        if records_skipped:
            metrics.recordsSkipped += records_skipped

        return self

    def update_skipped_records(
        self,
        keys: Optional[Union[str, List[str]]] = None,
        classification: Optional[SkipReason] = None,
        message: Optional[str] = None,
        error=None,
        records_count: Optional[int] = None,
        update_metrics: bool = True,
    ):
        entry = addict.Dict()

        if keys:
            entry = addict.Dict(keys=keys)

        if error:
            entry.classification = SkipReason.ERROR
            entry.message = error.args[0] if len(error.args) >= 1 else ""
            entry.traceback = format_exc()

        if classification:
            entry.classification = classification

        if message:
            entry.message = message

        if records_count:
            entry.recordsCount = records_count

        entry = SkippedRecords(**entry)

        if bool(self._record.skippedRecords):
            self._record.skippedRecords.append(entry)
        else:
            self._record.skippedRecords = [entry]

        # Update metrics
        if update_metrics:
            if keys:
                self.update_metrics(records_skipped=len(keys))
            else:
                self.update_metrics(records_skipped=records_count)

        return self

    def _report_errors_in_slack(self):
        skipped_errors = list(
            {x.message for x in self._record.skippedRecords if x.classification == SkipReason.ERROR}  # noqa: E501
        )

        if not self._record.error and not skipped_errors:
            return

        try:
            message = addict.Dict(
                errors=self._record.error,
                filters=self._record.filters,
                report_type=self._record.reportType.__str__(),
                skipped_errors=skipped_errors,
                tenant=self._context.tenant,
                stack=self._context.stack,
                id=self._context.watch_id,
                execution_id=self._context.watch_execution_id,
                thresholds=self._record.thresholds,
            )

            slack_report = SlackReport(
                message=SlackMessage(**message),
                username=f"{self._context.realm}",
                icon=":red_circle:",
            )
            slack_report.publish()

        except Exception as e:
            self._logger.info(str(e))
            self._logger.info("Response: Failed publishing error to slack")
            self._logger.info(f"Audit record: {self._record}")
            raise e
