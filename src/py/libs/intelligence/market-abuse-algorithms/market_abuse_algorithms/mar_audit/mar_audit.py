# type: ignore
import json
import logging
import os
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from pydantic import BaseModel, Field
from se_elastic_schema.static.mar_audit import DropReason, StepType
from se_io_utils.batching_utils import upload_temporary_file
from typing import List, Optional

logger = logging.getLogger(__name__)

DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S.%f%z"


class StepAudit(BaseModel):
    """Child of AggregatedStepAudit, each record holds key fields of a dropped
    order in a step."""

    step_id: str = Field(
        ...,
        description="UUID",
    )
    reason: str = Field(
        ...,
        description="Reason for the orders/instruments to be dropped",
    )
    list_of_order_ids: List[str] = Field(
        default_factory=list,
        description="Orders dropped in the step",
    )
    list_of_instruments: List[str] = Field(
        default_factory=list,
        description="Instruments dropped in the step",
    )
    grouping_id: Optional[str] = Field(None, description="NanoId for a group of data processed")
    step_type: Optional[StepType] = Field(None, description="Type of StepAudit created")
    step_name: Optional[str] = Field(None, description="Name for the AggregatedStep")
    start: Optional[str] = Field(None, description="Start date of the StepAudit")
    end: Optional[str] = Field(None, description="End date of the StepAudit")
    number_of_input_orders: Optional[int] = Field(
        None, description="Number of orders that reached the StepAudit"
    )
    number_of_resulting_orders: Optional[int] = Field(
        None, description="Number of orders that remained after the StepAudit"
    )


class AggregatedStepAudit:
    def __init__(
        self,
        aggregated_step_id: str,
        start: str,
        end: str,
        number_of_dropped_orders: int,
        number_of_input_orders: int,
        number_of_resulting_orders: int,
        step_name: str,
        step_type: Optional[StepType] = None,
        drop_reason: Optional[DropReason] = None,
        groupings: Optional[int] = None,
        groupings_dropped: Optional[int] = None,
    ):
        """Audit of a step within an execution, child of MarketAbuseAudit, one
        record equals one step within an audit.

        Holds metadata and aggregated metrics of a single step within an
        audit including number of records dropped at that stage.
        """
        self.aggregated_step_id = aggregated_step_id
        self.start = start  # TODO: moved to StepAudit; can be removed
        self.end = end  # TODO: moved to StepAudit; can be removed
        self.number_of_dropped_orders = (
            number_of_dropped_orders  # TODO: to be calculated on the auditor side
        )
        self.number_of_input_orders = (
            number_of_input_orders  # TODO: moved to StepAudit; can be removed
        )
        self.number_of_resulting_orders = (
            number_of_resulting_orders  # TODO: moved to StepAudit; can be removed
        )
        self.step_name = step_name  # TODO: moved to StepAudit; can be removed
        self.step_type = step_type  # TODO: moved to StepAudit; can be removed
        self.drop_reason = drop_reason  # TODO: this is to be removed as it is not used
        self.groupings = groupings  # TODO: moved to StepAudit; can be removed
        self.groupings_dropped = groupings_dropped  # TODO: to be calculated on the auditor side


class MarketAbuseAudit(BaseModel):
    """Parent Audit class.

    One record equally one execution of a watch. Holds metadata for that
    execution and aggregated metrics of the whole run.
    """

    class Config:
        allow_mutation = True
        validate_assignment = True

    id: Optional[str] = Field(default=None, description="ID of the market abuse audit")

    filters: Optional[str] = Field(
        None,
        description="Filters applied tot he algorithm",
    )
    algorithm_duration: int = Field(
        0,
        description="Amount of time the execution took",
    )
    start: Optional[datetime] = Field(
        None,
        description="Start date time of the algorithm execution",
    )
    end: Optional[datetime] = Field(
        None,
        description="End date time of the algorithm execution",
    )
    number_of_alerts: int = Field(
        0,
        description="Number of alerts created",
    )
    records_analysed: int = Field(
        0,
        description="Number of records analysed",
    )
    records_skipped: int = Field(
        0,
        description="Number of records dropped",
    )
    report_type: Optional[str] = Field(
        None,
        description="Algorithm",
    )
    requested_by: Optional[str] = Field(
        None,
        description="",
    )
    status: Optional[str] = Field(
        None,
        description="Status of the execution",
    )
    thresholds: Optional[str] = Field(
        None,
        description="Thresholds of the algorithm",
    )
    watch_execution_id: Optional[str] = Field(
        None,
        description="Id of the watch execution",
    )
    watch_id: Optional[str] = Field(
        None,
        description="Id of the watch",
    )
    watch_execution_type: Optional[str] = Field(
        None,
        description="Type of watch",
    )
    watch_name: Optional[str] = Field(
        None,
        description="Name of the watch",
    )
    query: Optional[str] = Field(
        None,
        description="Query the algorithm used to fetch data",
    )

    def reset_market_abuse_attributes(self):
        self.filters = None
        self.algorithm_duration = 0
        self.start = None
        self.end = None
        self.number_of_alerts = 0
        self.records_analysed = 0
        self.records_skipped = 0
        self.report_type = None
        self.requested_by = None
        self.status = None
        self.thresholds = None
        self.watch_execution_id = None
        self.watch_id = None
        self.watch_execution_type = None
        self.watch_name = None
        self.query = None


class SingletonMarAudit:
    """Singleton invoked at the beginning of a watch execution.

    Stores multiple levels of data, including MarketAbuseAudit,
    AggregatedStepAudit and StepAudit. Will write the audit data at the
    end of the watch execution and send the remote cloud file uri to the
    MarAuditService to be written to postgres.
    """

    __instance = None

    def __new__(cls, *args, **kwargs):
        if cls.__instance is None:
            cls.__instance = super(SingletonMarAudit, cls).__new__(cls)
        return cls.__instance

    def __init__(self, lake_prefix=None, watch_execution_id=None, report_type=None):
        self.mar_audit_path = Path("/tmp/mar_audit_data/")
        self.watch_execution_id = watch_execution_id
        self.report_type = report_type
        self.step_audit_url = ""
        self.aggregated_audit_url = ""
        self.mar_audit_url = ""
        self.records_skipped = 0
        self.lake_prefix = lake_prefix
        self.step_audit_lines = 0

        if not os.path.exists(self.mar_audit_path):
            self.temp_dir = tempfile.TemporaryDirectory(prefix=self.mar_audit_path._str)
            self.mar_audit_path = Path(self.temp_dir.name)

    def write_step_audit_locally(self, **kwargs):
        try:
            self.__write_audit_data_to_local_files(StepAudit(**kwargs))
        except ValueError as e:
            logger.exception(f"Error while validating StepAudit. {str(e)}")
        except Exception as e:
            logger.exception(f"Error while writing StepAudit. {str(e)}")

    def write_market_abuse_audit_locally(self, market_abuse_obj):
        try:
            self.__write_audit_data_to_local_files(market_abuse_obj)
        except Exception as e:
            logger.exception(f"Error while writing MarketAbuseAudit. {str(e)}")

    def reset_singleton_audit_attributes(self):
        self.mar_audit_path = Path("/tmp/mar_audit_data/")
        self.watch_execution_id = None
        self.step_audit_url = ""
        self.aggregated_audit_url = ""
        self.mar_audit_url = ""
        self.records_skipped = 0
        self.lake_prefix = None

        if not os.path.exists(self.mar_audit_path):
            self.temp_dir = tempfile.TemporaryDirectory(prefix=self.mar_audit_path._str)
            self.mar_audit_path = Path(self.temp_dir.name)

    def upload_audit_files_to_remote(self):
        time_now = datetime.now(timezone.utc)
        timestamp = time_now.strftime("%Y%m%d__%H%M%S")
        time_folders = time_now.strftime("%Y/%m/%d")
        base_path = f"{self.lake_prefix}lake/ingress/mar_audit/structured/{time_folders}/{self.report_type}__{self.watch_execution_id}/"  # noqa: E501

        # StepAudit
        temp_file_path = self.mar_audit_path.joinpath("StepAudit.json")
        if os.path.isfile(temp_file_path):
            self.step_audit_url = base_path + f"STEP/STEP__{timestamp}.json"
            upload_temporary_file(temp_file_path.as_posix(), self.step_audit_url)

        # AggregatedStepAudit
        temp_file_path = self.mar_audit_path.joinpath("AggregatedStepAudit.json")
        if os.path.isfile(temp_file_path):
            self.aggregated_audit_url = base_path + f"AGGR/AGGR__{timestamp}.json"
            upload_temporary_file(temp_file_path.as_posix(), self.aggregated_audit_url)

        # MarketAbuseAudit
        temp_file_path = self.mar_audit_path.joinpath("MarketAbuseAudit.json")
        if os.path.isfile(temp_file_path):
            self.mar_audit_url = base_path + f"AUDIT__{timestamp}.json"
            upload_temporary_file(temp_file_path.as_posix(), self.mar_audit_url)

    def delete_local_audit_files(self):
        files = ["StepAudit.json", "AggregatedStepAudit.json", "MarketAbuseAudit.json"]
        for file in files:
            if os.path.isfile(self.mar_audit_path.joinpath(file)):
                os.remove(self.mar_audit_path.joinpath(file))

    def write_audit_data_to_local_files(self, obj):
        logger.debug(
            "The method 'write_audit_data_to_local_files' is deprecated. Please use 'write_step_audit'"  # noqa: E501
        )
        self.__write_audit_data_to_local_files(obj=obj)

    def __write_audit_data_to_local_files(self, obj):
        logger.debug(f"Writing local audit files. Object: {obj}")
        if obj.__class__.__name__ == "StepAudit":
            with open(self.mar_audit_path.joinpath("StepAudit.json"), "a") as f:
                self.records_skipped += len(obj.list_of_order_ids)
                self.step_audit_lines += 1
                f.write(json.dumps(obj.__dict__, default=str) + "\n")
                logger.debug("Writing local StepAudit file")
        if obj.__class__.__name__ == "AggregatedStepAudit":
            with open(self.mar_audit_path.joinpath("AggregatedStepAudit.json"), "a") as f:
                f.write(json.dumps(obj.__dict__, default=str) + "\n")
                logger.debug("Writing local AggregatedStepAudit file")
        if obj.__class__.__name__ == "MarketAbuseAudit":
            with open(self.mar_audit_path.joinpath("MarketAbuseAudit.json"), "a") as f:
                f.write(json.dumps(obj.__dict__, default=str) + "\n")
                logger.debug("Writing local MarketAbuseAudit file")
