# ruff: noqa: E501
# mypy: disable-error-code="attr-defined, arg-type, union-attr, unreachable, return-value, no-any-return"
import datetime
import logging
import pandas as pd
from market_abuse_algorithms.cross_product.utils import (
    calculate_daily_return,
    parse_underlying_nested,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.data_source.repository.sdp.es_sdp import SDP
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from se_elastic_schema.models.tenant.mifid2.order import Order
from se_market_data_utils.market_data_settings import market_data_config
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="{asctime} | {levelname} | [{name}:{funcName}:{lineno}] -- {message}",
    style="{",
    datefmt="%Y-%m-%dT%H:%M:%S%s.%03d",
)

OBD_DATA_STORAGE_BUCKET = market_data_config.MASTER_DATA_STORAGE_BUCKET

SOURCES_PATH = "lake/trade-surveilllance/datasources/cross_product"
ROOT_SYMBOL_MAPP_FILE_NAME = "cross-product.implicitly-related.csv"
INDEX_CONSTITUENTS_MAP_FILE_NAME = "cross-product.index-constituents.csv"


class CrossProductQuery:
    def __init__(self, es_client: SDP, market_data_client: MarketDataClient):
        self.es_client = es_client
        self.market_data_client = market_data_client
        self.model_index = Order.get_elastic_index_alias()
        self.root_symbol_df = self.get_root_symbol_df()
        self.index_constituents_df = self.get_index_constituents_df()

    def fetch_results(self, query: dict) -> pd.DataFrame:
        """Method to search ES with a given query. Uses the.

        *search_after_query* method. After searching, it also parses the
        underlying instruments nested fields with the.

        *parse_underlying_nested* method.
        :param query: dict, query to search ES
        :return: pd.DataFrame, dataframe with the fetched and parsed data
        """
        try:
            result = self.es_client.search_after_query(query=query, model_index=self.model_index)
        except Exception as e:
            raise Exception(f"The request to Elastic Search failed with error {e}")

        result = parse_underlying_nested(result)

        return result

    def get_filter_orders(self, list_of_orders: List[str]) -> pd.DataFrame:
        """Method to fetch the records for the initial orders. Uses the.

        *fetch_results* method to search ES.

        :param list_of_orders: List[str], list of orders &keys to search for
        :return: pd.DataFrame, dataframe with the information retrieved
        """
        if not isinstance(list_of_orders, list):
            logger.warning(f"The provided orders is not a list. They are {list_of_orders}")
            return pd.DataFrame()

        query = self.build_base_query()

        query["query"]["bool"]["filter"] = [{"terms": {OrderField.META_KEY: list_of_orders}}]

        result = self.fetch_results(query=query)

        if result.empty:
            logger.warning(f"Unable to fetch data for orders: {list_of_orders}")

        return result

    def get_filter_underlying_to_isin(
        self,
        list_of_underlying: List[str],
        start: int,
        end: int,
        source_keys: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Method to search orders that have the provided Underlying ISINs as
        an ISIN. Uses the *fetch_results* method to search ES and the method.

        *get_filter_timestamp* to add the timestamp filter.

        :param source_keys: list of files to filter the search
        :param list_of_underlying: List[str], list of Underlying ISINs to search for
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the information retrieved
        """
        if not (isinstance(start, int) and isinstance(end, int)):
            logger.warning("The provided timestamps must be in milliseconds epoch (int).")
            return pd.DataFrame()

        query = self.build_base_query(source_keys=source_keys)

        query = self.get_filter_timestamp(query=query, start=start, end=end)

        query["query"]["bool"]["filter"].extend(
            [{"terms": {OrderField.INST_ID_CODE: list_of_underlying}}]
        )

        result = self.fetch_results(query=query)

        if result.empty:
            logger.warning(f"Unable to fetch data for Underlying ISINs: {list_of_underlying}")

        return result

    def get_filter_isin_to_underlying(
        self,
        list_of_isin: List[str],
        start: int,
        end: int,
        source_keys: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Method to search orders that have the provided ISINs as an
        Underlying ISIN. Uses the *fetch_results* method to search ES and the
        method *get_filter_timestamp* to add the timestamp filter.

        :param source_keys: list of files to filter the search
        :param list_of_isin: List[str], list of ISINs to search for
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the information retrieved
        """
        if not (isinstance(start, int) and isinstance(end, int)):
            logger.warning("The provided timestamps must be in milliseconds epoch (int).")
            return pd.DataFrame()

        query = self.build_base_query(source_keys=source_keys)

        query = self.get_filter_timestamp(query=query, start=start, end=end)

        nested_fields = [
            {
                "bool": {
                    "filter": [{"terms": {OrderField.INST_EXT_UNDER_INST_INST_CODE: list_of_isin}}]
                }
            },
            {
                "bool": {
                    "filter": [
                        {"terms": {OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE: list_of_isin}}
                    ]
                }
            },
        ]

        query["query"]["bool"]["should"] = nested_fields
        query["query"]["bool"]["minimum_should_match"] = 1

        result = self.fetch_results(query=query)

        if result.empty:
            logger.warning(f"Unable to fetch data for ISINs: {list_of_isin}")

        return result

    def get_filter_root(
        self,
        list_of_root: List[str],
        start: int,
        end: int,
        source_keys: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Method to search orders that have the provided Root Ticker. Uses
        the.

        *fetch_results* method to search ES and the method
        *get_filter_timestamp* to add the timestamp filter.

        :param source_keys: list of files to filter the search
        :param list_of_root: List[str], list of Root Tickers to search for
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the information retrieved
        """
        if not (isinstance(start, int) and isinstance(end, int)):
            logger.warning("The provided timestamps must be in milliseconds epoch (int).")
            return pd.DataFrame()

        query = self.build_base_query(source_keys=source_keys)

        query = self.get_filter_timestamp(query=query, start=start, end=end)

        query["query"]["bool"]["filter"].extend(
            [{"terms": {OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT: list_of_root}}]
        )

        result = self.fetch_results(query=query)

        if result.empty:
            logger.warning(f"Unable to fetch data for Root Tickers: {list_of_root}")

        return result

    def get_filter_issuer(
        self,
        list_of_issuer: List[str],
        start: int,
        end: int,
        source_keys: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Method to search orders that have the provided Issuer. Uses the.

        *fetch_results* method to search ES and the method
        *get_filter_timestamp* to add the timestamp filter.

        :param source_keys: list of files to filter the search
        :param list_of_issuer: List[str], list of Issuers to search for
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the information retrieved
        """
        if not (isinstance(start, int) and isinstance(end, int)):
            logger.warning("The provided timestamps must be in milliseconds epoch (int).")
            return pd.DataFrame()

        query = self.build_base_query(source_keys=source_keys)

        query = self.get_filter_timestamp(query=query, start=start, end=end)

        query["query"]["bool"]["filter"].extend(
            [{"terms": {OrderField.INST_ISSUER: list_of_issuer}}]
        )

        result = self.fetch_results(query=query)

        if result.empty:
            logger.warning(f"Unable to fetch data for Issuers: {list_of_issuer}")

        return result

    def get_daily_return(
        self, ric: str, dates: List[datetime.date]
    ) -> Dict[datetime.date, List[float]]:
        """Fetches and calculates the daily return for a provided record.

        :param ric: used to fetch market data
        :param dates: to determined time range filters to calculate the values
        :return: dictionary with date to list of daily return values
        """
        start_date = pd.Timestamp(min(dates))
        end_date = pd.Timestamp(max(dates))
        market_data = self.market_data_client.get_market_data_stats(
            instrument_ric=ric,
            start_date=start_date,
            end_date=end_date,
        )

        if market_data.empty:
            logger.warning(f"No market data for RIC: {ric}")
            return {}

        result_dict = {}

        for date in dates:
            list_of_daily_returns = calculate_daily_return(market_data=market_data, date=date)

            if not list_of_daily_returns:
                result_dict[date] = pd.NA
            else:
                result_dict[date] = list_of_daily_returns  # type: ignore[assignment]

        return result_dict

    def get_root_symbol_df(self) -> pd.DataFrame:
        """
        Fetches root symbol df
        :return: returns dataframe of the root symbol mapping for implicitly related
        """

        root_symbol_df = self.market_data_client.get_trade_surveillance_data(
            f"{SOURCES_PATH}/{ROOT_SYMBOL_MAPP_FILE_NAME}"
        )

        if root_symbol_df.empty:
            logger.warning("Failed to retrieve Root Symbol spreadsheet data")

        return root_symbol_df

    def get_index_constituents_df(self) -> pd.DataFrame:
        """
        Fetches index constituents df
        :return: returns dataframe of the index constituents mapping for implicitly related
        """

        index_constituents_df = self.market_data_client.get_trade_surveillance_data(
            f"{SOURCES_PATH}/{INDEX_CONSTITUENTS_MAP_FILE_NAME}"
        )

        if index_constituents_df.empty:
            logger.warning("Failed to retrieve Index Constituents spreadsheet data")

        return index_constituents_df

    @staticmethod
    def get_filter_timestamp(query: dict, start: int, end: int) -> dict:
        """Method to add the logic to filter the orders by an interval of
        timestamps.

        :param query: dict, query to add the timestamp logic
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: dict, query with the timestamp logic added
        """
        filter_by_range = [
            {
                "bool": {
                    "should": [
                        {
                            "bool": {
                                "filter": [
                                    {"term": {"executionDetails.orderStatus": "NEWO"}},
                                    {
                                        "range": {
                                            "timestamps.orderSubmitted": {
                                                "gte": start,
                                                "lte": end,
                                            }
                                        }
                                    },
                                ]
                            }
                        },
                        {
                            "bool": {
                                "filter": [
                                    {
                                        "range": {
                                            "timestamps.tradingDateTime": {
                                                "gte": start,
                                                "lte": end,
                                            }
                                        }
                                    }
                                ],
                                "must_not": [{"term": {"executionDetails.orderStatus": "NEWO"}}],
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            }
        ]

        if isinstance(query["query"]["bool"].get("filter"), list):
            query["query"]["bool"]["filter"].extend(filter_by_range)
        else:
            query["query"]["bool"]["filter"] = filter_by_range

        return query

    @staticmethod
    def build_base_query(source_keys: Optional[List[str]] = None) -> dict:
        """Method that returns the common base query for all query uses.

        :param source_keys: if provided, adds a filter for 'sourceKey'
        :return: dict, base query
        """
        query = {
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": OrderField.META_EXPIRY}}],
                }
            },
            "size": 1000,
            "_source": {
                "includes": [
                    OrderField.META_KEY,
                    OrderField.META_ID,
                    OrderField.INST_ID_CODE,
                    OrderField.INST_EXT_UNDER_INST_INST_CODE,
                    OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
                    OrderField.INST_EXT_UNIQUE_IDENT,
                    OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
                    OrderField.INST_ISSUER,
                    OrderField.TS_TRADING_DATE_TIME,
                    OrderField.TS_ORD_SUBMITTED,
                    OrderField.TRX_DTL_VENUE,
                ]
            },
        }

        if source_keys:
            query["query"]["bool"]["filter"] = [{"terms": {"sourceKey": source_keys}}]  # type: ignore[index]

        return query
