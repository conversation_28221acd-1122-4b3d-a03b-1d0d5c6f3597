class CrossProductColumns:
    CORRELATION_SCORE = "correlationScore"
    CORRELATION_SCORE_CATEGORY = "correlationScoreCategory"
    DATE = "date"
    INDEX = "Index"
    INDEX_ISIN = "Index ISIN"
    ISIN = "ISIN"
    ORIGINAL_ISIN = "originalISIN"
    ORIGINAL_RECORD_DAILY_RETURNS = "originalRecordDailyReturns"
    ORIGINAL_RECORD_METAKEY = "originalRecordMetaKey"
    ORIGINAL_RECORD_VENUE = "originalRecordVenue"
    ORIGINAL_ROOT_SYMBOL = "Original Root Symbol"
    RELATED_INSTRUMENT = "relatedInstrument"
    RELATED_RECORD_DAILY_RETURNS = "relatedRecordDailyReturns"
    RELATED_SUB_TYPE = "relatedSubType"
    RELATED_TYPE = "relatedType"
    RIC = "RIC"
    ROOT_TICKER = "Index Root Ticker"
    ROOT_TICKER_1 = "Cross Product Ticker 1"
    ROOT_TICKER_2 = "Cross Product Ticker 2"
    UNDERLYING_COL = "underlyingColumn"
    VENUE_MATCHING_TYPE = "venueMatchingType"


class RelatedType:
    EXPLICITLY_CORRELATED = "explicitlyCorrelated"
    EXPLICITLY_RELATED = "explicitlyRelated"
    IMPLICITLY_RELATED = "implicitlyRelated"


class ExplicitlyRelated:
    ISIN_TO_UNDERLYING = "isinToUnderlying"
    ISSUER = "issuer"
    ROOT_TICKER = "rootTicker"
    UNDERLYING_TO_ISIN = "underlyingToIsin"


class ExplicitlyCorrelated:
    SAME_ISIN = "sameIsin"
    SAME_ROOT = "sameRootTicker"
    SAME_UNDERLYING = "sameUnderlying"


class CorrelationCategories:
    VERY_STRONG_NEGATIVE = "Very Strong Negative"
    STRONG_NEGATIVE = "Strong Negative"
    MODERATE_NEGATIVE = "Moderate Negative"
    WEAK_NEGATIVE = "Weak Negative"
    VERY_WEAK_OR_NO_NEGATIVE = "Very Weak or No Negative"
    VERY_WEAK_OR_NO_POSITIVE = "Very Weak or No Positive"
    WEAK_POSITIVE = "Weak Positive"
    MODERATE_POSITIVE = "Moderate Positive"
    STRONG_POSITIVE = "Strong Positive"
    VERY_STRONG_POSITIVE = "Very Strong Positive"

    @classmethod
    def get_all_categories(cls):
        return [value for name, value in vars(cls).items() if not name.startswith("__")][:-2]

    @classmethod
    def get_category_intervals(cls):
        return [-1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
