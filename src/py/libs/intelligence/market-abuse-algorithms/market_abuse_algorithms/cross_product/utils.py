# ruff: noqa: E501
# mypy: disable-error-code="attr-defined, arg-type, return-value, no-any-return, assignment"
import datetime
import logging
import pandas as pd
from market_abuse_algorithms.cross_product.static import CorrelationCategories, CrossProductColumns
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from scipy.stats import pearsonr  # type: ignore[import-untyped]
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="{asctime} | {levelname} | [{name}:{funcName}:{lineno}] -- {message}",
    style="{",
    datefmt="%Y-%m-%dT%H:%M:%S%s.%03d",
)


def record_extractor(scenario: pd.Series) -> List[str]:
    """Method that extracts records from scenarios.

    :param scenario: Series with fields of a scenario
    :return: list of record keys
    """
    records_dict: Dict[str, List[str]] = scenario.loc["records"]

    records_list = []
    for key in records_dict.keys():
        records = records_dict[key]
        if isinstance(records, list):
            records_list.extend(records)
        else:
            records_list.append(records)  # type: ignore[unreachable]

    records_list = list(set(records_list))

    return records_list


def parse_underlying_nested(data: pd.DataFrame) -> pd.DataFrame:
    """Method to parse the underlying nested fields. Transforms the nested
    fields into a list with all the underlying instruments found and returns
    the transformed DataFrame.

    :param data: pd.DataFrame, data to parse the nested fields
    :return: pd.DataFrame, parsed data
    """
    if not (
        OrderField.INST_DERIV_UND_INSTS in data.columns
        or OrderField.INST_EXT_UNDER_INST in data.columns
    ):
        return data

    if OrderField.INST_DERIV_UND_INSTS in data.columns:
        # JUST TO RESOLVE THE LIST WITH DICT
        data.loc[:, OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE] = data[
            OrderField.INST_DERIV_UND_INSTS
        ].apply(
            lambda row: [element["underlyingInstrumentCode"] for element in row]
            if isinstance(row, list)
            else pd.NA
        )
        data = data.drop(columns=OrderField.INST_DERIV_UND_INSTS)

    if OrderField.INST_EXT_UNDER_INST in data.columns:
        # JUST TO RESOLVE THE LIST WITH DICT
        data.loc[:, OrderField.INST_EXT_UNDER_INST_INST_CODE] = data[
            OrderField.INST_EXT_UNDER_INST
        ].apply(
            lambda row: [element["instrumentIdCode"] for element in row]
            if isinstance(row, list)
            else pd.NA
        )
        data = data.drop(columns=OrderField.INST_EXT_UNDER_INST)

    return data


def get_end_of_day_timestamp(start_date: pd.Timestamp) -> pd.Timestamp:
    """
    Method to get the end of day Timestamp from a given date
    e.g., start_date = pd.Timestamp('2024-01-02 12:00:00') -> pd.Timestamp('2024-01-02 23:59:59')
    :param start_date: pd.Timestamp, date to get the end of day timestamp
    :return: pd.Timestamp, end of day timestamp
    """
    return start_date.date() + pd.DateOffset(1) - datetime.timedelta(seconds=1)  # type: ignore[misc]


def convert_timestamp_to_epoch(date: pd.Timestamp) -> int:
    """Helper function to convert pd.Timestamp into unix time (epoch). Converts
    to milliseconds.

    :param date: pd.Timestamp, timestamp to convert
    :return: int, provided timestamp in epoch
    """
    return date.value // 10**6


def get_underlying_values(data: pd.DataFrame) -> pd.DataFrame:
    """This method gets creates the column ExplicitlyCorrelated.UNDERLYING_COL
    for the data. This column is gets the exploded underlying value following
    the priority defined in https://steeleye.atlassian.net/wiki/spaces/PRODUCT/
    pages/874414183/MAR+-+Frequently+Used+Fields+-+FUFs#%5BUnderlying-ISIN%5D.

    :param data: pd.Dataframe with the orders
    :returns: pd.Dataframe, copy of the data with the generated column
              ExplicitlyCorrelated.UNDERLYING_COL
    """

    result = data.copy()

    result[CrossProductColumns.UNDERLYING_COL] = pd.NA

    if (
        OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE in result.columns
        and OrderField.INST_EXT_UNDER_INST_INST_CODE in result.columns
    ):
        result[CrossProductColumns.UNDERLYING_COL] = result[
            OrderField.INST_EXT_UNDER_INST_INST_CODE
        ]
        result[CrossProductColumns.UNDERLYING_COL] = result[
            CrossProductColumns.UNDERLYING_COL
        ].fillna(result[OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE])
    else:
        if OrderField.INST_EXT_UNDER_INST_INST_CODE in result.columns:
            result[CrossProductColumns.UNDERLYING_COL] = result[
                OrderField.INST_EXT_UNDER_INST_INST_CODE
            ]
        elif OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE in result.columns:
            result[CrossProductColumns.UNDERLYING_COL] = result[
                OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE
            ]

    return result.explode(CrossProductColumns.UNDERLYING_COL)


def generate_final_result_dict(
    records: pd.DataFrame,
    results_dict: Optional[dict] = None,
    explode_records: bool = True,
) -> Dict[str, List[Dict[str, str]]]:
    """Creates a dictionary with original record meta key as keys and a list of
    dictionaries Each dictionary contains meta key, meta id and subtype of each
    related record.

    :param explode_records: if False it does not explode the **records** on **originalRecordMetaKey**. Defaults to True
    :param results_dict: previously created dict with the &keys. Specially made for the explicitly related checks
    :param records: dataframe with related records
    :return: result dictionary with data for later enrichment
    """
    if explode_records:
        records = records.explode(column=CrossProductColumns.ORIGINAL_RECORD_METAKEY)

    if results_dict is None:
        results_dict = {
            key: [] for key in records.loc[:, CrossProductColumns.ORIGINAL_RECORD_METAKEY].unique()
        }

    records.apply(
        lambda x: results_dict[x[CrossProductColumns.ORIGINAL_RECORD_METAKEY]].append(
            {
                OrderField.META_KEY: x[OrderField.META_KEY],
                OrderField.META_ID: x[OrderField.META_ID],
                OrderField.TRX_DTL_VENUE: x.get(OrderField.TRX_DTL_VENUE, None),
                OrderField.INST_ID_CODE: x.get(OrderField.INST_ID_CODE, None),
                OrderField.INST_EXT_UNDER_INST_INST_CODE: x.get(
                    OrderField.INST_EXT_UNDER_INST_INST_CODE, None
                ),
                OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE: x.get(
                    OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE, None
                ),
                OrderField.INST_EXT_UNIQUE_IDENT: x.get(OrderField.INST_EXT_UNIQUE_IDENT, None),
                CrossProductColumns.RELATED_TYPE: x.get(CrossProductColumns.RELATED_TYPE, None),
                CrossProductColumns.RELATED_SUB_TYPE: x.get(
                    CrossProductColumns.RELATED_SUB_TYPE, None
                ),
            }
        ),
        axis=1,
    )

    return results_dict


def create_missing_columns(records: pd.DataFrame, columns: list) -> pd.DataFrame:
    """Function to add the missing columns to the dataframe provided. It
    populates the missing columns with None.

    :param records: dataframe to create the missing columns
    :param columns: list of columns to check on the dataframe
    :return: given dataframe with the missing columns added
    """
    missing_columns = list(set(columns) - set(records.columns))
    records.loc[:, missing_columns] = pd.NA
    return records


def create_df_from_result(result_dict: dict) -> pd.DataFrame:
    """Function to parse the result dict from the *generate_final_result_dict*
    method and return a dataframe.

    :param result_dict: dict with the structure of *generate_final_result_dict*
    :return: dataframe with the related orders
    """
    related_records = []
    for original_record in result_dict.keys():
        related_records_list = result_dict[original_record]
        related_records_list = [
            {**d, CrossProductColumns.ORIGINAL_RECORD_METAKEY: original_record}
            for d in related_records_list
        ]
        related_records.extend(related_records_list)

    related_df = pd.DataFrame(related_records)
    return related_df


def calculate_daily_return(market_data: pd.DataFrame, date: datetime.date) -> List[float]:
    """Calculates the daily return for a maximum of 30 days.

    :param market_data: dataframe with market data to get close prices
    :param date: dates to fetch the records before
    :return: list of daily returns float values
    """

    market_data_mask = market_data.loc[:, TradeStatsColumns.DATE] <= pd.to_datetime(
        date, format="mixed"
    )

    close_price_values = market_data.loc[market_data_mask, TradeStatsColumns.CLOSE_PRICE].tolist()[
        ::-1
    ]

    min_close_price_size = min(len(close_price_values), 30)

    close_price_values = close_price_values[:min_close_price_size]

    list_of_daily_returns = []

    for i in range(len(close_price_values) - 1):
        price_today = close_price_values[i]
        price_yesterday = close_price_values[i + 1]

        if (pd.isna(price_today) or price_today == 0) or (
            pd.isna(price_yesterday) or price_yesterday == 0
        ):
            val = pd.NA
        else:
            val = (price_today - price_yesterday) / price_yesterday

        list_of_daily_returns.append(val)

    return list_of_daily_returns


def calculate_correlation(data: pd.DataFrame) -> pd.DataFrame:
    """Calculates pearson correlation and assigns a category result.

    :param data: dataframe with daily return data for original and related records
    :return: input dataframe with additional of two columns with correlation score e category
    """

    data.loc[:, CrossProductColumns.CORRELATION_SCORE] = pd.NA

    daily_returns_mask = (
        ~data.loc[:, CrossProductColumns.RELATED_RECORD_DAILY_RETURNS].apply(
            lambda x: (pd.isna(x).any() or len(x) < 2) if isinstance(x, list) else True
        )
    ) & (
        ~data.loc[:, CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS].apply(
            lambda x: (pd.isna(x).any() or len(x) < 2) if isinstance(x, list) else True
        )
    )

    # Calculates the correlation score for each original/related record
    data.loc[daily_returns_mask, CrossProductColumns.CORRELATION_SCORE] = data.loc[
        daily_returns_mask,
        [
            CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS,
            CrossProductColumns.RELATED_RECORD_DAILY_RETURNS,
        ],
    ].apply(
        lambda x: correlation_formula(
            sample_1=x[CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS],
            sample_2=x[CrossProductColumns.RELATED_RECORD_DAILY_RETURNS],
        ),
        axis=1,
    )

    # Creates a column assigns a category according to correlation score
    data.loc[daily_returns_mask, CrossProductColumns.CORRELATION_SCORE_CATEGORY] = pd.cut(
        data.loc[daily_returns_mask, CrossProductColumns.CORRELATION_SCORE],
        bins=CorrelationCategories.get_category_intervals(),
        labels=CorrelationCategories.get_all_categories(),
        include_lowest=True,
    )

    return data


def correlation_formula(sample_1: List[float], sample_2: List[float]) -> float:
    """Trims the sample to be of the same size Calculates Pearson correlation
    coefficient according to specs: https://steeleye.atlassian.net/wiki/spaces/
    ECM/pages/2895314969/ECM-303+-+Cross+Product+-+Tranche+One#%5BUS4%5D---
    Enrich-Alerts-with-Related-Activity.

    :param sample_1: list of daily returns
    :param sample_2: list of daily returns
    :return: value of pearson correlation score
    """
    min_len = min(len(sample_1), len(sample_2))

    trimmed_sample_1 = sample_1[:min_len]
    trimmed_sample_2 = sample_2[:min_len]

    correlation_score, _ = pearsonr(trimmed_sample_1, trimmed_sample_2)

    return correlation_score


def get_souce_key_filters(filters: Dict) -> Union[List[str], None]:
    """Function to retrieve the 'sourceKey' from the filters provided to the
    algo.

    :param filters: filters provided to the algo
    :return: list of files to filter ES search or None in case there is no file
    """
    if not isinstance(filters, dict):
        logger.warning("The provided filters are not in dict format")  # type: ignore[unreachable]
        return None

    fields = [filters["bool"].get("must", {}), filters["bool"].get("filter", {})]

    files = []
    for field in fields:
        if isinstance(field, dict):
            files.extend(field.get("terms", {}).get("sourceKey", []))
        else:
            for element in field:
                files.extend(element.get("terms", {}).get("sourceKey", []))

    source_keys = list(set(files))
    return source_keys if len(source_keys) > 0 else None
