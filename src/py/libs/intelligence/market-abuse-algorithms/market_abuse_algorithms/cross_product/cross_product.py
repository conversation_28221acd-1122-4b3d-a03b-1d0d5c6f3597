# ruff: noqa: E501
# mypy: disable-error-code="attr-defined, index, arg-type, comparison-overlap, assignment"
import logging
import pandas as pd
from market_abuse_algorithms.cross_product.query import CrossProductQuery
from market_abuse_algorithms.cross_product.static import (
    CrossProductColumns,
    ExplicitlyCorrelated,
    ExplicitlyRelated,
    RelatedType,
)
from market_abuse_algorithms.cross_product.utils import (
    calculate_correlation,
    create_df_from_result,
    create_missing_columns,
    generate_final_result_dict,
    get_souce_key_filters,
    get_underlying_values,
    record_extractor,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.data_source.static.sdp.order import Order<PERSON>ield
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from market_abuse_algorithms.utils.processment import new_instrument_ric_map
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="{asctime} | {levelname} | [{name}:{funcName}:{lineno}] -- {message}",
    style="{",
    datefmt="%Y-%m-%dT%H:%M:%S%s.%03d",
)


REQUIRED_COLUMNS = [
    OrderField.META_KEY,
    OrderField.META_ID,
    OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
    OrderField.TRX_DTL_VENUE,
    OrderField.INST_ID_CODE,
    OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
    OrderField.INST_EXT_UNDER_INST_INST_CODE,
    OrderField.INST_EXT_UNIQUE_IDENT,
    OrderField.INST_ISSUER,
]

OUTPUT_COLUMNS = [
    OrderField.META_ID,
    CrossProductColumns.RELATED_TYPE,
    CrossProductColumns.RELATED_SUB_TYPE,
    CrossProductColumns.CORRELATION_SCORE,
    CrossProductColumns.CORRELATION_SCORE_CATEGORY,
    CrossProductColumns.ORIGINAL_RECORD_METAKEY,
    CrossProductColumns.VENUE_MATCHING_TYPE,
]


class CrossProductActivityDisplay:
    """ECM-303 Cross-Product Analysis.

    Confluence pages:
    Product specs: https://steeleye.atlassian.net/wiki/spaces/ECM/pages/2895314969/ECM-303+-+Cross+Product+-+Tranche+One#%5BUS4%5D---Enrich-Alerts-with-Related-Activity
    Architectural design: https://steeleye.atlassian.net/wiki/spaces/ECM/pages/2985951880/Design+-+Cross+Product+-+Tranche+One

    Dev work jira initiative:
    https://steeleye.atlassian.net/browse/ENG-6808
    """

    def __init__(
        self,
        es_client,
        market_data_client: MarketDataClient,
    ):
        self.query = CrossProductQuery(es_client=es_client, market_data_client=market_data_client)
        self.records = None
        self.scenario = None
        self.original_orders = None

    def set_orders(self, orders: List[str]):
        """Inserts a list of orders to be checked for cross products.

        :param orders: list of meta keys of orders
        """
        self.records = orders
        self.original_orders = self.query.get_filter_orders(list_of_orders=self.records)

    def set_scenario(
        self,
        scenario: Union[AbstractScenario, pd.Series],
        list_of_orders: Optional[List[str]] = None,
    ):
        """Inserts scenario into cross product to fetch related The.

        **list_of_orders** argument is used in cases where the provided
        scenario other orders besides the desired related.

        :param list_of_orders: list with &keys to fetch original records
        :param scenario: pandas Series with alerts
        """
        if isinstance(scenario, pd.Series):
            self.scenario = scenario
        else:
            self.scenario = scenario._scenario

        if list_of_orders:
            self.records = list_of_orders
        else:
            self.records = record_extractor(self.scenario)

        self.original_orders = self.query.get_filter_orders(list_of_orders=self.records)

    def run_checks_alert_detection(
        self, start: int, end: int, filters: Optional[dict] = None
    ) -> pd.DataFrame:
        """Method to run all checks for alert detection.

        :param filters: filters provided to the algo
        :param start: start timestamp (unix) to filter the orders
        :param end: end timestamp (unix) to filter the orders
        :return:
        """
        source_keys = None
        if filters and isinstance(filters, dict):
            source_keys = get_souce_key_filters(filters=filters)

        explicitly_related = self.explicitly_related(start=start, end=end, source_keys=source_keys)
        explicitly_correlated = self.explicitly_correlated(
            start=start, end=end, source_keys=source_keys
        )
        implicitly_related = self.implicitly_related(start=start, end=end, source_keys=source_keys)

        merge_all_checks = pd.concat(
            [explicitly_related, explicitly_correlated, implicitly_related], axis=0
        ).reset_index(drop=True)

        del explicitly_related, explicitly_correlated, implicitly_related

        if merge_all_checks.empty:
            return pd.DataFrame()

        merge_all_checks = merge_all_checks.explode(
            column=CrossProductColumns.ORIGINAL_RECORD_METAKEY
        )

        # DROP ALL RECORDS WITH ORIGINAL_RECORD_METAKEY AS NAN
        merge_all_checks = merge_all_checks.dropna(
            subset=[CrossProductColumns.ORIGINAL_RECORD_METAKEY]
        )
        if merge_all_checks.empty:
            return pd.DataFrame()

        return merge_all_checks

    def run_checks_enrichment(
        self, start: int, end: int, filters: Optional[dict] = None
    ) -> Dict[str, List[Dict[str, str]]]:
        """Method to run all checks for alert enrichment.

        :param filters: filters provided to the algo
        :param start: start timestamp (unix) to filter the orders
        :param end: end timestamp (unix) to filter the orders
        :return:
        """
        source_keys = None
        if filters and isinstance(filters, dict):
            source_keys = get_souce_key_filters(filters=filters)

        explicitly_related = self.explicitly_related(start=start, end=end, source_keys=source_keys)
        explicitly_correlated = self.explicitly_correlated(
            start=start, end=end, source_keys=source_keys
        )
        implicitly_related = self.implicitly_related(start=start, end=end, source_keys=source_keys)

        merge_all_checks = pd.concat(
            [explicitly_related, explicitly_correlated, implicitly_related], axis=0
        )

        del explicitly_related, explicitly_correlated, implicitly_related

        if merge_all_checks.empty:
            return {}

        results_dict = generate_final_result_dict(records=merge_all_checks)

        return results_dict

    def remove_original_orders_from_related(self, related_df: pd.DataFrame) -> pd.DataFrame:
        """Method to remove the original orders from the ones obtained from the
        related cross-product ones.

        :param related_df: pd.DataFrame, dataframe with the orders obtained from the cross-product checks
        :return: pd.DataFrame, dataframe without the original orders
        """
        if related_df.empty:
            logger.warning("The provided related dataframe is empty.")
            return pd.DataFrame()

        original_keys = self.original_orders[OrderField.META_KEY].tolist()
        result = related_df[~related_df[OrderField.META_KEY].isin(original_keys)]

        if result.empty:
            logger.warning("No related orders after removing original orders from the dataframe.")

        return result

    def isin_sub_check(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Method to perform the ISIN -> Underlying ISIN sub-check. It extracts
        relevant details from an ISIN Carrying Instrument and check whether
        there are any other records that have this ISIN but as an Underlying
        ISIN.

        :param source_keys: list of files to filter the search
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the orders that matched the ISINs
        """
        if OrderField.INST_ID_CODE not in self.original_orders.columns:
            logger.warning(
                f"Column {OrderField.INST_ID_CODE} is not present in the data. Skipping sub-check."
            )
            return pd.DataFrame()

        # drop na
        df_with_isin = self.original_orders.dropna(subset=[OrderField.INST_ID_CODE])

        if df_with_isin.empty:
            logger.warning(
                f"No orders left after dropping NA for columns {OrderField.INST_ID_CODE}."
            )
            return pd.DataFrame()

        isin = df_with_isin[OrderField.INST_ID_CODE].unique().tolist()

        result = self.query.get_filter_isin_to_underlying(
            list_of_isin=isin, start=start, end=end, source_keys=source_keys
        )

        if result.empty:
            logger.warning("No data fetched from ES on ISIN sub-check. Skipping sub-check.")
            return pd.DataFrame()

        # remove original records
        result = self.remove_original_orders_from_related(related_df=result)

        if result.empty:
            logger.warning("No related orders left after removing original.")
            return pd.DataFrame()

        # Drop duplicates in the result from the sub-check query
        # reset index to match correctly
        result = result.drop_duplicates(
            subset=[OrderField.META_KEY, OrderField.META_ID]
        ).reset_index(drop=True)

        # create empty fields for missing columns
        result = create_missing_columns(records=result, columns=REQUIRED_COLUMNS)

        result_exploded = get_underlying_values(data=result)

        # get a dict with the map for the desired col and the &key
        record_mapped: Dict[str, str] = (
            df_with_isin.groupby(OrderField.INST_ID_CODE)[OrderField.META_KEY].apply(list).to_dict()
        )

        # match each related order to its original order
        result_exploded.loc[:, CrossProductColumns.ORIGINAL_RECORD_METAKEY] = result_exploded.loc[
            :, CrossProductColumns.UNDERLYING_COL
        ].map(record_mapped)

        # filter only the desired cols from df.
        # This is done on the unexploded result df so that the return of this method matches the other sub-checks.
        related_records_filtered = result.loc[
            :,
            REQUIRED_COLUMNS,
        ]

        # we are getting every ORIGINAL_RECORD_METAKEY in the exploded result and adding it to the
        # return dataframe.
        # we group the exploded df by &key, and then we get all ORIGINAL_RECORD_METAKEY for that group and get
        # all unique values and store it as a list.
        cross_product_original_keys: pd.Series = result_exploded.groupby(
            OrderField.META_KEY, as_index=False
        ).agg(
            {
                CrossProductColumns.ORIGINAL_RECORD_METAKEY: lambda x: x.explode()
                .dropna()
                .unique()
                .tolist()
            }
        )[CrossProductColumns.ORIGINAL_RECORD_METAKEY]

        if len(cross_product_original_keys) != len(related_records_filtered):
            logger.warning(
                f"len of cross_product_original_keys {len(cross_product_original_keys)} is different from "
                f"the related_records_filtered {len(related_records_filtered)}."
            )
            return pd.DataFrame()

        related_records_filtered[CrossProductColumns.ORIGINAL_RECORD_METAKEY] = (
            cross_product_original_keys
        )

        return related_records_filtered

    def underlying_sub_check(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Method to perform the Underlying ISIN -> ISIN sub-check. It extracts
        relevant details from an Underlying ISIN Carrying Instrument and check
        whether any other records have this Underlying ISIN but as an ISIN.

        :param source_keys: list of files to filter the search
        :param start: int, start timestamp (unix) to filter the orders
        :param end: int, end timestamp (unix) to filter the orders
        :return: pd.DataFrame, dataframe with the orders that matched the Underlying ISINs
        """
        if self.original_orders.empty:
            logger.warning("No records to obtain the cross-product.")
            return pd.DataFrame()

        exploded_underlying: pd.DataFrame = get_underlying_values(data=self.original_orders)

        # drop na
        df_with_underlying = exploded_underlying.dropna(
            subset=[CrossProductColumns.UNDERLYING_COL],
        )
        del exploded_underlying

        if df_with_underlying.empty:
            logger.warning(
                f"No orders left after dropping NA for column {CrossProductColumns.UNDERLYING_COL} "
                f"with the underlying instruments."
            )
            return pd.DataFrame()

        underlying = df_with_underlying[CrossProductColumns.UNDERLYING_COL].unique().tolist()

        result = self.query.get_filter_underlying_to_isin(
            list_of_underlying=underlying, start=start, end=end, source_keys=source_keys
        )

        if result.empty:
            logger.warning(
                "No data fetched from ES on Underlying ISIN sub-check. Skipping sub-check."
            )
            return pd.DataFrame()

        # remove original records
        result = self.remove_original_orders_from_related(related_df=result)

        if result.empty:
            logger.warning("No related orders left after removing original.")
            return pd.DataFrame()

        # create empty fields for missing columns
        result = create_missing_columns(records=result, columns=REQUIRED_COLUMNS)

        # get a dict with the map for the desired col and the &key
        record_mapped: Dict[str, str] = (
            df_with_underlying.groupby(CrossProductColumns.UNDERLYING_COL)[OrderField.META_KEY]
            .apply(list)
            .to_dict()
        )
        # filter only the desired cols from df
        related_records_filtered = result.loc[
            :,
            REQUIRED_COLUMNS,
        ]
        # match each related order to its original order
        related_records_filtered.loc[:, CrossProductColumns.ORIGINAL_RECORD_METAKEY] = (
            related_records_filtered.loc[:, OrderField.INST_ID_CODE].map(record_mapped)
        )

        return related_records_filtered

    def same_issuer_or_root_sub_check(
        self,
        start: int,
        end: int,
        column: OrderField,
        source_keys: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Method that performs both Issuer and Root Ticker sub-checks
        independently. It extracts the Issuer/Root Ticker of a given security
        and see whether there are any related records on that same Issuer/Root
        Ticker.

        :param source_keys: list of files to filter the search
        :param start: start timestamp (unix) to filter the orders
        :param end: end timestamp (unix) to filter the orders
        :param column: column to perform the sub-check
        :return: dataframe with the orders that matched the Issuer
        """
        expected_cols = [
            OrderField.INST_ISSUER,
            OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
        ]
        if column not in expected_cols:
            logger.warning(f"Column {column} is not one of {expected_cols}. Skipping sub-check.")
            return pd.DataFrame()

        if column not in self.original_orders.columns:
            logger.warning(f"Column {column} is not present in the data. Skipping sub-check.")
            return pd.DataFrame()

        original_df = self.original_orders.dropna(subset=[column])

        if original_df.empty:
            logger.warning(f"No orders left after dropping NA for columns {column}.")
            return pd.DataFrame()

        values_to_query = original_df[column].unique().tolist()

        result = pd.DataFrame()

        if column == OrderField.INST_ISSUER:
            result = self.query.get_filter_issuer(
                list_of_issuer=values_to_query,
                start=start,
                end=end,
                source_keys=source_keys,
            )

            if result.empty:
                logger.warning("No data fetched from ES on Issuer sub-check. Skipping sub-check.")
                return pd.DataFrame()

        elif column == OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT:
            result = self.query.get_filter_root(
                list_of_root=values_to_query,
                start=start,
                end=end,
                source_keys=source_keys,
            )

            if result.empty:
                logger.warning(
                    "No data fetched from ES on Root Ticker sub-check. Skipping sub-check."
                )
                return pd.DataFrame()

        # remove original records
        result = self.remove_original_orders_from_related(related_df=result)

        if result.empty:
            logger.warning("No related orders left after removing original.")
            return pd.DataFrame()

        # create empty fields for missing columns
        result = create_missing_columns(records=result, columns=REQUIRED_COLUMNS)

        # get a dict with the map for the desired col and the &key
        record_mapped: Dict[str, str] = (
            original_df.groupby(column)[OrderField.META_KEY].apply(list).to_dict()
        )
        # filter only the desired cols from df
        related_records_filtered = result.loc[
            :,
            REQUIRED_COLUMNS,
        ]
        # match each related order to its original order
        related_records_filtered.loc[:, CrossProductColumns.ORIGINAL_RECORD_METAKEY] = (
            related_records_filtered.loc[:, column].map(record_mapped)
        )

        return related_records_filtered

    # TODO: as per https://steeleye.atlassian.net/browse/ENG-10521 the issuer check will not run until further notice
    def explicitly_related(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Method that performs all Explicitly Related sub-checks which are:

            - Same ISIN to Underlying ISIN sub-check;
            - Same Underlying ISIN to ISIN sub-check;
            - Same Root Ticker sub-check;
            - Same Issuer sub-check.

        https://steeleye.atlassian.net/browse/ENG-6810

        :param source_keys: list of files to filter the search
        :param start: int, start timestamp in epoch time to filter the orders
        :param end: int, end timestamp in epoch time to filter the orders
        :return: dict
        """
        same_root = self.same_issuer_or_root_sub_check(
            start=start,
            end=end,
            column=OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
            source_keys=source_keys,
        )
        # same_issuer = self.same_issuer_or_root_sub_check(
        #     start=start, end=end, column=OrderField.INST_ISSUER, source_keys=source_keys
        # )
        same_underlying_isin = self.underlying_sub_check(
            start=start, end=end, source_keys=source_keys
        )
        same_isin_underlying = self.isin_sub_check(start=start, end=end, source_keys=source_keys)

        # .loc[:, not used because any of this dataframes can be empty
        same_root[CrossProductColumns.RELATED_SUB_TYPE] = ExplicitlyRelated.ROOT_TICKER
        # same_issuer[CrossProductColumns.RELATED_SUB_TYPE] = ExplicitlyRelated.ISSUER
        same_underlying_isin[CrossProductColumns.RELATED_SUB_TYPE] = (
            ExplicitlyRelated.UNDERLYING_TO_ISIN
        )
        same_isin_underlying[CrossProductColumns.RELATED_SUB_TYPE] = (
            ExplicitlyRelated.ISIN_TO_UNDERLYING
        )

        # create empty dataframe to minimise changes
        same_issuer = pd.DataFrame()
        results = pd.concat(
            [same_root, same_issuer, same_underlying_isin, same_isin_underlying], axis=0
        ).reset_index(drop=True)

        results[CrossProductColumns.RELATED_TYPE] = RelatedType.EXPLICITLY_RELATED

        return results

    def explicitly_correlated(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """https://steeleye.atlassian.net/browse/ENG-6811.

        :param source_keys: list of files to filter the search
        :param start: int, epoch value for start timestamp
        :param end: int, epoch value for end timestamp
        :returns dict: dict with the explicitly correlated orders
        """
        # For Records being evaluated, retain only those that have either an [Underlying ISIN] or [ISIN]
        if self.original_orders.empty:
            logger.info("No records to be analysed")
            return pd.DataFrame()

        if self.query.index_constituents_df.empty:
            logger.info("No index constituents data retrieved")
            return pd.DataFrame()

        drop_na_subset = [CrossProductColumns.UNDERLYING_COL]

        original_orders: pd.DataFrame = get_underlying_values(data=self.original_orders)

        if OrderField.INST_ID_CODE in original_orders:
            drop_na_subset.append(OrderField.INST_ID_CODE)
        else:
            logger.info(f"{OrderField.INST_ID_CODE} not in Records")

        filtered_df = original_orders.dropna(subset=drop_na_subset, how="all")

        del original_orders

        if filtered_df.empty:
            logger.warning("No records to be analysed")
            return pd.DataFrame()

        # Search within the ISIN column of the spreadsheet

        map_data_start = self.create_initial_map(
            df_filtered=filtered_df,
            subset=drop_na_subset,
            additional_col=CrossProductColumns.UNDERLYING_COL,
        )

        # See whether there is any match to an Index
        isin_to_compare = map_data_start[CrossProductColumns.ORIGINAL_ISIN].unique().tolist()

        isin_mask = self.query.index_constituents_df.loc[:, CrossProductColumns.ISIN].isin(
            isin_to_compare
        )

        isin_mapping_dataframe = self.query.index_constituents_df.loc[
            isin_mask,
            [
                CrossProductColumns.ISIN,
                CrossProductColumns.INDEX_ISIN,
                CrossProductColumns.ROOT_TICKER,
            ],
        ].drop_duplicates()

        if isin_mapping_dataframe.empty:
            logger.warning("No correlation was found with the records")
            return pd.DataFrame()

        # create base maps for the [Index ISIN Check] and [Root Ticker Check]
        map_data_isin = (
            pd.merge(
                map_data_start,
                isin_mapping_dataframe.loc[
                    :, [CrossProductColumns.ISIN, CrossProductColumns.INDEX_ISIN]
                ],
                left_on=CrossProductColumns.ORIGINAL_ISIN,
                right_on=CrossProductColumns.ISIN,
            )
            .dropna()
            .drop_duplicates()
            .drop(columns=[CrossProductColumns.ISIN])
        )

        map_data_root_ticker = (
            pd.merge(
                map_data_start,
                isin_mapping_dataframe.loc[
                    :, [CrossProductColumns.ISIN, CrossProductColumns.ROOT_TICKER]
                ],
                left_on=CrossProductColumns.ORIGINAL_ISIN,
                right_on=CrossProductColumns.ISIN,
            )
            .dropna()
            .drop_duplicates()
            .drop(columns=[CrossProductColumns.ISIN])
        )

        del map_data_start

        # If there is, perform a wildmatch search based on the AII Patern defined in the spreadsheet column against Records Ingested

        if map_data_isin.empty and map_data_root_ticker.empty:
            logger.warning("Overlap between records and correlated records empty")
            return pd.DataFrame()

        # [Index ISIN Check]

        index_isin_to_fetch = map_data_isin[CrossProductColumns.INDEX_ISIN].unique().tolist()

        if index_isin_to_fetch:
            # map from Index ISIN to ISIN
            get_correlated_isin = self.query.get_filter_underlying_to_isin(
                list_of_underlying=index_isin_to_fetch,
                start=start,
                end=end,
                source_keys=source_keys,
            )

            # remove original records
            get_correlated_isin = self.remove_original_orders_from_related(
                related_df=get_correlated_isin
            )

            if get_correlated_isin.empty:
                logger.warning("No orders were found for the correlated ISIN")
            else:
                get_correlated_isin = get_correlated_isin.loc[
                    :,
                    [OrderField.INST_ID_CODE, OrderField.META_ID, OrderField.META_KEY],
                ]
                get_correlated_isin.loc[:, CrossProductColumns.RELATED_SUB_TYPE] = (
                    ExplicitlyCorrelated.SAME_ISIN
                )

                map_data_isin = (
                    pd.merge(
                        map_data_isin,
                        get_correlated_isin,
                        left_on=CrossProductColumns.INDEX_ISIN,
                        right_on=OrderField.INST_ID_CODE,
                    )
                    .dropna()
                    .drop_duplicates()
                )

            # map from Index ISIN to Underlying ISIN
            get_correlated_underlying = self.query.get_filter_isin_to_underlying(
                list_of_isin=index_isin_to_fetch,
                start=start,
                end=end,
                source_keys=source_keys,
            )

            # remove original records
            get_correlated_underlying = self.remove_original_orders_from_related(
                related_df=get_correlated_underlying
            )

            if get_correlated_underlying.empty:
                logger.warning("No orders were found for the correlated underlying ISIN")
            else:
                get_correlated_underlying = get_underlying_values(get_correlated_underlying)

                get_correlated_underlying = get_correlated_underlying.loc[
                    :,
                    [
                        CrossProductColumns.UNDERLYING_COL,
                        OrderField.META_ID,
                        OrderField.META_KEY,
                    ],
                ]
                get_correlated_underlying.loc[:, CrossProductColumns.RELATED_SUB_TYPE] = (
                    ExplicitlyCorrelated.SAME_UNDERLYING
                )

                map_data_isin = (
                    pd.merge(
                        map_data_isin,
                        get_correlated_underlying,
                        left_on=CrossProductColumns.INDEX_ISIN,
                        right_on=CrossProductColumns.UNDERLYING_COL,
                    )
                    .dropna()
                    .drop_duplicates()
                )

            # if both are empty reset the map_data_isin to empty
            if get_correlated_isin.empty and get_correlated_underlying.empty:
                map_data_isin = pd.DataFrame()

            del get_correlated_underlying, get_correlated_isin

        index_root_ticker_to_fetch = (
            map_data_root_ticker[CrossProductColumns.ROOT_TICKER].unique().tolist()
        )

        if index_root_ticker_to_fetch:
            # map from Index Root Ticker to Root Symbol
            get_correlated_root = self.query.get_filter_root(
                list_of_root=index_root_ticker_to_fetch,
                start=start,
                end=end,
                source_keys=source_keys,
            )

            # remove original records
            get_correlated_root = self.remove_original_orders_from_related(
                related_df=get_correlated_root
            )

            if get_correlated_root.empty:
                logger.warning("No orders were found for the correlated root ticker")
                # if is empty reset the map_data_root_ticker to empty
                map_data_root_ticker = pd.DataFrame()
            else:
                get_correlated_root = get_correlated_root.loc[
                    :,
                    [
                        OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
                        OrderField.META_ID,
                        OrderField.META_KEY,
                    ],
                ]

                get_correlated_root.loc[:, CrossProductColumns.RELATED_SUB_TYPE] = (
                    ExplicitlyCorrelated.SAME_ROOT
                )

                map_data_root_ticker = (
                    pd.merge(
                        map_data_root_ticker,
                        get_correlated_root,
                        left_on=CrossProductColumns.ROOT_TICKER,
                        right_on=OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
                    )
                    .dropna()
                    .drop_duplicates()
                )

                del get_correlated_root

        if map_data_isin.empty and map_data_root_ticker.empty:
            logger.warning("Overlap between records and correlated records empty")
            return pd.DataFrame()

        # Concat the maps with results
        final_map = pd.concat([map_data_isin, map_data_root_ticker])

        del map_data_root_ticker, map_data_isin

        final_map.loc[:, CrossProductColumns.RELATED_TYPE] = RelatedType.EXPLICITLY_CORRELATED

        final_map = create_missing_columns(records=final_map, columns=REQUIRED_COLUMNS)

        return final_map

    def implicitly_related(
        self, start: int, end: int, source_keys: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """This method is responsible for the implicitly related check which
        does:

            - filter root symbols and get the mapped implicitly related
            - fetches records with the mapped root tickers
            - creates dict with multiple dicts, one for each related record

        https://steeleye.atlassian.net/browse/ENG-6812
        :param source_keys: list of files to filter the search
        :param start: int, epoch value for start timestamp
        :param end: int, epoch value for end timestamp
        :return: dictionary with data for each implicitly related instrument found
        """

        if OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT not in self.original_orders.columns:
            logger.warning("No root symbols available to check implicitly related")
            return pd.DataFrame()

        if self.query.root_symbol_df.empty:
            logger.info("No root symbol data retrieved")
            return pd.DataFrame()

        df_with_root_symbol = self.original_orders.dropna(
            subset=[OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT]
        )

        if df_with_root_symbol.empty:
            logger.warning("No root symbols available to check implicitly related")
            return pd.DataFrame()

        record_root_mapped: Dict[str, str] = (
            df_with_root_symbol.groupby(OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT)[
                OrderField.META_KEY
            ]
            .apply(list)
            .to_dict()
        )

        root_symbols_list: List[str] = list(
            set(df_with_root_symbol.loc[:, OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT].values)
        )

        root_symbol_mask = self.query.root_symbol_df.loc[:, CrossProductColumns.ROOT_TICKER_1].isin(
            root_symbols_list
        )

        root_symbol_mapping_dataframe = self.query.root_symbol_df.loc[
            root_symbol_mask,
            [CrossProductColumns.ROOT_TICKER_1, CrossProductColumns.ROOT_TICKER_2],
        ].drop_duplicates()

        if root_symbol_mapping_dataframe.empty:
            logger.warning(
                f"No root symbol mapping were found for the root tickers: {root_symbols_list}"
            )
            return pd.DataFrame()

        root_symbol_mapping_dict: Dict[str, str] = (
            root_symbol_mapping_dataframe.groupby(CrossProductColumns.ROOT_TICKER_2)[
                CrossProductColumns.ROOT_TICKER_1
            ]
            .apply(list)
            .to_dict()
        )

        list_of_mapped_root_symbols = (
            root_symbol_mapping_dataframe.loc[:, CrossProductColumns.ROOT_TICKER_2]
            .unique()
            .tolist()
        )

        related_records: pd.DataFrame = self.query.get_filter_root(
            list_of_root=list_of_mapped_root_symbols,
            start=start,
            end=end,
            source_keys=source_keys,
        )

        if related_records.empty:
            logger.warning(
                f"No records were found for the mapped root tickers: {list_of_mapped_root_symbols}"
            )
            return pd.DataFrame()

        filtered_related_records = self.map_related_implicit_records_to_original(
            related_records=related_records,
            record_root_mapped_dict=record_root_mapped,
            root_symbol_mapping_dict=root_symbol_mapping_dict,
        )

        filtered_related_records.loc[:, CrossProductColumns.RELATED_TYPE] = (
            RelatedType.IMPLICITLY_RELATED
        )

        return filtered_related_records

    def related_activity(
        self, related_records_dict: Dict[str, List[Dict[str, str]]]
    ) -> pd.DataFrame:
        """https://steeleye.atlassian.net/browse/ENG-6813."""

        if not related_records_dict:
            logger.warning("Empty related records dictionary")
            return pd.DataFrame()

        original_records = list(set(related_records_dict.keys()))

        original_records_instrument_mask = self.original_orders.loc[:, OrderField.META_KEY].isin(
            original_records
        )

        # Selects the instrument unique identifiers of original records to get RIC/market data

        if OrderField.INST_EXT_UNIQUE_IDENT not in self.original_orders.columns:
            logger.warning(
                f"Instrument column {OrderField.INST_EXT_UNIQUE_IDENT} is not present in self.original_orders."
            )
            return pd.DataFrame()

        original_records_instruments = self.original_orders.loc[
            original_records_instrument_mask, OrderField.INST_EXT_UNIQUE_IDENT
        ]

        related_records_df = create_df_from_result(related_records_dict)

        if OrderField.INST_EXT_UNIQUE_IDENT not in related_records_df.columns:
            logger.warning(
                f"Instrument column {OrderField.INST_EXT_UNIQUE_IDENT} is not present in related_records_df."
            )
            return pd.DataFrame()

        related_records_unique_identifiers = list(
            related_records_df.loc[:, OrderField.INST_EXT_UNIQUE_IDENT].dropna().unique()
        )

        instruments_map_ric = list(
            {*original_records_instruments, *related_records_unique_identifiers}
        )

        ric_map = self.query.market_data_client.get_ric_map(list_inst_unique_id=instruments_map_ric)

        if ric_map is None:
            logger.warning(f"No RIC's were found for {instruments_map_ric}")
            return pd.DataFrame()

        mapped_instruments = new_instrument_ric_map(
            instruments_ids=instruments_map_ric, instrument_id_ric_mapping=ric_map
        )

        related_records_df[CrossProductColumns.RIC] = related_records_df.loc[
            :, OrderField.INST_EXT_UNIQUE_IDENT
        ].map(dict(mapped_instruments))

        self.original_orders[CrossProductColumns.RIC] = self.original_orders.loc[
            :, OrderField.INST_EXT_UNIQUE_IDENT
        ].map(dict(mapped_instruments))

        self.original_orders[OrderField.TS_ORD_SUBMITTED] = pd.to_datetime(
            self.original_orders.loc[:, OrderField.TS_ORD_SUBMITTED], format="mixed"
        )

        self.original_orders.loc[:, CrossProductColumns.DATE] = self.original_orders.loc[
            :, OrderField.TS_ORD_SUBMITTED
        ].dt.date

        # Fetch and calculate daily returns for original records
        self.original_orders.loc[:, CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS] = pd.NA

        for ric, group in self.original_orders.groupby(CrossProductColumns.RIC):
            dates = list(group.loc[:, CrossProductColumns.DATE].unique())

            market_data_daily_returns = self.query.get_daily_return(ric=ric, dates=dates)

            self.original_orders.loc[:, CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS] = (
                group.loc[:, CrossProductColumns.DATE].map(market_data_daily_returns)
            )

        self.original_orders = self.original_orders.rename(
            columns={
                OrderField.META_KEY: CrossProductColumns.ORIGINAL_RECORD_METAKEY,
                OrderField.TRX_DTL_VENUE: CrossProductColumns.ORIGINAL_RECORD_VENUE,
            }
        )

        # Added to avoid issues where one of these fields is not populated
        self.original_orders = create_missing_columns(
            records=self.original_orders,
            columns=[
                CrossProductColumns.ORIGINAL_RECORD_VENUE,
            ],
        )

        # Merge to add the required columns for scenario
        related_records_df = related_records_df.merge(
            self.original_orders.loc[
                :,
                [
                    CrossProductColumns.DATE,
                    CrossProductColumns.ORIGINAL_RECORD_METAKEY,
                    CrossProductColumns.ORIGINAL_RECORD_DAILY_RETURNS,
                    CrossProductColumns.ORIGINAL_RECORD_VENUE,
                ],
            ],
            on=CrossProductColumns.ORIGINAL_RECORD_METAKEY,
        )

        # Check if venues match between original and related records
        related_records_df.loc[:, CrossProductColumns.VENUE_MATCHING_TYPE] = (
            related_records_df.loc[:, OrderField.TRX_DTL_VENUE]
            == related_records_df.loc[:, CrossProductColumns.ORIGINAL_RECORD_VENUE]
        )

        related_records_df.loc[:, CrossProductColumns.RELATED_RECORD_DAILY_RETURNS] = pd.NA

        # Fetch and calculate daily returns for related records
        for ric, group in related_records_df.groupby(CrossProductColumns.RIC):
            dates = list(group.loc[:, CrossProductColumns.DATE].unique())

            market_data_daily_returns = self.query.get_daily_return(ric=ric, dates=dates)

            related_records_df.loc[
                group.index, CrossProductColumns.RELATED_RECORD_DAILY_RETURNS
            ] = group.loc[:, CrossProductColumns.DATE].map(market_data_daily_returns)

        # Calculation and categorization of correlation
        related_records_with_correlation = calculate_correlation(data=related_records_df)

        return related_records_with_correlation

    @staticmethod
    def map_related_implicit_records_to_original(
        related_records: pd.DataFrame,
        record_root_mapped_dict: Dict[str, str],
        root_symbol_mapping_dict: Dict[str, str],
    ) -> pd.DataFrame:
        """Method that creates columns required to generate the results of
        implicit check by mapping related records to original records.

        :param related_records: implicitly related records dataframe
        :param record_root_mapped_dict: dictionary with root symbol mapping to original records
        :param root_symbol_mapping_dict: dictionary with mapped root symbol to original root symbol
        :return: data with the related instruments data with column with meta key of the original record
        """
        related_records_filtered = create_missing_columns(
            records=related_records, columns=REQUIRED_COLUMNS
        )

        related_records_filtered = related_records_filtered.loc[:, REQUIRED_COLUMNS]

        related_records_filtered.loc[:, CrossProductColumns.ORIGINAL_ROOT_SYMBOL] = (
            related_records_filtered.loc[:, OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT].map(
                root_symbol_mapping_dict
            )
        )

        related_records_filtered = related_records_filtered.explode(
            column=CrossProductColumns.ORIGINAL_ROOT_SYMBOL
        )

        related_records_filtered.loc[:, CrossProductColumns.ORIGINAL_RECORD_METAKEY] = (
            related_records_filtered.loc[:, CrossProductColumns.ORIGINAL_ROOT_SYMBOL].map(
                record_root_mapped_dict
            )
        )
        return related_records_filtered

    @staticmethod
    def create_initial_map(
        df_filtered: pd.DataFrame, subset: List[str], additional_col: str
    ) -> pd.DataFrame:
        """Creates the initial dataframe for the mapping of the relation
        between ISIN and the required attributes.

        :df_filtered: pd.Dataframe, with the filter data to be analysed
        :subset: list with columns to be integrated in the mapping (at the moment only supports OrderField.INST_ID_CODE
                and an additional column)
        :additional_col: additional column to be considered in the map (required to be in the df_filtered), this column
                        is always considered for the mapping

        :returns: pd.Dataframe, with the initial map composed with the META_KEY, INST_ID_CODE,
        """

        # Get the column initial map dataframe

        if OrderField.INST_ID_CODE in subset:
            map_isin = df_filtered.loc[:, [OrderField.META_KEY, OrderField.INST_ID_CODE]].dropna(
                subset=[OrderField.INST_ID_CODE, OrderField.META_KEY]
            )
            map_underlying = (
                df_filtered.loc[:, [OrderField.META_KEY, additional_col]]
                .dropna(subset=[additional_col, OrderField.META_KEY])
                .rename(columns={additional_col: OrderField.INST_ID_CODE})
            )

            map_dataframe = (
                pd.concat([map_isin, map_underlying]).drop_duplicates().reset_index(drop=True)
            )

            del map_underlying, map_isin

        else:
            map_dataframe = (
                df_filtered.loc[:, [OrderField.META_KEY, additional_col]]
                .dropna(subset=[additional_col, OrderField.META_KEY])
                .rename(
                    columns={
                        additional_col: OrderField.INST_ID_CODE,
                    }
                )
            )

        return map_dataframe.rename(
            columns={
                OrderField.INST_ID_CODE: CrossProductColumns.ORIGINAL_ISIN,
                OrderField.META_KEY: CrossProductColumns.ORIGINAL_RECORD_METAKEY,
            }
        )

    def enrich_scenario(self, related_activity: pd.DataFrame):
        if related_activity.empty:
            self.scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][ScenarioFields.TOP_LEVEL][
                ScenarioFields.RELATED_ACTIVITY_DETECTED
            ] = False
            logger.warning("No related records with relative activity were detected")
            return self.scenario

        result_dict = related_activity.loc[:, OUTPUT_COLUMNS].to_dict(orient="records")
        self.scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][ScenarioFields.TOP_LEVEL][
            ScenarioFields.RELATED_ACTIVITY_DETECTED
        ] = True
        self.scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][ScenarioFields.TOP_LEVEL][
            ScenarioFields.RELATED_RECORDS
        ] = result_dict

        return self.scenario

    def add_related_records_to_scenario(self, all_related_orders_base: pd.DataFrame):
        """Method that inserts the received pd.DataFrame with the related
        records and inserts them into the current scenario.

        :param all_related_orders_base: pd.DataFrame with the related records to add to the scenario
        """
        for column in OUTPUT_COLUMNS:
            if column not in all_related_orders_base.columns:
                all_related_orders_base[column] = pd.NA

        result_dict = all_related_orders_base.loc[:, OUTPUT_COLUMNS].to_dict(orient="records")
        self.scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][ScenarioFields.TOP_LEVEL][
            ScenarioFields.RELATED_RECORDS
        ] = result_dict
