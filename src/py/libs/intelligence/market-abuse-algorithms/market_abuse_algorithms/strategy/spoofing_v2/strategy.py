import pandas as pd
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    add_vol_and_percentage_level_to_market_data,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.mar_audit.mar_audit import Aggregated<PERSON><PERSON><PERSON><PERSON><PERSON>, StepAudit
from market_abuse_algorithms.strategy.abstract_spoofing_v2.query import Queries
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    AlertColumnsEnum,
    AlgoColumnsEnum,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy import (
    SpoofingV2AbstractStrategy,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from se_elastic_schema.components.mar.strategy.spoofing_v2.thresholds import (
    SpoofingV2Thresholds,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import Tuple


class Strategy(SpoofingV2AbstractStrategy):
    """Spoofing V2 with order book depth level 2.

    <PERSON>ra tickets for the logic:
    https://steeleye.atlassian.net/browse/EP-159
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.SPOOFING_V2,
            queries=Queries,
            thresholds=SpoofingV2Thresholds,
        )
        self._th_spoof_order_percentage_level: int = self.context.thresholds.dict().get(
            ThresholdsNames.SPOOF_ORDER_PERCENTAGE_OF_LEVEL
        )

        self._refinitiv_client = self._market_data_client

    def step_6_7_get_params_at_level_apply_threshold(
        self,
        market_data: pd.DataFrame,
        tenant_data: pd.DataFrame,
        start_time_of_method: str,
        number_of_dropped_records: int,
        groupings_no: int,
        ric: str,
    ) -> Tuple[pd.DataFrame, int]:
        """
        For each order calculate
                vVolumeAtLevel = 7.1 Order Book Depth -
                vPercentageOfLevel = [Order Quantity] / vVolumeAtLevel
                vLevelOfOrderBook = retrieve the [Level of Order Book] each order was placed

        Apply step 7 at the end
        :param market_data:
        :param tenant_data:
        :param start_time_of_method: str with the time the method start
        :param number_of_dropped_records: int,
        :param groupings_no: int,
        :param ric: str
        :return:
        """

        data_with_level_of_order: pd.DataFrame = self._refinitiv_client.select_level_of_order_book(
            order_book_data=market_data,
            tenant_data=tenant_data,
            level_of_order_book_col=AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK,
        )

        data_with_level_of_order: pd.DataFrame = data_with_level_of_order.dropna(
            subset=[AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK]
        )

        end, method_id = self.get_start_time_and_unique_id()

        if data_with_level_of_order.empty:
            number_of_dropped_records += 1
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=tenant_data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"No Level of Order for RIC {ric}.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start_time_of_method,
                    end=end,
                    number_of_dropped_orders=len(tenant_data),
                    number_of_input_orders=len(tenant_data),
                    number_of_resulting_orders=0,
                    step_name="Fetch the LEVEL_OF_ORDER_BOOK from the order book depth data.",
                    step_type=StepType.MARKET_DATA_OBD,
                    drop_reason=DropReason.RECORDS_DROPPED_OBD_DATA,
                    groupings=groupings_no,
                    groupings_dropped=number_of_dropped_records,
                )
            )

            self.START_TIME = end

            self._logger.debug(f"No Level of Order for ric {ric}.")
            return pd.DataFrame(), number_of_dropped_records

        method_start = end

        valid_spoof_groups: pd.DataFrame = add_vol_and_percentage_level_to_market_data(
            data=data_with_level_of_order,
            market_data=market_data,
            level_of_order_book_col=AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK,
            volume_level_col=AlertColumnsEnum.VOLUME_LEVEL,
            percentage_level_col=AlertColumnsEnum.PERCENTAGE_LEVEL,
        )

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            StepAudit(
                step_id=method_id,
                list_of_order_ids=valid_spoof_groups.get(OrderField.META_KEY, pd.Series()).tolist(),  # noqa: E501
                reason="Getting volume and percentage level",
            )
        )
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=method_start,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(tenant_data),
                number_of_resulting_orders=len(tenant_data),
                step_name="Adding volume and percentage level",
                step_type=StepType.MARKET_DATA,
                groupings=groupings_no,
            )
        )

        (
            valid_spoof_groups,
            groupings_dropped,
        ) = self.step_7_apply_percentage_level_threshold(
            tenant_data_with_level=valid_spoof_groups,
            percentage_threshold=self._th_spoof_order_percentage_level,
            groupings_no=groupings_no,
            number_of_dropped_records=number_of_dropped_records,
        )

        return valid_spoof_groups, number_of_dropped_records

    def step_6_get_market_data(self, ric: str, dates: dict) -> pd.DataFrame:
        """Get market data to use for spoofing. For this case we want obd data.

        :param ric: str
        :param dates: dict with start and end dates. {"start": timestamp, "end": timestamp}
        :return: pd.DataFrame with market data
        """
        return self._refinitiv_client.get_order_book_depth_data(
            ric=ric, start_date=dates.get("start"), end_date=dates.get("end")
        )
