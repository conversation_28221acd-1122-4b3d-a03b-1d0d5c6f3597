import pandas as pd
import time
from elasticsearch_dsl.query import Exists
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import NumberOfCounterparties, StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.phishing_v2.static import ThresholdsNames
from market_abuse_algorithms.utils.data import process_numeric_columns
from typing import List, Optional


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_counterparties_involved = self.context.thresholds.dict().get(
            ThresholdsNames.NUMBER_OF_COUNTERPARTIES_INVOLVED
        )
        self._th_orders_in_opposite_direction = self.context.thresholds.dict().get(
            ThresholdsNames.ORDERS_IN_OPPOSITE_DIRECTION
        )

    def cases_to_analyse(self):
        query = self._get_orders_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._get_orders_query(inst_comb=inst_comb)

            query = self.get_initial_query(query)

            result = self._sdp_repository.search_after_query(query=query)

            result: pd.DataFrame = self.filter_newos_by_child_executions_fields(
                orders_df=result,
                mode="must_not",
                fields={OrderField.EXC_DTL_ORD_STATUS: [OrderStatus.CAME]},
                no_child=True,
            )

            if result.empty:
                self._logger.info(f"No data was found for the group of instruments: {inst_comb}")
                continue

            result: pd.DataFrame = process_numeric_columns(data=result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    def _get_orders_query(self, inst_comb: Optional[List[str]] = None) -> OrderQuery:
        q = OrderQuery()

        mandatory_fields = [OrderField.TS_ORD_SUBMITTED, OrderField.PC_FD_INIT_QTY]

        if self._th_counterparties_involved == NumberOfCounterparties.SINGLE:
            mandatory_fields.append(OrderField.COUNTERPARTY_ID)

        if self._th_orders_in_opposite_direction:
            mandatory_fields.append(OrderField.EXC_DTL_BUY_SELL_IND)

        includes = [
            OrderField.META_ID,
            OrderField.META_KEY,
            *mandatory_fields,
            *OrderField.get_instrument_fields(),
        ]

        mandatory_conditions = [Exists(field=field) for field in mandatory_fields]

        q.add_condition(mode="filter", conditions=mandatory_conditions)

        q.includes(includes)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    def get_additional_fields_for_scenarios(self, keys: List[str]) -> pd.DataFrame:
        q = OrderQuery()

        q.key(keys)

        includes = [
            OrderField.META_KEY,
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            OrderField.INST_FULL_NAME,
        ]

        q.includes(includes)

        results = self._sdp_repository.search_after_query(query=q)

        if results.empty:
            return pd.DataFrame()

        results = results.loc[:, results.columns.isin(includes)].set_index(OrderField.META_KEY)

        return results
