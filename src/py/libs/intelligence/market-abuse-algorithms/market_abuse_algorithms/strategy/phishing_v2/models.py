from market_abuse_algorithms.strategy.base.models import (
    CommonThresholds,
    NumberOfCounterparties,
    TimeUnit,
)
from pydantic import Field, validator
from pydantic.dataclasses import dataclass
from typing import Optional


@dataclass
class TimeWindow:
    unit: TimeUnit
    value: int = Field(..., ge=0)

    @validator("unit")
    def check_unit(cls, v):
        valid_units = [
            TimeUnit.MICROSECONDS,
            TimeUnit.MILLISECONDS,
            TimeUnit.SECONDS,
            TimeUnit.MINUTES,
        ]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_minutes = 400
        lower_bound_minutes = 1

        upper_bounds = {
            TimeUnit.MICROSECONDS: upper_bound_minutes * 60 * 1e6,
            TimeUnit.MILLISECONDS: upper_bound_minutes * 60 * 1e3,
            TimeUnit.SECONDS: upper_bound_minutes * 60,
            TimeUnit.MINUTES: upper_bound_minutes,
        }

        lower_bounds = {
            TimeUnit.MICROSECONDS: lower_bound_minutes * 60 * 1e6,
            TimeUnit.MILLISECONDS: lower_bound_minutes * 60 * 1e3,
            TimeUnit.SECONDS: lower_bound_minutes * 60,
            TimeUnit.MINUTES: lower_bound_minutes,
        }

        upper_bound = upper_bounds.get(unit)
        lower_bound = lower_bounds.get(unit)

        if v < lower_bound or v > upper_bound:
            raise ValueError(
                f"Failed check {lower_bound} < {v} (value) < {upper_bound}. Unit: {unit}"
            )

        return v


class Thresholds(CommonThresholds):
    timeWindow: TimeWindow
    percentageQuantityOfLargeOrder: float = Field(..., ge=0.01, le=1, description="percentage")
    minNumberOfSmallOrders: int = Field(..., ge=1, le=100)
    numberOfCounterpartiesInvolved: Optional[NumberOfCounterparties]
    ordersInOppositeDirection: Optional[bool]
    mergedAlerts: Optional[bool]
