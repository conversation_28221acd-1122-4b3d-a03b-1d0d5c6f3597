from enum import Enum
from market_abuse_algorithms.strategy.abstract_marking_the_price.static import (
    DFColumns as _DFColumns,
)


class DFColumns(_DFColumns):
    CLOSING_TIME_END_LOCAL = "closingTimeEndLocal"
    CLOSING_TIME_START_LOCAL = "closingTimeStartLocal"


class CloseType(str, Enum):
    MARKING_THE_CLOSE = "Marking the close"
    MARKING_THE_CLOSE_AUCTION = "Marking the close-auction"
    MARKING_THE_CLOSE_INC_CLOSE_AUCTION = "Marking the close (including close-auction)"
