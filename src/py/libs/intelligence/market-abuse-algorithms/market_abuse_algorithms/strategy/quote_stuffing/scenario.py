import addict
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders"])

    def _build_scenario(self):
        sample = self._result["sample"]
        date = self._result["date"]
        orders_cancelled = self._result["orders_cancelled"]

        top_level = dict(
            date=date.isoformat(),
            instrumentFullName=sample[OrderField.INST_FULL_NAME],
            instrumentId=sample[NewColumns.INSTRUMENT_CODE],
            venue=sample[NewColumns.VENUE],
            totalVolume=orders_cancelled[OrderField.PC_FD_INIT_QTY].sum(),
            numberOfCancelledOrders=len(orders_cancelled.index),
            bestExAssetClassMain=sample.get(OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN),
            bestExAssetClassSub=sample.get(OrderField.INST_EXT_BEST_EX_ASSET_CLASS_SUB),
        )

        arrival_times = (
            orders_cancelled[[OrderField.META_KEY, "arrival_time", "arrival_cancel_time"]]
            .rename(
                columns={
                    "arrival_time": "arrivalTimeWindow",
                    "arrival_cancel_time": "arrivalCancelTime",
                }
            )
            .set_index(OrderField.META_KEY)
            .to_dict("index")
        )

        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records.orders = sorted(orders_cancelled[OrderField.META_KEY].unique().tolist())
        d.additionalFields.topLevel = top_level
        d.additionalFields.orders = arrival_times
        scenario = pd.Series(d.to_dict())

        return scenario
