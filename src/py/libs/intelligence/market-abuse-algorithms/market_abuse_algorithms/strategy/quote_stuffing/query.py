import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import (
    OrderExecutionsQuery,
    OrderQuery,
    OrderStatus,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import process_numeric_columns


def get_timestamp_order_fields():
    return [OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED]


class Queries(BaseQuery):
    REQUIRED_FIELDS = [
        OrderField.META_KEY,
        OrderField.PC_FD_INIT_QTY,
        OrderField.ORD_IDENT_ID_CODE,
        get_timestamp_order_fields(),
        OrderField.get_venue_fields(),
        OrderField.get_instrument_fields(),
    ]
    INCLUDES_FIELDS = [
        OrderField.META_KEY,
        OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
        OrderField.INST_EXT_BEST_EX_ASSET_CLASS_SUB,
        *OrderField.get_instrument_fields(),
        *OrderField.get_venue_fields(),
        *get_timestamp_order_fields(),
        OrderField.INST_FULL_NAME,
        OrderField.PC_FD_INIT_QTY,
        OrderField.ORD_IDENT_ID_CODE,
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

    def cases_to_analyse(self):
        query = self._execs_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._execs_query(inst_comb)
            query = self.get_initial_query(query)

            query_result = self._sdp_repository.search_after_query(query=query)

            result = self.process_result_response(query_result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    @staticmethod
    def _execs_query(instr_comb=None):
        q = OrderExecutionsQuery()

        q.order_status(OrderStatus.CAME)

        if instr_comb:
            q.instrument_id(instr_comb)

        return q

    def get_orders_newo(self, orders_list):
        query = OrderQuery()
        query = self.get_initial_query(query)

        query.order_id(orders_list)
        query.includes(self.INCLUDES_FIELDS)

        result = self._sdp_repository.search_after_query(query=query)

        return result

    @staticmethod
    def process_result_response(result: pd.DataFrame) -> pd.DataFrame:
        if (OrderField.TS_ORD_SUBMITTED not in result.columns) and (
            OrderField.TS_ORD_UPDATED in result.columns
        ):
            result[OrderField.TS_ORD_SUBMITTED] = result[OrderField.TS_ORD_UPDATED]

        elif OrderField.TS_ORD_SUBMITTED and OrderField.TS_ORD_UPDATED in result.columns:
            result[OrderField.TS_ORD_SUBMITTED] = result[OrderField.TS_ORD_SUBMITTED].fillna(
                result[OrderField.TS_ORD_UPDATED]
            )

        result: pd.DataFrame = process_numeric_columns(data=result)
        return result
