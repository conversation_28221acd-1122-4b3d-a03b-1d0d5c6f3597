import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.quote_stuffing.models import Thresholds
from market_abuse_algorithms.strategy.quote_stuffing.query import Queries
from market_abuse_algorithms.strategy.quote_stuffing.scenario import Scenario
from market_abuse_algorithms.strategy.quote_stuffing.static import ThresholdsNames


class Strategy(AbstractStrategy):
    """Quote Stuffing.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-254

    Jira tickets for the front-end:
        - https://steeleye.atlassian.net/browse/PR-523
        - https://steeleye.atlassian.net/browse/PR-586
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.QUOTE_STUFFING,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_arrival_cancel_time = self.context.thresholds.dict().get(
            ThresholdsNames.ARRIVAL_CANCEL_TIME
        )
        self._th_min_cancel_orders = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_CANCELLED_ORDERS
        )
        self._th_arrival_time_window = self.context.thresholds.dict().get(
            ThresholdsNames.ARRIVAL_TIME_WINDOW
        )

    def _apply_strategy(self):
        for data in self.queries.cases_to_analyse():
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data):
        cols = (
            [NewColumns.INSTRUMENT_CODE, NewColumns.VENUE]
            if NewColumns.VENUE in data.columns
            else NewColumns.INSTRUMENT_CODE
        )

        for i, group in data.groupby(cols):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                self._run_algo(group)

    def _run_algo(self, orders: pd.DataFrame):
        results = self._algo(orders_cancelled=orders)

        if not results:
            return

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def _algo(self, orders_cancelled: pd.DataFrame):
        results = []

        orders_newo = self.queries.get_orders_newo(
            orders_cancelled[OrderField.ORD_IDENT_ID_CODE].tolist()
        )

        if orders_newo.empty:
            return results

        orders = pd.merge(
            orders_cancelled,
            orders_newo[[OrderField.TS_ORD_SUBMITTED, OrderField.ORD_IDENT_ID_CODE]],
            on=OrderField.ORD_IDENT_ID_CODE,
            suffixes=["_cancelled", "_newo"],
        )

        for date, group in orders.groupby(
            orders[OrderField.TS_ORD_SUBMITTED + "_cancelled"].dt.date
        ):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                self._logger.info(f"Analysing {date}...")
                if len(group.index) < self._th_min_cancel_orders:
                    continue

                group = group.copy()

                # check arrival cancel time
                group["arrival_cancel_time"] = abs(
                    group[OrderField.TS_ORD_SUBMITTED + "_newo"]
                    - group[OrderField.TS_ORD_SUBMITTED + "_cancelled"]
                ).dt.total_seconds()

                group = group[group["arrival_cancel_time"] <= self._th_arrival_cancel_time]

                if group.empty:
                    continue

                # NOTE: to make sure the dataframe is sorted chronologically
                group = group.sort_values(by=OrderField.TS_ORD_SUBMITTED + "_cancelled")

                # check arrival time
                group["arrival_time"] = abs(
                    group[OrderField.TS_ORD_SUBMITTED + "_cancelled"]
                    - group[OrderField.TS_ORD_SUBMITTED + "_cancelled"].shift(1)
                ).dt.total_seconds()

                group = group[group["arrival_time"] <= self._th_arrival_time_window]

                if len(group.index) < self._th_min_cancel_orders:
                    continue

                sample = group.iloc[0]
                group = group[
                    [
                        OrderField.META_KEY,
                        OrderField.PC_FD_INIT_QTY,
                        "arrival_time",
                        "arrival_cancel_time",
                    ]
                ]

                result = dict(date=date, sample=sample, orders_cancelled=group)

                results.append(result)

        return results
