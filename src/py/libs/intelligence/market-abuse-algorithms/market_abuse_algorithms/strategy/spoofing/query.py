import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import process_numeric_columns
from typing import List


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

    def cases_to_analyse(self):
        query = self._execs_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._execs_query(inst_comb=inst_comb)
            query = self.get_initial_query(query)

            result = self._sdp_repository.search_after_query(query=query)
            result: pd.DataFrame = process_numeric_columns(data=result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    @staticmethod
    def _execs_query(inst_comb=None) -> OrderExecutionsQuery:
        q = OrderExecutionsQuery()

        q.order_status([OrderStatus.FILL, OrderStatus.PARF, OrderStatus.CAME])

        # Includes
        includes = [
            OrderField.META_KEY,
            OrderField.EXC_DTL_ORD_STATUS,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.TS_ORD_UPDATED,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.PC_FD_TRD_QTY,
            OrderField.PC_FD_INIT_QTY,
            OrderField.EXC_DTL_VALIDITY_PERIOD,
            *OrderField.get_instrument_fields(),
        ]

        q.includes(includes)

        # Exists
        exists = [OrderField.EXC_DTL_ORD_STATUS, OrderField.EXC_DTL_BUY_SELL_IND]

        for field in exists:
            q.exists(field=field)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    def get_additional_fields_for_scenarios(self, keys: List[str]) -> pd.DataFrame:
        q = OrderExecutionsQuery()

        q.key(keys)

        q.order_status([OrderStatus.FILL, OrderStatus.PARF, OrderStatus.CAME])

        includes = [
            OrderField.META_KEY,
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            OrderField.INST_FULL_NAME,
        ]

        q.includes(includes)

        results = self._sdp_repository.search_after_query(query=q)

        if results.empty:
            return pd.DataFrame()

        results = results.loc[:, results.columns.isin(includes)].set_index(OrderField.META_KEY)

        return results
