from enum import Enum


class ThresholdsNames(str, Enum):
    TIME_WINDOW = "timeWindow"
    CANCELLATIONS_EXECUTIONS_VOLUME = "cancellationsExecutionsVolume"


class DFColumns:
    CANCELLED_EXECUTIONS_QTY_RATIO = "cancelledExecutionsQuantityRatio"
    CANCELLED_ORDERS = "cancelledOrders"
    CANCELLED_QTY_SUM = "cancelledQuantitySum"
    DATE = "date"
    EXECUTIONS_ORDERS = "executionsOrders"
    EXECUTIONS_QTY_SUM = "executionsQuantitySum"
    MAX_ORD_CANC_TIME_WINDOW = "maxOrdersCancelledTimeWindow"
    MAX_EXEC_TS = "maxExecutionsTS"
    MIN_CANC_TS = "minCancelledTS"
    TIME_DIFFERENCE = "timeDifference"
