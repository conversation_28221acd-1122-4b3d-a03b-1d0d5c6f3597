import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders", "executions"])

    def _build_scenario(self):
        scenario = dict(
            thresholds=self._thresholds,
            records={
                "orders": self._result.pop("orders"),
                "executions": self._result.pop("executions"),
            },
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(scenario)

        return scenario
