import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import (  # noqa: E501
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyLog, StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.spoofing.models import Thresholds
from market_abuse_algorithms.strategy.spoofing.query import Queries
from market_abuse_algorithms.strategy.spoofing.scenario import Scenario
from market_abuse_algorithms.strategy.spoofing.static import DFColumns, ThresholdsNames


class Strategy(AbstractStrategy):
    """Spoofing.

    Jira tickets for the logic:
        -

    <PERSON><PERSON> tickets for the front-end:
        - https://steeleye.atlassian.net/browse/PR-524
        - https://steeleye.atlassian.net/browse/PR-626
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.SPOOFING,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_time_window = self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)
        self._th_canc_exec_vol = self.context.thresholds.dict().get(
            ThresholdsNames.CANCELLATIONS_EXECUTIONS_VOLUME
        )

    def _apply_strategy(self):
        for data in self.queries.cases_to_analyse():
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        if NewColumns.INSTRUMENT_CODE not in data.columns:
            self._logger.info("There isn't a column with instrument code to do the grouping.")
            return pd.DataFrame()

        for i, group in data.groupby(NewColumns.INSTRUMENT_CODE):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                self._logger.info(StrategyLog.TOP_LEVEL.format(info=f"{i}"))
                self._run_algo(data=group)

    def _run_algo(self, data: pd.DataFrame):
        results = self._algo(data=data)

        if results.empty:
            return

        self._create_scenarios(df=results)

    def _algo(self, data: pd.DataFrame) -> pd.DataFrame:
        if (
            OrderField.EXC_DTL_ORD_STATUS not in data.columns
            or OrderField.TS_ORD_UPDATED not in data.columns
            or OrderField.PC_FD_TRD_QTY not in data.columns
        ):
            instrument_id: str = data.loc[:, NewColumns.INSTRUMENT_CODE].unique().tolist()[0]
            self._logger.info(
                f"Missing columns on the dataset for this instrument: {instrument_id}. "
                f" Continue to the next batch of data."
            )
            return pd.DataFrame()

        cancelled_orders_mask = (data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.CAME) & (
            data.loc[:, OrderField.TS_ORD_UPDATED].notna()
        )

        if cancelled_orders_mask.sum() == 0:
            return pd.DataFrame()

        cancelled_orders = data.loc[cancelled_orders_mask].sort_values(by=OrderField.TS_ORD_UPDATED)  # noqa: E501

        executions_orders_mask = (
            (data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.FILL, OrderStatus.PARF]))
            & (data.loc[:, OrderField.TS_ORD_UPDATED].notna())
            & (data.loc[:, OrderField.PC_FD_TRD_QTY].notna())
        )

        if executions_orders_mask.sum() == 0:
            return pd.DataFrame()

        executions_orders = data.loc[executions_orders_mask].sort_values(
            by=OrderField.TS_TRADING_DATE_TIME
        )

        df = (
            cancelled_orders[[OrderField.TS_ORD_UPDATED, OrderField.EXC_DTL_BUY_SELL_IND]]
            .groupby([OrderField.TS_ORD_UPDATED, OrderField.EXC_DTL_BUY_SELL_IND])
            .count()
            .reset_index()
        )

        # Max order cancel TS
        df[DFColumns.MAX_ORD_CANC_TIME_WINDOW] = df[OrderField.TS_ORD_UPDATED] + pd.Timedelta(
            seconds=self._th_time_window
        )

        # Cancelled orders within time frame
        df[DFColumns.CANCELLED_ORDERS] = df[
            [
                OrderField.TS_ORD_UPDATED,
                OrderField.EXC_DTL_BUY_SELL_IND,
                DFColumns.MAX_ORD_CANC_TIME_WINDOW,
            ]
        ].apply(
            lambda x: cancelled_orders.loc[
                (
                    cancelled_orders[OrderField.EXC_DTL_BUY_SELL_IND]
                    == x[OrderField.EXC_DTL_BUY_SELL_IND]
                )
                & (cancelled_orders[OrderField.TS_ORD_UPDATED] >= x[OrderField.TS_ORD_UPDATED])
                & (
                    cancelled_orders[OrderField.TS_ORD_UPDATED]
                    < x[DFColumns.MAX_ORD_CANC_TIME_WINDOW]
                ),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        # Executions within time frame
        df[DFColumns.EXECUTIONS_ORDERS] = df[
            [
                OrderField.TS_ORD_UPDATED,
                OrderField.EXC_DTL_BUY_SELL_IND,
                DFColumns.MAX_ORD_CANC_TIME_WINDOW,
            ]
        ].apply(
            lambda x: executions_orders.loc[
                (
                    executions_orders[OrderField.EXC_DTL_BUY_SELL_IND]
                    != x[OrderField.EXC_DTL_BUY_SELL_IND]
                )
                & (
                    executions_orders[OrderField.TS_TRADING_DATE_TIME]
                    >= x[OrderField.TS_ORD_UPDATED]
                )
                & (
                    executions_orders[OrderField.TS_TRADING_DATE_TIME]
                    < x[DFColumns.MAX_ORD_CANC_TIME_WINDOW]
                ),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        # time window threshold
        time_window_mask = df[DFColumns.EXECUTIONS_ORDERS].apply(len) > 0

        df = df.loc[time_window_mask]

        if df.empty:
            return pd.DataFrame()

        # Quantities
        if OrderField.PC_FD_INIT_QTY not in cancelled_orders.columns:
            return pd.DataFrame()

        if OrderField.PC_FD_TRD_QTY not in executions_orders.columns:
            return pd.DataFrame()

        df[DFColumns.CANCELLED_QTY_SUM] = df[DFColumns.CANCELLED_ORDERS].apply(
            lambda x: cancelled_orders.loc[
                cancelled_orders[OrderField.META_KEY].isin(x),
                OrderField.PC_FD_INIT_QTY,
            ].sum()
        )

        df[DFColumns.EXECUTIONS_QTY_SUM] = df[DFColumns.EXECUTIONS_ORDERS].apply(
            lambda x: executions_orders.loc[
                executions_orders[OrderField.META_KEY].isin(x),
                OrderField.PC_FD_TRD_QTY,
            ].sum()
        )

        df[DFColumns.CANCELLED_EXECUTIONS_QTY_RATIO] = (
            df[DFColumns.CANCELLED_QTY_SUM] / df[DFColumns.EXECUTIONS_QTY_SUM]
        )

        # ratio threshold
        ratio_mask = df[DFColumns.CANCELLED_EXECUTIONS_QTY_RATIO] > self._th_canc_exec_vol

        df = df.loc[ratio_mask]

        if df.empty:
            return pd.DataFrame()

        df[DFColumns.DATE] = df[OrderField.TS_ORD_UPDATED].dt.date

        df[DFColumns.MAX_EXEC_TS] = df[DFColumns.EXECUTIONS_ORDERS].apply(
            lambda x: executions_orders.loc[
                executions_orders[OrderField.META_KEY].isin(x),
                OrderField.TS_TRADING_DATE_TIME,
            ].max()
        )

        df[DFColumns.TIME_DIFFERENCE] = df[DFColumns.MAX_EXEC_TS] - df[OrderField.TS_ORD_UPDATED]

        df[DFColumns.TIME_DIFFERENCE] = df[DFColumns.TIME_DIFFERENCE].dt.total_seconds()

        return df

    def _create_scenarios(self, df: pd.DataFrame):
        # NOTE: it considers all cancellations are grouped by instrument
        df["key_index"] = df[DFColumns.CANCELLED_ORDERS].apply(lambda x: x[0])
        df = df.set_index("key_index")
        keys = df.index.unique().tolist()

        additional_fields = self.queries.get_additional_fields_for_scenarios(keys=keys)

        df = pd.concat([df, additional_fields], axis=1)

        fields_to_scenario_map = {
            DFColumns.CANCELLED_ORDERS: "orders",
            DFColumns.EXECUTIONS_ORDERS: "executions",
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN: "bestExAssetClassMain",
            OrderField.INST_FULL_NAME: "instrumentFullName",
            DFColumns.DATE: DFColumns.DATE,
            NewColumns.INSTRUMENT_CODE: "instrumentId",
            DFColumns.TIME_DIFFERENCE: DFColumns.TIME_DIFFERENCE,
            DFColumns.CANCELLED_EXECUTIONS_QTY_RATIO: "volumeRatio",
        }

        df = df.loc[:, df.columns.isin(fields_to_scenario_map.keys())]

        df.columns = df.columns.map(fields_to_scenario_map)

        results = df.to_dict(orient="records")

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)
