import pandas as pd
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    AlertColumnsEnum,
)
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields


class SpoofingV2AbstractAlert(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["fakeOrders", "executions", "realOrders"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "executions": self._result.pop(AlertColumnsEnum.ORDERS_STATE_KEYS),
            "fakeOrders": self._result.pop(AlertColumnsEnum.FAKE_ORDERS),
            "realOrders": self._result.pop(AlertColumnsEnum.REAL_ORDERS),
        }
        alert = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(alert)

        return scenario
