# ruff: noqa: E501
import logging
import pandas as pd
import time
from elasticsearch_dsl.query import Terms
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.cross_product.static import CrossProductColumns
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    Buy<PERSON><PERSON>,
    OrderField,
    OrderStatus,
    OrderType,
)
from market_abuse_algorithms.mar_audit.mar_audit import StepAudit
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    ORDER_TYPE_GROUP_MAP,
    AlertColumnsEnum,
    AlgoColumnsEnum,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts, StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.utils.data import get_venue_fallback
from market_abuse_algorithms.utils.filters import (
    add_real_orders_exec_fields,
    filter_market_facing_orders,
    filter_market_visible_orders,
)
from market_abuse_algorithms.utils.processment import (
    get_query_string_to_audit,
    new_instrument_combination_with_rics,
)
from typing import List, Optional, Union


class Queries(BaseQuery):
    include_fields = [
        OrderField.EXC_DTL_ORD_STATUS,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.EXC_DTL_LIMIT_PRICE,
        OrderField.EXC_DTL_ORD_TYPE,
        OrderField.EXC_DTL_VALIDITY_PERIOD,
        OrderField.META_KEY,
        OrderField.PC_FD_TRD_QTY,
        OrderField.PC_FD_INIT_QTY,
        OrderField.RPT_DTL_EXC_ENTITY_NAME,
        OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
        OrderField.TRADER_NAME,
        OrderField.TRX_DTL_ULTIMATE_VENUE,
        OrderField.TS_ORD_UPDATED,
        OrderField.TS_TRADING_DATE_TIME,
        OrderField.COUNTERPARTY_ID,
        OrderField.TRD_ALGO_FILE_IDENTIFIER,
        OrderField.CLIENT_FILE_IDENTIFIER,
        OrderField.TRADER_FILE_IDENTIFIER,
        *OrderField.get_instrument_fields(),
    ]

    fields_for_alert = [
        OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE,
        OrderField.CLIENT_IDENT_CLIENT,
        OrderField.INST_FULL_NAME,
        OrderField.INST_ID_CODE,
        *OrderField.get_involved_parties_fields(),
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PC_FD_PRICE,
        OrderField.TRADER_NAME,
        OrderField.TRD_ALGO_FIRM_NAME,
        OrderField.TRD_ALGO_FIRM_DESKS_NAME,
        *OrderField.get_venue_fields(),
    ]

    REQUIRED_FIELDS = [OrderField.META_KEY]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)
        self._market_data_client = get_market_client(tenant=context.tenant)
        evaluation_type: str = self.context.thresholds.dict().get(ThresholdsNames.EVALUATION_TYPE)
        self._th_evaluation_type: str = ORDER_TYPE_GROUP_MAP.get(evaluation_type)

        self._th_include_partial_cancellation: bool = self.context.thresholds.dict().get(
            ThresholdsNames.INCLUDE_PARTIALLY_CANCELLATIONS
        )
        self._th_real_order_percentage_fill: float = self.context.thresholds.dict().get(
            ThresholdsNames.REAL_ORDER_PERCENTAGE_FILLED
        )

    @property
    def market_data_client(self):
        return self._market_data_client

    def cases_to_analyse(self, strategy_name: str):
        """
        Fetched the spoof orders data to be analysed
        :return: Yields dataframes with spoof orders
        """
        query: OrderQuery = self._spoof_query()

        query: OrderQuery = self.get_initial_query(query, inspect_required_fields=True)

        market_abuse_audit_object.query = get_query_string_to_audit(query=query)

        agg_script = QueryAggScripts.VENUE_SCRIPT
        market_data_level = 2

        if strategy_name == StrategyName.SPOOFING_V2_LEVEL1:
            agg_script = QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV
            market_data_level = 1

        instruments_combinations: List = self.get_instruments_combinations(
            query=query,
            agg_script=agg_script,
        )

        instruments_combinations = [
            [inst.replace("[", "").replace("]", "") for inst in inst_comb]
            for inst_comb in instruments_combinations
        ]

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = (
            self._market_data_client.get_ric_map(instrument_combinations=instruments_combinations)
            if market_data_level == 1
            else self._market_data_client.get_local_ric_map(
                instrument_combinations=instruments_combinations
            )
        )

        new_instrument_combinations_with_ric: List = new_instrument_combination_with_rics(
            instrument_combinations=instruments_combinations,
            instrument_ric_mapping=instrument_id_ric_mapping,
        )
        for instruments_and_rics in new_instrument_combinations_with_ric:
            start = time.perf_counter()
            instruments = [inst.split(":")[-1] for inst, ric in instruments_and_rics]
            query: OrderQuery = self._spoof_query(inst_comb=instruments)
            query: OrderQuery = self.get_initial_query(query)

            result: pd.DataFrame = self.scroll_data(query=query)

            processed_result_include_parf_condition: pd.DataFrame = (
                self.process_include_parf_condition(
                    data=result, include_parf=self._th_include_partial_cancellation
                )
            )

            filtered_result: pd.DataFrame = self._filter_orders(
                data=processed_result_include_parf_condition
            )

            if filtered_result.empty:
                logging.warning(f"No data to analyse for instrument {instruments}")
                continue

            filtered_result[OrderField.INST_ID_VENUE_KEY] = (
                filtered_result[OrderField.INST_VENUE_TRD_VENUE]
                + ":"
                + filtered_result[OrderField.INST_ID_CODE]
            )

            instrument_col = OrderField.INST_ID_VENUE_KEY
            if strategy_name == StrategyName.SPOOFING_V2_LEVEL1:
                instrument_col = OrderField.INST_EXT_UNIQUE_IDENT

            filtered_result[AlgoColumnsEnum.RIC] = filtered_result[instrument_col].map(
                dict(instruments_and_rics)
            )

            self._logger.info(
                f"For a instrument combination of size {len(instruments)}, it took {time.perf_counter() - start} seconds"
            )
            yield filtered_result

    def scroll_data(self, query: Union[OrderQuery, OrderField]) -> pd.DataFrame:
        return self._sdp_repository.search_after_query(query=query)

    def _spoof_query(self, inst_comb=None) -> OrderQuery:
        """Creates instance of OrderQuery to fetch spoof orders.

        :param inst_comb: combination of instruments to be fetched
        :return: Instance of OrderQuery
        """
        query: OrderQuery = OrderQuery()

        include_fields = [
            *self.include_fields,
            OrderField.META_ID,
            OrderField.TS_ORD_SUBMITTED,
            self._th_evaluation_type,
            *self.fields_for_alert,
        ]

        query.includes(include_fields)

        for field in self.REQUIRED_FIELDS:
            query.exists(field=field)

        if inst_comb:
            query.instrument_id(inst_comb)

        return query

    def _filter_orders(self, data: pd.DataFrame) -> pd.DataFrame:
        """Filter the orders according to the first step of the algo.

        - the order is "Market Facing Orders” AND
        - the order is "Visibile Market Orders” AND
        - [Evaluation Type].EVALUATION_TYPE_GROUPING_MAP is populated AND
        - [Order Status] = “NEWO” AND # condition added in the query
        - [BuySell].isPopulated()
        - Check if the Cancelled Order Types # already added within the query (method process_include_parf_condition)
        :return: pd.DataFrame. Filtered data
        """
        if OrderField.EXC_DTL_BUY_SELL_IND not in data.columns:
            logging.warning(
                f"Column {OrderField.EXC_DTL_BUY_SELL_IND} not in NEWO data."
                f" This is mandatory field."
            )
            return pd.DataFrame()

        if self._th_evaluation_type not in data.columns:
            logging.warning(
                f"Column {self._th_evaluation_type} not in NEWO data. This is mandatory field."
            )
            return pd.DataFrame()

        buy_sell_nan: pd.DataFrame = data.dropna(subset=[OrderField.EXC_DTL_BUY_SELL_IND])

        if buy_sell_nan.empty:
            logging.warning(
                f"After removing NaN from {OrderField.EXC_DTL_BUY_SELL_IND} column, the dataframe is empty."
                f" This is mandatory field."
            )
            return pd.DataFrame()

        evaluation_type_nan: pd.DataFrame = buy_sell_nan.dropna(subset=[self._th_evaluation_type])
        if evaluation_type_nan.empty:
            logging.warning(
                f"After removing NaN from {self._th_evaluation_type} column, the dataframe is empty."
                f" This is mandatory field."
            )
            return pd.DataFrame()

        venue_column: str = get_venue_fallback(data_columns=evaluation_type_nan.columns)

        evaluation_type_nan.loc[:, AlgoColumnsEnum.VENUE] = evaluation_type_nan.loc[:, venue_column]

        market_facing_orders: pd.DataFrame = filter_market_facing_orders(
            data=evaluation_type_nan, venue_column=venue_column
        )

        if market_facing_orders.empty:
            logging.warning("No data to analyse, after applying the market_forcing_orders filter")
            return pd.DataFrame()

        if (
            OrderField.EXC_DTL_VALIDITY_PERIOD not in market_facing_orders.columns
            or OrderField.EXC_DTL_ORD_TYPE not in market_facing_orders.columns
        ):
            logging.warning(
                f"Columns {OrderField.EXC_DTL_VALIDITY_PERIOD}"
                f" or f{OrderField.EXC_DTL_ORD_TYPE} not in NEWO data."
            )

        market_visible_orders: pd.DataFrame = filter_market_visible_orders(
            data=market_facing_orders
        )

        if market_visible_orders.empty:
            logging.warning("No data to analyse, after applying the visible_market_orders filter")
            return pd.DataFrame()

        return market_visible_orders

    def get_spoof_execs(self, parent_ids: List[str]) -> pd.DataFrame:
        """Fetches spoof order's executions.

        :param parent_ids: list of parent IDs to get the child executions
        :return: Dataframe with child executions
        """

        query = OrderExecutionsQuery()

        query.add_terms_query_with_chunks(parent_ids, field=OrderField.META_PARENT, mode="filter")

        query.add_condition(
            mode="must_not",
            conditions=[Terms(**{OrderField.EXC_DTL_ORD_STATUS: [OrderStatus.NEWO]})],
        )

        includes_fields = [
            *self.include_fields,
            OrderField.META_PARENT,
            self._th_evaluation_type,
            *self.fields_for_alert,
        ]

        query.includes(includes_fields)

        result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        return result

    def fetch_real_orders(
        self,
        instrument: str,
        buy_sell_ind: str,
        spoof_evaluation: str,
        order_id: str,
        related_instrument: pd.DataFrame,
        method_id: Optional[str] = None,
    ) -> pd.DataFrame:
        """Uses instrument, buy sell indicator and order key to fetch real
        orders.

        “Market Facing Orders”  AND
        “Visibile Market Orders” AND
        “Top of Book Orders” = [Order Type].lower() = “Market”
        “Orders with Execution Events” = Orders w/ FILL/PARF
        “Required Evaluation Type” = t.[Evaluation Type].EVALUATION_TYPE_GROUPING_MAP is populated AND
        “New Orders”  AND
        “Same Instrument as Spoofing Order” = Where instrument is the same (Algo Abstractions | a.[Same-Instrument])  as one of the “Spoof Orders”
        “Opposite Side”
            If the Potential Spoofing Order is a BUY then: SELL
            elif Potential Spoofing Order is a SELL then: BUY

        “Same Evaluation Type”= where the EVALUATION_TYPE_GROUPING_MAP = the “Spoof Orders”.EVALUATION_TYPE_GROUPING_MAP
        i.e., if you’re searching for “Real Orders”,
        related to a “Spoof Orders” (where “Spoof Orders”.Trader = “Bob”, then the “Real Orders”.Trader = “Bob”)
        :param related_instrument: dataframe with all the related instruments fetched
        :param method_id: uuid to identify the method on the audit
        :param order_id: order key to be fetched
        :param instrument: instrument to be fetched
        :param buy_sell_ind: string indication of what the buy sell indicator is in the spoof orders
        :param spoof_evaluation: string with the value of the evaluation type on spoof orders
        :param order_id: string with the value of the order key, used for audit
        :return: Dataframe with real orders
        """
        buy_sell_indicator: str = BuySell.SELL if buy_sell_ind == BuySell.BUY else BuySell.BUY
        query: OrderQuery = self._real_orders_query(
            instrument=instrument, buy_sell_ind=buy_sell_indicator
        )

        first_cut_data: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        if first_cut_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason="Order without any child FILL/PARF.",
                )
            )
            logging.warning("Order without any child FILL/PARF.")
            return pd.NaT

        if self._th_real_order_percentage_fill > 0:
            first_cut_data: pd.DataFrame = self.filter_newos_by_child_executions_fields(
                orders_df=first_cut_data,
                mode="filter",
                fields={
                    OrderField.EXC_DTL_ORD_STATUS: [
                        OrderStatus.FILL,
                        OrderStatus.PARF,
                    ]
                },
            )

            if first_cut_data.empty:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=[order_id],
                        reason="Order without any child FILL/PARF.",
                    )
                )
                logging.warning("Order without any child FILL/PARF.")
                return pd.NaT

        first_cut_data.loc[:, CrossProductColumns.RELATED_INSTRUMENT] = False

        if not related_instrument.empty:
            # filter related where originalRecordMetaKey is the same as the order being evaluated (order_id)
            related_instrument = related_instrument[
                related_instrument[CrossProductColumns.ORIGINAL_RECORD_METAKEY].isin([order_id])
            ]

            # remove the real orders fetched above from the related_instrument
            related_instrument = related_instrument[
                ~related_instrument[OrderField.META_KEY].isin(
                    first_cut_data[OrderField.META_KEY].unique().tolist()
                )
            ]

            # if we have related left, concat with first_cut_data
            first_cut_data = pd.concat([first_cut_data, related_instrument]).reset_index(drop=True)

        if self._th_evaluation_type not in first_cut_data.columns:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason=f"The required column {self._th_evaluation_type} is not in NEWO data.",
                )
            )
            logging.warning(
                f"Column {self._th_evaluation_type} not in NEWO data. This is mandatory field."
            )
            return pd.NaT

        evaluation_type_nan: pd.DataFrame = first_cut_data.dropna(subset=[self._th_evaluation_type])

        if evaluation_type_nan.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason=f"After removing NaN from {self._th_evaluation_type} column, the dataframe is empty."
                    f" This is mandatory field.",
                )
            )
            logging.warning(
                f"After removing NaN from {self._th_evaluation_type} column, the dataframe is empty."
                f" This is mandatory field."
            )
            return pd.NaT

        equal_evaluation_type_mask: pd.Series = (
            evaluation_type_nan.loc[:, self._th_evaluation_type] == spoof_evaluation
        )
        same_evaluation_type_data: pd.DataFrame = evaluation_type_nan.loc[
            equal_evaluation_type_mask
        ]

        if same_evaluation_type_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason=f"No data with the same evaluation type: {self._th_evaluation_type}.",
                )
            )
            logging.warning(
                f"No data with the same evalution type: {self._th_evaluation_type}. The dataframe is empty."
            )
            return pd.NaT

        market_facing_real_orders: pd.DataFrame = filter_market_facing_orders(
            data=same_evaluation_type_data,
            venue_column=OrderField.TRX_DTL_ULTIMATE_VENUE,
        )

        if market_facing_real_orders.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason="No data to analyse after filtering for market facing orders.",
                )
            )
            logging.warning("No data to analyse, after applying the market_facing_orders filter")
            return pd.NaT

        visible_market_real_orders: pd.DataFrame = filter_market_visible_orders(
            data=market_facing_real_orders
        )

        if visible_market_real_orders.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason="No data to analyse after filtering for visible market orders.",
                )
            )
            logging.warning("No data to analyse, after applying the visible_market_orders filter")
            return pd.NaT

        top_of_book_orders: pd.DataFrame = visible_market_real_orders[
            visible_market_real_orders.loc[:, OrderField.EXC_DTL_ORD_TYPE].str.lower()
            == OrderType.MARKET.lower()
        ]

        if top_of_book_orders.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason="No data to analyse after filtering for market type orders.",
                )
            )
            logging.warning("No data to analyse, after applying the filter for market type orders.")
            return pd.NaT

        parent_ids: List[str] = top_of_book_orders.loc[:, OrderField.META_ID].unique().tolist()

        real_orders_execution_data: pd.DataFrame = self.get_real_execs(parent_ids=parent_ids)

        result: pd.DataFrame = add_real_orders_exec_fields(
            real_order_percentage_fill=self._th_real_order_percentage_fill,
            real_orders=top_of_book_orders,
            real_execs=real_orders_execution_data,
            execution_quantity_column=AlertColumnsEnum.REAL_ORDERS_EXECUTION_QUANTITY,
            filled_percentage_column=AlgoColumnsEnum.REAL_ORDERS_FILLED_PERCENTAGE,
        )

        if result.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[order_id],
                    reason="No real order data",
                )
            )
            return pd.NaT

        return result

    def _real_orders_query(
        self,
        instrument: Optional[str] = None,
        buy_sell_ind: Optional[str] = None,
        iris_filters: bool = True,
    ) -> OrderQuery:
        """Creates real orders OrderQuery to be used to fetch data.

        :param instrument: string with instrument to be fetched
        :param buy_sell_ind: string indication of what the buy sell indicator is in the spoof orders
        :return: OrderQuery instance to fetch real orders
        """
        q = OrderQuery()

        if isinstance(instrument, str):
            q.add_terms_query_with_chunks(
                [instrument], field=OrderField.INST_ID_CODE, mode="filter"
            )
        else:
            self._logger.info(
                f"The provided instrument {instrument} is not a string. Query won't filter on this field."
            )

        include_fields = [
            *self.include_fields,
            OrderField.META_ID,
            OrderField.TS_ORD_SUBMITTED,
            self._th_evaluation_type,
            *self.fields_for_alert,
        ]

        if isinstance(buy_sell_ind, str):
            q.buy_sell(value=buy_sell_ind)
        else:
            self._logger.info(
                f"The provided buy/sell indicator {buy_sell_ind} is not a string. Query won't filter on this field."
            )

        if iris_filters:
            q.add_iris_filters(filters=self._filters)

        q.includes(include_fields)

        for field in self.REQUIRED_FIELDS:
            q.exists(field=field)

        return q

    def get_real_execs(self, parent_ids: List[str]) -> pd.DataFrame:
        """Fetches real orders executions using the parent ids.

        :param parent_ids: list of strings with the parent ids
        :return: dataframe with execution data for real orders
        """

        query = OrderExecutionsQuery()

        query.add_terms_query_with_chunks(parent_ids, field=OrderField.META_PARENT, mode="filter")

        query.add_condition(
            mode="must_not",
            conditions=[Terms(**{OrderField.EXC_DTL_ORD_STATUS: [OrderStatus.NEWO]})],
        )

        includes_fields = [
            *self.include_fields,
            OrderField.META_PARENT,
            self._th_evaluation_type,
            *self.fields_for_alert,
        ]

        query.includes(includes_fields)

        result = self._sdp_repository.search_after_query(query=query)

        return result

    def process_include_parf_condition(
        self, data: pd.DataFrame, include_parf: bool
    ) -> pd.DataFrame:
        """Adds conditions to spoof order query to include PARF or not.

        :param data: dataframe for the spoof orders
        :param include_parf: bool regarding the inclusion of PARF or not
        :return: filtered dataframe
        """
        parent_ids: List[str] = []
        if OrderField.META_ID in data.columns:
            parent_ids: List[str] = data[OrderField.META_ID].unique().tolist()

        if not parent_ids:
            self._logger.warning(
                "Not able to get the parent IDs, so impossible to evaluate the include_parf condition."
            )
            return pd.DataFrame()

        newo_execution_child: pd.DataFrame = self.get_real_execs(parent_ids=parent_ids)

        if newo_execution_child.empty:
            self._logger.warning(
                f"No child executions were found for these parent ids: {parent_ids}"
            )
            return pd.DataFrame()

        if include_parf:
            result: pd.DataFrame = newo_execution_child.groupby(OrderField.META_PARENT).filter(
                lambda group: self.include_parf(
                    execution_status=group[OrderField.EXC_DTL_ORD_STATUS].unique().tolist()
                )
            )
        else:
            result: pd.DataFrame = newo_execution_child.groupby(OrderField.META_PARENT).filter(
                lambda group: self.not_include_parf(
                    execution_status=group[OrderField.EXC_DTL_ORD_STATUS].unique().tolist()
                )
            )

        return data[data[OrderField.META_ID].isin(result[OrderField.META_PARENT].unique().tolist())]

    @staticmethod
    def include_parf(execution_status: List) -> bool:
        """Check Partially Cancelled Orders: they should have Cancelled
        executions and at least one FILL or PARF executio.

        :param execution_status: status to be analysed
        :return:
        """
        return OrderStatus.CAME in execution_status or OrderStatus.CAMO in execution_status

    @staticmethod
    def not_include_parf(execution_status: List) -> bool:
        """Check Fully Cancelled Orders: they should have Cancelled executions
        and at NONE FILL or PARF execution.

        :param execution_status: status to be analysed
        :return:
        """
        if (OrderStatus.CAME in execution_status or OrderStatus.CAMO in execution_status) and not (
            OrderStatus.FILL in execution_status or OrderStatus.PARF in execution_status
        ):
            return True

        return False
