import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.static import (
    TransactionVolumeCurrency,
)
from typing import Dict, Optional


def get_involved_parties(record: pd.Series):
    involved_parties_cols = list(
        {*[OrderField.COUNTERPARTY_NAME, NewColumns.CLIENT, NewColumns.TRADER_NAME]}
        & set(record.index)
    )

    involved_parties = sorted(record[involved_parties_cols].dropna().tolist())

    return involved_parties


def convert_parent_id_to_parent_key(
    data: pd.DataFrame, parent_meta_keys: Dict[str, str], new_def_column: str
) -> pd.DataFrame:
    """When data has OrderField.META_PARENT but we want the META_KEY of the
    parent instead of the &id This method gets the META_KEY and changes the
    value in the dataframe that corresponds to the &id, to &key of the parent.

    :param data: data to convert
    :param parent_meta_keys: dict with meta_id and meta_key from parent
    :param new_def_column: new column that will be created

    :return:
    """

    if parent_meta_keys:
        mask = data[OrderField.META_PARENT].isin(parent_meta_keys.keys())
        data.loc[mask, new_def_column] = data.loc[mask, OrderField.META_PARENT].map(
            parent_meta_keys
        )

    return data


def get_sentiment_price_column(
    data: pd.DataFrame, currency: TransactionVolumeCurrency
) -> Optional[str]:
    """To evaluate the sentiment threshold we need quantity columns (bestEx
    transaction volume / init quantity or traded quantity) this method will get
    the field to use based on the data columns This is only used for ITWN and
    the algo is grouped by RIC, meaning that at this point the
    bestExecutionData.orderVolume.nativeCurrency column will have always the
    same value in all rows.

    :param currency:
    :param data:
    :return:
    """
    if OrderField.BEST_EXC_DATA_ORD_VOL_NATIVE_CURRENCY in data.columns and all(
        data[OrderField.BEST_EXC_DATA_ORD_VOL_NATIVE_CURRENCY] == currency.value
    ):
        return OrderField.BEST_EXC_DATA_ORD_VOL_NATIVE

    if (
        OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency.value) in data.columns
        and (
            any(data[OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency.value)].isna())
            or any(data[OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency.value)] == 0)
        )
    ) or OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency.value) not in data.columns:
        if OrderField.PC_FD_INIT_QTY in data.columns:
            return OrderField.PC_FD_INIT_QTY

        if OrderField.PC_FD_TRD_QTY in data.columns:
            return OrderField.PC_FD_TRD_QTY

        return None

    return OrderField.get_best_exc_ord_ecb_ref_rate_ccy(currency.value)
