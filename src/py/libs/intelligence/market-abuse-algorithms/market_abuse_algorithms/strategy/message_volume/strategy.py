import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.message_volume.models import Thresholds
from market_abuse_algorithms.strategy.message_volume.query import Queries
from market_abuse_algorithms.strategy.message_volume.scenario import Scenario
from market_abuse_algorithms.strategy.message_volume.static import (
    MCG_GROUPING_FIELDS_MAP,
    DFColumns,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import create_time_window_groups, set_time_thresholds
from pandas import DataFrame
from pandas.core.groupby import DataFrameGroupBy
from se_elastic_schema.static.mifid2 import OrderStatus
from typing import List, NoReturn


class Strategy(AbstractStrategy):
    """Message Volume.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/DE-315

    Jira tickets for the front-end:
        - https://steeleye.atlassian.net/browse/PR-1829
        -
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.MESSAGE_VOLUME,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )

        self._th_minimum_message_count = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_MESSAGE_COUNT
        )

        self._th_order_events = self.context.thresholds.dict().get(ThresholdsNames.ORDER_EVENTS)

        self._th_message_count_grouping = self.context.thresholds.dict().get(
            ThresholdsNames.MESSAGE_COUNT_GROUPING
        )

        self._th_time_window = set_time_thresholds(
            time_field=self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)
        )

        self._th_exc_validity_periods = self.context.thresholds.dict().get(
            ThresholdsNames.EXECUTION_VALIDITY_PERIODS
        )

        self._th_restrict_alerts = self.context.thresholds.dict().get(
            ThresholdsNames.RESTRICT_ALERTS
        )

    def _apply_strategy(self):
        """Apply algorithm strategy based on the cases retrieved when executing
        query."""

        for data in self.queries.cases_to_analyse(mcg_fields_map=MCG_GROUPING_FIELDS_MAP):
            self._apply_strategy_mini_batch(data=data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        """Execute strategy batch.

        :param data: pd.DataFrame. Data retrieved from sdp repository
        """

        for mcg_group in self._th_message_count_grouping:
            mcg = MCG_GROUPING_FIELDS_MAP.get(mcg_group)
            if self._th_restrict_alerts:
                mcg.append(OrderField.TRX_DTL_ULTIMATE_VENUE)
                data[OrderField.TRX_DTL_ULTIMATE_VENUE] = data[
                    OrderField.TRX_DTL_ULTIMATE_VENUE
                ].fillna("Unknown Venue")

            if not all(field in data.columns for field in mcg):
                self._logger.info(f"Not all grouping fields from {mcg} are present in the dataset.")  # noqa: E501
                continue

            group_results: DataFrameGroupBy = data.groupby(mcg)
            for ix, group_data in group_results:
                with StrategyError.handle_algo_records_error(audit=self._audit, data=group_data):
                    self._run_algo(data=group_data)

    def _run_algo(self, data: pd.DataFrame):
        """Global method to execute the algorithm and then create algorithm
        scenarios.

        :param data: pd.DataFrame. Data retrieved from sdp repository
        """

        if OrderField.TS_ORD_UPDATED not in data.columns:
            self._logger.info(
                f"The column {OrderField.TS_ORD_UPDATED} is not present in the dataset."
            )
            return

        results = self._algo(data=data)

        if not results:
            self._logger.info("After running the algorithm, there aren't alerts created.")
            return

        for result in results:
            self._create_scenarios(data=result)

    def _algo(self, data: pd.DataFrame) -> List[DataFrame]:
        """Execute message volume algorithm: group by time window and then
        check message count threshold.

        :param data: pd.DataFrame. Data retrieved from sdp repository
        :return: List[pd.DataFrame]
        """
        algo_result: List[DataFrame] = []

        time_window_groups: List[pd.DataFrame] = create_time_window_groups(
            data=data,
            time_column=OrderField.TS_ORD_UPDATED,
            time_threshold=self._th_time_window,
            time_delta_column=DFColumns.TIMEDELTA,
        )

        if (not time_window_groups) or (len(time_window_groups) < 1):
            self._logger.info(
                "STEP 4 | No time groups were created or the number of records per window is < 1."
            )
            return algo_result

        for time_group in time_window_groups:
            if not self._check_count_threshold(data=time_group):
                self._logger.info(
                    "STEP 5 | For this time group, after applying the message count threshold no alerts were generated."  # noqa: E501
                )
                continue

            algo_result.append(time_group)

        return algo_result

    def _check_count_threshold(self, data: pd.DataFrame) -> bool:
        """Check message count threshold for the a the given OrderEvents.

        :param data: pd.DataFrame. Data retrieved from sdp that was already filtered and grouped
        :return:
        """
        orders_states_count: int = data.loc[
            data[OrderField.EXC_DTL_ORD_STATUS].isin(self._th_order_events)
        ].shape[0]

        return orders_states_count >= self._th_minimum_message_count

    def _create_scenarios(self, data: pd.DataFrame) -> NoReturn:
        """Create algorithm scenarios, which is a dictionary.

        :param data: pd.DataFrame. Data that was filtered and grouped
        """
        result = data.to_dict()

        earliest_order: DataFrame = data.iloc[0]
        latest_order: DataFrame = data.iloc[-1]

        if OrderField.INST_EXT_UNIQUE_IDENT in data.columns:
            result[DFColumns.INSTRUMENT_ID] = data[OrderField.INST_EXT_UNIQUE_IDENT].tolist()

            result[DFColumns.NUMBER_INSTRUMENTS] = data[OrderField.INST_EXT_UNIQUE_IDENT].nunique()

        result[DFColumns.MESSAGE_VOLUME_BEHAVIOUR] = " ".join(self._th_message_count_grouping)

        result[DFColumns.EARLIEST_TIMESTAMP] = earliest_order[OrderField.TS_ORD_UPDATED]
        result[DFColumns.LATEST_TIMESTAMP] = latest_order[OrderField.TS_ORD_UPDATED]

        result[DFColumns.TIMESTAMP_DIFFERENCE] = (
            data.iloc[-1][OrderField.TS_ORD_UPDATED] - data.iloc[0][OrderField.TS_ORD_UPDATED]
        )

        if OrderField.TRX_DTL_ULTIMATE_VENUE in data.columns:
            not_null_venue_mask = data[OrderField.TRX_DTL_ULTIMATE_VENUE].notnull()

            if not not_null_venue_mask.empty:
                result[DFColumns.EXECUTION_VENUES] = (
                    data.loc[not_null_venue_mask, OrderField.TRX_DTL_ULTIMATE_VENUE]
                    .unique()
                    .tolist()
                )

        if NewColumns.TRADER_NAME in data.columns:
            result[DFColumns.TRADER_LIST] = data[NewColumns.TRADER_NAME].unique().tolist()

        if OrderField.TRADER_FILE_IDENTIFIER in data.columns:
            result[DFColumns.TRADER_FILE_IDENTIFIER_LIST] = (
                data[OrderField.TRADER_FILE_IDENTIFIER].unique().tolist()
            )

        if OrderField.CLIENT_IDENT_CLIENT_NAME in data.columns:
            result[DFColumns.CLIENT_LIST] = (
                data[OrderField.CLIENT_IDENT_CLIENT_NAME].unique().tolist()
            )

        if OrderField.CLIENT_FILE_IDENTIFIER in data.columns:
            result[DFColumns.CLIENT_ID_LIST] = (
                data[OrderField.CLIENT_FILE_IDENTIFIER].unique().tolist()
            )

        if OrderField.PC_FD_INIT_QTY in data.columns:
            result[DFColumns.TOTAL_ORDER_QUANTITY] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO,
                OrderField.PC_FD_INIT_QTY,
            ].sum()

        if OrderField.EXC_DTL_ORD_STATUS in data.columns:
            result[DFColumns.NUMBER_NEWOS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO,
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

            result[DFColumns.NUMBER_REPLACEMENTS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS].isin(
                    [OrderStatus.REME, OrderStatus.REMA, OrderStatus.REMH]
                ),
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

            result[DFColumns.NUMBER_EXECUTIONS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.PARF, OrderStatus.FILL]),
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

            result[DFColumns.NUMBER_REJECTIONS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.REMO,
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

            result[DFColumns.NUMBER_CANCELLATIONS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.CAMO, OrderStatus.CAME]),
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

            result[DFColumns.NUMBER_AMENDMENTS] = data.loc[
                data[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.CHME, OrderStatus.CHMO]),
                OrderField.EXC_DTL_ORD_STATUS,
            ].count()

        if OrderField.META_PARENT not in data.columns:
            result[DFColumns.ORDERS] = data[OrderField.META_KEY].tolist()
        else:
            newos_mask = data[OrderField.META_PARENT].isnull()
            result[DFColumns.ORDERS] = data.loc[newos_mask, OrderField.META_KEY].tolist()

        result[DFColumns.ORDER_STATES] = data[
            data[OrderField.EXC_DTL_ORD_STATUS] != OrderStatus.NEWO
        ][OrderField.META_KEY].tolist()

        result: dict = dict(
            (key, result[key]) for key in DFColumns.get_cols_to_scenario() if key in result
        )

        scenario = Scenario(result=result, context=self.context)
        self.scenarios.append(scenario)
