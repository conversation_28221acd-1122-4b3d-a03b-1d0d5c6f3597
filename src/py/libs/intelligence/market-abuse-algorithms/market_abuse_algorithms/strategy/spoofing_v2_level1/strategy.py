import pandas as pd
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    add_vol_and_percentage_level_to_market_data,
    get_volume_at_level,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.mar_audit.mar_audit import Aggregated<PERSON><PERSON><PERSON><PERSON><PERSON>, StepAudit
from market_abuse_algorithms.strategy.abstract_spoofing_v2.query import Queries
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    AlertColumnsEnum,
    AlgoColumnsEnum,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.abstract_spoofing_v2.strategy import (
    SpoofingV2AbstractStrategy,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.utils.data import calculate_percentage_level
from se_elastic_schema.components.mar.strategy.spoofing_v2_level1.thresholds import (
    SpoofingV2Level1Thresholds,
)
from se_elastic_schema.static.mar_audit import StepType
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from typing import Tuple


class Strategy(SpoofingV2AbstractStrategy):
    """Spoofing V2 with order book depth level 2.

    Jira tickets for the logic:
    https://steeleye.atlassian.net/browse/EP-159
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.SPOOFING_V2_LEVEL1,
            queries=Queries,
            thresholds=SpoofingV2Level1Thresholds,
        )
        self._th_spoof_order_percentage_level1: int = self.context.thresholds.dict().get(
            ThresholdsNames.SPOOF_ORDER_PERCENTAGE_OF_LEVEL1
        )

    def step_6_7_get_params_at_level_apply_threshold(
        self,
        market_data: pd.DataFrame,
        tenant_data: pd.DataFrame,
        start_time_of_method: str,
        number_of_dropped_records: int,
        groupings_no: int,
        ric: str,
    ) -> Tuple[pd.DataFrame, int]:
        """
        L1 params
        For each order calculate
            vVolumeAtLevel:
                    if the Order is a Algo Abstractions | a.[Buy Order] then:
                        retrieve the [Bid Size] from he L1 QUOTE Parquets for the nearest time
                        before the [Order Time Submitted]

                    elif the Order is a Algo Abstractions | a.[Sell Order] then:
                        retrieve the [Ask Size] from he L1 QUOTE Parquets for the nearest time
                        before the [Order Time Submitted]

            vPercentageOfLevel = [Order Quantity] / vVolumeAtLevel
            vLevelOfOrderBook = “1”
        Apply step 7 at the end
        :param market_data:
        :param tenant_data:
        :param start_time_of_method: str with the time the method start
        :param number_of_dropped_records: int,
        :param groupings_no: int,
        :param ric: str
        :return:
        """
        tenant_data[AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK] = 1

        data_with_level_and_volume: pd.DataFrame = add_vol_and_percentage_level_to_market_data(
            data=tenant_data,
            market_data=market_data,
            volume_level_col=AlertColumnsEnum.VOLUME_LEVEL,
            percentage_level_col=AlertColumnsEnum.PERCENTAGE_LEVEL,
            level_of_order_book_flag=False,
        )

        end, method_id = self.get_start_time_and_unique_id()

        singleton_audit_object.write_audit_data_to_local_files(
            StepAudit(
                step_id=method_id,
                list_of_order_ids=data_with_level_and_volume.get(
                    OrderField.META_KEY, pd.Series()
                ).tolist(),
                reason="Getting volume and percentage level",
            )
        )

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=start_time_of_method,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(tenant_data),
                number_of_resulting_orders=len(tenant_data),
                step_name="Adding volume and percentage level",
                step_type=StepType.MARKET_DATA,
                groupings=groupings_no,
            )
        )

        (
            valid_spoof_groups,
            groupings_dropped,
        ) = self.step_7_apply_percentage_level_threshold(
            tenant_data_with_level=data_with_level_and_volume,
            percentage_threshold=self._th_spoof_order_percentage_level1,
            groupings_no=groupings_no,
            number_of_dropped_records=number_of_dropped_records,
        )

        return valid_spoof_groups, number_of_dropped_records

    def step_6_get_market_data(self, ric: str, dates: dict) -> pd.DataFrame:
        """Get market data to use for spoofing. For this case we want quote
        tick data (L1)

        :param ric: str
        :param dates: dict with start and end dates. {"start": timestamp, "end": timestamp}
        :return: pd.DataFrame with market data
        """
        return self._market_data_client.get_tick_data(
            dates=dates.get("timestamps"),
            event_type=RefinitivEventType.QUOTE,
            instrument_ric=ric,
        )

    @staticmethod
    def step_6_calc_volume_and_percentage_level(
        tenant_data_with_level: pd.DataFrame,
        market_data: pd.DataFrame,
        volume_level_col: str,
        percentage_level_col: str,
    ):
        """

        :return:
        """
        data = tenant_data_with_level.copy()

        data.loc[:, volume_level_col] = data.apply(
            lambda row: get_volume_at_level(
                market_data=market_data,
                order_time_submitted=row.loc[OrderField.TS_ORD_SUBMITTED],
                buy_sell=row.loc[OrderField.EXC_DTL_BUY_SELL_IND],
                level_of_order_book=None,
            ),
            axis=1,
        )

        data.loc[:, percentage_level_col] = data.apply(
            lambda row: calculate_percentage_level(
                volume_at_level_col=volume_level_col, single_order_data=row
            ),
            axis=1,
        )

        return data
