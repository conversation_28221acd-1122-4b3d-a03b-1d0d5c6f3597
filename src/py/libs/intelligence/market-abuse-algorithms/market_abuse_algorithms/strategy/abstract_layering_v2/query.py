# type: ignore
import datetime
import logging
import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.static import (
    EVALUATION_TYPE_GROUPING_MAP,
    AlgoColumnsEnum,
    ThresholdsNamesEnum,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts, StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.utils.data import get_venue_fallback, process_numeric_columns
from market_abuse_algorithms.utils.filters import (
    filter_market_facing_orders,
    filter_non_market_visible_orders,
)
from market_abuse_algorithms.utils.processment import (
    get_query_string_to_audit,
    new_instrument_combination_with_rics,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import List, Union


class Queries(BaseQuery):
    include_fields = [
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.EXC_DTL_ORD_STATUS,
        OrderField.EXC_DTL_ORD_TYPE,
        OrderField.EXC_DTL_LIMIT_PRICE,
        OrderField.EXC_DTL_VALIDITY_PERIOD,
        OrderField.INST_EXT_UNIQUE_IDENT,
        OrderField.META_ID,
        OrderField.META_KEY,
        OrderField.META_PARENT,
        OrderField.PC_FD_INIT_QTY,
        OrderField.PC_FD_TRD_QTY,
        OrderField.RPT_DTL_EXC_ENTITY_NAME,
        OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
        OrderField.TRADER_NAME,
        OrderField.TRX_DTL_PC_CCY,
        OrderField.TS_ORD_UPDATED,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.COUNTERPARTY_ID,
        OrderField.TRD_ALGO_FILE_IDENTIFIER,
        OrderField.CLIENT_FILE_IDENTIFIER,
        OrderField.TRADER_FILE_IDENTIFIER,
    ]

    fields_for_alert = [
        OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE,
        OrderField.CLIENT_IDENT_CLIENT,
        OrderField.INST_FULL_NAME,
        OrderField.INST_ID_CODE,
        *OrderField.get_involved_parties_fields(),
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PC_FD_PRICE,
        OrderField.TRADER_NAME,
        OrderField.TRD_ALGO_FIRM_NAME,
        OrderField.TRD_ALGO_FIRM_DESKS_NAME,
        *OrderField.get_venue_fields(),
    ]

    required_fields = [OrderField.META_KEY]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_evaluation_type = EVALUATION_TYPE_GROUPING_MAP.get(
            self.context.thresholds.dict().get(ThresholdsNamesEnum.EVALUATION_TYPE)
        )

        self._market_data_client = get_market_client(tenant=context.tenant)

    @property
    def market_data_client(self):
        return self._market_data_client

    def get_cases_to_analyse(self, strategy_name: str):
        """
        Get data to be analysed within the Layering algo
        :return:
        """
        if strategy_name == StrategyName.LAYERING_V2:
            self.required_fields.extend([OrderField.INST_ID_CODE, OrderField.INST_VENUE_TRD_VENUE])
        else:
            self.required_fields.append(OrderField.INST_EXT_UNIQUE_IDENT)

        query: OrderQuery = self._get_orders_query()
        query: OrderQuery = self.get_initial_query(query, inspect_required_fields=True)

        market_abuse_audit_object.query = get_query_string_to_audit(query=query)

        agg_script = QueryAggScripts.VENUE_SCRIPT
        market_data_level = 2

        if strategy_name == StrategyName.LAYERING_V2_LEVEL1:
            agg_script = QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV
            market_data_level = 1

        instruments_combinations: List = self.get_instruments_combinations(
            query=query,
            agg_script=agg_script,
        )

        # Method required to remove square brackets from result of instrument aggregation
        # The output was modified with script changes due to ES8
        # this is to format the output for layeringV2
        instruments_combinations = [
            [inst.replace("[", "").replace("]", "") for inst in inst_comb]
            for inst_comb in instruments_combinations
        ]

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = (
            self._market_data_client.get_ric_map(instrument_combinations=instruments_combinations)
            if market_data_level == 1
            else self._market_data_client.get_local_ric_map(
                instrument_combinations=instruments_combinations
            )
        )

        new_instrument_combinations_with_ric: List = new_instrument_combination_with_rics(
            instrument_combinations=instruments_combinations,
            instrument_ric_mapping=instrument_id_ric_mapping,
        )

        for instruments_and_rics in new_instrument_combinations_with_ric:
            start = time.perf_counter()
            start_get_cases, method_id = self.get_start_time_and_unique_id()
            instruments = [inst.split(":")[-1] for inst, ric in instruments_and_rics]

            query: OrderQuery = self._get_orders_query(list_of_instruments=instruments)
            query: OrderQuery = self.get_initial_query(query)

            newos_data: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

            result: pd.DataFrame = self._filter_orders(data=newos_data)

            result: pd.DataFrame = process_numeric_columns(data=result)

            if result.empty:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=[],
                        reason=f"No Orders found for instruments {instruments}",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start_get_cases,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=0,
                        number_of_input_orders=0,
                        number_of_resulting_orders=0,
                        step_name="Initial Data Retrieval",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                    )
                )
                logging.warning(f"No data to analyse for instrument {instruments}")
                continue
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start_get_cases,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=0,
                    number_of_resulting_orders=len(result),
                    step_name="Initial Data Retrieval",
                    step_type=StepType.FILTERS,
                )
            )

            if strategy_name == StrategyName.LAYERING_V2_LEVEL1:
                instrument_col = OrderField.INST_EXT_UNIQUE_IDENT
            else:
                result[OrderField.INST_ID_VENUE_KEY] = (
                    result[OrderField.INST_VENUE_TRD_VENUE] + ":" + result[OrderField.INST_ID_CODE]
                )
                instrument_col = OrderField.INST_ID_VENUE_KEY

            result[AlgoColumnsEnum.RIC] = result[instrument_col].map(dict(instruments_and_rics))

            self._logger.info(
                f"For a instrument combination of size {len(instruments)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    def _get_orders_query(self, list_of_instruments: List = None) -> OrderQuery():
        """
        :param: instruments: List of instruments to fetch data from
        :return:
        """
        query = OrderQuery()

        fields_to_include: List = [
            self._th_evaluation_type,
            *self.include_fields,
            *self.fields_for_alert,
        ]
        query.includes(fields_to_include)

        for field in self.required_fields:
            query.exists(field, mode="must")

        if list_of_instruments:
            query.instrument_id(list_of_instruments)

        return query

    def _filter_orders(self, data: pd.DataFrame) -> pd.DataFrame:
        """Filter the orders according to the first step of the algo.

        - the order is "Market Facing Orders” AND
        - the order is "Non Market Visibile Orders” AND
        - the Order is not "Unfully Filled Orders” AND
        - [Evaluation Type].EVALUATION_TYPE_GROUPING_MAP is populated AND
        - [Order Status] = “NEWO” AND # condition added in the query
        - [BuySell].isPopulated()

        :return: pd.DataFrame. Filtered data
        """
        if OrderField.EXC_DTL_BUY_SELL_IND not in data.columns:
            logging.warning(
                f"Column {OrderField.EXC_DTL_BUY_SELL_IND} not in NEWO data."
                f" This is mandatory field."
            )
            return pd.DataFrame()

        if self._th_evaluation_type not in data.columns:
            logging.warning(
                f"Column {self._th_evaluation_type} not in NEWO data. This is mandatory field."
            )
            return pd.DataFrame()

        data: pd.DataFrame = data.dropna(subset=[OrderField.EXC_DTL_BUY_SELL_IND])

        if data.empty:
            logging.warning(
                f"After removing NaN from {OrderField.EXC_DTL_BUY_SELL_IND} column, the dataframe is empty."  # noqa: E501
                f" This is mandatory field."
            )
            return pd.DataFrame()

        data: pd.DataFrame = data.dropna(subset=[self._th_evaluation_type])

        if data.empty:
            logging.warning(
                f"After removing NaN from {self._th_evaluation_type} column, the dataframe is empty."  # noqa: E501
                f" This is mandatory field."
            )
            return pd.DataFrame()

        venue_column: str = get_venue_fallback(data_columns=data.columns)

        data.loc[:, AlgoColumnsEnum.VENUE] = data.loc[:, venue_column]

        market_facing_orders: pd.DataFrame = filter_market_facing_orders(
            data=data, venue_column=venue_column
        )

        if market_facing_orders.empty:
            logging.warning("No data to analyse, after applying the market_forcing_orders filter")
            return pd.DataFrame()

        if (
            OrderField.EXC_DTL_VALIDITY_PERIOD not in market_facing_orders.columns
            and OrderField.EXC_DTL_ORD_TYPE not in market_facing_orders.columns
        ):
            logging.warning(
                f"Columns {OrderField.EXC_DTL_VALIDITY_PERIOD}"
                f" and f{OrderField.EXC_DTL_ORD_TYPE} not in NEWO data."
                f" This is mandatory to get the non market visible orders."
            )
            return pd.DataFrame()

        non_market_visible_orders: pd.DataFrame = filter_non_market_visible_orders(
            data=market_facing_orders
        )

        if non_market_visible_orders.empty:
            return pd.DataFrame()

        unfully_filled_orders: pd.DataFrame = self._get_unfully_filled_orders(
            data=non_market_visible_orders
        )

        return unfully_filled_orders

    def _get_unfully_filled_orders(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Logic to detect where a given order is unfull filled order
        :param: data. dataframe w/ NEWOs data
        :return: pd.DataFrame. Dataframe with the orders that aligned with the condition described
        """
        if OrderField.META_ID not in data.columns:
            logging.warning(
                f"Column {OrderField.META_ID} not in NEWO data."
                f" This is mandatory to get parent ids."
            )
            return pd.DataFrame()

        parent_ids: List[str] = data.loc[:, OrderField.META_ID].unique().tolist()

        unfilled_orders: List[str] = [
            order
            for order in parent_ids
            if self._check_if_order_unfilled(
                order_id=order, include_fields=self.include_fields, newos_data=data
            )
        ]

        return data[data[OrderField.META_ID].isin(unfilled_orders)]

    def _check_if_order_unfilled(
        self, order_id: str, include_fields: List, newos_data: pd.DataFrame
    ) -> bool:
        """check if the order is unfilled If the sum of the [Fill Quantity] !=

        [Order Quantity] then the order is a [UnFully Filled Order]

        :param order_id: Str, id of NEWO
        :param include_fields: List. fields that need to be included in _source
        :param newos_data: pd.DataFrame, parent orders data
        :return: bool
        """
        order_executions_query = OrderExecutionsQuery()

        order_executions_query.add_condition(
            mode="filter", conditions=[{"term": {OrderField.META_PARENT: order_id}}]
        )

        order_executions_query.includes(include_fields)

        order_executions_query.order_status(
            [
                OrderStatus.FILL,
                OrderStatus.PARF,
                OrderStatus.CHMO,
                OrderStatus.CHME,
                OrderStatus.REME,
                OrderStatus.REMH,
                OrderStatus.REMA,
            ]
        )

        for field in self.required_fields:
            order_executions_query.exists(field)

        order_execution_result: pd.DataFrame = self._sdp_repository.search_after_query(
            query=order_executions_query
        )

        if order_execution_result.empty:
            return True

        if OrderField.EXC_DTL_ORD_STATUS not in order_execution_result.columns:
            logging.warning(
                f"Column {OrderField.EXC_DTL_ORD_STATUS} not in Order State data."
                f" This is mandatory to get orders state."
            )
            return False

        mask_fill_parf: bool = order_execution_result[OrderField.EXC_DTL_ORD_STATUS].isin(
            [OrderStatus.FILL, OrderStatus.PARF]
        )

        fill_parf_data: pd.DataFrame = order_execution_result.loc[mask_fill_parf]

        if fill_parf_data.empty or OrderField.PC_FD_TRD_QTY not in fill_parf_data.columns:
            return True

        fill_quantity: float = fill_parf_data.loc[:, OrderField.PC_FD_TRD_QTY].sum()

        mask_replace_cancelled: bool = order_execution_result[OrderField.EXC_DTL_ORD_STATUS].isin(
            [
                OrderStatus.CHMO,
                OrderStatus.CHME,
                OrderStatus.REME,
                OrderStatus.REMH,
                OrderStatus.REMA,
            ]
        )

        order_quantity: Union[float, None] = None

        if not order_execution_result.loc[mask_replace_cancelled].empty:
            if OrderField.TS_ORD_UPDATED in order_execution_result.columns:
                sort_order_state: pd.DataFrame = order_execution_result.sort_values(
                    by=[OrderField.TS_ORD_UPDATED], ascending=False
                )
                if self._check_if_initial_quantity(data=sort_order_state):
                    order_quantity: Union[float, None] = sort_order_state.loc[
                        :, OrderField.PC_FD_INIT_QTY
                    ].iloc[0]
        else:
            if self._check_if_initial_quantity(data=newos_data):
                order_quantity: Union[float, None] = newos_data[
                    newos_data[OrderField.META_ID] == order_id
                ][OrderField.PC_FD_INIT_QTY].iloc[0]

        if order_quantity is None:
            logging.warning(
                f"Column {OrderField.PC_FD_INIT_QTY} doesn't exist."
                f" This is mandatory to get the order quantity."
            )
            return False

        if fill_quantity == order_quantity:
            return False

        return True

    @staticmethod
    def _check_if_initial_quantity(data: pd.DataFrame) -> bool:
        """check if the dataframe has the column initial_quantity.

        :param data:
        :return: bool, true if exists
        """

        return OrderField.PC_FD_INIT_QTY in data.columns
