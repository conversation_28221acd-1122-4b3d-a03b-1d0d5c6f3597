# ruff: noqa: E501
import datetime
import pandas as pd
from addict import Dict
from market_abuse_algorithms.cross_product.cross_product import (
    CrossProductActivityDisplay,
)
from market_abuse_algorithms.cross_product.utils import (
    convert_timestamp_to_epoch,
    create_df_from_result,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    BuySell,
    NewColumns,
    Order<PERSON>ield,
    OrderType,
)
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.alerts import (
    LayeringV2AbstractAlert,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.query import (
    Queries,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.static import (
    EVALUATION_TYPE_GROUPING_MAP,
    AlertColumnsEnum,
    AlgoColumnsEnum,
    ThresholdsNamesEnum,
)
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import Strategy<PERSON>ontext
from market_abuse_algorithms.strategy.base.static import Scena<PERSON><PERSON>ields
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.utils.data import (
    calculate_percentage,
    check_number_of_orders,
    create_time_window_groups,
    get_client_name,
    get_instrument_id_code,
    get_order_status_for_ids,
    get_unique_value_for_alert,
    get_venue_name,
    sort_by_date,
)
from se_elastic_schema.components.mar.strategy.layering_v2.thresholds import (
    BehaviourTypeEnum,
    LayeringV2Thresholds,
)
from se_elastic_schema.components.mar.strategy.layering_v2_level1.thresholds import (
    LayeringV2Level1Thresholds,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import List, NoReturn, Tuple, Type, Union


class LayeringV2AbstractStrategy(AbstractStrategy):
    """Layering Level 2.

    Confluence page for the logic:
        - https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1011351590/Layering+Book+Imbalance+-+V2

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/EP-153
    """

    ORDER_BOOK_DEPTH_COLUMNS = [
        AlertColumnsEnum.ASK_PRICE_LEVELS,
        AlertColumnsEnum.BID_PRICE_LEVELS,
        AlertColumnsEnum.BUY_ORDERS_QUANTITY,
        AlertColumnsEnum.PERCENTAGE_LEVEL,
        AlertColumnsEnum.SELL_ORDERS_QUANTITY,
        AlertColumnsEnum.VOLUME_LEVEL,
        AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK,
    ]

    def __init__(
        self,
        context: StrategyContext,
        strategy_name: str,
        queries: Type[Queries],
        thresholds: Union[LayeringV2Thresholds, LayeringV2Level1Thresholds],
    ):
        super().__init__(
            context=context,
            strategy_name=strategy_name,
            thresholds_class=thresholds,
            queries_class=queries,
        )

        self._th_behaviour_type: str = self.context.thresholds.dict().get(
            ThresholdsNamesEnum.BEHAVIOUR_TYPE
        )

        self._th_evaluation_type: str = EVALUATION_TYPE_GROUPING_MAP.get(
            self.context.thresholds.dict().get(ThresholdsNamesEnum.EVALUATION_TYPE)
        )

        self._th_layering_number_of_price_levels: int = self.context.thresholds.dict().get(
            ThresholdsNamesEnum.LAYERING_NUMBER_OF_PRICE_LEVELS
        )

        self._th_layering_time_window: int = self.context.thresholds.dict().get(
            ThresholdsNamesEnum.LAYERING_TIME_WINDOW
        )

        self.START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        self._market_data_client = self.queries.market_data_client

        self.cross_product = CrossProductActivityDisplay(
            es_client=self.queries.sdp_repository,
            market_data_client=self._market_data_client,
        )

    def _apply_strategy(self):
        """

        :return:
        """
        self._logger.debug(f"Start getting data to analyse for {self.strategy_name}")
        for data in self.queries.get_cases_to_analyse(strategy_name=self.strategy_name):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=data):
                market_abuse_audit_object.records_analysed += len(data)
                self._run_algo(data)

    def _run_algo(self, data_to_analyse: pd.DataFrame) -> NoReturn:
        """

        :param data_to_analyse:
        :return:
        """
        self.START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        self._logger.debug(f"{self.strategy_name} is starting to run.")

        groupings_no = len(
            data_to_analyse.groupby(by=[NewColumns.INSTRUMENT_CODE, self._th_evaluation_type])
        )
        groupings_dropped = 0
        for i, group in data_to_analyse.groupby(
            by=[NewColumns.INSTRUMENT_CODE, self._th_evaluation_type]
        ):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"Grouping data by instrument {i[0]} and {self.context.thresholds.dict().get(ThresholdsNamesEnum.EVALUATION_TYPE)} {i[1]}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=len(group),
                        step_name="Creation of groupings per 'Evaluation Type' Threshold",
                        step_type=StepType.FILTERS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

                alerts_data: List[pd.DataFrame] = self._layering_level2_algorithm(data=group)
                if len(alerts_data) == 0:
                    groupings_dropped += 1
                    end, method_id = self.get_start_time_and_unique_id()
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.START_TIME,
                            end=end,
                            number_of_dropped_orders=len(group),
                            number_of_input_orders=len(group),
                            number_of_resulting_orders=0,
                            step_name="Creating Alerts",
                            step_type=StepType.ALERT_CREATION,
                            drop_reason=DropReason.RECORDS_DROPPED_CREATE_ALERTS,
                            groupings=groupings_no,
                            groupings_dropped=groupings_dropped,
                        )
                    )
                    self._logger.debug(
                        f"No alerts were generated for {self.strategy_name} in this execution. "
                        f" Terminating this batch run."
                    )
                else:
                    for alert in alerts_data:
                        self._logger.debug("Start creating alert.")
                        self._create_alert(alert_data=alert)

                    end, method_id = self.get_start_time_and_unique_id()

                    number_of_records = [len(alert) for alert in alerts_data]

                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.START_TIME,
                            end=end,
                            number_of_dropped_orders=0,
                            number_of_input_orders=sum(number_of_records),
                            number_of_resulting_orders=sum(number_of_records),
                            step_name="Creating Alerts",
                            step_type=StepType.ALERT_CREATION,
                            groupings=groupings_no,
                            groupings_dropped=groupings_dropped,
                        )
                    )

                    self.START_TIME = end

    # flake8: noqa: C901
    def _layering_level2_algorithm(self, data: pd.DataFrame) -> List[pd.DataFrame]:
        """Execute Layering level 2 algorithm.

        :param data: pd.Dataframe, data after grouping by INSTRUMENT_CODE & EVALUATION_TYPE
        :return: List[pd.DataFrame], list of dataframes with valid data
        """
        results: List[pd.DataFrame] = []

        sorted_data: pd.DataFrame = sort_by_date(data=data, date_column=OrderField.TS_ORD_SUBMITTED)

        self._logger.debug("Creating time window groups according with step 4.")
        time_window_groups: List[pd.DataFrame] = create_time_window_groups(
            data=sorted_data,
            time_column=OrderField.TS_ORD_SUBMITTED,
            time_threshold=self._th_layering_time_window,
            time_delta_column=AlgoColumnsEnum.TIMEDELTA,
        )

        end, method_id = self.get_start_time_and_unique_id()
        if (not time_window_groups) or (len(time_window_groups) < 1):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"No groups of records were using a {ThresholdsNamesEnum.LAYERING_TIME_WINDOW} "
                    f"threshold of {self._th_layering_time_window} seconds",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Creation of groupings per 'Layering Time Window' Threshold",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                )
            )
            self.START_TIME = end
            self._logger.debug(
                "Algorithm will terminate, since weren't find any time groups to evaluate."
            )
            return results

        resulting_orders = []
        for time_group in time_window_groups:
            resulting_orders.extend(time_group.get(OrderField.META_KEY, pd.Series()).tolist())

        if len(resulting_orders) < len(data):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(
                        set(data.get(OrderField.META_KEY, pd.Series()).tolist())
                        - set(resulting_orders)
                    ),
                    reason=f"The orders were not included in the time window groups for a "
                    f"{ThresholdsNamesEnum.LAYERING_TIME_WINDOW} of {self._th_layering_time_window} seconds.",
                )
            )
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.START_TIME,
                end=end,
                number_of_dropped_orders=len(data) - len(resulting_orders),
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(resulting_orders),
                step_name="Creation of groupings per 'Layering Time Window' Threshold",
                step_type=StepType.FILTERS,
                drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
            )
        )
        self.START_TIME = end

        groupings_no = len(time_window_groups)
        groupings_dropped = 0
        for time_group in time_window_groups:
            end, method_id = self.get_start_time_and_unique_id()
            if not check_number_of_orders(data=time_group):
                order_key: str = time_group.loc[:, OrderField.META_KEY][0]
                self._logger.debug(f"This time group only has one order ID {order_key}")

                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"This time group only has one order ID {order_key}",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name="Check if the group has data from more than one order id",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                continue

            orders_counts: Dict = self._get_counts(data=time_group)

            if orders_counts.LIMIT_ORDERS == 0:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason="Dropping of alert’s that contain only “market” orders "
                        "(i.e., that would not be eligible for being put on at a specific level of "
                        "the order book).",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name="Count number of distinct orders with Limit order type & buy/sell",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.debug(
                    "Dropping of alert’s that contain solely “market” orders "
                    "(i.e., that would not be eligible for being put on at a specific level of "
                    "the order book)."
                )
                continue

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(time_group),
                    step_name="Count number of distinct orders with Limit order type & buy/sell",
                    step_type=StepType.FILTERS,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self.START_TIME = end

            filter_limit_data: pd.DataFrame = self._filter_orders_without_limit_price(
                data=time_group
            )

            dropped = list(
                set(time_group.get(OrderField.META_KEY, pd.Series()).tolist())
                - set(filter_limit_data.get(OrderField.META_KEY, pd.Series()).tolist())
            )
            end, method_id = self.get_start_time_and_unique_id()
            if len(dropped) > 0:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=dropped,
                        reason="Filter orders without limit price.",
                    )
                )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(time_group) - len(filter_limit_data),
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(filter_limit_data),
                    step_name="Filtering by Limit Orders",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self.START_TIME = end

            if filter_limit_data.empty:
                self._logger.debug(
                    "After filtering by Limit type & Limit price we don't have data to work with."
                )
                continue

            count_of_orders = len(filter_limit_data.loc[:, OrderField.META_KEY].unique().tolist())

            end, method_id = self.get_start_time_and_unique_id()

            if count_of_orders == 0 or count_of_orders < self._th_layering_number_of_price_levels:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason="Drop of groupings where the number of orders is 0 or less than the desired number "
                        "of price levels for detection.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group) - len(filter_limit_data),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=len(filter_limit_data),
                        step_name="Check that there are Orders in Grouping and Number of Orders is >= the "
                        "'Layering Number of Price Levels' Threshold",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.debug(
                    "Drop of groupings where the number of orders is 0"
                    "and to drop groupings where the number of orders is less than the desired number "
                    "of price levels for detection."
                )
                continue

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(time_group) - len(filter_limit_data),
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(filter_limit_data),
                    step_name="Check that there are Orders in Grouping and Number of Orders is >= the "
                    "'Layering Number of Price Levels' Threshold",
                    step_type=StepType.FILTERS,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self.START_TIME = end

            rics = data[AlgoColumnsEnum.RIC].dropna().unique().tolist()

            end, method_id = self.get_start_time_and_unique_id()

            if not rics:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=filter_limit_data.get(
                            OrderField.META_KEY, pd.Series()
                        ).tolist(),
                        reason="No RICs were found on the group.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(filter_limit_data),
                        number_of_input_orders=len(filter_limit_data),
                        number_of_resulting_orders=0,
                        step_name="Extracting RICs",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.warning("Didn't find any RICs for this group")
                continue

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(filter_limit_data),
                    number_of_resulting_orders=len(filter_limit_data),
                    step_name="Extracting RICs",
                    step_type=StepType.FILTERS,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self.START_TIME = end

            start_date: pd.Timestamp = pd.Timestamp(
                time_group.loc[:, OrderField.TS_ORD_SUBMITTED].min()
            )

            end_date: pd.Timestamp = pd.Timestamp(
                time_group.loc[:, OrderField.TS_ORD_SUBMITTED].max()
            )

            timestamps = time_group.loc[:, OrderField.TS_ORD_SUBMITTED]

            # to search for OBD data
            # from the start of the day until the TS_ORD_SUBMITTED of the newest order
            # to assure that we have the nearest timestamp of the order
            dates = {"start": start_date, "end": end_date, "timestamps": timestamps}

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(filter_limit_data),
                    number_of_resulting_orders=len(filter_limit_data),
                    step_name="Get start/end dates to fetch market data",
                    step_type=StepType.FILTERS,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )

            for ric in rics:
                (
                    data_with_level_of_order,
                    market_data,
                    groupings_dropped,
                ) = self.market_data_retrieval_step_9(
                    ric=ric,
                    dates=dates,
                    tenant_data=filter_limit_data,
                    groupings_dropped=groupings_dropped,
                    filter_limit_data=filter_limit_data,
                    groupings_no=groupings_no,
                )

                # The logging for this case happens in market_data_retrieval_step_9 method
                if data_with_level_of_order.empty:
                    continue

                self.START_TIME = end

                threshold_counts: Dict = self._threshold_calculation(data=data_with_level_of_order)

                end, method_id = self.get_start_time_and_unique_id()

                if not self._validate_counts(
                    threshold_counts=threshold_counts, order_counts=orders_counts
                ):
                    groupings_dropped += 1
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=data_with_level_of_order.get(
                                OrderField.META_KEY, pd.Series()
                            ).tolist(),
                            reason="This time group did not have multiple orders at multiple levels.",  # TODO: improve
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.START_TIME,
                            end=end,
                            number_of_dropped_orders=len(data_with_level_of_order),
                            number_of_input_orders=len(data_with_level_of_order),
                            number_of_resulting_orders=0,
                            step_name="Application 'Layering number of price levels' Threshold",
                            step_type=StepType.FILTERS,
                            drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                            groupings=groupings_no,
                            groupings_dropped=groupings_dropped,
                        )
                    )
                    self.START_TIME = end
                    self._logger.debug(
                        "This time group didn't pass this threshold/count validation."
                    )
                    continue

                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(data_with_level_of_order),
                        number_of_resulting_orders=len(data_with_level_of_order),
                        step_name="Application 'Layering number of price levels' Threshold",
                        step_type=StepType.FILTERS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

                valid_alert_data = self.market_data_retrieval_step_12_and_13(
                    data_with_level_of_order=data_with_level_of_order,
                    market_data=market_data,
                    level_of_order_book_col=AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK,
                    volume_level_col=AlertColumnsEnum.VOLUME_LEVEL,
                    percentage_level_col=AlertColumnsEnum.PERCENTAGE_LEVEL,
                )

                dropped = set(data_with_level_of_order.get(OrderField.META_KEY, pd.Series())) - set(
                    valid_alert_data.get(OrderField.META_KEY, pd.Series())
                )

                end, method_id = self.get_start_time_and_unique_id()

                if dropped:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=list(dropped),
                            reason="Data dropped after application of the Layering Order % of Level threshold.",
                        )
                    )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=len(data_with_level_of_order),
                        number_of_resulting_orders=len(valid_alert_data),
                        step_name="Application of 'Layering Order % of Level'",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

                if valid_alert_data.empty:
                    self._logger.debug("No data after validating market data.")
                    continue

                valid_threshold_counts: Dict = self._threshold_calculation(data=valid_alert_data)
                valid_orders_counts: Dict = self._get_counts(data=valid_alert_data)

                valid_alert_data: pd.DataFrame = self.add_counts(
                    alert_data=valid_alert_data,
                    threshold_counts=valid_threshold_counts,
                    orders_counts=valid_orders_counts,
                )
                end, method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(valid_alert_data),
                        number_of_resulting_orders=len(valid_alert_data),
                        step_name="Calculation and Validation of alert fields",
                        step_type=StepType.FILTERS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

                results.append(valid_alert_data)

        self._logger.debug("Core logic is completed. Next step: creation of alerts.")
        return results

    def _filter_orders_without_limit_price(
        self,
        data: pd.DataFrame,
    ) -> pd.DataFrame:
        """Filter orders that have the limit price when the order type is
        limit.

        :param data: pd.Dataframe, data to check
        :return: pd.DataFrame, filtered data
        """
        if OrderField.EXC_DTL_LIMIT_PRICE not in data.columns:
            self._logger.debug(
                "Return only orders that are not Limit, because the limit price column is not available"
            )
            return pd.DataFrame()

        mask_limit_orders: pd.Series = (
            data[OrderField.EXC_DTL_ORD_TYPE].str.lower() == OrderType.LIMIT.lower()
        ) & ~(data.loc[:, OrderField.EXC_DTL_LIMIT_PRICE].isnull())

        mask_market_orders: pd.Series = (
            data[OrderField.EXC_DTL_ORD_TYPE].str.lower() == OrderType.MARKET.lower()
        )

        join_mask: pd.Series = mask_limit_orders | mask_market_orders

        return data.loc[join_mask]

    def _validate_counts(self, threshold_counts: Dict, order_counts: Dict):
        """For layering, if there are not multiple orders at multiple levels,
        the grouping is dropped. stabilisation looks for activity on both sides
        of the order book for a given grouping.

        :param threshold_counts: Dict, data to check
        :return:
        """

        if self._th_behaviour_type == BehaviourTypeEnum.LAYERING:
            if (
                order_counts.BUY_ORDERS >= 2
                and threshold_counts.COUNT_LEVEL_ORDER_BOOK_BUYS
                >= self._th_layering_number_of_price_levels
            ) or (
                order_counts.SELL_ORDERS >= 2
                and threshold_counts.COUNT_LEVEL_ORDER_BOOK_SELLS
                >= self._th_layering_number_of_price_levels
            ):
                return True

        elif self._th_behaviour_type == BehaviourTypeEnum.BOOK_IMBALANCE:
            if (
                order_counts.BUY_ORDERS >= 2
                and threshold_counts.COUNT_LEVEL_ORDER_BOOK_BUYS
                >= self._th_layering_number_of_price_levels
                and order_counts.SELL_ORDERS >= 2
                and threshold_counts.COUNT_LEVEL_ORDER_BOOK_SELLS
                >= self._th_layering_number_of_price_levels
            ):
                return True

        return False

    def _create_alert(self, alert_data: pd.DataFrame) -> NoReturn:
        """Create alert to be sent to the user.

        :param alert_data: pd.Dataframe, data with valid alert
        """
        alert: dict = alert_data.to_dict(orient="list")
        earliest_order = alert_data.iloc[0]
        latest_order = alert_data.iloc[-1]

        alert[AlertColumnsEnum.INSTRUMENT_NAME] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.INST_FULL_NAME
        )
        alert[AlertColumnsEnum.ISIN] = get_instrument_id_code(data=alert_data)
        alert[AlertColumnsEnum.VENUE_LIST] = get_venue_name(data=alert_data)
        alert[AlertColumnsEnum.EARLIEST_TIMESTAMP] = (
            earliest_order.loc[OrderField.TS_ORD_SUBMITTED]
            if OrderField.TS_ORD_SUBMITTED in alert_data.columns
            else pd.NA
        )
        alert[AlertColumnsEnum.NUMBER_BUY_ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.NUMBER_BUY_ORDERS
        )
        alert[AlertColumnsEnum.BUY_ORDERS_QUANTITY] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.BUY_ORDERS_QUANTITY
        )
        alert[AlertColumnsEnum.BID_PRICE_LEVELS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.BID_PRICE_LEVELS
        )

        alert[AlertColumnsEnum.PERCENTAGE_BID_LEVELS]: List = (
            self._get_percentage_levels(data=alert_data, order_type=BuySell.BUY).unique().tolist()
        )

        alert[AlertColumnsEnum.NUMBER_SELL_ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.NUMBER_SELL_ORDERS
        )

        alert[AlertColumnsEnum.SELL_ORDERS_QUANTITY] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.SELL_ORDERS_QUANTITY
        )

        alert[AlertColumnsEnum.ASK_PRICE_LEVELS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.ASK_PRICE_LEVELS
        )

        alert[AlertColumnsEnum.PERCENTAGE_ASK_LEVELS]: List = (
            self._get_percentage_levels(data=alert_data, order_type=BuySell.SELL).unique().tolist()
        )

        alert[AlertColumnsEnum.PERCENTAGE_ORDERS_BALANCE] = (
            self._calculate_percentage_balance(data=alert_data).unique().tolist()
        )

        alert[AlertColumnsEnum.INVOLVED_COUNTERPARTIES] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.COUNTERPARTY_ID
        )

        alert[AlertColumnsEnum.INVOLVED_CLIENTS] = get_client_name(data=alert_data)

        alert[AlertColumnsEnum.INVOLVED_DESKS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRD_ALGO_FIRM_DESKS_NAME
        )

        alert[AlertColumnsEnum.INVOLVED_PORTFOLIO_MANAGERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRD_ALGO_FILE_IDENTIFIER
        )

        alert[AlertColumnsEnum.INVOLVED_TRADERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRADER_FILE_IDENTIFIER
        )

        alert[AlertColumnsEnum.ORDER_ID_LIST] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.ORD_IDENT_ID_CODE
        )

        alert[AlertColumnsEnum.ORDER_STATUS_LIST]: List = get_order_status_for_ids(
            data=alert_data,
            order_ids=alert_data.loc[:, OrderField.META_KEY].unique().tolist(),
        )

        alert[AlertColumnsEnum.LIMIT_PRICE_LIST] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.EXC_DTL_LIMIT_PRICE
        )

        alert[AlertColumnsEnum.PRICE_LEVEL] = (
            alert_data.loc[:, AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK].tolist()
            if AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK in alert_data.columns
            else 1
        )

        alert[AlertColumnsEnum.PRICE_LIST] = (
            alert_data.loc[:, OrderField.PC_FD_PRICE].tolist()
            if OrderField.PC_FD_PRICE in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.TRADED_QUANTITY_LIST] = (
            alert_data.loc[:, OrderField.PC_FD_TRD_QTY].tolist()
            if OrderField.PC_FD_TRD_QTY in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.TRANSACTION_NATIVE_VOLUME_LIST] = (
            alert_data.loc[:, OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE].tolist()
            if OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.META_KEY
        )

        time_window_threshold_timdelta: pd.Timedelta = pd.to_timedelta(
            self._th_layering_time_window, unit="s"
        )

        start_time_window = earliest_order.loc[OrderField.TS_ORD_SUBMITTED]

        end_time_window = (
            latest_order.loc[OrderField.TS_ORD_SUBMITTED] + time_window_threshold_timdelta
        )

        alert[AlertColumnsEnum.ORDERS_STATE_KEYS] = self.queries.get_newo_child(
            parent_ids=alert_data.loc[:, OrderField.META_ID].unique().tolist(),
            required_fields=[OrderField.META_KEY],
            start=start_time_window,
            end=end_time_window,
        )

        alert[AlgoColumnsEnum.RIC.lower()] = None
        if AlgoColumnsEnum.RIC in alert_data.columns:
            alert[AlgoColumnsEnum.RIC.lower()] = get_unique_value_for_alert(
                data=alert_data, column_name=AlgoColumnsEnum.RIC
            )

        result: dict = dict(
            (key, alert[key]) for key in AlertColumnsEnum.get_cols_to_alert() if key in alert
        )

        result[ScenarioFields.RELATED_ACTIVITY_ALERTED] = False
        result[ScenarioFields.RELATED_ACTIVITY_DETECTED] = False

        scenario = LayeringV2AbstractAlert(result=result, context=self.context)

        # Time range definition for scenario enrichment
        lower_time_range_limit = convert_timestamp_to_epoch(
            earliest_order.loc[OrderField.TS_ORD_SUBMITTED] - time_window_threshold_timdelta
        )
        upper_time_range_limit = convert_timestamp_to_epoch(
            latest_order.loc[OrderField.TS_ORD_SUBMITTED] + time_window_threshold_timdelta
        )

        # scenario enrichment
        self.cross_product.set_scenario(scenario=scenario)

        related_records: dict[str, list[dict[str, str]]] = self.cross_product.run_checks_enrichment(
            start=lower_time_range_limit,
            end=upper_time_range_limit,
            filters=self.context.filters,
        )

        related_activity: pd.DataFrame = self.cross_product.related_activity(
            related_records_dict=related_records
        )

        # FALLBACK to add the related_records even if they do not have relative_activity
        if related_activity.empty and len(related_records) > 0:
            self._logger.debug(
                "Found related records but no related activity. Adding records to scenario"
            )
            # since here we have a dict, we need to transform it to DF
            related_records_df = create_df_from_result(related_records)
            # add the related records to the scenario
            self.cross_product.add_related_records_to_scenario(
                all_related_orders_base=related_records_df
            )
            # change RELATED_ACTIVITY_DETECTED to True because we have related records
            self.cross_product.scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][
                ScenarioFields.TOP_LEVEL
            ][ScenarioFields.RELATED_ACTIVITY_DETECTED] = True

            enriched_scenario = self.cross_product.scenario

        else:
            enriched_scenario = self.cross_product.enrich_scenario(
                related_activity=related_activity
            )

        scenario._scenario = enriched_scenario

        self.scenarios.append(scenario)

    def _calculate_percentage_balance(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate the % difference between Buy Orders, and Sell Orders
        :return: pd.Series with balance calculated
        """
        data.loc[:, AlertColumnsEnum.PERCENTAGE_ORDERS_BALANCE] = data.apply(
            lambda row: calculate_percentage(
                first_variable=row[AlertColumnsEnum.BUY_ORDERS_QUANTITY],
                second_variable=row[AlertColumnsEnum.SELL_ORDERS_QUANTITY],
                number_of_orders=self._get_number_of_orders_dict(row=row),
                absolute_value=True,
            ),
            axis=1,
        )

        return data.loc[:, AlertColumnsEnum.PERCENTAGE_ORDERS_BALANCE]

    def market_data_retrieval_step_9(
        self,
        ric: str,
        dates: dict,
        tenant_data: pd.DataFrame,
        groupings_dropped: int,
        groupings_no: int,
        filter_limit_data: pd.DataFrame,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, int]:
        raise NotImplementedError

    def market_data_retrieval_step_12_and_13(
        self,
        data_with_level_of_order: pd.DataFrame,
        market_data: pd.DataFrame,
        level_of_order_book_col: str,
        volume_level_col: str,
        percentage_level_col: str,
    ) -> pd.DataFrame:
        raise NotImplementedError

    @staticmethod
    def step_13_apply_percentage_level_threshold(
        tenant_data_valid: pd.DataFrame, percentage_threshold: float
    ):
        """Apply the percentage level threshold.

        :param tenant_data_valid:
        :param percentage_threshold:
        :return:
        """
        if any(tenant_data_valid.loc[:, AlertColumnsEnum.PERCENTAGE_LEVEL] > percentage_threshold):
            return tenant_data_valid

        return pd.DataFrame()

    @staticmethod
    def _check_number_of_orders(data: pd.DataFrame) -> bool:
        """Check if the group has data from more than one order id.

        :param data: pd.Dataframe, data to check
        :return: bool: true if there are more than one order id
        """
        groups = data.groupby(by=OrderField.META_KEY)
        return groups.ngroups > 1

    @staticmethod
    def _get_counts(data: pd.DataFrame) -> Dict:
        """Count number of distinct orders with Limit order type & buy/sell.

        :param data: pd.Dataframe with data to check
        :return: Dict with counts
        """
        count_of_limit_orders: int = (
            len(
                data[
                    data.loc[:, OrderField.EXC_DTL_ORD_TYPE].str.lower() == OrderType.LIMIT.lower()
                ]
                .loc[:, OrderField.META_KEY]
                .unique()
            )
            if OrderField.EXC_DTL_ORD_TYPE in data.columns
            else 0
        )

        count_of_buys: int = (
            len(
                data[
                    data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND].str.lower() == BuySell.BUY.lower()
                ]
                .loc[:, OrderField.META_KEY]
                .unique()
            )
            if OrderField.EXC_DTL_BUY_SELL_IND in data.columns
            else 0
        )

        count_of_sells: int = (
            len(
                data[
                    data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND].str.lower() == BuySell.SELL.lower()
                ]
                .loc[:, OrderField.META_KEY]
                .unique()
            )
            if OrderField.EXC_DTL_BUY_SELL_IND in data.columns
            else 0
        )

        counts: Dict = Dict(
            LIMIT_ORDERS=count_of_limit_orders,
            BUY_ORDERS=count_of_buys,
            SELL_ORDERS=count_of_sells,
        )

        return counts

    @staticmethod
    def _threshold_calculation(data: pd.DataFrame) -> Dict:
        raise NotImplementedError

    @staticmethod
    def _get_percentage_levels(data: pd.DataFrame, order_type: str) -> pd.Series:
        """Get the percentage level depending if the order is a buy or a sell.

        :param data: pd.Dataframe, already a valid alert
        :param order_type: str, OrderField.EXC_DTL_BUY_SELL_IND
        :return: pd.Series, with the values of percentage [AlertColumnsEnum.PERCENTAGE_LEVEL]
        """

        return data.loc[data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == order_type][
            AlertColumnsEnum.PERCENTAGE_LEVEL
        ]

    @staticmethod
    def _get_number_of_orders_dict(row: pd.Series) -> dict:
        """Get the number of orders by each type buy/sell orders.

        :param row: pd.Series with order data
        :return dict , with number of orders
        """
        number_of_orders_dict: dict = Dict(
            SELLS=row[AlertColumnsEnum.NUMBER_SELL_ORDERS],
            BUYS=row[AlertColumnsEnum.NUMBER_BUY_ORDERS],
        )
        return number_of_orders_dict

    @staticmethod
    def add_counts(alert_data: pd.DataFrame, threshold_counts: Dict, orders_counts: Dict):
        """Add alert counts.

        :param alert_data:
        :param threshold_counts:
        :param orders_counts:
        :return:
        """
        result = alert_data.copy()
        result[AlertColumnsEnum.BID_PRICE_LEVELS] = threshold_counts.COUNT_LEVEL_ORDER_BOOK_BUYS

        result[AlertColumnsEnum.BUY_ORDERS_QUANTITY] = threshold_counts.SUM_BUY_ORDER_QUANTITY

        result[AlertColumnsEnum.ASK_PRICE_LEVELS] = threshold_counts.COUNT_LEVEL_ORDER_BOOK_SELLS

        result[AlertColumnsEnum.SELL_ORDERS_QUANTITY] = threshold_counts.SUM_SELL_ORDER_QUANTY

        result[AlertColumnsEnum.NUMBER_BUY_ORDERS] = orders_counts.BUY_ORDERS
        result[AlertColumnsEnum.NUMBER_SELL_ORDERS] = orders_counts.SELL_ORDERS
        return result
