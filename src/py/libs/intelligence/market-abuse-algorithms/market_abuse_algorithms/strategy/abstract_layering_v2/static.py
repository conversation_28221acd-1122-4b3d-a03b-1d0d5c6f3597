from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from se_elastic_schema.components.mar.strategy.layering_v2.thresholds import (
    EvaluationTypeEnum,
)
from typing import List


class ThresholdsNamesEnum(str, Enum):
    BEHAVIOUR_TYPE = "behaviourType"
    EVALUATION_TYPE = "evaluationType"
    LAYERING_MAXIMUM_PRICE_LEVEL = "layeringMaximumPriceLevel"
    LAYERING_NUMBER_OF_PRICE_LEVELS = "layeringNumberOfPriceLevels"
    LAYERING_ORDER_PERCENTAGE = "layeringOrderPercentage"
    LAYERING_ORDER_PERCENTAGE_L1 = "layeringOrderPercentageL1"
    LAYERING_TIME_WINDOW = "layeringTimeWindow"


class AlertColumnsEnum:
    ASK_PRICE_LEVELS = "askPriceLevels"
    BID_PRICE_LEVELS = "bidPriceLevels"
    BUY_ORDERS_QUANTITY = "buyOrdersQuantity"
    EARLIEST_TIMESTAMP = "earliestTimestamp"
    INSTRUMENT_NAME = "instrumentName"
    INVOLVED_CLIENTS = "partiesClientList"
    INVOLVED_COUNTERPARTIES = "partiesCounterpartyList"
    INVOLVED_DESKS = "partiesDeskList"
    INVOLVED_PORTFOLIO_MANAGERS = "partiesInvestmentDecisionMakerList"
    INVOLVED_TRADERS = "partiesTraderList"
    ISIN = "isin"
    LIMIT_PRICE_LIST = "limitPriceList"
    NUMBER_BUY_ORDERS = "numberBuyOrders"
    NUMBER_SELL_ORDERS = "numberSellOrders"
    ORDERS = "orders"
    ORDER_ID_LIST = "orderIdList"
    ORDERS_STATE_KEYS = "orderStateKeys"
    ORDER_STATUS_LIST = "orderStatusList"
    PERCENTAGE_ASK_LEVELS = "percentageAskLevels"
    PERCENTAGE_BID_LEVELS = "percentageBidLevels"
    PERCENTAGE_LEVEL = "percentageLevel"
    PERCENTAGE_ORDERS_BALANCE = "percentageOrdersBalance"
    PRICE_LEVEL = "priceLevel"
    PRICE_LIST = "priceList"
    SELL_ORDERS_QUANTITY = "sellOrdersQuantity"
    VENUE_LIST = "venueList"
    VOLUME_LEVEL = "volumeLevel"
    TRADED_QUANTITY_LIST = "tradedQuantityList"
    TRANSACTION_NATIVE_VOLUME_LIST = "transactionVolumeList"

    @classmethod
    def get_cols_to_alert(cls) -> List[str]:
        return [
            cls.ASK_PRICE_LEVELS,
            cls.BID_PRICE_LEVELS,
            cls.BUY_ORDERS_QUANTITY,
            cls.EARLIEST_TIMESTAMP,
            cls.INSTRUMENT_NAME,
            cls.INVOLVED_CLIENTS,
            cls.INVOLVED_COUNTERPARTIES,
            cls.INVOLVED_DESKS,
            cls.INVOLVED_PORTFOLIO_MANAGERS,
            cls.INVOLVED_TRADERS,
            cls.ISIN,
            cls.LIMIT_PRICE_LIST,
            cls.NUMBER_BUY_ORDERS,
            cls.NUMBER_SELL_ORDERS,
            cls.ORDERS,
            cls.ORDER_ID_LIST,
            cls.ORDERS_STATE_KEYS,
            cls.ORDER_STATUS_LIST,
            cls.PERCENTAGE_ASK_LEVELS,
            cls.PERCENTAGE_BID_LEVELS,
            cls.PERCENTAGE_LEVEL,
            cls.PERCENTAGE_ORDERS_BALANCE,
            cls.PRICE_LEVEL,
            cls.PRICE_LIST,
            cls.SELL_ORDERS_QUANTITY,
            cls.VENUE_LIST,
            cls.VOLUME_LEVEL,
            cls.TRADED_QUANTITY_LIST,
            cls.TRANSACTION_NATIVE_VOLUME_LIST,
        ]


class AlgoColumnsEnum:
    TIMEDELTA = "timeDelta"
    LEVEL_OF_ORDER_BOOK = "levelOfOrderBook"
    VENUE = "venue"
    RIC = "RIC"


EVALUATION_TYPE_GROUPING_MAP = {
    EvaluationTypeEnum.CLIENT: OrderField.CLIENT_FILE_IDENTIFIER,
    EvaluationTypeEnum.COUNTERPARTY: OrderField.COUNTERPARTY_ID,
    EvaluationTypeEnum.DESK: OrderField.TRD_ALGO_FIRM_DESKS_NAME,
    EvaluationTypeEnum.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    EvaluationTypeEnum.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    EvaluationTypeEnum.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
}
