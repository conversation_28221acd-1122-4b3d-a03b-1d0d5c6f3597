import pandas as pd
from market_abuse_algorithms.strategy.abstract_layering_v2.static import (
    AlertColumnsEnum,
)
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns


class LayeringV2AbstractAlert(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders", "executions"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "orders": self._result.pop(AlertColumnsEnum.ORDERS),
            "executions": self._result.pop(AlertColumnsEnum.ORDERS_STATE_KEYS),
        }

        alert = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={"topLevel": self._result},
        )

        scenario = pd.Series(alert)

        return scenario
