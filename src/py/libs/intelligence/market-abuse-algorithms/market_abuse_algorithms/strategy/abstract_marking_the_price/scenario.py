import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abs<PERSON><PERSON><PERSON><PERSON><PERSON>, TradeColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["order"], multiple=[])

    def _build_scenario(self) -> pd.Series:
        d = addict.Dict()
        d.thresholds = self._thresholds

        order = list(self._result.keys())[0]
        d.records.order = order
        d.additionalFields.topLevel = self._result[order]

        return pd.Series(d.to_dict())
