# ruff: noqa: E501
import datetime
import numpy as np
import os
import pandas as pd
from abc import abstractmethod
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
    get_market_client,
)
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.ref_data.trading_hours_report import (
    CoppClarkColumn,
    ExtInstrumentClassification,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    BestExAssetClassMain,
    BuySell,
    NewColumns,
    OrderField,
)
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.query import (
    MarkingThePriceQueries,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.scenario import (
    <PERSON><PERSON><PERSON>,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.static import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.utils.formulas import calculate_percentage_difference
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import Type

COPP_CLARK_S3_KEY = os.getenv("COPP_CLARK_FILE_PATH")


class MarkingThePriceStrategy(AbstractStrategy):
    def __init__(
        self,
        context: StrategyContext,
        strategy_name: str,
        queries: Type[MarkingThePriceQueries],
    ):
        super().__init__(
            context=context,
            strategy_name=strategy_name,
            thresholds_class=Thresholds,
            queries_class=queries,
        )
        self._th_marking_type = self.context.thresholds.dict().get(ThresholdsNames.MARKING_TYPE)
        self._th_lbp = self.context.thresholds.dict().get(
            ThresholdsNames.LOOK_BACK_PERIOD
        )  # in seconds
        self._th_min_notional = self.context.thresholds.dict().get(ThresholdsNames.MINIMUM_NOTIONAL)
        self._th_min_notional_ccy = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_CURRENCY
        )
        self._th_client_20_day_adv = self.context.thresholds.dict().get(
            ThresholdsNames.CLIENT_20_DAY_ADV
        )
        self._th_market_20_day_adv = self.context.thresholds.dict().get(
            ThresholdsNames.MARKET_20_DAY_ADV
        )
        self._th_price_spike = self.context.thresholds.dict().get(ThresholdsNames.PRICE_SPIKE)
        self.client: MarketDataClient = get_market_client(tenant=context.tenant)

    def _apply_strategy(self):
        start, method_id = self.get_start_time_and_unique_id()

        thr_data = self._get_thr_data()

        if thr_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="No Trading Hours Report data fetched.",
                    list_of_instruments=[],
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=0,
                    number_of_resulting_orders=0,
                    step_name="Fetch Trading Hours Report data.",
                    step_type=StepType.REFERENCE_DATA,
                    drop_reason=DropReason.RECORDS_DROPPED_TRADING_HOURS,
                )
            )
            return

        for data in self.queries.get_data_to_analyse():
            with StrategyError.handle_algo_records_error(audit=self._audit, data=data):
                market_abuse_audit_object.records_analysed += len(data)
                self._apply_strategy_mini_batch(data=data, thr_data=thr_data)

    def _process_copp_clark_frame_result(self, result: pd.DataFrame) -> pd.DataFrame:
        # convert to pd.Timestamp
        for col in [
            CoppClarkColumn.CALENDAR_END,
            CoppClarkColumn.CALENDAR_START,
            CoppClarkColumn.LAST_CONFIRMED,
            CoppClarkColumn.DST_STARTS,
            CoppClarkColumn.DST_ENDS,
        ]:
            if col in result.columns:
                result[col] = pd.to_datetime(result[col], format="mixed")

        return result

    def _get_thr_data(self) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        venues = self.queries.get_venues_to_analyse_venues()

        if not venues:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="No venues fetched.",
                    list_of_instruments=[],
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=0,
                    number_of_resulting_orders=0,
                    step_name="Get venues before fetching trading hours report data.",
                    step_type=None,
                    drop_reason=None,
                )
            )
            return pd.DataFrame()

        # get trading hours report data from copp clark file
        # thr_data = self.queries.get_trading_hours_report_data(venues)

        phase_types = self._get_phase_types()

        filter_map = {
            CoppClarkColumn.MIC_CODE: venues,
            CoppClarkColumn.PHASE_TYPE: phase_types,
        }
        columns_to_include = [
            CoppClarkColumn.CALENDAR_END,
            CoppClarkColumn.CALENDAR_START,
            CoppClarkColumn.MIC_CODE,
            CoppClarkColumn.ASSET_CLASS,
            CoppClarkColumn.CODE,
            CoppClarkColumn.PHASE_TYPE,
            CoppClarkColumn.PHASE_ENDS,
            CoppClarkColumn.PHASE_STARTS,
            CoppClarkColumn.OLSON_TIMEZONE,
        ]

        filtered_copp_clark_data = self.client.get_filtered_trade_surveillance_data(
            filename=COPP_CLARK_S3_KEY,
            filter_map=filter_map,
            columns_to_include=columns_to_include,
        )
        thr_data = self._process_copp_clark_frame_result(result=filtered_copp_clark_data)

        return thr_data

    def _apply_strategy_mini_batch(self, data: pd.DataFrame, thr_data: pd.DataFrame):
        data = self._get_thr_times(data=data, thr_data=thr_data)

        if data.empty:
            return

        # Groupby instrument
        for inst_id, inst_group in data.groupby(NewColumns.INSTRUMENT_CODE):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=inst_group):
                self._run_algo(data=inst_group)

    def _get_thr_times(self, data: pd.DataFrame, thr_data: pd.DataFrame) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        order_ids_original = list(set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        data = self._merge_tenant_and_thr_data(data=data, thr_data=thr_data)

        original_len = len(order_ids_original)

        if data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped after merging tenant data with trading hours data.",
                    list_of_order_ids=order_ids_original,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(order_ids_original),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(order_ids_original),
                    step_name="Filter data based on date.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )
            return data

        start, method_id = self.get_start_time_and_unique_id()

        date_mask = (
            data[CoppClarkColumn.CALENDAR_START].dt.date
            <= data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.date
        ) & (
            data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.date
            <= data[CoppClarkColumn.CALENDAR_END].dt.date
        ) | (
            (data[CoppClarkColumn.CALENDAR_END].isnull())
            & (
                data[CoppClarkColumn.CALENDAR_START].dt.date
                == data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.date
            )
        )

        data = data.loc[date_mask]

        dropped = list(
            set(order_ids_original) - set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        )

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped after filtering by calendar date.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Filter data based on date.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        if data.empty:
            return data

        start, method_id = self.get_start_time_and_unique_id()

        # Filter cases where TS OrderSubmitted date == calendar start
        same_date_mask = (
            data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.date
            == data[CoppClarkColumn.CALENDAR_START].dt.date
        )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        same_date_df = data.loc[same_date_mask]

        dropped = list(dropped - set(same_date_df.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Filter cases where OrderSubmitted date is equal to calendar start.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Filter data based on date.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        if not same_date_df.empty:
            not_same_date_df = data[
                ~data[OrderField.META_KEY].isin(same_date_df[OrderField.META_KEY].unique())
            ]

            data = pd.concat([same_date_df, not_same_date_df])

        start, method_id = self.get_start_time_and_unique_id()
        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = self._calc_marking_times(data=data, method_id=method_id)

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped while calculating the marking Time.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Calculating Marking Time.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )

        if data.empty:
            return data

        start, method_id = self.get_start_time_and_unique_id()
        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        # Filter only data with t-mtc start and end defined
        cols = thr_data.columns.unique().tolist()
        cols.remove(CoppClarkColumn.OLSON_TIMEZONE)

        final_data = (
            data.dropna(
                subset=[
                    DFColumns.MARKET_TIME_END_LOCAL,
                    DFColumns.MARKET_TIME_START_LOCAL,
                ]
            )
            .loc[:, ~data.columns.isin(cols)]
            .drop_duplicates()
        )

        final_data[CoppClarkColumn.PHASE_TYPE] = data[CoppClarkColumn.PHASE_TYPE]

        dropped = list(dropped - set(final_data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Remove data where both {DFColumns.MARKET_TIME_END_LOCAL} and "
                    f"{DFColumns.MARKET_TIME_START_LOCAL} fields are empty.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(final_data),
                    step_name=f"Remove Orders will Null {DFColumns.MARKET_TIME_END_LOCAL} and {DFColumns.MARKET_TIME_START_LOCAL}",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )

        return final_data

    @abstractmethod
    def _calc_marking_times(self, data: pd.DataFrame, method_id: str) -> pd.DataFrame:
        return pd.DataFrame()

    def _merge_tenant_and_thr_data(
        self, data: pd.DataFrame, thr_data: pd.DataFrame
    ) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        venue_mask = data[OrderField.TRX_DTL_ULTIMATE_VENUE].isin(
            thr_data[CoppClarkColumn.MIC_CODE].unique().tolist()
        )
        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = data.loc[venue_mask]

        if data.empty:
            return data

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Orders dropped because THR venue {CoppClarkColumn.MIC_CODE} "
                    f"not in tenant venue {OrderField.TRX_DTL_ULTIMATE_VENUE}.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Merge Tenant data and thr data.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        # Stocks and ETFs mask
        stocks_and_etfs_mask = data[OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN].isin(
            [BestExAssetClassMain.EQUITY, BestExAssetClassMain.EXCHANGE_TRADED_PRODUCTS]
        ) | data[OrderField.INST_CLASSIFICATION].str.startswith("C")

        data.loc[stocks_and_etfs_mask, CoppClarkColumn.ASSET_CLASS] = (
            ExtInstrumentClassification.STOCKS_AND_ETFS
        )

        # Structured Finance Instruments mask
        structured_finance_instruments_mask = (
            data[OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN]
            == BestExAssetClassMain.STRUCTURED_FINANCE_INSTRUMENTS
        )

        data.loc[
            structured_finance_instruments_mask,
            CoppClarkColumn.ASSET_CLASS,
        ] = ExtInstrumentClassification.STRUCTURED_FINANCE_INSTRUMENTS

        # TODO: check Debt Instrument and Debt InstrumentS
        # Debt Instruments mask
        debt_instruments_mask = (
            data[OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN]
            == BestExAssetClassMain.DEBT_INSTRUMENTS
        )

        data.loc[debt_instruments_mask, CoppClarkColumn.ASSET_CLASS] = (
            ExtInstrumentClassification.DEBT_INSTRUMENTS
        )

        if OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT in data.columns:
            thr_inst_codes = thr_data[CoppClarkColumn.CODE].unique().tolist()

            # Derivative - Options mask
            derivative_options_mask = (
                data[OrderField.INST_CLASSIFICATION].str.startswith(("O", "H"))
            ) & (data[OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT].isin(thr_inst_codes))

            data.loc[
                derivative_options_mask,
                CoppClarkColumn.ASSET_CLASS,
            ] = ExtInstrumentClassification.DERIVATIVE_OPTIONS

            # Derivative - Futures mask
            derivative_futures_mask = (data[OrderField.INST_CLASSIFICATION].str.startswith("F")) & (
                data[OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT].isin(thr_inst_codes)
            )

            data.loc[
                derivative_futures_mask,
                CoppClarkColumn.ASSET_CLASS,
            ] = ExtInstrumentClassification.DERIVATIVE_FUTURES

        inst_classification_not_null_mask = data[CoppClarkColumn.ASSET_CLASS].notnull()

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = data.loc[inst_classification_not_null_mask]

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if data.empty:
            return pd.DataFrame()

        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Drop orders which have empty values for {CoppClarkColumn.ASSET_CLASS}.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Merge Tenant data and thr data.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        # Merge tenant and thr data
        data = data.merge(
            thr_data,
            left_on=[
                OrderField.TRX_DTL_ULTIMATE_VENUE,
                CoppClarkColumn.ASSET_CLASS,
            ],
            right_on=[
                CoppClarkColumn.MIC_CODE,
                CoppClarkColumn.ASSET_CLASS,
            ],
            how="left",
        )

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Orders dropped while merging THR data with tenant data on "
                    f"{OrderField.TRX_DTL_ULTIMATE_VENUE} and {CoppClarkColumn.MIC_CODE}.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Merge Tenant data and thr data.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        # Filter Derivatives
        if OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT in data.columns:
            derivatives_mask = (
                data[CoppClarkColumn.ASSET_CLASS].isin(
                    [
                        ExtInstrumentClassification.DERIVATIVE_OPTIONS,
                        ExtInstrumentClassification.DERIVATIVE_FUTURES,
                    ]
                )
            ) & (data[OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT] != data[CoppClarkColumn.CODE])

            dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
            original_len = len(dropped)

            data = data.loc[~derivatives_mask]

            dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

            start, method_id = self.get_start_time_and_unique_id()
            if dropped:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="Orders dropped while filtering derivatives.",
                        list_of_order_ids=dropped,
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=original_len,
                        number_of_resulting_orders=len(data),
                        step_name="Merge Tenant data and thr data.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                    )
                )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        timezone_mask = data[CoppClarkColumn.OLSON_TIMEZONE].notnull()
        data = data.loc[timezone_mask]

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if data.empty:
            return pd.DataFrame()

        start, method_id = self.get_start_time_and_unique_id()

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Drop orders with invalid values for {CoppClarkColumn.OLSON_TIMEZONE}.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Merge Tenant data and thr data.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_TRADING_HOURS,
                )
            )

        # Convert timestamps.orderSubmitted to local time
        data[DFColumns.ORDER_SUBMITTED_TS_LOCAL] = data[
            [OrderField.TS_ORD_SUBMITTED, CoppClarkColumn.OLSON_TIMEZONE]
        ].apply(
            lambda x: x[OrderField.TS_ORD_SUBMITTED]
            .tz_localize("UTC")
            .tz_convert(x[CoppClarkColumn.OLSON_TIMEZONE])
            .tz_localize(None),
            axis=1,
        )

        return data

    def _run_algo(self, data: pd.DataFrame):
        result = self._algo(data)

        if result.empty:
            return

        self._create_scenarios(result)

    def _algo(self, data: pd.DataFrame) -> pd.DataFrame:
        data = self._filter_data_by_thr_time_period(data=data)
        if data.empty:
            self._logger.debug("Order time not within the time window used by the model")
            return data

        start, method_id = self.get_start_time_and_unique_id()

        if all(
            map(
                lambda x: x is None,
                [
                    self._th_client_20_day_adv,
                    self._th_market_20_day_adv,
                    self._th_price_spike,
                ],
            )
        ):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Thresholds client20DayAdv, market20DayAdv, and priceSpike need to be populated.",
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Checking Thresholds: client20DayAdv, market20DayAdv, priceSpike.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )
            self._logger.debug(
                "Thresholds: client20DayAdv, market20DayAdv, priceSpike are all None"
            )

            return data

        mask = True
        start, method_id = self.get_start_time_and_unique_id()

        # Client ADV
        if self._th_client_20_day_adv is not None and self._th_client_20_day_adv > 0:
            data = self._add_client_adv(data)
            inst_adv_mask = data[DFColumns.ORDER_QTY_INST_ADV] > self._th_client_20_day_adv

            mask = mask & inst_adv_mask

        # Market ADV
        if self._th_market_20_day_adv is not None and self._th_market_20_day_adv > 0:
            data = self._add_market_adv(data)
            market_adv_mask = data[DFColumns.ORDER_QTY_MKT_ADV] > self._th_market_20_day_adv
            mask = mask & market_adv_mask

        # Price spike
        if self._th_price_spike is not None and self._th_price_spike > 0:
            data = self._add_price_spike(data=data)

            price_spike_mask: pd.Series = self.check_values(
                data, column=DFColumns.MKT_PRICE_PCT_DIFFERENCE, threshold=self._th_price_spike
            )

            direction_mask: pd.Series = self.verify_direction_price_spike(df=data)

            mask = mask & price_spike_mask & direction_mask

        if isinstance(mask, bool) and mask:
            return data

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = data.loc[mask]

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Orders dropped after calculating client20DayAdv ({self._th_client_20_day_adv}), "
                    f"market20DayAdv ({self._th_market_20_day_adv}), and priceSpike "
                    f"({self._th_price_spike}) thresholds.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Thresholds Calculation: client20DayAdv, market20DayAdv, priceSpike",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_PROCESS_DATA,
                )
            )

        return data

    def _filter_data_by_thr_time_period(self, data: pd.DataFrame) -> pd.DataFrame:
        # t-mtc filter
        start, method_id = self.get_start_time_and_unique_id()

        t_mtc_mask = (
            data[DFColumns.MARKET_TIME_START_LOCAL]
            <= data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.time
        ) & (
            data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].dt.time
            <= data[DFColumns.MARKET_TIME_END_LOCAL]
        )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = data.loc[t_mtc_mask]

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Filter data where {DFColumns.ORDER_SUBMITTED_TS_LOCAL} is between {DFColumns.MARKET_TIME_START_LOCAL} and {DFColumns.MARKET_TIME_END_LOCAL}.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Filter data by THR time period.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_TRADING_HOURS,
                )
            )

        return data

    def _add_client_adv(self, data: pd.DataFrame) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        data[DFColumns.DATE] = pd.to_datetime(data[DFColumns.DATE], format="mixed")

        data.loc[:, DFColumns.ORDER_QTY_INST_ADV] = np.nan

        # create a copy of the original data
        data_original = data.copy()

        data = data.dropna(subset=[OrderField.CLIENT_FILE_IDENTIFIER])

        # if we drop every row we will want to return the original data
        # the algo uses masks to filter data, so we need the original data
        if data.empty:
            data_original = data_original.reset_index(drop=True)
            self._logger.info(
                f"There was no record with the field {OrderField.CLIENT_FILE_IDENTIFIER}. "
                f"Skipping client adv"
            )
            return data_original

        # delete the variable with the copied data to free memory
        del data_original

        inst_codes = data[NewColumns.INSTRUMENT_CODE].unique().tolist()
        client_ids = data[OrderField.CLIENT_FILE_IDENTIFIER].unique().tolist()

        start_date = data[OrderField.TS_ORD_SUBMITTED].min()
        end_date = data[OrderField.TS_ORD_SUBMITTED].max()

        client_adv = []
        no_dropped = 0
        for client in client_ids:
            adv = self.queries.get_client_adv(
                instrument_codes=inst_codes,
                client_ids=[client],
                start_date=start_date,
                end_date=end_date,
            )

            if adv.empty:
                orders_dropped = (
                    data[data[OrderField.CLIENT_FILE_IDENTIFIER] == client][OrderField.META_KEY]
                    .unique()
                    .tolist()
                )
                no_dropped += len(orders_dropped)
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"There isn't any Client ADV between {start_date} and {end_date} for client "
                        f"{client} and instrument {inst_codes}.",
                        list_of_instruments=inst_codes,
                        list_of_order_ids=orders_dropped,
                    )
                )
                self._logger.info(
                    f"There isn't any Client ADV between {start_date} and {end_date} for client {client} "
                    f"and instrument {inst_codes}."
                )

            client_adv.append(adv)

        client_adv = pd.concat(client_adv)

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=start,
                end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                number_of_dropped_orders=no_dropped,
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(data) - no_dropped,
                step_name="Add Client ADV.",
                step_type=StepType.THRESHOLD_CALCULATION,
                drop_reason=DropReason.RECORDS_DROPPED_CLIENT_ADV,
            )
        )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        start, method_id = self.get_start_time_and_unique_id()
        if client_adv.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="No Client ADV after merging the data from all clients.",
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=no_dropped,
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=len(data) - no_dropped,
                    step_name="Merging Client ADV from all clients.",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_CLIENT_ADV,
                )
            )
            self._logger.info("No Client ADV after merging the data from all clients")
            return data

        data = data.merge(
            client_adv,
            how="left",
            on=[
                NewColumns.INSTRUMENT_CODE,
                DFColumns.DATE,
                OrderField.CLIENT_FILE_IDENTIFIER,
            ],
        )

        data[DFColumns.ORDER_QTY_INST_ADV] = (
            data[OrderField.PC_FD_INIT_QTY] / data[DFColumns.CLIENT_ADV]
        )

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        start, method_id = self.get_start_time_and_unique_id()

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped while merging Client ADV with Orders data.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Add Client ADV.",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_CLIENT_ADV,
                )
            )

        return data

    def _add_market_adv(self, data: pd.DataFrame) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        instrument_unique_identifiers = (
            data[OrderField.INST_EXT_UNIQUE_IDENT].dropna().unique().tolist()
        )

        data.loc[:, DFColumns.ORDER_QTY_MKT_ADV] = np.nan

        if not instrument_unique_identifiers:
            return data

        market_data = self.queries.get_market_data_adv(
            instrument_unique_identifiers=instrument_unique_identifiers,
        )

        if market_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="There isn't any Market ADV to be added",
                    list_of_instruments=instrument_unique_identifiers,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=len(data),
                    step_name="Add Market ADV.",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_ADV,
                )
            )
            return data

        start, method_id = self.get_start_time_and_unique_id()
        # Merge market data into tenant activity
        date_fmt = "%Y-%m-%d"
        md_ident = "md_ident"

        data.loc[:, md_ident] = (
            pd.to_datetime(data[DFColumns.DATE], format="mixed").dt.strftime(date_fmt)
            + data[OrderField.INST_EXT_UNIQUE_IDENT]
        )

        market_data[md_ident] = (
            market_data[TradeStatsColumns.DATE].dt.strftime(date_fmt)
            + market_data[OrderField.INST_EXT_UNIQUE_IDENT]
        )

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = data.merge(market_data, how="left", on=md_ident, suffixes=(None, "_right"))

        data.loc[:, DFColumns.ORDER_QTY_MKT_ADV] = (
            data[OrderField.PC_FD_INIT_QTY] / data[DFColumns.MARKET_ADV]
        )

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped while merging Market ADV with Orders data.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Add Market ADV.",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_ADV,
                )
            )

        return data

    def _add_price_spike(self, data: pd.DataFrame) -> pd.DataFrame:
        start, method_id = self.get_start_time_and_unique_id()

        data.loc[:, DFColumns.MKT_PRICE_PCT_DIFFERENCE] = np.nan

        data.loc[:, DFColumns.LOCAL_DATE] = data[DFColumns.ORDER_SUBMITTED_TS_LOCAL].apply(
            lambda x: x.date().isoformat()
        )

        # Convert T MTC start and end to TS UTC
        data.loc[:, DFColumns.PRICE_SPIKE_DT_START_UTC] = self._get_utc_timestamps_from_local_time(
            df=data, local_time_col=DFColumns.MARKET_TIME_START_LOCAL
        )
        data.loc[:, DFColumns.PRICE_SPIKE_DT_END_UTC] = self._get_utc_timestamps_from_local_time(
            df=data, local_time_col=DFColumns.MARKET_TIME_END_LOCAL
        )

        price_spike_cols = [
            OrderField.INST_EXT_UNIQUE_IDENT,
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            DFColumns.PRICE_SPIKE_DT_START_UTC,
            DFColumns.PRICE_SPIKE_DT_END_UTC,
        ]

        df = data[price_spike_cols].drop_duplicates()

        instrument_unique_identifiers = (
            df[OrderField.INST_EXT_UNIQUE_IDENT].dropna().unique().tolist()
        )

        price_spike_data = self.queries.get_market_data_for_price_spike(data=df)

        if price_spike_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="There isn't any market data for price spike to be added",
                    list_of_instruments=instrument_unique_identifiers,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(df),
                    number_of_resulting_orders=len(df),
                    step_name="Add Price Spike.",
                    step_type=StepType.MARKET_DATA,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                )
            )
            return data

        start, method_id = self.get_start_time_and_unique_id()

        dropped = set(data.get(OrderField.META_KEY, pd.Series()).tolist())
        original_len = len(dropped)

        data = self._merge_tenant_data_and_price_spike_data(
            tenant_data=data, price_spike_data=price_spike_data
        )

        data[DFColumns.MKT_PRICE_PCT_DIFFERENCE] = calculate_percentage_difference(
            x=data[DFColumns.MARKET_PRICE_START],
            y=data[DFColumns.MARKET_PRICE_END],
            as_percentage=False,
            absolute_value=False,
        )

        dropped = list(dropped - set(data.get(OrderField.META_KEY, pd.Series()).tolist()))

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped while merging Price Spike data with Orders data.",
                    list_of_order_ids=dropped,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=original_len,
                    number_of_resulting_orders=len(data),
                    step_name="Add Price Spike.",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                )
            )

        return data

    @staticmethod
    def _get_utc_timestamps_from_local_time(df: pd.DataFrame, local_time_col: str) -> pd.Series:
        """Returns a Series with the local timestamps in UTC.

        :param df: data
        :param local_time_col: Column with local time
        :return: (pd.Series)
        """

        result = df[[DFColumns.LOCAL_DATE, local_time_col, CoppClarkColumn.OLSON_TIMEZONE]].apply(
            lambda x: pd.to_datetime(
                x[DFColumns.LOCAL_DATE] + " " + x[local_time_col].isoformat(),
                format="mixed",
            )
            .tz_localize(x[CoppClarkColumn.OLSON_TIMEZONE])
            .tz_convert("UTC")
            .tz_localize(None),
            axis=1,
        )

        return result

    @staticmethod
    def _merge_tenant_data_and_price_spike_data(
        tenant_data: pd.DataFrame, price_spike_data: pd.DataFrame
    ) -> pd.DataFrame:
        md_ident = "md_ident"
        datetime_fmt = "%Y-%m-%d %H:%M:%S"

        for df in [tenant_data, price_spike_data]:
            df.loc[:, md_ident] = (
                df[OrderField.INST_EXT_UNIQUE_IDENT]
                + df[DFColumns.PRICE_SPIKE_DT_START_UTC].dt.strftime(datetime_fmt)
                + df[DFColumns.PRICE_SPIKE_DT_END_UTC].dt.strftime(datetime_fmt)
            )

        df = tenant_data.merge(
            price_spike_data[[md_ident, DFColumns.MARKET_PRICE_START, DFColumns.MARKET_PRICE_END]],
            how="left",
            on=md_ident,
            suffixes=(None, "_y"),
        )

        return df

    def _create_scenarios(self, result: pd.DataFrame):
        # Add scenario fields
        scenario_fields = self.queries.get_additional_fields_result(
            result[OrderField.META_KEY].unique().tolist()
        )

        if not scenario_fields.empty:
            result = result.merge(scenario_fields, left_on=OrderField.META_KEY, right_index=True)

        if (NewColumns.TRADER_NAME not in result.columns) and (
            NewColumns.TRADER_NAME + "_x" in result.columns
            and NewColumns.TRADER_NAME + "_y" in result.columns
        ):
            result[NewColumns.TRADER_NAME + "_x"] = result[NewColumns.TRADER_NAME + "_x"].fillna(
                result[NewColumns.TRADER_NAME + "_y"]
            )
            result[NewColumns.TRADER_NAME] = result[NewColumns.TRADER_NAME + "_x"]

        result = result.rename(
            columns={
                NewColumns.INSTRUMENT_CODE: DFColumns.INSTRUMENT_ID,
                OrderField.TRX_DTL_ULTIMATE_VENUE: DFColumns.VENUE,
                CoppClarkColumn.OLSON_TIMEZONE: DFColumns.TIMEZONE,
                NewColumns.TRADER_NAME: DFColumns.TRADER_NAME,
                CoppClarkColumn.PHASE_TYPE: DFColumns.PHASE_TYPE,
            }
        )

        result[DFColumns.ORDER_SUBMITTED_TS_LOCAL] = result[OrderField.TS_ORD_SUBMITTED]

        result = result.loc[
            :,
            result.columns.isin([*DFColumns.get_scenario_fields(), OrderField.META_KEY]),
        ].to_dict(orient="records")

        for record in result:
            meta_key = record.pop(OrderField.META_KEY)
            scenario = Scenario(result={meta_key: record}, context=self.context)
            self.scenarios.append(scenario)

    @abstractmethod
    def _get_phase_types(self):
        return NotImplemented

    @staticmethod
    def check_values(df, column: str, threshold: float) -> pd.Series:
        """Checks if values in the specified column meet the given conditions.

        - If the value is negative, it must be below `-threshold`
        - If the value is positive, it must be above `threshold`

        Returns a boolean Series indicating whether each row meets the conditions.
        """
        return df[column].apply(lambda x: abs(x) > threshold)

    @staticmethod
    def verify_direction_price_spike(df: pd.DataFrame) -> pd.DataFrame:
        """Verifies if it is a buy and the price spike is positive and vice
        versa.

        Returns a boolean Series where False indicates a row that does
        not meet the conditions.
        """
        buy_condition = (df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY) & (
            df[DFColumns.MKT_PRICE_PCT_DIFFERENCE] >= 0
        )
        sell_condition = (df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL) & (
            df[DFColumns.MKT_PRICE_PCT_DIFFERENCE] <= 0
        )

        valid_rows = buy_condition | sell_condition

        return valid_rows
