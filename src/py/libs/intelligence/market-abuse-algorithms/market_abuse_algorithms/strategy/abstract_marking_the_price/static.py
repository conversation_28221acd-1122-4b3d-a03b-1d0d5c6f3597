from enum import Enum


class ThresholdsNames(str, Enum):
    MARKING_TYPE = "markingType"
    LOOK_BACK_PERIOD = "lookBackPeriod"
    MINIMUM_NOTIONAL = "minimumNotional"
    MINIMUM_NOTIONAL_CURRENCY = "minimumNotionalCurrency"
    CLIENT_20_DAY_ADV = "client20DayAdv"
    MARKET_20_DAY_ADV = "market20DayAdv"
    PRICE_SPIKE = "priceSpike"


class DFColumns:
    CLIENT_NAME = "clientName"
    TRADER_NAME = "traderName"
    COUNTERPARTY_NAME = "counterpartyName"
    DATE = "date"
    DATE_ADV = "Date"
    CLIENT_ADV = "clientAdv"
    INSTRUMENT_ID = "instrumentId"
    INST_FULL_NAME = "instrumentFullName"
    CLIENT_TOTAL_VOLUME = "clientTotalVolume"
    LOCAL_DATE = "local_date"
    MARKET_ADV = "marketAdv"
    MARKET_PRICE_END = "marketPriceEnd"
    MARKET_PRICE_START = "marketPriceStart"
    MARKET_TIME_END_LOCAL = "marketTimeEndLocal"
    MARKET_TIME_START_LOCAL = "marketTimeStartLocal"
    MKT_PRICE_PCT_DIFFERENCE = "marketPricePercentageDifference"
    MKT_TOTAL_VOLUME = "marketTotalVolume"
    ORDER_QTY_INST_ADV = "orderQuantityInstrumentAdv"
    ORDER_QTY_MKT_ADV = "orderQuantityMarketAdv"
    ORDER_SUBMITTED_TS_LOCAL = "orderSubmittedTSLocal"
    PHASE_TYPE = "phaseType"
    PRICE_SPIKE_DT_END_UTC = "price_spike_dt_end_utc"
    PRICE_SPIKE_DT_START_UTC = "price_spike_dt_start_utc"
    TIMEZONE = "timezone"
    VENUE = "venue"

    @classmethod
    def get_scenario_fields(cls):
        return [
            cls.CLIENT_NAME,
            cls.TRADER_NAME,
            cls.COUNTERPARTY_NAME,
            cls.CLIENT_ADV,
            cls.INSTRUMENT_ID,
            cls.INST_FULL_NAME,
            cls.MARKET_ADV,
            cls.MARKET_PRICE_START,
            cls.MARKET_PRICE_END,
            cls.MARKET_TIME_START_LOCAL,
            cls.MARKET_TIME_END_LOCAL,
            cls.MKT_PRICE_PCT_DIFFERENCE,
            cls.MKT_TOTAL_VOLUME,
            cls.ORDER_QTY_INST_ADV,
            cls.ORDER_QTY_MKT_ADV,
            cls.ORDER_SUBMITTED_TS_LOCAL,
            cls.PHASE_TYPE,
            cls.TIMEZONE,
            cls.VENUE,
        ]
