# ruff: noqa: E501
import addict
import logging
import pandas as pd
import time
from elasticsearch_dsl.query import Terms
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import (
    OrderAggs,
    OrderExecutionsQuery,
    OrderQuery,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    BestExAssetClassMain,
    NewColumns,
    OrderField,
    OrderStatus,
    VenueCode,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.static import (
    DFColumns,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import process_numeric_columns
from pandas.tseries.offsets import BDay
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from typing import List, Optional


class MarkingThePriceQueries(BaseQuery):
    ADV_WINDOW = 20

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        # DBs
        self._market_data_client = get_market_client(tenant=context.tenant)

        self._th_marking_type = self.context.thresholds.dict().get(ThresholdsNames.MARKING_TYPE)
        self._th_lbp = self.context.thresholds.dict().get(
            ThresholdsNames.LOOK_BACK_PERIOD
        )  # in seconds
        self._th_min_notional = self.context.thresholds.dict().get(ThresholdsNames.MINIMUM_NOTIONAL)
        self._th_min_notional_ccy = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_CURRENCY
        )
        self._th_client_20_day_adv = self.context.thresholds.dict().get(
            ThresholdsNames.CLIENT_20_DAY_ADV
        )
        self._th_market_20_day_adv = self.context.thresholds.dict().get(
            ThresholdsNames.MARKET_20_DAY_ADV
        )

        # Required fields
        self._required_fields = [
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.INST_CLASSIFICATION,
        ]

        self._evaluate_thresholds()

    def _evaluate_thresholds(self):
        # set date range
        query = self._get_base_query()
        self._set_date_range_thresholds(query)

        if self._th_client_20_day_adv is not None:
            self._required_fields.append(OrderField.PC_FD_INIT_QTY)

        if self._th_market_20_day_adv is not None:
            self._required_fields.append(OrderField.INST_EXT_UNIQUE_IDENT)

    def get_data_to_analyse(self):
        query = self._get_data_to_analyse_query()

        instrument_combinations = self.get_instruments_combinations(query)

        for inst_comb in instrument_combinations:
            start = time.perf_counter()
            query = self._get_data_to_analyse_query(inst_comb=inst_comb)

            result = self._sdp_repository.search_after_query(query)

            if result.empty:
                continue

            if self._th_min_notional is not None:
                result = self.get_filtered_order_dataframe(result)

            if result.empty:
                logging.info("No data available after applying Minimum Notional filter")
                continue

            result = self._process_tenant_data(result)

            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"
            )
            yield result

    def _get_data_to_analyse_query(
        self, inst_comb: Optional[List[str]] = None, query_client_adv: bool = False
    ) -> OrderQuery:
        if query_client_adv:
            query = self._get_base_query(add_base_filters=False)
        else:
            query = self._get_base_query()

            query.add_end_date(self._end_date, field=OrderField.TS_ORD_SUBMITTED)

            query.add_start_date(self._start_date, field=OrderField.TS_ORD_SUBMITTED)

        query.size(OrderQuery.SIZE)

        includes = [
            OrderField.BUYER,
            OrderField.CLIENT_FILE_IDENTIFIER,
            OrderField.CLIENT_IDENT_CLIENT,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.INST_CLASSIFICATION,
            OrderField.INST_EXT_EXCHANGE_SYMBOL_ROOT,
            OrderField.META_KEY,
            OrderField.PARTICIPANTS,
            OrderField.SELLER,
            OrderField.TRADER,
            *self._required_fields,
            *OrderField.get_instrument_fields(),
        ]

        if self._th_market_20_day_adv is not None:
            includes.extend([OrderField.INST_EXT_UNIQUE_IDENT, OrderField.TRX_DTL_ULTIMATE_VENUE])

        query.includes(includes)

        if inst_comb is not None:
            query.instrument_id(inst_comb)

        return query

    def _get_base_query(self, add_base_filters: bool = True) -> OrderQuery:
        """Method to build the base query to be used across MTC and MTO.

        :param add_base_filters: if True it will add the iris filters to the query
        :return: OrderQuery
        """
        query = OrderQuery()

        self.add_default_conditions_to_query(query, add_base_filters=add_base_filters)

        query = self.get_query_with_required_fields(query, fields=self._required_fields)

        # THR matching conditions
        shoulds = [
            {"regexp": {OrderField.INST_CLASSIFICATION: {"value": "[CFOH]{1}.*"}}},
            Terms(
                **{
                    OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN: [
                        BestExAssetClassMain.EQUITY,
                        BestExAssetClassMain.EXCHANGE_TRADED_PRODUCTS,
                        BestExAssetClassMain.STRUCTURED_FINANCE_INSTRUMENTS,
                        BestExAssetClassMain.DEBT_INSTRUMENTS,
                    ]
                }
            ),
        ]
        query.add_condition(conditions=shoulds, mode="should")

        # Discard records with XXXX or XOFF ultimate venue
        query.add_condition(
            mode="must_not",
            conditions=[
                Terms(
                    **{
                        OrderField.TRX_DTL_ULTIMATE_VENUE: [
                            VenueCode.XXXX,
                            VenueCode.XOFF,
                        ]
                    }
                )
            ],
        )

        return query

    @staticmethod
    def _process_tenant_data(result: pd.DataFrame) -> pd.DataFrame:
        # Set types float

        result: pd.DataFrame = process_numeric_columns(data=result)

        result[DFColumns.DATE] = result[OrderField.TS_ORD_SUBMITTED].dt.date

        return result

    def get_venues_to_analyse_venues(self) -> List[str]:
        query = self._get_data_to_analyse_query()

        query.size(0)

        query.aggs.bucket(
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            "terms",
            field=OrderField.TRX_DTL_ULTIMATE_VENUE,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        result = self._sdp_repository.search_query(query)

        if result["hits"]["total"]["value"] == 0:
            return []

        venues = [
            x["key"] for x in result["aggregations"][OrderField.TRX_DTL_ULTIMATE_VENUE]["buckets"]
        ]

        return venues

    # ------------------------------------
    # Client ADV
    # ------------------------------------

    def get_client_adv(
        self,
        instrument_codes: List[str],
        client_ids: List[str],
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> pd.DataFrame:
        """Method to get the client adv for a given instrument and client.

        :param instrument_codes: list with the instrument to calculate adv
        :param client_ids: list with the client to calculate adv
        :param start_date: start date to search for
        :param end_date: end date to search for
        :return: dataframe with calculated adv
        """
        query = self._get_client_adv_query(
            instrument_codes=instrument_codes,
            client_ids=client_ids,
            start_date=start_date,
            end_date=end_date,
        )

        result = self._sdp_repository.search_query(query)

        if result.get("hits", {}).get("total", {}).get("value", 0) == 0:
            return pd.DataFrame()

        result = result.get("aggregations").get(OrderAggs.BY_INSTRUMENT).get("buckets")

        result = self._process_client_adv_result(result)

        result[DFColumns.DATE] = pd.to_datetime(result[DFColumns.DATE], format="mixed")

        # Logic to fill the missing days to have 20 business days to calculate the adv
        date_range = pd.date_range(
            start=result[DFColumns.DATE].min() - BDay(self.ADV_WINDOW),
            end=result[DFColumns.DATE].max(),
            freq="D",
        )

        missing_dates = date_range[~date_range.isin(result[DFColumns.DATE])]

        missing_df = pd.DataFrame(
            {
                DFColumns.DATE: missing_dates,
                DFColumns.CLIENT_TOTAL_VOLUME: 0.0,
                NewColumns.INSTRUMENT_CODE: instrument_codes[0],
            }
        )

        result = pd.concat([result, missing_df]).sort_values(DFColumns.DATE).reset_index(drop=True)

        result = result.sort_values(by=DFColumns.DATE, ascending=True)

        # select only business days
        result: pd.DataFrame = result.loc[result[DFColumns.DATE].dt.dayofweek < 5]

        avg_col = self._market_data_client.calculate_average_daily_trading_volume(
            market_data=result,
            data_column=DFColumns.CLIENT_TOTAL_VOLUME,
            look_back_period=self.ADV_WINDOW,
            time_series=DFColumns.CLIENT_ADV,
            min_periods=1,
        )

        result.loc[:, OrderField.CLIENT_FILE_IDENTIFIER] = client_ids[0]
        result.loc[:, DFColumns.CLIENT_ADV] = avg_col

        return result

    def _get_client_adv_query(
        self,
        instrument_codes: List[str],
        client_ids: List[str],
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> OrderQuery:
        """Method build the query to fetch the total quantities of orders for
        the given instrument and client.

        :param instrument_codes: list with the instrument to calculate adv
        :param client_ids: list with the client to calculate adv
        :param start_date: start date to search for
        :param end_date: end date to search for
        :return: query to fetch the client total quantities per day
        """
        query = self._get_data_to_analyse_query(query_client_adv=True)
        query.size(0)
        query.instrument_id(instrument_codes)
        query.add_terms_query_with_chunks(
            value=client_ids, field=OrderField.CLIENT_FILE_IDENTIFIER, mode="filter"
        )

        # remove/add 5 days to the dates so that we have a margin to calculate the adv with 20 BDays
        start_date = start_date - BDay(self.ADV_WINDOW) - pd.Timedelta(days=5)
        end_date = end_date + pd.Timedelta(days=5)

        query.add_start_date(start_date, field=OrderField.TS_ORD_SUBMITTED)
        query.add_end_date(end_date, field=OrderField.TS_ORD_SUBMITTED)

        q_aggs = query.q.aggs

        # Aggregations
        q_aggs = OrderAggs.aggs_instrument(q_aggs)

        q_aggs = OrderAggs.aggs_date_histogram(
            q_aggs, field=OrderField.TS_ORD_SUBMITTED, interval="1d"
        )

        q_aggs.metric(OrderAggs.TOTAL_VOLUME, "sum", field=OrderField.PC_FD_INIT_QTY)

        return query

    def _process_client_adv_result(self, result: addict.Dict) -> pd.DataFrame:
        dfs = [self._get_instrument_histogram_result(x, instrument=x["key"]) for x in result]

        df = pd.concat(dfs, ignore_index=True)

        if df.empty:
            return pd.DataFrame()

        df[DFColumns.DATE] = pd.to_datetime(df[DFColumns.DATE], format="mixed").dt.date

        return df

    @staticmethod
    def _get_instrument_histogram_result(result: addict.Dict, instrument: str) -> pd.DataFrame:
        df = pd.json_normalize(result.to_dict()[OrderAggs.DATE_HISTOGRAM]["buckets"])
        df.columns = df.columns.str.rstrip("value").str.replace(".", "")

        df = df.loc[:, ~df.columns.isin(["doc_count", "key"])].rename(
            columns={
                "key_as_string": DFColumns.DATE,
                OrderAggs.ADV: DFColumns.CLIENT_ADV,
                OrderAggs.TOTAL_VOLUME: DFColumns.CLIENT_TOTAL_VOLUME,
            }
        )

        if df.empty:
            return df

        df[NewColumns.INSTRUMENT_CODE] = instrument

        return df

    # ------------------------------------
    # Market ADV
    # ------------------------------------

    def get_market_data_adv(
        self,
        instrument_unique_identifiers: List[str],
    ) -> pd.DataFrame:
        """Based on the instrument unique identifiers retrieved, fetches the
        market data with the adv calculated for the window set in the
        threshold.

        :param instrument_unique_identifiers: List of instrument unique identifiers.
        :return:
        """

        results = []

        for inst_id in instrument_unique_identifiers:
            data = self._market_data_client.get_avg_daily_trading_volume(
                instrument_unique_identifier=inst_id,
                time_series_col=DFColumns.DATE_ADV,
                adtv_column=DFColumns.MARKET_ADV,
                look_back_period=self.ADV_WINDOW,
                min_periods=2,
                start_date=self.look_back_period_ts,
                end_date=self.market_data_end_date,
            )

            if data.empty:
                continue

            data[OrderField.INST_EXT_UNIQUE_IDENT] = inst_id

            results.append(data)

        if not results:
            return pd.DataFrame()

        df = pd.concat(results).drop_duplicates()

        return df

    # ------------------------------------
    # Price Spike
    # ------------------------------------
    def get_market_data_for_price_spike(self, data: pd.DataFrame) -> pd.DataFrame:
        with_inst_unique_ident_mask = data[OrderField.INST_EXT_UNIQUE_IDENT].notnull()

        if not with_inst_unique_ident_mask.any():
            return pd.DataFrame()

        results = []

        timestamps_series_name = "__input_timestamp__"
        for inst_id, group in data.groupby(OrderField.INST_EXT_UNIQUE_IDENT):
            timestamps = self._get_unique_timestamps(
                data=group,
                timestamps_columns=[
                    DFColumns.PRICE_SPIKE_DT_START_UTC,
                    DFColumns.PRICE_SPIKE_DT_END_UTC,
                ],
            )
            timestamps.name = timestamps_series_name

            market_data: pd.DataFrame = self._market_data_client.get_tick_data(
                instrument_unique_identifier=inst_id,
                dates=timestamps,
                nearest_timestamp=True,  # used to have single_tick=True
                event_type=RefinitivEventType.TRADE,
            )

            if market_data.empty:
                continue

            result = self._process_market_price(
                df=group, market_data=market_data, timestamp_col=timestamps_series_name
            )

            results.append(result)

        if not results:
            return pd.DataFrame()

        df = pd.concat(results).drop_duplicates()

        return df

    @staticmethod
    def _get_unique_timestamps(data: pd.DataFrame, timestamps_columns: List[str]) -> pd.Series:
        """Given a dataframe a list of timestamps columns, returns a series
        with unique timestamps on those columns.

        :param data:
        :param timestamps_columns:
        :return:
        """
        timestamps = pd.Series(pd.unique(data[timestamps_columns].values.ravel("K")))

        return timestamps

    @staticmethod
    def _process_market_price(
        df: pd.DataFrame, market_data: pd.DataFrame, timestamp_col: str
    ) -> pd.DataFrame:
        """Returns a dataframe with the MARKET_PRICE_START and MARKET_PRICE_END
        mapped.

        :param df:
        :param market_data:
        :param timestamp_col:
        :return:
        """
        timestamp_price_map = (
            market_data[[timestamp_col, RefinitivExtractColumns.PRICE]]
            .set_index(timestamp_col)[RefinitivExtractColumns.PRICE]
            .to_dict()
        )

        result = df[
            [
                OrderField.INST_EXT_UNIQUE_IDENT,
                DFColumns.PRICE_SPIKE_DT_START_UTC,
                DFColumns.PRICE_SPIKE_DT_END_UTC,
            ]
        ]

        result[DFColumns.MARKET_PRICE_START] = result[DFColumns.PRICE_SPIKE_DT_START_UTC].map(
            timestamp_price_map
        )
        result[DFColumns.MARKET_PRICE_END] = result[DFColumns.PRICE_SPIKE_DT_END_UTC].map(
            timestamp_price_map
        )

        return result

    # -----------------------------------------
    # Additional fields
    # -----------------------------------------
    def get_additional_fields_result(self, orders_keys: list) -> pd.DataFrame:
        query = OrderQuery()
        query.size(self.MINI_BATCH_SIZE)

        query.key(orders_keys)

        includes = [
            OrderField.META_KEY,
            OrderField.INST_FULL_NAME,
            OrderField.EXC_DTL_BUY_SELL_IND,
            *OrderField.get_client_fields(),
            OrderField.COUNTERPARTY_NAME,
            OrderField.PARTICIPANTS,
        ]

        query.includes(includes)

        result = self._sdp_repository.search_after_query(query)

        if result.empty:
            return result

        client_name = NewColumns.CLIENT

        if NewColumns.CLIENT not in result.columns and NewColumns.TRADER_NAME in result.columns:
            client_name = NewColumns.TRADER_NAME

        result = (
            result.rename(
                columns={
                    OrderField.INST_FULL_NAME: DFColumns.INST_FULL_NAME,
                    client_name: DFColumns.CLIENT_NAME,
                    OrderField.COUNTERPARTY_NAME: DFColumns.COUNTERPARTY_NAME,
                }
            )
            .drop(columns=[NewColumns.INSTRUMENT_CODE])
            .set_index(OrderField.META_KEY)
        )

        return result

    @staticmethod
    def datetime_to_timestamp(df: pd.DataFrame, column: str) -> pd.DataFrame:
        """Converts a column with multiple datetime types into timestamps.

        :param df: dataframe to be coverted
        :param column: name of the column to be converted
        :return: processed df
        """
        try:
            df[column] = df[column].apply(
                lambda x: pd.to_datetime(x, utc=True, format="mixed").replace(tzinfo=None)
            )
        except ValueError:
            logging.error(
                msg="Non datetime like values used when only datimetime values are allowed",
                exc_info=True,
            )
            return pd.DataFrame()

        return df

    def get_filtered_order_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filters orders dataframe with minimum notional above the threshold.

        :param df: dataframe with NEWOs
        :return: filtered dataframe
        """

        if self._th_min_notional is None or self._th_min_notional == 0:
            logging.warning(
                "minimumNotional has been set to 0 or null. Orders will not be filtered based on minimumNotional"
            )

        order_ids: List[str] = df.loc[:, OrderField.ORD_IDENT_ID_CODE].unique().tolist()

        executions_df: pd.DataFrame = self.fetch_order_executions(order_ids=order_ids)

        if executions_df.empty:
            logging.info(f"No executions data exist for the the following OrderIds: {order_ids}")
            return pd.DataFrame()

        filtered_order_ids: List[str] = (
            self.filter_minimum_notional(df=executions_df)
            if self._th_min_notional > 0
            else order_ids
        )

        if not filtered_order_ids:
            logging.info(
                f"No Order has a minimum notional value above the threshold: {self._th_min_notional}"
            )
            return pd.DataFrame()

        ### Filter order that have minimum notional above the threshold
        filter_mask: pd.Series = df.loc[:, OrderField.ORD_IDENT_ID_CODE].isin(filtered_order_ids)
        filtered_df: pd.DataFrame = df.loc[filter_mask]

        return filtered_df

    def fetch_order_executions(self, order_ids: List[str]) -> pd.DataFrame:
        """Fetches execution data for the input order ids.

        :param dataframe: pd.DataFrame. Newos data
        :param order_ids: list of newos ids
        :return: dataframe with executions data
        """

        executions_query: OrderExecutionsQuery = self._get_order_executions_query(
            orders_ids=order_ids
        )
        executions_df: pd.DataFrame = self._sdp_repository.search_after_query(executions_query)

        return executions_df

    def _get_order_executions_query(
        self,
        orders_ids: List[str],
    ) -> OrderExecutionsQuery:
        """Query necessary to obtain executions to calculate minimum notional.

        :param orders_ids: lists of order ids to fetch executions
        :return: order state query
        """

        specific_fields = [
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
        ]

        if self._th_min_notional > 0:
            specific_fields.append(
                f"{OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE}.{self._th_min_notional_ccy}"
            )

        required_fields = [*specific_fields]
        include_fields = [*specific_fields]

        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.includes(include_fields)

        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=required_fields)

        query.order_id(orders_ids)

        return query

    def filter_minimum_notional(self, df: pd.DataFrame) -> List[str]:
        """Filter executions and return list of filtered order ids by minimum
        notional value.

        :param df: executions to be filtered
        :return: list of order ids
        """

        order_to_bool: pd.Series = (
            df.groupby(OrderField.ORD_IDENT_ID_CODE)[
                f"{OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE}.{self._th_min_notional_ccy}"
            ].sum()
            >= self._th_min_notional
        )
        filtered_orders: List[str] = order_to_bool.loc[order_to_bool].index.tolist()

        return filtered_orders
