import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON><PERSON><PERSON><PERSON>, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["executions"])

    def _build_scenario(self) -> pd.Series:
        top_level = dict(
            date=self._result["date"].isoformat(),
            instrumentId=self._result["instrument_id"],
            instrumentFullName=self._result["instrument_full_name"],
            totalBuyVolume=self._result["total_buys_volume"],
            totalSellVolume=self._result["total_sells_volume"],
            buySellRatio=self._result["buy_sell_ratio"],
            sellBuyRatio=self._result["sell_buy_ratio"],
            bestExAssetClassMain=self._result["bestExAssetClassMain"],
            bestExAssetClassSub=self._result["bestExAssetClassSub"],
        )

        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records.executions = sorted(self._result["executions"])
        d.additionalFields.topLevel = top_level

        records = {"executions": self._result["executions"]}

        scenario = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: top_level},
        )

        scenario = pd.Series(scenario)

        return scenario
