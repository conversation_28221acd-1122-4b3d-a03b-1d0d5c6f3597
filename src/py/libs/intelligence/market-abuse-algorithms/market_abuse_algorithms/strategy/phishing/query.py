# ruff: noqa: E501
import pandas as pd
import time
from elasticsearch_dsl import A
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery
from market_abuse_algorithms.data_source.static.sdp.order import Buy<PERSON><PERSON>, OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from market_abuse_algorithms.strategy.phishing.static import ThresholdsNames


class Queries(BaseQuery):
    """aggs query for instruments traded within a day where aggregated buy
    count > 0 AND aggregated sell count > 0."""

    INCLUDES_FIELDS = [
        OrderField.META_KEY,
        OrderField.INST_FULL_NAME,
        OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
        OrderField.INST_EXT_BEST_EX_ASSET_CLASS_SUB,
    ]

    REQUIRED_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.PC_FD_TRD_QTY,
        OrderField.get_instrument_fields(),
        OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)
        self._th_buy_sell_ratio = self.context.thresholds.dict().get(ThresholdsNames.BUY_SELL_RATIO)
        self._th_sell_buy_ratio = self.context.thresholds.dict().get(ThresholdsNames.SELL_BUY_RATIO)

    def cases_to_analyse(self):
        start = time.perf_counter()
        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query=query, fields=self.REQUIRED_FIELDS)

        query.size(0)

        date_agg = A("terms", field="date", size=100, order={"_key": "asc"})
        instrument_agg = A("terms", script=QueryAggScripts.ISIN_PRIORITY_SCRIPT, size=1000)

        # Define sub-aggregations for buys
        buys_filter = A(
            "filters",
            filters={"buy_filter": {"term": {OrderField.EXC_DTL_BUY_SELL_IND: BuySell.BUY}}},
        )
        buys_volume = A("sum", field=OrderField.PC_FD_TRD_QTY)
        buys_filter.bucket("volume", buys_volume)

        # Define sub-aggregations for sells
        sells_filter = A(
            "filters",
            filters={"sell_filter": {"term": {OrderField.EXC_DTL_BUY_SELL_IND: BuySell.SELL}}},
        )
        sells_volume = A("sum", field=OrderField.PC_FD_TRD_QTY)
        sells_filter.bucket("volume", sells_volume)

        # Sum bucket aggregations
        buy_volume_sum = A("sum_bucket", buckets_path="buys>volume")
        sell_volume_sum = A("sum_bucket", buckets_path="sells>volume")

        # Bucket script aggregations
        buy_vs_sell_volumes_ratio = A(
            "bucket_script",
            buckets_path={"x": "buyVolumeSum", "y": "sellVolumeSum"},
            script="params.x / params.y",
        )
        sell_vs_buy_volumes_ratio = A(
            "bucket_script",
            buckets_path={"x": "buyVolumeSum", "y": "sellVolumeSum"},
            script="params.y / params.x",
        )

        # Bucket selector aggregation
        vol_ratio_filter = A(
            "bucket_selector",
            buckets_path={"x": "buyVsSellVolumesRatio", "y": "sellVsBuyVolumesRatio"},
            script=f"params.x >= {self._th_buy_sell_ratio} || params.y >= {self._th_sell_buy_ratio}",
        )

        # Combine all sub-aggregations under instrument aggregation
        instrument_agg.bucket("buys", buys_filter)
        instrument_agg.bucket("sells", sells_filter)
        instrument_agg.bucket("buyVolumeSum", buy_volume_sum)
        instrument_agg.bucket("sellVolumeSum", sell_volume_sum)
        instrument_agg.bucket("buyVsSellVolumesRatio", buy_vs_sell_volumes_ratio)
        instrument_agg.bucket("sellVsBuyVolumesRatio", sell_vs_buy_volumes_ratio)
        instrument_agg.bucket("vol_ratio_filter", vol_ratio_filter)

        # Combine all under date aggregation
        date_agg.bucket("instrument", instrument_agg)

        # Add the aggregation to the search object
        query.aggs.bucket("date", date_agg)

        result = self._sdp_repository.search_query(query=query)
        self._logger.info(
            f"For a instrument combination of size {len([])}, it took {time.perf_counter() - start} seconds"
        )
        return result

    def get_order_executions_data(self, instrument_id: str, date: pd.Timestamp) -> pd.DataFrame:
        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.instrument_id(instrument_id)
        query.date(date, to_date=True)
        query = self.get_initial_query(query)
        result = self._sdp_repository.search_after_query(query=query)

        if result.empty:
            raise Exception("No order states data.")

        return result

    # TODO: currently I couldn't get further with the dsl
    # def get_results_2(
    #     self,
    #     asset_class,
    #     buysell_ratio_threshold,
    #     sellbuy_ratio_threshold,
    #     filters=None,
    # ):
    #     query = self.cd_order.get_order_states(
    #         size=500,
    #         filters=filters,
    #         order_status=[OrderStatus.FILL, OrderStatus.PARF],
    #         asset_class=asset_class,
    #     )
    #
    #     # Groupby date
    #     query.q.aggs.bucket("date", "terms", field="date", size=100, order="asc")
    #
    #     # Groupby instrument
    #     query.q.aggs["date"].bucket(
    #         "instrument",
    #         "terms",
    #         script={
    #             "source": f"doc['{OrderField.BEST_EX_ASSET_CLASS_MAIN}'].value + '{INSTR_SEPARATOR}' + doc['{OrderField.INSTRUMENT_ID_CODE}'].value + '{INSTR_SEPARATOR}' + doc['{OrderField.INSTRUMENT_FULL_NAME}'].value",
    #             "lang": "painless",
    #         },
    #         size=1000,
    #     )
    #
    #     # Top hits
    #     query.q.aggs["date"]["instrument"].metric(
    #         "results", "top_hits", size=1000, _source={"includes": [MetaFields.KEY]}
    #     )
    #
    #     # Buys
    #     query.q.aggs["date"]["instrument"]["buys"] = {
    #         "filters": {"filters": {"term": {OrderField.BUY_SELL: BuySell.BUY}}},
    #         "aggs": {"volume": {"sum": {"field": OrderField.QUANTITY}}},
    #     }
    #
    #     # Sells
    #     query.q.aggs["date"]["instrument"]["sells"] = {
    #         "filters": {"filters": {"term": {OrderField.BUY_SELL: BuySell.SELL}}},
    #         "aggs": {"volume": {"sum": {"field": OrderField.QUANTITY}}},
    #     }
    #
    #     # Buy Volume Sum
    #     query.q.aggs["date"]["instrument"]["buyVolumeSum"] = {
    #         "sum_bucket": {"buckets_path": "buys>volume"}
    #     }
    #
    #     # Sell Volume Sum
    #     query.q.aggs["date"]["instrument"]["sellVolumeSum"] = {
    #         "sum_bucket": {"buckets_path": "sells>volume"}
    #     }
    #
    #     # Buy Vs Sell Volumes Ratio
    #     query.q.aggs["date"]["instrument"]["buyVsSellVolumesRatio"] = {
    #         "bucket_script": {
    #             "buckets_path": {"x": "buyVolumeSum", "y": "sellVolumeSum"},
    #             "script": "params.x / params.y",
    #         }
    #     }
    #
    #     # Sell Vs Buy Volumes Ratio
    #     query.q.aggs["date"]["instrument"]["sellVsBuyVolumesRatio"] = {
    #         "bucket_script": {
    #             "buckets_path": {"x": "buyVolumeSum", "y": "sellVolumeSum"},
    #             "script": "params.y / params.x",
    #         }
    #     }
    #
    #     # Volume Ratio Filter
    #     query.q.aggs["date"]["instrument"]["vol_ratio_filter"] = {
    #         "bucket_selector": {
    #             "buckets_path": {
    #                 "x": "buyVsSellVolumesRatio",
    #                 "y": "sellVsBuyVolumesRatio",
    #             },
    #             "script": f"params.x >= {buysell_ratio_threshold} || params.y >= {sellbuy_ratio_threshold}",
    #         }
    #     }
    #
    #     result = self.cd_order.search_after_query(query=query)
    #
    #     return result
