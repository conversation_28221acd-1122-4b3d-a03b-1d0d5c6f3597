import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import (
    <PERSON><PERSON>ell,
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.query import (
    Queries,
)
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.scenario import (
    Scenario,
)
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.static import (
    DFColumns,
    DisorderlyWindowTypeEnum,
    ThresholdsNames,
    TraderEnum,
)
from market_abuse_algorithms.utils.data import set_time_thresholds
from typing import List


class Strategy(AbstractStrategy):
    """Forcing the Market Against Its Will.

    https://steeleye.atlassian.net/browse/PR-1453
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.FORCING_THE_MARKET_AGAINST_ITS_WILL,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_disorderly_fill_lot_minimum = self.context.thresholds.dict().get(
            ThresholdsNames.DISORDERLY_FILL_LOT_MINIMUM
        )
        self._th_disorderly_window = set_time_thresholds(
            time_field=self.context.thresholds.dict().get(ThresholdsNames.DISORDERLY_WINDOW)
        )
        self._th_disorderly_window_type = self.context.thresholds.dict().get(
            ThresholdsNames.DISORDERLY_WINDOW_TYPE
        )
        self._th_group_by = self.context.thresholds.dict().get(ThresholdsNames.GROUP_BY)
        self._th_limit_price_vs_execution_price_spread = self.context.thresholds.dict().get(
            ThresholdsNames.LIMIT_PRICE_VS_EXECUTION_PRICE_SPREAD
        )
        self._th_minimum_new_replace_instructions = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NEW_REPLACE_INSTRUCTIONS
        )
        self._th_spread_contraction = self.context.thresholds.dict().get(
            ThresholdsNames.SPREAD_CONTRACTION
        )
        # self._set_time_thresholds()

    def _set_time_thresholds(self):
        """This method converts time window thresholds to pd.Timedelta."""
        if self._th_disorderly_window is not None:
            self._th_disorderly_window = pd.Timedelta(
                value=self._th_disorderly_window.value,
                unit=self._th_disorderly_window.unit.value,
            )

    def _apply_strategy(self):
        """for loop for applying the strategy to each chunck of date obtained
        by the query."""
        for data in self.queries.get_cases_to_analyse():
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        """"""
        if self._th_group_by:
            if self._th_group_by == TraderEnum.TRADER and OrderField.TRADER in data.columns:
                for ix, group in data.groupby(
                    [NewColumns.INSTRUMENT_CODE, OrderField.TRADER], as_index=False
                ):
                    self._run_algo(df=group)
            elif (
                self._th_group_by == TraderEnum.TRADER_FILE_IDENTIFIER
                and OrderField.TRADER_FILE_IDENTIFIER in data.columns
            ):
                for ix, group in data.groupby(
                    [NewColumns.INSTRUMENT_CODE, OrderField.TRADER_FILE_IDENTIFIER],
                    as_index=False,
                ):
                    self._run_algo(df=group)

        else:
            for ix, group in data.groupby(NewColumns.INSTRUMENT_CODE, as_index=False):
                self._run_algo(df=group)

    def _run_algo(self, df: pd.DataFrame):
        """Runs the algo with the input data pandas Dataframe Creates possible
        scenarios with the results of the algo."""
        results = self._algo(df)

        if not results:
            return

        for result in results:
            self._create_scenarios(df=result)

    def _algo(self, df: pd.DataFrame) -> List[pd.DataFrame]:
        """Algo with multiple filters being applied."""
        res = []
        df = df.sort_values(by=OrderField.TS_ORD_UPDATED, ascending=True)
        disorderly_groups = self._apply_disorderly_window(df=df)
        if len(disorderly_groups) < 1:
            return res
        for group in disorderly_groups:
            if not self._check_passive_agressive_indicator(group):
                continue
            if not self._check_limit_vs_execution_spread(group):
                continue
            if not self._check_minimum_new_replace_instructions(group):
                continue
            if not self._check_filtering_data(group):
                continue
            if (
                self._th_disorderly_fill_lot_minimum is not None
                and not self._check_quantity_minimum(group)
            ):
                continue
            group = self._check_disorderly_spread(group)
            if group.empty:
                continue
            res.append(group)
        return res

    def _apply_disorderly_window(self, df: pd.DataFrame) -> List[pd.DataFrame]:
        res = []
        df[DFColumns.TIMEDELTA] = (
            df[OrderField.TS_ORD_UPDATED]
            .diff()
            .fillna(pd.Timedelta(seconds=0))
            .astype("timedelta64[s]")
        )
        df[DFColumns.TIMEDELTA] = df[DFColumns.TIMEDELTA].apply(
            lambda x: pd.to_timedelta(x, unit="seconds")
        )
        df = df.reset_index(drop=True)
        if self._th_disorderly_window_type == DisorderlyWindowTypeEnum.DYNAMIC:
            count = 0
            disorderly_groups = []
            for index, row in df.iterrows():
                if row[DFColumns.TIMEDELTA] > self._th_disorderly_window:
                    count += 1
                    disorderly_groups.append(count)
                else:
                    disorderly_groups.append(count)
            df[DFColumns.DISORDERLY_GROUPS] = pd.Series(disorderly_groups)
            for ix, group in df.groupby(DFColumns.DISORDERLY_GROUPS):
                if len(group[OrderField.ORD_IDENT_ID_CODE].unique()) > 1:
                    res.append(group)

        if self._th_disorderly_window_type == DisorderlyWindowTypeEnum.STATIC:
            df[DFColumns.MAX_WINDOW] = df[OrderField.TS_ORD_UPDATED] + self._th_disorderly_window

            df[DFColumns.DISORDERLY_GROUPS] = df.apply(
                lambda x: df.loc[
                    (x[DFColumns.MAX_WINDOW] >= df[OrderField.TS_ORD_UPDATED])
                    & (df[OrderField.TS_ORD_UPDATED] >= x[OrderField.TS_ORD_UPDATED]),
                    OrderField.META_KEY,
                ].tolist(),
                axis=1,
            )

            for ix, row in df.iterrows():
                temp_def = df.loc[df[OrderField.META_KEY].isin(row[DFColumns.DISORDERLY_GROUPS])]
                if any(map(temp_def.equals, res)):
                    continue
                else:
                    res.append(temp_def)

        return res

    def _check_passive_agressive_indicator(self, df: pd.DataFrame) -> bool:
        return df[OrderField.EXC_DTL_PASS_AGGR_IND].isin(["AGRE"]).any()

    def _check_limit_vs_execution_spread(self, df: pd.DataFrame) -> bool:
        if OrderField.EXC_DTL_LIMIT_PRICE not in df.columns:
            return False
        aggressive_orders_df = df[
            (df[OrderField.EXC_DTL_PASS_AGGR_IND] == "AGRE")
            | (df[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.FILL, OrderStatus.PARF]))
        ]
        indicator = False
        for index, row in aggressive_orders_df.iterrows():
            if indicator:
                price = row[OrderField.PC_FD_PRICE]
                limit_price = row[OrderField.EXC_DTL_LIMIT_PRICE]
                if row[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY:
                    if (limit_price - price) >= self._th_limit_price_vs_execution_price_spread:
                        return True
                elif row[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL:
                    if (price - limit_price) >= self._th_limit_price_vs_execution_price_spread:
                        return True
            if row[OrderField.EXC_DTL_PASS_AGGR_IND] == "AGRE":
                indicator = True
            else:
                indicator = False

        return False

    def _check_minimum_new_replace_instructions(self, df: pd.DataFrame) -> bool:
        buy_sell_ind = df[OrderField.EXC_DTL_BUY_SELL_IND].iloc[0]
        buy_sell_counts = (
            (
                df[OrderField.EXC_DTL_ORD_STATUS]
                .iloc[1:]
                .isin(
                    [
                        OrderStatus.NEWO,
                        OrderStatus.REMA,
                        OrderStatus.REME,
                        OrderStatus.REMH,
                    ]
                )
            )
            & (df[OrderField.EXC_DTL_BUY_SELL_IND] == buy_sell_ind)
        ).sum()

        if buy_sell_counts >= self._th_minimum_new_replace_instructions:
            return True

        return False

    @staticmethod
    def _check_filtering_data(df: pd.DataFrame) -> bool:
        if OrderField.EXC_DTL_LIMIT_PRICE not in df.columns:
            return False
        earliest_order = df.iloc[0]
        df = df.iloc[1:, :]
        buy_sell_ind = earliest_order[OrderField.EXC_DTL_BUY_SELL_IND]
        order_states = df[
            (df[OrderField.EXC_DTL_BUY_SELL_IND] == buy_sell_ind)
            & (
                df[OrderField.EXC_DTL_ORD_STATUS].isin(
                    [
                        OrderStatus.NEWO,
                        OrderStatus.REMA,
                        OrderStatus.REME,
                        OrderStatus.REMH,
                    ]
                )
            )
        ]
        if buy_sell_ind == BuySell.BUY:
            if (
                order_states[OrderField.EXC_DTL_LIMIT_PRICE]
                >= earliest_order[OrderField.EXC_DTL_LIMIT_PRICE]
            ).any():
                return True
        elif buy_sell_ind == BuySell.SELL:
            if (
                order_states[OrderField.EXC_DTL_LIMIT_PRICE]
                <= earliest_order[OrderField.EXC_DTL_LIMIT_PRICE]
            ).any():
                return True

        return False

    def _check_quantity_minimum(self, df: pd.DataFrame) -> bool:
        orders_states = df[
            df[OrderField.EXC_DTL_ORD_STATUS].isin([OrderStatus.FILL, OrderStatus.PARF])
        ]
        fill_quantity = orders_states[OrderField.PC_FD_TRD_QTY].sum()
        if fill_quantity >= self._th_disorderly_fill_lot_minimum:
            return True
        return False

    def _check_disorderly_spread(self, df: pd.DataFrame) -> pd.DataFrame:
        order_states = df[df[OrderField.PC_FD_PRICE] != 0]
        earliest_order = order_states.iloc[0]
        latest_order = order_states.iloc[-1]
        spread_contraction = abs(
            earliest_order[OrderField.PC_FD_PRICE] - latest_order[OrderField.PC_FD_PRICE]
        )
        if spread_contraction >= self._th_spread_contraction:
            df[DFColumns.SPREAD_CONTRACTION] = spread_contraction
            return df
        return pd.DataFrame()

    def _create_scenarios(self, df: pd.DataFrame):
        """creates scenarios with the input data results."""
        result = df.to_dict()

        earliest_order = df.iloc[0]

        result[DFColumns.EARLIEST_TIMESTAMP] = earliest_order[OrderField.TS_ORD_UPDATED]
        result[DFColumns.EARLIEST_INSTRUMENT_ID] = earliest_order[OrderField.INST_ID_CODE]
        result[DFColumns.EARLIEST_INSTRUMENT_FULL_NAME] = earliest_order[OrderField.INST_FULL_NAME]

        if OrderField.TRADER in df.columns:
            result[DFColumns.TRADER_LIST] = df[OrderField.TRADER].unique().tolist()
        if OrderField.TRADER_FILE_IDENTIFIER in df.columns:
            result[DFColumns.TRADER_FILE_IDENTIFIER_LIST] = (
                df[OrderField.TRADER_FILE_IDENTIFIER].unique().tolist()
            )

        result[DFColumns.NUMBER_NEWOS] = df[df[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO][
            OrderField.EXC_DTL_ORD_STATUS
        ].count()

        result[DFColumns.NUMBER_REPLACEMENTS] = df.loc[
            df[OrderField.EXC_DTL_ORD_STATUS].isin(
                [OrderStatus.REME, OrderStatus.REMA, OrderStatus.REMH]
            ),
            OrderField.ORD_IDENT_ID_CODE,
        ].nunique()

        result[DFColumns.TOTAL_ORDER_QUANTITY] = df[
            df[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO
        ][OrderField.PC_FD_INIT_QTY].sum()

        result[DFColumns.FILL_QUANTITY] = df[df[OrderField.EXC_DTL_ORD_STATUS] != OrderStatus.NEWO][  # noqa: E501
            OrderField.PC_FD_TRD_QTY
        ].sum()

        result[DFColumns.TIMESTAMP_DIFFERENCE] = (
            df.iloc[-1][OrderField.TS_ORD_UPDATED] - df.iloc[0][OrderField.TS_ORD_UPDATED]
        )

        result[DFColumns.SPREAD_CONTRACTION] = df[DFColumns.SPREAD_CONTRACTION].iloc[0]

        result[DFColumns.ORDERS] = df[df[OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO][
            OrderField.META_KEY
        ].tolist()

        result[DFColumns.ORDER_STATES] = df[df[OrderField.EXC_DTL_ORD_STATUS] != OrderStatus.NEWO][
            OrderField.META_KEY
        ].tolist()

        result = dict(
            (key, result[key]) for key in DFColumns.get_cols_to_scenario() if key in result
        )
        scenario = Scenario(result=result, context=self.context)
        self.scenarios.append(scenario)
