from enum import Enum
from typing import List


class ThresholdsNames(str, Enum):
    DISORDERLY_FILL_LOT_MINIMUM = "disorderlyFillLotMinimum"
    DISORDERLY_WINDOW = "disorderlyWindow"
    DISORDERLY_WINDOW_TYPE = "disorderlyWindowType"
    GROUP_BY = "groupBy"
    LIMIT_PRICE_VS_EXECUTION_PRICE_SPREAD = "limitPriceVsExecutionPriceSpread"
    MINIMUM_NEW_REPLACE_INSTRUCTIONS = "minimumNewReplaceInstructions"
    SPREAD_CONTRACTION = "spreadContraction"


class DFColumns:
    DISORDERLY_GROUPS = "disorderlyGroups"
    EARLIEST_INSTRUMENT_FULL_NAME = "earliestInstrumentFullName"
    EARLIEST_INSTRUMENT_ID = "earliestInstrumentID"
    EARLIEST_TIMESTAMP = "earliestTimestamp"
    FILL_QUANTITY = "fillQuantity"
    INSTRUMENT_ID = "instrumentID"
    MAX_WINDOW = "maxWindow"
    NUMBER_NEWOS = "numberNewos"
    NUMBER_REPLACEMENTS = "numberReplacements"
    ORDERS = "orders"
    ORDER_STATES = "orderStates"
    SPREAD_CONTRACTION = "spreadContraction"
    TIMEDELTA = "timedelta"
    TIMESTAMP_DIFFERENCE = "timestampDifference"
    TOTAL_ORDER_QUANTITY = "totalOrderQuantity"
    TRADER_FILE_IDENTIFIER_LIST = "traderFileIdentifierList"
    TRADER_LIST = "traderList"

    @classmethod
    def get_cols_to_scenario(cls) -> List[str]:
        return [
            cls.EARLIEST_INSTRUMENT_FULL_NAME,
            cls.EARLIEST_INSTRUMENT_ID,
            cls.EARLIEST_TIMESTAMP,
            cls.FILL_QUANTITY,
            cls.NUMBER_NEWOS,
            cls.NUMBER_REPLACEMENTS,
            cls.ORDERS,
            cls.ORDER_STATES,
            cls.SPREAD_CONTRACTION,
            cls.TIMESTAMP_DIFFERENCE,
            cls.TOTAL_ORDER_QUANTITY,
            cls.TRADER_FILE_IDENTIFIER_LIST,
            cls.TRADER_LIST,
        ]


class TraderEnum(str, Enum):
    TRADER = "Trader"
    TRADER_FILE_IDENTIFIER = "Trader Account"


class DisorderlyWindowTypeEnum(str, Enum):
    DYNAMIC = "Dynamic"
    STATIC = "Static"
