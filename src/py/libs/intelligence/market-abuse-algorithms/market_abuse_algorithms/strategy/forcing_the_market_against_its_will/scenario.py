import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.static import (
    DFColumns,
)


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders", "orderStates"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "orders": self._result.pop(DFColumns.ORDERS),
            "orderStates": self._result.pop(DFColumns.ORDER_STATES),
        }

        scenario = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(scenario)
        return scenario
