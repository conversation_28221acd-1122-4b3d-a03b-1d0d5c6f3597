from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.static import (
    DisorderlyWindowTypeEnum,
    TraderEnum,
)
from pydantic import Field, validator
from pydantic.dataclasses import dataclass
from typing import Optional


@dataclass
class DisorderlyWindow:
    unit: TimeUnit
    value: int = Field(..., ge=0, le=600)

    @validator("unit")
    def check_unit(cls, v):
        valid_units = [
            TimeUnit.MILLISECONDS,
            TimeUnit.SECONDS,
            TimeUnit.MINUTES,
        ]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_seconds = 600

        upper_bounds = {
            TimeUnit.MILLISECONDS: upper_bound_seconds * 1e3,
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class Thresholds(CommonThresholds):
    disorderlyFillLotMinimum: Optional[int] = Field(None, ge=0, le=100000)
    disorderlyWindow: DisorderlyWindow
    disorderlyWindowType: DisorderlyWindowTypeEnum
    groupBy: Optional[TraderEnum]
    limitPriceVsExecutionPriceSpread: float = Field(..., ge=0, le=50)
    minimumNewReplaceInstructions: int = Field(..., ge=0, le=100000)
    spreadContraction: float = Field(..., ge=0, le=50)
