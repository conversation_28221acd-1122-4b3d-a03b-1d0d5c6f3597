import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import process_numeric_columns, process_query_results
from typing import List


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

    def get_cases_to_analyse(self):
        """yields the result of the queries obtained in
        _get_cases_to_analyse_query."""
        query = self._get_cases_to_analyse_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instrument_combinations = self.get_instruments_combinations(query)

        for inst_comb in instrument_combinations:
            start = time.perf_counter()
            query = self._get_cases_to_analyse_query(inst_comb)
            query = self.get_initial_query(query)
            result = self._sdp_repository.search_after_query(query=query)

            parent_ids = result[OrderField.META_PARENT].unique().tolist()

            order_query = self._get_orders_to_analyse_query(
                parents_ids=parent_ids, inst_comb=inst_comb
            )
            order_result = self._sdp_repository.search_after_query(query=order_query)

            results = self.process_results(order_states=result, orders=order_result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield results

    @staticmethod
    def _get_cases_to_analyse_query(
        inst_comb: List[str] = None,
    ) -> OrderExecutionsQuery:
        """creates the query and the fields If minimum_notional != None:

        filter_by_minimum_notional (implemented in OrderBaseQuery)
        """
        q = OrderExecutionsQuery()

        q.add_condition(
            mode="filter",
            conditions=[
                {
                    "terms": {
                        OrderField.EXC_DTL_ORD_STATUS: [
                            OrderStatus.REME,
                            OrderStatus.REMA,
                            OrderStatus.REMH,
                            OrderStatus.FILL,
                            OrderStatus.PARF,
                        ]
                    }
                }
            ],
        )

        includes_fields = [
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.EXC_DTL_LIMIT_PRICE,
            OrderField.EXC_DTL_PASS_AGGR_IND,
            OrderField.EXC_DTL_ORD_STATUS,
            *OrderField.get_instrument_fields(),
            OrderField.INST_FULL_NAME,
            OrderField.META_KEY,
            OrderField.META_PARENT,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.PC_FD_INIT_QTY,
            OrderField.PC_FD_PRICE,
            OrderField.PC_FD_TRD_QTY,
            OrderField.TRADER_FILE_IDENTIFIER,
            OrderField.TRADER,
            OrderField.TS_ORD_UPDATED,
        ]

        required_fields = [
            OrderField.EXC_DTL_PASS_AGGR_IND,
            OrderField.META_KEY,
            OrderField.META_PARENT,
        ]

        for field in required_fields:
            q.exists(field=field)

        q.includes(includes_fields)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    @staticmethod
    def _get_orders_to_analyse_query(
        parents_ids: List[str], inst_comb: List[str] = None
    ) -> OrderQuery():
        """creates the query and the fields If minimum_notional != None:

        filter_by_minimum_notional (implemented in OrderBaseQuery)
        """
        q = OrderQuery()

        q.add_condition(
            mode="filter",
            conditions=[{"terms": {OrderField.META_ID: parents_ids}}],
        )

        includes_fields = [
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.EXC_DTL_LIMIT_PRICE,
            OrderField.EXC_DTL_ORD_STATUS,
            OrderField.EXC_DTL_PASS_AGGR_IND,
            *OrderField.get_instrument_fields(),
            OrderField.INST_FULL_NAME,
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.PC_FD_INIT_QTY,
            OrderField.PC_FD_PRICE,
            OrderField.PC_FD_TRD_QTY,
            OrderField.TRADER_FILE_IDENTIFIER,
            OrderField.TRADER,
            OrderField.TS_ORD_UPDATED,
        ]

        required_fields = [
            OrderField.META_ID,
            OrderField.META_KEY,
        ]

        for field in required_fields:
            q.exists(field=field)

        q.includes(includes_fields)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    @staticmethod
    def process_results(order_states: pd.DataFrame, orders: pd.DataFrame) -> pd.DataFrame:
        """process results, concat order_states & orders and convert float
        columns to float.

        :param order_states:
        :param orders:
        :return: pd.Dataframe processed
        """

        data: pd.DataFrame = pd.concat([order_states, orders], axis=0)

        results: pd.DataFrame = process_query_results(dataset=data)

        processed_results: pd.DataFrame = process_numeric_columns(data=results)

        return processed_results
