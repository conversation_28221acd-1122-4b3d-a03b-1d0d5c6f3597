from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.static import (
    CategoryEvaluationType,
    DayAndOrderEvaluationType,
    GeneralEvaluationType,
    MarketDataEvaluationType,
    NormaliseBehaviour,
    QuantityEvaluationType,
)
from pydantic import Field, root_validator, validator
from pydantic.dataclasses import dataclass
from typing import Optional


@dataclass
class TimeWindow:
    unit: TimeUnit = None
    value: int = Field(None, ge=0, le=2592000)

    @validator("unit")
    def check_unit(cls, v):
        valid_units = [
            TimeUnit.MILLISECONDS,
            TimeUnit.SECONDS,
            TimeUnit.MINUTES,
            TimeUnit.HOURS,
            TimeUnit.DAYS,
        ]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_seconds = 2592000

        upper_bounds = {
            TimeUnit.MILLISECONDS: upper_bound_seconds * 1e3,
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
            TimeUnit.HOURS: upper_bound_seconds / 3600,
            TimeUnit.DAYS: upper_bound_seconds / (1800 * 24),
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class Thresholds(CommonThresholds):
    categoryEvaluationType: CategoryEvaluationType
    dayAndOrderEvaluationType: Optional[DayAndOrderEvaluationType]
    executionNotionalValueCurrency: Optional[str] = Field(None, regex="^[A-Z]{3}$")
    generalEvaluationType: Optional[GeneralEvaluationType]
    lookBackPeriod: Optional[TimeWindow]
    marketDataEvaluationType: Optional[MarketDataEvaluationType]
    marketPriceImpact: Optional[float] = Field(None, ge=0, le=0.2)
    minimumQuantity: float = Field(None, ge=0, le=1000000)
    minNumberOfDaysOrderFlow: Optional[TimeWindow]
    normaliseBehaviour: Optional[NormaliseBehaviour]
    percentageAdv: float = Field(..., ge=0, le=10)
    quantityEvaluationType: QuantityEvaluationType

    @root_validator
    def check_thresholds_combination(cls, values):
        """Validate thresholds.

        See if we have options that are not possible to be selected
        together
        """
        if (
            values.get("dayAndOrderEvaluationType") == DayAndOrderEvaluationType.DAY
            and values.get("generalEvaluationType") is None
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When selecting the Day and Order to Evaluate by, "
                "must select a General Evaluation Type."
            )

        if (
            values.get("normaliseBehaviour")
            and values.get("categoryEvaluationType") == CategoryEvaluationType.MARKET
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When normalising by Asset Class or Instrument, can't select Market Data to Evaluate by."  # noqa: E501
            )

        if (
            values.get("minNumberOfDaysOrderFlow")
            and values.get("categoryEvaluationType") == CategoryEvaluationType.MARKET
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When using the minimum days of order flow, can't select Market Data to Evaluate by."  # noqa: E501
            )
        if (
            values.get("minNumberOfDaysOrderFlow")
            and values.get("lookBackPeriod")
            and values.get("minNumberOfDaysOrderFlow").value > values.get("lookBackPeriod").value
        ):
            raise ValueError(
                "Invalid thresholds. "
                "The minimum days of order flow should be less than the look back period."
            )

        if (
            values.get("quantityEvaluationType")
            and values.get("dayAndOrderEvaluationType")
            and values.get("quantityEvaluationType") == QuantityEvaluationType.ORDER_QUANTITY
            and values.get("dayAndOrderEvaluationType") == DayAndOrderEvaluationType.PARENT_ORDER
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When the Quantity Evaluation Type selected is Order Quantity, then the "
                "Day Order Evaluation Type can’t be by parentOrder."
            )

        if (
            values.get("quantityEvaluationType")
            and values.get("quantityEvaluationType") == QuantityEvaluationType.EXECUTED_NOTIONAL
            and values.get("categoryEvaluationType") == CategoryEvaluationType.MARKET
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When the Quantity Evaluation Type selected is Executed Notional, then the "
                "can't select Market Data to Evaluate by."
            )

        if (
            values.get("quantityEvaluationType")
            and values.get("quantityEvaluationType") == QuantityEvaluationType.EXECUTED_NOTIONAL
            and values.get("executionNotionalValueCurrency") is None
        ):
            raise ValueError(
                "Invalid thresholds. "
                "When Quantity Evaluation Type selected is Executed Notional, then the "
                "Execution Notional Value Currency should be sent."
            )
        return values
