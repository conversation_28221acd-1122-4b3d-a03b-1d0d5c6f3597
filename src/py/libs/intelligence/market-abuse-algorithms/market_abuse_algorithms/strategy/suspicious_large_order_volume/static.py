from enum import Enum
from typing import List


class ThresholdsNames(str, Enum):
    EVALUATION_TYPE = "evaluationType"
    LOOK_BACK_PERIOD = "lookBackPeriod"
    MINIMUM_ORDER_QUANTITY = "minimumOrderQuantity"
    SECURITY_ADV_LBP_DAY_EMA = "securityAdvLBPDayEma"


class EvaluationType(str, Enum):
    ALL = "All"
    COUNTERPARTY = "Counterparty"
    COUNTERPARTY_INSTRUMENT = "Counterparty by Instrument"
    FIRM = "Firm"
    INSTRUMENT = "Instrument"
    PERSON = "Person"
    PERSON_INSTRUMENT = "Person by Instrument"

    @classmethod
    def get_values(cls, remove_type_all: bool = False) -> List[str]:
        values = [v for k, v in vars(cls).items() if k.isupper()]

        if remove_type_all:
            values.remove(cls.ALL)

        return values


class DFColumns:
    DATE = "date"
    CLIENT_NAME = "clientName"
    COUNTERPARTY_NAME = "counterpartyName"
    EVALUATION_TYPE = "evaluationType"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID = "instrumentId"
    MARKET_ADV = "marketAdv"
    MARKET_TOTAL_VOLUME = "marketTotalVolume"
    ORDERS_KEYS = "ordersKeys"
    ORDER_QUANTITY = "orderQuantity"
    ORDER_QUANTITY_MARKET_ADV = "orderQuantityMarketAdv"
    ORDER_QUANTITY_TENANT_ADV = "orderQuantityTenantAdv"
    ORDER_SUBMITTED_TS = "orderSubmittedTS"
    TENANT_ADV = "tenantAdv"
    TENANT_TOTAL_VOLUME = "tenantTotalVolume"
    TIME_DIFFERENCE = "timeDifference"
    TRADER_NAME = "traderName"
    ULTIMATE_VENUE = "ultimateVenue"

    @classmethod
    def get_scenario_fields(cls):
        return [
            cls.CLIENT_NAME,
            cls.COUNTERPARTY_NAME,
            cls.EVALUATION_TYPE,
            cls.INSTRUMENT_FULL_NAME,
            cls.INSTRUMENT_ID,
            cls.MARKET_ADV,
            cls.MARKET_TOTAL_VOLUME,
            cls.ORDER_QUANTITY,
            cls.ORDER_QUANTITY_MARKET_ADV,
            cls.ORDER_QUANTITY_TENANT_ADV,
            cls.ORDER_SUBMITTED_TS,
            cls.TENANT_ADV,
            cls.TENANT_TOTAL_VOLUME,
            cls.TRADER_NAME,
            cls.ULTIMATE_VENUE,
        ]


class AggsType:
    BY_CLIENT = "by_client"
    BY_COUNTERPARTY = "by_counterparty"
    BY_FIRM = "by_firm"
    BY_INSTRUMENT = "by_instrument"
    BY_TRADER = "by_trader"
