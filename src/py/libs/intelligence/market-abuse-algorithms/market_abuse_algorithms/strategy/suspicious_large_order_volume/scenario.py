import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON><PERSON><PERSON><PERSON>, TradeColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["order"], multiple=[])

    def _build_scenario(self) -> pd.Series:
        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records.order = self._result["order"]
        d.additionalFields.topLevel = self._result["additionalFields"]

        return pd.Series(d.to_dict())
