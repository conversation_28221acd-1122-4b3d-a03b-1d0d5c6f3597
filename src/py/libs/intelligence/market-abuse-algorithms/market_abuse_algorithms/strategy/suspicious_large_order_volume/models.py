from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.suspicious_large_order_volume.static import (
    EvaluationType,
)
from pydantic import Field
from typing import Optional


class Thresholds(CommonThresholds):
    evaluationType: EvaluationType
    lookBackPeriod: int = Field(..., ge=1, le=60, description="In days")
    minimumOrderQuantity: Optional[int] = Field(None, ge=0, le=1000000)
    securityAdvLBPDayEma: float = Field(..., ge=0, le=10)
