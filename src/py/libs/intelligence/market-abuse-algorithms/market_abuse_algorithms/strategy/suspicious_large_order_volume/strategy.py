import copy
import pandas as pd
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.suspicious_large_order_volume.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume.query import Queries
from market_abuse_algorithms.strategy.suspicious_large_order_volume.scenario import (
    Scenario,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume.static import (
    AggsType,
    DFColumns,
    EvaluationType,
    ThresholdsNames,
)


class Strategy(AbstractStrategy):
    """Suspicious Large Order Volume.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-472
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.SUSPICIOUS_LARGE_ORDER_VOLUME,
            thresholds_class=Thresholds,
        )
        self._th_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )
        self._th_lbp = self.context.thresholds.dict().get(ThresholdsNames.LOOK_BACK_PERIOD)
        self._th_minimum_order_quantity = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_ORDER_QUANTITY
        )
        self._th_security_adv_lbp_day_ema = self.context.thresholds.dict().get(
            ThresholdsNames.SECURITY_ADV_LBP_DAY_EMA
        )

    def _apply_strategy(self):
        if self._th_evaluation_type == EvaluationType.ALL:
            for eval_type in EvaluationType.get_values(remove_type_all=True):
                self._evaluate_type(evaluation_type=eval_type)

        else:
            self._evaluate_type(evaluation_type=self._th_evaluation_type)

    def _evaluate_type(self, evaluation_type: str):
        params = dict(
            evaluation_type=evaluation_type,
            sec_adv_lbp_day_ema=self._th_security_adv_lbp_day_ema,
            lbp=self._th_lbp,
        )

        # Implies algo runs > 1x
        # Person
        if evaluation_type == EvaluationType.PERSON:
            # Client
            params_client = copy.deepcopy(params)
            params_client[AggsType.BY_CLIENT] = True
            self._run_algo(params=params_client)

            # Trader
            params_trader = copy.deepcopy(params)
            params_trader[AggsType.BY_TRADER] = True
            self._run_algo(params=params_trader)
            return

        # Person by Instrument
        elif evaluation_type == EvaluationType.PERSON_INSTRUMENT:
            # Client
            params_client = copy.deepcopy(params)
            params_client[AggsType.BY_CLIENT] = True
            params_client[AggsType.BY_INSTRUMENT] = True
            self._run_algo(params=params_client)

            # Trader
            params_trader = copy.deepcopy(params)
            params_trader[AggsType.BY_TRADER] = True
            params_trader[AggsType.BY_INSTRUMENT] = True
            self._run_algo(params=params_trader)
            return

        # Algo runs just 1x
        # Firm
        if evaluation_type == EvaluationType.FIRM:
            params[AggsType.BY_FIRM] = True

        # Counterparty
        elif evaluation_type == EvaluationType.COUNTERPARTY:
            params[AggsType.BY_COUNTERPARTY] = True

        # Counterparty by Instrument
        elif evaluation_type == EvaluationType.COUNTERPARTY_INSTRUMENT:
            params[AggsType.BY_COUNTERPARTY] = True
            params[AggsType.BY_INSTRUMENT] = True

        # Instrument
        elif evaluation_type == EvaluationType.INSTRUMENT:
            params[AggsType.BY_INSTRUMENT] = True

        self._run_algo(params=params)

    def _run_algo(self, params: dict):
        activities = self._analyse_activities(params)

        if activities.empty:
            return

        result = self._analyse_results(df=activities)

        if result.empty:
            return

        self._create_scenarios(result)

    def _analyse_activities(self, params: dict) -> pd.DataFrame:
        """

        :param params:
        :return:
        """
        self._queries = Queries(context=self.context, audit=self.audit, **params)

        tenant_activity = self.queries.tenant_activity()

        if tenant_activity.empty:
            return pd.DataFrame()

        tenant_activity[DFColumns.ORDER_QUANTITY_TENANT_ADV] = (
            tenant_activity[OrderField.PC_FD_INIT_QTY] / tenant_activity[DFColumns.TENANT_ADV]
        )

        if params.get(AggsType.BY_INSTRUMENT):
            tenant_activity = self._get_market_data_into_tenant_activity(df=tenant_activity)

        return tenant_activity

    def _get_market_data_into_tenant_activity(self, df: pd.DataFrame) -> pd.DataFrame:
        instrument_unique_identifiers = (
            df[OrderField.INST_EXT_UNIQUE_IDENT].dropna().unique().tolist()
        )

        if not instrument_unique_identifiers:
            return df

        market_data = self.queries.get_market_data(
            instrument_unique_identifiers=instrument_unique_identifiers
        )

        if market_data.empty:
            return df

        # Merge market data into tenant activity
        date_fmt = "%Y-%m-%d"
        md_ident = "md_ident"

        df.loc[:, md_ident] = (
            df[DFColumns.DATE].dt.strftime(date_fmt) + df[OrderField.INST_EXT_UNIQUE_IDENT]
        )

        market_data[md_ident] = (
            market_data[TradeStatsColumns.DATE].dt.strftime(date_fmt)
            + market_data[OrderField.INST_EXT_UNIQUE_IDENT]
        )

        df = df.merge(market_data, how="left", on=md_ident)

        df[DFColumns.ORDER_QUANTITY_MARKET_ADV] = (
            df[OrderField.PC_FD_INIT_QTY] / df[DFColumns.MARKET_ADV]
        )

        return df

    def _analyse_results(self, df: pd.DataFrame) -> pd.DataFrame:
        tenant_adv_mask = pd.Series(False, index=df.index)
        market_adv_mask = pd.Series(False, index=df.index)

        if DFColumns.ORDER_QUANTITY_TENANT_ADV in df.columns:
            tenant_adv_mask = (
                df[DFColumns.ORDER_QUANTITY_TENANT_ADV] > self._th_security_adv_lbp_day_ema
            )

        if DFColumns.ORDER_QUANTITY_MARKET_ADV in df.columns:
            market_adv_mask = (
                df[DFColumns.ORDER_QUANTITY_MARKET_ADV] > self._th_security_adv_lbp_day_ema
            )

        mask = tenant_adv_mask | market_adv_mask

        df = df.loc[mask]

        return df

    def _create_scenarios(self, result: pd.DataFrame):
        # Add scenario fields
        scenario_fields = self.queries.get_additional_fields_result(
            result[OrderField.META_KEY].unique().tolist()
        )

        result = result.set_index(OrderField.META_KEY)
        scenario_fields = scenario_fields.set_index(OrderField.META_KEY)

        result[scenario_fields.columns.tolist()] = scenario_fields[scenario_fields.columns.tolist()]  # noqa: E501

        fields_map = {
            f"{OrderField.PC_FD_INIT_QTY}": DFColumns.ORDER_QUANTITY,
            f"{OrderField.TRX_DTL_ULTIMATE_VENUE}": DFColumns.ULTIMATE_VENUE,
            NewColumns.INSTRUMENT_CODE: DFColumns.INSTRUMENT_ID,
            OrderField.INST_FULL_NAME: DFColumns.INSTRUMENT_FULL_NAME,
            OrderField.CLIENT_IDENT_CLIENT_NAME: DFColumns.CLIENT_NAME,
            OrderField.COUNTERPARTY_NAME: DFColumns.COUNTERPARTY_NAME,
            NewColumns.TRADER_NAME: DFColumns.TRADER_NAME,
            OrderField.TS_ORD_SUBMITTED: DFColumns.ORDER_SUBMITTED_TS,
            TradeStatsColumns.TRADE_VOLUME: DFColumns.MARKET_TOTAL_VOLUME,
        }

        result = result.rename(columns={k: v for k, v in fields_map.items() if k in result.columns})  # noqa: E501

        result = result.loc[:, result.columns.isin(DFColumns.get_scenario_fields())]

        if self._th_minimum_order_quantity is not None:
            quantity_mask = result[DFColumns.ORDER_QUANTITY] > self._th_minimum_order_quantity
            result = result.loc[quantity_mask]

        result_scenarios = result.to_dict(orient="index")

        for k, v in result_scenarios.items():
            scenario = Scenario(
                result={"order": k, "additionalFields": v},
                context=self.context,
            )
            self.scenarios.append(scenario)
