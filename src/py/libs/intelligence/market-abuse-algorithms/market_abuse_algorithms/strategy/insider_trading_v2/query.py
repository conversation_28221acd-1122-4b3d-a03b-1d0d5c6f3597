# ruff: noqa: E501
import datetime
import pandas as pd
import re
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import (
    OrderAggs,
    OrderExecutionsQuery,
    OrderQuery,
    OrderStatus,
)
from market_abuse_algorithms.data_source.repository.market_data.client import get_market_client
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.strategy.insider_trading_v2.static import (
    EVENTS_DATA_MAP,
    DFColumns,
    EventData,
    OrderEvents,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import process_numeric_columns
from market_abuse_algorithms.utils.formulas import calculate_average_execution_price
from market_abuse_algorithms.utils.processment import (
    new_instrument_combination_with_rics,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from typing import Dict, Generator, List, Optional, Tuple, Union


class Queries(BaseQuery):
    TRADER_ORDERS_PAST_DAYS = 90
    COMMS_DAYS_OFFSET = 7

    # Number of days to calculate volatility
    VOLATILITY_DAYS = 10
    MIN_DAYS_FOR_VOLATILITY = 6

    COMMON_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.INST_EXT_UNIQUE_IDENT,
        OrderField.ORD_IDENT_ID_CODE,
    ]

    REQUIRED_FIELDS = [
        *COMMON_FIELDS,
        OrderField.get_instrument_fields(),
        OrderField.get_venue_fields(),
        OrderField.TRX_DTL_PC_CCY,
    ]

    INCLUDE_FIELDS = [
        *COMMON_FIELDS,
        *OrderField.get_instrument_fields(),
        *OrderField.get_venue_fields(),
        *OrderField.get_client_fields(),
        *OrderField.get_involved_parties_fields(),
        OrderField.CLIENT_IDENT_CLIENT,
        OrderField.COUNTERPARTY_NAME,
        OrderField.INST_CLASSIFICATION,
        OrderField.INST_DERIV_OPTION_TYPE,
        OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
        OrderField.INST_EXT_UNDER_INST_INST_CODE,
        OrderField.INST_FULL_NAME,
        OrderField.TRX_DTL_PC_CCY,
        OrderField.TRADER,
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)
        self._th_min_notional_value = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_VALUE
        )
        self._th_min_notional_val_ccy = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_VALUE_CURRENCY
        )

        if self.context.thresholds.dict().get(ThresholdsNames.ORDER_EVENTS):
            self._th_order_events = self.context.thresholds.dict().get(ThresholdsNames.ORDER_EVENTS)
        else:
            self._th_order_events = OrderEvents.EXECUTION_LEVEL

        self._market_data_client = get_market_client(tenant=context.tenant)

    def get_data_to_analyse(self) -> Generator[pd.DataFrame, None, None]:
        query = self._get_data()

        instrument_combinations = self.get_instruments_combinations(
            query=query,
            agg_script=QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV,
        )

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = self._market_data_client.get_ric_map(
            instrument_combinations=instrument_combinations
        )

        new_instrument_combinations_with_ric: List = new_instrument_combination_with_rics(
            instrument_combinations=instrument_combinations,
            instrument_ric_mapping=instrument_id_ric_mapping,
        )

        for instruments_and_rics in new_instrument_combinations_with_ric:
            start, method_id = self.get_start_time_and_unique_id()

            inst_comb = [inst for inst, ric in instruments_and_rics]

            start_timer = time.perf_counter()
            orders_ids = self._get_orders_ids_to_analyse(inst_comb=inst_comb)

            if not orders_ids:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"No orders/order states for this list of instruments/rics {instruments_and_rics}.",
                        list_of_instruments=inst_comb,
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=0,
                        number_of_input_orders=0,
                        number_of_resulting_orders=0,
                        step_name=f"Fetching orders for this list of instruments/rics {instruments_and_rics}.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                    )
                )

                continue

            query = self._get_data(orders_ids=orders_ids)

            result = self._sdp_repository.search_after_query(query)

            result = self._process_data_to_analyse(orders_ids=orders_ids, df=result)

            result[DFColumns.RIC] = result[OrderField.INST_EXT_UNIQUE_IDENT].map(
                dict(instruments_and_rics)
            )

            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, "
                f"it took {time.perf_counter() - start_timer} seconds"
            )

            yield result

    def _get_data(
        self,
        inst_comb: Optional[List[str]] = None,
        orders_ids: Optional[List[str]] = None,
    ) -> Union[OrderExecutionsQuery, OrderQuery]:
        if self._th_order_events == OrderEvents.ORDER_LEVEL:
            return self._get_order_query(inst_comb=inst_comb, orders_ids=orders_ids)
        else:
            return self._get_order_executions_query(inst_comb=inst_comb, orders_ids=orders_ids)

    def _get_order_query(
        self,
        inst_comb: Optional[List[str]] = None,
        orders_ids: Optional[List[str]] = None,
    ) -> OrderQuery:
        required_fields = [
            *self.REQUIRED_FIELDS,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.PC_FD_INIT_QTY,
        ]

        include_fields = [
            *self.INCLUDE_FIELDS,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.PC_FD_INIT_QTY,
        ]

        query = OrderQuery()

        query.includes(include_fields)
        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=required_fields)

        if inst_comb:
            query.instrument_id(inst_comb)

        if orders_ids:
            query.order_id(orders_ids)

        return query

    def _get_order_executions_query(
        self,
        inst_comb: Optional[List[str]] = None,
        orders_ids: Optional[List[str]] = None,
    ) -> OrderExecutionsQuery:
        specific_fields = [
            OrderField.PC_FD_PRICE,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.PC_FD_TRD_QTY,
            OrderField.META_PARENT,
        ]

        required_fields = [*self.REQUIRED_FIELDS, OrderField.TS_TRADING_DATE_TIME]
        include_fields = [*self.INCLUDE_FIELDS, *specific_fields]

        query = OrderExecutionsQuery()
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.includes(include_fields)
        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=required_fields)

        if inst_comb:
            query.instrument_id(inst_comb)

        if orders_ids:
            query.order_id(orders_ids)

        return query

    def _get_orders_ids_to_analyse(self, inst_comb: Optional[List[str]] = None) -> List[str]:
        """Return orders ids list to analyse based on minimum notional
        threshold.

        :param inst_comb:
        :return:
        """
        query = self._get_data(inst_comb=inst_comb)
        query.size(0)

        ccy_field = EVENTS_DATA_MAP.get(self._th_order_events).get(EventData.EVENT_NOTIONAL)(
            self._th_min_notional_val_ccy
        )

        q_aggs = query.aggs
        q_aggs = OrderAggs.aggs_order_id(q_aggs)
        q_aggs.metric("notionalValueSum", "sum", field=ccy_field)
        q_aggs.metric(
            "minimumNotional",
            "bucket_selector",
            buckets_path={"x": "notionalValueSum"},
            script=f"params.x >= {self._th_min_notional_value}",
        )

        result = self._sdp_repository.search_query(query)

        buckets = result["aggregations"][OrderAggs.BY_ORDER_ID]["buckets"]

        if not buckets:
            return []

        orders_ids = [bucket["key"] for bucket in buckets]

        return orders_ids

    def _process_data_to_analyse(self, orders_ids: List[str], df: pd.DataFrame) -> pd.DataFrame:
        """
        TODO: probably we can remove this when swarm is fixed to pass numeric fields as numeric and not strings.
        Add the average execution price to the dataframe when the orderEvents is OrderLevel
        & Set numeric columns to float
        & Remove rows where the price, tradedQuantity and initialQuantity is NaN
        :param orders_ids: list[str]. list of order ids to get the child executions
        :param df: pd.DataFrame. newos data to process
        :return: dataframe processed with floats and average execution price calculated
        """

        if self._th_order_events == OrderEvents.ORDER_LEVEL:
            merged_data = self._process_order_executions(order_ids=orders_ids, dataframe=df)

            df = calculate_average_execution_price(data=merged_data)

        df: pd.DataFrame = process_numeric_columns(data=df)

        return df

    def _process_order_executions(
        self, order_ids: List[str], dataframe: pd.DataFrame
    ) -> pd.DataFrame:
        """Join executions and newos data into one dataframe.

        :param dataframe: pd.DataFrame. Newos data
        :param order_ids: list of newos ids
        :return: pd.DataFrame merged with newos and respective executions
        """
        executions_query = self._get_order_executions_query(orders_ids=order_ids)
        executions = self._sdp_repository.search_after_query(executions_query)

        merged_data = pd.merge(
            dataframe, executions, on=OrderField.ORD_IDENT_ID_CODE, suffixes=("", "_y")
        )

        merged_data.drop(merged_data.filter(regex="_y$").columns.tolist(), axis=1, inplace=True)
        return merged_data

    def get_market_data(
        self,
        data: pd.DataFrame,
        options: bool = False,
    ) -> pd.DataFrame:
        """Method for fetching market data stats including volatility using
        instrument unique identifier and currency.

        :param options: bool
        :param data: contains multiple columns with different types of data
        :return: dataframe with market data stats including volatility
        """
        # No Nans because are mandatory fields

        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            self._logger.info("Dataframe is empty. Can't get market data.")
            return pd.DataFrame()

        ric = data.loc[:, DFColumns.RIC].dropna().unique().tolist()
        ric: str = ric[0] if ric else None

        if options:
            inst_unique_id = data.loc[:, NewColumns.UNDERLYING_INSTRUMENT_CODE].unique()[0]
            currency = None

        else:
            inst_unique_id = data.loc[:, OrderField.INST_EXT_UNIQUE_IDENT].unique()[0]
            currency = data.loc[:, OrderField.TRX_DTL_PC_CCY].unique()[0]

        data = self._market_data_client.get_market_data_volatility(
            instrument_ric=ric,
            instrument_unique_identifier=inst_unique_id,
            volatility_col_name=DFColumns.VOLATILITY,
            currency=currency,
            min_periods=self.MIN_DAYS_FOR_VOLATILITY,
            volatility_window=self.VOLATILITY_DAYS,
            start_date=self.look_back_period_ts,
            end_date=self.market_data_end_date,
        )
        if data.empty:
            return pd.DataFrame()
        return data

    def get_additional_field_query(
        self, record_keys: List[str]
    ) -> Union[OrderQuery, OrderExecutionsQuery]:
        """Get the query to fetch additional fields to be added the result
        table.

        :param record_keys: list of record keys
        :return:
        """
        if self._th_order_events == OrderEvents.ORDER_LEVEL:
            query = OrderQuery()
        else:
            query = OrderExecutionsQuery()
            query.order_status([OrderStatus.FILL, OrderStatus.PARF])

        query.size(OrderExecutionsQuery.SIZE)
        query.key(record_keys)
        query.includes(
            [
                OrderField.META_KEY,
                OrderField.EXC_DTL_BUY_SELL_IND,
                OrderField.INST_FULL_NAME,
                OrderField.CLIENT_IDENT_CLIENT,
                OrderField.COUNTERPARTY_NAME,
                *OrderField.get_client_fields(),
                *OrderField.get_involved_parties_fields(),
            ]
        )

        return query

    def get_additional_field_for_results(self, keys: List[str]) -> pd.DataFrame:
        """Get the additional fields to be added to the results table.

        :param keys: list of record keys
        :return:
        """
        query_to_be_executed: Union[OrderQuery, OrderExecutionsQuery] = (
            self.get_additional_field_query(record_keys=keys)
        )

        result: pd.DataFrame = self._sdp_repository.search_after_query(query=query_to_be_executed)

        if result.empty:
            return pd.DataFrame()

        return result

    def get_orders_keys(self, orders_ids: List[str]) -> Dict[str, str]:
        # TODO: search query with aggs?
        query = OrderQuery()
        query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])
        query.size(OrderQuery.SIZE)
        query.order_id(orders_ids)

        result = self._sdp_repository.search_after_query(query=query)

        if result.empty:
            return {}

        result = dict(zip(result[OrderField.ORD_IDENT_ID_CODE], result[OrderField.META_KEY]))

        return result

    def process_meta_id(self, parent_keys: List[str]) -> List[str]:
        """For the Kytebroking tenant we must check the order of parent_id, to
        put it with the order that is being ingested with the new swarm.

        NOTE: this will be removed as soon as the old swarm is deprecated and kytebroking ingestions change to new swarm
        :param parent_keys: list of parent meta_ids that are on the order states
        :return: List[str]. List of parent keys with the new meta_ids
        """

        if self._sdp_repository._tenant == "kytebroking":
            new_parent_keys = []
            for parent in parent_keys:
                old_list = parent.split(":")
                code = old_list[2]
                if re.search(r"^[-+]?[0-9]+$", code):
                    buy_sell = old_list[0]
                    order_status = old_list[1]
                    parent = f"{code}:{buy_sell}:{order_status}"
                new_parent_keys.append(parent)
            return new_parent_keys
        return parent_keys

    def fetch_nearest_timestamp_price(self, data: pd.DataFrame) -> pd.DataFrame:
        """Fetches the nearest timestamp with market prices.

        :param data: dataframe with normal timestamp
        :return: dataframe with nearest timestamp
        """
        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            self._logger.info("Dataframe is empty. Can't fetch nearest timestamp price.")
            return pd.DataFrame()

        event_date = EVENTS_DATA_MAP.get(self._th_order_events).get(EventData.EVENT_DATE)

        instrument_id = data.loc[:, NewColumns.UNDERLYING_INSTRUMENT_CODE].values[0]
        ric = data.loc[:, DFColumns.RIC].dropna().unique().tolist()
        ric = ric[0] if ric else None
        records_dates = data.loc[:, event_date]
        dates = records_dates.dt.date.unique().tolist()  # TODO: computational expensive? verify

        try:
            start, method_id = self.get_start_time_and_unique_id()
            market_data: pd.DataFrame = self.get_tick_trades(ric=ric, timestamps=records_dates)

            if market_data.empty:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"No market tick data for instrument {instrument_id} on the {dates}.",
                        list_of_order_ids=[],
                        list_of_instruments=[str(instrument_id)],
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=0,
                        number_of_input_orders=0,
                        number_of_resulting_orders=0,
                        step_name="Get Market Tick Data.",
                        step_type=StepType.MARKET_DATA_TICKS,
                        drop_reason=DropReason.RECORDS_DROPPED_TICK_DATA,
                    )
                )

                self._logger.warning(
                    f"No market tick data for trades for instrument {ric}, "
                    f"going to fallback for quotes",
                    exc_info=True,
                )

                start, method_id = self.get_start_time_and_unique_id()
                market_data: pd.DataFrame = self.get_tick_quotes(ric=ric, timestamps=records_dates)

                if market_data.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            reason=f"No market quote data for instrument {instrument_id} on the {dates}.",
                            list_of_order_ids=[],
                            list_of_instruments=[str(instrument_id)],
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Get Market Quote Data.",
                            step_type=StepType.MARKET_DATA,
                            drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                        )
                    )

                    self._logger.warning(
                        f"No market tick data for quotes for instrument {instrument_id}",
                        exc_info=True,
                    )

                    return data

            market_data = market_data.drop_duplicates()

            price_column = (
                RefinitivExtractColumns.PRICE
                if RefinitivExtractColumns.PRICE in market_data.columns
                else RefinitivExtractColumns.MID_PRICE
            )

            if price_column not in market_data.columns:
                self._logger.warning(
                    f"No price column to merge data. Instrument {instrument_id}",
                    exc_info=True,
                )
                return data

            data, market_data = convert_date_columns(
                tick_data=market_data, tenant_data=data, date_col=event_date
            )

            df = data.merge(
                market_data.loc[
                    :,
                    [
                        price_column,
                        event_date,
                    ],
                ],
                how="left",
                on=event_date,
            )

            df = df.rename(columns={price_column: DFColumns.MPU})

            return df

        except Exception as e:
            self._logger.error(
                f"Cannot fetch nearest tick data for instrument {instrument_id}.Error: {str(e)}.",
                exc_info=True,
            )
            return data

    def get_tick_trades(self, ric: str, timestamps: pd.Series):
        """Get the trades tick files.

        :param ric: RIC to fetch trade tick data
        :param timestamps: pd.Series with the dates of the records
        :return:
        """
        return self._market_data_client.get_tick_data(
            instrument_ric=ric,
            dates=timestamps,
            event_type=RefinitivEventType.TRADE,
            nearest_timestamp=True,
        )

    def get_tick_quotes(self, ric: str, timestamps: pd.Series):
        """Get the quotes tick files.

        :param ric: RIC to fetch quote tick data
        :param timestamps: pd.Series with the dates of the records
        :return:
        """
        return self._market_data_client.get_tick_data(
            instrument_ric=ric,
            dates=timestamps,
            event_type=RefinitivEventType.QUOTE,
            nearest_timestamp=True,
        )


def convert_date_columns(
    tick_data: pd.DataFrame, tenant_data: pd.DataFrame, date_col: str
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    tenant_data[date_col] = pd.to_datetime(tenant_data[date_col])
    tick_data[date_col] = pd.to_datetime(tick_data[date_col])
    return tenant_data, tick_data
