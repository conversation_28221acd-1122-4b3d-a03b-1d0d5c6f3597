from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.insider_trading_v2.static import (
    OrderEvents,
    PriceCheckNatureOfCheck,
    PriceCheckStrategy,
)
from pydantic import BaseModel, Field, root_validator
from typing import List, Optional


class PriceChecks(BaseModel):
    daysFrom: int = Field(..., ge=0, le=10)
    daysTo: int = Field(..., ge=0, le=10)
    index: int = Field(..., ge=0)
    natureOfCheck: PriceCheckNatureOfCheck
    strategy: PriceCheckStrategy
    threshold: float

    @root_validator
    def validate_threshold_value_range(cls, values):
        nature_check = values.get("natureOfCheck")
        threshold = values.get("threshold")
        if nature_check == PriceCheckNatureOfCheck.PERCENTAGE_PRICE_MOVEMENT:
            if threshold < 0 or threshold > 2:
                raise ValueError("Threshold value must be a float between 0 and 2")
        else:
            if threshold < 1.5 or threshold > 15:
                raise ValueError("Threshold value must be a float between 1.5 and 15")
        return values


class Thresholds(CommonThresholds):
    enableMinimumNotionalValue: Optional[bool] = Field(True)
    minimumNotionalValue: int = Field(..., ge=0, le=1000000)
    minimumNotionalValueCurrency: str = Field(..., regex="^[A-Z]{3}$")
    priceChecks: List[PriceChecks]
    orderEvents: Optional[OrderEvents]
