import addict
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.insider_trading_v2.static import DFColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["order"], multiple=["executions"])

    def _build_scenario(self) -> pd.Series:
        records = addict.Dict()

        result = self._result.iloc[0]

        records.order = result.get(DFColumns.ORDER_KEY)
        records.executions = sorted(self._result[OrderField.META_KEY].unique().tolist())

        top_level = addict.Dict()
        top_level["involvedParties"] = result.get(DFColumns.INVOLVED_PARTIES)

        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records = records
        d.additionalFields.topLevel = top_level

        # Executions additional fields
        exec_cols = [
            DFColumns.CLOSE_PRICE,
            DFColumns.CLOSE_TIMESTAMP,
            DFColumns.LOWER_BOUND,
            DFColumns.ORDER_KEY,
            DFColumns.PRICE_MOVEMENT,
            DFColumns.RULE_INDEX,
            DFColumns.STD,
            DFColumns.STRATEGY,
            DFColumns.UPPER_BOUND,
            DFColumns.TRADE_DATE,
            DFColumns.TRIGGER_DATE,
            DFColumns.VOLATILITY,
            OrderField.INST_EXT_UNIQUE_IDENT,
        ]

        execs_additional_fields = {}

        for exec_key, group in self._result.groupby(OrderField.META_KEY):
            execs_additional_fields[exec_key] = (
                group.loc[:, group.columns.isin(exec_cols)]
                .sort_values(by=DFColumns.RULE_INDEX)
                .to_dict(orient="records")
            )

        d.additionalFields.executions = execs_additional_fields

        result = pd.Series(d.to_dict())

        return result
