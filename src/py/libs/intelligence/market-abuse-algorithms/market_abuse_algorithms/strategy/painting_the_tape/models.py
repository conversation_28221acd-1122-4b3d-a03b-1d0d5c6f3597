from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.painting_the_tape.static import EvaluationType
from pydantic import Field
from typing import Optional


class Thresholds(CommonThresholds):
    adv30DayEma: float = Field(None, ge=0, le=1)
    evaluationType: EvaluationType
    lookBackPeriod: int = Field(..., ge=0, le=86400, description="In seconds")
    marketAdv30DayEma: Optional[float] = Field(None, ge=0, le=1)
    priceDifference: float = Field(..., ge=0, le=1)
    volumeDifference: float = Field(..., ge=0, le=1)
