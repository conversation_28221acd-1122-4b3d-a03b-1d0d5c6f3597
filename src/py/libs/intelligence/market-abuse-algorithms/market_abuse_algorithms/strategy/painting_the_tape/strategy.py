import copy
import numpy as np
import pandas as pd
from market_abuse_algorithms.data_source.query.sdp.order import OrderAggs
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import <PERSON><PERSON><PERSON>, NewColumns, OrderField
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.painting_the_tape.models import Thresholds
from market_abuse_algorithms.strategy.painting_the_tape.query import Queries
from market_abuse_algorithms.strategy.painting_the_tape.scenario import Scenario
from market_abuse_algorithms.strategy.painting_the_tape.static import (
    DFColumns,
    EvaluationType,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.formulas import calculate_percentage_difference
from typing import Optional


class Strategy(AbstractStrategy):
    """Painting The Tape.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-473
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.PAINTING_THE_TAPE,
            thresholds_class=Thresholds,
        )

        self._th_adv_30_day_ema = self.context.thresholds.dict().get(ThresholdsNames.ADV_30_DAY_EMA)  # noqa: E501
        self._th_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )
        self._th_lbp = self.context.thresholds.dict().get(ThresholdsNames.LOOK_BACK_PERIOD)
        self._th_market_adv_30_day_ema = self.context.thresholds.dict().get(
            ThresholdsNames.MARKET_ADV_30_DAY_EMA
        )
        self._th_price_difference = self.context.thresholds.dict().get(
            ThresholdsNames.PRICE_DIFFERENCE
        )
        self._th_volume_difference = self.context.thresholds.dict().get(
            ThresholdsNames.VOLUME_DIFFERENCE
        )

    def _apply_strategy(self):
        if self._th_evaluation_type not in EvaluationType:
            raise Exception("Invalid Evaluation Type")

        if self._th_evaluation_type == EvaluationType.ALL:
            for eval_type in EvaluationType.get_values(remove_type_all=True):
                self._evaluate_type(evaluation_type=eval_type)

        else:
            self._evaluate_type(evaluation_type=self._th_evaluation_type)

    def _evaluate_type(self, evaluation_type: str):
        params = dict(
            evaluation_type=self._th_evaluation_type,
            market_adv_30_day_ema=self._th_market_adv_30_day_ema,
        )

        aggs_analysis = None

        # Implies run_algo > 1x
        # Person
        if evaluation_type == EvaluationType.PERSON:
            # Client
            params_client = copy.deepcopy(params)
            params_client[OrderAggs.BY_CLIENT] = True

            self._run_algo(params=params_client, aggs_analysis=OrderField.CLIENT_FILE_IDENTIFIER)

            # Trader
            params_trader = copy.deepcopy(params)
            params_trader[OrderAggs.BY_TRADER] = True
            self._run_algo(params=params_trader, aggs_analysis=OrderField.TRADER_FILE_IDENTIFIER)
            return

        # Runs run_algo just 1x
        # Firm
        if evaluation_type == EvaluationType.FIRM:
            params[OrderAggs.BY_FIRM] = True

        # Counterparty
        elif evaluation_type == EvaluationType.COUNTERPARTY:
            params[OrderAggs.BY_COUNTERPARTY] = True
            aggs_analysis = OrderField.COUNTERPARTY_ID

        self._run_algo(params=params, aggs_analysis=aggs_analysis)

    def _run_algo(self, params: dict, aggs_analysis: Optional[str] = None):
        self._queries = Queries(context=self.context, audit=self.audit, **params)

        result = self.queries.get_data_to_analyse()

        if result.empty:
            return

        # Set TS_TRADING_DATE_TIME as index and sort it to be able to apply rolling window
        result = result.sort_values(by=OrderField.TS_TRADING_DATE_TIME)

        results = []

        for inst_unique_ident, inst_group in result.groupby(OrderField.INST_EXT_UNIQUE_IDENT):
            if not aggs_analysis:
                group_result = self._do_analysis(inst_group)
                if not group_result.empty:
                    results.append(group_result)

            else:
                for _, group in inst_group.groupby(aggs_analysis):
                    group_result = self._do_analysis(group)
                    if not group_result.empty:
                        results.append(group_result)

        if not results:
            return

        df = pd.concat(results, ignore_index=True)

        self._create_scenarios(df)

    def _do_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        df_original = df[[OrderField.META_KEY, OrderField.TS_TRADING_DATE_TIME]].copy()

        df = self._add_volume_related_columns(df=df)

        if df.empty:
            return df

        # TODO: Zero is a hack to have this threshold as optional
        if self._th_volume_difference:
            df = self._check_volume_difference(df)

            if df.empty:
                return df

        # TODO: Zero is a hack to have this threshold as optional
        if self._th_adv_30_day_ema:
            df = self._check_tenant_adv(df)

            if df.empty:
                return df

        # TODO: Zero is a hack to have this threshold as optional
        if self._th_market_adv_30_day_ema:
            df = self._check_market_adv(df)

            if df.empty:
                return df

        # TODO: Zero is a hack to have this threshold as optional
        if self._th_price_difference:
            df = self._check_price_difference(df)

            if df.empty:
                return df

        df = self._add_timestamps(df=df)

        if self._th_market_adv_30_day_ema:
            df = self._add_market_impact(df)

        # Execution keys
        df[DFColumns.EXECUTIONS] = df[[DFColumns.FIRST_TRADE_TS, DFColumns.LAST_TRADE_TS]].apply(
            lambda x: df_original.loc[
                (df_original[OrderField.TS_TRADING_DATE_TIME] >= x[DFColumns.FIRST_TRADE_TS])
                & (df_original[OrderField.TS_TRADING_DATE_TIME] <= x[DFColumns.LAST_TRADE_TS]),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        df: pd.DataFrame = self._filter_alert_candidates_with_one_sided_buysell_ind(df=df)
        return df

    @staticmethod
    def _filter_alert_candidates_with_one_sided_buysell_ind(
        df: pd.DataFrame,
    ) -> pd.DataFrame:
        """Verifies that each scenario candidate has both buys and sells.

        :param df: pandas dataframe with scenario candidate in each row
        :return: Filtered dataframe with scenario candidates
        """

        columns_to_check = [DFColumns.EXECUTIONS, OrderField.EXC_DTL_BUY_SELL_IND]

        if not set(columns_to_check).issubset(df.columns):
            return df

        df[DFColumns.BOTH_SIDES] = df.loc[
            :,
            [
                DFColumns.EXECUTIONS,
                OrderField.EXC_DTL_BUY_SELL_IND,
                OrderField.META_KEY,
            ],
        ].apply(
            lambda x: df.loc[
                df.loc[:, OrderField.META_KEY].isin(x[DFColumns.EXECUTIONS]),
                OrderField.EXC_DTL_BUY_SELL_IND,
            ].nunique()
            > 1,
            axis=1,
        )

        df_filtered = df.loc[df.loc[:, DFColumns.BOTH_SIDES]]

        return df_filtered

    def _add_volume_related_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        buys_mask = df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY
        sells_mask = df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL

        # Volume checking
        df.loc[buys_mask, DFColumns.BUYS_VOLUME] = df.loc[buys_mask, OrderField.PC_FD_TRD_QTY]
        df.loc[sells_mask, DFColumns.SELLS_VOLUME] = df.loc[sells_mask, OrderField.PC_FD_TRD_QTY]

        df.loc[:, DFColumns.BUYS_TOTAL_VOLUME] = self._rolling(
            df, column=DFColumns.BUYS_VOLUME, mode="sum"
        )
        df.loc[:, DFColumns.SELLS_TOTAL_VOLUME] = self._rolling(
            df, column=DFColumns.SELLS_VOLUME, mode="sum"
        )

        return df

    def _check_volume_difference(self, df: pd.DataFrame) -> pd.DataFrame:
        drop_null_mask = (
            df[DFColumns.BUYS_TOTAL_VOLUME].notnull() & df[DFColumns.SELLS_TOTAL_VOLUME].notnull()
        )
        self._logger.info(
            f"Number of orders dropped due to having a empty value in either {DFColumns.BUYS_TOTAL_VOLUME} or {DFColumns.SELLS_TOTAL_VOLUME}: {df.loc[~drop_null_mask].shape[0]}"  # noqa: E501
        )
        df = df.loc[drop_null_mask]

        if df.empty:
            self._logger.info(
                f"All orders have an empty value in either {DFColumns.BUYS_TOTAL_VOLUME} or {DFColumns.SELLS_TOTAL_VOLUME}. All orders dropped."  # noqa: E501
            )
            return df

        df.loc[:, DFColumns.VOLUME_DIFFERENCE] = calculate_percentage_difference(
            df[DFColumns.BUYS_TOTAL_VOLUME],
            df[DFColumns.SELLS_TOTAL_VOLUME],
            as_percentage=False,
        )

        volume_diff_mask = df[DFColumns.VOLUME_DIFFERENCE] < self._th_volume_difference

        df = df.loc[volume_diff_mask]

        return df

    def _check_tenant_adv(self, df: pd.DataFrame) -> pd.DataFrame:
        if DFColumns.TENANT_ADV not in df.columns:
            return pd.DataFrame()

        df[DFColumns.BUYS_TOTAL_VOLUME_TENANT_ADV] = (
            df[DFColumns.BUYS_TOTAL_VOLUME] / df[DFColumns.TENANT_ADV]
        )
        df[DFColumns.SELLS_TOTAL_VOLUME_TENANT_ADV] = (
            df[DFColumns.SELLS_TOTAL_VOLUME] / df[DFColumns.TENANT_ADV]
        )

        adv_mask = (df[DFColumns.BUYS_TOTAL_VOLUME_TENANT_ADV] > self._th_adv_30_day_ema) & (
            df[DFColumns.SELLS_TOTAL_VOLUME_TENANT_ADV] > self._th_adv_30_day_ema
        )

        df = df.loc[adv_mask]

        return df

    def _check_market_adv(self, df: pd.DataFrame):
        if DFColumns.MARKET_ADV not in df.columns:
            return pd.DataFrame()

        df.loc[:, DFColumns.BUYS_TOTAL_VOLUME_MARKET_ADV] = (
            df[DFColumns.BUYS_TOTAL_VOLUME] / df[DFColumns.MARKET_ADV]
        )
        df.loc[:, DFColumns.SELLS_TOTAL_VOLUME_MARKET_ADV] = (
            df[DFColumns.SELLS_TOTAL_VOLUME] / df[DFColumns.MARKET_ADV]
        )

        adv_mask = (df[DFColumns.BUYS_TOTAL_VOLUME_MARKET_ADV] > self._th_market_adv_30_day_ema) & (  # noqa: E501
            df[DFColumns.SELLS_TOTAL_VOLUME_MARKET_ADV] > self._th_market_adv_30_day_ema
        )

        df = df.loc[adv_mask]

        return df

    def _check_price_difference(self, df: pd.DataFrame) -> pd.DataFrame:
        buys_mask = df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY
        sells_mask = df[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL

        df.loc[buys_mask, DFColumns.BUYS_PRICE] = df.loc[buys_mask, OrderField.PC_FD_PRICE]
        df.loc[sells_mask, DFColumns.SELLS_PRICE] = df.loc[sells_mask, OrderField.PC_FD_PRICE]

        df.loc[:, DFColumns.MIN_BUYS_PRICE] = self._rolling(
            df, column=DFColumns.BUYS_PRICE, mode="min"
        )

        df.loc[:, DFColumns.MAX_SELLS_PRICE] = self._rolling(
            df, column=DFColumns.SELLS_PRICE, mode="max"
        )

        df = df.dropna(subset=[DFColumns.MIN_BUYS_PRICE, DFColumns.MAX_SELLS_PRICE])

        if df.empty:
            return df

        df.loc[:, DFColumns.PRICE_DIFFERENCE] = abs(
            df[DFColumns.MIN_BUYS_PRICE] / df[DFColumns.MAX_SELLS_PRICE]
        )

        price_diff_mask = df[DFColumns.PRICE_DIFFERENCE] <= self._th_price_difference

        df = df.loc[price_diff_mask]

        return df

    def _add_timestamps(self, df: pd.DataFrame) -> pd.DataFrame:
        df.loc[:, DFColumns.EPOCH_TS] = df[OrderField.TS_TRADING_DATE_TIME].values.astype(int)

        # Epoch Timestamps
        df[DFColumns.FIRST_TRADE_TS] = pd.to_datetime(
            self._rolling(df, column=DFColumns.EPOCH_TS, mode="min"), format="mixed"
        )
        df[DFColumns.LAST_TRADE_TS] = pd.to_datetime(
            self._rolling(df, column=DFColumns.EPOCH_TS, mode="max"), format="mixed"
        )

        # Time Difference
        df.loc[:, DFColumns.TIME_DIFFERENCE] = (
            (df[DFColumns.LAST_TRADE_TS] - df[DFColumns.FIRST_TRADE_TS]) / np.timedelta64(1, "s")
        ).round()

        df[DFColumns.FIRST_TRADE_DATE] = pd.to_datetime(
            df[DFColumns.FIRST_TRADE_TS].dt.date, format="mixed"
        )
        df[DFColumns.LAST_TRADE_DATE] = pd.to_datetime(
            df[DFColumns.LAST_TRADE_TS].dt.date, format="mixed"
        )

        return df

    @staticmethod
    def _add_market_impact(df: pd.DataFrame) -> pd.DataFrame:
        # Add market impact
        df.loc[:, DFColumns.MARKET_IMPACT] = (
            df[TradeStatsColumns.OPEN_PRICE] / df[TradeStatsColumns.CLOSE_PRICE]
        )

        return df

    def _rolling(self, df: pd.DataFrame, column: str, mode="sum") -> pd.Series:
        if mode not in ["sum", "min", "max"]:
            raise Exception("Invalid mode")

        rolling = df[[OrderField.TS_TRADING_DATE_TIME, column]].rolling(
            on=OrderField.TS_TRADING_DATE_TIME, window=f"{self._th_lbp}s", min_periods=1
        )

        if mode == "sum":
            result = rolling.sum()[column]
        elif mode == "min":
            result = rolling.min()[column]
        elif mode == "max":
            result = rolling.max()[column]
        else:
            raise Exception("Invalid mode")

        return result

    def _create_scenarios(self, df: pd.DataFrame):
        df = df.drop(columns=[NewColumns.INSTRUMENT_CODE], errors="ignore")

        scenario_fields = self.queries.get_additional_fields_result(
            df[OrderField.META_KEY].unique().tolist()
        )
        df = pd.merge(df, scenario_fields, on=OrderField.META_KEY, suffixes=("", "_y"))

        df.drop([col for col in df.columns if "_y" in col], axis=1, inplace=True)

        counterparty_field = (
            OrderField.COUNTERPARTY_NAME
            if OrderField.COUNTERPARTY_NAME in df.columns
            and not df[OrderField.COUNTERPARTY_NAME].isna().all()
            else OrderField.COUNTERPARTY_ID
        )
        client_field = (
            OrderField.CLIENT_IDENT_CLIENT_NAME
            if OrderField.CLIENT_IDENT_CLIENT_NAME in df.columns
            and not df[OrderField.CLIENT_IDENT_CLIENT_NAME].isna().all()
            else OrderField.CLIENT_FILE_IDENTIFIER
        )
        trader_field = (
            NewColumns.TRADER_NAME
            if NewColumns.TRADER_NAME in df.columns and not df[NewColumns.TRADER_NAME].isna().all()
            else OrderField.TRADER_FILE_IDENTIFIER
        )

        venue_field = (
            NewColumns.VENUE
            if NewColumns.VENUE in df.columns and not df[NewColumns.VENUE].isna().all()
            else OrderField.TRX_DTL_ULTIMATE_VENUE
        )

        fields_map = {
            venue_field: DFColumns.ULTIMATE_VENUE,
            NewColumns.INSTRUMENT_CODE: DFColumns.INSTRUMENT_ID,
            OrderField.INST_FULL_NAME: DFColumns.INSTRUMENT_FULL_NAME,
            client_field: DFColumns.CLIENT_NAME,
            counterparty_field: DFColumns.COUNTERPARTY_NAME,
            trader_field: DFColumns.TRADER_NAME,
        }

        df[DFColumns.TOTAL_VOLUME] = (
            df[DFColumns.BUYS_TOTAL_VOLUME] + df[DFColumns.SELLS_TOTAL_VOLUME]
        )

        if self._th_market_adv_30_day_ema:
            df[DFColumns.TOTAL_VOLUME_ADV] = (
                df[DFColumns.BUYS_TOTAL_VOLUME_MARKET_ADV]
                + df[DFColumns.SELLS_TOTAL_VOLUME_MARKET_ADV]
            )

        df = df.rename(columns={k: v for k, v in fields_map.items() if k in df.columns})

        df = df.loc[:, df.columns.isin(DFColumns.get_scenario_columns())]

        results = df.to_dict(orient="records")

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)
