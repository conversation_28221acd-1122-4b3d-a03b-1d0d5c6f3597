import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON><PERSON>nar<PERSON>, TradeColumns
from market_abuse_algorithms.strategy.painting_the_tape.static import DFColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=[DFColumns.EXECUTIONS])

    def _build_scenario(self) -> pd.Series:
        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records.executions = self._result.pop(DFColumns.EXECUTIONS)

        d.additionalFields.topLevel = self._result

        return pd.Series(d.to_dict())
