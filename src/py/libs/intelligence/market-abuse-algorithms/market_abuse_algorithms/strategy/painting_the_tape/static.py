from enum import Enum
from typing import List


class ThresholdsNames(str, Enum):
    ADV_30_DAY_EMA = "adv30DayEma"
    EVALUATION_TYPE = "evaluationType"
    LOOK_BACK_PERIOD = "lookBackPeriod"
    MARKET_ADV_30_DAY_EMA = "marketAdv30DayEma"
    PRICE_DIFFERENCE = "priceDifference"
    VOLUME_DIFFERENCE = "volumeDifference"


class EvaluationType(str, Enum):
    ALL = "All"
    COUNTERPARTY = "Counterparty"
    FIRM = "Firm"
    PERSON = "Person"

    @classmethod
    def get_values(cls, remove_type_all: bool = False) -> List[str]:
        values = [v for k, v in vars(cls).items() if k.isupper()]

        if remove_type_all:
            values.remove(cls.ALL)

        return values


class DFColumns:
    BOTH_SIDES = "bothSides"
    BUYS_PRICE = "buysPrice"
    BUYS_VOLUME = "buysVolume"
    BUYS_TOTAL_VOLUME = "buysTotalVolume"
    BUYS_TOTAL_VOLUME_TENANT_ADV = "buysTotalVolumeTenantAdv"
    BUYS_TOTAL_VOLUME_MARKET_ADV = "buysTotalVolumeMarketAdv"
    CLIENT_NAME = "clientName"
    COUNTERPARTY_NAME = "counterpartyName"
    DATE = "date"
    EPOCH_TS = "epochTS"
    EVALUATION_TYPE = "EvaluationType"
    EXECUTIONS = "executions"
    FIRST_TRADE_TS = "firstTradeTS"
    FIRST_TRADE_DATE = "firstTradeDate"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID = "instrumentId"
    LAST_TRADE_TS = "lastTradeTS"
    LAST_TRADE_DATE = "lastTradeDate"
    MARKET_ADV = "marketAdv"
    MARKET_IMPACT = "marketImpact"
    MAX_SELLS_PRICE = "maxSellsPrice"
    MIN_BUYS_PRICE = "minBuysPrice"
    PRICE_DIFFERENCE = "priceDifference"
    RIC = "RIC"
    SELLS_PRICE = "sellsPrice"
    SELLS_VOLUME = "sellsVolume"
    SELLS_TOTAL_VOLUME = "sellsTotalVolume"
    SELLS_TOTAL_VOLUME_TENANT_ADV = "sellsTotalVolumeTenantAdv"
    SELLS_TOTAL_VOLUME_MARKET_ADV = "sellsTotalVolumeMarketAdv"
    TENANT_ADV = "tenantAdv"
    TENANT_TOTAL_VOLUME = "tenantTotalVolume"
    TIME_DIFFERENCE = "timeDifference"
    TRADER_NAME = "traderName"
    ULTIMATE_VENUE = "ultimateVenue"
    VOLUME_DIFFERENCE = "volumeDifference"
    TOTAL_VOLUME = "totalVolume"
    TOTAL_VOLUME_ADV = "totalVolumeADV"

    @classmethod
    def get_scenario_columns(cls):
        return [
            cls.DATE,
            cls.INSTRUMENT_FULL_NAME,
            cls.INSTRUMENT_ID,
            cls.COUNTERPARTY_NAME,
            cls.CLIENT_NAME,
            cls.TRADER_NAME,
            cls.ULTIMATE_VENUE,
            cls.BUYS_TOTAL_VOLUME,
            cls.SELLS_TOTAL_VOLUME,
            cls.VOLUME_DIFFERENCE,
            cls.TENANT_ADV,
            cls.BUYS_TOTAL_VOLUME_TENANT_ADV,
            cls.SELLS_TOTAL_VOLUME_TENANT_ADV,
            cls.PRICE_DIFFERENCE,
            cls.TIME_DIFFERENCE,
            cls.MARKET_ADV,
            cls.BUYS_TOTAL_VOLUME_MARKET_ADV,
            cls.SELLS_TOTAL_VOLUME_MARKET_ADV,
            cls.MARKET_IMPACT,
            cls.EXECUTIONS,
            cls.TOTAL_VOLUME,
            cls.TOTAL_VOLUME_ADV,
        ]
