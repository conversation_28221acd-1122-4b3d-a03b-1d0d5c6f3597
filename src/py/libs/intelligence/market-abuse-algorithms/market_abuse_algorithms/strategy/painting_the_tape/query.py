import addict
import pandas as pd
from elasticsearch_dsl.query import Term
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderAggs, OrderExecutionsQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.repository.market_data.static import (
    TradeStatsColumns,
)
from market_abuse_algorithms.data_source.static.sdp.order import (  # noqa: E501
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.painting_the_tape.static import DFColumns
from market_abuse_algorithms.utils.data import process_numeric_columns
from pandas.tseries.offsets import BDay
from typing import List, Optional, Union


class Queries(BaseQuery):
    BACK_DAYS_FOR_ADV = 15

    EMA_WINDOW = 30  # EMA 30 days

    def __init__(
        self,
        context: Strategy<PERSON>ontext,
        audit: Audit,
        evaluation_type: str,
        market_adv_30_day_ema: float,
        by_firm: bool = False,
        by_counterparty: bool = False,
        by_client: bool = False,
        by_trader: bool = False,
    ):
        super().__init__(context=context, audit=audit)

        self._market_data_client = get_market_client(tenant=context.tenant)

        # Thresholds
        self._th_evaluation_type = evaluation_type
        self._th_market_adv_30_day_ema = market_adv_30_day_ema
        # Evaluation type
        self._by_firm = by_firm
        self._by_counterparty = by_counterparty
        self._by_client = by_client
        self._by_trader = by_trader

        self._required_fields = self._get_required_fields()

        self._start_date_for_adv = None

        self._evaluate_thresholds()

    def _evaluate_thresholds(
        self,
    ):
        # set date range
        query = self._get_base_query()

        self._set_date_range_thresholds(query, remove_data_range_from_filter=False)
        if self._start_date is not None:
            self._start_date_for_adv = self._start_date - BDay(
                self.EMA_WINDOW + self.BACK_DAYS_FOR_ADV
            )

    def _get_required_fields(self) -> List[str]:
        required_fields = [
            OrderField.META_KEY,
            OrderField.PC_FD_TRD_QTY,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.INST_EXT_UNIQUE_IDENT,
        ]

        if self._by_client:
            required_fields.append(OrderField.CLIENT_FILE_IDENTIFIER)

        if self._by_counterparty:
            required_fields.append(OrderField.COUNTERPARTY_ID)

        if self._by_trader:
            required_fields.append(OrderField.TRADER_FILE_IDENTIFIER)

        return required_fields

    def _get_base_query(self) -> OrderExecutionsQuery:
        query = OrderExecutionsQuery()

        self.add_default_conditions_to_query(query)

        query.add_condition(
            mode="must_not",
            conditions=[Term(**{OrderField.EXC_DTL_ORD_STATUS: OrderStatus.NEWO})],
        )

        query = self.get_query_with_required_fields(query, fields=self._required_fields)

        return query

    def get_data_to_analyse(self) -> pd.DataFrame:
        # Tenant query
        instrument_unique_identifiers = self._get_instrument_unique_identifiers()
        # TODO: Zero is a hack to have this threshold as optional

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = self._market_data_client.get_ric_map(  # noqa: E501
            list_inst_unique_id=instrument_unique_identifiers
        )

        if self._th_market_adv_30_day_ema:
            market_data = self._get_market_data(instrument_id_ric_mapping=instrument_id_ric_mapping)  # noqa: E501

            if market_data.empty:
                return pd.DataFrame()
        else:
            market_data = pd.DataFrame()

        if market_data.empty and instrument_unique_identifiers is None:
            return pd.DataFrame()

        result = self._tenant_activity(
            instrument_unique_identifiers=instrument_unique_identifiers,
            market_data=market_data,
        )

        result: pd.DataFrame = process_numeric_columns(data=result)

        return result

    # -------------------------------------------------------------------------
    #                   Tenant Activity
    # -------------------------------------------------------------------------

    def _tenant_activity(
        self, instrument_unique_identifiers: List[str], market_data: pd.DataFrame
    ) -> pd.DataFrame:
        if not market_data.empty:
            instrument_unique_identifiers = (
                market_data[OrderField.INST_EXT_UNIQUE_IDENT].unique().tolist()
            )

        # Tenant data
        tenant_query = self._get_tenant_activity_query(
            instrument_unique_identifiers=instrument_unique_identifiers
        )

        tenant_result = self._sdp_repository.search_after_query(tenant_query)

        tenant_df = self._process_tenant_result(tenant_result)

        # Tenant ADV
        tenant_adv_query = self._get_tenant_adv_query(
            instrument_unique_identifiers=instrument_unique_identifiers
        )

        tenant_adv_result = self._sdp_repository.search_query(tenant_adv_query)

        tenant_adv_df = self._process_tenant_adv_result(tenant_adv_result)

        on_list = [
            OrderField.INST_EXT_UNIQUE_IDENT,
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            OrderField.EXC_DTL_BUY_SELL_IND,
            DFColumns.DATE,
        ]

        # Client aggregation
        if self._by_client:
            # todo: should be changed to clientFileIdentifier?
            on_list.append(OrderField.CLIENT_IDENT_CLIENT_ID)

        # Counterparty aggregation
        elif self._by_counterparty:
            on_list.append(OrderField.COUNTERPARTY_ID)

        # Trader aggregation
        elif self._by_trader:
            # todo: should be changed to traderFileIdentifier
            on_list.append(NewColumns.TRADER_ID)

        df = tenant_df

        # Merge tenant df and tenant adv df
        if not tenant_adv_df.empty:
            df = pd.merge(df, tenant_adv_df, how="inner", on=on_list)

        # Merge market data into tenant activity
        date_fmt = "%Y-%m-%d"
        md_identifier = "md_identifier"

        df.loc[:, md_identifier] = (
            df[DFColumns.DATE].apply(lambda x: x.strftime(date_fmt))
            + df[OrderField.INST_EXT_UNIQUE_IDENT]
        )

        if not market_data.empty:
            market_data.loc[:, md_identifier] = (
                market_data[TradeStatsColumns.DATE].dt.strftime(date_fmt)
                + market_data[OrderField.INST_EXT_UNIQUE_IDENT]
            )

            df = df.merge(market_data, how="left", on=md_identifier, suffixes=(None, "_right"))

            with_market_adv_mask = df[DFColumns.MARKET_ADV].notnull()

            df = df.loc[with_market_adv_mask]

        return df

    def _get_tenant_activity_query(
        self, instrument_unique_identifiers: List[str]
    ) -> OrderExecutionsQuery:
        query = self._get_base_query()

        query.size(self.MINI_BATCH_SIZE)

        query.add_start_date(self._start_date, field=OrderField.TS_ORD_SUBMITTED)
        query.add_end_date(self._end_date, field=OrderField.TS_ORD_SUBMITTED)
        query.instrument_unique_identifier(instrument_unique_identifiers)

        includes = [
            OrderField.META_KEY,
            OrderField.INST_EXT_UNIQUE_IDENT,
            OrderField.PC_FD_PRICE,
            OrderField.PC_FD_TRD_QTY,
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.TRADER_FILE_IDENTIFIER,
            OrderField.CLIENT_FILE_IDENTIFIER,
            OrderField.TRX_DTL_VENUE,
        ]

        # Client aggregation
        if self._by_client:
            # todo: should be changed to clientFileIdentifier?
            includes.append(OrderField.CLIENT_IDENT_CLIENT_ID)
            includes.append(OrderField.CLIENT_FILE_IDENTIFIER)

        # Counterparty aggregation
        elif self._by_counterparty:
            includes.append(OrderField.COUNTERPARTY_ID)
            includes.append(OrderField.COUNTERPARTY_ID)

        # Trader aggregation
        elif self._by_trader:
            includes.append(OrderField.PARTICIPANTS)
            includes.append(OrderField.TRADER_FILE_IDENTIFIER)

        query.includes(includes)

        return query

    @staticmethod
    def _process_tenant_result(df: pd.DataFrame):
        df = df.drop(
            columns=[
                OrderField.META_PARENT,
                OrderField.META_MODEL,
                OrderField.PARTICIPANTS,
            ],
            errors="ignore",
        )

        df[DFColumns.DATE] = pd.to_datetime(
            df[OrderField.TS_TRADING_DATE_TIME], format="mixed"
        ).dt.date

        return df

    def _get_tenant_adv_query(
        self, instrument_unique_identifiers: List[str]
    ) -> OrderExecutionsQuery:
        query = self._get_base_query()

        query.size(0)

        query.add_start_date(self._start_date_for_adv, field=OrderField.TS_ORD_SUBMITTED)
        query.add_end_date(self._end_date, field=OrderField.TS_ORD_SUBMITTED)

        query.instrument_unique_identifier(instrument_unique_identifiers)

        q_aggs = query.aggs

        # Buy Sell side aggs
        q_aggs = q_aggs.bucket(
            OrderField.EXC_DTL_BUY_SELL_IND,
            "terms",
            field=OrderField.EXC_DTL_BUY_SELL_IND,
            size=OrderAggs.MAX_AGGS_SIZE,
        )

        # Ultimate Venue aggs
        q_aggs = q_aggs.bucket(
            OrderField.TRX_DTL_ULTIMATE_VENUE,
            "terms",
            field=OrderField.TRX_DTL_ULTIMATE_VENUE,
            size=OrderAggs.MAX_AGGS_SIZE,
        )

        # Client aggregation
        if self._by_client:
            q_aggs = OrderAggs.aggs_client(q_aggs)

        # Counterparty aggregation
        elif self._by_counterparty:
            q_aggs = OrderAggs.aggs_counterparty(q_aggs)

        # Trader aggregation
        elif self._by_trader:
            q_aggs = OrderAggs.aggs_trader(q_aggs)

        # Instrument unique identifier aggs
        q_aggs = OrderAggs.aggs_instrument_unique_identifier(q_aggs)

        q_aggs = OrderAggs.aggs_date_histogram(
            q_aggs, field=OrderField.TS_TRADING_DATE_TIME, interval="1d"
        )

        q_aggs.metric(OrderAggs.TOTAL_VOLUME, "sum", field=OrderField.PC_FD_TRD_QTY)

        OrderAggs.aggs_adv(q_aggs, window=self.EMA_WINDOW, buckets_path=OrderAggs.TOTAL_VOLUME)
        OrderAggs.metric_adv_exists(q_aggs)

        return query

    def _process_tenant_adv_result(self, result: addict.Dict) -> pd.DataFrame:
        if result.get("hits", {}).get("total", {}).get("value", 0) == 0:
            return pd.DataFrame()

        result = result["aggregations"][OrderField.EXC_DTL_BUY_SELL_IND]["buckets"]

        dfs_list = []

        for side in result:
            side_value = side["key"]
            for venue in side[OrderField.TRX_DTL_ULTIMATE_VENUE]["buckets"]:
                venue_value = venue["key"]
                if self._by_client:
                    venue_dfs = [
                        self._get_tenant_histogram_result(
                            instrument,
                            side=side_value,
                            venue=venue_value,
                            client=client["key"],
                        )
                        for client in venue[OrderAggs.BY_CLIENT][OrderAggs.BY_CLIENT_ID]["buckets"]
                        for instrument in client[OrderAggs.REVERSE_NESTED][
                            OrderAggs.BY_INSTRUMENT_UNIQUE_IDENTIFIER
                        ]["buckets"]
                    ]
                elif self._by_counterparty:
                    venue_dfs = [
                        self._get_tenant_histogram_result(
                            instrument,
                            side=side_value,
                            venue=venue_value,
                            counterparty=counterparty["key"],
                        )
                        for counterparty in venue[OrderAggs.BY_COUNTERPARTY]["buckets"]
                        for instrument in counterparty[OrderAggs.BY_INSTRUMENT_UNIQUE_IDENTIFIER][
                            "buckets"
                        ]
                    ]

                elif self._by_trader:
                    venue_dfs = [
                        self._get_tenant_histogram_result(
                            instrument,
                            side=side_value,
                            venue=venue_value,
                            trader=trader["key"],
                        )
                        for trader in venue[OrderAggs.BY_TRADER][OrderAggs.BY_TRADER_ID]["buckets"]
                        for instrument in trader[OrderAggs.REVERSE_NESTED][
                            OrderAggs.BY_INSTRUMENT_UNIQUE_IDENTIFIER
                        ]["buckets"]
                    ]
                # Firm
                elif self._by_firm:
                    venue_dfs = [
                        self._get_tenant_histogram_result(
                            instrument, side=side_value, venue=venue_value
                        )
                        for instrument in venue[OrderAggs.BY_INSTRUMENT_UNIQUE_IDENTIFIER][
                            "buckets"
                        ]
                    ]

                else:
                    raise Exception("Evaluation type not found to parse response.")

                dfs_list.extend(venue_dfs)

        dfs_list = list(filter(lambda x: not x.empty, dfs_list))

        if not dfs_list:
            return pd.DataFrame()

        df = pd.concat(dfs_list, ignore_index=True)

        df = df.rename(
            columns={
                f"{OrderAggs.ADV}.value": DFColumns.TENANT_ADV,
                "key_as_string": DFColumns.DATE,
                f"{OrderAggs.TOTAL_VOLUME}.value": DFColumns.TENANT_TOTAL_VOLUME,
            }
        ).drop(columns=["doc_count", "key"], errors="ignore")

        df[DFColumns.DATE] = pd.to_datetime(
            df[DFColumns.DATE],
            infer_datetime_format=False,
            format="%Y-%m-%dT%H:%M:%S.%fZ",
        ).dt.date

        return df

    def _get_tenant_histogram_result(
        self,
        result: addict.Dict,
        side: str,
        venue: str,
        counterparty: Optional[str] = None,
        client: Optional[str] = None,
        trader: Optional[str] = None,
    ) -> pd.DataFrame:
        df = pd.json_normalize(result.to_dict()[OrderAggs.DATE_HISTOGRAM]["buckets"])

        if df.empty:
            return df

        df[DFColumns.EVALUATION_TYPE] = self._th_evaluation_type
        df[OrderField.EXC_DTL_BUY_SELL_IND] = side
        df[OrderField.INST_EXT_UNIQUE_IDENT] = result["key"]
        df[OrderField.TRX_DTL_ULTIMATE_VENUE] = venue

        if counterparty:
            df[OrderField.COUNTERPARTY_ID] = counterparty
        if client:
            # todo: should be changed to clientFileIdentifier?
            df[OrderField.CLIENT_IDENT_CLIENT_ID] = client
        if trader:
            # todo: should be changed to traderFileIdentifier?
            df[NewColumns.TRADER_ID] = trader

        return df

    # -------------------------------------------------------------------------
    #                   Market Activity
    # -------------------------------------------------------------------------

    def _get_market_data(self, instrument_id_ric_mapping: dict) -> pd.DataFrame:
        """Gets the market data with the adv column populated."""
        if not instrument_id_ric_mapping:
            return pd.DataFrame()

        market_data = self._fetch_market_data(instrument_id_ric_mapping=instrument_id_ric_mapping)

        return market_data

    def _get_instrument_unique_identifiers(self) -> Optional[List[str]]:
        """Gets all the instrument unique identifiers for the trades under
        analysis.

        :return:
        """
        # Query
        query = self._get_base_query()

        query.size(0)
        query.add_start_date(self._start_date, field=OrderField.TS_ORD_SUBMITTED)
        query.add_end_date(self._end_date, field=OrderField.TS_ORD_SUBMITTED)

        q_aggs = query.q.aggs

        OrderAggs.aggs_instrument_unique_identifier(q_aggs)

        response = self._sdp_repository.search_query(query)

        # Process response
        buckets = response.aggregations.get(OrderAggs.BY_INSTRUMENT_UNIQUE_IDENTIFIER).buckets

        if not buckets:
            return

        result = [bucket.key for bucket in buckets]

        return result

    def _fetch_market_data(self, instrument_id_ric_mapping: dict) -> pd.DataFrame:
        """Based on the instrument unique identifiers retrieved, fetches the
        market data with the adv calculated for the window set in the
        threshold.

        :param instrument_unique_identifiers: List of instrument unique identifiers.
        :return:
        """

        results = []

        for instrument_id, instrument_ric in instrument_id_ric_mapping.items():
            data = self._market_data_client.get_market_data_adv(
                instrument_ric=instrument_ric,
                instrument_unique_identifier=instrument_id,
                adv_col_name=DFColumns.MARKET_ADV,
                window=self.EMA_WINDOW,
                start_date=self.look_back_period_ts,
                end_date=self.market_data_end_date,
            )

            if data.empty:
                continue

            data[DFColumns.RIC] = instrument_ric
            data[OrderField.INST_EXT_UNIQUE_IDENT] = instrument_id

            results.append(data)

        if not results:
            return pd.DataFrame()

        df = pd.concat(results).drop_duplicates()

        return df

    # -------------------------------------------------------------------------
    #                   Additional queries
    # -------------------------------------------------------------------------

    def get_additional_fields_result(self, orders_keys: list) -> pd.DataFrame:
        query = OrderExecutionsQuery()
        query.size(self.MINI_BATCH_SIZE)

        query.add_condition(
            mode="must_not",
            conditions=[Term(**{OrderField.EXC_DTL_ORD_STATUS: OrderStatus.NEWO})],
        )

        query.key(orders_keys)

        includes = [
            OrderField.META_KEY,
            OrderField.INST_FULL_NAME,
            *OrderField.get_instrument_fields(),
        ]

        if self._by_client:
            includes.append(OrderField.CLIENT_FILE_IDENTIFIER)
            includes.append(OrderField.CLIENT_IDENT_CLIENT_NAME)

        elif self._by_counterparty:
            includes.append(OrderField.COUNTERPARTY_ID)
            includes.append(OrderField.COUNTERPARTY_NAME)

        elif self._by_trader:
            includes.append(OrderField.TRADER_FILE_IDENTIFIER)
            includes.append(NewColumns.TRADER_NAME)

        query.includes(includes)

        result = self._sdp_repository.search_after_query(query)

        cols = [OrderField.INST_FULL_NAME, NewColumns.INSTRUMENT_CODE]

        if self._by_client:
            if OrderField.CLIENT_IDENT_CLIENT_NAME in result.columns:
                cols.append(OrderField.CLIENT_IDENT_CLIENT_NAME)
            elif OrderField.CLIENT_FILE_IDENTIFIER in result.columns:
                cols.append(OrderField.CLIENT_FILE_IDENTIFIER)

        elif self._by_counterparty:
            if OrderField.COUNTERPARTY_NAME in result.columns:
                cols.append(OrderField.COUNTERPARTY_NAME)
            elif OrderField.COUNTERPARTY_ID in result.columns:
                cols.append(OrderField.COUNTERPARTY_ID)

        elif self._by_trader:
            if NewColumns.TRADER_NAME in result.columns:
                cols.append(NewColumns.TRADER_NAME)
            elif OrderField.TRADER_FILE_IDENTIFIER in result.columns:
                cols.append(OrderField.TRADER_FILE_IDENTIFIER)

        result = result[[OrderField.META_KEY, *cols]]

        return result
