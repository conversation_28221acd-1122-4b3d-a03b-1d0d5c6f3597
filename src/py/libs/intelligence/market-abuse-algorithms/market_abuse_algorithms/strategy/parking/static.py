from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from typing import List


class DFColumns:
    BUY_SELL_INDICATOR = "buySellIndicator"
    BUYER_NAME = "buyerName"
    CLIENT_NAMES = "clientNames"
    DATE = "date"
    EXECUTED_BUY_QUANTITY = "executedBuyQuantity"
    EXECUTED_SELL_QUANTITY = "executedSellQuantity"
    EXECUTIONS_KEYS = "executionsKeys"
    IMPLIED_PL = "impliedPL"
    IMPLIED_PL_CURRENCY = "impliedPLCurrency"
    INST_EXT_BEST_EX_ASSET_CLASS_MAIN = "bestExAssetClassMain"
    ISIN = "isin"
    INSTRUMENT_ID = "instrumentId"
    INSTRUMENT_NAME = "instrumentName"
    INVOLVED_PARTIES = "involvedParties"
    MAX_PRICE_DIFFERENCE = "maxPriceDifference"
    NUMBER_OF_EXECUTIONS = "numberOfExecutions"
    NUMBER_OF_ORDERS = "numberOfOrders"
    ORDERS = "orders"
    ORDER_TYPES = "orderTypes"
    PRICE_DIFFERENCE = "priceDifference"
    PRICE_DIFFERENCE_PERCENTAGE = "priceDifferencePercentage"
    QUANTITY_DIFFERENCE = "quantityDifference"
    QUANTITY_DIFFERENCE_PERCENTAGE = "quantityDifferencePercentage"
    TIME_DIFFERENCE = "timeDifference"
    TIMEDELTA = "timedelta"
    TRADE_DATE = "tradeDate"
    TRADER_NAMES = "traderNames"
    VOLUME_PERCENTAGE_DIFFERENCE = "volumePercentageDifference"
    ULTIMATE_VENUES = "ultimateVenues"
    SELLER_NAME = "sellerName"

    @classmethod
    def get_cols_to_scenario(cls) -> List[str]:
        return [
            cls.BUY_SELL_INDICATOR,
            cls.BUYER_NAME,
            cls.CLIENT_NAMES,
            cls.DATE,
            cls.EXECUTED_BUY_QUANTITY,
            cls.EXECUTED_SELL_QUANTITY,
            cls.EXECUTIONS_KEYS,
            cls.IMPLIED_PL,
            cls.IMPLIED_PL_CURRENCY,
            cls.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            cls.ISIN,
            cls.INSTRUMENT_ID,
            cls.INSTRUMENT_NAME,
            cls.INVOLVED_PARTIES,
            cls.MAX_PRICE_DIFFERENCE,
            cls.NUMBER_OF_EXECUTIONS,
            cls.NUMBER_OF_ORDERS,
            cls.ORDERS,
            cls.ORDER_TYPES,
            cls.PRICE_DIFFERENCE,
            cls.PRICE_DIFFERENCE_PERCENTAGE,
            cls.QUANTITY_DIFFERENCE,
            cls.QUANTITY_DIFFERENCE_PERCENTAGE,
            cls.TIME_DIFFERENCE,
            cls.TRADE_DATE,
            cls.TRADER_NAMES,
            cls.VOLUME_PERCENTAGE_DIFFERENCE,
            cls.ULTIMATE_VENUES,
            cls.SELLER_NAME,
        ]


class EvaluationType(str, Enum):
    CLIENT = "Client"
    COUNTERPARTY = "Counterparty"
    DESK = "Desk"
    EXECUTING_ENTITY = "Executing Entity"
    PORTFOLIO_MANAGER = "Portfolio Manager"
    TRADER = "Trader"


class ThresholdsNames(str, Enum):
    EVALUATION_TYPE = "evaluationType"
    LOOK_BACK_PERIOD = "lookBackPeriod"
    MAXIMUM_TRADE_COUNT = "maxTradeCount"
    MINIMUM_TRADE_QUANTITY = "minTradeQuantity"
    PRICE_PERCENTAGE_DIFFERENCE = "priceDifference"
    QUANTITY_PERCENTAGE_DIFFERENCE = "quantityDifference"
    SELL_DIRECTIONALITY = "sellDirectionalityFirst"


EVALUATION_TYPE_GROUPING_MAP = {
    EvaluationType.CLIENT: OrderField.CLIENT_FILE_IDENTIFIER,
    EvaluationType.COUNTERPARTY: OrderField.COUNTERPARTY_ID,
    EvaluationType.DESK: OrderField.TRD_ALGO_FIRM_DESKS_NAME,
    EvaluationType.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    EvaluationType.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    EvaluationType.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
}


SCENARIO_COLUMNS = [
    NewColumns.CLIENT,
    NewColumns.INSTRUMENT_CODE,
    NewColumns.VENUE,
    OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY,
    OrderField.COUNTERPARTY_ID,
    OrderField.EXC_DTL_BUY_SELL_IND,
    OrderField.EXC_DTL_ORD_TYPE,
    OrderField.ORD_IDENT_ID_CODE,
    OrderField.PC_FD_PRICE,
    OrderField.TRADER_FILE_IDENTIFIER,
    OrderField.TS_TRADING_DATE_TIME,
]
