from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.parking.static import EvaluationType
from pydantic import Field
from typing import Optional


class Thresholds(CommonThresholds):
    """Thresholds to be applied to the algorithm."""

    evaluationType: EvaluationType
    lookBackPeriod: int = Field(..., ge=2, le=35, description="In days")
    maxTradeCount: Optional[int] = Field(None, ge=2, le=100)
    minTradeQuantity: Optional[int] = Field(None, ge=1, le=10000000)
    priceDifference: Optional[float] = Field(None, ge=0, le=1)
    quantityDifference: float = Field(..., ge=0, le=1)
    sellDirectionalityFirst: Optional[bool]
