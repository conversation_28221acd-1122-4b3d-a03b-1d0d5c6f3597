import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.communications import (
    CommunicationsQuery,
)
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.query.utils import (
    remove_date_range_from_iris_filters,
)
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.communications import (
    CommunicationsField as CommsField,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import check_value_exists, process_numeric_columns
from pandas.tseries.offsets import BDay
from se_market_data_utils.schema.refinitiv import RefinitivEventType


class Queries(BaseQuery):
    REQUIRED_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.INST_EXT_UNIQUE_IDENT,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PARTICIPANTS,  # ?
        OrderField.PC_FD_PRICE,
        OrderField.TS_TRADING_DATE_TIME,
        OrderField.get_instrument_fields(),
        OrderField.get_venue_fields(),
    ]

    INCLUDES_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.INST_EXT_UNIQUE_IDENT,
        OrderField.INST_FULL_NAME,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PARTICIPANTS,
        OrderField.PC_FD_PRICE,
        OrderField.TS_TRADING_DATE_TIME,
        *OrderField.get_instrument_fields(),
        *OrderField.get_venue_fields(),
        *OrderField.get_involved_parties_fields(),
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._market_data_client = get_market_client(tenant=context.tenant)

    def cases_to_analyse(self):
        query = self._execs_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._execs_query(inst_comb=inst_comb)
            query = self.get_initial_query(query)

            result = self._sdp_repository.search_after_query(query=query)

            result: pd.DataFrame = process_numeric_columns(data=result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    @staticmethod
    def _execs_query(inst_comb=None) -> OrderExecutionsQuery:
        q = OrderExecutionsQuery()
        q.order_status([OrderStatus.FILL, OrderStatus.PARF])
        q.buy_sell(BuySell.SELL)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    def get_market_data(self, df: pd.DataFrame) -> pd.DataFrame:
        instrument_unique_identifiers = (
            df[OrderField.INST_EXT_UNIQUE_IDENT].dropna().unique().tolist()
        )

        if not instrument_unique_identifiers:
            return pd.DataFrame()

        data = df[[OrderField.INST_EXT_UNIQUE_IDENT, OrderField.TS_TRADING_DATE_TIME]]

        date_col = "date_col"
        date_plus_bday_col = "date_plus_bday_col"

        data[date_col] = data[OrderField.TS_TRADING_DATE_TIME].dt.date
        data[date_plus_bday_col] = (data[date_col] + BDay(1)).dt.date

        results = []

        for inst_id, group in df.groupby(OrderField.INST_EXT_UNIQUE_IDENT):
            dates = pd.unique(data[[date_col, date_plus_bday_col]].values.ravel("K"))

            result = self._market_data_client.get_tick_data(
                instrument_unique_identifier=inst_id,
                dates=dates,
                event_type=RefinitivEventType.TRADE,
            )

            if result.empty:
                continue

            result[OrderField.INST_EXT_UNIQUE_IDENT] = inst_id

            results.append(result)

        result = pd.concat(results, ignore_index=True) if results else pd.DataFrame()

        return result

    def get_communications_instrument(
        self, instrument_name, instrument_id, start_date, end_date
    ) -> int:
        query = CommunicationsQuery()
        query.size(0)
        query.add_start_date(start_date, field=CommsField.TS_START)
        query.add_end_date(end_date, field=CommsField.TS_START)
        query.evidences_of_instrument([instrument_id, instrument_name])

        result = self._sdp_repository.search_query(query=query)

        return result.get("hits", {}).get("total", {}).get("value", 0)

    def get_communications_instrument_trader(
        self, instrument_name, instrument_id, start_date, end_date, trader_id
    ) -> int:
        if not check_value_exists(trader_id):
            # TODO: add log
            return 0

        query = CommunicationsQuery()
        query.size(0)
        query.add_start_date(start_date, field=CommsField.TS_START)
        query.add_end_date(end_date, field=CommsField.TS_START)
        query.evidences_of_instrument([instrument_id, instrument_name])
        query.trader_id(trader_id)

        result = self._sdp_repository.search_query(query=query)

        return result.get("hits", {}).get("total", {}).get("value", 0)

    def get_executions_previous_days(self, instrument_id, start_date, end_date) -> list:
        filters = remove_date_range_from_iris_filters(self._filters)

        # TODO: verify if we really need to include the ORD_IDENT_ID_CODE since we are retrieving a list of &keys  # noqa: E501
        query = OrderExecutionsQuery()
        query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])
        query.size(500)
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.buy_sell(BuySell.BUY)
        query.instrument_id(instrument_id)
        query.add_iris_filters(filters)
        query.add_start_date(start_date, field=OrderField.TS_TRADING_DATE_TIME)
        query.add_end_date(end_date, field=OrderField.TS_TRADING_DATE_TIME)

        result = self._sdp_repository.search_after_query(query=query)

        if result.empty:
            return list()

        keys = result[OrderField.META_KEY].unique().tolist()

        return keys

    def get_orders_keys_map(self, orders_ids) -> dict:
        query = OrderQuery()
        query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])
        query.size(self.MINI_BATCH_SIZE)
        query.order_id(orders_ids)

        result = self._sdp_repository.search_after_query(query=query)

        if result.empty:
            return {}

        result = dict(zip(result[OrderField.ORD_IDENT_ID_CODE], result[OrderField.META_KEY]))

        return result
