import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON>ce<PERSON>io, TradeColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["order"], multiple=["executions", "executionsPrevious7Days"])

    def _build_scenario(self) -> pd.Series:
        records = addict.Dict()
        records.order = self._result["order"]
        records.executions = sorted(self._result["order_executions"])
        records.executionsPrevious7Days = sorted(self._result["executions_previous_7_days"])

        top_level = addict.Dict()
        top_level.involvedParties = self._result["involved_parties"]
        top_level.numberOfComms = self._result["comms_count"]
        top_level.lowestMarketPrice24h = self._result["lowest_market_price_24h"]
        top_level.executionVsMarketPrice = self._result["execution_vs_market_price"]

        d = addict.Dict()
        d.thresholds = self._thresholds
        d.records = records
        d.additionalFields.topLevel = top_level

        scenario = pd.Series(d.to_dict())

        return scenario
