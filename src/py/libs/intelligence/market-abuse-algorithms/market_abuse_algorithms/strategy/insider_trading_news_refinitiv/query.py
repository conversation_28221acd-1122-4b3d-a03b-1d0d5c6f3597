import numpy as np
import pandas as pd
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.repository.news.refinitiv_news import (
    get_refinitiv_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.errors import NoAvailableDataToAnalyse
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import QueryAggScripts
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.static import (
    DFColumns,
    ThresholdsNames,
)
from typing import Dict, List, Optional, Union


class Queries(BaseQuery):
    INCLUDES_FIELDS = [
        OrderField.TS_ORD_SUBMITTED,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.INST_ID_CODE,
        OrderField.INST_DERIV_UND_INSTS,
        OrderField.INST_EXT_UNIQUE_IDENT,
        OrderField.INST_FULL_NAME,
        OrderField.META_KEY,
        OrderField.META_PARENT,
        OrderField.TS_ORD_UPDATED,
        OrderField.BEST_EXC_DATA_TRX_VOL_ECB_REF_RATE,
        OrderField.BEST_EXC_DATA_ORD_VOL_ECB_REF_RATE,
        OrderField.BEST_EXC_DATA_ORD_VOL,
        OrderField.BEST_EXC_DATA_TRX_VOL,
        *OrderField.get_involved_parties_fields(),
        OrderField.PC_FD_INIT_QTY,
        OrderField.PC_FD_TRD_QTY,
    ]

    REQUIRED_FIELDS = [OrderField.META_KEY, OrderField.EXC_DTL_BUY_SELL_IND]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        # Thresholds
        self._th_minimum_order_quantity = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_ORDER_QUANTITY
        )
        self._th_relevance = self.context.thresholds.dict().get(ThresholdsNames.RELEVANCE)
        self._th_news_sources = self.context.thresholds.dict().get(ThresholdsNames.NEWS_SOURCES)

        self._market_data_client = get_market_client(tenant=context.tenant)

        self.refinitiv_news_client = get_refinitiv_client(relevance=self._th_relevance)
        self._refinitiv_sources = self.get_news_sources()

    def get_cases_to_analyse(self):
        """yields the result of the queries obtained in
        _get_cases_to_analyse_query."""
        query: OrderQuery = self.get_query(fetch_executions=False, inspect_required_fields=True)
        order_state_query: OrderExecutionsQuery = self.get_query(
            fetch_executions=True, inspect_required_fields=True
        )

        # orders instrument combinations
        try:
            instruments_combinations = self.get_instruments_combinations(
                query=query,
                agg_script=QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV,
            )

        except NoAvailableDataToAnalyse:
            instruments_combinations = []

        # orders states instrument combinations
        try:
            state_instrument_combinations = self.get_instruments_combinations(
                query=order_state_query,
                agg_script=QueryAggScripts.UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV,
            )

        except NoAvailableDataToAnalyse:
            state_instrument_combinations = []

        if not instruments_combinations and not state_instrument_combinations:
            self._logger.debug("No Instrument combinations found while getting data from ES.")
            return []

        elif instruments_combinations and state_instrument_combinations:
            instruments_combinations.extend(state_instrument_combinations)
            final_instruments_combinations = []
            duplicates = []
            for sublist in instruments_combinations:
                unique = []
                for item in sublist:
                    if item not in duplicates:
                        unique.append(item)
                        duplicates.append(item)
                if unique:
                    final_instruments_combinations.append(unique)
        else:
            final_instruments_combinations = (
                instruments_combinations
                if instruments_combinations
                else state_instrument_combinations
            )

        instrument_id_ric_mapping: Union[dict, pd.DataFrame] = self._market_data_client.get_ric_map(
            instrument_combinations=final_instruments_combinations,
            use_prefix_query=True,
        )

        if instrument_id_ric_mapping is None:
            self._logger.info(
                f"None of instruments {final_instruments_combinations} have a RIC mapped to."
            )
            return pd.DataFrame()

        for inst_comb in final_instruments_combinations:
            query: OrderQuery = self.get_query(inst_comb=inst_comb)
            order_state_query: OrderExecutionsQuery = self.get_query(
                fetch_executions=True, inst_comb=inst_comb
            )

            order_state_result: pd.DataFrame = self._sdp_repository.search_after_query(
                order_state_query
            )
            result: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

            if OrderField.INST_DERIV_UND_INSTS in result.columns:
                result = result.drop(OrderField.INST_DERIV_UND_INSTS, axis=1)

            if OrderField.INST_DERIV_UND_INSTS in order_state_result.columns:
                order_state_result = order_state_result.drop(
                    OrderField.INST_DERIV_UND_INSTS, axis=1
                )

            results: pd.DataFrame = pd.concat(
                [result, order_state_result], axis=0, ignore_index=True
            ).drop_duplicates()

            results = self._process_cases_to_analyse(results)

            if OrderField.INST_EXT_UNIQUE_IDENT not in results:
                self._logger.info(
                    f"The column {OrderField.INST_EXT_UNIQUE_IDENT} is "
                    f"not present in the results dataset."
                )
                continue

            results[DFColumns.ORDER_TIMESTAMP] = results[OrderField.TS_ORD_SUBMITTED].fillna(
                results[OrderField.TS_ORD_UPDATED]
            )
            results.loc[:, DFColumns.RIC] = results[OrderField.INST_EXT_UNIQUE_IDENT].map(
                dict(instrument_id_ric_mapping)
            )
            yield results

    def get_query(
        self,
        fetch_executions: bool = False,
        inspect_required_fields: bool = False,
        inst_comb: Optional[List] = None,
    ) -> Union[OrderQuery, OrderExecutionsQuery]:
        """
        :param fetch_executions:
        :param inspect_required_fields:
        :param inst_comb:
        :return:
        """

        if fetch_executions:
            query = OrderExecutionsQuery()
            query.add_condition(
                mode="must_not",
                conditions=[
                    {
                        "terms": {
                            OrderField.EXC_DTL_ORD_STATUS: [
                                OrderStatus.NEWO,
                                OrderStatus.FILL,
                                OrderStatus.PARF,
                            ]
                        }
                    }
                ],
            )
        else:
            query = OrderQuery()

        quantity_field = OrderField.PC_FD_TRD_QTY if fetch_executions else OrderField.PC_FD_INIT_QTY

        if self._th_minimum_order_quantity is not None:
            query.add_condition(
                mode="filter",
                conditions=[{"range": {quantity_field: {"gte": self._th_minimum_order_quantity}}}],
            )

        for field in self.REQUIRED_FIELDS:
            query.exists(field=field)

        query.includes(self.INCLUDES_FIELDS)

        if inst_comb:
            query.instrument_id(inst_comb)

        self.add_default_conditions_to_query(query)

        if inspect_required_fields:
            self.inspect_required_fields(
                query, fields=self.REQUIRED_FIELDS, update_audit_metrics=True
            )

        query = self.get_query_with_required_fields(query, fields=self.REQUIRED_FIELDS)

        return query

    def get_news_sources(self) -> Dict:
        """Queries the available news sources on refinitiv and removes the
        sources not available.

        Returns a dict with a key saying if we want all sources present
        in refinitiv and another key with the
        """
        refinitiv_sources = self.refinitiv_news_client.get_refinitiv_sources()

        if not refinitiv_sources:
            self._logger.warning(
                "There isn't any available news sources in Refinitiv.", exc_info=True
            )
            return {}

        sources_to_use: List[str] = [
            source for source in self._th_news_sources if source in refinitiv_sources
        ]

        if set(self._th_news_sources) == set(refinitiv_sources):
            return {"all_sources": True, "sources": refinitiv_sources}

        return {"all_sources": False, "sources": sources_to_use}

    def fetch_refinitiv_news(
        self, ric: str, date_range: Dict, method_id_audit: str, orders_for_ric: List
    ) -> pd.DataFrame:
        """Get the refinitiv news based on ric; date and sources.

        :param orders_for_ric: list of orders for StepAudit
        :param ric: RIC to fetch news
        :param date_range: date limits to fetch news
        :param method_id_audit: uuid for StepAudit
        :return: dataframe with news fetched
        """

        all_sources: bool = self._refinitiv_sources.get("all_sources")
        sources: List = self._refinitiv_sources.get("sources")

        refinitiv_news = self.refinitiv_news_client.get_refinitiv_news(
            ric=ric,
            start_date=date_range.get("start"),
            end_date=date_range.get("end"),
            all_sources=all_sources,
            method_id=method_id_audit,
            sources=sources,
            all_subjects=True,
            orders_for_ric=orders_for_ric,
        )
        return refinitiv_news

    @staticmethod
    def _process_cases_to_analyse(df: pd.DataFrame) -> pd.DataFrame:
        """process dataframe.

        :param df:
        :return:
        """
        # datetime columns
        for col in [OrderField.TS_ORD_SUBMITTED, OrderField.TS_ORD_UPDATED]:
            if col in df.columns:
                continue
            df[col] = pd.NaT

        for col in [OrderField.META_PARENT]:
            if col in df.columns:
                continue
            df[col] = np.nan

        return df

    def get_net_scenario(self, keys: List[str]) -> pd.DataFrame:
        """Query for fetching quantities to calculate net quantity of each
        scenario.

        :param keys: list of order metakeys to be fetched
        :return: pandas Dataframe with quantity, meta key and buy sell indicator.
        """
        q = OrderQuery()

        q.key(keys)

        includes_fields = [
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.META_KEY,
            OrderField.PC_FD_INIT_QTY,
        ]

        required_fields = [
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.META_KEY,
            OrderField.PC_FD_INIT_QTY,
        ]

        for field in required_fields:
            q.exists(field=field)

        q.includes(includes_fields)

        result = self._sdp_repository.search_after_query(q)

        return result
