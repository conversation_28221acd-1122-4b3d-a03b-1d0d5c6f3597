# type: ignore
import logging
from market_abuse_algorithms.data_source.static.news.refinitiv_news import (
    RefinitivNewsRelevance,
)
from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.static import (
    ThresholdsNames,
)
from pydantic import Field, validator
from pydantic.class_validators import root_validator
from pydantic.dataclasses import dataclass
from typing import List, Optional


@dataclass
class BeforeTradeTimestamp:
    unit: TimeUnit
    value: int = Field(..., ge=0)

    @validator("unit")
    def check_unit(cls, v):
        if v not in TimeUnit:
            raise ValueError(f"`{v}` not valid unit for this strategy: {list(TimeUnit)}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_minutes = 1500

        upper_bounds = {
            TimeUnit.MILLISECONDS: upper_bound_minutes * 60 * 1e3,
            TimeUnit.SECONDS: upper_bound_minutes * 60,
            TimeUnit.MINUTES: upper_bound_minutes,
            TimeUnit.HOURS: upper_bound_minutes / 60,
            TimeUnit.DAYS: upper_bound_minutes / (60 * 24),
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


@dataclass
class AfterTradeTimestamp:
    unit: TimeUnit
    value: int = Field(..., ge=0)

    @validator("unit")
    def check_unit(cls, v):
        if v not in TimeUnit:
            raise ValueError(f"`{v}` not valid unit for this strategy: {list(TimeUnit)}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_minutes = 14400

        upper_bounds = {
            TimeUnit.MILLISECONDS: upper_bound_minutes * 60 * 1e3,
            TimeUnit.SECONDS: upper_bound_minutes * 60,
            TimeUnit.MINUTES: upper_bound_minutes,
            TimeUnit.HOURS: upper_bound_minutes / 60,
            TimeUnit.DAYS: upper_bound_minutes / (60 * 24),
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class InsiderTradingWithNewsRefinitivThresholds(CommonThresholds):
    afterTradeTimestamp: AfterTradeTimestamp
    beforeTradeTimestamp: BeforeTradeTimestamp
    minimumOrderQuantity: Optional[int] = Field(None, ge=0, le=1000000)
    newsSources: List[str]
    sentiment: Optional[float] = Field(None, ge=0, le=1)
    relevance: RefinitivNewsRelevance

    @root_validator(pre=True)
    def setting_after_trade_timestamp_default(cls, values):
        """Set the default of afterTradeTimestamp if they are missing."""
        if ThresholdsNames.AFTER_TRADE_TIMESTAMP not in values.keys():
            logging.info(
                f"Missing the {ThresholdsNames.AFTER_TRADE_TIMESTAMP}, setting its value to its default one, where unit"  # noqa: E501
                f" is days and value is 10."
            )
            values[ThresholdsNames.AFTER_TRADE_TIMESTAMP] = {
                "unit": "days",
                "value": 10,
            }

        return values

    @root_validator(pre=True)
    def setting_before_trade_timestamp_default(cls, values):
        """Set the default of beforeTradeTimestamp if they are missing."""
        if ThresholdsNames.BEFORE_TRADE_TIMESTAMP not in values.keys():
            logging.info(
                f"Missing the {ThresholdsNames.BEFORE_TRADE_TIMESTAMP}, setting its value to its default one, where unit"  # noqa: E501
                f" is days and value is 1."
            )
            values[ThresholdsNames.BEFORE_TRADE_TIMESTAMP] = {
                "unit": "days",
                "value": 1,
            }

        return values
