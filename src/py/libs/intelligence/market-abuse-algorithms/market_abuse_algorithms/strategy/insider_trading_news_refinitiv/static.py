from enum import Enum
from typing import List


class ThresholdsNames(str, Enum):
    MINIMUM_ORDER_QUANTITY = "minimumOrderQuantity"
    RELEVANCE = "relevance"
    AFTER_TRADE_TIMESTAMP = "afterTradeTimestamp"
    BEFORE_TRADE_TIMESTAMP = "beforeTradeTimestamp"
    NEWS_SOURCES = "newsSources"
    SENTIMENT = "sentiment"


class DFColumns:
    HEADLINE = "headline"
    ENTITY_NAME = "entityName"
    INVOLVED_PARTIES = "involvedParties"
    LINK_URL = "linkURL"
    LIST_ORDERS = "listOrders"
    NET_QUANTITY = "netQuantity"
    NET_SCENARIO_POSITION = "netScenarioPosition"
    NEWS_SOURCE_NAME = "newsSourceName"
    NUMBER_OF_ORDERS = "numberOfOrders"
    ORDER_TIMESTAMP = "orderTimestamp"
    ORDERS_KEYS = "ordersKeys"
    RIC = "RIC"
    SENTIMENT_NEGATIVE = "sentimentNegative"
    SENTIMENT_NEUTRAL = "sentimentNeutral"
    SENTIMENT_POSITIVE = "sentimentPositive"
    SOURCE = "source"
    STORY_CREATED = "storyCreated"
    STORY_ID = "storyId"
    STORY_SUBJECT_RELEVANCE = "storySubjectRelevance"
    TIMESTAMP = "timestamp"
    TITLE = "title"
    TS_AFTER = "timestampAfter"
    TS_AFTER_ISO = "tsAfterISO"
    TS_BEFORE = "timestampBefore"
    TS_BEFORE_ISO = "tsBeforeISO"

    @classmethod
    def get_cols_to_scenario(cls) -> List[str]:
        return [
            cls.ENTITY_NAME,
            cls.HEADLINE,
            cls.INVOLVED_PARTIES,
            cls.LINK_URL,
            cls.NEWS_SOURCE_NAME,
            cls.ORDERS_KEYS,
            cls.SENTIMENT_NEGATIVE,
            cls.SENTIMENT_NEUTRAL,
            cls.SENTIMENT_POSITIVE,
            cls.STORY_CREATED,
            cls.STORY_ID,
            cls.STORY_SUBJECT_RELEVANCE,
        ]


class RefinitivNewsColumns:
    CONFIDENCE = "confidence"
    CONTENT_ID = "contentID"
    CONTENT_URI = "contentUri"
    MATERIAL_IMPACT = "materialImpact"
    PERM_ID = "permId"
    PROVIDER = "provider"
    SCORE = "score"
    SENTIMENT_NEGATIVE = "sentimentNegative"
    SENTIMENT_NEUTRAL = "sentimentNeutral"
    SENTIMENT_POSITIVE = "sentimentPositive"
    SOURCES = "sources"
    STORY_CREATED = "storyCreated"
    STORY_ID = "storyId"
    STORY_SUBJECT_RELEVANCE = "storySubjectRelevance"
    SUBJECT = "subject"
    TITLE = "title"


class TransactionVolumeCurrency(str, Enum):
    USD = "USD"
    GBP = "GBP"
    JPY = "JPY"
    CHF = "CHF"
    EUR = "EUR"
