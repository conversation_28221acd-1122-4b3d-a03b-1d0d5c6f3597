# ruff: noqa: E501
import itertools
import pandas as pd
from datetime import datetime, timezone
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, NewColumns, OrderField
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    Aggregated<PERSON>tep<PERSON>udit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.models import (
    InsiderTradingWithNewsRefinitivThresholds,
)
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.query import (
    Queries,
)
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.scenario import (
    Scenario,
)
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.static import (
    DFColumns,
    RefinitivNewsColumns,
    ThresholdsNames,
    TransactionVolumeCurrency,
)
from market_abuse_algorithms.strategy.utils import get_sentiment_price_column
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import List, NoReturn, Optional


class Strategy(AbstractStrategy):
    """Insider Trading with News Refinitiv:

    https://steeleye.atlassian.net/browse/PR-819
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.INSIDER_TRADING_WITH_NEWS_REFINITIV,
            thresholds_class=InsiderTradingWithNewsRefinitivThresholds,
            queries_class=Queries,
        )

        self._th_minimum_order_quantity = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_ORDER_QUANTITY
        )
        self._th_relevance = self.context.thresholds.dict().get(ThresholdsNames.RELEVANCE)
        self._th_before_trade_timestamp = self.context.thresholds.dict().get(
            ThresholdsNames.BEFORE_TRADE_TIMESTAMP
        )
        self._th_after_trade_timestamp = self.context.thresholds.dict().get(
            ThresholdsNames.AFTER_TRADE_TIMESTAMP
        )
        self._sentiment = self.context.thresholds.dict().get(ThresholdsNames.SENTIMENT)

        self._th_news_sources = self.queries.get_news_sources()

        self._set_time_thresholds()

    def _set_time_thresholds(self):
        """This method converts time window thresholds to pd.Timedelta."""
        if self._th_before_trade_timestamp is not None:
            self._th_before_trade_timestamp = pd.Timedelta(
                value=self._th_before_trade_timestamp.value,
                unit=self._th_before_trade_timestamp.unit.value,
            )

        if self._th_after_trade_timestamp is not None:
            self._th_after_trade_timestamp = pd.Timedelta(
                value=self._th_after_trade_timestamp.value,
                unit=self._th_after_trade_timestamp.unit.value,
            )

    def _apply_strategy(self):
        """for loop for applying the strategy to each chunk of date obtained by
        the query."""
        for data in self.queries.get_cases_to_analyse():
            market_abuse_audit_object.records_analysed += len(data)
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame) -> NoReturn:
        """Groups each chunk of data by RIC Applies the algo for each group."""
        start, method_id = self.get_start_time_and_unique_id()

        if data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=0,
                    number_of_resulting_orders=0,
                    step_name="Getting data from ElasticSearch.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                )
            )
            self._logger.warning("No data available to analyse.", exc_info=True)
            return

        if DFColumns.RIC not in data.columns or data[DFColumns.RIC].isna().all():
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Required column {DFColumns.RIC} is not present in the data or is empty.",
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    list_of_instruments=data.get(OrderField.INST_EXT_UNIQUE_IDENT).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(data),
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=0,
                    step_name="Checking for required columns.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )
            self._logger.info(
                f"Required column {DFColumns.RIC} is not present in the data or is empty.",
                exc_info=True,
            )
            return

        for ix, group in data.groupby(DFColumns.RIC, as_index=False):
            result: pd.DataFrame = self._run_algo(dataset=group, ric=ix)
            if result.empty:
                self._logger.info(f"No alerts for RIC {ix}. Skipping to next RIC")
                continue

            self._create_scenarios(df=result)

    def _run_algo(self, dataset: pd.DataFrame, ric: str) -> pd.DataFrame:
        """Runs the algo with the input data pandas Dataframe Creates possible
        scenarios with the results of the algo."""
        dataset = self.assign_timestamp_columns(dataset_without_datetime=dataset)

        start, method_id = self.get_start_time_and_unique_id()

        orders_meta_key = dataset.get(OrderField.META_KEY, pd.Series()).tolist()

        refinitiv_data: pd.DataFrame = self.queries.fetch_refinitiv_news(
            ric=ric,
            date_range={
                "start": min(dataset[DFColumns.TS_BEFORE_ISO]),
                "end": max(dataset[DFColumns.TS_AFTER_ISO]),
            },
            method_id_audit=method_id,
            orders_for_ric=orders_meta_key,
        )

        if refinitiv_data.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dataset),
                    number_of_input_orders=len(dataset),
                    number_of_resulting_orders=0,
                    step_name="Get Refinitiv News Data.",
                    step_type=StepType.REFINITIV_NEWS,
                    drop_reason=DropReason.RECORDS_DROPPED_REFINITIV_NEWS,
                )
            )
            return pd.DataFrame()

        result_data = self.merge_tenant_refinitiv_data(
            tenant_data=dataset, refinitiv_data=refinitiv_data
        )
        orders_merged = result_data.get(OrderField.META_KEY, pd.Series()).tolist()

        del refinitiv_data

        dropped = list(
            set(dataset.get(OrderField.META_KEY, pd.Series()).tolist()) - set(orders_merged)
        )

        if len(dropped) > 0:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped after merging refinitiv and tenant data.",
                    list_of_order_ids=dropped,
                    list_of_instruments=dataset.get(OrderField.INST_EXT_UNIQUE_IDENT).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(
                        set(dataset.get(OrderField.META_KEY, pd.Series()).tolist())
                    ),
                    number_of_resulting_orders=len(set(orders_merged)),
                    step_name="Get Refinitiv News Data.",
                    step_type=StepType.REFINITIV_NEWS,
                    drop_reason=DropReason.RECORDS_DROPPED_REFINITIV_NEWS,
                )
            )

        if result_data.empty:
            return result_data

        start, method_id = self.get_start_time_and_unique_id()

        if self._sentiment is not None:
            result_data: pd.DataFrame = self._evaluate_sentiment_threshold(
                data=result_data,
                currency=TransactionVolumeCurrency.GBP,
                method_id=method_id,
            )

        dropped = list(
            set(orders_merged) - set(result_data.get(OrderField.META_KEY, pd.Series()).tolist())
        )

        if len(dropped) > 0:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Orders dropped after applying the sentiment threshold for RIC {ric}",
                    list_of_order_ids=dropped,
                    list_of_instruments=dataset.get(OrderField.INST_EXT_UNIQUE_IDENT).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(set(orders_merged)),
                    number_of_resulting_orders=len(
                        set(result_data.get(OrderField.META_KEY, pd.Series()).tolist())
                    ),
                    step_name="Get Refinitiv News Data.",
                    step_type=StepType.MARKET_DATA,
                    drop_reason=DropReason.RECORDS_DROPPED_REFINITIV_NEWS,
                )
            )

        return result_data

    def _evaluate_sentiment_threshold(
        self, data: pd.DataFrame, currency: TransactionVolumeCurrency, method_id: str
    ) -> pd.DataFrame:
        """
        If the t.[Sentiment Threshold] is activated, sum up the [Transaction Volume - GBP] of all records in the grouping.
            If the figure is < 0 then:
                Drop any news articles where [Sentiment Negative] (from Refinitiv News DB) < t.[Sentiment Threshold]
            elif the figure is > 0 then:
                Drop any news articles where [Sentiment Positive] (from Refinitiv News DB) < t.[Sentiment Threshold]
            else:
                Drop all orders
        :param method_id:
        :param data: records data with orders/orders state & news
        :param currency: TransactionVolumeCurrency
        :return:
        """
        price_columns = [
            OrderField.get_best_exc_trx_ecb_ref_rate_ccy(currency),
            OrderField.PC_FD_TRD_QTY,
            OrderField.PC_FD_INIT_QTY,
        ]
        sentiment_quantity_column: Optional[str] = get_sentiment_price_column(
            data=data, currency=currency
        )
        if sentiment_quantity_column is None:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"None of {price_columns} orders fields are present in the dataset columns",
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    list_of_instruments=data.get(OrderField.INST_ID_CODE).unique().tolist(),
                )
            )
            return pd.DataFrame()

        self._logger.debug(
            f"The 'price' column to use when evaluating the sentiment threshold is {sentiment_quantity_column}."
        )

        sells = data[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL

        sentiment_volume = (
            data[~sells][sentiment_quantity_column].sum()
            - data[sells][sentiment_quantity_column].sum()
        )

        if sentiment_volume == 0:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Orders have a {sentiment_quantity_column} equal to 0. Dropping orders",
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                )
            )
            self._logger.info(
                f"Orders have a {sentiment_quantity_column} equal to 0. Dropping orders."
            )
            return pd.DataFrame()

        sentiment = (
            RefinitivNewsColumns.SENTIMENT_NEGATIVE
            if sentiment_volume < 0
            else RefinitivNewsColumns.SENTIMENT_POSITIVE
        )

        return data[data[sentiment] > self._sentiment]

    def _create_scenarios(self, df: pd.DataFrame):
        """Generates the alerts.

        :param df:
        :return:
        """
        start, method_id = self.get_start_time_and_unique_id()

        # Involved parties
        involved_parties_cols = list(
            {*[OrderField.COUNTERPARTY_NAME, NewColumns.CLIENT, NewColumns.TRADER_NAME]}
            & set(df.columns.tolist())
        )

        if involved_parties_cols:
            df[DFColumns.INVOLVED_PARTIES] = df.loc[:, involved_parties_cols].apply(
                lambda x: sorted(x.dropna().values), axis=1
            )

        df[DFColumns.NEWS_SOURCE_NAME] = df[RefinitivNewsColumns.SOURCES]
        df[DFColumns.LINK_URL] = df[RefinitivNewsColumns.CONTENT_URI]
        df[DFColumns.HEADLINE] = df[RefinitivNewsColumns.TITLE]
        df[DFColumns.ENTITY_NAME] = df[OrderField.INST_FULL_NAME]

        parent_ids = df[OrderField.META_PARENT].dropna().tolist()

        if parent_ids:
            order_meta_keys = self.queries.get_order_meta_key(parent_ids=parent_ids)
            df.loc[
                df[OrderField.META_PARENT].isin(order_meta_keys.keys()),
                OrderField.META_KEY,
            ] = df[OrderField.META_PARENT].map(order_meta_keys)

        df = df.loc[:, ~df.columns.duplicated()]

        # Format columns to scenario columns
        df = df.rename(columns={OrderField.META_KEY: DFColumns.ORDERS_KEYS})

        scenario_columns = DFColumns.get_cols_to_scenario()

        sentiment_column = get_sentiment_price_column(df, currency=TransactionVolumeCurrency.GBP)
        if sentiment_column not in scenario_columns:
            scenario_columns.append(sentiment_column)

        cols_mask = df.columns.isin(scenario_columns)

        df = df.loc[:, cols_mask]

        def format_alert(alert_data: pd.DataFrame):
            record = alert_data.iloc[0].to_dict()
            record[DFColumns.NET_SCENARIO_POSITION] = sum(alert_data[DFColumns.NET_QUANTITY])
            record[DFColumns.ORDERS_KEYS] = alert_data.pop(DFColumns.ORDERS_KEYS).tolist()
            if DFColumns.INVOLVED_PARTIES in alert_data.columns:
                record[DFColumns.INVOLVED_PARTIES] = list(
                    set(itertools.chain.from_iterable(alert_data[DFColumns.INVOLVED_PARTIES]))
                )
            return record

        add_df: pd.DataFrame = self.queries.get_net_scenario(
            keys=df[DFColumns.ORDERS_KEYS].tolist()
        )

        if add_df.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Could not calculate net quantity for each alert.",
                    list_of_order_ids=df.get(OrderField.META_KEY, pd.Series()).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.now(timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(df),
                    number_of_input_orders=len(df),
                    number_of_resulting_orders=0,
                    step_name="Creating alerts.",
                    step_type=StepType.ALERT_CREATION,
                    drop_reason=DropReason.RECORDS_DROPPED_CREATE_ALERTS,
                )
            )
            self._logger.info(
                f"Not able to fetch quantities to calculate net quantity for each scenario."
                f" For these keys: {set(df[DFColumns.ORDERS_KEYS])}"
            )
            return

        if OrderField.PC_FD_INIT_QTY in df.columns:
            add_df = add_df.drop(columns=[OrderField.PC_FD_INIT_QTY])

        df = df.merge(
            add_df,
            how="left",
            left_on=DFColumns.ORDERS_KEYS,
            right_on=OrderField.META_KEY,
        )

        df[DFColumns.NET_QUANTITY] = df.apply(
            lambda x: -x[OrderField.PC_FD_INIT_QTY]
            if x[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL
            else x[OrderField.PC_FD_INIT_QTY],
            axis=1,
        )

        results: List = []

        for ind, group in df.groupby(RefinitivNewsColumns.STORY_ID, as_index=False):
            if self._sentiment is not None:
                sentiment_column = get_sentiment_price_column(
                    group, currency=TransactionVolumeCurrency.GBP
                )

                sells = group[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL

                sentiment_volume = (
                    group[~sells][sentiment_column].sum() - group[sells][sentiment_column].sum()
                )

                sentiment_group = (
                    RefinitivNewsColumns.SENTIMENT_NEGATIVE
                    if sentiment_volume < 0
                    else RefinitivNewsColumns.SENTIMENT_POSITIVE
                )

                news_negative_sentiment = group.iloc[0][RefinitivNewsColumns.SENTIMENT_NEGATIVE]
                news_sentiment = (
                    RefinitivNewsColumns.SENTIMENT_NEGATIVE
                    if news_negative_sentiment > self._sentiment
                    else RefinitivNewsColumns.SENTIMENT_POSITIVE
                )

                if sentiment_group != news_sentiment:
                    continue

            record = format_alert(group)
            results.append(record)

        for result in results:
            result[DFColumns.NUMBER_OF_ORDERS] = len(result[DFColumns.ORDERS_KEYS])
            result[DFColumns.TIMESTAMP] = result[RefinitivNewsColumns.STORY_CREATED]
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def assign_timestamp_columns(self, dataset_without_datetime: pd.DataFrame):
        """Assign timestamp columns.

        :param dataset_without_datetime: dataframe without date columns
        :return:
        """

        dataset_copy = dataset_without_datetime.copy()

        dataset_copy[DFColumns.TS_BEFORE] = (
            dataset_without_datetime[DFColumns.ORDER_TIMESTAMP] - self._th_before_trade_timestamp
        )
        dataset_copy[DFColumns.TS_AFTER] = (
            dataset_without_datetime[DFColumns.ORDER_TIMESTAMP] + self._th_after_trade_timestamp
        )
        dataset_copy[DFColumns.TS_BEFORE_ISO] = dataset_copy[DFColumns.TS_BEFORE].apply(
            lambda x: pd.Timestamp(x, unit="ns").tz_localize(None)
        )
        dataset_copy[DFColumns.TS_AFTER_ISO] = dataset_copy[DFColumns.TS_AFTER].apply(
            lambda x: pd.Timestamp(x, unit="ns").tz_localize(None)
        )

        return dataset_copy

    @staticmethod
    def merge_tenant_refinitiv_data(
        tenant_data: pd.DataFrame, refinitiv_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Method to merge orders to the corresponding news.

        :param tenant_data: pd.DataFrame with tenant data (orders)
        :param refinitiv_data: pd.DataFrame with news from refinitiv
        :return pd.DataFrame with news and orders merged
        """
        refinitiv_data[RefinitivNewsColumns.STORY_CREATED] = refinitiv_data[
            RefinitivNewsColumns.STORY_CREATED
        ].apply(lambda x: pd.Timestamp(x, unit="ns").tz_localize(None))

        # Logic to map META_KEYS to news. Will create a list with meta keys for each news
        refinitiv_data[DFColumns.LIST_ORDERS] = refinitiv_data.apply(
            lambda x: tenant_data.loc[
                (tenant_data[DFColumns.TS_BEFORE_ISO] <= x[RefinitivNewsColumns.STORY_CREATED])
                & (x[RefinitivNewsColumns.STORY_CREATED] <= tenant_data[DFColumns.TS_AFTER_ISO]),
                OrderField.META_KEY,
            ]
            .unique()
            .tolist(),
            axis=1,
        )

        # remove the lists and create one row per combination of news and order
        refinitiv_data = (
            refinitiv_data.explode(column=[DFColumns.LIST_ORDERS])
            .dropna(subset=[DFColumns.LIST_ORDERS])
            .rename(columns={DFColumns.LIST_ORDERS: OrderField.META_KEY})
        )

        # merge orders on news using the &key field. We should have one row per combination of news and order
        result_data = refinitiv_data.merge(right=tenant_data, on=OrderField.META_KEY)

        return result_data
