import datetime
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.front_running_client_vs_client.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.front_running_client_vs_client.query import (
    Queries,
)
from market_abuse_algorithms.strategy.front_running_client_vs_client.scenario import (
    Scenario,
)
from market_abuse_algorithms.strategy.front_running_client_vs_client.static import (
    DFColumns,
    EvaluationType,
    OrderType,
    ThresholdsNames,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import Dict, List


class Strategy(AbstractStrategy):
    """Front Running Client VS Client.

    Backend Jira ticket:
    https://steeleye.atlassian.net/browse/MA-42

    Confluence page:
    https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1038319641/Frontrunning+-+V1.5
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.FRONT_RUNNING_CLIENT_VS_CLIENT,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )

        self._th_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )

        self._th_front_running_volume_difference = self.context.thresholds.dict().get(
            ThresholdsNames.FRONT_RUNNING_VOLUME_DIFFERENCE
        )
        self._th_front_run_order_volume = self.context.thresholds.dict().get(
            ThresholdsNames.FRONT_RUN_ORDER_VOLUME
        )
        self._th_time_window = self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)

        self._set_time_window_threshold()

    def _set_time_window_threshold(self):
        """
        This method converts time window thresholds to pd.Timedelta
        :return:
        """

        self._th_time_window = pd.Timedelta(
            value=self._th_time_window.value, unit=self._th_time_window.unit.value
        )

    def _apply_strategy(self):
        """for loop for applying the strategy to each chunk of data obtained by
        the query."""
        for data in self.queries.get_cases_to_analyse():
            market_abuse_audit_object.records_analysed += len(data)
            self._apply_strategy_mini_batch(data=data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        """performs a grouping by instrument code and date and applies the algo
        to each group.

        :param data:
        :return:
        """
        for ix, group in data.groupby([NewColumns.INSTRUMENT_CODE, OrderField.DATE]):
            self._run_algo(df=group)

    def _run_algo(self, df: pd.DataFrame):
        """Runs the algo with the input data pandas Dataframe Creates possible
        scenarios with the results of the algo."""
        results = self._algo(df)

        if not results:
            return

        for result in results:
            self._create_scenarios(df=result)

    def _algo(self, df: pd.DataFrame) -> List[pd.DataFrame]:
        """For each dataframe performs a group of filters and thresholds.

        :param df: dataframe to be analysed
        :return: list of dataframes which creat alerts
        """
        start, method_id = self.get_start_time_and_unique_id()

        res = []
        df_sorted = df.sort_values(by=[OrderField.TS_ORD_SUBMITTED])

        if not self.evaluation_type_filter(df=df_sorted):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Dropped orders with less than 2 unique values for {self._th_evaluation_type} "  # noqa: E501
                    f"{ThresholdsNames.EVALUATION_TYPE}.",
                    list_of_order_ids=df.get(OrderField.META_KEY, pd.Series()).tolist(),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(df),
                    number_of_input_orders=len(df),
                    number_of_resulting_orders=0,
                    step_name="Check if evaluation type field has unique values.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )
            return res

        orders_remaining_after_time_window_thresold = []
        orders_remaining_after_buy_sell_check = []
        orders_remaining_after_evaluation_type_filter = []
        orders_remaining_after_checking_volume_difference = []
        orders_remaining_after_checking_frontrun_order_volume = []

        start, method_id = self.get_start_time_and_unique_id()

        for df_window in self._timewindow_threshold(df=df_sorted):
            if df_window.empty:
                dropped = set(df_window.get(OrderField.META_KEY, pd.Series()).tolist())
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="Orders dropped after applying time window threshold.",
                        list_of_order_ids=list(dropped),
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=len(df_sorted),
                        number_of_resulting_orders=len(
                            set(orders_remaining_after_time_window_thresold)
                        ),
                        step_name=f"Apply threshold: {ThresholdsNames.TIME_WINDOW}.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                    )
                )
                continue

            orders_remaining_after_time_window_thresold.extend(
                df_window.get(OrderField.META_KEY, pd.Series()).tolist()
            )
            start, method_id = self.get_start_time_and_unique_id()
            df_window = self.buy_sell_checker(df_window)
            orders_remaining_after_buy_sell_check.extend(
                df_window.get(OrderField.META_KEY, pd.Series()).tolist()
            )
            if df_window.empty or not self.evaluation_type_filter(df=df_window):
                dropped = set(df_window.get(OrderField.META_KEY, pd.Series()).tolist())
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="Orders dropped after applying time window threshold.",
                        list_of_order_ids=list(dropped),
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=len(df_window),
                        number_of_resulting_orders=len(set(orders_remaining_after_buy_sell_check)),
                        step_name=f"Apply threshold: {ThresholdsNames.TIME_WINDOW}.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                    )
                )
                continue

            orders_remaining_after_evaluation_type_filter.extend(
                df_window.get(OrderField.META_KEY, pd.Series()).tolist()
            )
            start, method_id = self.get_start_time_and_unique_id()

            df_volume = self._check_front_running_volume_difference(df=df_window)
            orders_remaining_after_checking_volume_difference.extend(
                df_volume.get(OrderField.META_KEY, pd.Series()).tolist()
            )
            if df_volume.empty or (
                self._th_front_run_order_volume is not None
                and not self._check_front_run_order_volume(df=df_volume)
            ):
                dropped = set(df_volume.get(OrderField.META_KEY, pd.Series()).tolist())
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason="Orders dropped after applying time window threshold.",
                        list_of_order_ids=list(dropped),
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=len(df_window),
                        number_of_resulting_orders=len(
                            set(orders_remaining_after_checking_volume_difference)
                        ),
                        step_name=f"Apply threshold: {ThresholdsNames.TIME_WINDOW}.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                    )
                )
                continue

            orders_remaining_after_checking_frontrun_order_volume.extend(
                df_volume.get(OrderField.META_KEY, pd.Series()).tolist()
            )
            res.append(df_volume)

        dropped = set(df.get(OrderField.META_KEY, pd.Series()).tolist()) - set(
            orders_remaining_after_time_window_thresold
        )

        start, method_id = self.get_start_time_and_unique_id()

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped after applying time window threshold.",
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(df),
                    number_of_resulting_orders=len(
                        set(orders_remaining_after_time_window_thresold)
                    ),
                    step_name=f"Apply threshold: {ThresholdsNames.TIME_WINDOW}.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                )
            )

        dropped = set(orders_remaining_after_time_window_thresold) - set(
            orders_remaining_after_buy_sell_check
        )

        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason="Orders dropped after BUY/SELL check.",
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(set(orders_remaining_after_time_window_thresold)),
                    number_of_resulting_orders=len(set(orders_remaining_after_buy_sell_check)),
                    step_name="Buy Sell check.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )

        dropped = set(orders_remaining_after_buy_sell_check) - set(
            orders_remaining_after_evaluation_type_filter
        )
        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Dropped orders with less than 2 unique values for {self._th_evaluation_type} "  # noqa: E501
                    f"{ThresholdsNames.EVALUATION_TYPE}.",
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(set(orders_remaining_after_buy_sell_check)),
                    number_of_resulting_orders=len(
                        set(orders_remaining_after_evaluation_type_filter)
                    ),
                    step_name="Check if evaluation type field has unique values.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                )
            )

        dropped = set(orders_remaining_after_evaluation_type_filter) - set(
            orders_remaining_after_checking_volume_difference
        )
        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Checks if the volume difference is below the threshold "
                    f"{ThresholdsNames.FRONT_RUNNING_VOLUME_DIFFERENCE} "
                    f"({self._th_front_running_volume_difference}).",
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(set(orders_remaining_after_evaluation_type_filter)),
                    number_of_resulting_orders=len(
                        set(orders_remaining_after_checking_volume_difference)
                    ),
                    step_name=f"Apply threshold: {ThresholdsNames.FRONT_RUNNING_VOLUME_DIFFERENCE}.",  # noqa: E501
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )

        dropped = set(orders_remaining_after_checking_volume_difference) - set(
            orders_remaining_after_checking_frontrun_order_volume
        )
        start, method_id = self.get_start_time_and_unique_id()
        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Checks if the volume of the front run is above the threshold "
                    f"{ThresholdsNames.FRONT_RUN_ORDER_VOLUME} ({self._th_front_run_order_volume}).",  # noqa: E501
                    list_of_order_ids=list(dropped),
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=len(dropped),
                    number_of_input_orders=len(
                        set(orders_remaining_after_checking_volume_difference)
                    ),
                    number_of_resulting_orders=len(
                        set(orders_remaining_after_checking_frontrun_order_volume)
                    ),
                    step_name=f"Apply threshold: {ThresholdsNames.FRONT_RUN_ORDER_VOLUME}.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )

        return res

    def evaluation_type_filter(self, df: pd.DataFrame) -> bool:
        """checks if the group has more than one evaluation type unique filter.

        :param df: dataframe to be checked
        :return: bool true if passes the check, false otherwise
        """
        if self._th_evaluation_type == EvaluationType.CLIENT:
            if OrderField.CLIENT_IDENT_CLIENT_NAME not in df.columns:
                return False
            else:
                return df[OrderField.CLIENT_IDENT_CLIENT_NAME].nunique() > 1

        elif self._th_evaluation_type == EvaluationType.DESK:
            if OrderField.TRD_ALGO_FIRM_DESKS not in df.columns:
                return False
            else:
                return df[OrderField.TRD_ALGO_FIRM_DESKS].nunique() > 1

    def _timewindow_threshold(self, df: pd.DataFrame) -> pd.DataFrame:
        """Creates groups applying a time window.

        :param df: dataframe to be grouped
        :return: dataframes from windows
        """
        df[DFColumns.FRONT_RUNNING_CAPTURE_WINDOW] = (
            df[OrderField.TS_ORD_SUBMITTED] + self._th_time_window
        )

        df[DFColumns.FRONT_RUN_ORDERS] = df.apply(
            lambda x: df.loc[
                (x[DFColumns.FRONT_RUNNING_CAPTURE_WINDOW] >= df[OrderField.TS_ORD_SUBMITTED])
                & (df[OrderField.TS_ORD_SUBMITTED] >= x[OrderField.TS_ORD_SUBMITTED])
                & (x[OrderField.META_KEY] != df[OrderField.META_KEY]),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        for ix, row in df.iterrows():
            if len(row[DFColumns.FRONT_RUN_ORDERS]) <= 0:
                continue
            df_front_run = df.loc[df[OrderField.META_KEY].isin(row[DFColumns.FRONT_RUN_ORDERS])]
            res = pd.concat(
                [pd.DataFrame(row).transpose(), df_front_run],
                axis=0,
                ignore_index=True,
            )
            res[DFColumns.ORDER_TYPE] = OrderType.FRONT_RUN
            res.loc[0, DFColumns.ORDER_TYPE] = OrderType.FRONT_RUNNING
            yield res

        return pd.DataFrame()

    def buy_sell_checker(self, df: pd.DataFrame) -> pd.DataFrame:
        """Checker that checks if at least one of the front run orders.

        :param df:
        :return:
        """
        df = df.reset_index(drop=True)

        front_running_indicator = df.loc[0, OrderField.EXC_DTL_BUY_SELL_IND]

        df_clean = df[df[OrderField.EXC_DTL_BUY_SELL_IND] == front_running_indicator]

        if df_clean.shape[0] >= 2:
            return df_clean

        return pd.DataFrame()

    def _check_front_running_volume_difference(self, df: pd.DataFrame) -> pd.DataFrame:
        """Checks if the volume difference is below the threshold.

        :param df: dataframe to be checked
        :return: dataframe
        """
        try:
            front_running_volume_difference = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                    OrderField.PC_FD_INIT_QTY,
                ].sum()
                / df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                    OrderField.PC_FD_INIT_QTY,
                ].sum()
            )
        except ZeroDivisionError:
            return pd.DataFrame()

        if front_running_volume_difference <= self._th_front_running_volume_difference:
            df[DFColumns.VOLUME_DIFFERENCE] = front_running_volume_difference
            return df

        return pd.DataFrame()

    def _check_front_run_order_volume(self, df: pd.DataFrame) -> bool:
        """Checks if the volume of the front run is above the threshold.

        :param df: dataframe to be checked
        :return: bool true if passes the check, false otherwise
        """
        front_run_order_volume = df.loc[
            df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
            OrderField.PC_FD_INIT_QTY,
        ].sum()

        return front_run_order_volume >= self._th_front_run_order_volume

    def _create_scenarios(self, df: pd.DataFrame):
        """created a dictionary with the expected fields for the scenario and
        created the scenario objects.

        :param df:
        :return:
        """
        result = {}

        df = df.reset_index(drop=True)

        if self._th_evaluation_type == EvaluationType.DESK:
            if OrderField.CLIENT_IDENT_CLIENT_NAME not in df.columns:
                client_names = self.queries.fetch_client_id_name(
                    meta_keys=df[OrderField.META_KEY].tolist()
                )
                if not client_names.empty:
                    df = df.set_index(OrderField.META_KEY, drop=False)

                    df = df.merge(client_names, how="left", left_index=True, right_index=True)

                    df = df.reset_index(drop=True)

        if OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN in df.columns:
            result[DFColumns.ASSET_CLASS] = df.loc[0, OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN]

        if OrderField.INST_FULL_NAME in df.columns:
            result[DFColumns.INSTRUMENT_NAME] = df.loc[0, OrderField.INST_FULL_NAME]

        if OrderField.EXC_DTL_BUY_SELL_IND in df.columns:
            result[DFColumns.BUY_SELL] = df.loc[0, OrderField.EXC_DTL_BUY_SELL_IND]

        if OrderField.ORD_IDENT_ID_CODE in df.columns:
            result[DFColumns.FRONT_RUN_ORDERS_COUNT] = len(
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                    OrderField.ORD_IDENT_ID_CODE,
                ].unique()
            )

        if OrderField.TS_ORD_SUBMITTED in df.columns:
            result[DFColumns.FRONT_RUN_ORDERS_TIMESTAMP] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                OrderField.TS_ORD_SUBMITTED,
            ].min()

        if OrderField.PC_FD_INIT_QTY in df.columns:
            result[DFColumns.FRONT_RUN_ORDERS_QUANTITY] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                OrderField.PC_FD_INIT_QTY,
            ].sum()

            result[DFColumns.FRONT_RUNNING_ORDERS_QUANTITY] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                OrderField.PC_FD_INIT_QTY,
            ].sum()

        if OrderField.CLIENT_IDENT_CLIENT_NAME in df.columns:
            result[DFColumns.FRONT_RUN_ORDERS_CLIENTS] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                    OrderField.CLIENT_IDENT_CLIENT_NAME,
                ]
                .unique()
                .tolist()
            )

            result[DFColumns.FRONT_RUNNING_ORDERS_CLIENTS] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                    OrderField.CLIENT_IDENT_CLIENT_NAME,
                ]
                .unique()
                .tolist()
            )

        result: Dict[str, str] = self.populate_alert_trader_fields(result=result, df=df)

        if OrderField.TS_ORD_SUBMITTED in df.columns:
            result[DFColumns.FRONT_RUNNING_ORDER_TIMESTAMP] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                OrderField.TS_ORD_SUBMITTED,
            ][0]

        if (
            DFColumns.FRONT_RUN_ORDERS_TIMESTAMP and DFColumns.FRONT_RUNNING_ORDER_TIMESTAMP
        ) in result.keys():
            result[DFColumns.TIME_DIFFERENCE] = (
                result[DFColumns.FRONT_RUN_ORDERS_TIMESTAMP]
                - result[DFColumns.FRONT_RUNNING_ORDER_TIMESTAMP]
            ).total_seconds()

        if DFColumns.VOLUME_DIFFERENCE in df.columns:
            result[DFColumns.VOLUME_DIFFERENCE] = df.loc[0, DFColumns.VOLUME_DIFFERENCE]

        if OrderField.TRX_DTL_TR_CAPACITY in df.columns:
            result[DFColumns.FRONT_RUNNING_TRADING_CAPACITY] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                OrderField.TRX_DTL_TR_CAPACITY,
            ][0]

        if OrderField.META_KEY in df.columns:
            result[DFColumns.FRONT_RUN_ORDERS] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN, OrderField.META_KEY
            ].tolist()

            result[DFColumns.FRONT_RUNNING_ORDER] = df.loc[
                df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING, OrderField.META_KEY
            ][0]

        if OrderField.TRD_ALGO_FIRM_DESKS in df.columns:
            result[DFColumns.FRONT_RUNNING_ORDERS_DESKS] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                    OrderField.TRD_ALGO_FIRM_DESKS,
                ]
                .unique()
                .tolist()
            )
            result[DFColumns.FRONT_RUN_ORDERS_DESK] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                    OrderField.TRD_ALGO_FIRM_DESKS,
                ]
                .unique()
                .tolist()
            )

        scenario = Scenario(result=result, context=self.context)
        self.scenarios.append(scenario)

    def populate_alert_trader_fields(
        self, result: Dict[str, str], df: pd.DataFrame
    ) -> Dict[str, str]:
        """Verifies if trader data exists and populates it in the alert
        dictionary.

        :param result: alert dictionary
        :param df: dataframe with alert data
        :return: alert dictionary with populated trader fields
        """

        trader_column = None
        if OrderField.TRADER in df.columns:
            trader_column = OrderField.TRADER

        elif NewColumns.TRADER_NAME in df.columns:
            trader_column = NewColumns.TRADER_NAME

        if trader_column:
            result[DFColumns.FRONT_RUN_ORDERS_TRADERS] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUN,
                    trader_column,
                ]
                .unique()
                .tolist()
            )
            result[DFColumns.FRONT_RUNNING_ORDERS_TRADERS] = (
                df.loc[
                    df[DFColumns.ORDER_TYPE] == OrderType.FRONT_RUNNING,
                    trader_column,
                ]
                .unique()
                .tolist()
            )

        return result
