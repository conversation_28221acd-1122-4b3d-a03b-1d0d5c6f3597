# ruff: noqa: E501
import datetime
import pandas as pd
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.strategy.front_running_client_vs_client.static import (
    EvaluationType,
    QueryProcessingColumns,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import process_numeric_columns
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import Dict, List, Optional


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_evaluation_type = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )

    def get_cases_to_analyse(self):
        """
        fetches data from ES
        :return: yields Dataframes with ES results
        """
        query = self._get_orders_query()
        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start, method_id = self.get_start_time_and_unique_id()
            query = self._get_orders_query(inst_comb=inst_comb)
            query = self.get_initial_query(query)

            results = self._sdp_repository.search_after_query(query=query)

            if results.empty:
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"No Orders were fetched for instruments {inst_comb}.",
                        list_of_instruments=inst_comb,
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=start,
                        end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                        number_of_dropped_orders=0,
                        number_of_input_orders=0,
                        number_of_resulting_orders=0,
                        step_name="Fetching orders/orders states to be analysed by the algo.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                    )
                )
                continue

            trader_results = self._process_column_name(
                df=results, column=QueryProcessingColumns.TRADER
            )
            final_results = self._process_column_name(
                df=trader_results, column=QueryProcessingColumns.DESK
            )

            if self._th_evaluation_type == EvaluationType.CLIENT:
                if OrderField.CLIENT_IDENT_CLIENT_NAME in final_results.columns:
                    final_results = final_results.dropna(
                        subset=[OrderField.CLIENT_IDENT_CLIENT_NAME]
                    )
                else:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            reason=f"Column {OrderField.CLIENT_IDENT_CLIENT_NAME} not present in dataframe for instruments {inst_comb}.",
                            list_of_instruments=inst_comb,
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Fetching orders/orders states to be analysed by the algo.",
                            step_type=StepType.FILTERS,
                            drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                        )
                    )
                    continue

            elif self._th_evaluation_type == EvaluationType.DESK:
                if OrderField.TRD_ALGO_FIRM_DESKS in final_results.columns:
                    final_results = final_results.dropna(subset=[OrderField.TRD_ALGO_FIRM_DESKS])
                else:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            reason=f"Column {OrderField.TRD_ALGO_FIRM_DESKS} not present in dataframe for instruments {inst_comb}.",
                            list_of_instruments=inst_comb,
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=start,
                            end=datetime.datetime.now(datetime.timezone.utc).strftime(
                                DATETIME_FORMAT
                            ),
                            number_of_dropped_orders=0,
                            number_of_input_orders=0,
                            number_of_resulting_orders=0,
                            step_name="Fetching orders/orders states to be analysed by the algo.",
                            step_type=StepType.FILTERS,
                            drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                        )
                    )
                    continue

            final_results: pd.DataFrame = process_numeric_columns(data=final_results)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took "
                f"{(datetime.datetime.now(datetime.timezone.utc) - datetime.datetime.strptime(start, DATETIME_FORMAT)).total_seconds()} seconds"
            )

            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=f"Fetching orders/orders states to be analysed by the algo. For instrument {inst_comb}",
                    list_of_instruments=inst_comb,
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT),
                    number_of_dropped_orders=0,
                    number_of_input_orders=0,
                    number_of_resulting_orders=len(results),
                    step_name="Fetching orders/orders states to be analysed by the algo.",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                )
            )
            yield final_results

    def _get_orders_query(self, inst_comb: List[str] = None) -> OrderQuery:
        """Creates a query for retrieving Orders from ES.

        :param inst_comb: List of instruments to be fetched
        :return:
        """
        q = OrderQuery()

        required = [
            OrderField.DATE,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.META_KEY,
            OrderField.PC_FD_INIT_QTY,
            OrderField.TS_ORD_SUBMITTED,
        ]

        for field in required:
            q.exists(field=field)

        # includes
        includes = [
            *OrderField.get_instrument_fields(),
            OrderField.CLIENT_IDENT_CLIENT,
            OrderField.CLIENT_IDENT_CLIENT_NAME,
            OrderField.DATE,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
            OrderField.INST_FULL_NAME,
            OrderField.META_KEY,
            OrderField.ORD_IDENT_ID_CODE,
            OrderField.PC_FD_INIT_QTY,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TRADER,
            OrderField.TRADER_NAME,
            OrderField.TRD_ALGO_FIRM_DESKS,
            OrderField.TRX_DTL_TR_CAPACITY,
        ]

        q.includes(includes)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    @staticmethod
    def _process_column_name(df: pd.DataFrame, column: str) -> pd.DataFrame:
        """Transforms the column with a list with trader names into str.

        :param df:
        :return:
        """
        name_column_dict = {
            "trader": OrderField.TRADER,
            "desk": OrderField.TRD_ALGO_FIRM_DESKS,
        }
        name_column = name_column_dict.get(column)

        def extract_trader_name(trader_list: List[Dict]) -> Optional[str]:
            if isinstance(trader_list, list):
                trader_dict = trader_list[0]
                trader_name = trader_dict["name"]
                return trader_name
            else:
                return pd.NA

        if name_column not in df.columns:
            return df

        trader_mask = df[name_column].notnull()

        if not trader_mask.any():
            return df

        df.loc[trader_mask, name_column] = df.loc[trader_mask, name_column].apply(
            lambda x: extract_trader_name(trader_list=x)
        )

        return df

    def fetch_client_id_name(self, meta_keys: List[str]) -> pd.DataFrame:
        """Fetch client name for NEWOs.

        :param meta_keys: list of string woth the &key from orders
        :return: dataframe with the client column
        """
        query = OrderQuery()

        query.key(meta_keys)

        query.includes(
            [
                OrderField.CLIENT_IDENT_CLIENT_NAME,
                OrderField.META_KEY,
                OrderField.TRADER,
            ]
        )

        results = self._sdp_repository.search_after_query(query=query)

        if (
            OrderField.CLIENT_IDENT_CLIENT_NAME not in results.columns
            or results.empty
            or (
                OrderField.CLIENT_IDENT_CLIENT_NAME in results.columns
                and results.dropna(subset=[OrderField.CLIENT_IDENT_CLIENT_NAME]).empty
            )
        ):
            return pd.DataFrame()

        client_results = self._process_column_name(df=results, column=QueryProcessingColumns.TRADER)

        client_results = client_results.set_index(OrderField.META_KEY, drop=True)

        return client_results.loc[:, OrderField.CLIENT_IDENT_CLIENT_NAME]
