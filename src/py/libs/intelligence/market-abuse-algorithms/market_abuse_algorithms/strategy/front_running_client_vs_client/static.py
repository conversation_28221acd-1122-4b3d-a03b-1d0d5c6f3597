from enum import Enum


class ThresholdsNames(str, Enum):
    EVALUATION_TYPE = "evaluationType"
    FRONT_RUNNING_VOLUME_DIFFERENCE = "frontRunningVolumeDifference"
    FRONT_RUN_ORDER_VOLUME = "frontRunOrderVolume"
    TIME_WINDOW = "timeWindow"


class DFColumns:
    ASSET_CLASS = "assetClass"
    BUY_SELL = "buySell"
    INSTRUMENT_NAME = "instrumentName"
    FRONT_RUN_ORDERS = "frontRunOrders"
    FRONT_RUN_ORDERS_CLIENTS = "frontRunOrdersClients"
    FRONT_RUN_ORDERS_COUNT = "frontRunOrdersCount"
    FRONT_RUN_ORDERS_DESK = "frontRunDesk"
    FRONT_RUN_ORDERS_QUANTITY = "frontRunOrdersQuantity"
    FRONT_RUN_ORDERS_TIMESTAMP = "frontRunOrdersTimestamp"
    FRONT_RUN_ORDERS_TRADERS = "frontRunOrdersTraders"
    FRONT_RUNNING_CAPTURE_WINDOW = "frontRunningCaptureWindow"
    FRONT_RUNNING_ORDER_TIMESTAMP = "frontRunningOrderTimestamp"
    FRONT_RUNNING_ORDER = "frontRunningOrder"
    FRONT_RUNNING_ORDERS_CLIENTS = "frontRunningOrdersClients"
    FRONT_RUNNING_ORDERS_DESKS = "frontRunningOrderDesks"
    FRONT_RUNNING_ORDERS_TRADERS = "frontRunningOrdersTraders"
    FRONT_RUNNING_ORDERS_QUANTITY = "frontRunningOrdersQuantity"
    FRONT_RUNNING_TRADING_CAPACITY = "frontRunningTradingCapacity"
    ORDER_TYPE = "orderType"
    TIME_DIFFERENCE = "timeDifference"
    VOLUME_DIFFERENCE = "volumeDifference"


class OrderType(str, Enum):
    FRONT_RUN = "frontRun"
    FRONT_RUNNING = "frontRunning"


class EvaluationType(str, Enum):
    CLIENT = "client"
    DESK = "desk"


class QueryProcessingColumns:
    DESK = "desk"
    TRADER = "trader"
