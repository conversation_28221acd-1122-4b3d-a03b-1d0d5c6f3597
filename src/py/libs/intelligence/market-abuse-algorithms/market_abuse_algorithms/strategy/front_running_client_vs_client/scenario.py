import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from market_abuse_algorithms.strategy.front_running_client_vs_client.static import (
    DFColumns,
)


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["frontRunningOrder"], multiple=["frontRunOrders"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "frontRunningOrder": self._result.pop(DFColumns.FRONT_RUNNING_ORDER),
            "frontRunOrders": self._result.pop(DFColumns.FRONT_RUN_ORDERS),
        }

        scenario = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(scenario)
        return scenario
