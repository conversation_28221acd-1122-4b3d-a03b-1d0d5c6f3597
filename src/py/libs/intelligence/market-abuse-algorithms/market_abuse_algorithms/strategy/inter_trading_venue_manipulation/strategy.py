# ruff: noqa: E501
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import <PERSON><PERSON>ell, NewColumns, OrderField
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyLog, StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.inter_trading_venue_manipulation.models import (
    Thresholds,
)
from market_abuse_algorithms.strategy.inter_trading_venue_manipulation.query import (
    Queries,
)
from market_abuse_algorithms.strategy.inter_trading_venue_manipulation.scenario import (
    Scenario,
)
from market_abuse_algorithms.strategy.inter_trading_venue_manipulation.static import (
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.utils import get_involved_parties
from pandas.tseries.offsets import BDay


class Strategy(AbstractStrategy):
    """Inter Trading Venue Manipulation.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-212
        - https://steeleye.atlassian.net/browse/PR-622
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.INTER_TRADING_VENUE_MANIPULATION,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_time_window = self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)

    def _apply_strategy(self):
        for data in self.queries.cases_to_analyse():
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data):
        for instrument_id, group in data.groupby(NewColumns.INSTRUMENT_CODE):
            self._logger.info(StrategyLog.TOP_LEVEL.format(info=f"Instrument: {instrument_id}"))

            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                # Check if there are buys in orders for the instrument
                if BuySell.BUY not in group[OrderField.EXC_DTL_BUY_SELL_IND].unique():
                    self._logger.info("No orders to buy.")
                    continue

                start_date = pd.to_datetime(
                    group[OrderField.TS_ORD_SUBMITTED].min() - BDay(30), format="mixed"
                )
                end_date = pd.to_datetime(
                    group[OrderField.TS_ORD_SUBMITTED].max()
                    + pd.Timedelta(seconds=self._th_time_window),
                    format="mixed",
                )

                executions = self.queries.get_executions(
                    instrument_id=instrument_id,
                    start_date=start_date,
                    end_date=end_date,
                )

                if executions.empty:
                    self._logger.info(
                        f"No executions found between {start_date.isoformat()} and {end_date.isoformat()}"
                    )
                    continue

                # There is only one venue to analyse
                if (
                    len(
                        set(group[NewColumns.VENUE].unique())
                        | set(executions[NewColumns.VENUE].unique())
                    )
                    == 1
                ):
                    continue

                self._run_algo(orders=group, executions=executions)

    def _run_algo(self, orders, executions):
        results = self._algo(orders=orders, executions=executions)

        self._logger.info(StrategyLog.N_RESULTS_FOUND.format(n=len(results)))

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def _algo(self, orders, executions):
        results = []

        for order_venue, orders_venue_group in orders.groupby(NewColumns.VENUE):
            with StrategyError.handle_algo_records_error(
                audit=self._audit, data=orders_venue_group
            ):
                executions_diff_venue = executions[executions[NewColumns.VENUE] != order_venue]

                if executions_diff_venue.empty:
                    self._logger.info("There were no executions in other venues.")
                    continue

                for order_key, order in orders_venue_group.groupby(OrderField.META_KEY):
                    order = order.squeeze()

                    ts = order[OrderField.TS_ORD_SUBMITTED]

                    previous_30th_day = pd.to_datetime(ts - BDay(30), format="mixed")
                    posterior_time = pd.to_datetime(
                        ts + pd.Timedelta(seconds=self._th_time_window), format="mixed"
                    )

                    for exec_venue, executions_venue in executions_diff_venue.groupby(
                        NewColumns.VENUE
                    ):
                        executions_before = executions_venue[
                            (executions_venue[OrderField.TS_TRADING_DATE_TIME] >= previous_30th_day)
                            & (executions_venue[OrderField.TS_TRADING_DATE_TIME] < ts)
                        ]

                        # check trades before order for different venues
                        if executions_before.empty:
                            self._logger.info(
                                f"There were no executions in {exec_venue} between {previous_30th_day.isoformat()} and {ts.isoformat()}"
                            )
                            continue

                        if (
                            BuySell.BUY
                            not in executions_before[OrderField.EXC_DTL_BUY_SELL_IND].unique()
                        ):
                            self._logger.info(
                                f"There were no executions BUY in {exec_venue} between {previous_30th_day.isoformat()} and {ts.isoformat()}"
                            )
                            continue

                        quantities_sum = (
                            executions_before[
                                [
                                    OrderField.EXC_DTL_BUY_SELL_IND,
                                    OrderField.PC_FD_TRD_QTY,
                                ]
                            ]
                            .groupby(OrderField.EXC_DTL_BUY_SELL_IND)
                            .sum()[OrderField.PC_FD_TRD_QTY]
                        )

                        net_quantity = (
                            quantities_sum[BuySell.BUY]
                            if BuySell.SELL not in quantities_sum
                            else quantities_sum[BuySell.BUY] - quantities_sum[BuySell.SELL]
                        )

                        if net_quantity <= 0:
                            self._logger.info(f"Net quantity {net_quantity} <= 0.")
                            continue

                        executions_before_keys = (
                            executions_before.loc[
                                executions_before[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY,
                                OrderField.META_KEY,
                            ]
                            .unique()
                            .tolist()
                        )

                        # Check if quantities were sold on those venues after order analysed submission
                        executions_after = executions_venue[
                            (executions_venue[OrderField.TS_TRADING_DATE_TIME] > ts)
                            & (executions_venue[OrderField.TS_TRADING_DATE_TIME] <= posterior_time)
                            & (executions_venue[OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL)
                        ]

                        if executions_after.empty:
                            self._logger.info(
                                f"There were no executions SELL in other venues (apart from {exec_venue}) between {previous_30th_day.isoformat()} and {ts.isoformat()}"
                            )
                            continue

                        time_difference = (
                            executions_after[OrderField.TS_TRADING_DATE_TIME].max()
                            - order[OrderField.TS_ORD_SUBMITTED]
                        ).total_seconds()

                        executions_after_keys = (
                            executions_after[OrderField.META_KEY].unique().tolist()
                        )

                        result = dict(
                            order=order_key,
                            executions_before=executions_before_keys,
                            executions_after=executions_after_keys,
                            net_quantity=net_quantity,
                            time_difference=time_difference,
                            involved_parties=get_involved_parties(order),
                        )

                        results.append(result)

        return results
