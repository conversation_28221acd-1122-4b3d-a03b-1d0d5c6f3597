import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.query.utils import (
    remove_date_range_from_iris_filters,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    Buy<PERSON>ell,
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery


class Queries(BaseQuery):
    VENUES_FIELDS = [OrderField.TRX_DTL_ULTIMATE_VENUE, OrderField.TRX_DTL_VENUE]

    REQUIRED_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.get_instrument_fields(),
    ]

    INCLUDES_FIELDS = [
        OrderField.META_KEY,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.TS_TRADING_DATE_TIME,
        OrderField.PC_FD_TRD_QTY,
        OrderField.INST_DERIV_UND_INSTS,
        *OrderField.get_instrument_fields(),
        *VENUES_FIELDS,
        *OrderField.get_involved_parties_fields(),
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

    def cases_to_analyse(self):
        query = self._orders_query()

        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._orders_query(inst_comb)
            query = self.get_initial_query(query)

            result = self._sdp_repository.search_after_query(query=query)

            result = self._populate_venue(result)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield result

    @staticmethod
    def _orders_query(inst_comb=None):
        q = OrderQuery()
        q.buy_sell(BuySell.BUY)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    def get_executions(self, instrument_id, start_date, end_date):
        required_fields = self.REQUIRED_FIELDS + [
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.PC_FD_TRD_QTY,
        ]

        filters = remove_date_range_from_iris_filters(self._filters)

        query = OrderExecutionsQuery()
        query.size(self.MINI_BATCH_SIZE)
        query.includes(self.INCLUDES_FIELDS)
        query.order_status([OrderStatus.FILL, OrderStatus.PARF])
        query.instrument_id(instrument_id, check_underlying=True)
        query.add_start_date(start_date, field=OrderField.TS_TRADING_DATE_TIME)
        query.add_end_date(end_date, field=OrderField.TS_TRADING_DATE_TIME)

        query.add_iris_filters(filters)

        self.inspect_required_fields(query, fields=required_fields)
        query = self.get_query_with_required_fields(query, fields=required_fields)

        result = self._sdp_repository.search_after_query(query=query)

        result = self._populate_venue(result)

        return result

    def _populate_venue(self, df: pd.DataFrame) -> pd.DataFrame:
        df[NewColumns.VENUE] = pd.NA

        for col in self.VENUES_FIELDS:
            if col in df.columns:
                df[NewColumns.VENUE] = df[NewColumns.VENUE].fillna(df[col])

        return df
