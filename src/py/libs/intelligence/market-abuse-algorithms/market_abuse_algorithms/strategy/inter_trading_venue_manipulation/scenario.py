import addict
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON><PERSON><PERSON><PERSON>, TradeColumns


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=["order"], multiple=["executionsBefore", "executionsAfter"])

    def _build_scenario(self) -> pd.Series:
        records = addict.Dict()
        records.order = self._result["order"]
        records.executionsBefore = sorted(self._result["executions_before"])
        records.executionsAfter = sorted(self._result["executions_after"])

        top_level = addict.Dict()
        top_level.involvedParties = self._result["involved_parties"]
        top_level.timeDifference = self._result["time_difference"]

        d = addict.Dict()
        d.thresholds = self._thresholds
        d.additionalFields.topLevel = top_level
        d.records = records

        scenario = pd.Series(d.to_dict())

        return scenario
