# ruff: noqa: E501
import pandas as pd
from market_abuse_algorithms.data_source.static.ref_data.trading_hours_report import (
    CoppClarkColumn,
    PhaseType,
)
from market_abuse_algorithms.data_source.static.sdp.base import SDPField
from market_abuse_algorithms.mar_audit.mar_audit import Step<PERSON>udit
from market_abuse_algorithms.strategy.abstract_marking_the_price.query import (
    MarkingThePriceQueries,
)
from market_abuse_algorithms.strategy.abstract_marking_the_price.strategy import (
    MarkingThePriceStrategy,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from market_abuse_algorithms.strategy.marking_the_open.static import DFColumns, OpenType


class Strategy(MarkingThePriceStrategy):
    """Marking The Open.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-693


    NOTE: This strategy inherits from MarkingThePrice strategy, which includes
     the common logic to Marking the Open and Marking The Close.
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.MARKING_THE_OPEN,
            queries=MarkingThePriceQueries,
        )

    def _calc_marking_times(self, data: pd.DataFrame, method_id: str) -> pd.DataFrame:
        data.loc[:, DFColumns.MARKET_TIME_END_LOCAL] = pd.NaT
        data.loc[:, DFColumns.MARKET_TIME_START_LOCAL] = pd.NaT

        data.loc[:, CoppClarkColumn.PHASE_STARTS] = pd.to_datetime(
            data[CoppClarkColumn.PHASE_STARTS], format="mixed"
        )

        orders = []
        for meta_key, original_order_group in data.groupby(SDPField.META_KEY):
            if original_order_group[CoppClarkColumn.PHASE_STARTS].isnull().all():
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        reason=f"All orders with &key={meta_key} were dropped for not having a single value for {CoppClarkColumn.PHASE_STARTS}.",
                        list_of_order_ids=[str(meta_key)],
                    )
                )

                self._logger.info(
                    f"All orders with '&key'='{meta_key}' were dropped for not having a single value for '{CoppClarkColumn.PHASE_STARTS}'."
                )
                continue

            day_trading_t0_mask = original_order_group[CoppClarkColumn.PHASE_TYPE].isin(
                [PhaseType.DAY_TRADING_T0]
            )
            opening_auction_call_mask = original_order_group[CoppClarkColumn.PHASE_TYPE].isin(
                [PhaseType.OPENING_AUCTION_CALL]
            )

            # t-mto-start
            original_order_group.loc[day_trading_t0_mask, DFColumns.MARKET_TIME_START_LOCAL] = (
                original_order_group.loc[day_trading_t0_mask, CoppClarkColumn.PHASE_STARTS].min()
            )
            original_order_group.loc[
                opening_auction_call_mask, DFColumns.MARKET_TIME_START_LOCAL
            ] = original_order_group.loc[
                opening_auction_call_mask, CoppClarkColumn.PHASE_STARTS
            ].min()

            if self._th_marking_type == OpenType.MARKING_THE_OPEN:
                order_group = original_order_group.loc[day_trading_t0_mask]

                if order_group.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            reason=f"Orders dropped after filtering by {CoppClarkColumn.PHASE_TYPE} "
                            f"{PhaseType.DAY_TRADING_T0} because Marking Type is {OpenType.MARKING_THE_OPEN}.",
                            list_of_order_ids=[str(meta_key)],
                        )
                    )
                    self._logger.info(
                        f"Orders dropped after filtering by {CoppClarkColumn.PHASE_TYPE} "
                        f"{PhaseType.DAY_TRADING_T0} because Marking Type is {OpenType.MARKING_THE_OPEN}."
                    )
                    continue

                # t-mto-end
                order_group[DFColumns.MARKET_TIME_END_LOCAL] = order_group.loc[
                    day_trading_t0_mask, DFColumns.MARKET_TIME_START_LOCAL
                ] + pd.Timedelta(seconds=self._th_lbp)

            elif self._th_marking_type == OpenType.MARKING_THE_OPEN_AUCTION:
                order_group = original_order_group.loc[opening_auction_call_mask]

                if order_group.empty:
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            reason=f"Orders dropped after filtering by {CoppClarkColumn.PHASE_TYPE} "
                            f"{PhaseType.OPENING_AUCTION_CALL} because Marking Type is "
                            f"{OpenType.MARKING_THE_OPEN_AUCTION}.",
                            list_of_order_ids=[str(meta_key)],
                        )
                    )
                    self._logger.info(
                        f"Orders dropped after filtering by {CoppClarkColumn.PHASE_TYPE} "
                        f"{PhaseType.OPENING_AUCTION_CALL} because Marking Type is "
                        f"{OpenType.MARKING_THE_OPEN_AUCTION}."
                    )
                    continue

                # t-mto-end
                phase_ends_mask = (
                    order_group[DFColumns.MARKET_TIME_START_LOCAL]
                    + pd.Timedelta(seconds=self._th_lbp)
                    > order_group[CoppClarkColumn.PHASE_STARTS]
                )

                order_group.loc[phase_ends_mask, DFColumns.MARKET_TIME_END_LOCAL] = order_group.loc[
                    phase_ends_mask, CoppClarkColumn.PHASE_STARTS
                ]

                order_group.loc[~phase_ends_mask, DFColumns.MARKET_TIME_END_LOCAL] = (
                    order_group.loc[~phase_ends_mask, DFColumns.MARKET_TIME_START_LOCAL]
                    + pd.Timedelta(seconds=self._th_lbp)
                )

            elif self._th_marking_type == OpenType.MARKING_THE_OPEN_INC_OPEN_AUCTION:
                order_group = original_order_group
                # t-mtc-end
                order_group.loc[:, DFColumns.MARKET_TIME_END_LOCAL] = order_group[
                    DFColumns.MARKET_TIME_START_LOCAL
                ] + pd.Timedelta(seconds=self._th_lbp)

            else:
                raise ValueError(f"Invalid open type: {self._th_marking_type}")

            orders.append(order_group)

        if not orders:
            self._logger.info("No orders left to work with.")
            return pd.DataFrame()

        data = pd.concat(orders, ignore_index=True)

        cols = [
            CoppClarkColumn.PHASE_STARTS,
            DFColumns.MARKET_TIME_START_LOCAL,
            DFColumns.MARKET_TIME_END_LOCAL,
        ]

        for col in cols:
            data[col] = pd.Series(pd.to_datetime(data[col], format="mixed")).dt.time

        return data

    def _get_phase_types(self):
        if self._th_marking_type == OpenType.MARKING_THE_OPEN:
            phase_types = [PhaseType.DAY_TRADING_T0]
        elif self._th_marking_type == OpenType.MARKING_THE_OPEN_AUCTION:
            phase_types = [PhaseType.OPENING_AUCTION_CALL]
        elif self._th_marking_type == OpenType.MARKING_THE_OPEN_INC_OPEN_AUCTION:
            phase_types = [PhaseType.OPENING_AUCTION_CALL, PhaseType.DAY_TRADING_T0]
        else:
            raise ValueError(f"Invalid open type: {self._th_marking_type}")

        return phase_types
