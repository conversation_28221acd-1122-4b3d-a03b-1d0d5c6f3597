from enum import Enum
from market_abuse_algorithms.strategy.abstract_marking_the_price.static import (
    DFColumns as _DFColumns,
)


class DFColumns(_DFColumns):
    OPENING_TIME_END_LOCAL = "openingTimeEndLocal"
    OPENING_TIME_START_LOCAL = "openingTimeStartLocal"


class OpenType(str, Enum):
    MARKING_THE_OPEN = "Marking the open"
    MARKING_THE_OPEN_AUCTION = "Marking the open-auction"
    MARKING_THE_OPEN_INC_OPEN_AUCTION = "Marking the open (including open-auction)"
