import numpy as np
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import Buy<PERSON><PERSON>, OrderField
from market_abuse_algorithms.strategy.base.static import StrategyLog, StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy, StrategyContext
from market_abuse_algorithms.strategy.layering.models import Thresholds
from market_abuse_algorithms.strategy.layering.query import Queries
from market_abuse_algorithms.strategy.layering.scenario import Scenario
from market_abuse_algorithms.strategy.layering.static import (
    DFColumns,
    ParticipantRestriction,
    ThresholdsNames,
)
from se_elastic_schema.static.mifid2 import ValidityPeriod
from typing import Optional, Tuple


class Strategy(AbstractStrategy):
    """Layering.

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/PR-690
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.LAYERING,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        # TODO: convert this to timedelta according to new time threshold structure

        self._th_cancellation_time_window = self.context.thresholds.dict().get(
            ThresholdsNames.CANCELLATION_TIME_WINDOW
        )

        self._th_detect_layering_behaviour = self.context.thresholds.dict().get(
            ThresholdsNames.DETECT_LAYERING_BEHAVIOUR
        )

        self._th_detect_successful_attempt = self.context.thresholds.dict().get(
            ThresholdsNames.DETECT_SUCCESSFUL_ATTEMPT
        )

        self._th_order_time_window = self.context.thresholds.dict().get(
            ThresholdsNames.ORDER_TIME_WINDOW
        )

        self._th_participant_restriction = self.context.thresholds.dict().get(
            ThresholdsNames.PARTICIPANT_RESTRICTION
        )

        self._th_pct_opposite_order_vol = self.context.thresholds.dict().get(
            ThresholdsNames.PCT_OPPOSITE_ORDER_VOLUME
        )

        self._th_pct_price_improvement = self.context.thresholds.dict().get(
            ThresholdsNames.PCT_PRICE_IMPROVEMENT
        )

        self._th_pct_proximity_from_touch = self.context.thresholds.dict().get(
            ThresholdsNames.PCT_PROXIMITY_FROM_TOUCH
        )

        self._th_successful_reverse_trade_window = self.context.thresholds.dict().get(
            ThresholdsNames.SUCCESSFUL_REVERSE_TRADE_WINDOW
        )

        self._th_exc_validity_periods = self.context.thresholds.dict().get(
            ThresholdsNames.EXECUTION_VALIDITY_PERIODS
        )

        self._set_time_window_thresholds()

    def _set_time_window_thresholds(self):
        """
        This method converts time window thresholds to pd.Timedelta
        :return:
        """

        if self._th_order_time_window is not None:
            self._th_order_time_window = pd.Timedelta(
                value=self._th_order_time_window.value,
                unit=self._th_order_time_window.unit.value,
            )

        if self._th_cancellation_time_window is not None:
            self._th_cancellation_time_window = pd.Timedelta(
                value=self._th_cancellation_time_window.value,
                unit=self._th_cancellation_time_window.unit.value,
            )

        if self._th_successful_reverse_trade_window is not None:
            self._th_successful_reverse_trade_window = pd.Timedelta(
                value=self._th_successful_reverse_trade_window.value,
                unit=self._th_successful_reverse_trade_window.unit.value,
            )

    def _apply_strategy(self):
        for data in self.queries.get_cases_to_analyse():
            self._apply_strategy_mini_batch(data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        data.groupby(
            [OrderField.INST_ID_CODE, OrderField.EXC_DTL_BUY_SELL_IND], as_index=False
        ).apply(self._apply_strategy_mini_batch_group)

    def _apply_strategy_mini_batch_group(self, df: pd.DataFrame):
        inst_id = df[OrderField.INST_ID_CODE].unique().item()
        buy_sell_ind = df[OrderField.EXC_DTL_BUY_SELL_IND].unique().item()
        opposite_side = BuySell.get_opposite_side(buy_sell_ind)

        self._logger.info(StrategyLog.TOP_LEVEL.format(info=f"Instrument: {inst_id}"))

        real_orders_data = self._get_real_orders_data(
            df=df, inst_id=inst_id, opposite_side=opposite_side
        )

        if real_orders_data.empty:
            return

        if self._th_participant_restriction is None:
            self._run_algo(fictitious_orders_data=df, real_orders_data=real_orders_data)
            return

        if self._th_participant_restriction == ParticipantRestriction.TRADER:
            field = OrderField.TRADER_FILE_IDENTIFIER
        elif self._th_participant_restriction == ParticipantRestriction.CLIENT:
            field = OrderField.CLIENT_FILE_IDENTIFIER
        else:
            raise ValueError(
                f"Participant restriction not valid: {self._th_participant_restriction}"
            )

        if df[field].dropna().empty:
            return

        df.groupby(field, as_index=False).apply(
            lambda x: self._run_algo(
                fictitious_orders_data=x,
                real_orders_data=real_orders_data,
                participant_field=field,
            )
        )

    def _get_real_orders_data(
        self, df: pd.DataFrame, inst_id: str, opposite_side: str
    ) -> pd.DataFrame:
        underlying_insts = (
            df[OrderField.INST_EXT_UNDER_INST]
            .dropna()
            .apply(lambda x: [y.get("instrumentIdCode") for y in x])
            if OrderField.INST_EXT_UNDER_INST in df.columns
            else []
        )

        deriv_underlying_index_name = (
            (df[OrderField.INST_DERIV_UND_INDEX_NAME].dropna().unique().tolist())
            if OrderField.INST_DERIV_UND_INDEX_NAME in df.columns
            else []
        )

        min_ts = df[OrderField.TS_ORD_SUBMITTED].min() - self._th_order_time_window

        max_ts = df[OrderField.TS_ORD_SUBMITTED].max() + self._th_order_time_window

        result = self.queries.get_real_orders(
            instrument_id=inst_id,
            ext_underlying_insts=underlying_insts,
            underlying_index_name=deriv_underlying_index_name,
            min_ts=min_ts,
            max_ts=max_ts,
            opposite_side=opposite_side,
        )

        return result

    def _run_algo(
        self,
        fictitious_orders_data: pd.DataFrame,
        real_orders_data: pd.DataFrame,
        participant_field: Optional[str] = None,
    ):
        fictitious_orders_data, real_orders_data = self._process_data_for_algo(
            fictitious_orders_data=fictitious_orders_data,
            real_orders_data=real_orders_data,
            participant_field=participant_field,
        )

        if real_orders_data.empty:
            return

        results = self._algo(
            fictitious_orders_data=fictitious_orders_data,
            real_orders_data=real_orders_data,
        )

        if results.empty:
            return

        self._create_scenarios(df=results)

    def _algo(
        self, fictitious_orders_data: pd.DataFrame, real_orders_data: pd.DataFrame
    ) -> pd.DataFrame:
        # Sort fictitious_orders_data frames by TS Order Submitted

        fictitious_orders_data = (
            fictitious_orders_data.sort_values(by=OrderField.TS_ORD_SUBMITTED)
            .reset_index()
            .drop(columns=["index"])
        )

        real_orders_data = (
            real_orders_data.sort_values(by=OrderField.TS_ORD_SUBMITTED)
            .reset_index()
            .drop(columns=["index"])
        )

        # New threshold for PR-1822
        if self._th_exc_validity_periods:
            fictitious_orders_data, real_orders_data = self.check_validity_periods(
                fictitious_data=fictitious_orders_data, real_data=real_orders_data
            )

        if fictitious_orders_data.empty or real_orders_data.empty:
            self._logger.debug("After applying the validity period mask, the dataframe is empty.")
            return pd.DataFrame()
        cols = [OrderField.TS_ORD_SUBMITTED, OrderField.EXC_DTL_BUY_SELL_IND]

        df = fictitious_orders_data[cols].groupby(cols).count().reset_index()

        # Add instrument columns to df
        for col in [OrderField.INST_ID_CODE, OrderField.INST_FULL_NAME]:
            data_col = fictitious_orders_data[col].dropna().str.upper()
            df[col] = data_col.unique()[0] if data_col.nunique() > 1 else data_col.unique().item()

        # Order Submitted TS + Time Window
        df[DFColumns.ORDER_TS_PLUS_OTW] = (
            df[OrderField.TS_ORD_SUBMITTED] + self._th_order_time_window
        )

        df[OrderField.EXC_DTL_VALIDITY_PERIOD] = fictitious_orders_data[
            OrderField.EXC_DTL_VALIDITY_PERIOD
        ]

        # Fictitious orders
        df[DFColumns.FICT_ORDERS_KEYS] = df[
            [
                OrderField.TS_ORD_SUBMITTED,
                OrderField.EXC_DTL_BUY_SELL_IND,
                DFColumns.ORDER_TS_PLUS_OTW,
            ]
        ].apply(
            lambda x: fictitious_orders_data.loc[
                (
                    fictitious_orders_data[OrderField.TS_ORD_SUBMITTED]
                    >= x[OrderField.TS_ORD_SUBMITTED]
                )
                & (
                    fictitious_orders_data[OrderField.TS_ORD_SUBMITTED]
                    <= x[DFColumns.ORDER_TS_PLUS_OTW]
                ),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        # Filter cases with fictitious orders
        with_fictitious_orders_mask = df[DFColumns.FICT_ORDERS_KEYS].apply(len) > 0

        df = df.loc[with_fictitious_orders_mask]

        if df.empty:
            return pd.DataFrame()

        # Max order TS
        df[DFColumns.FICT_ORDERS_MAX_TS] = df[DFColumns.FICT_ORDERS_KEYS].apply(
            lambda x: fictitious_orders_data.loc[
                fictitious_orders_data[OrderField.META_KEY].isin(x),
                OrderField.TS_ORD_SUBMITTED,
            ].max()
        )

        # Min order TS
        df[DFColumns.FICT_ORDERS_MIN_TS] = df[DFColumns.FICT_ORDERS_KEYS].apply(
            lambda x: fictitious_orders_data.loc[
                fictitious_orders_data[OrderField.META_KEY].isin(x),
                OrderField.TS_ORD_SUBMITTED,
            ].min()
        )

        # Max order TS + OTW
        df[DFColumns.FICT_ORDERS_MAX_TS_PLUS_OTW] = (
            df[DFColumns.FICT_ORDERS_MAX_TS] + self._th_order_time_window
        )

        # Min order TS - OTW
        df[DFColumns.FICT_ORDERS_MIN_TS_MINUS_OTW] = (
            df[DFColumns.FICT_ORDERS_MIN_TS] - self._th_order_time_window
        )

        # Real orders keys
        df[DFColumns.REAL_ORDERS_KEYS] = df[
            [
                DFColumns.FICT_ORDERS_MIN_TS_MINUS_OTW,
                DFColumns.FICT_ORDERS_MAX_TS_PLUS_OTW,
                OrderField.EXC_DTL_BUY_SELL_IND,
            ]
        ].apply(
            lambda x: real_orders_data.loc[
                (
                    real_orders_data[OrderField.TS_ORD_SUBMITTED]
                    >= x[DFColumns.FICT_ORDERS_MIN_TS_MINUS_OTW]
                )
                & (
                    real_orders_data[OrderField.TS_ORD_SUBMITTED]
                    <= x[DFColumns.FICT_ORDERS_MAX_TS_PLUS_OTW]
                ),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        # Real orders ids
        df[DFColumns.REAL_ORDERS_IDS] = self._add_ids_column(
            df=df, data=real_orders_data, df_field=DFColumns.REAL_ORDERS_KEYS
        )

        # Filter cases with real orders
        with_real_orders_mask = df[DFColumns.REAL_ORDERS_KEYS].apply(len) > 0

        df = df.loc[with_real_orders_mask]

        if df.empty:
            return pd.DataFrame()

        # Calculate opposite order volume
        df = self._calc_opposite_order_volume(
            df=df,
            fictitious_orders_data=fictitious_orders_data,
            real_orders_data=real_orders_data,
        )

        # Check opposite order volume
        opposite_order_vol_check_mask = (
            df[DFColumns.OPPOSITE_ORDER_VOLUME] >= self._th_pct_opposite_order_vol
        )

        df = df.loc[opposite_order_vol_check_mask]

        if df.empty:
            return pd.DataFrame()

        # Get real orders executions data
        real_orders_executions_data = self._get_real_orders_executions_data(df=df)

        # Check detect successful attempt
        if self._th_detect_successful_attempt:
            # Detect successful attempt
            df = self._calc_detect_successful_attempt(
                df=df, real_orders_executions_data=real_orders_executions_data
            )

            detect_successful_attempt_mask = (
                df[DFColumns.SUCCESSFUL_REAL_ORDERS_EXECUTIONS].apply(len) > 0
            )

            df = df.loc[detect_successful_attempt_mask]

            if df.empty:
                return pd.DataFrame()

        # Update fictitious orders with price fields
        fictitious_orders_data = self._update_fictitious_orders_price_fields(
            df=df, fictitious_orders_data=fictitious_orders_data
        )

        # Detect layering behaviour
        df = self._calc_detect_layering_behaviour(
            df=df, fictitious_orders_data=fictitious_orders_data
        )

        # Check detect layering behaviour
        if self._th_detect_layering_behaviour:
            df = df.loc[df[DFColumns.LAYERING_BEHAVIOUR]]

            if df.empty:
                return pd.DataFrame()

        # Proximity from touch
        df = self._calc_proximity_from_touch(df=df, fictitious_orders_data=fictitious_orders_data)

        # Check proximity from touch
        if self._th_pct_proximity_from_touch:
            proximity_from_touch_mask = (
                df[DFColumns.PCT_PROXIMITY_FROM_TOUCH] <= self._th_pct_proximity_from_touch
            )

            df = df.loc[proximity_from_touch_mask]
            if df.empty:
                return pd.DataFrame()

        # Fictitious orders ids
        df[DFColumns.FICT_ORDERS_IDS] = self._add_ids_column(
            df=df, data=fictitious_orders_data, df_field=DFColumns.FICT_ORDERS_KEYS
        )

        fictitious_cancellations_data = self._get_fictitious_cancellations_data(df=df)

        # Cancellation time window
        df = self._calc_cancellation_time_window(
            df=df, fictitious_cancellations_data=fictitious_cancellations_data
        )

        if self._th_cancellation_time_window is not None:
            if df[DFColumns.CANCELLATION_TIME_WINDOW].dropna().empty:
                return pd.DataFrame()

            df = df.dropna(subset=[DFColumns.CANCELLATION_TIME_WINDOW])

            canc_time_window_check_mask = (
                df[DFColumns.CANCELLATION_TIME_WINDOW] <= self._th_cancellation_time_window
            )
            df = df.loc[canc_time_window_check_mask]

            if df.empty:
                return pd.DataFrame()

        # Price improvement
        df = self._calc_price_improvement(
            df=df, real_orders_executions_data=real_orders_executions_data
        )

        if self._th_pct_price_improvement:
            pct_price_improvement_mask = (
                df[DFColumns.PRICE_IMPROVEMENT] >= self._th_pct_price_improvement
            )

            df = df.loc[pct_price_improvement_mask]

        return df

    def check_validity_periods(
        self, fictitious_data: pd.DataFrame, real_data: pd.DataFrame
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Check validity periods threshold.

        :param self:
        :param fictitious_data:
        :param real_data:
        :return:
        """

        if OrderField.EXC_DTL_VALIDITY_PERIOD in fictitious_data.columns:
            fictitious_data = self.exclude_validity_periods(dataset_to_exclude=fictitious_data)

        if OrderField.EXC_DTL_VALIDITY_PERIOD in real_data.columns:
            real_data = self.exclude_validity_periods(dataset_to_exclude=real_data)
        return fictitious_data, real_data

    def exclude_validity_periods(self, dataset_to_exclude: pd.DataFrame) -> pd.DataFrame:
        """
        :param dataset_to_exclude:
        :return:
        """
        exc_validity_periods_mask = dataset_to_exclude[OrderField.EXC_DTL_VALIDITY_PERIOD].apply(
            lambda x: self.validate_validity_periods_mask(validity_period_row=x)
        )

        dataset_excluded = dataset_to_exclude.loc[~exc_validity_periods_mask]

        return dataset_excluded

    @staticmethod
    def validate_validity_periods_mask(validity_period_row):
        """check if the row has the values FOKV or IOCV.

        :param validity_period_row:
        :return:
        """

        if (
            pd.notna(validity_period_row)
            and isinstance(validity_period_row, list)
            and (
                ValidityPeriod.FOKV in validity_period_row
                or ValidityPeriod.IOCV in validity_period_row
            )
        ):
            return True
        elif (
            pd.notna(validity_period_row)
            and isinstance(validity_period_row, str)
            and (
                ValidityPeriod.FOKV == validity_period_row
                or ValidityPeriod.IOCV == validity_period_row
            )
        ):
            return True

        return False

    @staticmethod
    def _add_ids_column(df: pd.DataFrame, data: pd.DataFrame, df_field: str) -> pd.Series:
        orders_key_id_map = dict(zip(data[OrderField.META_KEY], data[OrderField.META_ID]))

        ids_series = df[df_field].apply(lambda x: list(map(orders_key_id_map.get, x)))

        return ids_series

    @staticmethod
    def _process_data_for_algo(
        fictitious_orders_data: pd.DataFrame,
        real_orders_data: pd.DataFrame,
        participant_field: Optional[str] = None,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        def drop_duplicated_records(df: pd.DataFrame) -> pd.DataFrame:
            cols_with_lists_mask = df.columns.isin(
                [
                    OrderField.TRADER_FILE_IDENTIFIER,
                    OrderField.CLIENT_FILE_IDENTIFIER,
                    OrderField.EXC_DTL_VALIDITY_PERIOD,
                ]
            )
            cols_to_inspect_dupes = df.loc[:, (~cols_with_lists_mask)].columns.tolist()

            df = df.drop(columns=participant_field).drop_duplicates(subset=cols_to_inspect_dupes)

            return df

        if (
            participant_field is not None
            and participant_field in fictitious_orders_data.columns
            and participant_field in real_orders_data.columns
        ):
            participant_id = fictitious_orders_data[participant_field].unique().item()
            participant_mask = real_orders_data[participant_field] == participant_id

            real_orders_data = real_orders_data.loc[participant_mask]

            if real_orders_data.empty:
                return pd.DataFrame(), pd.DataFrame()

            # drop duplicates
            real_orders_data = drop_duplicated_records(df=real_orders_data)
            fictitious_orders_data = drop_duplicated_records(df=fictitious_orders_data)

        return fictitious_orders_data, real_orders_data

    @staticmethod
    def _calc_opposite_order_volume(
        df: pd.DataFrame,
        fictitious_orders_data: pd.DataFrame,
        real_orders_data: pd.DataFrame,
    ):
        # Fictitious orders volume sum
        df[DFColumns.FICT_ORDERS_VOLUME] = df[DFColumns.FICT_ORDERS_KEYS].apply(
            lambda x: fictitious_orders_data.loc[
                fictitious_orders_data[OrderField.META_KEY].isin(x),
                OrderField.PC_FD_INIT_QTY,
            ].sum()
        )

        # Real orders volume sum
        df[DFColumns.REAL_ORDERS_VOLUME] = df[DFColumns.REAL_ORDERS_KEYS].apply(
            lambda x: real_orders_data.loc[
                real_orders_data[OrderField.META_KEY].isin(x), OrderField.PC_FD_INIT_QTY
            ].sum()
        )

        # Opposite Order Volume
        df[DFColumns.OPPOSITE_ORDER_VOLUME] = (
            df[DFColumns.FICT_ORDERS_VOLUME] / df[DFColumns.REAL_ORDERS_VOLUME]
        )

        return df

    def _get_real_orders_executions_data(self, df: pd.DataFrame):
        # Real orders ids
        real_orders_ids = (
            df[[DFColumns.REAL_ORDERS_IDS]]
            .explode(DFColumns.REAL_ORDERS_IDS)[DFColumns.REAL_ORDERS_IDS]
            .unique()
            .tolist()
        )

        real_orders_executions_df = self.queries.get_real_orders_executions_data(
            parent_ids=real_orders_ids
        )

        return real_orders_executions_df

    def _get_fictitious_cancellations_data(self, df: pd.DataFrame) -> pd.DataFrame:
        # Real orders ids
        fictitious_orders_ids = (
            df[[DFColumns.FICT_ORDERS_IDS]]
            .explode(DFColumns.FICT_ORDERS_IDS)[DFColumns.FICT_ORDERS_IDS]
            .unique()
            .tolist()
        )

        fictitious_cancellations_df = self.queries.get_fictitious_cancellations_data(
            parent_ids=fictitious_orders_ids
        )

        return fictitious_cancellations_df

    def _calc_detect_successful_attempt(
        self, df: pd.DataFrame, real_orders_executions_data: pd.DataFrame
    ) -> pd.DataFrame:
        # Max Order TS + SRTW
        df[DFColumns.FICT_ORDERS_MAX_TS_PLUS_SRTW] = (
            df[DFColumns.FICT_ORDERS_MAX_TS] + self._th_successful_reverse_trade_window
        )

        # Min Order TS - SRTW
        df[DFColumns.FICT_ORDERS_MIN_TS_MINUS_SRTW] = (
            df[DFColumns.FICT_ORDERS_MIN_TS] - self._th_successful_reverse_trade_window
        )

        # Successful real orders executions
        if real_orders_executions_data.empty:
            df[DFColumns.SUCCESSFUL_REAL_ORDERS_EXECUTIONS] = np.empty((df.shape[0], 0)).tolist()
            return df

        df[DFColumns.SUCCESSFUL_REAL_ORDERS_EXECUTIONS] = df[
            [
                DFColumns.FICT_ORDERS_MIN_TS_MINUS_SRTW,
                DFColumns.FICT_ORDERS_MAX_TS_PLUS_SRTW,
                DFColumns.REAL_ORDERS_IDS,
            ]
        ].apply(
            lambda x: real_orders_executions_data.loc[
                (
                    real_orders_executions_data[OrderField.META_PARENT].isin(
                        x[DFColumns.REAL_ORDERS_IDS]
                    )
                )
                & (
                    real_orders_executions_data[OrderField.TS_TRADING_DATE_TIME]
                    >= x[DFColumns.FICT_ORDERS_MIN_TS_MINUS_SRTW]
                )
                & (
                    real_orders_executions_data[OrderField.TS_TRADING_DATE_TIME]
                    <= x[DFColumns.FICT_ORDERS_MAX_TS_PLUS_SRTW]
                ),
                OrderField.META_KEY,
            ].tolist(),
            axis=1,
        )

        return df

    def _update_fictitious_orders_price_fields(
        self, df: pd.DataFrame, fictitious_orders_data: pd.DataFrame
    ) -> pd.DataFrame:
        fictitious_orders_keys = (
            df[[DFColumns.FICT_ORDERS_KEYS]]
            .explode(DFColumns.FICT_ORDERS_KEYS)[DFColumns.FICT_ORDERS_KEYS]
            .unique()
            .tolist()
        )

        fic_keys_under_analysis_mask = fictitious_orders_data[OrderField.META_KEY].isin(
            fictitious_orders_keys
        )

        fictitious_orders_data = fictitious_orders_data.loc[fic_keys_under_analysis_mask].set_index(  # noqa: E501
            OrderField.META_KEY, drop=False
        )

        side = fictitious_orders_data[OrderField.EXC_DTL_BUY_SELL_IND].unique().item()

        price_fields_data = self.queries.get_prices_for_fictitious_orders(
            keys=fictitious_orders_keys, side=side
        )

        fictitious_orders_data = pd.concat([fictitious_orders_data, price_fields_data], axis=1)

        return fictitious_orders_data

    @staticmethod
    def _calc_detect_layering_behaviour(
        df: pd.DataFrame, fictitious_orders_data: pd.DataFrame
    ) -> pd.DataFrame:
        def get_data(x):
            return fictitious_orders_data.loc[
                fictitious_orders_data[OrderField.META_KEY].isin(x),
                DFColumns.NEWO_PRICE,
            ]

        side = df[OrderField.EXC_DTL_BUY_SELL_IND].unique().item()
        # If the dataframe has only 1 row, the method is_monotonic won't work the way we need
        # It should return false, instead is returning true. Based on this, we added this id clause.  # noqa: E501
        if df.shape[0] == 1:
            df[DFColumns.LAYERING_BEHAVIOUR] = False
        else:
            if side == BuySell.BUY:
                df[DFColumns.LAYERING_BEHAVIOUR] = df[DFColumns.FICT_ORDERS_KEYS].apply(
                    lambda x: get_data(x).is_monotonic_decreasing and get_data(x).is_unique
                )

            elif side == BuySell.SELL:
                df[DFColumns.LAYERING_BEHAVIOUR] = df[DFColumns.FICT_ORDERS_KEYS].apply(
                    lambda x: get_data(x).is_monotonic_increasing and get_data(x).is_unique
                )

        return df

    @staticmethod
    def _calc_proximity_from_touch(
        df: pd.DataFrame, fictitious_orders_data: pd.DataFrame
    ) -> pd.DataFrame:
        # TODO: needs data to be tested
        def proximity_from_touch_formula(df_: pd.DataFrame) -> float:
            x = (1 - (df_[DFColumns.NEWO_PRICE] / df_[DFColumns.TOUCH_PRICE])).abs().mean()
            return x

        df[DFColumns.PCT_PROXIMITY_FROM_TOUCH] = df[DFColumns.FICT_ORDERS_KEYS].apply(
            lambda x: proximity_from_touch_formula(
                fictitious_orders_data.loc[fictitious_orders_data[OrderField.META_KEY].isin(x)]
            )
        )

        return df

    @staticmethod
    def _calc_cancellation_time_window(
        df: pd.DataFrame, fictitious_cancellations_data: pd.DataFrame
    ) -> pd.DataFrame:
        if fictitious_cancellations_data.empty:
            df[DFColumns.CANCELLATION_TIME_WINDOW] = pd.NaT
            return df

        df[DFColumns.FICT_CANC_ORDERS_KEYS] = df[DFColumns.FICT_ORDERS_IDS].apply(
            lambda x: fictitious_cancellations_data.loc[
                fictitious_cancellations_data[OrderField.META_PARENT].isin(x),
                OrderField.META_KEY,
            ]
            .dropna()
            .tolist()
        )

        df[DFColumns.ORDER_CANC_MAX_TS] = df[DFColumns.FICT_ORDERS_IDS].apply(
            lambda x: fictitious_cancellations_data.loc[
                fictitious_cancellations_data[OrderField.META_PARENT].isin(x),
                OrderField.TS_ORD_UPDATED,
            ].max()
        )

        df[DFColumns.CANCELLATION_TIME_WINDOW] = (
            df[DFColumns.ORDER_CANC_MAX_TS] - df[DFColumns.FICT_ORDERS_MAX_TS]
        )

        return df

    @staticmethod
    def _calc_price_improvement(df: pd.DataFrame, real_orders_executions_data: pd.DataFrame):
        def price_improvement_formula(df_: pd.DataFrame) -> float:
            x = (
                (
                    1
                    - (
                        df_[OrderField.TRX_DTL_PC_DTL_MID_POINT_PRICE]
                        / df_[OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE]
                    )
                    * 2
                )
                .abs()
                .mean()
            )

            return x

        if real_orders_executions_data.empty:
            df[DFColumns.PRICE_IMPROVEMENT] = np.nan
            return df

        df[DFColumns.PRICE_IMPROVEMENT] = df[DFColumns.REAL_ORDERS_IDS].apply(
            lambda x: price_improvement_formula(
                real_orders_executions_data.loc[
                    real_orders_executions_data[OrderField.META_PARENT].isin(x)
                ]
            )
        )

        return df

    def _create_scenarios(self, df: pd.DataFrame):
        df = df.rename(
            columns={
                OrderField.EXC_DTL_BUY_SELL_IND: DFColumns.FICT_ORDERS_SIDE,
                OrderField.INST_FULL_NAME: DFColumns.INSTRUMENT_FULL_NAME,
                OrderField.INST_ID_CODE: DFColumns.INSTRUMENT_ID_CODE,
            }
        )

        df = df.loc[:, df.columns.intersection(DFColumns.get_cols_to_scenario())]

        # Numbers of
        df[DFColumns.NUMBER_OF_FICTITIOUS_ORDERS] = (
            df[DFColumns.FICT_ORDERS_KEYS].dropna().apply(len)
        )

        if DFColumns.FICT_CANC_ORDERS_KEYS in df.columns:
            df[DFColumns.NUMBER_OF_FICTITIOUS_CANCELLED_ORDERS] = (
                df[DFColumns.FICT_CANC_ORDERS_KEYS].dropna().apply(len)
            )

        df[DFColumns.NUMBER_OF_REAL_ORDERS] = df[DFColumns.REAL_ORDERS_KEYS].dropna().apply(len)

        # Drop columns with all nans
        df = df.dropna(how="all", axis="columns")

        results = df.to_dict(orient="records")

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)
