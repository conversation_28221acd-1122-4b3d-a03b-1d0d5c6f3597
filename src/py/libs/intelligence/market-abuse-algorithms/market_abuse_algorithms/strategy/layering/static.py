from enum import Enum
from typing import List


class ThresholdsNames:
    CANCELLATION_TIME_WINDOW = "cancellationTimeWindow"
    DETECT_LAYERING_BEHAVIOUR = "detectLayeringBehaviour"
    DETECT_SUCCESSFUL_ATTEMPT = "detectSuccessfulAttempt"
    ORDER_TIME_WINDOW = "orderTimeWindow"
    PARTICIPANT_RESTRICTION = "participantRestriction"
    PCT_OPPOSITE_ORDER_VOLUME = "percentageOppositeOrderVolume"
    PCT_PRICE_IMPROVEMENT = "percentagePriceImprovement"
    PCT_PROXIMITY_FROM_TOUCH = "percentageProximityFromTouch"
    SUCCESSFUL_REVERSE_TRADE_WINDOW = "successfulReverseTradeWindow"
    EXECUTION_VALIDITY_PERIODS = "excValidityPeriods"


class DFColumns:
    CANCELLATION_TIME_WINDOW = "cancellationTimeWindow"
    FICT_CANC_ORDERS_KEYS = "fictitiousCancelledOrdersKeys"
    FICT_ORDERS_KEYS = "fictitiousOrdersKeys"
    FICT_ORDERS_IDS = "fictitiousOrdersIds"
    FICT_ORDERS_SIDE = "fictitiousOrdersSide"
    FICT_ORDERS_VOLUME = "fictitiousOrdersVolume"
    FICT_ORDERS_MAX_TS = "fictitiousOrdersMaxTS"
    FICT_ORDERS_MAX_TS_PLUS_OTW = "fictitiousOrdersMaxTSPlusOTW"
    FICT_ORDERS_MAX_TS_PLUS_SRTW = "fictitiousOrdersMaxTSPlusSRTW"
    FICT_ORDERS_MIN_TS = "fictitiousOrdersMinTS"
    FICT_ORDERS_MIN_TS_MINUS_OTW = "fictitiousOrdersMinTSMinusOTW"
    FICT_ORDERS_MIN_TS_MINUS_SRTW = "fictitiousOrdersMinTSMinusSRTW"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    NEWO_PRICE = "newoPrice"
    NUMBER_OF_FICTITIOUS_ORDERS = "numberOfFictitiousOrders"
    NUMBER_OF_FICTITIOUS_CANCELLED_ORDERS = "numberOfFictitiousCancelledOrders"
    NUMBER_OF_REAL_ORDERS = "numberOfRealOrders"
    LAYERING_BEHAVIOUR = "layeringBehaviour"
    OPPOSITE_ORDER_VOLUME = "oppositeOrderVolume"
    ORDER_CANC_MAX_TS = "orderCancelledMaxTS"
    ORDER_TS_PLUS_OTW = "orderTSPlusOTW"
    PCT_PROXIMITY_FROM_TOUCH = "pctProximityFromTouch"
    PRICE_IMPROVEMENT = "priceImprovement"
    REAL_ORDERS_IDS = "realOrdersIds"
    REAL_ORDERS_KEYS = "realOrdersKeys"
    REAL_ORDERS_VOLUME = "realOrdersVolume"
    SUCCESSFUL_REAL_ORDERS_EXECUTIONS = "successfulRealOrdersExecutions"
    TOUCH_PRICE = "touchPrice"
    UNDER_INST_ID_CODE = "underlyingInstrumentIdCode"

    @classmethod
    def get_cols_to_scenario(cls) -> List[str]:
        return [
            cls.CANCELLATION_TIME_WINDOW,
            cls.FICT_CANC_ORDERS_KEYS,
            cls.FICT_ORDERS_IDS,
            cls.FICT_ORDERS_KEYS,
            cls.FICT_ORDERS_SIDE,
            cls.FICT_ORDERS_VOLUME,
            cls.FICT_ORDERS_MAX_TS,
            cls.FICT_ORDERS_MAX_TS_PLUS_OTW,
            cls.FICT_ORDERS_MAX_TS_PLUS_SRTW,
            cls.FICT_ORDERS_MIN_TS,
            cls.FICT_ORDERS_MIN_TS_MINUS_OTW,
            cls.FICT_ORDERS_MIN_TS_MINUS_SRTW,
            cls.INSTRUMENT_FULL_NAME,
            cls.INSTRUMENT_ID_CODE,
            cls.LAYERING_BEHAVIOUR,
            cls.NUMBER_OF_FICTITIOUS_ORDERS,
            cls.NUMBER_OF_FICTITIOUS_CANCELLED_ORDERS,
            cls.NUMBER_OF_REAL_ORDERS,
            cls.OPPOSITE_ORDER_VOLUME,
            cls.ORDER_CANC_MAX_TS,
            cls.PCT_PROXIMITY_FROM_TOUCH,
            cls.PRICE_IMPROVEMENT,
            cls.REAL_ORDERS_IDS,
            cls.REAL_ORDERS_KEYS,
            cls.REAL_ORDERS_VOLUME,
            cls.SUCCESSFUL_REAL_ORDERS_EXECUTIONS,
            cls.TOUCH_PRICE,
        ]


class ParticipantRestriction(str, Enum):
    CLIENT = "Client"
    TRADER = "Trader"
