# ruff: noqa: E501
import pandas as pd
import time
from datetime import timezone
from elasticsearch_dsl.query import Terms
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderBaseQuery
from market_abuse_algorithms.data_source.repository.master_data.client import (
    get_master_data_client,
)
from market_abuse_algorithms.data_source.static.sdp.base import SDPField
from market_abuse_algorithms.data_source.static.sdp.order import (
    Model,
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.data_source.static.sdp.order import (
    NewColumns as SDPMetaOrderColumns,
)
from market_abuse_algorithms.data_source.static.srp.potam_case import PotamCaseField
from market_abuse_algorithms.mar_audit.mar_audit import AggregatedStepAudit
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.potam.static import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StrategyMetaField
from market_abuse_algorithms.utils.processment import get_query_string_to_audit
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import List, Optional, Union


class PotamQuery(BaseQuery):
    include_fields = [
        OrderField.CLIENT_FILE_IDENTIFIER,
        OrderField.CLIENT_IDENT_CLIENT_ID,
        OrderField.CLIENT_IDENT_CLIENT_NAME,
        OrderField.COUNTERPARTY_NAME,
        OrderField.EXC_DTL_ORD_STATUS,
        OrderField.INST_DERIV_UND_INSTS_UND_INST_CODE,
        OrderField.INST_FULL_NAME,
        OrderField.INST_ID_CODE,
        OrderField.META_KEY,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PC_FD_INIT_QTY,
        OrderField.PC_FD_TRD_QTY,
        OrderField.TRADER_FILE_IDENTIFIER,
        OrderField.TRADER_ID,
        OrderField.TS_ORD_RECEIVED,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.TS_ORD_UPDATED,
        OrderField.TS_TRADING_DATE_TIME,
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)
        self.get_master_data_client = get_master_data_client()

    def get_orders_to_analyse(self, query: OrderBaseQuery) -> pd.DataFrame:
        return self._sdp_repository.search_after_query(query)

    def get_potam_cases(self, isins: List[str]) -> pd.DataFrame:
        """

        :param isins:
        :return:
        """
        try:
            return pd.json_normalize(
                self.get_master_data_client.get_cases_from_isins(isins=isins)["results"]
            )
        except Exception as e:
            raise Exception(f"Cannot fetch potam cases: {type(e)} - {e}")

    def isins_from_potam_cases(self) -> List[str]:
        """
        :return: set of unique isins across all PotamCase records
        """
        try:
            return list(self.get_master_data_client.get_isins_from_cases())
        except Exception as e:
            raise Exception(f"Cannot fetch potam cases from isins: {type(e)} - {e}")

    def orders_to_analyse(
        self, isins: List[str] = None, method_id: Optional[str] = None
    ) -> pd.DataFrame:
        """
        :param method_id: uuid to identify the method on the audit
        :param isins:
        :param method_id:
        :return:
        """
        start_step, _ = self.get_start_time_and_unique_id()

        query = OrderBaseQuery(model=Model.ORDER)

        query.order_status([OrderStatus.NEWO, OrderStatus.FILL, OrderStatus.PARF])

        query.includes(self.include_fields)

        market_abuse_audit_object.query = get_query_string_to_audit(query=query)

        if isins:
            query.instrument_id(mode="should", value=isins, check_underlying=True)

        self.add_default_conditions_to_query(query)

        start = time.perf_counter()
        orders = self.get_orders_to_analyse(query=query)
        self._logger.info(
            f"For a instrument combination of size {len(isins)}, it took {time.perf_counter() - start} seconds"
        )

        if orders.empty:
            return orders

        if (
            NewColumns.UNDERLYING_INSTRUMENT_CODE not in orders.columns
            and NewColumns.INSTRUMENT_CODE not in orders.columns
        ):
            return pd.DataFrame()

        if (
            NewColumns.UNDERLYING_INSTRUMENT_CODE in orders.columns
            and NewColumns.INSTRUMENT_CODE in orders.columns
        ):
            is_nan_mask = (
                orders[NewColumns.UNDERLYING_INSTRUMENT_CODE].isna()
                & orders[NewColumns.INSTRUMENT_CODE].isna()
            )
            orders_before_mask = orders[OrderField.META_KEY].tolist()
            orders: pd.DataFrame = orders[~is_nan_mask]

            vars_to_melt: List = [
                SDPMetaOrderColumns.INSTRUMENT_CODE,
                SDPMetaOrderColumns.UNDERLYING_INSTRUMENT_CODE,
            ]

            dropped = list(
                set(orders_before_mask) - set(orders.get(OrderField.META_KEY, pd.Series()).tolist())
            )
            if len(dropped) > 0:
                end_step, _ = self.get_start_time_and_unique_id()
                singleton_audit_object.write_step_audit_locally(
                    step_id=method_id,
                    list_of_order_ids=dropped,
                    reason="Remove orders with empty Instrument Code / Underlying Instrument Code",
                    grouping_id=None,
                    step_type=StepType.FILTERS,
                    step_name=StepNameStatic.RECORDS_RETRIEVAL,
                    start=start_step,
                    end=end_step,
                    number_of_input_orders=len(orders_before_mask),
                    number_of_resulting_orders=len(dropped),
                )
                start_step = end_step

            if orders.empty:
                return orders

        if (
            NewColumns.UNDERLYING_INSTRUMENT_CODE in orders.columns
            and NewColumns.INSTRUMENT_CODE not in orders.columns
        ):
            orders_before_mask = orders[OrderField.META_KEY].tolist()
            orders: pd.DataFrame = orders.dropna(subset=[NewColumns.UNDERLYING_INSTRUMENT_CODE])

            vars_to_melt: List = [
                SDPMetaOrderColumns.UNDERLYING_INSTRUMENT_CODE,
            ]

            dropped = list(
                set(orders_before_mask) - set(orders.get(OrderField.META_KEY, pd.Series()).tolist())
            )
            if len(dropped) > 0:
                end_step, _ = self.get_start_time_and_unique_id()
                singleton_audit_object.write_step_audit_locally(
                    step_id=method_id,
                    list_of_order_ids=dropped,
                    reason="Remove orders with empty Underlying Instrument Code",
                    grouping_id=None,
                    step_type=StepType.FILTERS,
                    step_name=StepNameStatic.RECORDS_RETRIEVAL,
                    start=start_step,
                    end=end_step,
                    number_of_input_orders=len(orders_before_mask),
                    number_of_resulting_orders=len(dropped),
                )
                start_step = end_step

            if orders.empty:
                return orders

        if (
            NewColumns.UNDERLYING_INSTRUMENT_CODE not in orders.columns
            and NewColumns.INSTRUMENT_CODE in orders.columns
        ):
            orders_before_mask = orders[OrderField.META_KEY].tolist()
            orders: pd.DataFrame = orders.dropna(subset=[NewColumns.INSTRUMENT_CODE])

            vars_to_melt: List = [
                SDPMetaOrderColumns.INSTRUMENT_CODE,
            ]

            dropped = list(
                set(orders_before_mask) - set(orders.get(OrderField.META_KEY, pd.Series()).tolist())
            )

            if len(dropped) > 0:
                end_step, new_method_id = self.get_start_time_and_unique_id()
                singleton_audit_object.write_step_audit_locally(
                    step_id=new_method_id,
                    list_of_order_ids=dropped,
                    reason="Remove orders with empty Instrument Code",
                    grouping_id=None,
                    step_type=StepType.FILTERS,
                    step_name=StepNameStatic.RECORDS_RETRIEVAL,
                    start=start_step,
                    end=end_step,
                    number_of_input_orders=len(orders_before_mask),
                    number_of_resulting_orders=len(orders),
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=new_method_id,
                        start=start_step,
                        end=end_step,
                        number_of_dropped_orders=len(dropped),
                        number_of_input_orders=len(orders_before_mask),
                        number_of_resulting_orders=len(orders),
                        step_name=StepNameStatic.RECORDS_RETRIEVAL,
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                    )
                )

            if orders.empty:
                return orders

        # parse timestamp fields
        for column in [
            OrderField.TS_ORD_RECEIVED,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TS_ORD_UPDATED,
            OrderField.TS_TRADING_DATE_TIME,
        ]:
            if column in orders.columns:
                try:
                    orders[column] = pd.to_datetime(orders[column], format="mixed")
                    orders[column] = orders[column].dt.tz_localize(timezone.utc)

                except Exception as e:
                    self._logger.warning(
                        f"It is necessary to use the flag UTC equals True. Error {e.__str__()}"
                    )
                    orders[column] = pd.to_datetime(orders[column], utc=True, format="mixed")

        orders = self.decouple_underlying_isins(orders=orders)
        # add meta fields
        melted_orders = pd.melt(
            orders,
            id_vars=SDPField.META_KEY,
            # all instrument instrument cols that may contain isins
            value_vars=vars_to_melt,
        ).rename(columns={"value": StrategyMetaField.ISIN})

        melted_orders = melted_orders.explode(StrategyMetaField.ISIN)

        orders = pd.merge(melted_orders, orders, on=SDPField.META_KEY)

        # remove redundant cols
        if "variable" in orders.columns:
            orders = orders.drop(columns=["variable"])

        if StrategyMetaField.ISIN in orders.columns:
            orders = (
                orders.dropna(subset=[StrategyMetaField.ISIN]).reset_index().drop(columns=["index"])
            )

        return orders

    def non_potam_hits_matching_order_id(self, hits: pd.DataFrame) -> pd.DataFrame:
        """
        :param hits:
        :return: list of &key for orders with

        return list of &key for orders with matching orderIds from hits
        results exclude
        """
        excluded_keys = hits["&key"].unique().tolist()
        target_order_ids = hits[OrderField.ORD_IDENT_ID_CODE].unique().tolist()
        query = OrderBaseQuery(model=[Model.ORDER, Model.ORDER_STATE])
        query.includes([OrderField.META_KEY, OrderField.ORD_IDENT_ID_CODE])
        query.order_id(target_order_ids)
        query.add_condition(
            mode="must_not", conditions=[Terms(**{OrderField.META_KEY: excluded_keys})]
        )
        orders = self._sdp_repository.search_after_query(query)
        return orders

    def potam_cases_to_analyse(self, isins: list) -> pd.DataFrame:
        potam_results = self.get_potam_cases(isins=isins)
        potam = self.process_frame_result(potam_results)

        if potam.empty:
            self._logger.info("No POTAM cases for further analysis.")
            return potam

        melted = (
            pd.melt(
                potam,
                id_vars=PotamCaseField.CASE_ID,
                value_vars=PotamCaseField.get_isin_fields(),
            )
            .rename(columns={"value": StrategyMetaField.ISIN})[
                [PotamCaseField.CASE_ID, StrategyMetaField.ISIN]
            ]
            .dropna(subset=[StrategyMetaField.ISIN])
        )

        explode_isin_dataset: pd.DataFrame = (
            melted.explode(column="META_ISIN").reset_index().drop(columns="index")
        )

        potam: pd.DataFrame = pd.merge(explode_isin_dataset, potam, on=PotamCaseField.CASE_ID)

        potam: pd.DataFrame = potam.drop_duplicates(
            subset=[
                StrategyMetaField.ISIN,
                PotamCaseField.OFFEREE_PERIOD_COMMENCED,
                PotamCaseField.OFFEROR_PERIOD_IDENTIFIED,
            ]
        )

        # add timezones (for comparing with Order data)
        for col in PotamCaseField.get_datetime_fields():
            if col in potam.columns:
                try:
                    potam[col] = pd.to_datetime(potam[col], format="mixed")
                    potam[col] = potam[col].dt.tz_localize(timezone.utc)
                except Exception as e:
                    self._logger.warning(
                        f"It is necessary to use the flag UTC equals True. Error {e.__str__()}"
                    )
                    potam[col] = pd.to_datetime(potam[col], utc=True, format="mixed")

        # to differentiate from order &key
        potam = potam.rename(columns={SDPField.META_KEY: StrategyMetaField.POTAM_META_KEY})

        return potam

    def decouple_underlying_isins(self, orders: pd.DataFrame) -> pd.DataFrame:
        """process_frame_result is only getting the first underlying ISIN,
        because we assume that 1 ISIN should only have 1 underlying ISIN,
        however there are cases where we can have more than 1 underlying ISIN.
        Here we will get the rest of the underlying ISINs.

        :param orders: pd.Dataframe, orders to be analysed
        :return: pd.Dataframe, with all underlying ISINs
        """

        if OrderField.INST_DERIV_UND_INSTS in orders.columns:
            orders[NewColumns.UNDERLYING_INSTRUMENT_CODE] = orders[
                OrderField.INST_DERIV_UND_INSTS
            ].apply(self.get_list_of_underlying_isins)

            orders[NewColumns.INSTRUMENT_CODE] = orders[NewColumns.INSTRUMENT_CODE].fillna(
                orders[NewColumns.UNDERLYING_INSTRUMENT_CODE]
            )

        return orders

    @staticmethod
    def get_list_of_underlying_isins(underlyings: List[dict]) -> List[dict]:
        """Decouple list of dicts of underlying isins instead of getting the
        first one.

        :param underlyings:
        :return: list of list of underlying isins
        """
        underlyings_isins = []
        if not isinstance(underlyings, float):
            for underlying_dict in underlyings:
                underlyings_isins.append(underlying_dict.get("underlyingInstrumentCode"))
        else:
            return underlyings

        return underlyings_isins

    def extract_detail_info(
        self,
        dataset_off: pd.DataFrame,
        column_to_extract_data: str,
        target_column_to_create: str,
    ) -> pd.Series:
        """
        Get of OFFEROR and OFFEREE information - ISIN, dates
        :param dataset_off: dataset with data from offeror and offerees
        :param column_to_extract_data: dataset column to extract data from
        :param target_column_to_create: dataset column to be created
        :return: dataframe with more data
        """
        potam_result: pd.Series = pd.Series(pd.NA, index=dataset_off.index)

        if (
            target_column_to_create == PotamCaseField.OFFEROR_TTI_ISIN
            or target_column_to_create == PotamCaseField.OFFEROR_PERIOD_IDENTIFIED
        ) & (PotamCaseField.OFFERORS not in dataset_off.columns):
            return potam_result

        mask = dataset_off[column_to_extract_data].notnull()

        if not mask.any():
            return potam_result

        if target_column_to_create in [
            PotamCaseField.OFFREE_TTI_ISIN,
            PotamCaseField.OFFEROR_TTI_ISIN,
        ]:
            potam_result.loc[mask] = dataset_off.loc[mask, column_to_extract_data].apply(
                lambda x: _extract_offerors_offerees_data(
                    offerors_offerees_info=x, field_to_fetch=PotamCaseField.C_ISIN
                )
            )

        elif target_column_to_create == PotamCaseField.OFFEREE_PERIOD_COMMENCED:
            potam_result.loc[mask] = dataset_off.loc[mask, column_to_extract_data].apply(
                lambda x: _extract_offerors_offerees_data(
                    offerors_offerees_info=x,
                    field_to_fetch=PotamCaseField.C_OFFER_PERIOD_COMMENCED,
                )
            )

        elif target_column_to_create == PotamCaseField.OFFEROR_PERIOD_IDENTIFIED:
            potam_result.loc[mask] = dataset_off.loc[mask, column_to_extract_data].apply(
                lambda x: _extract_offerors_offerees_data(
                    offerors_offerees_info=x,
                    field_to_fetch=PotamCaseField.C_OFFEROR_PERIOD_IDENTIFIED,
                )
            )

        else:
            raise AttributeError(f"{target_column_to_create} logic not implemented.")

        return potam_result

    def process_frame_result(self, result: pd.DataFrame) -> pd.DataFrame:
        """Process dataframe with cases returned from master-data-api.

        :param result:
        :return:
        """
        if (
            PotamCaseField.OFFEREES not in result.columns
            and PotamCaseField.OFFERORS not in result.columns
        ):
            return result

        # Decouple by OFFEREES
        if PotamCaseField.OFFEREES in result.columns:
            result: pd.DataFrame = result.explode(PotamCaseField.OFFEREES)

        # Decouple by OFFERORS
        if PotamCaseField.OFFERORS in result.columns:
            result: pd.DataFrame = result.explode(PotamCaseField.OFFERORS)

        result: pd.DataFrame = result.reset_index().drop(columns="index")

        # map important nested fields to root

        result.loc[:, PotamCaseField.OFFREE_TTI_ISIN] = self.extract_detail_info(
            dataset_off=result,
            column_to_extract_data=PotamCaseField.OFFEREES,
            target_column_to_create=PotamCaseField.OFFREE_TTI_ISIN,
        )

        result.loc[:, PotamCaseField.OFFEROR_TTI_ISIN] = self.extract_detail_info(
            dataset_off=result,
            column_to_extract_data=PotamCaseField.OFFERORS,
            target_column_to_create=PotamCaseField.OFFEROR_TTI_ISIN,
        )

        result.loc[:, PotamCaseField.OFFEREE_PERIOD_COMMENCED] = self.extract_detail_info(
            dataset_off=result,
            column_to_extract_data=PotamCaseField.OFFEREES,
            target_column_to_create=PotamCaseField.OFFEREE_PERIOD_COMMENCED,
        )
        result.loc[:, PotamCaseField.OFFEROR_PERIOD_IDENTIFIED] = self.extract_detail_info(
            dataset_off=result,
            column_to_extract_data=PotamCaseField.OFFERORS,
            target_column_to_create=PotamCaseField.OFFEROR_PERIOD_IDENTIFIED,
        )

        # parse datetime fields
        for col in PotamCaseField.get_datetime_fields():
            if col in result.columns:
                result[col] = pd.to_datetime(result[col], format="mixed")
        return result


def _extract_offerors_offerees_data(
    offerors_offerees_info: Union[dict, list],
    field_to_fetch: str,
) -> [List[str], str]:
    """Gets the list of isin for both offeror or offeree.

    :param offerors_offerees_info: dict or list with two ten info
    :param field_to_fetch: name of the field(column) to get from two ten info
    :return: list of isins
    """

    if not offerors_offerees_info or len(offerors_offerees_info) == 0:
        return pd.NA

    if not isinstance(offerors_offerees_info, dict):
        raise AttributeError(
            f"Invalid type of structure for of offerors_offerees_info {offerors_offerees_info}."
        )

    if (
        field_to_fetch == PotamCaseField.C_OFFER_PERIOD_COMMENCED
        or field_to_fetch == PotamCaseField.C_OFFEROR_PERIOD_IDENTIFIED
    ):
        return offerors_offerees_info.get(field_to_fetch)

    two_ten_info = offerors_offerees_info.get(PotamCaseField.C_TWO_TEN_INFORMATION, None)

    if not two_ten_info:
        return pd.NA

    if field_to_fetch == PotamCaseField.C_ISIN:
        return [result.get(field_to_fetch) for result in two_ten_info]
