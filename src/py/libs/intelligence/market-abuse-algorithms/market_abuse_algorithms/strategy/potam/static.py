from enum import Enum


class StepNameStatic:
    CASES_RETRIEVAL = "Retrieval of POTAM Cases"
    CREATING_ALERTS = "Creating Alerts"
    EXTENDED_WINDOW_THRESHOLD = "Application of 'Extended Window' Threshold"
    EXTRACTION = "Extraction of ISIN/Underlying ISIN from POTAM"
    FILTERING_CASES_BY_INCLUDE_FORMAL_SALES = (
        "Filtering POTAM cases by `Include Formal Sales Process` threshold"
    )
    RECORDS_RETRIEVAL = "Retrieval of Order Records related to POTAM Listings"


class AlertColumns:
    COUNTERPARTY_NAME = "counterpartyName"
    INSTRUMENT_NAME = "instrumentFullName"
    INSTRUMENT_ID = "instrumentId"  # add fallback to new columns instrument code
    UNDERLYING_INSTRUMENT_ID = "underlyingInstrumentId"
    ORDERS_QUANTITY = "orderInitialQuantity"  # SUM OF OrderField.PC_FD_INIT_QTY
    TRADES_QUANTITY = "tradedQuantity"  # SUM OF OrderField.PC_FD_TRD_QTY
    ORDER_TS_SUBMITTED = "orderSubmittedTSLocal"
    POTAM_WINDOW_START = "potamWindowStart"
    POTAM_WINDOW_END = "potamWindowEnd"
    POTAM_OFFEROR_NAME = "potamOfferor"
    POTAM_OFFEREE_NAME = "potamOfferee"
    TRADER_NAME = "traderName"


class ThresholdsNames(str, Enum):
    EXTENDED_WINDOW_DAYS_BEFORE = "extendedWindowDaysBefore"
    EXTENDED_WINDOW_DAYS_AFTER = "extendedWindowDaysAfter"
    INCLUDE_FORMAL_SALES_PROCESS = "includeFormalSalesProcess"
    RUN_TYPE = "runType"


class RunTypeMode(str, Enum):
    OUTSIDE = "outside"
    INSIDE = "inside"
    ALL = "all"


class StrategyMetaField:
    """df columns added manually to use while processing data."""

    ISIN = "META_ISIN"  # used to join potam & order dataframes
    HIT_TYPE = "META_HIT_TYPE"
    HIT_RESPONSE_MESSAGE = "META_HIT_RESPONSE_MESSAGE"

    ORDER_META_KEY = "__order_meta_key__"
    POTAM_META_KEY = "__potam_meta_key__"
    IN_POTAM_WINDOW = "InPotamWindow"
    IN_EXTENDED_WINDOW = "InExtendedWindow"
    NON_POTAM_RECORD_KEYS = "__nonPotamOrderRecords__"
    DATE_COLUMN = "dateColumn"

    @classmethod
    def all(cls):
        return [cls.ISIN, cls.HIT_TYPE, cls.HIT_RESPONSE_MESSAGE]


class PotamHitField:
    TYPE = "POTAM_HIT_TYPE"
    RESPONSE_MESSAGE = "POTAM_HIT_RESPONSE_MESSAGE"


class PotamResponseMessage:
    POTAM_WINDOW_DERIV_NEWO = (
        "New Order on a Derivative of the POTAM security detected during the POTAM window"
    )
    POTAM_WINDOW_DERIV_FILL = (
        "Trade on a Derivative of the POTAM security detected during the POTAM window"
    )
    POTAM_WINDOW_DERIV_OTHER = "Order + <orderState (PR-299)> on a Derivative of the POTAM security detected during the POTAM window"  # noqa: E501
    POTAM_WINDOW_DIRECT_NEWO = "New Order on this security detected during the POTAM window"
    POTAM_WINDOW_DIRECT_FILL = "Trade on this security detected during the POTAM window"
    POTAM_WINDOW_DIRECT_OTHER = (
        "Order + <orderState (PR-299)> on this security detected during the POTAM window"
    )

    EXTENDED_WINDOW_DERIV_NEWO = (
        "New Order on a Derivative of the POTAM security detected during the EXTENDED window"
    )
    EXTENDED_WINDOW_DERIV_FILL = (
        "Trade on a Derivative of the POTAM security detected during the EXTENDED window"
    )
    EXTENDED_WINDOW_DERIV_OTHER = "Order + <orderState (PR-299)> on a Derivative of the POTAM security detected during the EXTENDED window"  # noqa: E501
    EXTENDED_WINDOW_DIRECT_NEWO = "New Order on this security detected during the EXTENDED window"
    EXTENDED_WINDOW_DIRECT_FILL = "Trade on this security detected during the EXTENDED window"
    EXTENDED_WINDOW_DIRECT_OTHER = (
        "Order + <orderState (PR-299)> on this security detected during the EXTENDED window"
    )
