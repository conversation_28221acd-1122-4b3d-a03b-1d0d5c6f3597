from enum import StrEnum
from typing import List


class ExistingCurrencies:
    """This class should hold the biggest set of currencies we support based on
    ISO 4217 format Because of historical reasons some currencies are kept on
    the list although they are no longer in ISO4217."""

    AUD = "AUD"
    BGN = "BGN"
    BRL = "BRL"
    CAD = "CAD"
    CHF = "CHF"
    CNY = "CNY"
    CZK = "CZK"
    DKK = "DKK"
    EUR = "EUR"
    GBP = "GBP"
    HKD = "HKD"
    HRK = "HRK"  # Croatian Kuna - from 1994 until 2023
    HUF = "HUF"
    IDR = "IDR"
    ILS = "ILS"
    INR = "INR"
    ISK = "ISK"
    JPY = "JPY"
    KRW = "KRW"
    MXN = "MXN"
    MYR = "MYR"
    NOK = "NOK"
    NZD = "NZD"
    PHP = "PHP"
    PLN = "PLN"
    RON = "RON"
    RUB = "RUB"
    SEK = "SEK"
    SGD = "SGD"
    THB = "THB"
    TRY = "TRY"
    USD = "USD"
    ZAR = "ZAR"

    @classmethod
    def get_currencies(cls) -> List[str]:
        return [
            val_
            for key_, val_ in cls.__dict__.items()
            if not key_.startswith("__") and isinstance(val_, str)
        ]


NotionalCurrency = StrEnum("NotionalCurrency", {k: k for k in ExistingCurrencies.get_currencies()})  # type: ignore
