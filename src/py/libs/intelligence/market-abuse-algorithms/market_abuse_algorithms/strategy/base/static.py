# ruff: noqa: E501
from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import OrderVolume
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from typing import List


class CommonThresholdsNames(str, Enum):
    ASSET_CLASS = "assetClass"


class StrategyName:
    FORCING_THE_MARKET_AGAINST_ITS_WILL = "Forcing the Market Against Its Will"
    FRONT_RUNNING = "Front Running"
    FRONT_RUNNING_CLIENT_VS_CLIENT = "Front Running Client Vs Client"
    FRONT_RUNNING_V2 = "Front Running V2"
    INSIDER_TRADING = "Insider Trading"
    INSIDER_TRADING_WITH_NEWS = "Insider Trading with News"
    INSIDER_TRADING_WITH_NEWS_REFINITIV = "Insider Trading with News - Refinitiv version"
    INSIDER_TRADING_V3 = "Insider Trading V3"
    INSIDER_TRADING_V3_REFINITIV = "Insider Trading V3 - Refinitiv version"
    INTER_TRADING_VENUE_MANIPULATION = "Inter-Trading Venue Manipulation"
    LAYERING = "Layering"
    LAYERING_V2 = "Layering V2"
    LAYERING_V2_LEVEL1 = "Layering V2 Level 1"
    MARKING_THE_CLOSE = "Marking the Close"
    MARKING_THE_OPEN = "Marking the Open"
    MARKING_OPEN_CLOSE_INTRADAY_V2 = "Marking the Open/Close/Intraday V2"
    MESSAGE_VOLUME = "Message Volume"
    PAINTING_THE_TAPE = "Painting the Tape"
    PAINTING_THE_TAPE_V2 = "Painting the Tape V2"
    PARKING = "Parking"
    PHISHING = "Phishing"
    PHISHING_V2 = "Phishing V2"
    PRICE_OUTLIERS = "Price Outliers"
    POTAM = "Potam"
    PUMP_AND_DUMP = "Pump and Dump"
    QUOTE_STUFFING = "Quote Stuffing"
    SPOOFING = "Spoofing"
    SPOOFING_V2 = "Spoofing V2"
    SPOOFING_V2_LEVEL1 = "Spoofing V2 Level 1"
    SUSPICIOUS_LARGE_ORDER_VOLUME = "Suspicious Large Order Volume"
    SUSPICIOUS_LARGE_ORDER_VOLUME_V2 = "Suspicious Large Order Volume V2"
    TRASH_AND_CASH = "Trade and Cash"
    WASH_TRADING = "Wash Trading"
    RESTRICTED_LIST_V2 = "Restricted List V2"

    @classmethod
    def get_report_type(cls, strategy_name):
        report_type_map = {
            cls.FORCING_THE_MARKET_AGAINST_ITS_WILL: MarketAbuseReportType.FORCING_THE_MARKET_AGAINST_ITS_WILL,
            cls.FRONT_RUNNING: MarketAbuseReportType.FRONT_RUNNING,
            cls.FRONT_RUNNING_CLIENT_VS_CLIENT: MarketAbuseReportType.FRONT_RUNNING_CLIENT_VS_CLIENT,
            cls.FRONT_RUNNING_V2: MarketAbuseReportType.FRONT_RUNNING_V2,
            cls.INSIDER_TRADING: MarketAbuseReportType.INSIDER_TRADING,
            cls.INSIDER_TRADING_WITH_NEWS: MarketAbuseReportType.INSIDER_TRADING_NEWS,
            cls.INSIDER_TRADING_WITH_NEWS_REFINITIV: MarketAbuseReportType.INSIDER_TRADING_NEWS_REFINITIV,
            cls.INSIDER_TRADING_V3: MarketAbuseReportType.INSIDER_TRADING_V3,
            cls.INSIDER_TRADING_V3_REFINITIV: MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV,
            cls.INTER_TRADING_VENUE_MANIPULATION: MarketAbuseReportType.INTER_TRADING_VENUE_MANIPULATION,
            cls.LAYERING: MarketAbuseReportType.LAYERING,
            cls.LAYERING_V2: MarketAbuseReportType.LAYERING_V2,
            cls.LAYERING_V2_LEVEL1: MarketAbuseReportType.LAYERING_V2_LEVEL1,
            cls.MARKING_THE_CLOSE: MarketAbuseReportType.MARKING_THE_CLOSE,
            cls.MARKING_THE_OPEN: MarketAbuseReportType.MARKING_THE_OPEN,
            cls.MARKING_OPEN_CLOSE_INTRADAY_V2: MarketAbuseReportType.MARKING_OPEN_CLOSE_INTRADAY_V2,
            cls.MESSAGE_VOLUME: MarketAbuseReportType.MESSAGE_VOLUME,
            cls.PAINTING_THE_TAPE: MarketAbuseReportType.PAINTING_THE_TAPE,
            cls.PAINTING_THE_TAPE_V2: MarketAbuseReportType.PAINTING_THE_TAPE_V2,
            cls.PARKING: MarketAbuseReportType.PARKING,
            cls.PHISHING: MarketAbuseReportType.PHISHING,
            cls.PHISHING_V2: MarketAbuseReportType.PHISHING_V2,
            cls.PRICE_OUTLIERS: MarketAbuseReportType.PRICE_OUTLIERS,
            cls.POTAM: MarketAbuseReportType.POTAM,
            cls.PUMP_AND_DUMP: MarketAbuseReportType.PUMP_AND_DUMP,
            cls.QUOTE_STUFFING: MarketAbuseReportType.QUOTE_STUFFING,
            cls.SPOOFING: MarketAbuseReportType.SPOOFING,
            cls.SPOOFING_V2: MarketAbuseReportType.SPOOFING_V2,
            cls.SPOOFING_V2_LEVEL1: MarketAbuseReportType.SPOOFING_V2_LEVEL1,
            cls.SUSPICIOUS_LARGE_ORDER_VOLUME: MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME,
            cls.SUSPICIOUS_LARGE_ORDER_VOLUME_V2: MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2,
            cls.TRASH_AND_CASH: MarketAbuseReportType.TRASH_AND_CASH,
            cls.WASH_TRADING: MarketAbuseReportType.WASH_TRADING,
            cls.RESTRICTED_LIST_V2: MarketAbuseReportType.RESTRICTED_LIST_V2,
        }

        return report_type_map[strategy_name]


class StrategyLog:
    TOP_LEVEL = (
        "\n################## {info} ##########################################################"
    )
    N_RESULTS_FOUND = "# results found: {n}"
    NO_AVAILABLE_DATA_FOR_PARAMETERS = "No available data for parameters selected."
    NO_MARKET_DATA_FOR_DATA = "No market data for data to analyse."


class ScenarioLog:
    BUILDING = "Building scenario..."
    BUILT = "Scenario built successfully."


class ScenarioFields:
    ADDITIONAL_FIELDS = "additionalFields"
    RELATED_ACTIVITY_ALERTED = "relatedActivityAlerted"
    RELATED_ACTIVITY_DETECTED = "relatedActivityDetected"
    RELATED_RECORDS = "relatedRecords"
    TOP_LEVEL = "topLevel"


class AggsType:
    MIN_TS = "min_ts"
    MAX_TS = "max_ts"


class FloatColumns(str, Enum):
    ARRIVAL_PRICE = OrderField.TRX_DTL_PC_DTL_ARRIVAL_PRICE
    EXECUTION_PRICE = OrderVolume.WEIGHTED_EXECUTION_PRICE
    INIT_QTY = OrderField.PC_FD_INIT_QTY
    LIMIT_PRICE = OrderField.EXC_DTL_LIMIT_PRICE
    MID_POINT_PRICE = OrderField.TRX_DTL_PC_DTL_MID_POINT_PRICE
    PERCENT_VS_ASK_PRICE = OrderField.TRX_DTL_PC_DTL_PERCENT_VS_ASK_PRICE
    PERCENT_VS_BID_PRICE = OrderField.TRX_DTL_PC_DTL_PERCENT_VS_BID_PRICE
    PRICE = OrderField.PC_FD_PRICE
    TRD_QTY = OrderField.PC_FD_TRD_QTY

    @classmethod
    def get_values(cls) -> List:
        values = [v for k, v in vars(cls).items() if k.isupper()]

        return values


class QueryAggScripts:
    VENUE_SCRIPT = {
        "source": f"def instVenueTrdVenue = doc['{OrderField.INST_VENUE_TRD_VENUE}'];\
                    def IdVenueKey = doc['{OrderField.INST_ID_CODE}'];\
                    def alternativeIdCode = doc['{OrderField.INST_EXT_ALT_IDENT}'];\
                    def uniqueIdCode = doc['{OrderField.INST_EXT_UNIQUE_IDENT}'];\
                        if (!IdVenueKey.empty) {{instVenueTrdVenue + ':' + IdVenueKey}}\
                        else if (!alternativeIdCode.empty) {{instVenueTrdVenue + ':' + alternativeIdCode}}\
                        else {{instVenueTrdVenue + ':' + uniqueIdCode}}",
        "lang": "painless",
    }

    ISIN_PRIORITY_SCRIPT = {
        "source": f"def instrumentIdCode = doc['{OrderField.INST_ID_CODE}']; \
                    def alternativeIdCode = doc['{OrderField.INST_EXT_ALT_IDENT}'];\
                    def uniqueIdCode = doc['{OrderField.INST_EXT_UNIQUE_IDENT}'];\
                        if (!instrumentIdCode.empty) {{instrumentIdCode}} \
                        else if (!alternativeIdCode.empty) {{alternativeIdCode}}\
                        else {{uniqueIdCode}}",
        "lang": "painless",
    }

    UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_NON_DERIV = {
        "source": f"def uniqueIdCode = doc['{OrderField.INST_EXT_UNIQUE_IDENT}']; \
                    def alternativeIdCode = doc['{OrderField.INST_EXT_ALT_IDENT}'];\
                    def instrumentIdCode= doc['{OrderField.INST_ID_CODE}'];\
                        if (!uniqueIdCode.empty) {{uniqueIdCode}} \
                        else if (!alternativeIdCode.empty) {{alternativeIdCode}}\
                        else {{instrumentIdCode}}",
        "lang": "painless",
    }

    UNIQUE_IDENTIFIER_PRIORITY_SCRIPT_DERIV = {
        "source": f"def instrument_details=params._source.instrumentDetails; \
                        if (instrument_details!=null && instrument_details.instrument!=null && instrument_details.instrument.derivative!=null && instrument_details.instrument.derivative.underlyingInstruments!=null && instrument_details.instrument.derivative.underlyingInstruments.length>0 && instrument_details.instrument.derivative.underlyingInstruments[0].underlyingInstrumentCode!=null) return instrument_details.instrument.derivative.underlyingInstruments[0].underlyingInstrumentCode; \
                        else  {{def uniqueIdCode = doc['{OrderField.INST_EXT_UNIQUE_IDENT}']; \
                            def alternativeIdCode = doc['{OrderField.INST_EXT_ALT_IDENT}'];\
                            def instrumentIdCode= doc['{OrderField.INST_ID_CODE}'];\
                                if (!uniqueIdCode.empty) {{uniqueIdCode}} \
                                else if (!alternativeIdCode.empty) {{alternativeIdCode}}\
                                else {{instrumentIdCode}}}}",
        "lang": "painless",
    }

    EXT_DERIVATIVE_UNDERLYING_INSTRUMENTS_PRIORITY = {
        "source": f"def instrument_details=params._source.instrumentDetails; \
                            if (instrument_details!=null && instrument_details.instrument!=null && instrument_details.instrument.ext!=null && instrument_details.instrument.ext.underlyingInstruments!=null && instrument_details.instrument.ext.underlyingInstruments.length>0 && instrument_details.instrument.ext.underlyingInstruments[0].underlyingInstrumentCode!=null) return instrument_details.instrument.ext.underlyingInstruments[0].instrumentIdCode; \
                            else if (instrument_details!=null && instrument_details.instrument!=null && instrument_details.instrument.derivative!=null && instrument_details.instrument.derivative.underlyingInstruments!=null && instrument_details.instrument.derivative.underlyingInstruments.length>0 && instrument_details.instrument.derivative.underlyingInstruments[0].underlyingInstrumentCode!=null) return instrument_details.instrument.derivative.underlyingInstruments[0].underlyingInstrumentCode; \
                            else  {{def uniqueIdCode = doc['{OrderField.INST_EXT_UNIQUE_IDENT}']; \
                                def alternativeIdCode = doc['{OrderField.INST_EXT_ALT_IDENT}'];\
                                def instrumentIdCode= doc['{OrderField.INST_ID_CODE}'];\
                                    if (!uniqueIdCode.empty) {{uniqueIdCode}} \
                                    else if (!alternativeIdCode.empty) {{alternativeIdCode}}\
                                    else {{instrumentIdCode}}}}",
        "lang": "painless",
    }

    TRADER_SCRIPT = {
        "source": f"def type = doc['{OrderField.PARTICIPANTS_TYPE}'];\
                            def trader_id = doc['{OrderField.PARTICIPANTS_VALUE_ID}'];\
                            if (type == params.type){{ \
                                return trader_id}}",
        "lang": "painless",
        "params": {"type": "TRADER"},
    }
