# type: ignore
import addict
import datetime
import json
import logging
import nanoid
import os
import requests
import uuid
from abc import ABC, abstractmethod
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.audit.models import SlackMessage
from market_abuse_algorithms.audit.slack import SlackReport
from market_abuse_algorithms.data_source.repository.sdp.es_sdp import SDP
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    MarketAbuseAudit,
    SingletonMarAudit,
)
from market_abuse_algorithms.strategy.base.errors import NoAvailableDataToAnalyse, ParameterError
from market_abuse_algorithms.strategy.base.models import CommonThresholds, StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.base.static import StrategyName
from se_elastic_schema.components.mar.strategy.base import BaseStrategyThresholds
from se_elastic_schema.static.mar import ErrorClassification
from se_elastic_schema.static.surveillance import WatchExecutionStatus
from typing import Optional, Type, Union

CREATE_AUDIT = os.environ.get("CREATE_AUDIT")

logging.getLogger("elasticsearch").setLevel(logging.DEBUG)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s.%(msecs)03d %(levelname)s [%(funcName)s:%(lineno)d] %(message)s",
    datefmt="%Y-%m-%dT%H:%M:%S",
)

singleton_audit_object = SingletonMarAudit()
market_abuse_audit_object = MarketAbuseAudit()


class AbstractStrategy(ABC):
    def __init__(
        self,
        context: StrategyContext,
        strategy_name: str,
        thresholds_class: Type[Union[CommonThresholds, BaseStrategyThresholds]],
        queries_class: Optional[Type[BaseQuery]] = None,
    ):
        market_abuse_audit_object.reset_market_abuse_attributes()
        singleton_audit_object.reset_singleton_audit_attributes()

        self._logger = logging.getLogger(__name__)
        self._context = context
        self.run_local = True if CREATE_AUDIT == "DEV" else False

        # Strategy context

        self._strategy_name = strategy_name
        self._thresholds_class = thresholds_class
        self._evaluate_thresholds()

        self._es_client = SDP(tenant=self._context.tenant)

        self._audit = Audit(
            context=self._context,
            strategy_name=self._strategy_name,
            es_client=self._es_client,
        )

        self._queries = (
            queries_class(context=self._context, audit=self._audit)
            if queries_class is not None
            else None
        )
        self._scenarios = list()

        singleton_audit_object.lake_prefix = self._context.lake_prefix
        singleton_audit_object.watch_execution_id = self._context.watch_execution_id
        singleton_audit_object.report_type = StrategyName.get_report_type(self._strategy_name).value  # noqa: E501

        self._look_back_period_ts = (
            self.queries.look_back_period_ts if queries_class is not None else None
        )
        self._market_data_end_date = (
            self.queries.market_data_end_date if queries_class is not None else None
        )

    @property
    def audit(self):
        return self._audit

    @property
    def context(self):
        return self._context

    @property
    def queries(self):
        return self._queries

    @property
    def strategy_name(self):
        return self._strategy_name

    @property
    def scenarios(self):
        return self._scenarios

    @property
    def look_back_period_ts(self):
        return self._look_back_period_ts

    @property
    def market_data_end_date(self):
        return self._market_data_end_date

    @abstractmethod
    def _apply_strategy(self):
        return NotImplemented

    def run(self):
        exception = None
        should_trigger_auditor = False

        start_of_strategy = datetime.datetime.now(datetime.timezone.utc)

        try:
            self._logger.debug(f"Applying {self.strategy_name} Strategy")

            market_abuse_audit_object.records_analysed = 0

            self._apply_strategy()

            end_of_strategy = datetime.datetime.now(datetime.timezone.utc)

            self.update_market_abuse_object(
                start_timestamp=start_of_strategy, end_timestamp=end_of_strategy
            )

            self._audit.update_metrics(number_of_scenarios=len(self.scenarios))
            self._audit.update_status(WatchExecutionStatus.COMPLETED)

            should_trigger_auditor = True

            return self.scenarios

        except NoAvailableDataToAnalyse as e:
            end_of_strategy = datetime.datetime.now(datetime.timezone.utc)

            self.update_market_abuse_object(
                start_timestamp=start_of_strategy,
                end_timestamp=end_of_strategy,
            )

            self._audit.update_status(WatchExecutionStatus.COMPLETED)
            self._logger.info(e.message)

            should_trigger_auditor = True

        except Exception as e:
            end_of_strategy = datetime.datetime.now(datetime.timezone.utc)

            self.update_market_abuse_object(
                start_timestamp=start_of_strategy,
                end_timestamp=end_of_strategy,
            )

            self._audit.update_status(WatchExecutionStatus.ERROR).set_error(
                classification=ErrorClassification.STRATEGY, error=e
            )
            exception = e

        finally:
            if not self.run_local:
                self._audit.finish()

                if should_trigger_auditor:
                    try:
                        self.trigger_mar_auditor_job(start_timestamp=end_of_strategy)
                    except Exception as e:
                        self._logger.info(f"Error triggering MAR audit: {str(e)}")

                singleton_audit_object.delete_local_audit_files()

            if exception:
                raise exception

    def _evaluate_thresholds(self):
        self._logger.info(f"Thresholds: {self.context.thresholds}")
        try:
            self._logger.info(f"Thresholds type: {type(self.context.thresholds)}")

            if type(self.context.thresholds) is not dict:
                raise ParameterError(f"Unknown threshold type: {type(self.context.thresholds)}")

            unknown_thresholds = set(self.context.thresholds.keys()) - set(
                self._thresholds_class.schema().get("properties").keys()
            )

            if unknown_thresholds:
                raise ParameterError(f"Unknown threshold(s): {unknown_thresholds}")

            thresholds = self._thresholds_class.validate(self.context.thresholds)

            self.context.thresholds = thresholds
        except Exception as e:
            if not self.run_local:
                message = addict.Dict(
                    report_type=self._strategy_name,
                    tenant=self._context.tenant,
                    stack=self._context.stack,
                    id=self.context.watch_id,
                    execution_id=self.context.watch_execution_id,
                    thresholds=self.context.thresholds,
                    filters=self.context.filters,
                    errors=e.__str__(),
                )

                slack_report = SlackReport(
                    message=SlackMessage(**message),
                    username=f"{self._context.realm}",
                    icon=":red_circle:",
                )

                slack_report.publish()

            raise Exception(f"Failed validating thresholds: {e}")

    @staticmethod
    def get_start_time_and_unique_id():
        return datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT), str(
            uuid.uuid4()
        )

    def update_market_abuse_object(self, start_timestamp, end_timestamp):
        """Update market_abuse_audit_object & write audiit local files.

        :param start_timestamp:
        :param end_timestamp:
        :return:
        """
        market_abuse_audit_object.filters = str(self.context.filters)
        market_abuse_audit_object.thresholds = str(self.context.thresholds)
        market_abuse_audit_object.algorithm_duration = abs(
            int((start_timestamp - end_timestamp).total_seconds())
        )
        market_abuse_audit_object.start = start_timestamp
        market_abuse_audit_object.end = end_timestamp
        market_abuse_audit_object.number_of_alerts = len(self.scenarios)
        market_abuse_audit_object.status = WatchExecutionStatus.COMPLETED
        market_abuse_audit_object.report_type = StrategyName.get_report_type(self._strategy_name)
        market_abuse_audit_object.watch_execution_id = self._context.watch_execution_id

        market_abuse_audit_object.watch_execution_type = self._context.watch_execution_type
        market_abuse_audit_object.watch_id = self._context.watch_id
        market_abuse_audit_object.requested_by = self._context.requested_by
        market_abuse_audit_object.records_skipped = singleton_audit_object.records_skipped
        market_abuse_audit_object.watch_name = self._context.watch_name
        singleton_audit_object.write_market_abuse_audit_locally(market_abuse_audit_object)

        if not self.run_local:
            try:
                singleton_audit_object.upload_audit_files_to_remote()
            except Exception as e:
                self._logger.info(f"Error uploading audit files to remote: {str(e)}")

    def trigger_mar_auditor_job(self, start_timestamp):
        """
        Trigger the MAR auditor job
        :return:
        """
        environment = os.environ.get("STACK", os.environ.get("ENVIRONMENT"))
        data = {
            "workflow": {
                "trace_id": nanoid.generate(),
                "name": "mar_auditor",
                "tenant": self._context.tenant,
                "stack": environment,
                "start_timestamp": str(start_timestamp),
            },
            "task": {
                "name": "task",
                "version": "latest",
                "success": True,
            },
            "io_param": {},
        }

        params = {
            "mar_audit_url": singleton_audit_object.mar_audit_url,
            "se_realm": self._context.realm,
            "watch_execution_id": singleton_audit_object.watch_execution_id,
        }

        if singleton_audit_object.step_audit_url:
            params["step_audit_url"] = singleton_audit_object.step_audit_url
            params["step_audit_lines"] = int(singleton_audit_object.step_audit_lines)

        if singleton_audit_object.aggregated_audit_url:
            params["aggregated_audit_url"] = singleton_audit_object.aggregated_audit_url

        data["io_param"]["params"] = params

        kafka_rest = os.environ.get("KAFKA_REST_PROXY_URL")
        kafka_topic = f"aries.mar_auditor.{environment}.{self._context.tenant}.events"

        data = {
            "records": [
                {
                    "value": json.loads(json.dumps(data)),
                }
            ]
        }

        if singleton_audit_object.mar_audit_url:
            headers = {
                "Content-Type": "application/vnd.kafka.json.v2+json",
                "Accept": "application/vnd.kafka.v2+json",
            }
            response = requests.post(
                url=f"{kafka_rest}/topics/{kafka_topic}", headers=headers, json=data
            )
            response.raise_for_status()
            logging.info(response)
