# type: ignore
import json
import logging
import pandas as pd
import uuid
from abc import ABC, abstractmethod
from collections import namedtuple
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import ScenarioLog

TradeColumns = namedtuple("TradeColumns", ["single", "multiple"])
LabeledTrade = namedtuple("LabeledTrade", ["label", "trade"])
Normalized = namedtuple("Normalized", ["raw", "scenario_id", "trades", "tags"])


class AbstractScenario(ABC):
    def __init__(self, result, context: StrategyContext):
        self._logger = logging.getLogger(__name__)
        self._context = context
        self._result = result
        self._thresholds = json.loads(self._context.thresholds.json())
        self._scenario = self.build_scenario()

    @property
    def json(self):
        return json.loads(self._scenario.to_json(date_format="iso"))

    @property
    def normalized_dict(self) -> dict:
        normalized_data = self.normalize
        d_normalized = dict(
            raw=normalized_data.raw,
            scenario_id=normalized_data.scenario_id,
            tags=normalized_data.tags,
            trades=[trade._asdict() for trade in normalized_data.trades],
        )
        return d_normalized

    @property
    def normalize(self) -> Normalized:
        """Map scenario series to a named tuple with:

        - trades flattened in a list of dict(label=str,trade=trade)
        - all other series columns consolidated to dict(str=str)
        :return: a Normalized tuple
        """
        scenario = self.json
        trade_cols = self._trade_columns()

        tags = list(set(self._scenario.index.tolist()))

        trades = list()
        for col in trade_cols.single:
            trades.append(LabeledTrade(label=f"{col}", trade=scenario.get("records").get(col)))

        for col in trade_cols.multiple:
            col_data = scenario.get("records").get(col)

            if not col_data:
                continue

            trades_in_records = [LabeledTrade(label=f"{col}", trade=t) for t in col_data]
            trades.extend(trades_in_records)

        return Normalized(
            raw=self._scenario.to_json(date_format="iso"),
            scenario_id=str(uuid.uuid4()),
            tags=self._scenario[tags].to_json(date_format="iso"),
            trades=trades,
        )

    @staticmethod
    def _unflatten_asserts(flat_dict, separator):
        assert isinstance(flat_dict, dict), "un_flatten requires a dictionary input"
        assert isinstance(separator, str), "separator must be a string"
        # assert all((not isinstance(value, Iterable) or isinstance(value, str)
        #             for value in flat_dict.values())), "provided dictionary is not flat"

    @staticmethod
    def unflatten_list(list, seperator="."):
        return [AbstractScenario.unflatten(i, seperator) for i in list]

    @staticmethod
    def unflatten(flat_dict, separator="."):
        """Creates a hierarchical dictionary from a flattened dictionary
        Assumes no lists are present.

        :param flat_dict: a dictionary with no hierarchy
        :param separator: a string that separates keys
        :return: a dictionary with hierarchy
        """
        AbstractScenario._unflatten_asserts(flat_dict, separator)

        # This global dictionary is mutated and returned
        unflattened_dict = dict()

        def _unflatten(dic, keys, value):
            for key in keys[:-1]:
                dic = dic.setdefault(key, {})

            dic[keys[-1]] = value

        for item in flat_dict:
            if isinstance(item, list):
                continue
            _unflatten(unflattened_dict, item.split(separator), flat_dict[item])

        return unflattened_dict

    def build_scenario(self):
        self._logger.debug(ScenarioLog.BUILDING)

        scenario = self._build_scenario()

        self._logger.debug(ScenarioLog.BUILT)

        return scenario

    @abstractmethod
    def _build_scenario(self) -> pd.Series:
        raise NotImplementedError

    @abstractmethod
    def _trade_columns(self) -> TradeColumns:
        raise ValueError("Not implemented")
