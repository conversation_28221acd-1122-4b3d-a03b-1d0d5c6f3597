import pydantic
from enum import Enum
from pydantic import BaseModel
from typing import Optional


class BasicModel(BaseModel):
    class Config:
        arbitrary_types_allowed = True


class StrategyContext(BasicModel):
    created_by: Optional[str] = None
    commit_audit: bool = True
    filters: Optional[dict] = None
    requested_by: str = "Programmatic"
    realm: Optional[str] = None
    tenant: str
    stack: Optional[str] = None
    thresholds: Optional[dict] = None
    watch_execution_id: Optional[str] = None
    watch_execution_type: Optional[str] = None
    watch_id: Optional[str] = None
    watch_name: Optional[str] = None
    look_back_period_ts: Optional[str] = None
    lake_prefix: Optional[str] = None


class CommonThresholds(BaseModel):
    assetClass: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True
        extra = pydantic.Extra.forbid


class TimeUnit(str, Enum):
    """
    WARNING: if we add new units here we need to evaluate
    if the it is supported in pd.Timedelta as unit keyword
    """

    MICROSECONDS = "microseconds"
    MILLISECONDS = "milliseconds"
    SECONDS = "seconds"
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"


class TimedeltaThresholdModel(BaseModel):
    unit: TimeUnit


class NumberOfCounterparties(str, Enum):
    SINGLE = "single"
    MULTIPLE = "multiple"
