import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import AbstractS<PERSON>nario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from market_abuse_algorithms.strategy.price_outliers.static import AlgoColumnsEnum


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders", "executions"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "orders": self._result.pop(AlgoColumnsEnum.ORDER),
            "executions": self._result.pop(AlgoColumnsEnum.EXECUTIONS),
        }

        alert = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(alert)

        return scenario
