from market_abuse_algorithms.strategy.base.currency import NotionalCurrency
from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.price_outliers.static import (
    Benchmark,
    BenchmarkOperator,
    Level,
    OutlierNature,
)
from pydantic import Field


class Thresholds(CommonThresholds):
    benchmarkOne: Benchmark = Field(Benchmark.MARKET_TOUCH_PRICE)
    benchmarkOnePercentage: float = Field(0.05, ge=0.0001, le=1)
    benchmarkTwoOperator: BenchmarkOperator = Field(BenchmarkOperator.BLANK)
    benchmarkTwo: Benchmark = Field(Benchmark.MARKET_TOUCH_PRICE)
    benchmarkTwoPercentage: float = Field(0.05, ge=0.0001, le=1)
    level: Level = Field(Level.ORDER)
    minimumNotional: int = Field(10_000, ge=0, le=10_000_000)
    minimumNotionalCurrency: NotionalCurrency = Field(NotionalCurrency.USD)
    natureOfOutlier: OutlierNature = Field(OutlierNature.BOTH)
