# ruff: noqa: E501
import logging
import pandas as pd
import time
from elasticsearch_dsl.query import Terms
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus, RecordType
from market_abuse_algorithms.strategy.base.currency import NotionalCurrency
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.price_outliers.static import (
    MAX_LENGTH_INSTRUMENTS_QUERY,
    Benchmark,
    BenchmarkOperator,
    Level,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import process_numeric_columns
from market_abuse_algorithms.utils.filters import filter_newos, filter_required_directionality
from se_elastic_schema.static.mifid2 import HierarchyEnum
from typing import Generator, List, Optional


class Queries(BaseQuery):
    _required_fields = [
        OrderField.META_KEY,
        OrderField.META_ID,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_NEAREST_QUOTE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_CLOSE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_OPEN,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.INST_FULL_NAME,
        OrderField.INST_ID_CODE,
        OrderField.EXC_DTL_ORD_STATUS,
    ]

    _include_fields = [
        OrderField.META_ID,
        OrderField.META_KEY,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_NEAREST_QUOTE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_CLOSE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_OPEN,
        OrderField.EXC_DTL_BUY_SELL_IND,
        OrderField.TS_ORD_SUBMITTED,
        OrderField.INST_FULL_NAME,
        OrderField.INST_ID_CODE,
        OrderField.EXC_DTL_ORD_STATUS,
    ]

    executions_fields = [
        OrderField.META_ID,
        OrderField.META_KEY,
        OrderField.ORD_IDENT_ID_CODE,
        OrderField.PC_FD_PRICE,
        OrderField.TS_TRADING_DATE_TIME,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_NEAREST_QUOTE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_CLOSE,
        OrderField.TRX_DTL_PC_DTL_PERCENT_VS_OPEN,
    ]

    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self.level = self.context.thresholds.dict().get(ThresholdsNames.LEVEL)
        self.benchmark_one: Benchmark = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_ONE
        )
        self.benchmark_two_operator: BenchmarkOperator = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_TWO_OPERATOR
        )
        self.benchmark_two: Benchmark = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_TWO
        )
        self.minimum_notional: int = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL
        )
        self.minimum_notional_currency: NotionalCurrency = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_CURRENCY
        )

        self.minimum_notional_currency_column = OrderField.TRX_DTL_QUANTITY

    def get_data_to_analyse(self) -> Generator[pd.DataFrame, None, None]:
        """
        Get data to be analysed within the price outliers algo
        :return:
        """
        query: OrderQuery = self._get_orders_query()
        query: OrderQuery = self.get_initial_query(query=query, inspect_required_fields=True)
        instruments_combinations: List = self.get_instruments_combinations(query)

        for instruments in instruments_combinations:
            start = time.perf_counter()
            query: OrderQuery = self._get_orders_query(list_of_instruments=instruments)
            query: OrderQuery = self.get_initial_query(query=query)

            # Step 1.A.5 & 1.B.4 of algo logic
            query: OrderQuery = self._condition_on_record_type(query=query)

            if self.level == Level.ORDER:
                # Step 1.A.4 of algo logic
                query: OrderQuery = self._condition_on_hierarchy(query=query)

            data: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

            data: pd.DataFrame = self._filter_orders(data=data)

            # Step 2 of algo logic
            if self.minimum_notional is None or self.minimum_notional == 0:
                logging.warning(
                    "minimumNotional has been set to 0 or None. Orders will not be queried based on minimumNotional"
                )
            else:
                query.filter_by_minimum_notional(
                    value=self.minimum_notional, currency=self.minimum_notional_currency
                )

            result: pd.DataFrame = process_numeric_columns(data=data)

            if result.empty:
                logging.warning(f"No data to analyse for instrument {instruments}")
                continue
            self._logger.info(
                msg=f"For a instrument combination of size {len(instruments)}, it took {time.perf_counter() - start} seconds"
            )
            yield result

    def _get_orders_query(self, list_of_instruments: Optional[List] = None) -> OrderQuery:
        """
        :param: instruments: List of instruments to fetch data from
        :return:
        """
        query = OrderQuery()

        query.includes(value=self._include_fields)

        for field in self._required_fields:
            query.exists(field=field, mode="filter")

        if list_of_instruments:
            l_ins = len(list_of_instruments)
            if l_ins >= MAX_LENGTH_INSTRUMENTS_QUERY:
                logging.warning(
                    f"Requesting orders with an instrument list length of {l_ins}. Lengths over {MAX_LENGTH_INSTRUMENTS_QUERY} "
                    "are not guaranteed to work, and may fail silently. Please reduce the size of the instrument list in the query"
                )

            query.instrument_id(value=list_of_instruments)

        return query

    def _condition_on_hierarchy(self, query: OrderQuery) -> OrderQuery:
        """Modifies a query to filter out PARENT hierarchy.

        Args:
            query (OrderQuery): The initial query to condition on hierarchy

        Returns:
            OrderQuery: The query conditioned on hierarchy
        """
        query.add_condition(
            mode="must_not",
            conditions=[
                Terms(
                    **{
                        # Step 1.4 of algo logic
                        "hierarchy": [HierarchyEnum.PARENT.value]
                    }
                )
            ],
        )

        return query

    def _condition_on_record_type(self, query: OrderQuery) -> OrderQuery:
        """Modifies a query to filter out ALLOCATION record types.

        Args:
            query (OrderQuery): The initial query to condition on record type

        Returns:
            OrderQuery: The query conditioned on record type
        """
        # Step 1.5 of algo logic
        query.record_type(mode="must_not", value=[RecordType.ALLOCATION])

        return query

    def _filter_orders(self, data: pd.DataFrame) -> pd.DataFrame:
        """Filter the orders according to the first step of the algo.

        - [BuySell].isPopulated() AND
        - [Transaction Date Time].isPopulated() AND
        - [Record Type] not “Allocation” AND  # condition added in the query (_condition_on_record_type)

        Moreover, if the threshold "Level" is set to ORDER, filter with:

        - [Order Status] == “NEWO” AND
        - [Hierarchy] != "Parent"  # condition added in the query (_condition_on_hierarchy)

        Otherwise, if the threshold "Level" is set to EXECUTION, filter with:

        - the Order is at least a a.[Partially Filled Order]

        :return: pd.DataFrame. Filtered data
        """

        # Step 1.1 of algo logic
        data: pd.DataFrame = filter_required_directionality(data=data)

        if data.empty:
            logging.warning(
                f"After removing NaN from {OrderField.EXC_DTL_BUY_SELL_IND} column, the dataframe is empty."
            )
            return pd.DataFrame()

        # Step 1.3 of algo logic
        # [Transaction Date Time].isPopulated(), field in `include_fields`

        # Step 1.A.5 & 1.B.4 of algo logic
        # [Record Type] not “Allocation” done in the query (_condition_on_record_type)

        if self.level == Level.ORDER:
            # Step 1.A.2 of algo logic
            data = filter_newos(data=data)

            if data.empty:
                logging.warning(
                    "After removing non-NEWO orders, the dataframe is empty. Cannot continue."
                )
                return pd.DataFrame()

            # Step 1.A.4 of algo logic
            # [Hierarchy] != "Parent" done in the query (_condition_on_hierarchy)

        elif self.level == Level.EXECUTION:
            # Step 1.B.2 of algo logic
            p_filled: pd.DataFrame = self.get_filled_or_partially_filled_orders(
                parent_ids=data.loc[:, OrderField.META_ID].unique()
            )

            data = data[data[OrderField.META_ID].isin(p_filled[OrderField.META_PARENT])]

            if data.empty:
                logging.warning(
                    "After filtering for at least partially filled orders, the dataframe is empty."
                )
                return pd.DataFrame()

        return data

    def fetch_order_executions(self, order_ids: List[str]) -> pd.DataFrame:
        """Fetches execution data for the input order ids.

        :param dataframe: pd.DataFrame. Newos data
        :param order_ids: list of newos ids
        :return: dataframe with executions data
        """
        fields: List[str] = [
            self.minimum_notional_currency_column,
            OrderField.META_PARENT,
            *self.executions_fields,
        ]

        query = OrderExecutionsQuery()

        query.includes(value=fields)
        query = self.get_query_with_required_fields(query=query, fields=fields)
        self.add_default_conditions_to_query(query=query)

        query.order_status(value=[OrderStatus.FILL, OrderStatus.PARF])
        query.parent(value=order_ids)

        executions_df: pd.DataFrame = self._sdp_repository.search_after_query(query=query)

        if executions_df.empty:
            logging.info(
                f"No order state data exist for the the requested Order Meta IDs: {order_ids}"
            )
            return pd.DataFrame()

        return executions_df
