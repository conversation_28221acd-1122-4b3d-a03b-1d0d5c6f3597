import numpy as np
import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import Buy<PERSON>ell, OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.price_outliers.models import Thresholds
from market_abuse_algorithms.strategy.price_outliers.query import Queries
from market_abuse_algorithms.strategy.price_outliers.scenario import Scenario
from market_abuse_algorithms.strategy.price_outliers.static import (
    BENCHMARK_MAP,
    AlgoColumnsEnum,
    BenchmarkOperator,
    Level,
    OutlierNature,
    ThresholdsNames,
)
from market_abuse_algorithms.utils.data import get_unique_value_for_alert
from typing import Optional


class Strategy(AbstractStrategy):
    """Price Outliers.

    DESCRIPTION:
    This model detects whether execution level, or order level average
    execution prices are divergent from market prices. This may indicate
    an offmarket trade where a client has either received poor execution,
    a trading error, or any other reason where there is no legitimate
    purpose for over/underpaying.
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.PRICE_OUTLIERS,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )

        self.th_benchmark_one = self.context.thresholds.dict().get(ThresholdsNames.BENCHMARK_ONE)
        self.th_benchmark_one_percentage = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_ONE_PERCENTAGE
        )
        self.th_benchmark_two_operator = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_TWO_OPERATOR
        )
        self.th_benchmark_two = self.context.thresholds.dict().get(ThresholdsNames.BENCHMARK_TWO)
        self.th_benchmark_two_percentage = self.context.thresholds.dict().get(
            ThresholdsNames.BENCHMARK_TWO_PERCENTAGE
        )
        self.th_level = self.context.thresholds.dict().get(ThresholdsNames.LEVEL)
        self.th_minimum_notional = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL
        )
        self.th_minimum_notional_currency = self.context.thresholds.dict().get(
            ThresholdsNames.MINIMUM_NOTIONAL_CURRENCY
        )
        self.th_nature_of_outlier = self.context.thresholds.dict().get(
            ThresholdsNames.NATURE_OF_OUTLIER
        )

    @property
    def benchmarkTwoActive(self) -> bool:
        return self.th_benchmark_two_operator != BenchmarkOperator.BLANK

    def _apply_strategy(self):
        """Loop through the data and run the algorithm on each batch of
        data."""
        self._logger.debug(msg=f"Start getting data to analyse for {StrategyName.PRICE_OUTLIERS}")

        # Step 1 of algo logic is implemented on the query
        for data in self.queries.get_data_to_analyse():
            if data.empty:
                self._logger.debug(
                    msg=f"Tried to run {StrategyName.PRICE_OUTLIERS} but no data was provided."
                )
                return

            executions: pd.DataFrame = self.queries.fetch_order_executions(
                order_ids=data[OrderField.META_ID].dropna().unique().tolist()
            )

            with StrategyError.handle_algo_records_error(audit=self._audit, data=data):
                self._run_algo_batch(orders=data, executions=executions)

    def _run_algo_batch(self, orders: pd.DataFrame, executions: pd.DataFrame):
        """Split the input data per order or execution, according to the algo
        logic.

        Args:
            orders (pd.DataFrame): The batch of orders to run the algo on
            executions (pd.DataFrame): The batch of executions from the given orders
        """
        # TODO in the test files there are duplicate NEWOs with the same ORD_IDENT_ID_CODE
        # Changed to META_ID until it is clarified
        for order_key in orders[OrderField.META_ID].dropna():
            s_order_data: pd.DataFrame = orders.loc[orders[OrderField.META_ID].isin([order_key]), :]  # noqa: E501

            assert len(s_order_data) == 1, (
                f"Single order data for {OrderStatus.NEWO} orders should only contain one row"
            )
            order_data: pd.Series = s_order_data.squeeze()

            s_order_execs: pd.DataFrame = executions.loc[
                executions[OrderField.META_PARENT] == order_data[OrderField.META_ID],
                :,
            ]

            # Looping per execution
            for execution_idx in range(s_order_execs.shape[0]):
                execution: pd.Series = s_order_execs.iloc[execution_idx, :]
                alert_data: Optional[pd.Series] = self._run_algo_single_event(
                    order_data=order_data, execution=execution
                )

                if alert_data is not None:
                    if self.th_level == Level.ORDER:
                        # On level ORDER, if at least one execution triggers an alert,
                        # we send a single alert with all executions for the order
                        self._create_alert(alert_data=alert_data, executions=s_order_execs)
                        break
                    elif self.th_level == Level.EXECUTION:
                        # On level EXECUTION, each execution sends its own potential alert
                        self._create_alert(alert_data=alert_data, executions=execution.to_frame().T)  # noqa: E501
                    else:
                        raise NotImplementedError

    def _run_algo_single_event(
        self, order_data: pd.Series, execution: pd.Series
    ) -> Optional[pd.Series]:
        """Run the algorithm on a single order or execution, according to the
        algo logic.

        Args:
            order_data (pd.Series): The order to run the algo on
            executions (pd.DataFrame): The batch of executions from the given order
        """
        # Step 2 of algo logic
        if self.th_minimum_notional is None:
            self._logger.debug(msg="Minimum notional value not provided.")
            return None

        # Threshold [Minimum Notional]:
        # This is calculated the sum of the notional values for each individual Trades of the alert.  # noqa: E501
        notional_value = execution[self.queries.minimum_notional_currency_column].sum()
        assert not np.isnan(notional_value)
        if notional_value < self.th_minimum_notional:
            self._logger.debug(
                msg=f"Notional value '{notional_value}' smaller than minimum ({self.th_minimum_notional})."  # noqa: E501
            )
            return None
        order_data[AlgoColumnsEnum.NOTIONAL_VALUE] = notional_value

        # Steps 3 & 4 of algo logic
        order_data[AlgoColumnsEnum.BENCHMARK_ONE_ACTUAL] = execution[
            BENCHMARK_MAP[self.th_benchmark_one]
        ]

        order_data[AlgoColumnsEnum.BENCHMARK_ONE_TOUCH_PRICE_TIMESTAMP] = execution[
            OrderField.TS_TRADING_DATE_TIME
        ]

        if self.th_benchmark_two_operator != BenchmarkOperator.BLANK:
            order_data[AlgoColumnsEnum.BENCHMARK_TWO_ACTUAL] = execution[
                BENCHMARK_MAP[self.th_benchmark_two]
            ]

            order_data[AlgoColumnsEnum.BENCHMARK_TWO_TOUCH_PRICE_TIMESTAMP] = execution[
                OrderField.TS_TRADING_DATE_TIME
            ]

        # Step 5 of algo logic
        buy_sell = order_data[OrderField.EXC_DTL_BUY_SELL_IND].upper()

        def _calc_benchmark(actual: float, benchmark_percentage: float) -> bool:
            higher = abs(actual) > benchmark_percentage
            profit = (actual < 0 and higher and buy_sell == BuySell.BUY) or (
                actual > 0 and higher and buy_sell == BuySell.SELL
            )
            loss = (actual > 0 and higher and buy_sell == BuySell.BUY) or (
                actual < 0 and higher and buy_sell == BuySell.SELL
            )
            return (
                (self.th_nature_of_outlier == OutlierNature.PROFIT and profit)
                or (self.th_nature_of_outlier == OutlierNature.LOSS and loss)
                or (self.th_nature_of_outlier == OutlierNature.BOTH and (profit or loss))
            )

        bench_one: bool = _calc_benchmark(
            actual=order_data[AlgoColumnsEnum.BENCHMARK_ONE_ACTUAL],
            benchmark_percentage=self.th_benchmark_one_percentage,
        )

        if self.benchmarkTwoActive:
            bench_two: bool = _calc_benchmark(
                actual=order_data[AlgoColumnsEnum.BENCHMARK_TWO_ACTUAL],
                benchmark_percentage=self.th_benchmark_two_percentage,
            )

        if self.th_benchmark_two_operator == BenchmarkOperator.BLANK:
            bench_active = bench_one
        elif self.th_benchmark_two_operator == BenchmarkOperator.OR:
            bench_active = bench_one or bench_two
        elif self.th_benchmark_two_operator == BenchmarkOperator.AND:
            bench_active = bench_one and bench_two

        if not bench_active:
            self._logger.debug(
                msg="Benchmark calculations do not exceed the profit/loss thresholds"
            )
            return None

        # Step 6 of algo logic

        def _indicative_calc(actual: float) -> float:
            indicativeBenchmark = abs(actual * notional_value)
            flagDirection = -1 if buy_sell == BuySell.SELL else 1
            signBenchmarkOne = -1 if actual < 0 else 1
            return indicativeBenchmark * signBenchmarkOne * flagDirection

        order_data[AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_ONE] = _indicative_calc(
            actual=order_data[AlgoColumnsEnum.BENCHMARK_ONE_ACTUAL]
        )

        if self.benchmarkTwoActive:
            order_data[AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_TWO] = _indicative_calc(
                actual=order_data[AlgoColumnsEnum.BENCHMARK_TWO_ACTUAL]
            )

        return order_data

    def _create_alert(self, alert_data: pd.Series, executions: pd.DataFrame) -> None:
        """Create alert to be sent to the user.

        :param alert_data: pd.Series, data with valid alert
        :param executions: pd.Dataframe, the executions of the orders that generated the alert
        """
        self._logger.info(msg=f"Appending alert data for {StrategyName.PRICE_OUTLIERS}")
        alert: dict = alert_data.to_dict()

        alert[AlgoColumnsEnum.INST_FULL_NAME] = alert_data[OrderField.INST_FULL_NAME]
        alert[AlgoColumnsEnum.ISIN] = alert_data[OrderField.INST_ID_CODE]
        alert[AlgoColumnsEnum.LEVEL] = self.th_level
        alert[AlgoColumnsEnum.TS_ORD_SUBMITTED] = alert_data[OrderField.TS_ORD_SUBMITTED].strftime(
            "%d-%m-%Y %H:%M:%S.%f"
        )[:-3]
        alert[AlgoColumnsEnum.NATURE_OF_OUTLIER] = self.th_nature_of_outlier
        alert[AlgoColumnsEnum.COUNT_OF_EXECUTIONS] = executions.shape[0]
        alert[AlgoColumnsEnum.NOTIONAL_VALUE] = alert_data[AlgoColumnsEnum.NOTIONAL_VALUE]
        alert[AlgoColumnsEnum.MINIMUM_NOTIONAL_CURRENCY] = self.th_minimum_notional_currency
        alert[AlgoColumnsEnum.EXECUTION_PRICE] = executions[OrderField.PC_FD_PRICE].mean()
        alert[AlgoColumnsEnum.BENCHMARK_ONE_ACTUAL] = alert_data[
            AlgoColumnsEnum.BENCHMARK_ONE_ACTUAL
        ]
        alert[AlgoColumnsEnum.BENCHMARK_ONE_TOUCH_PRICE_TIMESTAMP] = alert_data[
            AlgoColumnsEnum.BENCHMARK_ONE_TOUCH_PRICE_TIMESTAMP
        ].strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
        alert[AlgoColumnsEnum.BENCHMARK_ONE_PERCENTAGE] = self.th_benchmark_one_percentage
        alert[AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_ONE] = alert_data[
            AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_ONE
        ]

        if self.benchmarkTwoActive:
            alert[AlgoColumnsEnum.BENCHMARK_TWO_ACTUAL] = alert_data[
                AlgoColumnsEnum.BENCHMARK_TWO_ACTUAL
            ]
            alert[AlgoColumnsEnum.BENCHMARK_TWO_TOUCH_PRICE_TIMESTAMP] = alert_data[
                AlgoColumnsEnum.BENCHMARK_TWO_TOUCH_PRICE_TIMESTAMP
            ].strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            alert[AlgoColumnsEnum.BENCHMARK_TWO_PERCENTAGE] = self.th_benchmark_two_percentage
            alert[AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_TWO] = alert_data[
                AlgoColumnsEnum.INDICATIVE_PNL_BENCHMARK_TWO
            ]

        alert[AlgoColumnsEnum.ORDER] = alert_data[OrderField.META_KEY]

        alert[AlgoColumnsEnum.EXECUTIONS] = get_unique_value_for_alert(
            data=executions, column_name=OrderField.META_KEY
        )

        result: dict = dict(
            (key, alert[key]) for key in AlgoColumnsEnum.get_scenario_columns() if key in alert
        )

        scenario = Scenario(result=result, context=self.context)
        self.scenarios.append(scenario)
