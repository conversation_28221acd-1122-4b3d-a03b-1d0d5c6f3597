from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField

MAX_LENGTH_INSTRUMENTS_QUERY = 2**16


class ThresholdsNames(str, Enum):
    BENCHMARK_ONE = "benchmarkOne"
    BENCHMARK_ONE_PERCENTAGE = "benchmarkOnePercentage"
    BENCHMARK_TWO_OPERATOR = "benchmarkTwoOperator"
    BENCHMARK_TWO = "benchmarkTwo"
    BENCHMARK_TWO_PERCENTAGE = "benchmarkTwoPercentage"
    LEVEL = "level"
    MINIMUM_NOTIONAL = "minimumNotional"
    MINIMUM_NOTIONAL_CURRENCY = "minimumNotionalCurrency"
    NATURE_OF_OUTLIER = "natureOfOutlier"


class Benchmark(str, Enum):
    MARKET_TOUCH_PRICE = "Market Touch Price"
    MARKET_CLOSE_PRICE = "Market Close Price"
    MARKET_OPEN_PRICE = "Market Open Price"


class BenchmarkOperator(str, Enum):
    AND = "And"
    OR = "Or"
    BLANK = " "


class Level(str, Enum):
    EXECUTION = "Execution"
    ORDER = "Order"


class OutlierNature(str, Enum):
    LOSS = "Loss"
    PROFIT = "Profit"
    BOTH = "Both"


class AlgoColumnsEnum(str, Enum):
    INST_FULL_NAME = "instrumentName"
    ISIN = "isin"
    LEVEL = "level"
    TS_ORD_SUBMITTED = "timestamps.orderSubmitted"
    NATURE_OF_OUTLIER = "vNatureOfOutlier"
    COUNT_OF_EXECUTIONS = "countexecutions"
    NOTIONAL_VALUE = "vNotional"
    MINIMUM_NOTIONAL_CURRENCY = "currency"
    EXECUTION_PRICE = "executionPrice"
    BENCHMARK_ONE_ACTUAL = "vBenchmarkOneActual"
    BENCHMARK_ONE_TOUCH_PRICE_TIMESTAMP = "benchmarkOneDateTime"
    BENCHMARK_ONE_PERCENTAGE = "benchmarkOnePercentage"
    INDICATIVE_PNL_BENCHMARK_ONE = "vIndicativeP&LBenchmarkOne"
    BENCHMARK_TWO_ACTUAL = "vBenchmarkTwoActual"
    BENCHMARK_TWO_TOUCH_PRICE_TIMESTAMP = "benchmarkTwoDateTime"
    BENCHMARK_TWO_PERCENTAGE = "benchmarkTwoPercentage"
    INDICATIVE_PNL_BENCHMARK_TWO = "vIndicativeP&LBenchmarkTwo"
    ORDER = "orders"
    EXECUTIONS = "executions"

    @classmethod
    def get_scenario_columns(cls):
        return [
            cls.INST_FULL_NAME,
            cls.ISIN,
            cls.LEVEL,
            cls.TS_ORD_SUBMITTED,
            cls.NATURE_OF_OUTLIER,
            cls.COUNT_OF_EXECUTIONS,
            cls.NOTIONAL_VALUE,
            cls.MINIMUM_NOTIONAL_CURRENCY,
            cls.EXECUTION_PRICE,
            cls.BENCHMARK_ONE_ACTUAL,
            cls.BENCHMARK_ONE_TOUCH_PRICE_TIMESTAMP,
            cls.BENCHMARK_ONE_PERCENTAGE,
            cls.INDICATIVE_PNL_BENCHMARK_ONE,
            cls.BENCHMARK_TWO_ACTUAL,
            cls.BENCHMARK_TWO_TOUCH_PRICE_TIMESTAMP,
            cls.BENCHMARK_TWO_PERCENTAGE,
            cls.INDICATIVE_PNL_BENCHMARK_TWO,
            cls.ORDER,
            cls.EXECUTIONS,
        ]


BENCHMARK_MAP = {
    Benchmark.MARKET_TOUCH_PRICE: OrderField.TRX_DTL_PC_DTL_PERCENT_VS_NEAREST_QUOTE,
    Benchmark.MARKET_CLOSE_PRICE: OrderField.TRX_DTL_PC_DTL_PERCENT_VS_CLOSE,
    Benchmark.MARKET_OPEN_PRICE: OrderField.TRX_DTL_PC_DTL_PERCENT_VS_OPEN,
}
