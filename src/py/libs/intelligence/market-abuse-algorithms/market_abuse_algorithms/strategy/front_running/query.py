import pandas as pd
import time
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery, OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField, OrderStatus
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.utils.data import process_numeric_columns
from typing import List


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

    def cases_to_analyse(self):
        query = self._get_exec_query()
        query = self.get_initial_query(query, inspect_required_fields=True)

        instruments_combinations = self.get_instruments_combinations(query)

        for inst_comb in instruments_combinations:
            start = time.perf_counter()
            query = self._get_exec_query(inst_comb=inst_comb)
            query = self.get_initial_query(query)

            results = self._sdp_repository.search_after_query(query=query)

            if results.empty:
                continue

            results: pd.DataFrame = process_numeric_columns(data=results)
            self._logger.info(
                f"For a instrument combination of size {len(inst_comb)}, it took {time.perf_counter() - start} seconds"  # noqa: E501
            )
            yield results

    @staticmethod
    def _get_exec_query(inst_comb: List[str] = None) -> OrderExecutionsQuery:
        q = OrderExecutionsQuery()
        q.order_status([OrderStatus.FILL, OrderStatus.PARF])

        required = [
            OrderField.META_PARENT,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.EXC_DTL_ORD_STATUS,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.CLIENT_FILE_IDENTIFIER,
        ]

        for field in required:
            q.exists(field=field)

        # includes
        includes = [
            OrderField.CLIENT_FILE_IDENTIFIER,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.META_KEY,
            OrderField.META_PARENT,
            OrderField.PARTICIPANTS,
            OrderField.TRX_DTL_TR_CAPACITY,
            OrderField.TS_ORD_SUBMITTED,
            OrderField.TS_TRADING_DATE_TIME,
            *OrderField.get_instrument_fields(),
        ]

        q.includes(includes)

        if inst_comb:
            q.instrument_id(inst_comb)

        return q

    def get_additional_fields_for_scenarios(self, ids: List[str]) -> pd.DataFrame:
        q = OrderQuery()

        q.condition_by_id(ids)

        includes = [OrderField.META_ID, OrderField.META_KEY]

        q.includes(includes)

        results = self._sdp_repository.search_after_query(query=q)

        if results.empty:
            return pd.DataFrame()

        results = results.loc[:, results.columns.isin(includes)].set_index(OrderField.META_ID)

        return results

    def get_orders_keys_map(self, new_orders_ids: List[str]) -> dict:
        q = OrderQuery()

        q.condition_by_id(new_orders_ids)

        includes = [OrderField.META_ID, OrderField.META_KEY]

        q.includes(includes)

        results = self._sdp_repository.search_after_query(query=q)

        if results.empty:
            return {}

        id_key_map = (
            results[[OrderField.META_ID, OrderField.META_KEY]]
            .set_index(OrderField.META_ID)[OrderField.META_KEY]
            .to_dict()
        )

        return id_key_map
