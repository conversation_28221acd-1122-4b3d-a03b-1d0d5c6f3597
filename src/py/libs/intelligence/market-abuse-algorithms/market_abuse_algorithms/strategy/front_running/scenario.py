import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import AbstractScenario, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(
            single=["order"],
            multiple=["newOrders", "executionsEntry", "executionsExit"],
        )

    def _build_scenario(self) -> pd.Series:
        scenario = dict(
            thresholds=self._thresholds,
            records={
                "order": self._result.pop("order"),
                "newOrders": self._result.pop("newOrders"),
                "executionsEntry": self._result.pop("executionsEntry"),
                "executionsExit": self._result.pop("executionsExit"),
            },
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario = pd.Series(scenario)

        return scenario
