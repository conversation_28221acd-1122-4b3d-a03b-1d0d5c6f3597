from enum import Enum


class ThresholdsNames(str, Enum):
    TIME_WINDOW = "timeWindow"


class DFColumns:
    EXECUTIONS_ENTRY = "executionsEntry"
    EXECUTIONS_EXIT = "executionsExit"
    INVOLVED_PARTIES = "involvedParties"
    ORDER = "order"
    NEW_ORDERS_IDS = "newOrdersIds"
    NEW_ORDERS = "newOrders"
    TIME_DIFFERENCE = "timeDifference"
    TS_LAST_EXECUTION = "tsLastExecution"
    TS_LAST_EXECUTION_PLUS_TW = "tsLastExecutionPlusTimeWindow"
    INVOLVED_TRADERS = "involvedTraders"
    INVOLVED_CLIENTS = "involvedClients"
    TRADING_CAPACITIES = "tradingCapacities"
