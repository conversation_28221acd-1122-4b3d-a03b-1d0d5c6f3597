# ruff: noqa: E501
import datetime
import pandas as pd
from addict import Dict
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    add_vol_and_percentage_level_to_market_data,
)
from market_abuse_algorithms.data_source.static.sdp.order import <PERSON><PERSON>ell, OrderField, OrderStatus
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.query import Queries
from market_abuse_algorithms.strategy.abstract_layering_v2.static import (
    AlertColumnsEnum,
    ThresholdsNamesEnum,
)
from market_abuse_algorithms.strategy.abstract_layering_v2.strategy import (
    LayeringV2AbstractStrategy,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyName
from market_abuse_algorithms.strategy.base.strategy import singleton_audit_object
from se_elastic_schema.components.mar.strategy.layering_v2_level1.thresholds import (
    LayeringV2Level1Thresholds,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from typing import Tuple


class Strategy(LayeringV2AbstractStrategy):
    """Layering Level 2.

    Confluence page for the logic:
        - https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1011351590/Layering+Book+Imbalance+-+V2

    Jira tickets for the logic:
        - https://steeleye.atlassian.net/browse/EP-153
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.LAYERING_V2_LEVEL1,
            queries=Queries,
            thresholds=LayeringV2Level1Thresholds,
        )

        self._th_layering_order_percentage_level1: float = self.context.thresholds.dict().get(
            ThresholdsNamesEnum.LAYERING_ORDER_PERCENTAGE_L1
        )

    def market_data_retrieval_step_9(
        self,
        ric: str,
        dates: Dict,
        tenant_data: pd.DataFrame,
        groupings_dropped: int,
        groupings_no: int,
        filter_limit_data: pd.DataFrame,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, int]:
        """Get quote tick data.

        :param filter_limit_data: used for audit
        :param groupings_no: used for audit
        :param groupings_dropped: used for audit
        :param tenant_data: tenant data for the grouping
        :param ric: ric to get market data
        :param dates: Dict, either with start/end or just a list of timestamps
        :return: tuple with market
        """

        start = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        try:
            level_1_market_data: pd.DataFrame = self._market_data_client.get_tick_data(
                instrument_ric=ric,
                dates=dates["timestamps"],
                event_type=RefinitivEventType.QUOTE,
                nearest_timestamp=True,
            )
        except Exception as e:
            self._logger.warning(f"Could not fetch OBD data for RIC {ric} due to '{e}'")
            level_1_market_data = pd.DataFrame()

        end, method_id = self.get_start_time_and_unique_id()

        if level_1_market_data.empty:
            groupings_dropped += 1
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=filter_limit_data.get(
                        OrderField.META_KEY, pd.Series()
                    ).tolist(),
                    reason=f"No market data available for RIC {ric}.",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start,
                    end=end,
                    number_of_dropped_orders=len(filter_limit_data),
                    number_of_input_orders=len(filter_limit_data),
                    number_of_resulting_orders=0,
                    step_name="Fetching Market Data for Instrument/s",
                    step_type=StepType.MARKET_DATA_RIC,
                    drop_reason=DropReason.RECORDS_DROPPED_MARKET_DATA,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self._logger.debug(f"No Order Book Depth data available for ric {ric}.")
            return pd.DataFrame(), pd.DataFrame(), groupings_dropped

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=start,
                end=end,
                number_of_dropped_orders=0,
                number_of_input_orders=len(filter_limit_data),
                number_of_resulting_orders=len(filter_limit_data),
                step_name="Fetching Market Data for Instrument/s",
                step_type=StepType.MARKET_DATA_RIC,
                groupings=groupings_no,
                groupings_dropped=groupings_dropped,
            )
        )
        self.START_TIME = end

        return tenant_data, level_1_market_data, groupings_dropped

    # todo: add audit
    def market_data_retrieval_step_12_and_13(
        self,
        data_with_level_of_order: pd.DataFrame,
        market_data: pd.DataFrame,
        level_of_order_book_col: str,
        volume_level_col: str,
        percentage_level_col: str,
    ) -> pd.DataFrame:
        """Apply step 12 and step 13 of Layering v2 algo.

        :param data_with_level_of_order: tenant data
        :param market_data: pd.Dataframe, order book depth data
        :param level_of_order_book_col: str, level of order book column to be created
        :param volume_level_col: str, column name for volume level of order book
        :param percentage_level_col: str, column name for percentage level of order book
        :return:
        """

        data_with_level_of_order: pd.DataFrame = add_vol_and_percentage_level_to_market_data(
            data=data_with_level_of_order,
            market_data=market_data,
            volume_level_col=AlertColumnsEnum.VOLUME_LEVEL,
            percentage_level_col=AlertColumnsEnum.PERCENTAGE_LEVEL,
            level_of_order_book_flag=False,
        )

        if data_with_level_of_order.empty:
            self._logger.warning("No volume was found in OBD")
            return pd.DataFrame()

        valid_layering_data: pd.DataFrame = self.step_13_apply_percentage_level_threshold(
            tenant_data_valid=data_with_level_of_order,
            percentage_threshold=self._th_layering_order_percentage_level1,
        )

        return valid_layering_data

    @staticmethod
    def _threshold_calculation(data: pd.DataFrame) -> Dict:
        """

        Calculate the value for some thresholds
            vCountOfLevelOfOrderBook = count(distinct [Limit Price])
            vCountOfLevelOfOrderBookForBuys = count(distinct [Limit Price] where [BuySell] = ‘BUYI')
            vCountOfLevelOfOrderBookForSells = count(distinct [Limit Price] where [BuySell] = ‘SELL')
            vSumOfBuyOrderQuantity = (sum([Order Quantity] where [Order Status] = 'NEWO' and [BuySell] = 'BUYI')
            vSumOfSellOrderQuantity = (sum([Order Quantity] where [Order Status] = 'NEWO' and [BuySell] = 'SELL')
        For each grouping

        :param data: pd.Dataframe with data to check
        :return:
        """
        count_level_of_order_book: int = data.loc[:, OrderField.EXC_DTL_LIMIT_PRICE].nunique()

        count_level_of_order_book_buys: int = data.loc[
            (data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY)
        ][OrderField.EXC_DTL_LIMIT_PRICE].nunique()

        count_level_of_order_book_sells: int = data.loc[
            (data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL)
        ][OrderField.EXC_DTL_LIMIT_PRICE].nunique()

        sum_buy_order_quantity: int = data.loc[
            (data.loc[:, OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO)
            & (data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.BUY)
        ][OrderField.PC_FD_INIT_QTY].sum()

        sum_sell_order_quantity: int = data.loc[
            (data.loc[:, OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO)
            & (data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND] == BuySell.SELL)
        ][OrderField.PC_FD_INIT_QTY].sum()

        return Dict(
            COUNT_LEVEL_ORDER_BOOK=count_level_of_order_book,
            COUNT_LEVEL_ORDER_BOOK_BUYS=count_level_of_order_book_buys,
            COUNT_LEVEL_ORDER_BOOK_SELLS=count_level_of_order_book_sells,
            SUM_BUY_ORDER_QUANTITY=sum_buy_order_quantity,
            SUM_SELL_ORDER_QUANTY=sum_sell_order_quantity,
        )
