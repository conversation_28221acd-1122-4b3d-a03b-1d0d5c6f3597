import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import DictResponseParser
from reference_api_client.base import ReferenceAPI
from typing import ClassVar, Dict, List

log = logging.getLogger(__name__)


class PathParamPathSpecifierFormatter(str):
    """Formats a path parameter which has :path format specifier used by
    FastAPI https://fastapi.tiangolo.com/tutorial/path-params/#path-parameters-
    containing-paths."""

    def __format__(self, format_spec):
        if format_spec == "path":
            return self
        return super(PathParamPathSpecifierFormatter, self).__format__(format_spec)


class PermIDs(ReferenceAPI):
    PREFIX: ClassVar[str] = "/perm-ids"
    BLANK: ClassVar[str] = ""
    GET_PERM_ID: ClassVar[str] = "/{id_type}/{instrument_id}"

    def get_prefix(self) -> str:
        return super().get_prefix() + PermIDs.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._perm_id_get: EndPoint[Dict] = EndPoint(
            path=PermIDs.GET_PERM_ID,
            http_verb="GET",
            response_parser=DictResponseParser(),
        )
        self._perm_id_post: EndPoint[Dict] = EndPoint(
            path=PermIDs.BLANK,
            http_verb="POST",
            response_parser=DictResponseParser(),
        )

    def get_perm_id(self, id_value: str, id_type: str, timeout=30):
        return self._client.call_api(
            api=self,
            endpoint=self._perm_id_get,
            path_params={"instrument_id": id_value, "id_type": id_type},
            timeout=timeout,
        )

    def bulk_get_perm_ids(self, json_body: Dict[str, List[str]]):
        return self._client.call_api(api=self, endpoint=self._perm_id_post, json_body=json_body)
