import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import DictResponseParser
from reference_api_client.base import ReferenceAPI
from typing import ClassVar, Dict, List

log = logging.getLogger(__name__)


class PathParamPathSpecifierFormatter(str):
    """Formats a path parameter which has :path format specifier used by
    FastAPI https://fastapi.tiangolo.com/tutorial/path-params/#path-parameters-
    containing-paths."""

    def __format__(self, format_spec):
        if format_spec == "path":
            return self
        return super(PathParamPathSpecifierFormatter, self).__format__(format_spec)


class Ric(ReferenceAPI):
    PREFIX: ClassVar[str] = "/ric"
    CURRENCY_NORM_FLAG: ClassVar[str] = "/currency_norm_overrides/{extraction_type}"
    CURRENCY: ClassVar[str] = "/currency/{ric}"
    BULK_CURRENCY: ClassVar[str] = "/currency/{ric}"
    CURRENCY_BY_LIST: ClassVar[str] = "/currency/by_list/{list_id}"

    def get_prefix(self) -> str:
        return super().get_prefix() + Ric.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._currency_norm_flag: EndPoint[Dict] = EndPoint(
            path=Ric.CURRENCY_NORM_FLAG,
            http_verb="GET",
            response_parser=DictResponseParser(),
        )
        self._currency: EndPoint[Dict] = EndPoint(
            path=Ric.CURRENCY,
            http_verb="GET",
            response_parser=DictResponseParser(),
        )
        self._currency_by_list: EndPoint[Dict] = EndPoint(
            path=Ric.CURRENCY_BY_LIST,
            http_verb="GET",
            response_parser=DictResponseParser(),
        )
        self._bulk_currency: EndPoint[Dict] = EndPoint(
            path=Ric.BULK_CURRENCY,
            http_verb="POST",
            response_parser=DictResponseParser(),
        )

    def currency_norm_flags(self, extraction_type: str):
        return self._client.call_api(
            api=self,
            endpoint=self._currency_norm_flag,
            path_params={"extraction_type": extraction_type},
        ).content  # type:ignore

    def get_currency(self, ric: str):
        return self._client.call_api(
            api=self,
            endpoint=self._currency,
            path_params={"ric": ric},
        ).content  # type:ignore

    def get_currencies_by_list(self, list_id: str, level1: bool = True):
        return self._client.call_api(
            api=self,
            endpoint=self._currency_by_list,
            query_param={"level1": level1},
            path_params={"list_id": list_id},
        ).content  # type:ignore

    def bulk_currency(self, rics: List[str]):
        return self._client.call_api(
            api=self,
            endpoint=self._bulk_currency,
            json_body=rics,
        ).content  # type:ignore
