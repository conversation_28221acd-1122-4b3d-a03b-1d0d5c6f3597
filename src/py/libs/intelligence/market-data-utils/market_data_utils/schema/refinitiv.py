import os
from enum import Enum
from market_data_utils.schema.base import BaseColumns

REFINITIV_STORAGE_BUCKET = os.environ.get("REFINITIV_STORAGE_BUCKET", "refinitiv.steeleye.co")


class RefinitivEventType(str, Enum):
    QUOTE = "Quote"
    TRADE = "Trade"
    ELEKTRON = "Elektron"
    AUCTION = "Auction"


class RefinitivExtractColumns(BaseColumns):
    RIC = "#RIC"
    EXCH_TIME = "Exch Time"
    ACCUMULATED_ASK_ORDER = "Accumulated Ask Order"
    ACCUMULATED_BID_ORDER = "Accumulated Bid Order"
    ALIAS_UNDERLYING_RIC = "Alias Underlying RIC"
    ASK_PRICE = "Ask Price"
    ASK_SIZE = "Ask Size"
    BID_PRICE = "Bid Price"
    BID_SIZE = "Bid Size"
    DATE_TIME = "Date-Time"
    DOMAIN = "Domain"
    EXCH_TIME = "Exch Time"
    GMT_OFFSET = "GMT Offset"
    HIGH = "High"
    LOW = "Low"
    MARKET_VWAP = "Market VWAP"
    MID_PRICE = "Mid Price"
    OPEN = "Open"
    PRICE = "Price"
    TRADE_PRICE_CURRENCY = "Trade Price Currency"
    TYPE = "Type"
    VOLUME = "Volume"
    CLOSE_PRICE = "Close Price"
    TRADE_COUNT = "Trade Count"
    IND_AUCTION_PRICE = "Indicative Auction Price"
    IND_AUCTION_VOLUME = "Indicative Auction Volume"
    QUALIFIERS = "Qualifiers"
    SEQ_NO = "Seq. No."


class RefinitivOrderBookDepthColumns(BaseColumns):
    RIC = "#RIC"
    DATE_TIME = "Date-Time"
    DOMAIN = "Domain"
    EXCH_TIME = "Exch Time"
    GMT_OFFSET = "GMT Offset"
    TYPE = "Type"
    COUNT = "Count"
    DATE_TIME_RESAMPLED = "Date-Time-Resampled"

    # L1
    L1_BID_PRICE = "L1-BidPrice"
    L1_BID_SIZE = "L1-BidSize"
    L1_BUY_NO = "L1-BuyNo"
    L1_ASK_PRICE = "L1-AskPrice"
    L1_ASK_SIZE = "L1-AskSize"
    L1_SELL_NO = "L1-SellNo"

    # L2
    L2_BID_PRICE = "L2-BidPrice"
    L2_BID_SIZE = "L2-BidSize"
    L2_BUY_NO = "L2-BuyNo"
    L2_ASK_PRICE = "L2-AskPrice"
    L2_ASK_SIZE = "L2-AskSize"
    L2_SELL_NO = "L2-SellNo"

    # L3
    L3_BID_PRICE = "L3-BidPrice"
    L3_BID_SIZE = "L3-BidSize"
    L3_BUY_NO = "L3-BuyNo"
    L3_ASK_PRICE = "L3-AskPrice"
    L3_ASK_SIZE = "L3-AskSize"
    L3_SELL_NO = "L3-SellNo"

    # L4
    L4_BID_PRICE = "L4-BidPrice"
    L4_BID_SIZE = "L4-BidSize"
    L4_BUY_NO = "L4-BuyNo"
    L4_ASK_PRICE = "L4-AskPrice"
    L4_ASK_SIZE = "L4-AskSize"
    L4_SELL_NO = "L4-SellNo"

    # L5
    L5_BID_PRICE = "L5-BidPrice"
    L5_BID_SIZE = "L5-BidSize"
    L5_BUY_NO = "L5-BuyNo"
    L5_ASK_PRICE = "L5-AskPrice"
    L5_ASK_SIZE = "L5-AskSize"
    L5_SELL_NO = "L5-SellNo"

    # L6
    L6_BID_PRICE = "L6-BidPrice"
    L6_BID_SIZE = "L6-BidSize"
    L6_BUY_NO = "L6-BuyNo"
    L6_ASK_PRICE = "L6-AskPrice"
    L6_ASK_SIZE = "L6-AskSize"
    L6_SELL_NO = "L6-SellNo"

    # L7
    L7_BID_PRICE = "L7-BidPrice"
    L7_BID_SIZE = "L7-BidSize"
    L7_BUY_NO = "L7-BuyNo"
    L7_ASK_PRICE = "L7-AskPrice"
    L7_ASK_SIZE = "L7-AskSize"
    L7_SELL_NO = "L7-SellNo"

    # L8
    L8_BID_PRICE = "L8-BidPrice"
    L8_BID_SIZE = "L8-BidSize"
    L8_BUY_NO = "L8-BuyNo"
    L8_ASK_PRICE = "L8-AskPrice"
    L8_ASK_SIZE = "L8-AskSize"
    L8_SELL_NO = "L8-SellNo"

    # L9
    L9_BID_PRICE = "L9-BidPrice"
    L9_BID_SIZE = "L9-BidSize"
    L9_BUY_NO = "L9-BuyNo"
    L9_ASK_PRICE = "L9-AskPrice"
    L9_ASK_SIZE = "L9-AskSize"
    L9_SELL_NO = "L9-SellNo"

    # L10
    L10_BID_PRICE = "L10-BidPrice"
    L10_BID_SIZE = "L10-BidSize"
    L10_BUY_NO = "L10-BuyNo"
    L10_ASK_PRICE = "L10-AskPrice"
    L10_ASK_SIZE = "L10-AskSize"
    L10_SELL_NO = "L10-SellNo"


class OrderBookDepthPriceColumns(BaseColumns):
    # L1
    L1_BID_PRICE = "L1-BidPrice"
    L1_ASK_PRICE = "L1-AskPrice"

    # L2
    L2_BID_PRICE = "L2-BidPrice"
    L2_ASK_PRICE = "L2-AskPrice"

    # L3
    L3_BID_PRICE = "L3-BidPrice"
    L3_ASK_PRICE = "L3-AskPrice"

    # L4
    L4_BID_PRICE = "L4-BidPrice"
    L4_ASK_PRICE = "L4-AskPrice"

    # L5
    L5_BID_PRICE = "L5-BidPrice"
    L5_ASK_PRICE = "L5-AskPrice"

    # L6
    L6_BID_PRICE = "L6-BidPrice"
    L6_ASK_PRICE = "L6-AskPrice"

    # L7
    L7_BID_PRICE = "L7-BidPrice"
    L7_ASK_PRICE = "L7-AskPrice"

    # L8
    L8_BID_PRICE = "L8-BidPrice"
    L8_ASK_PRICE = "L8-AskPrice"

    # L9
    L9_BID_PRICE = "L9-BidPrice"
    L9_ASK_PRICE = "L9-AskPrice"

    # L10
    L10_BID_PRICE = "L10-BidPrice"
    L10_ASK_PRICE = "L10-AskPrice"


class ElektronExtractColumns(BaseColumns):
    ASK_HIGH = "Ask High"
    BID_HIGH = "Bid High"
    CURRENCY_CODE = "Currency Code"
    EXCHANGE_CODE = "Exchange Code"
    HIGH = "High Price"
    LOW = "Low Price"
    LOW_ASK = "Ask Low"
    LOW_BID = "Bid Low"
    MARKET_VWAP = "Market VWAP"
    VWAP = "VWAP Price"
    OPEN = "Open Price"
    OPEN_ASK = "Open Ask"
    OPEN_BID = "Open Bid"
    OPEN_INTEREST = "Open Interest"
    RIC = "RIC"
    ORC = "Instrument ID"
    TRADE_DATE = "Trade Date"
    UNIVERSAL_ASK_PRICE = "Universal Ask Price"
    UNIVERSAL_BID_PRICE = "Universal Bid Price"
    UNIVERSAL_CLOSE_PRICE = "Universal Close Price"
    VOLUME = "Volume"


class InstrumentListType(str, Enum):
    EOD = "EOD"
    OBD = "OBD"
    TICK = "TICK"
