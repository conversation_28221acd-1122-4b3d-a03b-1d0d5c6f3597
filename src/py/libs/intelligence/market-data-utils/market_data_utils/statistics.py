import logging
import numpy as np
import pandas as pd
from market_data_utils.utils import get_data_based_on_window, get_volatility_time, rolling_window
from typing import Optional, Union

logger = logging.getLogger(__name__)


def calculate_exponential_moving_average(
    market_data: pd.DataFrame,
    data_column: str,
    alpha: float,
    window: Union[int, str],
    time_series_column: Optional[str] = None,
    min_periods: Optional[int] = None,
    adv_column: str = "ADV_BY_VOLUME",
    decimal_precision: int = 8,
) -> pd.Series:
    """
    :param adv_column:
    :param market_data: Pandas DataFrame. Market data to calculate the ADV
    :param data_column: RefinitivExtractColumns. Needs to be a column defined on the scope
    :param alpha: Float. Specify smoothing factor 𝛼 directly
    :param window: Int or Str. Window size to calculate the metric.
    :param min_periods: Int. Minimum Observations to calculate the ADV
    :param decimal_precision: int. Decimal precision of volatility. Default: 8
    :param time_series_column: str. Datetime column name to set as index. Default is None

    :return: Pandas Series. A new column with the ADV calculated
    """
    data: pd.Series = get_data_based_on_window(
        window=window,
        data=market_data,
        data_column=data_column,
        time_series_column=time_series_column,
    )
    try:
        adv: pd.Series = _calculate_exponential_moving_average(
            data, alpha=alpha, window=window, min_periods=min_periods
        )
        adv = adv.round(decimals=decimal_precision)

        # Set series name
        adv.name = adv_column
    except ArithmeticError as error:
        message = f"Error calculating the Average Moving Aggregation. Error {error}"
        logger.error(message)
        raise ArithmeticError(message)
    return adv


def _calculate_exponential_moving_average(
    data: pd.Series,
    alpha: float,
    window: Union[int, str],
    min_periods: Optional[int] = None,
) -> pd.Series:
    """
    :param data: Pandas Series. Data that we want to apply the rolling window
    :param alpha: Float. Specify smoothing factor 𝛼 directly.
    :param window: Int or Str. Window size to calculate the metric.
    :param min_periods: Int. Minimum Observations to calculate the ADV.

    :return: Pandas Series. A new column with the ADV calculated.
    """
    adv: pd.Series = (
        rolling_window(window=window, data=data, min_periods=min_periods)
        .mean()
        .ewm(alpha=alpha)
        .mean()
    )
    return adv


def calculate_volatility(
    market_data: pd.DataFrame,
    data_column: str,
    volatility_window: Union[int, str],
    time_series_column: Optional[str] = None,
    min_periods: Optional[int] = None,
    volatility_column: str = "Volatility",
    decimal_precision: int = 8,
) -> pd.Series:
    """
    :param market_data: Pandas DataFrame
    :param data_column: str. Needs to be a column in the market_data DataFrame
    :param volatility_window: Int or Str. Type of window to use
    :param min_periods: Int. Minimum days to calculate the volatility
    :param time_series_column: str. Datetime column name to set as index. Default is None
    :param volatility_column: Str. Volatility series name. Default: Volatility
    :param decimal_precision: int. Decimal precision of volatility. Default: 8

    :return: Pandas Series. Group of volatility values

    """
    if data_column not in market_data.columns.values:
        raise Exception("Column does not exist in input dataframe")

    try:
        volatility: pd.Series = _calculate_volatility(
            market_data,
            data_column=data_column,
            volatility_window=volatility_window,
            volatility_col_name=volatility_column,
            decimal_precision=decimal_precision,
            min_periods=min_periods,
            time_series_column=time_series_column,
        )
    except Exception as error:
        message = f"Error calculating the volatility. Error {error}"
        logger.error(message)
        raise Exception(message)

    return volatility


def _calculate_volatility(
    market_data: pd.DataFrame,
    data_column: str,
    volatility_window: Union[int, str],
    volatility_col_name: str,
    decimal_precision: int,
    time_series_column: Optional[str] = None,
    min_periods: Optional[int] = None,
    as_prct: bool = True,
) -> pd.Series:
    """

    :param market_data: Pandas DataFrame with all market data
    :param data_column: str. Needs to be a column in the market_data DataFrame
    :param volatility_window: Int or Str. Type of window to use
    :param min_periods: Int. Minimum days to calculate the volatility
    :param volatility_col_name: Str. Volatility series name.
    :param decimal_precision: int. Decimal precision of volatility.
    :param: str. Datetime column name to set as index. Default is None

    :return: Pandas Series. Group of volatility values

    """
    data: pd.Series = get_data_based_on_window(
        window=volatility_window,
        data=market_data,
        data_column=data_column,
        time_series_column=time_series_column,
    )
    data = data.fillna(np.nan)

    log_diff: pd.Series = np.log(data).diff()

    volatility_days: pd.Series = rolling_window(
        window=volatility_window, data=log_diff, min_periods=min_periods
    ).mean()

    square_deviations: pd.Series = np.square(log_diff - volatility_days)

    volatility_time = get_volatility_time(volatility_window)

    volatility: pd.Series = np.sqrt(
        rolling_window(
            window=volatility_window,
            data=square_deviations,
            min_periods=min_periods,
        ).sum()
        / volatility_time
    )

    if as_prct:
        volatility = volatility * 100

    # Round to decimal precision
    volatility = volatility.round(decimals=decimal_precision)

    # Set series name
    volatility.name = volatility_col_name

    return volatility
