import logging
import pandas as pd
import re
from pandas.core.window import Rolling
from typing import Optional, Union

logger = logging.getLogger(__name__)

INVERSE_USD = ["AUD", "EUR", "GBP", "NZD"]


class Currencies:
    USD = "USD"


def refactor_date_column(
    data: pd.DataFrame,
    time_series_column: Optional[str] = None,
    date_format: Optional[str] = "%Y-%m-%d",
    sort: bool = False,
) -> pd.DataFrame:
    """Refactor the date column: set the date column as index after set it to
    ISO format and then sort the dataframe by the index (that should be a date
    column)

    :param data: Pandas DataFrame. It has all market data
    :param time_series_column: str. Datetime column name to set as index. Default is None
    :param date_format: bool. Boolean to set the datetime to isoformat. Default is True
    :param sort: bool. Boolean to check if it necessary to set the column to datetime. Default False

    :return: Pandas Dataframe with the Date column in datetime type
    """
    if date_format:
        data[time_series_column] = pd.to_datetime(data[time_series_column], format=date_format)
    else:
        data.loc[:, time_series_column] = pd.to_datetime(data[time_series_column])  # type: ignore

    data = data.set_index(time_series_column)

    if sort:
        data = data.sort_index()

    return data


def get_data_based_on_window(
    window: Union[int, str],
    data: pd.DataFrame,
    data_column: str,
    time_series_column: Optional[str] = None,
) -> pd.Series:
    """Define the data to use for the rolling window based on the window type.
    If it is integer or string. For a time series window, the type should be
    string. If the window is set as string, we must provide the time series
    column to use as index on the calculation.

    :param window: str or int. Type of window to use
    :param data: pd.DataFrame. Market Data
    :param data_column: str. Needs to be a column defined in the dataframe
    :param time_series_column: str. Datetime column name to set as index. Default is None

    :return: Pandas Series. Data to perform the calculations
    """

    if isinstance(window, str):
        data = refactor_date_column(
            data=data,
            time_series_column=time_series_column,
            date_format="%Y-%m-%d",
            sort=True,
        )

    data_window: pd.Series = data[data_column]
    return data_window


def rolling_window(
    window: Union[int, str], data: pd.Series, min_periods: Optional[int] = None
) -> Rolling:
    """Calculate the rolling window using a integer or a time window. The
    min_periods variable is only used when the window is integer. If the window
    is set as string, we must provide the time series column to use as index on
    the calculation.

    :param window: str or int. Type of window to use
    :param data: pd.Series. Market Data
    :param min_periods: int. Minimum days to calculate the rolling window
    :return: Rolling object based on the window type defined previously
    """
    if isinstance(window, (int, str)):
        return data.rolling(window=window, min_periods=min_periods)

    raise Exception("Window type provided is not correct")


def get_integer_from_time_window(window: Union[int, str]) -> int:
    """Retrieve integer part from string window.

    :param window: str. Time window
    :return: Integer
    """
    if isinstance(window, int):
        return window

    return int(re.match(r"\d+", window).group())  # type: ignore


def get_volatility_time(volatility_window: Union[int, str]) -> int:
    """Get the the volatility time (t-1) to use on the volatility calculation.

    :param volatility_window: Int or Str. Window size to calculate the metric.
    :return: Integer volatility window
    """
    return get_integer_from_time_window(volatility_window) - 1


def get_ric_for_currency_conversion(ccy_1: str, ccy_2: str) -> str:
    """
    Get ric for currency conversion
    NOTES:
        if any of the RICs is USD then it just uses the other RIC and =
    """
    if ccy_2 == Currencies.USD:
        ccy_conversion_ric = ccy_1 + "="
    elif ccy_1 == Currencies.USD:
        ccy_conversion_ric = ccy_2 + "="
    else:
        ccy_conversion_ric = ccy_1 + ccy_2 + "=R"

    return ccy_conversion_ric


def currency_inversion_check(base_currency: str, quote_currency: str) -> bool:
    """utility function that checks if the conversion needs to be inverted or
    not.

    :param base_currency: string with base currency
    :param quote_currency: string with quote currency
    :return: flag to if the currency fx rate needs to be inverted
    """

    if not any(x == Currencies.USD for x in [base_currency, quote_currency]):
        return False

    non_usd_ccy = base_currency if base_currency != Currencies.USD else quote_currency

    relationship = (
        [non_usd_ccy, Currencies.USD]
        if any(x in [base_currency, quote_currency] for x in INVERSE_USD)
        else [Currencies.USD, non_usd_ccy]
    )

    if base_currency == relationship[0]:
        return False
    else:
        return True
