from market_data_utils.schema.base import BaseColumns


class ElasticOrderFields:
    EXECUTION_ORDER_STATUS = "executionDetails.orderStatus"
    ORDER_MODEL = "Order"
    DATE = "date"
    RIC = "instrumentDetails.instrument.ext.pricingReferences.RIC"
    TIMESTAMPS_TRADING_DATETIME = "timestamps.tradingDateTime"
    VOLUME = "priceFormingData.tradedQuantity"
    VOLUME_2 = "transactionDetails.quantity"
    INSTRUMENT_UID = "instrumentDetails.instrument.ext.instrumentUniqueIdentifier"


class ParentOrderFields(BaseColumns):
    SPREAD = "bestExecutionData.effectiveSpread"
    INITIAL_QUANTITY = "priceFormingData.initialQuantity"
    ORDER_SUBMITTED = "timestamps.orderSubmitted"
    ORDER_RECEIVED = "timestamps.orderReceived"
    ECB_EUR_RATE = "bestExecutionData.transactionVolume.ecbRefRate.EUR"
    ECB_USD_RATE = "bestExecutionData.transactionVolume.ecbRefRate.USD"
    ECB_CHF_RATE = "bestExecutionData.transactionVolume.ecbRefRate.CHF"
    ECB_GBP_RATE = "bestExecutionData.transactionVolume.ecbRefRate.GBP"
    ECB_JPY_RATE = "bestExecutionData.transactionVolume.ecbRefRate.JPY"
    ECB_NATIVE_RATE = "bestExecutionData.transactionVolume.ecbRefRate.native"
    ORDER_TYPE = "executionDetails.orderType"
    LIMIT_PRICE = "executionDetails.limitPrice"
    BUY_SELL = "executionDetails.buySellIndicator"
    VENUE = "transactionDetails.venue"
    ISIN = "instrumentDetails.instrument.instrumentIdCode"
    INSTRUMENT_CLASS = "instrumentDetails.instrumentClassification"
    DATE = "date"


class TCAFlagBoolColumns(BaseColumns):
    EXECUTION_BEFORE_MARKET = "audit.executionBeforeMarket"
    MISSING_MARKET_DATA = "audit.missingMarketData"
    MISSING_RIC = "audit.missingRIC"
    CURRENCY_MISMATCH = "audit.currencyMismatch"
    INVALID_CURRENCY = "audit.invalidCurrency"
    DATA_INTEGRITY = "audit.dataIntegrity"
    TRADE_TS_INTEGRITY = "audit.tradeTsIntegrity"
    ORDER_TS_INTEGRITY = "audit.orderTsIntegrity"


class PriceAuditing(BaseColumns):
    QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN = "audit.quotesOrderReceivedExecutedBeforeOpen"
    QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN = "audit.quotesExecutionTsExecutedBeforeOpen"
    TRADES_EXECUTED_BEFORE_OPEN = "audit.tradesExecutedBeforeOpen"
