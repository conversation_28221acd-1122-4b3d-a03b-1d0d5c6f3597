import polars as pl
from market_data_utils.schema.base import BaseColumns
from market_data_utils.schema.parquet import EoDStatsColumns, QuoteTickColumns, TradeTickColumns


class TickColumns(BaseColumns):
    VENUE = "Venue"
    DATE = "Date"


class TickDtypes:
    TRADES = {
        TradeTickColumns.DATE_TIME: pl.Int64,
        TickColumns.DATE: pl.Date,
        TickColumns.VENUE: pl.Utf8,
        TradeTickColumns.GMT_OFFSET: pl.Int64,
        TradeTickColumns.HIGH: pl.Float64,
        TradeTickColumns.LOW: pl.Float64,
        TradeTickColumns.MARKET_VWAP: pl.Float64,
        TradeTickColumns.OPEN: pl.Float64,
        TradeTickColumns.PRICE: pl.Float64,
        TradeTickColumns.RIC: pl.Utf8,
        TradeTickColumns.TRADE_PRICE_CURRENCY: pl.Utf8,
        TradeTickColumns.VOLUME: pl.Int64,
    }

    QUOTES = {
        QuoteTickColumns.ACCUMULATED_ASK_ORDER: pl.Float64,
        QuoteTickColumns.ACCUMULATED_BID_ORDER: pl.Float64,
        QuoteTickColumns.ASK_PRICE: pl.Float64,
        QuoteTickColumns.ASK_SIZE: pl.Float64,
        QuoteTickColumns.BID_PRICE: pl.Float64,
        QuoteTickColumns.BID_SIZE: pl.Float64,
        QuoteTickColumns.DATE_TIME: pl.Int64,
        QuoteTickColumns.GMT_OFFSET: pl.Int64,
        QuoteTickColumns.MID_PRICE: pl.Float64,
        QuoteTickColumns.RIC: pl.Utf8,
        TickColumns.DATE: pl.Date,
        TickColumns.VENUE: pl.Utf8,
    }


class EoDStatsRenamed(BaseColumns):
    DATE_PREVAL_ONE_DAY = "prevalOneDayDate"
    RIC_VENUE_PREVAL_ONE_DAY = "prevalOneDayRicVenue"
    CLOSE_PREVAL_ONE_DAY = "prevalOneDayClose"
    DATE_PREVAL_FIVE_DAY = "prevalFiveDayDate"
    CLOSE_PREVAL_FIVE_DAY = "prevalFiveDayClose"
    RIC_VENUE_PREVAL_FIVE_DAY = "prevalFiveDayRicVenue"
    DATE_PREVAL_TEN_DAY = "prevalTenDayDate"
    CLOSE_PREVAL_TEN_DAY = "prevalTenDayClose"
    RIC_VENUE_PREVAL_TEN_DAY = "prevalTenDayRicVenue"
    DATE_PREVAL_TWENTY_DAY = "prevalTwentyDayDate"
    CLOSE_PREVAL_TWENTY_DAY = "prevalTwentyDayClose"
    RIC_VENUE_PREVAL_TWENTY_DAY = "prevalTwentyDayRicVenue"


class QuoteTickColumnsRenamed(BaseColumns):
    EXECUTION_TS_QUOTES_DATE_TIME = "executionTsDateTimeQuotes"
    EXECUTION_TS_ASK_PRICE = "executionTsAskPrice"
    EXECUTION_TS_BID_PRICE = "executionTsBidPrice"
    EXECUTION_TS_MID_PRICE = "executionTsMidPrice"
    EXECUTION_TS_ASK_SIZE = "executionTsAskSize"
    EXECUTION_TS_BID_SIZE = "executionTsBidSize"
    EXECUTION_TS_RIC_VENUE = "executionTsRicVenue"

    ORDER_RECEIVED_TS_QUOTES_DATE_TIME = "orderReceivedTsQuotes"
    ORDER_RECEIVED_TS_ASK_PRICE = "orderReceivedAskPrice"
    ORDER_RECEIVED_TS_BID_PRICE = "orderReceivedBidPrice"
    ORDER_RECEIVED_TS_MID_PRICE = "orderReceivedMidPrice"
    ORDER_RECEIVED_ASK_SIZE = "orderReceivedAskSize"
    ORDER_RECEIVED_BID_SIZE = "orderReceivedBidSize"
    ORDER_RECEIVED_RIC_VENUE = "orderReceivedRicVenue"

    ORDER_SUBMITTED_TS_QUOTES_DATE_TIME = "orderSubmittedTsQuotes"
    ORDER_SUBMITTED_TS_ASK_PRICE = "orderSubmittedAskPrice"
    ORDER_SUBMITTED_TS_BID_PRICE = "orderSubmittedBidPrice"
    ORDER_SUBMITTED_TS_MID_PRICE = "orderSubmittedMidPrice"
    ORDER_SUBMITTED_ASK_SIZE = "orderSubmittedAskSize"
    ORDER_SUBMITTED_BID_SIZE = "orderSubmittedBidSize"
    ORDER_SUBMITTED_RIC_VENUE = "orderSubmittedRicVenue"

    EXECUTION_TS_MINUS_ONE_MIN_QUOTES_DATE_TIME = "executionTsMinusOneMinDateTimeQuotes"
    EXECUTION_TS_MINUS_ONE_MIN_ASK_PRICE = "executionTsMinusOneMinAskPrice"
    EXECUTION_TS_MINUS_ONE_MIN_BID_PRICE = "executionTsMinusOneMinBidPrice"
    EXECUTION_TS_MINUS_ONE_MIN_MID_PRICE = "executionTsMinusOneMinMidPrice"
    EXECUTION_TS_MINUS_ONE_MIN_ASK_SIZE = "executionTsMinusOneMinAskSize"
    EXECUTION_TS_MINUS_ONE_MIN_BID_SIZE = "executionTsMinusOneMinBidSize"
    EXECUTION_TS_MINUS_ONE_MIN_RIC_VENUE = "executionTsMinusOneMinRicVenue"

    EXECUTION_TS_MINUS_FIVE_MIN_QUOTES_DATE_TIME = "executionTsMinusFiveMinDateTimeQuotes"
    EXECUTION_TS_MINUS_FIVE_MIN_ASK_PRICE = "executionTsMinusFiveMinAskPrice"
    EXECUTION_TS_MINUS_FIVE_MIN_BID_PRICE = "executionTsMinusFiveMinBidPrice"
    EXECUTION_TS_MINUS_FIVE_MIN_MID_PRICE = "executionTsMinusFiveMinMidPrice"
    EXECUTION_TS_MINUS_FIVE_MIN_ASK_SIZE = "executionTsMinusFiveMMinAskSize"
    EXECUTION_TS_MINUS_FIVE_MIN_BID_SIZE = "executionTsMinusFiveMMinBidSize"
    EXECUTION_TS_MINUS_FIVE_MIN_RIC_VENUE = "executionTsMinusFiveMMinRicVenue"

    EXECUTION_TS_MINUS_TEN_MIN_QUOTES_DATE_TIME = "executionTsMinusTenMinDateTimeQuotes"
    EXECUTION_TS_MINUS_TEN_MIN_ASK_PRICE = "executionTsMinusTenMinAskPrice"
    EXECUTION_TS_MINUS_TEN_MIN_BID_PRICE = "executionTsMinusTenMinBidPrice"
    EXECUTION_TS_MINUS_TEN_MIN_MID_PRICE = "executionTsMinusTenMinMidPrice"
    EXECUTION_TS_MINUS_TEN_MIN_ASK_SIZE = "executionTsMinusTenMinAskSize"
    EXECUTION_TS_MINUS_TEN_MIN_BID_SIZE = "executionTsMinusTenMinBidSize"
    EXECUTION_TS_MINUS_TEN_MIN_RIC_VENUE = "executionTsMinusTenMinRicVenue"

    EXECUTION_TS_MINUS_ONE_HOUR_QUOTES_DATE_TIME = "executionTsMinusOneHourDateTimeQuotes"
    EXECUTION_TS_MINUS_ONE_HOUR_ASK_PRICE = "executionTsMinusOneHourAskPrice"
    EXECUTION_TS_MINUS_ONE_HOUR_BID_PRICE = "executionTsMinusOneHourBidPrice"
    EXECUTION_TS_MINUS_ONE_HOUR_MID_PRICE = "executionTsMinusOneHourMidPrice"
    EXECUTION_TS_MINUS_ONE_HOUR_ASK_SIZE = "executionTsMinusOneHourAskSize"
    EXECUTION_TS_MINUS_ONE_HOUR_BID_SIZE = "executionTsMinusOneHourBidSize"
    EXECUTION_TS_MINUS_ONE_HOUR_RIC_VENUE = "executionTsMinusOneHourRicVenue"

    EXECUTION_TS_MINUS_TWO_HOURS_QUOTES_DATE_TIME = "executionTsMinusTwoHoursDateTimeQuotes"
    EXECUTION_TS_MINUS_TWO_HOURS_ASK_PRICE = "executionTsMinusTwoHoursAskPrice"
    EXECUTION_TS_MINUS_TWO_HOURS_BID_PRICE = "executionTsMinusTwoHoursBidPrice"
    EXECUTION_TS_MINUS_TWO_HOURS_MID_PRICE = "executionTsMinusTwoHoursMidPrice"
    EXECUTION_TS_MINUS_TWO_HOURS_ASK_SIZE = "executionTsMinusTwoHoursAskSize"
    EXECUTION_TS_MINUS_TWO_HOURS_BID_SIZE = "executionTsMinusTwoHoursBidSize"
    EXECUTION_TS_MINUS_TWO_HOURS_RIC_VENUE = "executionTsMinusTwoHoursRicVenue"


class TradeTickColumnsRenamed(BaseColumns):
    ORDER_RECEIVED_PRICE = "tradePriceOrderReceived"
    ORDER_RECEIVED_DATE_TIME = "tradeDateTimeOrderReceived"
    ORDER_RECEIVED_RIC_VENUE = "tradeRicVenueOrderReceived"

    EXECUTION_TS_PRICE = "tradePriceExecutionTs"
    EXECUTION_TS_VENUE = "tradeVenueExecutionTs"
    EXECUTION_TS_DATE_TIME = "tradeDateTimeExecutionTs"


class MarketDataAsofJoinData:
    RENAME_MAP = "renameMap"
    TOLERANCE = "tolerance"

    REQUIRED_COLUMNS_QUOTES = [
        QuoteTickColumns.DATE_TIME,
        QuoteTickColumns.BID_PRICE,
        QuoteTickColumns.BID_SIZE,
        QuoteTickColumns.ASK_SIZE,
        QuoteTickColumns.ASK_PRICE,
        QuoteTickColumns.MID_PRICE,
        TickColumns.VENUE,
    ]
    REQUIRED_COLUMNS_PREVAL_QUOTES = [
        QuoteTickColumns.DATE_TIME,
        QuoteTickColumns.BID_PRICE,
        QuoteTickColumns.ASK_PRICE,
        TickColumns.VENUE,
    ]
    STATS_COLUMNS = [
        EoDStatsColumns.OPEN_PRICE,
        # EoDStatsColumns.LOW_PRICE,
        # EoDStatsColumns.HIGH_PRICE,
        EoDStatsColumns.CLOSE_PRICE,
        EoDStatsColumns.TRADE_VOLUME,
        EoDStatsColumns.DATE,
        EoDStatsColumns.VOLUME_VOLATILITY,
        EoDStatsColumns.PRICE_VOLATILITY,
        EoDStatsColumns.VOLUME_EMA,
        EoDStatsColumns.VWAP,
        EoDStatsColumns.VENUE,
    ]
    PREVAL_STATS_COLUMNS = [
        EoDStatsColumns.DATE,
        EoDStatsColumns.CLOSE_PRICE,
        EoDStatsColumns.VENUE,
    ]
    PREVAL_ONE_DAY_RENAME = {
        EoDStatsColumns.DATE: EoDStatsRenamed.DATE_PREVAL_ONE_DAY,
        EoDStatsColumns.CLOSE_PRICE: EoDStatsRenamed.CLOSE_PREVAL_ONE_DAY,
        EoDStatsColumns.VENUE: EoDStatsRenamed.RIC_VENUE_PREVAL_ONE_DAY,
    }
    PREVAL_FIVE_DAY_RENAME = {
        EoDStatsColumns.DATE: EoDStatsRenamed.DATE_PREVAL_FIVE_DAY,
        EoDStatsColumns.CLOSE_PRICE: EoDStatsRenamed.CLOSE_PREVAL_FIVE_DAY,
        EoDStatsColumns.VENUE: EoDStatsRenamed.RIC_VENUE_PREVAL_FIVE_DAY,
    }
    PREVAL_TEN_DAY_RENAME = {
        EoDStatsColumns.DATE: EoDStatsRenamed.DATE_PREVAL_TEN_DAY,
        EoDStatsColumns.CLOSE_PRICE: EoDStatsRenamed.CLOSE_PREVAL_TEN_DAY,
        EoDStatsColumns.VENUE: EoDStatsRenamed.RIC_VENUE_PREVAL_TEN_DAY,
    }
    PREVAL_TWENTY_DAY_RENAME = {
        EoDStatsColumns.DATE: EoDStatsRenamed.DATE_PREVAL_TWENTY_DAY,
        EoDStatsColumns.CLOSE_PRICE: EoDStatsRenamed.CLOSE_PREVAL_TWENTY_DAY,
        EoDStatsColumns.VENUE: EoDStatsRenamed.RIC_VENUE_PREVAL_TWENTY_DAY,
    }

    TRADE_COLUMNS = [
        TradeTickColumns.DATE_TIME,
        TradeTickColumns.PRICE,
        TradeTickColumns.VOLUME,
        TradeTickColumns.RIC,
        TickColumns.VENUE,
    ]
    TRADE_COLUMNS_ORDER_RECEIVED = [
        TradeTickColumnsRenamed.ORDER_RECEIVED_PRICE,
        TradeTickColumnsRenamed.ORDER_RECEIVED_DATE_TIME,
        TradeTickColumnsRenamed.ORDER_RECEIVED_RIC_VENUE,
    ]
    TRADE_COLUMN_EXECUTION_TS_RENAME = {
        TradeTickColumns.DATE_TIME: TradeTickColumnsRenamed.EXECUTION_TS_DATE_TIME,
        TickColumns.VENUE: TradeTickColumnsRenamed.EXECUTION_TS_VENUE,
        TradeTickColumns.PRICE: TradeTickColumnsRenamed.EXECUTION_TS_PRICE,
    }
    TRADES_COLUMN_ORDER_RECEIVED_RENAME = {
        TradeTickColumns.PRICE: TradeTickColumnsRenamed.ORDER_RECEIVED_PRICE,
        TradeTickColumns.DATE_TIME: TradeTickColumnsRenamed.ORDER_RECEIVED_DATE_TIME,
        TickColumns.VENUE: TradeTickColumnsRenamed.ORDER_RECEIVED_RIC_VENUE,
    }
    QUOTE_COLUMNS_ORDER_RECEIVED_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_QUOTES_DATE_TIME,
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_BID_PRICE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.ORDER_RECEIVED_ASK_SIZE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.ORDER_RECEIVED_BID_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.ORDER_RECEIVED_RIC_VENUE,
    }
    QUOTE_COLUMNS_ORDER_SUBMITTED_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_QUOTES_DATE_TIME,
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_BID_PRICE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_ASK_SIZE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_BID_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.ORDER_SUBMITTED_RIC_VENUE,
    }
    QUOTE_COLUMNS_EXECUTION_TS_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE,
    }
    MINUS_ONE_MIN_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_QUOTES_DATE_TIME,  # noqa: E501
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_MIN_RIC_VENUE,
    }
    MINUS_FIVE_MIN_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_QUOTES_DATE_TIME,  # noqa: E501
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_FIVE_MIN_RIC_VENUE,
    }
    MINUS_TEN_MIN_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_QUOTES_DATE_TIME,  # noqa: E501
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TEN_MIN_RIC_VENUE,
    }
    MINUS_ONE_HOUR_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_QUOTES_DATE_TIME,  # noqa: E501
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_ONE_HOUR_RIC_VENUE,
    }
    MINUS_TWO_HOUR_RENAME = {
        QuoteTickColumns.DATE_TIME: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_QUOTES_DATE_TIME,  # noqa: E501
        QuoteTickColumns.MID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_MID_PRICE,
        QuoteTickColumns.ASK_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_ASK_PRICE,
        QuoteTickColumns.BID_PRICE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_BID_PRICE,
        QuoteTickColumns.BID_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_BID_SIZE,
        QuoteTickColumns.ASK_SIZE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_ASK_SIZE,
        TickColumns.VENUE: QuoteTickColumnsRenamed.EXECUTION_TS_MINUS_TWO_HOURS_RIC_VENUE,
    }
