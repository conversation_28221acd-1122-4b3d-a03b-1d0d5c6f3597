from elasticsearch8 import Elasticsearch
from typing import Any, Dict


def search_after(es_client: Elasticsearch, query: Dict[str, Any], index: str):
    """

    :param es_client:
    :param query:
    :param index:
    :return:
    """
    response = es_client.search(index=index, body=query)
    hits = response["hits"]["hits"]

    while hits:
        for hit in hits:
            yield hit

        # Get the sort values from the last document
        last_sort_values = hits[-1]["sort"]

        # Use the sort values in the next search_after query
        query["search_after"] = last_sort_values

        response = es_client.search(index=index, body=query)
        hits = response["hits"]["hits"]
