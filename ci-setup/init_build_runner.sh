#!/bin/bash -xe
echo "Please enter github PAT:"

# Read the input to the variable 'name'
read pat

export DEBIAN_FRONTEND=noninteractive

sudo apt-get update
sudo apt-get upgrade -y
sudo apt-get install -y unzip

# install runner, update version from new releases of github runner at https://github.com/actions/runner
export GITHUB_RUNNER_VERSION="2.322.0"

# use dpkg --print-architecture with if condition to check current architecture, and set GITHUB_RUNNER_AARCH and RUNNER_LABEL accordingly
if [ "$(dpkg --print-architecture)" = "amd64" ]; then
  export GITHUB_RUNNER_AARCH="x64"
  export RUNNER_LABEL="se-mono-amd-runners"
else
  export GITHUB_RUNNER_AARCH="arm64"
  export RUNNER_LABEL="se-mono-arm-runners"
fi

# install aws cli
sudo snap install aws-cli --classic

# install in home directory
cd /home/<USER>
curl -Ls https://github.com/actions/runner/releases/download/v$GITHUB_RUNNER_VERSION/actions-runner-linux-$GITHUB_RUNNER_AARCH-$GITHUB_RUNNER_VERSION.tar.gz | tar -zxf -

# config runner
./config.sh --pat $pat --unattended --url https://github.com/steeleye/se-mono --labels $RUNNER_LABEL --replace

# install as service
sudo ./svc.sh install
sudo ./svc.sh start
sudo ./svc.sh status

