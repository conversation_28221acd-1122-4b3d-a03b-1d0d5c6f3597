# SE-MONO CI

## Overview
There are 2 main workflows in CI:

### Validation
This workflow is triggered on pushing a specific commit message "[validate]". It also runs automatically whenever a PR is requested to be merged into the main branch.
The validation runs pants tests and linters on both ARM and ARM self-hosted runners. It consists of following steps
1. pre-checks - check if the commit message contains "[validate]" or if the PR is requested to be merged into the main branch
2. ecr-creds - get the ECR credentials for pulling images from ECR for running further CI steps. All CI steps run in specific docker images
3. Validation steps
   1. python-validation-amd - run pants tests and linters on AMD runner
   2. python-validation-arm - run pants tests and linters on ARM runner

### Publish
This workflow is triggered on following situations:
1. Pushing commit message "[publish-docker|changed]" - this will trigger the publish workflow to build and push the docker images to ECR and ACR, which are changed on current branch since `main`
2. Pushing commit message"[publish-docker|<pants-docker-target>] - this will trigger the publish workflow to build and push the request docker image specified by `<pants-docker-target>` to ECR and ACR
3. Merge to main 

It consists of following steps
1. publish-check - Checks if this workflow should run as per above situations
2. ecr-creds - get the ECR credentials for pulling CI runner images from ECR
3. Publish steps - This step gets the ECR and ACR credentials, and builds the docker images using docker buildx. It pushes images in the form of docker manifest. The manifest is suffixed with `.arm64` and `.amd64` for ARM and AMD runners respectively.
   1. publish-docker-amd - build and push the docker images to ECR and ACR for AMD runner. 
   2. publish-docker-arm - build and push the docker images to ECR and ACR for ARM runner
4. publish-docker-manifests - create and push the docker manifest for multi-arch images. It figures out the image manifests which were pushed for amd and arm in previous runs, and pushes a combined manifest for both architectures.


## Runners
There are 2 types of runners used in CI:
1. self-hosted runners are used for python-validation-amd, python-validation-arm, python-validation-amd, publish-docker-amd, publish-docker-arm steps. These runners are hosted on AWS and are used for running pants tests and linters. github-hosted runners do not support ARM and are ephemeral, which means they do not persist docker cache and pants cache. Although GHA can be used to persist cache, it's size is limited to 10Gib and limited by the speeds of network and storage. Using self-hosted runners allows us to use local disk based cache for docker and remote cache for pants. The remote cache for pants is hosted in same AZ, which allows for fastest possible network speeds, and no network transfer cost.
2. All other jobs use ubuntu-latest github-hosted runners. (ubuntu-latest runners have some free tier usage with our enterprise plan)

## Deploying self-hosted runners

### ARM and AMD runners
1. Start EC2 instance for given architecture m7g.2xlarge (ARM) and m7a.2xlarge (AMD)
  - instance role -  arn:aws:iam::112367482278:instance-profile/se-mono-ci-role
  - instance key - se-mono-runners-key
  - security group - se-mono-security
  - VPC - vpc-17674b70	
  - AZ - eu-west-1c	
2. Connect root storage with 1-1.5 TiB gp3 volume, 3K IOPS, 750 MiB/s throughput
3. Use key se-mono-runners-key.pem (available with the team)
4. Make sure all runner instances are in same AZ and same region as ECR (to reduce network transfer costs)
5. SSH into the instance, and follow these installation steps
6. Install docker - using `install_docker.sh`
7. Setup docker using `setup_docker.sh`. This script disables build cache GC, and add crontab to autocleanup docker build cache. Configure var BUILD_CACHE_TTL_DAYS in the script to modify caching TTL
8. Install github runners using script `init_build_runner.sh`. When asked for PAT (personal access token) you can use the existing PAT (can be found with solutions engineering team), or generate (please save it) a new classic PAT with `repo` and `admin:org -- manage_runners:org` perms
9. This will install the runner as a service, which persists between system reboots
10. Use `remove_runner.sh` to remove the runner service
11. The instance will store docker build cache upto configured TTL as per point 7

### Pants remote cache
1. Bazel cache is used for pants remote cache -https://github.com/buchgr/bazel-remote
2. Deploy an ec2 instance, with EBS 525 Gib, 3k IOPS, 300 MiB/s throughput, in same AZ as the runners
3. Install docker using `install_docker.sh`
4. Create directory `~/.cache` and change its perms to `chmod -R 777 ~/.cache`
4. Run the bezel cache `docker compose -f docker-compose-bazel-cache.yml up -d`
5. It opens port 9092 for gRPC and 8080 for HTTP. Make sure these ports are allowed in security group for all runners and cache instance
6. Use the private IP of the instance in the pants remote cache config in pants.ci.toml in `remote_store_address`
7. The cache instance will store the cache upto 475 Gib, as configure in docker-compose file

## CI Details

### validate.yaml and python-validation.yaml
1. `validate.yaml` is the main workflow, which is compose of ARM and AMD jobs for running pants tests and linters in respective runners. 
2. `python-validation.yaml` Uses pants to run tests and linters
3. Pants `named_caches` needs to be different for each default.lock files. So we update its location based on hash of the lockfile. Old caches are cleaned up Caching Housekeeping step in the workflow, so that we don't run out of disk space. Configure `CACHE_TTL_DAYS` in the workflow to modify caching TTL
4. The `lmdb_store` pants cache is configure to used remote cache, using several `remote*` config options in `pants.ci.toml`
5. At the end of the job, the local `lmdb_store` is cleaned up, because we are using remote cache for pants. This is done to save disk space on runners

### `publish.yaml` and `publish-docker.yaml`
1. `publish.yaml` is the main workflow, which is composed of ARM and AMD jobs for building and pushing docker images to ECR and ACR
2. `publish-docker.yaml` is used to build and push the docker images to ECR and ACR
3. At the end of the job, all generated docker images are cleaned up, to save disk space on runners. The layers of images are part of docker build cache, stored on the instance. The cache is reused by docker in subsequent runs to speed up the build process. The cache is cleaned up after TTL, as configured in `setup_docker.sh` script
4. The Publish images step outputs a json file with image names and tags, which is used by the next step to create and push the docker manifest
5. The docker manifest is created and pushed for multi-arch images. The manifest is suffixed with `.arm64` and `.amd64` for ARM and AMD runners respectively
6. The Publish Docker Manifests step uses the json file generated by Publish images step to figure out the image manifests which were pushed for amd and arm in previous runs, and pushes a combined manifest for both architectures

