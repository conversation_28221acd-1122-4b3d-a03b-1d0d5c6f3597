#!/bin/bash -xe
export DEBIAN_FRONTEND=noninteractive

# -------------------------------
# 1. Update and Install Dependencies
# -------------------------------
sudo apt-get update
sudo apt-get install -y unzip

# -------------------------------
# 2. Define CloudWatch Agent Directory
# -------------------------------
CW_AGENT_DIR="$HOME/cw-agent"

# -------------------------------
# 3. Clean Up Existing cw-agent Directory
# -------------------------------
if [ -d "$CW_AGENT_DIR" ]; then
  echo "Removing existing CloudWatch Agent directory: $CW_AGENT_DIR"
  sudo rm -rf "$CW_AGENT_DIR"
fi

# Create a fresh CloudWatch Agent directory
mkdir -p "$CW_AGENT_DIR"
cd "$CW_AGENT_DIR" || exit

# -------------------------------
# 4. Determine Architecture
# -------------------------------
ARCH=$(dpkg --print-architecture)
if [ "$ARCH" = "amd64" ]; then
  CW_ARCH="amd64"
elif [ "$ARCH" = "arm64" ]; then
  CW_ARCH="arm64"
else
  echo "Unsupported architecture: $ARCH"
  exit 1
fi

# -------------------------------
# 5. Download and Install CloudWatch Agent
# -------------------------------
echo "Downloading Amazon CloudWatch Agent for architecture: $CW_ARCH..."
sudo wget https://s3.amazonaws.com/amazoncloudwatch-agent/linux/"$CW_ARCH"/latest/AmazonCloudWatchAgent.zip

echo "Unzipping AmazonCloudWatchAgent.zip..."
sudo unzip AmazonCloudWatchAgent.zip

echo "Installing Amazon CloudWatch Agent..."
sudo ./install.sh

# -------------------------------
# 6. Create CloudWatch Agent Configuration
# -------------------------------
# Use 'sudo tee' to create the config file with root permissions
echo "Creating CloudWatch Agent configuration file..."
sudo tee "$CW_AGENT_DIR/cwagent-config.json" > /dev/null <<EOF
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "cwagent"
  },
  "metrics": {
    "append_dimensions": {
      "InstanceId": "\${aws:InstanceId}"
    },
    "metrics_collected": {
      "cpu": {
        "measurement": [
          "usage_idle",
          "usage_iowait",
          "usage_user",
          "usage_system",
          "usage_guest"
        ],
        "metrics_collection_interval": 60,
        "totalcpu": true
      },
      "mem": {
        "measurement": [
          "mem_used_percent",
          "mem_available_percent",
          "mem_total",
          "mem_free"
        ],
        "metrics_collection_interval": 60
      },
      "disk": {
        "measurement": [
          "used_percent"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      },
      "diskio": {
        "measurement": [
          "read_bytes",
          "write_bytes",
          "reads",
          "writes"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      }
    }
  }
}
EOF

# -------------------------------
# 7. Verify Configuration File Exists
# -------------------------------
echo "Verifying the configuration file exists..."
if [ ! -f "$CW_AGENT_DIR/cwagent-config.json" ]; then
  echo "Configuration file not found: $CW_AGENT_DIR/cwagent-config.json"
  exit 1
fi
echo "Configuration file successfully created."

# -------------------------------
# 8. Start the CloudWatch Agent
# -------------------------------
echo "Starting Amazon CloudWatch Agent with the new configuration..."
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
  -a fetch-config -m ec2 -c file:"$CW_AGENT_DIR"/cwagent-config.json -s

# -------------------------------
# 9. Verify Agent Status
# -------------------------------
echo "Verifying Amazon CloudWatch Agent status..."
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -m ec2 -a status