#!/bin/bash -xe
export DEBIAN_FRONTEND=noninteractive



# create folder /etc/docker if not exists using sudo
sudo mkdir -p /etc/docker
# write daemon.json file inside, using this config
sudo tee /etc/docker/daemon.json <<EOF
{
  "builder": {
    "gc": {
      "enabled": false
    }
  },
  "max-concurrent-downloads": 16,
  "max-concurrent-uploads": 16
}
EOF

# chown the directory and its contents to ubuntu:ubuntu
sudo chown -R ubuntu:ubuntu /etc/docker
sudo service docker restart
sudo service docker restart

sudo touch /var/log/docker_builder_cleanup.log
sudo chown ubuntu:ubuntu /var/log/docker_builder_cleanup.log

sudo touch /var/log/aws_ecr_login.log
sudo chown ubuntu:ubuntu /var/log/aws_ecr_login.log

export DOCKER_ASSETS_TTL_DAYS=15
docker_assets_ttl_hours=$((DOCKER_ASSETS_TTL_DAYS * 24))

# Add the above commands to crontab
# Remove the existing crontab
crontab -l && crontab -r

# Add both cron jobs
(
  echo "0 */3 * * * /usr/bin/docker container prune --filter=\"until=${docker_assets_ttl_hours}h\" --force >> /var/log/docker_builder_cleanup.log 2>&1"
  echo "10 */3 * * * /usr/bin/docker network prune --filter=\"until=${docker_assets_ttl_hours}h\" --force >> /var/log/docker_builder_cleanup.log 2>&1"
  echo "20 */3 * * * /usr/bin/docker image prune --filter=\"until=${docker_assets_ttl_hours}h\" --force --all>> /var/log/docker_builder_cleanup.log 2>&1"
  echo "30 */3 * * * /usr/bin/docker volume prune --force --all>> /var/log/docker_builder_cleanup.log 2>&1"
  echo "40 */3 * * * /usr/bin/docker buildx prune --filter=\"until=${docker_assets_ttl_hours}h\" --force --all --verbose>> /var/log/docker_builder_cleanup.log 2>&1"
  echo "50 */3 * * * /usr/bin/docker builder prune --filter=\"until=${docker_assets_ttl_hours}h\" --force --all>> /var/log/docker_builder_cleanup.log 2>&1"
  echo "7 */4 * * * aws ecr get-login-password | docker login --username AWS --password-stdin 698897937809.dkr.ecr.eu-west-1.amazonaws.com >> /var/log/aws_ecr_login.log 2>&1"
) | crontab -

# List the current crontab
crontab -l
