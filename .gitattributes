*               filter=lfs diff=lfs merge=lfs -text
*.cfg        filter= diff= merge= text
*.csv        filter= diff= merge= text
*.env        filter= diff= merge= text
*.flake8        filter= diff= merge= text
*.html          filter= diff= merge= text
*.ini        filter= diff= merge= text
*.json        filter= diff= merge= text
*.lock        filter= diff= merge= text
*.mako          filter= diff= merge= text
*.md        filter= diff= merge= text
*.ndjson        filter= diff= merge= text
*.py            filter= diff= merge= text
*.sql       filter= diff= merge= text
*.toml        filter= diff= merge= text
*.txt        filter= diff= merge= text
*.xml        filter= diff= merge= text
*.yaml        filter= diff= merge= text
*.yml        filter= diff= merge= text
*.sql      filter= diff= merge= text
*BUILD          filter= diff= merge= text
*Dockerfile     filter= diff= merge= text
*README          filter= diff= merge= text
Makefile          filter= diff= merge= text
.gitattributes  filter= diff= merge= text
.gitignore      filter= diff= merge= text
.pants.rc.sample       filter= diff= merge= text
CODEOWNERS          filter= diff= merge= text
src/py/tasks/intelligence/aries/email-classifier/email_classifier/model/huggingface/** filter=lfs diff=lfs merge=lfs -text
src/py/tasks/intelligence/aries/email-zoner/email_zoner/model/huggingface/** filter=lfs diff=lfs merge=lfs -text
src/py/tasks/intelligence/aries/email-zoner/email_zoner/model/distilbert-base-multilingual-cased-email-zoner/** filter=lfs diff=lfs merge=lfs -text
src/py/tasks/intelligence/aries/language-detection/language_detection/model/fasttext/** filter=lfs diff=lfs merge=lfs -text
src/py/tasks/intelligence/aries/language-detection/language_detection/model/transformer/** filter=lfs diff=lfs merge=lfs -text
src/py/jobs/intelligence/mar-trials/mar_trials/market_data_stash/market_data.tar.lz4 filter=lfs diff=lfs merge=lfs -text
src/py/jobs/intelligence/mar-trials/mar_trials/elastic_search/docker_compose/es_snapshot.tar.lz4 filter=lfs diff=lfs merge=lfs -text
src/py/jobs/intelligence/mar-trials/mar_trials/data_fetching/data/test_cases.json filter=lfs diff=lfs merge=lfs -text
src/py/jobs/intelligence/mar-trials/mar_trials/data_fetching/data/order_dump.NDJSON filter=lfs diff=lfs merge=lfs -text
src/py/libs/integration/comms/aries-se-comms-tasks/tests_aries_se_comms_tasks/feeds/message/refinitiv_tr_eikon/data/normalize_chat_dataframes/multiple_attachments.pkl filter=lfs diff=lfs merge=lfs -text
src/py/jobs/intelligence/se-lexica/se_lexica/lexica_storage/** filter=lfs diff=lfs merge=lfs -text
*.sh  filter= diff= merge= text
src/py/libs/steeleye/shared/apache-tika-client/tests_apache_tika_client/sample_files/** filter=lfs diff=lfs merge=lfs -text
src/py/tasks/intelligence/aries/market-data-ingestion/loans-cds-converter/tests_loans_cds_converter/data/LX119407__EOD_ROLLING_STATS__EXPECTED.parquet filter=lfs diff=lfs merge=lfs -text
