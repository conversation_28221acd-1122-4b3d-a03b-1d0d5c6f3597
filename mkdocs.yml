site_name: <PERSON><PERSON><PERSON> Monorepo
theme:
  name: material

# Repository
repo_name: steeleye/se-mono
repo_url: https://github.com/steeleye/se-mono
# To show the pencil button in each page
# and redirects into the edit page in GitHub
edit_uri: edit/main/docs/


nav:
  - index.md
  - getting-started.md
  - contributing.md
  - ci.md
  - se-monorepo.md
  - publish-docker.md
  - validate.md
  - lfs.md
  - Tools:
      - tools/pants.md
      - tools/ci-build-images.md

plugins:
  - search
  - git-revision-date-localized # to show last update date
  - mermaid2
  - section-index

markdown_extensions:
  # To render correctly nested lists
  - def_list
  # To support emojis
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  # To support note blocks
  - admonition
  - pymdownx.details
  # To support code syntax highlighting
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:mermaid2.fence_mermaid
  - pymdownx.snippets
  # More extensions to be added under here


extra_javascript:
  # To allow table sorting
  - extra/javascript/tablesort.js
  - https://cdnjs.cloudflare.com/ajax/libs/tablesort/5.2.1/tablesort.min.js
  - https://unpkg.com/mermaid/dist/mermaid.min.js # Explicit declaration of the Mermaid library to use last version

extra_css:
  - extra/css/mkdocstrings.css
