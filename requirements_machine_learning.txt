# Contains a SORTED list of machine learning dependencies

cmake==3.26.3
fasttext-wheel~=0.9.2
ftfy==6.1.1
langid~=1.1.6
lit==16.0.5.post0
torch==2.5.1; sys_platform == "linux" or (sys_platform == "darwin" and platform_machine == "arm64")
tensorflow==2.16.2
tf-keras==2.16.0 # Needed for keras 2.x backend
sentence-transformers==3.4.0; sys_platform == "linux" or (sys_platform == "darwin" and platform_machine == "arm64")
transformers==4.48.0
presidio-anonymizer==2.2.357
presidio-analyzer==2.2.357

nvidia-cublas-cu12==12.4.5.8; sys_platform == 'linux'
nvidia-cuda-cupti-cu12==12.4.127; sys_platform == 'linux'
nvidia-cuda-nvrtc-cu12==12.4.127; sys_platform == 'linux'
nvidia-cuda-runtime-cu12==12.4.127; sys_platform == 'linux'
nvidia-cudnn-cu12==9.1.0.70; sys_platform == 'linux'
nvidia-cufft-cu12==11.2.1.3; sys_platform == 'linux'
nvidia-curand-cu12==10.3.5.147; sys_platform == 'linux'
nvidia-cusolver-cu12==11.6.1.9; sys_platform == 'linux'
nvidia-cusparse-cu12==12.3.1.170; sys_platform == 'linux'
nvidia-cusparselt-cu12==0.7.1; sys_platform == 'linux'
nvidia-nccl-cu12==2.21.5; sys_platform == 'linux'
nvidia-nvjitlink-cu12==12.4.127; sys_platform == 'linux'
nvidia-nvtx-cu12==12.4.127; sys_platform == 'linux'
triton==3.1.0; sys_platform == 'linux'
