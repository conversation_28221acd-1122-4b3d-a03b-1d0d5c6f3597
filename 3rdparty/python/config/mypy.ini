[mypy]
plugins = pydantic.mypy, sqlmypy

# Optionals
implicit_optional = False
strict_optional = True

# Strictness
allow_untyped_globals = False
allow_redefinition = False
implicit_reexport = False
strict_equality = True
strict_concatenate = True
check_untyped_defs = True
disallow_subclassing_any = True
disallow_any_generics = False

# Warnings
warn_unused_ignores = True
warn_no_return = True
warn_return_any = True
warn_redundant_casts = True
warn_unreachable = True
warn_unused_configs = True

# Error output
show_column_numbers = True
show_error_context = True
show_error_codes = True
show_traceback = True
pretty = True
color_output = True
error_summary = True

[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
warn_untyped_fields = True

[mypy-nanoid]
ignore_missing_imports = True

[mypy-conductor.client.*]
ignore_missing_imports = True

[mypy-e164_converter.*]
ignore_missing_imports = True

[mypy-apscheduler.*]
ignore_missing_imports = True

[mypy-schema_sdk.*]
ignore_missing_imports = True

[mypy-indict.*]
ignore_missing_imports = True

[mypy-addict.*]
ignore_missing_imports = True

[mypy-fsspec.*]
ignore_missing_imports = True

[mypy-se_schema.*,master_data_schema.*,tenant_data_schema.*]
ignore_missing_imports = True

[mypy-event_sdk.*]
ignore_missing_imports = True

[mypy-se_boltons.*]
ignore_missing_imports = True

[mypy-se_core_tasks.*]
ignore_missing_imports = True

[mypy-Crypto.*]
ignore_missing_imports = True

[mypy-se_elasticsearch.*]
ignore_missing_imports = True

[mypy-chardet.*]
ignore_missing_imports = True

[mypy-bs4.*]
ignore_missing_imports = True

[mypy-elasticsearch6.*]
ignore_missing_imports = True

[mypy-s3fs.*]
ignore_missing_imports = True

[mypy-fastparquet.*]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-starlette.*]
ignore_missing_imports = True

[mypy-mock_alchemy.*]
ignore_missing_imports = True

[mypy-confluent_kafka.*]
ignore_missing_imports = True

[mypy-moto.*]
ignore_missing_imports = True

[mypy-importlib.*]
ignore_missing_imports = True

[mypy-hvac.*]
ignore_missing_imports = True

[mypy-lexica_matcher.*]
ignore_missing_imports = True

[mypy-api_sdk.*]
ignore_missing_imports = True

[mypy-master_data_api_client.*]
ignore_missing_imports = True

[mypy-pyarrow.*]
ignore_missing_imports = True

[mypy-flang.*]
ignore_missing_imports = True

[mypy-se_trades_tasks.*]
ignore_missing_imports = True

[mypy-slugify.*]
ignore_missing_imports = True

[mypy-surveillance_query_builder.*]
ignore_missing_imports = True

[mypy-data_platform_sdk.*]
ignore_missing_imports = True

[mypy-bcrypt.*]
ignore_missing_imports = True

[mypy-yaml.*]
ignore_missing_imports = True

[mypy-ndjson.*]
ignore_missing_imports = True

[mypy-azure.*]
ignore_missing_imports = True

[mypy-orjson.*]
ignore_missing_imports = True

[mypy-importlib_metadata.*]
ignore_missing_imports = True

[mypy-se_api_client.*]
ignore_missing_imports = True

[mypy-market_data_sdk.*]
ignore_missing_imports = True

[mypy-flatten_json.*]
ignore_missing_imports = True

[mypy-aiobotocore.*]
ignore_missing_imports = True

# Temporary ignore
[mypy-se_market_data_utils.*]
ignore_missing_imports = True

# Temporary ignore
[mypy-se_elastic_schema.*]
ignore_missing_imports = True

[mypy-pyspark.*]
ignore_missing_imports = True

[mypy-se_schema_meta.*]
ignore_missing_imports = True

[mypy-unflatten.*]
ignore_missing_imports = True

[mypy-webvtt.*]
ignore_missing_imports = True

[mypy-market_abuse_algorithms.*]
ignore_missing_imports = True

[mypy-generator.*]
ignore_missing_imports = True

[mypy-jinja2.*]
ignore_missing_imports = True

[mypy-pycountry.*]
ignore_missing_imports = True

[mypy-pydub.*]
ignore_missing_imports = True

[mypy-zstandard.*]
ignore_missing_imports = True

[mypy-singleton_decorator.*]
ignore_missing_imports = True

[mypy-tika.*]
ignore_missing_imports=True
