line-length = 100
target-version = "py311"

# this is kept to false, because value of `true` cause the pants lint goal to not report linting
# issues, which gets committed to the repository.
fix = false

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "pants-plugins",
]


[lint]
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
# not using isort with ruff due to https://github.com/pantsbuild/pants/issues/18410
select = ["E", "F", "T20", "W", "I"]

[lint.isort]
# because of https://github.com/pantsbuild/pants/issues/18410 the section ordering does not work
# pants, because pants runs sorting goals without awareness about other projects of the workspace.
# so isort is unable to identify which imports belong to first or third party.
no-sections = true
