# Global ownership is for Solutions Engineering Team, as mono repo and developer productivity falls in Solutions Engineering team.
# Please add ownership lines following this to own other directories, without dependency on Solutions Engineering Team team
* @steeleye/solutions-engineering-owners


# all directories by team names are respectively owned by teams, irrespective of their location in repo
platform/ @steeleye/solutions-engineering-owners
solutions-eng/ @steeleye/solutions-engineering-owners
integration/ @steeleye/de-integration-owners
intelligence/ @steeleye/de-intelligence-owners
data-science/ @steeleye/data-science-owners
secops/ @davidfhaines @amanthakur-7
# specific rules for API team, to avoid clash with `apis` directory
/src/py/apis/api/ @steeleye/api-admins
/src/py/libs/db-models/api/ @steeleye/api-admins
/src/py/libs/api/ @steeleye/api-admins
/src/py/tasks/api/ @steeleye/api-admins
/src/py/workflows/aries-workflow-deployer/definitions/tasks/api/ @steeleye/api-admins
/src/py/workflows/aries-workflow-deployer/definitions/workflows/api/ @steeleye/api-admins
/src/py/jobs/api/ @steeleye/api-admins


# specific rules for solutions team
/src/py/workflows/aries-workflow-deployer/definitions/tasks/solutions-engineering/ @steeleye/solutions-engineering-owners
/src/py/workflows/aries-workflow-deployer/definitions/workflows/solutions-engineering/ @steeleye/solutions-engineering-owners
/src/py/workflows/aries-workflow-deployer/aries_workflow_deployer/ @steeleye/solutions-engineering-owners
/src/py/workflows/aries-workflow-deployer/definitions/workflows/master-data/ @steeleye/solutions-engineering-owners
/src/py/tasks/intelligence/aries/ric-lookup @steeleye/solutions-engineering-owners
/src/py/tasks/intelligence/aries/trade-cost-analytics @steeleye/solutions-engineering-owners
/src/py/tasks/intelligence/aries/refinitiv-news-ingestion @steeleye/solutions-engineering-owners
/src/py/workflows/aries-workflow-deployer/definitions/tasks/master-data/ @steeleye/efdh-owners
/src/py/tasks/efdh/ @steeleye/efdh-owners

# all libs within the steeleye directory are a shared responsibility across all teams
/src/py/libs/steeleye/ @steeleye/solutions-engineering-owners @steeleye/de-integration-owners @steeleye/de-intelligence-owners @steeleye/api-admins


# all db-models are owned by all teams, as they are shared across all teams
/src/py/libs/db-models/ @steeleye/solutions-engineering-owners @steeleye/de-integration-owners @steeleye/de-intelligence-owners @steeleye/api-admins


# requirements.txt is a hard requirement file across the repo, so we make it owned by Solutions Engineering Team, so that they can assign the review to respective teams
**/requirements*.txt @steeleye/solutions-engineering-owners


# se_libs requirements file is owned by eng-leads and solutions-engineering-owners (one approval from any of the teams is enough)
**/requirements_se_libs.txt @steeleye/solutions-engineering-owners @steeleye/eng-leadership


# SQL files are owned by solutions-engineering-owners
**/*.sql @steeleye/solutions-engineering-owners


# no one owns these files to avoid review spams
/3rdparty/python/default.lock


# File specific owners
/src/py/tasks/intelligence/aries/mar/insider-trading-v3/ @redking1 @r-priyam @patricialima
# owning the failure workflow to ensure that SolEng is aware of this critical workflow update
src/py/workflows/aries-workflow-deployer/definitions/workflows/intelligence/surveillance_workflow_failure.json @steeleye/solutions-engineering-owners
src/py/workflows/aries-workflow-deployer/definitions/workflows/integration/aries_auditable_workflow_failure.json @steeleye/solutions-engineering-owners

# MAR algo changes
/src/py/libs/intelligence/market-abuse-algorithms/ @microft @omsoares @parakhshrenik

# owning pants specific files to ensure quality of the build system
**/BUILD @steeleye/solutions-engineering-owners
**/Dockerfile @steeleye/solutions-engineering-owners
**/SOURCE_ROOT @steeleye/solutions-engineering-owners

# Tools folders assigned to respective owners
/tools/data-science @steeleye/data-science-owners
