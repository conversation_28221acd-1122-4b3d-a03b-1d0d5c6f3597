# Contains a SORTED list of 3rd party dependencies
# https://peps.python.org/pep-0440/#compatible-release


APScheduler[sqlalchemy]~=3.10
PyJWT~=2.9.0
SQLAlchemy[postgresql]~=1.4
XlsxWriter>=1.2.8
addict~=2.4
adlfs>=2024.12.0
aiobotocore==2.17.0
aiohttp~=3.8
alembic~=1.10
arrow~=1.3
asyncio~=3.4
atlassian-python-api~=3.41.16
azure-ai-translation-text~=1.0.1
azure-communication-email~=1.0.0
azure-core~=1.29
azure-identity~=1.15
azure-storage-blob~=12.19
backoff~=2.2
beautifulsoup4~=4.11
boltons~=23.0
boto3~=1.24
botocore~=1.27
bounded-executor~=0.0.5
cachetools~=5.3
celery~=5.3.1
chardet~=5.1.0
click~=8.1
cloudpathlib~=0.20
conductor-python~=1.0.60
confection~=0.1.3
confluent-kafka~=2.6
deepmerge~=1.1.1
dependency-injector[yaml]~=4.42
dill~=0.3.6
elastic-transport~=8.13.0
elasticsearch-dsl~=7.4.0
elasticsearch6~=6.8
elasticsearch7~=7.17
elasticsearch8~=8.12
emoji~=2.6.0
fastapi~=0.115
fastparquet~=2023.2
faust-streaming~=0.11
filetype~=1.2.0
flatten_json~=0.1.14
fsspec>=2024.12.0
glom~=23.5.0
graphviz~=0.20
html2text~=2024.2.26
httpx~=0.23
humanize~=4.6
hvac~=1.1.0
inscriptis~=2.3.2
jinja2~=3.1.5
libjson2csv~=0.1.1
lxml==5.3.2 # pinned because getting `xmlsec.InternalError: (-1, 'lxml & xmlsec libxml2 library version mismatch')` for updated version
matplotlib~=3.10.1
mode-streaming # no version, as its controlled by faust-streaming
mutagen~=1.46.0
nanoid~=2.0
ndjson~=0.3.1
numpy~=1.24
omegaconf~=2.3
openai~=1.48.0
openpyxl~=3.1.2
orjson~=3.8
pandas~=2.1.3
paramiko~=3.5.0
pendulum~=3.0.0
polars==1.1.0
prometheus-client~=0.16
psutil~=5.9.4
psycopg2~=2.9
pyarrow~=18.0.0
pybase64~=1.4
pycountry~=22.3.5
pycryptodome~=3.17
pydantic-sqlalchemy~=0.0.9
pydantic==1.10.15
pydub~=0.25.1
pyspark~=3.5
python-dateutil~=2.9
python-gnupg~=0.5.0
python-magic~=0.4.27
python-multipart~=0.0.20
python-slugify>=5.0.0,<6.0.0
pytz~=2023.3
pyyaml~=6.0
ratelimit~=2.2.1
redis==5.0.2 # redis must be pinned because 5.0.3 breaks fakeredis
requests
ringcentral~=0.9.0
s3fs>=2024.12.0
scipy~=1.10.1
scikit-learn~=1.6.1
sentry-sdk~=2.18
singleton-decorator~=1.0.0
slack-sdk~=3.26.2
spacy==3.7.5
sse-starlette~=2.2.1
starlette # let fastapi control the version
sudachipy @ git+https://github.com/steeleye/SudachiPy@248af583344d757c14cb3c551c0894747780244f # sudachipy is installed from our fork, with pip 23.x fixes for compatibility, it mimics 0.5.5
tika==2.6
tiktoken~=0.8.0
tqdm
unflatten~=0.1.1
urllib3
uvicorn~=0.21.1
webvtt-py~=0.4.6
xlrd~=2.0.1
xmlsec==1.3.14 # pinned due to https://github.com/xmlsec/python-xmlsec/issues/344
xmltodict~=0.13.0
xxhash~=3.4
zstandard~=0.21
